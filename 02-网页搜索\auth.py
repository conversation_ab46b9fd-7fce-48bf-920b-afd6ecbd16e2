import secrets
import datetime
import uuid
from functools import wraps
from flask import request, jsonify
from database import get_user_by_id, consume_credits, refresh_monthly_credits
from token_counter import count_tokens, count_messages_tokens

def generate_session_token():
    """生成会话令牌"""
    return secrets.token_urlsafe(32)

def create_session(user_id):
    """创建用户会话"""
    # 使用database.py中的session系统
    from database import create_session as db_create_session
    return db_create_session(user_id)

def get_user_from_session(session_token):
    """从会话令牌获取用户信息"""
    if not session_token:
        return None

    # 使用database.py中的session系统
    from database import get_user_by_session_token
    user = get_user_by_session_token(session_token)

    if user:
        # 自动刷新每月积分
        refresh_monthly_credits(user['id'])
        # 重新获取用户信息（积分可能已更新）
        user = get_user_by_id(user['id'])

    return user

def delete_session(session_token):
    """删除会话"""
    from database import delete_session as db_delete_session
    return db_delete_session(session_token)


def require_auth(f):
    """需要认证的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 从请求头或cookie中获取session token
        session_token = request.headers.get('Authorization')
        auth_method = 'header'

        if session_token and session_token.startswith('Bearer '):
            session_token = session_token[7:]
            print(f"[AUTH] 使用Authorization头认证，token: {session_token[:10]}...")
        else:
            session_token = request.cookies.get('session_id')
            auth_method = 'cookie'
            if session_token:
                print(f"[AUTH] 使用Cookie认证，token: {session_token[:10]}...")
            else:
                print(f"[AUTH] 没有找到认证信息")

        user = get_user_from_session(session_token)
        if not user:
            print(f"[AUTH] 认证失败，无效的session token")
            return jsonify({'error': '请先登录'}), 401

        print(f"[AUTH] 认证成功，用户: {user['username']}, 方式: {auth_method}")

        # 将用户信息添加到请求上下文
        request.current_user = user
        return f(*args, **kwargs)

    return decorated_function

def check_credits(required_credits=1):
    """检查积分的装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = getattr(request, 'current_user', None)
            if not user:
                return jsonify({'error': '请先登录'}), 401

            # VIP用户有无限积分
            if user['is_vip'] and user['vip_expire_date']:
                vip_expire = datetime.datetime.fromisoformat(user['vip_expire_date'])
                if vip_expire >= datetime.datetime.now():
                    # VIP未过期，继续执行
                    return f(*args, **kwargs)

            # 普通用户或VIP已过期，检查积分
            if user['credits'] < required_credits:
                return jsonify({
                    'error': '积分不足',
                    'required': required_credits,
                    'current': user['credits']
                }), 402

            return f(*args, **kwargs)

        return decorated_function
    return decorator

def get_user_credits(user_id):
    """获取用户积分"""
    user = get_user_by_id(user_id)
    if not user:
        return 0

    # VIP用户显示无限积分
    if user['is_vip'] and user['vip_expire_date']:
        vip_expire = datetime.datetime.fromisoformat(user['vip_expire_date'])
        if vip_expire >= datetime.datetime.now():
            return 10000000  # 1000万，表示无限

    return user['credits']

def calculate_token_cost(input_tokens, output_tokens):
    """计算token消耗的积分"""
    # 计算规则：每200个token消耗1积分
    total_tokens = input_tokens + output_tokens
    cost = max(1, total_tokens // 200)  # 至少消耗1积分

    print(f"[TOKEN_COST] 输入tokens: {input_tokens}, 输出tokens: {output_tokens}")
    print(f"[TOKEN_COST] 总tokens: {total_tokens}, 消耗积分: {cost}")

    return cost

def count_input_tokens(text):
    """计算输入文本的精确token数"""
    return count_tokens(text, "DeepSeek-R1-Distill-Qwen-32B")

def count_output_tokens(text):
    """计算输出文本的精确token数"""
    return count_tokens(text, "DeepSeek-R1-Distill-Qwen-32B")

def count_conversation_tokens(messages):
    """计算对话消息的精确token数"""
    return count_messages_tokens(messages, "DeepSeek-R1-Distill-Qwen-32B")

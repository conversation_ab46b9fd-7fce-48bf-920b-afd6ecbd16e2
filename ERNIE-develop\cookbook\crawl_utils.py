# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at

#     http://www.apache.org/licenses/LICENSE-2.0

# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
CrawlUtils is a class that provides utility methods for web crawling and processing.
"""

import logging
import re

from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig, DefaultMarkdownGenerator, PruningContentFilter


class CrawlUtils:
    """
    Provides web crawling and content extraction utilities with intelligent filtering.
    Features include asynchronous crawling, content pruning, markdown generation,
    and configurable link/media filtering. Uses crawl4ai library for core functionality.
    """

    def __init__(self, page_timeout=30000, max_retries=2):
        """
        Initialize the CrawlUtils instance.

        Args:
            page_timeout (int): Page load timeout in milliseconds (default: 30000)
            max_retries (int): Maximum retry attempts for failed requests (default: 2)
        """
        self.logger = logging.getLogger(__name__)
        self.max_retries = max_retries
        self._crawler = None  # Reuse crawler instance

        # Configure content filter with more lenient settings for better content extraction
        content_filter = PruningContentFilter(threshold=0.1, threshold_type="fixed")
        # Configure markdown generator, apply the above content filter to generate "fit_markdown"
        md_generator = DefaultMarkdownGenerator(content_filter=content_filter)
        # Configure crawler run parameters with more lenient filtering
        self.run_config = CrawlerRunConfig(
            # Configurable page timeout (default 30 seconds)
            page_timeout=page_timeout,
            # More lenient filtering settings
            word_count_threshold=1,  # Reduced from 10 to 1
            excluded_tags=["script", "style", "iframe"],  # Reduced exclusions
            exclude_external_links=False,  # Allow external links
            exclude_internal_links=False,  # Allow internal links
            exclude_social_media_links=True,
            exclude_external_images=True,
            only_text=True,
            # Markdown generation
            markdown_generator=md_generator,
            # Cache
            cache_mode=CacheMode.BYPASS,
            # Additional settings for better content extraction
            wait_until="domcontentloaded",  # Faster than networkidle
            delay_before_return_html=1000,  # Reduced from 2000 to 1000ms
        )

    async def __aenter__(self):
        """Async context manager entry"""
        if not self._crawler:
            self._crawler = AsyncWebCrawler()
            await self._crawler.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self._crawler:
            try:
                await self._crawler.__aexit__(exc_type, exc_val, exc_tb)
            except Exception as e:
                self.logger.warning(f"Error closing crawler: {e}")
            finally:
                self._crawler = None

    async def get_webpage_text(self, url: str) -> str:
        """
        Asynchronously fetches and cleans webpage content from given URL using configured crawler.
        Applies content filtering, markdown conversion, and text cleaning (removing undefined,
        excess whitespace, tabs). Includes retry mechanism for failed requests.

        Args:
            url (str): The URL to retrieve the text from.

        Returns:
            str: The plain text retrieved from the specified URL, or None if all attempts fail.
        """
        last_error = None

        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"Retry attempt {attempt}/{self.max_retries} for {url}")

                # Use reusable crawler or create new one
                if self._crawler:
                    result = await self._crawler.arun(url=url, config=self.run_config)
                else:
                    async with AsyncWebCrawler() as crawler:
                        result = await crawler.arun(url=url, config=self.run_config)

                # Check if result exists
                if not result:
                    raise ValueError("No result returned from crawler")

                # Try different content extraction methods
                webpage_text = None
                extraction_method = "unknown"

                # Method 1: Try fit_markdown (filtered content)
                if result.markdown and result.markdown.fit_markdown and len(result.markdown.fit_markdown.strip()) > 10:
                    webpage_text = result.markdown.fit_markdown
                    extraction_method = "fit_markdown"

                # Method 2: Try raw_markdown (unfiltered content)
                elif result.markdown and result.markdown.raw_markdown and len(result.markdown.raw_markdown.strip()) > 10:
                    webpage_text = result.markdown.raw_markdown
                    extraction_method = "raw_markdown"

                # Method 3: Try cleaned_html
                elif result.cleaned_html and len(result.cleaned_html.strip()) > 10:
                    webpage_text = result.cleaned_html
                    extraction_method = "cleaned_html"

                # Method 4: Try raw HTML as last resort
                elif result.html and len(result.html.strip()) > 100:
                    # Basic HTML to text conversion
                    html_text = re.sub(r'<[^>]+>', '', result.html)
                    html_text = re.sub(r'\s+', ' ', html_text).strip()
                    if len(html_text) > 50:
                        webpage_text = html_text
                        extraction_method = "raw_html_converted"

                if not webpage_text or len(webpage_text.strip()) <= 10:
                    raise ValueError(f"No meaningful content extracted from webpage. Available methods checked: fit_markdown, raw_markdown, cleaned_html, raw_html")

                self.logger.info(f"Successfully extracted {len(webpage_text)} characters from {url} using {extraction_method}")

                # Clean up the text
                cleaned_text = webpage_text.replace("undefined", "")
                cleaned_text = re.sub(r'(\n\s*){3,}', '\n\n', cleaned_text)
                cleaned_text = re.sub(r'[\r\t]', '', cleaned_text)
                cleaned_text = re.sub(r' +', ' ', cleaned_text)
                cleaned_text = re.sub(r'^\s+|\s+$', '', cleaned_text, flags=re.MULTILINE)
                return cleaned_text.strip()

            except Exception as e:
                last_error = e
                self.logger.warning(f"Attempt {attempt + 1} failed for {url}: {str(e)}")

                # If this is not the last attempt, wait before retrying
                if attempt < self.max_retries:
                    import asyncio
                    wait_time = (attempt + 1) * 2  # Progressive backoff: 2s, 4s, 6s...
                    self.logger.info(f"Waiting {wait_time} seconds before retry...")
                    await asyncio.sleep(wait_time)

        # All attempts failed
        self.logger.error(f"All {self.max_retries + 1} attempts failed for {url}. Last error: {last_error}")
        return None

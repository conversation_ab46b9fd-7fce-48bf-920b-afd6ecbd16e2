﻿# 南威软件2023年年度董事会经营评述

**发布日期**: 2024年04月29日

**原文链接**: http://yuanchuang.10jqka.com.cn/20240429/c657423882.shtml

## 📄 原文内容

脛脧脥镁脠铆录镁2023脛锚脛锚露脠露颅脢脗禄谩戮颅脫陋脝脌脢枚
						                    脥卢禄篓脣鲁陆冒脠脷脩脨戮驴脰脨脨脛
隆隆隆隆脪禄隆垄戮颅脫陋脟茅驴枚脤脰脗脹脫毛路脰脦枚
(function(){var obj = null;if(document.getElementById('arctTailMark')) {obj = document.getElementById('arctTailMark');}if(obj!=null){var str = '<a href="http://www.10jqka.com.cn" target="_blank" id="back_web" style="font-weight:bold" rel="nofollow" class="backweb"><img src="http://i.thsi.cn/images/article/logo.jpg" style="height:15px">  路碌禄脴脢脳脪鲁</a><a href="https://t.10jqka.com.cn/m/game/getActivityPageById/?id=295" target="_blank" style="font-weight: bold; font-size: 14px; padding-left: 5px; margin-left: 5px; border-left: 2px solid">戮脵卤篓 &gt;</a>';var showed = 0;try{var oo = obj.previousSibling;while(oo.nodeType!=1 || oo.innerHTML.replace(/\s|\u3000/g,'').length==0){oo = oo.previousSibling;}if(oo.nodeType==1 && oo.tagName.toLowerCase()=='p') {var span = document.createElement('span');span.innerHTML =str;span.style.width = '130px';oo.appendChild(span);showed = 1;}}catch (e){}if(showed == 0){obj.innerHTML =str;}}})();
var hotstocks = new Array();

	        	        hotstocks['000716'] = '000716';

	    	    	        hotstocks['002456'] = '002456';

	    	    	        hotstocks['601216'] = '601216';

	    	    	        hotstocks['603005'] = '603005';

	    	    	        hotstocks['600206'] = '600206';

	    	    	        hotstocks['000795'] = '000795';

	    	    	        hotstocks['000564'] = '000564';

	    	    	        hotstocks['002510'] = '002510';
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄脗貌脠毛脟掳10鹿脡
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄陆酶脡垄禄搂脤脫脟掳10鹿脡
露脌录脪脳脢陆冒拢潞陆帽脠脮脰梅脕娄脗貌陆酶脟掳10鹿脡
CPO赂脜脛卯脮冒碌麓禄脴脡媒 脨脗脪脳脢垄脮脟鲁卢8%
脧没路脩碌莽脳脫赂脜脛卯鹿脡脮冒碌麓脳脽碌脥 脜路路脝鹿芒碌脠露脿鹿脡碌酶鲁卢5%
A鹿脡脠媒麓贸碌莽脨脜脭脣脫陋脡脤录炉脤氓脳脽脟驴 脰脨鹿煤碌莽脨脜脮脟鲁卢5%
脡脧潞拢脢脨脮镁赂庐鲁拢脦帽禄谩脪茅虏驴脢冒拢卢脰搂鲁脰脡脧脢脨鹿芦脣戮虏垄鹿潞脰脴脳茅拢卢脤谩脡媒鹿芦脣戮脰脢脕驴脜脿脫媒脕煤脥路脝贸脪碌
脰脨脨脜脰陇脠炉脨脗脠脦脳脺戮颅脌铆 脢脳麓脦鹿芦驴陋路垄脡霉
GPU露脌陆脟脢脼脛娄露没脧脽鲁脤陆芦鲁氓麓脤IPO拢隆虏脦鹿脡隆垄潞脧脳梅碌脠赂脜脛卯鹿脡脙没碌楼脪禄脌脌
脭酶脴鹿脠潞拢潞脠么脤脴脌脢脝脮隆掳驴陋脗脤碌脝隆卤拢卢脛镁碌脗脢卤麓煤陆芦驴录脗脟赂掳脙脌陆篓鲁搂
赂脮赂脮拢隆脩脟脤芦鹿脡脢脨拢卢脠芦脧脽脤酶脣庐拢隆A50脰卤脧脽路颅脗脤拢卢赂脹鹿脡脡卯碌梅拢隆路垄脡煤脕脣脢虏脙麓
脦脢陆莽M7脢脗鹿脢脨脗陆酶脮鹿拢潞鲁碌脰梅鲁脝隆掳鹿媒脫脷脧脿脨脜脫脿鲁脨露芦虏脜鹿潞脗貌隆卤虏垄脝冒脣脽禄陋脦陋
脌毛隆掳虏禄脗么戮脥陆没隆卤脰禄脢拢脕陆赂枚露脿脭脗拢隆脤脴脌脢脝脮脥脜露脫拢潞陆芦露脪脧脰鲁脨脜碌 脮眉戮脠TikTok
脡卯陆禄脣霉拢潞11脭脗16脠脮陆芦陆酶脨脨脡卯脢脨陆禄脪脳脧碌脥鲁虏芒脢脭
		var s = "_" + Math.random().toString(36).slice(2);
		document.write('<div id="' + s + '"></div>');
		(window.slotbydup=window.slotbydup || []).push({
			display: 'inlay-fix'
document.addEventListener('DOMContentLoaded', function () {
      var userid = 'nouser';
        // 忙拢聙忙聼楼 Vue 氓聮聦 AdvertCommon 忙聵炉氓聬娄氓颅聵氓聹篓
        if (Vue && window['mobileweb_AdvertCommon@1.0.4'].default) {
          Vue.use(window['mobileweb_AdvertCommon@1.0.4'].default);
          // 氓掳聺猫炉聲盲禄聨 cookie 盲赂颅忙聫聬氓聫聳 userid
          var match = document.cookie.match(/userid=([^;]*)/);
          if (match) {
            userid = match[1] || 'nouser';
      } catch (error) {
        console.error(error);
      // 氓聢聺氓搂聥氓聦聳 Vue 氓庐聻盲戮聥
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
脡忙脦麓鲁脡脛锚脠脣脦楼鹿忙脛脷脠脻戮脵卤篓
虏禄脕录脨脜脧垄戮脵卤篓碌莽禄掳拢潞(0571)88933003
戮脵卤篓脫脢脧盲拢潞*****************
脮茫陆颅脥卢禄篓脣鲁禄楼脕陋脨脜脧垄录录脢玫脫脨脧脼鹿芦脣戮掳忙脠篓脣霉脫脨
var newsid = '657423882';
    var id='c_657423882';
	var initctime = '1714392713';
	var stitle = encodeURIComponent('脛脧脥镁脠铆录镁2023脛锚脛锚露脠露颅脢脗禄谩戮颅脫陋脝脌脢枚');
	var artStock = new Array();
	var cotitle = '脛脧脥镁脠铆录镁2023脛锚脛锚露脠露颅脢脗禄谩戮颅脫陋脝脌脢枚';
	var codata = cocontent.replace(/<[^>]*>/g, '').substr(0,250);
	var courl = 'http://yuanchuang.10jqka.com.cn/20240429/c657423882.shtml';
		artStock[0] = '603636';
        newRobotRecList : [],
		cid: ''  //脳卯潞贸脪禄脤玫脝脌脗脹碌脛ID
	ARTINFO.tday = $.timeFormat(ARTINFO.tday, 'yyyyMMddHHmm');
	ARTINFO.tday = parseInt(ARTINFO.tday);
	ARTINFO.userid = WEB.basic.getUserid();
$(document).ready(function(){
	    	    TA.log({id:id, ld:'browser',fid:'info_gather,zx_n_all,zx_wzb_total,zx_ll_984,zxych,ch_yuanchuang', pt:2,_sid:'imgzx'});
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?78c58f01938e4d85eaf619eae71b4ed1";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?f79b64788a4e377c608617fba4c736e2";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
// 麓娄脌铆脥录脝卢src脦陋undefined
  var imageDomList = document.querySelectorAll('img');
  if (imageDomList.length > 0) {
      imageDomList.forEach(ele => {
          var imgSrc = ele.src;
          if (imgSrc.endsWith('/undefined')) {
              ele.remove();
  var srcVideoDom = document.querySelector('input.video-insert');
    var videoUrl = srcVideoDom.getAttribute('data-src');
    var videoThumb = srcVideoDom.getAttribute('data-thumb');
    var videoDom = document.createElement('video');
    var parent = srcVideoDom.parentNode;
    videoDom.src = videoUrl;
    videoDom.poster = videoThumb;
    videoDom.setAttribute("controls", true);
    videoDom.setAttribute("style", "width:627px;height:353px");
    parent.insertBefore(videoDom, srcVideoDom);
    srcVideoDom.remove();
  $('.self-stock-con').css({'height':'206px'})
// 脧脼脰脝碌脛禄煤脝梅脠脣脳茅录镁掳脳脙没碌楼
    const LIMIT_ROBOT_VISUAL_COMS = [
        'thsf2e_common-long-hu-rank',
        'thsf2e_ai-table'
    const checkLimitCom = (dataType) => {
        return LIMIT_ROBOT_VISUAL_COMS.includes(dataType);
    // 脕煤禄垄掳帽脤脴脢芒麓娄脌铆
    const LONGHU_TYPE = 'thsf2e_common';
    // 禄帽脠隆dom陆脷碌茫脨脜脧垄
    const getDomAttrData = (robotParse) => {
        const dataType = robotParse.getAttribute('data-type');
        const dataProps = robotParse.getAttribute('data-props');
        const dataComurl = robotParse.getAttribute('data-comurl');
        const dataStyle = robotParse.getAttribute('data-style');
        const dataVersion = robotParse.getAttribute('data-version');
            dataType,
            dataProps,
            dataComurl,
            dataStyle,
            dataVersion,
    const loadScript = (url, callback) => {
        const originalDefine = window.define;
        window.define = undefined;
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.charset = 'utf-8';
        script.src = url;
        script.onload = function() {
            callback(null)
        script.onerror = function() {
            callback(`Failed to load script: ${url}`);
        document.body.appendChild(script);
    // 脜脨露脧window脌茂脢脟路帽脫脨脮芒赂枚脳茅录镁拢卢脠莽鹿没脫脨戮脥脰卤陆脫脢鹿脫脙拢卢脠莽鹿没脙禄脫脨戮脥录脫脭脴
    const getComponent = (id, url, callback) => {
        return new Promise((resolve, reject) => {
            if (window[id]) {
                return resolve(window[id].default)
            loadScript(url, (err) => {
                if (err || !window[id]) {
                    reject(err || '脳茅录镁id麓铆脦贸')
                } else {
                    resolve(window[id].default)
    // 陆芒脦枚禄煤脝梅脠脣脛脷脠脻
    const parseRobot = (ele) => {
        const { dataType, dataProps, dataComurl, dataStyle, dataVersion } = getDomAttrData(ele);
        const props = JSON.parse(decodeURIComponent(dataProps));
        const style = JSON.parse(decodeURIComponent(dataStyle));
        // 脜脨露脧脢脟路帽虏禄脭脷limit盲脰脠戮脰脨
        if (!checkLimitCom(dataType)) {
        const id = `${dataType}_index@${dataVersion}`;
        getComponent(id, dataComurl).then((com) => {
            const placeholder = document.createElement('div');
            placeholder.className = 'robot-visual-container';
            ele.replaceWith(placeholder);
            const comInstance = new Vue({
                el: placeholder,
                render: h => h('div', { attr: { id: 'robotVisual' }, style }, [
                    h(com, { props })
            }).$mount();
        }).catch(err => {
            console.error(err)
    const parseContentMain = () => {
        const eles = document.getElementById('contentApp').querySelectorAll('robot-parse');
        eles.forEach(ele => {
            parseRobot(ele)
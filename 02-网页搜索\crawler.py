import sys
import urllib.request
import requests
from urllib.parse import quote
from lxml import etree
import random
import time
import os
import re
import chardet
from fake_useragent import UserAgent
from datetime import datetime, timedelta
import logging
import queue
import json
from ai_service import AIService

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler(stream=sys.stdout)
    ]
)

BAIDU_COOKIE = "BIDUPSID=88389C77C7B393432107779033EF2C2F; PSTM=1700540987; BDUSS=2hkb2ZUUDdaWWlQUGlHemJlY0tiR0JTYkludlFRcTBEOUtVZ25hU2NyRFY4bXhtRVFBQUFBJCQAAAAAAQAAAAEAAADkocCKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANVlRWbVZUVmT; BDUSS_BFESS=2hkb2ZUUDdaWWlQUGlHemJlY0tiR0JTYkludlFRcTBEOUtVZ25hU2NyRFY4bXhtRVFBQUFBJCQAAAAAAQAAAAEAAADkocCKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANVlRWbVZUVmT; BAIDUID=04E1D6B0673AD509E04403C753B52528:FG=1; H_WISE_SIDS=60277_61027_61442_60853_61491_61429_61527_61523_61534_61563_61575_61607_61657_61635; BD_UPN=12314753; BAIDUID_BFESS=04E1D6B0673AD509E04403C753B52528:FG=1; ZFY=tscJEE0EVGCSNaThhqsbMoavPR2R0ILAggblVRV7pQ0:C; BDRCVFR[TD7X-Sygd1t]=R7sKU0eflJ_pA7EP1DdQhPEUf; H_PS_PSSID=60277_61027_62325_62338_62869_62878_62882_62886_62926_62968_62956_63040_63044_63032; BA_HECTOR=a480250l2la02g2l802g0581au4frq1k0hl0g22; H_WISE_SIDS_BFESS=60277_61027_61442_60853_61491_61429_61527_61523_61534_61563_61575_61607_61657_61635; BDRCVFR[S4-dAuiWMmn]=sAawziRpLOtfj6dnjTsnWnkg1mLgv99; delPer=0; BD_CK_SAM=1; PSINO=7; H_PS_645EC=60e6%2BWU3KtS9KJ%2F3LMUMf0CwPinjz8AeLeJ08Aexjei%2BsmoQrkmX6Hx314ot8jxbhw; BDORZ=B490B5EBF6F3CD402E515D22BCDA1598; BDSVRTM=0; ab_sr=1.0.1_ZDRiMDhjNTFhM2VmYTcwMjI3MGQxYTQ2MTQ1MzAzMGQ1YzE3NGI5ZmVmZGE3MGY2NjQ4ZDc0NjEwOGMwMDIyYjU2MWDxY2FhNTJhNjdmMmJjMGRkMThiMTZiMGNhMDgzNzU4YzBmZmUxOTg4NGZkZDA0MjA8NTU0MzRhY2M2YzdkNTU3MTJkMmNiODExMjIyNjUzY2VkODFlZTQ0MjMyNg=="


class WebCrawler:
    def __init__(self, message_queue=None, user_id=None):
        self.message_queue = message_queue
        self.counter = 1
        self.success_count = 0
        self.total_links = 0
        self.ai_service = AIService(user_id=user_id)
        self.enable_ai_summary = True  # 是否启用AI总结
        self.enable_question_answer = True  # 是否启用问题回答
        self.collected_articles = []  # 收集的文章列表
        self.user_id = user_id
        
    def send_message(self, message_type, **kwargs):
        """发送消息到队列"""
        if self.message_queue:
            if message_type is None:
                # 发送完成信号
                self.message_queue.put(None)
            else:
                message = {'type': message_type, **kwargs}
                self.message_queue.put(message)
    
    def clean_filename(self, title: str) -> str:
        """清洗文件名中的非法字符，保留中文和常用标点"""
        cleaned_title = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\-—，。、；：？！（）《》]', '', title.strip())
        cleaned_title = re.sub(r'\s+', ' ', cleaned_title)
        cleaned_title = cleaned_title.strip()
        return cleaned_title[:150]

    def contains_garbled_text(self, text: str) -> bool:
        """检测文本是否包含乱码"""
        if not text:
            return False

        # 检查是否包含典型的乱码字符
        garbled_patterns = [
            r'[À-ÿ]{3,}',  # 连续的拉丁扩展字符
            r'Â[^\s]{2,}',  # 以Â开头的乱码
            r'[ÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]{2,}',
            r'È[^\s]{2,}',  # 以È开头的乱码（常见于GBK误解码）
            r'Ã[^\s]{2,}',  # 以Ã开头的乱码
            r'[ÒªÎÅ£º]{2,}',  # 特定的乱码组合
            r'[ÃÀ¹ú¿¼ÂÇ]{3,}',  # 另一种常见乱码模式
        ]

        for pattern in garbled_patterns:
            if re.search(pattern, text):
                return True

        # 检查中文字符比例，如果太少可能是乱码
        chinese_chars = len(re.findall(r'[\u4e00-\u9fa5]', text))
        total_chars = len(text.replace(' ', ''))
        if total_chars > 10 and chinese_chars / total_chars < 0.1:
            return True

        return False

    def fix_encoding_issues(self, text: str, raw_content: bytes) -> str:
        """尝试修复编码问题"""
        if not text or not raw_content:
            return text

        # 尝试不同的编码组合来修复
        encoding_pairs = [
            ('latin1', 'gbk'),
            ('latin1', 'gb2312'),
            ('latin1', 'gb18030'),
            ('iso-8859-1', 'gbk'),
            ('iso-8859-1', 'gb2312'),
            ('iso-8859-1', 'gb18030'),
            ('utf-8', 'gbk'),
            ('utf-8', 'gb2312'),
            ('cp1252', 'gbk'),  # Windows-1252 到 GBK
            ('cp1252', 'gb2312'),
        ]

        # 首先尝试编码转换修复
        for wrong_encoding, correct_encoding in encoding_pairs:
            try:
                # 先用错误编码编码，再用正确编码解码
                fixed_text = text.encode(wrong_encoding).decode(correct_encoding)
                if not self.contains_garbled_text(fixed_text):
                    logging.info(f"编码修复成功: {wrong_encoding} -> {correct_encoding}")
                    return fixed_text
            except (UnicodeEncodeError, UnicodeDecodeError, LookupError):
                continue

        # 如果编码转换失败，尝试直接从原始内容重新解码
        if raw_content:
            for encoding in ['gbk', 'gb2312', 'gb18030', 'big5', 'utf-8']:
                try:
                    fixed_text = raw_content.decode(encoding, errors='ignore')
                    # 提取标题部分进行比较
                    if '<title>' in fixed_text.lower():
                        title_match = re.search(r'<title[^>]*>(.*?)</title>', fixed_text, re.IGNORECASE | re.DOTALL)
                        if title_match:
                            title_text = title_match.group(1).strip()
                            if not self.contains_garbled_text(title_text) and len(title_text) > 5:
                                logging.info(f"从原始内容修复编码成功: {encoding}")
                                return title_text
                except (UnicodeDecodeError, LookupError):
                    continue

        return text

    def parse_publish_date(self, tree, date_tags, fallback_date=""):
        """从网页中解析发布日期"""
        
        def extract_date(raw_date):
            """从原始日期字符串中提取日期部分"""
            raw_date = re.sub(r'[\s\xa0]+', ' ', raw_date.strip())
            patterns = [
                r'(\d{4})年(\d{1,2})月(\d{1,2})日',
                r'(\d{4})-(\d{1,2})-(\d{1,2})',
                r'(\d{4})\.(\d{1,2})\.(\d{1,2})',
                r'(\d{4})/(\d{1,2})/(\d{1,2})',
                r'发布于\s*(\d{4})[年\-\./](\d{1,2})[月\-\./](\d{1,2})',
                r'更新于\s*(\d{4})[年\-\./](\d{1,2})[月\-\./](\d{1,2})',
                r'(\d{1,2})月(\d{1,2})日',
                r'(\d{4})年(\d{1,2})月',
                r'(\d{1,2})月(\d{1,2})日\s+(\d{4})',
                r'(\d{4})年(\d{1,2})月(\d{1,2})',
                r'(\d{4})-(\d{1,2})-(\d{1,2})\s+\d{2}:\d{2}:\d{2}',
                r'(\d{4})/(\d{1,2})/(\d{1,2})\s+\d{2}:\d{2}:\d{2}',
            ]
            for pattern in patterns:
                if match := re.search(pattern, raw_date):
                    groups = match.groups()
                    if len(groups) >= 3:
                        year, month, day = groups[-3], groups[-2], groups[-1]
                    elif len(groups) == 2:
                        now = datetime.now()
                        year = str(now.year)
                        month, day = groups[0], groups[1]
                    else:
                        now = datetime.now()
                        year = str(now.year)
                        month, day = groups[0], "01"
                    if len(year) == 2:
                        year = f"20{year}"
                    try:
                        datetime.strptime(f"{year}-{month}-{day}", "%Y-%m-%d")
                        return f"{year}年{month.zfill(2)}月{day.zfill(2)}日"
                    except ValueError:
                        continue
            return None

        def handle_relative_date(raw_date):
            """处理相对日期"""
            now = datetime.now()
            raw_date = raw_date.lower()
            relative_patterns = [
                (r'(\d+)\s*小时前', 'hours'),
                (r'(\d+)\s*分钟前', 'minutes'),
                (r'(\d+)\s*天前', 'days'),
                (r'昨天', 'yesterday'),
                (r'今天|刚刚', 'today')
            ]
            for pattern, typ in relative_patterns:
                if match := re.search(pattern, raw_date):
                    if typ == 'hours':
                        hours_ago = int(match.group(1))
                        return (now - timedelta(hours=hours_ago)).strftime("%Y年%m月%d日")
                    elif typ == 'minutes':
                        minutes_ago = int(match.group(1))
                        return (now - timedelta(minutes=minutes_ago)).strftime("%Y年%m月%d日")
                    elif typ == 'days':
                        days_ago = int(match.group(1))
                        return (now - timedelta(days=days_ago)).strftime("%Y年%m月%d日")
                    elif typ == 'yesterday':
                        return (now - timedelta(days=1)).strftime("%Y年%m月%d日")
                    elif typ == 'today':
                        return now.strftime("%Y年%m月%d日")
            return None

        for tag in date_tags:
            try:
                if dates := tree.xpath(tag):
                    raw_date = dates[0].strip()
                    if relative_date := handle_relative_date(raw_date):
                        return relative_date
                    if standard_date := extract_date(raw_date):
                        return standard_date
                    if " " in raw_date:
                        date_part = raw_date.split()[0]
                        if standard_date := extract_date(date_part):
                            return standard_date
            except Exception as e:
                logging.warning(f"日期解析异常: {str(e)}")
                continue

        if fallback_date:
            if relative_date := handle_relative_date(fallback_date):
                return relative_date
            if standard_date := extract_date(fallback_date):
                return standard_date

        return datetime.now().strftime("%Y年%m月%d日")

    def extract_keywords_from_question(self, question: str) -> list:
        """从用户问题中提取多个搜索关键词"""
        import re

        # 更全面的停用词列表
        stop_words = [
            '在哪', '在哪里', '哪里', '什么', '怎么', '如何', '为什么', '是什么',
            '有什么', '有哪些', '怎样', '多少', '几个', '哪个', '哪些', '我该',
            '应该', '可以', '能够', '需要', '想要', '希望', '请问', '请告诉',
            '的', '了', '吗', '呢', '吧', '啊', '呀', '嘛', '？', '?', '，', ',',
            '和', '与', '或者', '还是', '以及', '关于', '对于', '使用', '操作'
        ]

        keywords_list = []

        # 1. 识别核心实体（专有名词）
        # 匹配中文专有名词（2-8个字符，避免过长）
        chinese_entities = re.findall(r'[\u4e00-\u9fa5]{2,8}', question)
        core_entities = []
        for entity in chinese_entities:
            if entity not in stop_words and len(entity) >= 2:
                # 过滤常见词汇
                if not any(stop in entity for stop in ['什么', '怎么', '如何', '哪里', '关系']):
                    core_entities.append(entity)

        # 2. 特殊模式识别 - "A和B的关系"
        relation_patterns = [
            r'([\u4e00-\u9fa5a-zA-Z]{2,8})和([\u4e00-\u9fa5a-zA-Z]{2,8}).*?关系',
            r'([\u4e00-\u9fa5a-zA-Z]{2,8})与([\u4e00-\u9fa5a-zA-Z]{2,8}).*?关系',
            r'([\u4e00-\u9fa5a-zA-Z]{2,8}).*?([\u4e00-\u9fa5a-zA-Z]{2,8}).*?关系'
        ]

        entities_from_relation = []
        for pattern in relation_patterns:
            matches = re.findall(pattern, question)
            for match in matches:
                entity1, entity2 = match
                if entity1 not in stop_words and entity2 not in stop_words:
                    entities_from_relation.extend([entity1, entity2])
                    # 添加组合搜索词
                    keywords_list.append(f"{entity1} {entity2}")
                    keywords_list.append(f"{entity1}和{entity2}")
                    break  # 找到一个匹配就够了

        # 3. 构建搜索关键词
        all_entities = list(set(core_entities + entities_from_relation))

        # 添加单个实体作为关键词
        for entity in all_entities:
            if len(entity) >= 2:
                keywords_list.append(entity)

        # 4. 添加问题中的核心概念组合
        if len(all_entities) >= 2:
            # 取前两个最重要的实体组合
            main_entities = all_entities[:2]
            keywords_list.append(' '.join(main_entities))

        # 5. 如果是使用类问题，添加使用相关的搜索词
        if any(word in question for word in ['如何使用', '怎么使用', '使用方法', '使用教程']):
            for entity in all_entities:
                keywords_list.append(f"{entity} 使用")
                keywords_list.append(f"{entity} 教程")

        # 6. 去重并排序（按长度和重要性）
        unique_keywords = []
        for keyword in keywords_list:
            keyword = keyword.strip()
            if keyword and len(keyword) >= 2 and keyword not in unique_keywords:
                unique_keywords.append(keyword)

        # 按关键词质量排序（优先选择包含核心实体的关键词）
        def keyword_score(kw):
            score = 0
            # 包含核心实体的关键词得分更高
            for entity in all_entities:
                if entity in kw:
                    score += len(entity) * 2
            # 较短的关键词得分更高（更精准）
            score += max(0, 10 - len(kw.split()))
            return score

        unique_keywords.sort(key=keyword_score, reverse=True)

        # 最多返回前5个最相关的关键词
        final_keywords = unique_keywords[:5]

        # 如果没有提取到有效关键词，使用原问题
        if not final_keywords:
            final_keywords = [question]

        logging.info(f"从问题 '{question}' 提取关键词: {final_keywords}")
        return final_keywords

    def calculate_relevance_score(self, title: str, content: str, user_question: str, search_keywords: list) -> float:
        """计算文章与用户问题的相关性得分"""
        score = 0.0

        # 将所有文本转为小写进行比较
        title_lower = title.lower()
        content_lower = content.lower()
        question_lower = user_question.lower()

        # 提取问题中的核心实体
        import re
        core_entities = re.findall(r'[\u4e00-\u9fa5a-zA-Z]{2,8}', user_question)
        core_entities = [entity for entity in core_entities if entity not in ['什么', '怎么', '如何', '哪里', '在哪', '关系', '使用', '我该']]

        # 1. 核心实体匹配（最高权重）
        entity_score = 0
        for entity in core_entities:
            entity_lower = entity.lower()
            # 标题中出现核心实体
            if entity_lower in title_lower:
                entity_score += 20  # 标题中的核心实体权重很高
            # 内容中出现核心实体
            content_count = content_lower.count(entity_lower)
            if content_count > 0:
                entity_score += min(content_count * 5, 15)  # 内容中的核心实体，但有上限
        score += entity_score

        # 2. 搜索关键词匹配
        keyword_score = 0
        for keyword in search_keywords:
            keyword_lower = keyword.lower()
            # 完整关键词在标题中出现
            if keyword_lower in title_lower:
                keyword_score += len(keyword.split()) * 8  # 多词关键词权重更高
            # 关键词在内容中出现
            content_count = content_lower.count(keyword_lower)
            if content_count > 0:
                keyword_score += min(content_count * 2, 8)
        score += keyword_score

        # 3. 标题质量评估
        title_quality_score = 0
        # 标题长度合理性
        if 5 <= len(title) <= 100:
            title_quality_score += 2
        # 标题包含问号（可能是相关问题）
        if '?' in title or '？' in title:
            title_quality_score += 3
        # 标题包含关键动词
        action_words = ['使用', '操作', '教程', '方法', '介绍', '说明', '指南']
        for word in action_words:
            if word in title:
                title_quality_score += 2
                break
        score += title_quality_score

        # 4. 内容质量评估
        content_quality_score = 0
        # 内容长度合理性
        if len(content) > 500:
            content_quality_score += 3
        elif len(content) > 200:
            content_quality_score += 1
        # 内容结构性（包含常见的结构词汇）
        structure_words = ['首先', '其次', '然后', '最后', '步骤', '方法', '注意']
        structure_count = sum(1 for word in structure_words if word in content)
        content_quality_score += min(structure_count, 3)
        score += content_quality_score

        # 5. 负面因素惩罚
        penalty = 0
        # 标题包含明显不相关的内容
        irrelevant_patterns = ['乱码', '测试', '404', '错误', '加载失败', '广告', '推广']
        for pattern in irrelevant_patterns:
            if pattern in title_lower:
                penalty += 10

        # 内容过短可能是无效内容
        if len(content) < 100:
            penalty += 5

        # 标题过于通用
        generic_patterns = ['新闻', '资讯', '公告', '通知']
        for pattern in generic_patterns:
            if pattern in title and len(title) < 20:
                penalty += 3

        score = max(0, score - penalty)

        # 6. 基础分数（确保有一定的基础分）
        score += 1.0

        return score

    def filter_relevant_articles(self, articles: list, user_question: str, search_keywords: list, min_score: float = 5.0) -> list:
        """过滤出与问题相关的文章"""
        scored_articles = []

        for article in articles:
            title = article.get('title', '')
            content = article.get('content', '')

            # 计算相关性得分
            score = self.calculate_relevance_score(title, content, user_question, search_keywords)

            if score >= min_score:
                article['relevance_score'] = score
                scored_articles.append(article)
                logging.info(f"文章相关性得分: {score:.1f} - {title[:50]}...")
            else:
                logging.info(f"文章相关性过低({score:.1f})，已过滤: {title[:50]}...")

        # 按相关性得分排序
        scored_articles.sort(key=lambda x: x['relevance_score'], reverse=True)

        # 最多保留前10篇最相关的文章
        return scored_articles[:10]

    def init_session(self):
        """初始化带Cookie的Session"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': UserAgent().random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Referer': 'https://www.baidu.com/',
            'Connection': 'keep-alive'
        })
        cookie_dict = {cookie.split('=')[0]: cookie.split('=')[1]
                       for cookie in BAIDU_COOKIE.split('; ')}
        session.cookies.update(cookie_dict)
        return session

    def get_article_content(self, session, url, fallback_date, keyword, retry=3):
        """获取单篇文章内容"""
        title_tags = [
            '//h1[not(contains(@class,"hidden"))]//text()',
            '//title/text()',
            '//meta[@property="og:title"]/@content',
            '//div[contains(@class,"title")]//text()',
            '//h2[contains(@class,"title")]//text()',
        ]
        content_tags = [
            '//div[@class="article-content"]//p | //div[@class="article-content"]//div',
            '//div[contains(@class,"article-content")]//p | //div[contains(@class,"article-content")]//div',
            '//div[contains(@class,"content")]//p | //div[contains(@class,"content")]//div',
            '//div[contains(@class,"article-body")]//p | //div[contains(@class,"article-body")]//div',
            '//article//p | //article//div',
            '//div[contains(@class,"article")]//p | //div[contains(@class,"article")]//div',
            '//div[contains(@class,"text")]//p | //div[contains(@class,"text")]//div',
            '//div[contains(@class,"main-content")]//p | //div[contains(@class,"main-content")]//div',
            '//div[contains(@class,"content-main")]//p | //div[contains(@class,"content-main")]//div',
        ]
        date_tags = [
            '//span[@class="date"]/text()',
            '//div[contains(@class,"time")]//text()',
            '//span[contains(@class,"date")]//text()',
            '//meta[@property="article:published_time"]/@content',
            '//meta[@name="publishdate"]/@content',
            '//meta[@name="pubdate"]/@content',
            '//span[contains(@class,"time")]//text()',
            '//div[contains(@class,"publish-time")]//text()',
        ]

        for attempt in range(retry):
            try:
                response = session.get(url, timeout=15, allow_redirects=True)
                logging.info(f"{self.counter}| 请求: {response.url} | 状态码: {response.status_code}")

                if response.status_code != 200:
                    logging.warning(f"请求失败: {response.status_code}")
                    if response.status_code == 403:
                        time.sleep(random.uniform(10, 20))
                    continue

                # 改进编码检测和处理
                html_content = None

                # 首先尝试从HTTP头获取编码
                content_type = response.headers.get('content-type', '').lower()
                charset_from_header = None
                if 'charset=' in content_type:
                    charset_from_header = content_type.split('charset=')[1].split(';')[0].strip()

                # 使用chardet检测编码
                detected_encoding = chardet.detect(response.content)['encoding']

                # 构建编码尝试列表，优先级从高到低
                encoding_candidates = []

                # 1. HTTP头中的编码
                if charset_from_header:
                    encoding_candidates.append(charset_from_header)

                # 2. chardet检测的编码
                if detected_encoding:
                    encoding_candidates.append(detected_encoding)

                # 3. 常见中文编码
                encoding_candidates.extend(['utf-8', 'gbk', 'gb2312', 'gb18030'])

                # 4. 其他编码
                encoding_candidates.extend(['latin1', 'iso-8859-1'])

                # 去重并保持顺序
                seen = set()
                unique_encodings = []
                for enc in encoding_candidates:
                    if enc and enc.lower() not in seen:
                        seen.add(enc.lower())
                        unique_encodings.append(enc)

                # 尝试解码
                for encoding in unique_encodings:
                    try:
                        html_content = response.content.decode(encoding, errors='strict')
                        logging.info(f"成功使用编码 {encoding} 解析内容")
                        break
                    except (UnicodeDecodeError, LookupError):
                        continue

                # 如果所有编码都失败，使用utf-8并忽略错误
                if not html_content:
                    html_content = response.content.decode('utf-8', errors='replace')
                    logging.warning(f"所有编码尝试失败，使用UTF-8替换错误字符")

                tree = etree.HTML(html_content)

                # 提取标题
                title = ""
                for tag in title_tags:
                    try:
                        if titles := tree.xpath(tag):
                            title = ' '.join(t.strip() for t in titles if t.strip())
                            if title:
                                # 检查标题是否包含乱码，如果有则尝试修复
                                if self.contains_garbled_text(title):
                                    fixed_title = self.fix_encoding_issues(title, response.content)
                                    if fixed_title and not self.contains_garbled_text(fixed_title):
                                        title = fixed_title
                                        logging.info(f"> 修复编码后的标题: {title[:50]}...")
                                    else:
                                        logging.warning(f"> 标题包含乱码，无法修复: {title[:50]}...")
                                else:
                                    logging.info(f"> 提取标题: {title[:50]}...")
                                break
                    except Exception as e:
                        logging.warning(f"标题提取异常: {str(e)}")
                        continue

                # 提取正文 - 改进版本
                content = ""
                for tag in content_tags:
                    try:
                        if paragraphs := tree.xpath(tag):
                            content_parts = []
                            for p in paragraphs:
                                # 更好地处理文本提取
                                text_parts = []
                                for text_node in p.itertext():
                                    clean_text = text_node.strip()
                                    if clean_text:
                                        text_parts.append(clean_text)

                                text = ' '.join(text_parts).strip()

                                # 过滤条件优化
                                if text and len(text) > 15:  # 增加最小长度要求
                                    # 更全面的过滤规则
                                    filter_words = [
                                        "广告", "版权", "©", "免责声明", "转载", "来源",
                                        "编辑", "责编", "window.", "function", "var ",
                                        "document.", "script", "style", "onclick"
                                    ]
                                    if not any(word in text for word in filter_words):
                                        # 检查是否包含有意义的中文内容
                                        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
                                        if chinese_chars > 5:  # 至少包含5个中文字符
                                            content_parts.append(text)

                            if content_parts:
                                content = "\n\n".join(content_parts)  # 使用双换行分隔段落
                                if len(content) > 100:
                                    logging.info(f"> 正文长度: {len(content)}字符")
                                    break
                    except Exception as e:
                        logging.warning(f"正文提取异常: {str(e)}")
                        continue

                # 备用内容提取方法
                if not content or len(content) < 100:
                    logging.info("尝试备用内容提取方法...")
                    body_text = tree.xpath('//body//text()')
                    if body_text:
                        content = '\n'.join(t.strip() for t in body_text if t.strip())
                        content = '\n'.join(line for line in content.split('\n')
                                            if len(line) > 20 and not any(
                            w in line.lower() for w in ['版权', '免责', '©', '广告']))

                # 检查并修复内容编码问题
                if content and self.contains_garbled_text(content):
                    fixed_content = self.fix_encoding_issues(content, response.content)
                    if fixed_content and not self.contains_garbled_text(fixed_content):
                        content = fixed_content
                        logging.info(f"> 修复内容编码问题")
                    else:
                        logging.warning(f"> 内容包含乱码，无法修复")

                # 解析发布日期
                pub_date = self.parse_publish_date(tree, date_tags, fallback_date)
                logging.info(f"> 发布日期: {pub_date}")

                # 验证数据有效性
                if not title:
                    logging.warning("! 标题为空，将重试")
                    continue
                if not content or len(content) < 50:
                    logging.warning("! 正文内容不足，将重试")
                    continue

                return title, content, pub_date, response.url

            except requests.exceptions.RequestException as e:
                logging.warning(f"! 请求异常: {str(e)}")
                if attempt < retry - 1:
                    sleep_time = random.uniform(5, 10)
                    logging.info(f"等待 {sleep_time:.1f}秒后重试...")
                    time.sleep(sleep_time)
                continue
            except Exception as e:
                logging.error(f"! 处理异常: {str(e)}")
                if attempt < retry - 1:
                    sleep_time = random.uniform(3, 8)
                    logging.info(f"等待 {sleep_time:.1f}秒后重试...")
                    time.sleep(sleep_time)
                continue
            finally:
                time.sleep(random.uniform(2, 5))
                self.counter += 1

        return None, None, None, None

    def fetch_baidu_links(self, key_word: str, pages=3):
        """从百度搜索获取结果链接"""
        encoded_word = quote(key_word, encoding='utf-8')
        all_links = []
        all_dates = []
        session = self.init_session()

        for page in range(pages):
            url = f'https://www.baidu.com/s?tn=news&rtt=4&bsst=1&cl=2&wd={encoded_word}&pn={page * 10}&medium=0'
            logging.info(f"\n正在抓取第 {page + 1} 页: {url}")

            # 发送进度更新
            progress = (page / pages) * 30  # 搜索链接占总进度的30%
            self.send_message('progress',
                            progress=progress,
                            message=f'正在搜索第 {page + 1}/{pages} 页...')

            try:
                response = session.get(url, timeout=15)
                tree = etree.HTML(response.text)
                links = tree.xpath('//div[contains(@class,"result-op")]//h3/a/@href')
                dates = tree.xpath('//div[contains(@class,"result-op")]//span[contains(@class,"c-color-gray2")]/text()')

                valid_links = []
                valid_dates = []
                for link, date in zip(links, dates):
                    if link.startswith(('http://', 'https://')):
                        valid_links.append(link)
                        valid_dates.append(date.strip())

                all_links.extend(valid_links)
                all_dates.extend(valid_dates)
                logging.info(f"本页获取 {len(valid_links)} 条有效结果")

                # 更新统计信息
                self.total_links = len(all_links)
                self.send_message('stats', stats={
                    'total_links': self.total_links,
                    'success_count': self.success_count,
                    'current_page': page + 1,
                    'total_pages': pages
                })

                time.sleep(random.uniform(3, 6))
            except Exception as e:
                logging.error(f"! 页面抓取失败: {str(e)}")
                continue

        return all_links, all_dates

    def save_article(self, keyword, title, content, pub_date, url):
        """保存文章到对应日期的文件夹，包含AI总结"""
        if not title or not content:
            logging.warning("! 标题或内容为空，跳过保存")
            return False

        try:
            # 创建以问题命名的根目录（不再创建日期子目录）
            root_dir = self.clean_filename(keyword)
            os.makedirs(root_dir, exist_ok=True)

            # 直接在根目录下保存文件
            safe_title = self.clean_filename(title)
            filename = f"{safe_title}_{pub_date}.md"
            filepath = os.path.join(root_dir, filename)

            # 强行覆盖已存在的文件
            if os.path.exists(filepath):
                os.remove(filepath)

            # 生成AI总结
            ai_summary = None
            if self.enable_ai_summary:
                try:
                    logging.info("正在生成AI总结...")
                    ai_result = self.ai_service.summarize_article(title, content, url)
                    if ai_result and ai_result.get('success'):
                        ai_summary = ai_result.get('summary')
                        logging.info("AI总结生成成功")
                    else:
                        logging.warning("AI总结生成失败")
                except Exception as e:
                    logging.error(f"AI总结生成异常: {str(e)}")

            # 写入文件内容
            with open(filepath, 'w', encoding='utf-8-sig') as f:
                f.write(f"# {title}\n\n")
                f.write(f"**发布日期**: {pub_date}\n\n")
                f.write(f"**原文链接**: {url}\n\n")

                # 添加AI总结部分
                if ai_summary:
                    f.write("---\n\n")
                    f.write("## 🤖 AI智能总结\n\n")
                    f.write(f"{ai_summary}\n\n")
                    f.write("---\n\n")

                f.write("## 📄 原文内容\n\n")
                f.write(content)

            logging.info(f"> 已保存到: {filepath}")
            return True, ai_summary
        except Exception as e:
            logging.error(f"! 文件保存失败: {str(e)}")
            return False, None

    def save_article_simple(self, keyword, title, content, pub_date, url):
        """简化的文章保存方法，不生成单篇AI总结"""
        if not title or not content:
            logging.warning("! 标题或内容为空，跳过保存")
            return False

        try:
            # 创建以问题命名的根目录（不再创建日期子目录）
            root_dir = self.clean_filename(keyword)
            os.makedirs(root_dir, exist_ok=True)

            # 直接在根目录下保存文件
            safe_title = self.clean_filename(title)
            filename = f"{safe_title}_{pub_date}.md"
            filepath = os.path.join(root_dir, filename)

            # 强行覆盖已存在的文件
            if os.path.exists(filepath):
                os.remove(filepath)

            # 写入文件内容（不包含AI总结）
            with open(filepath, 'w', encoding='utf-8-sig') as f:
                f.write(f"# {title}\n\n")
                f.write(f"**发布日期**: {pub_date}\n\n")
                f.write(f"**原文链接**: {url}\n\n")
                f.write("## 📄 原文内容\n\n")
                f.write(content)

            logging.info(f"> 已保存到: {filepath}")
            return True
        except Exception as e:
            logging.error(f"! 文件保存失败: {str(e)}")
            return False

    def save_global_summary(self, keyword, summary, summary_info):
        """保存全局AI总结到文件"""
        try:
            # 创建以关键词命名的根目录
            root_dir = self.clean_filename(keyword)
            os.makedirs(root_dir, exist_ok=True)

            # 创建全局总结文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"全局AI总结_{keyword}_{timestamp}.md"
            filepath = os.path.join(root_dir, filename)

            # 写入全局总结
            with open(filepath, 'w', encoding='utf-8-sig') as f:
                f.write(f"# 🤖 全局AI智能总结\n\n")
                f.write(f"**搜索关键词**: {keyword}\n\n")
                f.write(f"**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
                f.write(f"**AI模型**: {summary_info.get('model', 'Unknown')}\n\n")
                f.write(f"**文章数量**: {summary_info.get('article_count', 0)}篇\n\n")
                f.write("---\n\n")
                f.write(summary)
                f.write("\n\n---\n\n")
                f.write("*此总结由AI自动生成，仅供参考*")

            logging.info(f"> 全局总结已保存到: {filepath}")
            return True
        except Exception as e:
            logging.error(f"! 全局总结保存失败: {str(e)}")
            return False

    def save_question_answer(self, user_question, search_keyword, answer, answer_info):
        """保存问题回答到文件"""
        try:
            # 创建以关键词命名的根目录
            root_dir = self.clean_filename(search_keyword)
            os.makedirs(root_dir, exist_ok=True)

            # 创建问题回答文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"AI问答_{search_keyword}_{timestamp}.md"
            filepath = os.path.join(root_dir, filename)

            # 写入问题回答
            with open(filepath, 'w', encoding='utf-8-sig') as f:
                f.write(f"# 🤖 AI智能问答\n\n")
                f.write(f"**用户问题**: {user_question}\n\n")
                f.write(f"**搜索关键词**: {search_keyword}\n\n")
                f.write(f"**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
                f.write(f"**AI模型**: {answer_info.get('model', 'Unknown')}\n\n")
                f.write(f"**参考文章数量**: {answer_info.get('article_count', 0)}篇\n\n")
                f.write("---\n\n")
                f.write(answer)
                f.write("\n\n---\n\n")
                f.write("*此回答由AI基于搜索到的文章自动生成，仅供参考*")

            logging.info(f"> 问题回答已保存到: {filepath}")
            return True
        except Exception as e:
            logging.error(f"! 问题回答保存失败: {str(e)}")
            return False

    def start_search(self, user_question, max_pages=3):
        """主启动函数 - 基于用户问题进行搜索"""
        logging.info(f"\n{'=' * 50}\n开始搜索问题: {user_question}\n{'=' * 50}")

        # 从问题中提取搜索关键词
        search_keywords = self.extract_keywords_from_question(user_question)

        # 重置收集的文章列表
        self.collected_articles = []

        self.send_message('progress', progress=0, message='开始搜索...')

        # 使用优化的搜索策略
        all_urls = []
        all_dates = []

        # 限制使用前3个最相关的关键词
        selected_keywords = search_keywords[:3]

        for i, keyword in enumerate(selected_keywords):
            logging.info(f"使用关键词 '{keyword}' 进行搜索...")

            # 动态分配页数：第一个关键词（最重要）获得更多页数
            if i == 0:
                pages_for_keyword = max(1, max_pages // 2)  # 第一个关键词获得一半页数
            else:
                remaining_pages = max_pages - (max_pages // 2)
                pages_for_keyword = max(1, remaining_pages // (len(selected_keywords) - 1))

            try:
                urls, dates = self.fetch_baidu_links(keyword, pages=pages_for_keyword)
                all_urls.extend(urls)
                all_dates.extend(dates)
                logging.info(f"关键词 '{keyword}' 获取到 {len(urls)} 个链接")
            except Exception as e:
                logging.error(f"搜索关键词 '{keyword}' 失败: {str(e)}")
                continue

        # 去重并保持顺序（优先保留前面关键词的结果）
        unique_urls = []
        unique_dates = []
        seen_urls = set()
        for url, date in zip(all_urls, all_dates):
            if url not in seen_urls:
                unique_urls.append(url)
                unique_dates.append(date)
                seen_urls.add(url)

        urls, dates = unique_urls, unique_dates
        primary_keyword = search_keywords[0] if search_keywords else user_question

        logging.info(f"总共获取到 {len(urls)} 个唯一链接")

        if not urls:
            self.send_message('error', message='未获取到任何有效链接')
            return

        session = self.init_session()
        logging.info(f"\n{'=' * 50}\n开始解析 {len(urls)} 篇文章\n{'=' * 50}")

        # 解析文章
        for i, (url, fallback_date) in enumerate(zip(urls, dates)):
            progress = 30 + (i / len(urls)) * 50  # 文章解析占50%进度，为全局总结预留10%
            self.send_message('progress',
                            progress=progress,
                            message=f'正在解析第 {i+1}/{len(urls)} 篇文章...')

            title, content, pub_date, final_url = self.get_article_content(
                session, url, fallback_date, primary_keyword)

            if title and content and pub_date:
                # 收集文章信息用于批量总结
                article_info = {
                    'title': title,
                    'content': content,
                    'pub_date': pub_date,
                    'url': final_url
                }
                self.collected_articles.append(article_info)

                # 保存文章（暂时不生成单篇总结）
                save_result = self.save_article_simple(primary_keyword, title, content, pub_date, final_url)

                if save_result:
                    self.success_count += 1

                    # 发送文章数据到前端（不包含单篇AI总结）
                    article_data = {
                        'title': title,
                        'content': content,
                        'pub_date': pub_date,
                        'url': final_url,
                        'ai_summary': None  # 单篇总结暂时为空
                    }

                    self.send_message('article', article=article_data)

                # 更新统计
                self.send_message('stats', stats={
                    'total_links': self.total_links,
                    'success_count': self.success_count,
                    'processed': i + 1,
                    'total': len(urls)
                })

        # 过滤相关文章
        relevant_articles = []
        if self.collected_articles:
            self.send_message('progress', progress=80, message='正在过滤相关文章...')
            relevant_articles = self.filter_relevant_articles(
                self.collected_articles, user_question, search_keywords
            )
            logging.info(f"从 {len(self.collected_articles)} 篇文章中筛选出 {len(relevant_articles)} 篇相关文章")

        # 生成问题回答
        question_answer = None
        if self.enable_question_answer and relevant_articles:
            self.send_message('progress', progress=85, message='正在生成AI回答...')
            try:
                logging.info("正在生成AI回答...")
                answer_result = self.ai_service.answer_question_with_articles(relevant_articles, user_question)
                if answer_result and answer_result.get('success'):
                    question_answer = answer_result.get('answer')
                    logging.info("AI回答生成成功")

                    # 保存回答到文件
                    self.save_question_answer(user_question, primary_keyword, question_answer, answer_result)
                else:
                    logging.warning("AI回答生成失败")
            except Exception as e:
                logging.error(f"AI回答生成异常: {str(e)}")
        elif not relevant_articles:
            logging.warning("没有找到相关文章，无法生成AI回答")

        # 搜索完成
        self.send_message('complete', summary={
            'total_articles': len(urls),
            'success_count': self.success_count,
            'relevant_articles': len(relevant_articles) if relevant_articles else 0,
            'question': user_question,
            'keyword': primary_keyword,
            'question_answer': question_answer
        })

        # 发送完成信号
        self.send_message(None)

        logging.info(f"\n{'=' * 50}")
        logging.info(f"完成! 成功保存 {self.success_count}/{len(urls)} 篇文章")
        if question_answer:
            logging.info("AI问题回答已生成")
        logging.info(f"{'=' * 50}\n")

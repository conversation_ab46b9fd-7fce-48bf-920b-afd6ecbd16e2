#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import logging
from baidu_search_utils import BaiduSearchUtils

# 设置日志
logging.basicConfig(level=logging.INFO)

async def save_html_for_debug():
    """保存HTML用于调试"""
    search_utils = BaiduSearchUtils()
    
    print("获取MONKEY_OCR搜索页面HTML...")
    
    # 直接调用crawl4ai获取HTML
    from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig
    
    run_config = CrawlerRunConfig(
        page_timeout=15000,
        word_count_threshold=10,
        excluded_tags=["script", "style", "iframe", "meta", "nav", "footer"],
        exclude_external_links=False,
        exclude_internal_links=True,
        exclude_social_media_links=True,
        exclude_external_images=True,
        only_text=False,
        cache_mode=CacheMode.BYPASS,
    )
    
    async with AsyncWebCrawler() as crawler:
        search_url = "https://www.baidu.com/s?wd=MONKEY_OCR&rn=10"
        result = await crawler.arun(url=search_url, config=run_config)
        
        if result.success:
            # 保存HTML到文件
            with open("monkey_ocr_search.html", "w", encoding="utf-8") as f:
                f.write(result.html)
            print(f"HTML已保存到 monkey_ocr_search.html，长度: {len(result.html)}")
        else:
            print(f"获取HTML失败: {result.error_message}")

if __name__ == "__main__":
    asyncio.run(save_html_for_debug())

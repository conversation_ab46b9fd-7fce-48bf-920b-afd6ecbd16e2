# Accelerate配置文件 - 8×RTX 3090分布式训练
# 针对Wan2.1-I2V-14B-480P模型优化的配置

compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 8
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false

# 额外的分布式训练配置
# 这些配置针对RTX 3090进行了优化
deepspeed_config: {}
fsdp_config: {}
megatron_lm_config: {}

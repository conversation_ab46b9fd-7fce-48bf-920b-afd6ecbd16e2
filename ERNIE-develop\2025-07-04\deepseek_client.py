# -*- coding: utf-8 -*-
"""
DeepSeek API客户端，支持流式输出
"""

import json
import logging
import requests
from typing import Generator, Dict, Any


class DeepSeekClient:
    """
    DeepSeek API客户端类
    """

    def __init__(self, api_key: str = "sk-lBsjhuzN4GriMRqfD45744C34dDa4e39922bCfC9A911Cf1d"):
        """
        初始化DeepSeek客户端
        
        Args:
            api_key (str): DeepSeek API密钥
        """
        self.api_key = api_key
        self.base_url = "https://api.edgefn.net/v1/chat/completions"
        self.logger = logging.getLogger(__name__)
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def chat_completion(self, messages: list, model: str = "DeepSeek-R1-0528", 
                       temperature: float = 0.7, stream: bool = False) -> Dict[str, Any]:
        """
        发送聊天完成请求
        
        Args:
            messages (list): 消息列表
            model (str): 模型名称
            temperature (float): 温度参数
            stream (bool): 是否使用流式输出
            
        Returns:
            Dict[str, Any]: API响应
        """
        data = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "stream": stream
        }
        
        try:
            response = requests.post(
                self.base_url, 
                headers=self.headers, 
                json=data, 
                stream=stream
            )
            
            if response.status_code != 200:
                self.logger.error(f"API请求失败，状态码: {response.status_code}")
                self.logger.error(f"响应内容: {response.text}")
                return {"error": f"API请求失败，状态码: {response.status_code}"}
            
            if stream:
                return {"stream": response}
            else:
                return response.json()
                
        except Exception as e:
            self.logger.error(f"API请求异常: {e}")
            return {"error": str(e)}

    def chat_completion_stream(self, messages: list, model: str = "DeepSeek-R1-0528", 
                              temperature: float = 0.7) -> Generator[Dict[str, Any], None, None]:
        """
        流式聊天完成请求
        
        Args:
            messages (list): 消息列表
            model (str): 模型名称
            temperature (float): 温度参数
            
        Yields:
            Dict[str, Any]: 流式响应数据块
        """
        result = self.chat_completion(messages, model, temperature, stream=True)
        
        if "error" in result:
            yield result
            return
        
        response = result["stream"]
        
        try:
            for line in response.iter_lines():
                if line:
                    try:
                        # DeepSeek API的流式响应通常以 "data: " 前缀开始
                        if line.startswith(b'data: '):
                            line = line[6:]
                        
                        # 跳过空行或终止信号
                        if line.strip() == b'' or line.strip() == b'[DONE]':
                            continue
                        
                        # 解析JSON数据块
                        chunk = json.loads(line)
                        yield chunk
                        
                    except json.JSONDecodeError as e:
                        self.logger.error(f"JSON解析错误: {e}")
                        self.logger.error(f"接收到的数据: {line}")
                        continue
                        
        except Exception as e:
            self.logger.error(f"处理流式响应时出错: {e}")
            yield {"error": str(e)}

    def format_stream_response(self, stream_generator: Generator) -> Generator[str, None, None]:
        """
        格式化流式响应，分别处理思考过程和最终回答
        
        Args:
            stream_generator: 流式响应生成器
            
        Yields:
            str: 格式化后的响应内容
        """
        current_content_type = None
        has_printed_reasoning_header = False
        has_printed_content_header = False
        
        for chunk in stream_generator:
            if "error" in chunk:
                yield f"错误: {chunk['error']}"
                return
            
            try:
                # 检查是否有 delta 字段
                if 'choices' in chunk and len(chunk['choices']) > 0 and 'delta' in chunk['choices'][0]:
                    delta = chunk['choices'][0]['delta']
                    
                    # 检查是否包含思考过程
                    if 'reasoning_content' in delta and delta['reasoning_content']:
                        # 第一次出现思考过程时打印标题
                        if not has_printed_reasoning_header:
                            yield "\n===== 思考过程 =====\n"
                            has_printed_reasoning_header = True
                            current_content_type = 'reasoning'
                        
                        # 输出思考过程内容
                        yield delta['reasoning_content']
                    
                    # 检查是否包含最终回答
                    elif 'content' in delta and delta['content']:
                        # 如果从思考过程切换到最终回答，打印分隔符
                        if current_content_type == 'reasoning' or not has_printed_content_header:
                            if has_printed_reasoning_header:
                                yield "\n\n===== 思考结束 =====\n"
                            yield "\n===== 最终回答 =====\n"
                            has_printed_content_header = True
                            current_content_type = 'content'
                        
                        # 输出最终回答内容
                        yield delta['content']
                    
                    # 检查是否完成
                    if 'finish_reason' in chunk['choices'][0] and chunk['choices'][0]['finish_reason'] == 'stop':
                        yield "\n\n=====================\n"
                        
            except Exception as e:
                self.logger.error(f"处理响应块时出错: {e}")
                continue

    def simple_chat(self, user_message: str, system_message: str = None) -> str:
        """
        简单的聊天接口，返回完整响应
        
        Args:
            user_message (str): 用户消息
            system_message (str): 系统消息（可选）
            
        Returns:
            str: 模型回复
        """
        messages = []
        
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        messages.append({"role": "user", "content": user_message})
        
        result = self.chat_completion(messages, stream=False)
        
        if "error" in result:
            return f"错误: {result['error']}"
        
        try:
            return result['choices'][0]['message']['content']
        except (KeyError, IndexError) as e:
            self.logger.error(f"解析响应失败: {e}")
            return "解析响应失败"

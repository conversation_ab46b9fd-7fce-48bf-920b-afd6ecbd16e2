#!/usr/bin/env python3
"""
8×RTX 3090多GPU训练环境设置脚本
"""

import os
import sys
import torch
import subprocess
from pathlib import Path

def check_gpu_configuration():
    """检查GPU配置"""
    print("🔍 检查8×RTX 3090配置...")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    gpu_count = torch.cuda.device_count()
    print(f"🎯 检测到 {gpu_count} 块GPU")
    
    total_memory = 0
    rtx3090_count = 0
    
    for i in range(gpu_count):
        gpu_name = torch.cuda.get_device_name(i)
        memory_gb = torch.cuda.get_device_properties(i).total_memory / 1024**3
        total_memory += memory_gb
        
        if "RTX 3090" in gpu_name:
            rtx3090_count += 1
            
        print(f"   GPU {i}: {gpu_name} ({memory_gb:.1f}GB)")
    
    print(f"💾 总显存: {total_memory:.1f}GB")
    print(f"🎯 RTX 3090数量: {rtx3090_count}")
    
    if rtx3090_count == 8:
        print("✅ 完美！8×RTX 3090配置确认")
        return True
    elif rtx3090_count > 0:
        print(f"⚠️  检测到{rtx3090_count}×RTX 3090，将使用可用的GPU")
        return True
    else:
        print("❌ 未检测到RTX 3090")
        return False

def create_accelerate_config_8gpu():
    """创建8GPU Accelerate配置"""
    print("🔧 创建8×RTX 3090 Accelerate配置...")
    
    config_content = """compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 8
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
"""
    
    # 创建配置目录
    config_dir = Path.home() / ".cache" / "huggingface" / "accelerate"
    config_dir.mkdir(parents=True, exist_ok=True)
    
    config_file = config_dir / "default_config.yaml"
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    print(f"✅ Accelerate配置已创建: {config_file}")
    return config_file

def test_multi_gpu_communication():
    """测试多GPU通信"""
    print("🧪 测试多GPU通信...")
    
    try:
        # 创建测试脚本
        test_script = """
import torch
import torch.distributed as dist
import os

def test_multi_gpu():
    if torch.cuda.device_count() < 2:
        print("⚠️  GPU数量不足，无法测试多GPU通信")
        return
    
    print(f"🔍 测试{torch.cuda.device_count()}个GPU的通信...")
    
    # 测试基本GPU操作
    for i in range(min(8, torch.cuda.device_count())):
        device = torch.device(f'cuda:{i}')
        x = torch.randn(1000, 1000, device=device)
        y = torch.randn(1000, 1000, device=device)
        z = torch.mm(x, y)
        print(f"✅ GPU {i}: 矩阵运算测试通过 {z.shape}")
    
    print("✅ 多GPU基础通信测试完成")

if __name__ == "__main__":
    test_multi_gpu()
"""
        
        with open("test_multi_gpu.py", 'w') as f:
            f.write(test_script)
        
        # 运行测试
        result = subprocess.run([sys.executable, "test_multi_gpu.py"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 多GPU通信测试通过")
            print(result.stdout)
            return True
        else:
            print("❌ 多GPU通信测试失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 多GPU测试出错: {e}")
        return False

def create_8gpu_training_scripts():
    """创建8GPU训练脚本"""
    print("📝 创建8×RTX 3090训练脚本...")
    
    # 高分辨率训练脚本
    high_res_script = """#!/bin/bash

# 8×RTX 3090高分辨率LoRA训练脚本
# 分辨率: 480×832, 适合高质量视频生成

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=INFO
export NCCL_TREE_THRESHOLD=0

echo "🚀 开始8×RTX 3090高分辨率LoRA训练..."

accelerate launch \\
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \\
    --num_processes 8 \\
    --mixed_precision bf16 \\
    examples/wanvideo/model_training/train.py \\
    --dataset_base_path "./data/example_video_dataset" \\
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \\
    --height 480 \\
    --width 832 \\
    --dataset_repeat 50 \\
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \\
    --learning_rate 1e-4 \\
    --num_epochs 3 \\
    --gradient_accumulation_steps 2 \\
    --remove_prefix_in_ckpt "pipe.dit." \\
    --output_path "./models/train/8x3090_high_res" \\
    --lora_base_model "dit" \\
    --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \\
    --lora_rank 64 \\
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090高分辨率训练完成"
"""
    
    # 快速训练脚本
    fast_script = """#!/bin/bash

# 8×RTX 3090快速LoRA训练脚本
# 分辨率: 320×576, 适合快速验证

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN

echo "🚀 开始8×RTX 3090快速LoRA训练..."

accelerate launch \\
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \\
    --num_processes 8 \\
    --mixed_precision bf16 \\
    examples/wanvideo/model_training/train.py \\
    --dataset_base_path "./data/example_video_dataset" \\
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \\
    --height 320 \\
    --width 576 \\
    --dataset_repeat 20 \\
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \\
    --learning_rate 1e-4 \\
    --num_epochs 1 \\
    --gradient_accumulation_steps 1 \\
    --remove_prefix_in_ckpt "pipe.dit." \\
    --output_path "./models/train/8x3090_fast" \\
    --lora_base_model "dit" \\
    --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \\
    --lora_rank 32 \\
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090快速训练完成"
"""
    
    # 保存脚本
    scripts = [
        ("train_8x3090_high_res.sh", high_res_script),
        ("train_8x3090_fast.sh", fast_script)
    ]
    
    for script_name, script_content in scripts:
        with open(script_name, 'w') as f:
            f.write(script_content)
        os.chmod(script_name, 0o755)
        print(f"✅ 创建训练脚本: {script_name}")
    
    return scripts

def estimate_training_performance():
    """估算8×RTX 3090训练性能"""
    print("📊 估算8×RTX 3090训练性能...")
    
    # 基于单GPU性能估算
    single_gpu_times = {
        "320x576_lora": 12,  # 秒/步
        "480x832_lora": 25,  # 秒/步
        "320x576_full": 45,  # 秒/步
        "480x832_full": 90,  # 秒/步
    }
    
    # 8GPU并行效率 (考虑通信开销)
    parallel_efficiency = 0.85  # 85%效率
    
    print("🎯 8×RTX 3090预估性能:")
    print("=" * 50)
    
    for config, single_time in single_gpu_times.items():
        multi_time = single_time / (8 * parallel_efficiency)
        speedup = single_time / multi_time
        
        resolution, training_type = config.split("_")
        print(f"📈 {resolution} {training_type.upper()}:")
        print(f"   单GPU: {single_time}秒/步")
        print(f"   8GPU:  {multi_time:.1f}秒/步")
        print(f"   加速比: {speedup:.1f}x")
        print()
    
    return True

def main():
    print("🎬 8×RTX 3090多GPU训练环境设置")
    print("=" * 60)
    
    # 检查GPU配置
    if not check_gpu_configuration():
        print("❌ GPU配置检查失败")
        return 1
    
    # 创建Accelerate配置
    create_accelerate_config_8gpu()
    
    # 测试多GPU通信
    test_multi_gpu_communication()
    
    # 创建训练脚本
    create_8gpu_training_scripts()
    
    # 估算性能
    estimate_training_performance()
    
    print("=" * 60)
    print("✅ 8×RTX 3090训练环境设置完成！")
    print()
    print("🚀 可用的训练命令:")
    print("   bash train_8x3090_fast.sh      # 快速训练 (320×576)")
    print("   bash train_8x3090_high_res.sh  # 高分辨率训练 (480×832)")
    print()
    print("💡 建议:")
    print("   - 首次使用建议先运行快速训练验证环境")
    print("   - 确认无误后再进行高分辨率训练")
    print("   - 训练过程中可使用 nvidia-smi 监控GPU使用情况")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

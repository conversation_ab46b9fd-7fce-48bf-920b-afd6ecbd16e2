import requests

url = "https://api.edgefn.net/v1/chat/completions"
headers = {
    "Authorization": "Bearer sk-JGGCOW4Ye6fhyVFK45Fe46734bF84d1a934dFa3cCfD7337b",
    "Content-Type": "application/json"
}
data = {
    "model": "DeepSeek-R1-0528-Qwen3-8B",
    "messages": [{"role": "user", "content": "Hello, how are you?"}]
}

response = requests.post(url, headers=headers, json=data)
print(response.json())

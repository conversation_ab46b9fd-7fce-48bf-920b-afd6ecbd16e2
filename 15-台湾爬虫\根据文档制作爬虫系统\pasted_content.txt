#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版完美爬虫 - 解决数据源访问问题
作者: MiniMax Agent
时间: 2025-07-15
基于字段对比分析报告的问题修正，实现真正的采购公告数据爬取。
修正了URL配置、搜索参数和解析逻辑。
"""
import requests
import time
import random
import csv
import json
import logging
import re
import os
import sqlite3
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin, quote, unquote, parse_qs, urlparse
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from bs4 import BeautifulSoup
import urllib.parse
# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/workspace/logs/perfect_crawler_fixed.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)
class PerfectCrawlerFixed:
    """修正版完美爬虫 - 解决核心问题"""
    
    def __init__(self):
        """初始化爬虫"""
        self.base_url = "https://web.pcc.gov.tw"
        
        # 修正的URL配置（基于实际网站结构）
        self.urls = {
            # 主要搜索端点
            'main_search': 'https://web.pcc.gov.tw/tps/pss/tender.do',
            'search_action': 'https://web.pcc.gov.tw/tps/pss/tender.do',
            
            # 备用搜索端点
            'bulletin_search': 'https://web.pcc.gov.tw/prkms/tender/common/bulletion/resultBulletion',
            'tender_list': 'https://web.pcc.gov.tw/tps/pss/tender.do?searchMode=common&searchType=basic',
            'award_list': 'https://web.pcc.gov.tw/tps/pss/tender.do?searchMode=common&searchType=award',
            
            # 详情页基础URL
            'detail_base': 'https://web.pcc.gov.tw/tps/pss/tender.do?method=goDetail&pkAtm=',
            'award_detail_base': 'https://web.pcc.gov.tw/tps/pss/tender.do?method=goDetail&pkAtl='
        }
        
        # 创建目录
        self._create_directories()
        
        # 初始化session
        self.session = requests.Session()
        self._setup_session()
        
        # 基于图片分析的完整字段映射
        self.field_mapping = self._load_field_mapping()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_items': 0,
            'complete_records': 0,
            'field_coverage': {},
            'start_time': datetime.now()
        }
    
    def _create_directories(self):
        """创建必要目录"""
        directories = [
            '/workspace/logs',
            '/workspace/data/fixed_crawler',
            '/workspace/debug/fixed_crawler'
        ]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _setup_session(self):
        """设置会话"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'max-age=0',
            'Referer': 'https://web.pcc.gov.tw'
        })
    
    def _load_field_mapping(self):
        """加载字段映射（基于图片分析）"""
        return {
            # 基本信息字段
            '案號': 'announcement_id',
            '標案案號': 'announcement_id', 
            '標案名稱': 'title',
            '機關名稱': 'organization',
            '需求機關': 'organization',
            '單位名稱': 'unit_name',
            '機關代碼': 'agency_code',
            
            # 联络信息字段
            '聯絡人': 'contact_person',
            '聯絡電話': 'contact_phone', 
            '傳真號碼': 'fax_number',
            '電子郵件': 'email',
            
            # 日期时间字段
            '公告日期': 'announcement_date',
            '截止收件': 'deadline_date',
            '開標時間': 'bid_opening_time',
            '決標日期': 'award_date',
            
            # 金额字段
            '預算金額': 'budget_amount',
            '決標金額': 'award_amount',
            '採購金額級距': 'procurement_amount_range',
            
            # 采购信息字段
            '採購性質': 'procurement_type',
            '招標方式': 'tendering_method',
            '決標方式': 'awarding_method',
            '標案狀態': 'tender_status',
            
            # 地点信息字段
            '履約地點': 'performance_location',
            
            # 厂商信息字段
            '投標廠商家數': 'number_of_bidders',
            '廠商代碼': 'vendor_code',
            '廠商名稱': 'vendor_name',
            '是否得標': 'won_bid',
            '是否為中小企業': 'is_sme'
        }
    
    def build_corrected_search_params(self, search_type: str = 'basic', 
                                    keyword: str = '', 
                                    date_range: int = 30) -> Dict[str, Any]:
        """构建修正的搜索参数"""
        
        # 计算日期范围（民国纪年）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=date_range)
        
        # 转换为民国纪年
        end_roc_year = end_date.year - 1911
        start_roc_year = start_date.year - 1911
        
        if search_type == 'basic':
            # 基础搜索（招标公告）
            params = {
                'method': 'search',
                'searchMode': 'common',
                'searchType': 'basic',
                'searchTarget': 'ATM',
                'orgName': '',
                'orgId': '',
                'tenderName': keyword,
                'tenderId': '',
                'tenderStatus': '5,21,1,3,2,4',  # 所有状态
                'tenderWay': '',
                'awardAnnounceStartDate': f'{start_roc_year}/{start_date.month:02d}/{start_date.day:02d}',
                'awardAnnounceEndDate': f'{end_roc_year}/{end_date.month:02d}/{end_date.day:02d}',
                'radProctrgCate': 'All',
                'pageIndex': '1',
                'recordCountPerPage': '100'
            }
        
        elif search_type == 'award':
            # 决标公告搜索
            params = {
                'method': 'search',
                'searchMode': 'common',
                'searchType': 'award',
                'searchTarget': 'ATL',
                'orgName': '',
                'tenderName': keyword,
                'tenderId': '',
                'awardAnnounceStartDate': f'{start_roc_year}/{start_date.month:02d}/{start_date.day:02d}',
                'awardAnnounceEndDate': f'{end_roc_year}/{end_date.month:02d}/{end_date.day:02d}',
                'radProctrgCate': 'All',
                'pageIndex': '1',
                'recordCountPerPage': '100'
            }
        
        elif search_type == 'bulletin':
            # 公告搜索（备用方案）
            params = {
                'method': 'search',
                'searchMethod': 'true',
                'searchTarget': 'ATM',
                'orgName': '',
                'tenderName': keyword,
                'tenderStatus': '招標,決標',
                'pageSize': '100',
                'pageIndex': '1'
            }
        
        return params
    
    def search_procurement_announcements(self, search_type: str = 'basic',
                                       keyword: str = '',
                                       max_pages: int = 3) -> List[Dict[str, Any]]:
        """搜索采购公告（修正版）"""
        logger.info(f"开始修正版搜索 - 类型: {search_type}, 关键词: {keyword}")
        
        all_results = []
        
        # 选择正确的URL和参数
        if search_type == 'award':
            search_url = self.urls['main_search']
        elif search_type == 'bulletin':
            search_url = self.urls['bulletin_search']
        else:
            search_url = self.urls['main_search']
        
        # 构建搜索参数
        search_params = self.build_corrected_search_params(search_type, keyword)
        
        # 分页搜索
        for page in range(1, max_pages + 1):
            try:\n                logger.info(f\"正在搜索第 {page} 页\")\n                \n                # 更新页码\n                search_params['pageIndex'] = str(page)\n                \n                # 发送搜索请求\n                response = self._make_request(search_url, 'POST', search_params)\n                if not response:\n                    logger.warning(f\"第 {page} 页请求失败\")\n                    continue\n                \n                # 保存调试文件\n                debug_path = f'/workspace/debug/fixed_crawler/search_{search_type}_page_{page}.html'\n                with open(debug_path, 'w', encoding='utf-8') as f:\n                    f.write(response.text)\n                \n                # 解析搜索结果\n                page_results = self._parse_corrected_search_results(response.text, search_url, search_type)\n                \n                if not page_results:\n                    logger.info(f\"第 {page} 页无结果\")\n                    # 如果第一页就没结果，尝试其他搜索方式\n                    if page == 1 and search_type != 'bulletin':\n                        logger.info(\"尝试备用搜索方式\")\n                        backup_results = self.search_procurement_announcements('bulletin', keyword, 1)\n                        all_results.extend(backup_results)\n                    break\n                \n                all_results.extend(page_results)\n                logger.info(f\"第 {page} 页获得 {len(page_results)} 条结果，累计 {len(all_results)} 条\")\n                \n                # 延迟\n                time.sleep(random.uniform(2, 4))\n                \n            except Exception as e:\n                logger.error(f\"搜索第 {page} 页时出错: {e}\")\n                continue\n        \n        return all_results\n    \n    def _make_request(self, url: str, method: str = 'GET', data: Dict = None) -> Optional[requests.Response]:\n        \"\"\"发送HTTP请求\"\"\"\n        for attempt in range(3):\n            try:\n                self.stats['total_requests'] += 1\n                \n                # 添加延迟\n                time.sleep(random.uniform(1, 3))\n                \n                # 发送请求\n                if method.upper() == 'POST':\n                    response = self.session.post(url, data=data, timeout=30)\n                else:\n                    response = self.session.get(url, params=data, timeout=30)\n                \n                response.raise_for_status()\n                response.encoding = 'utf-8'\n                \n                self.stats['successful_requests'] += 1\n                logger.debug(f\"请求成功: {url} (尝试 {attempt + 1})\")\n                \n                return response\n                \n            except requests.exceptions.RequestException as e:\n                logger.warning(f\"请求失败 (尝试 {attempt + 1}/3): {url} - {e}\")\n                if attempt < 2:\n                    time.sleep((2 ** attempt) * random.uniform(1, 2))\n        \n        self.stats['failed_requests'] += 1\n        return None\n    \n    def _parse_corrected_search_results(self, html_content: str, base_url: str, search_type: str) -> List[Dict[str, Any]]:\n        \"\"\"解析修正的搜索结果页面\"\"\"\n        try:\n            soup = BeautifulSoup(html_content, 'html.parser')\n            results = []\n            \n            # 查找结果表格 - 更精确的定位\n            target_tables = []\n            \n            # 方法1: 查找包含采购相关关键词的表格\n            for table in soup.find_all('table'):\n                table_text = table.get_text()\n                if any(keyword in table_text for keyword in ['案號', '標案名稱', '機關名稱', '公告日期', '截止收件']):\n                    target_tables.append(table)\n            \n            # 方法2: 查找特定class或id的表格\n            for selector in ['table.result_table', 'table#result', 'table[class*=\"tender\"]', 'table[id*=\"result\"]']:\n                tables = soup.select(selector)\n                target_tables.extend(tables)\n            \n            # 方法3: 查找包含链接的表格\n            for table in soup.find_all('table'):\n                links = table.find_all('a', href=True)\n                if len(links) >= 3:  # 有多个链接的表格更可能是结果表格\n                    target_tables.append(table)\n            \n            # 去重\n            unique_tables = list({id(table): table for table in target_tables}.values())\n            \n            logger.info(f\"找到 {len(unique_tables)} 个可能的结果表格\")\n            \n            for table in unique_tables:\n                table_results = self._extract_from_result_table(table, base_url, search_type)\n                results.extend(table_results)\n            \n            # 如果表格解析失败，尝试其他方法\n            if not results:\n                results = self._parse_alternative_structures(soup, base_url)\n            \n            logger.info(f\"解析结果: {len(results)} 条记录\")\n            return results\n            \n        except Exception as e:\n            logger.error(f\"解析搜索结果失败: {e}\")\n            return []\n    \n    def _extract_from_result_table(self, table, base_url: str, search_type: str) -> List[Dict[str, Any]]:\n        \"\"\"从结果表格中提取数据\"\"\"\n        results = []\n        \n        try:\n            rows = table.find_all('tr')\n            if len(rows) < 2:\n                return results\n            \n            # 分析表头\n            header_row = rows[0]\n            headers = [self._clean_text(th.get_text()) for th in header_row.find_all(['th', 'td'])]\n            \n            logger.debug(f\"表头: {headers}\")\n            \n            # 处理数据行\n            for row in rows[1:]:\n                try:\n                    cells = row.find_all(['td', 'th'])\n                    if len(cells) < 3:\n                        continue\n                    \n                    result = self._extract_row_data(cells, headers, row, base_url, search_type)\n                    if result and (result.get('title') or result.get('announcement_id')):\n                        results.append(result)\n                        \n                except Exception as e:\n                    logger.warning(f\"解析表格行失败: {e}\")\n                    continue\n            \n        except Exception as e:\n            logger.error(f\"解析表格失败: {e}\")\n        \n        return results\n    \n    def _extract_row_data(self, cells: List, headers: List, row, base_url: str, search_type: str) -> Optional[Dict[str, Any]]:\n        \"\"\"从表格行提取数据\"\"\"\n        try:\n            data = {}\n            \n            # 按列映射数据\n            for i, cell in enumerate(cells):\n                cell_text = self._clean_text(cell.get_text())\n                \n                if i < len(headers):\n                    header = headers[i]\n                    \n                    # 直接字段映射\n                    if header in self.field_mapping:\n                        field_name = self.field_mapping[header]\n                        data[field_name] = cell_text\n                    \n                    # 智能识别\n                    elif self._is_announcement_id(cell_text):\n                        data['announcement_id'] = cell_text\n                    elif self._is_organization_name(cell_text):\n                        data['organization'] = cell_text\n                    elif self._is_date_format(cell_text):\n                        if not data.get('announcement_date'):\n                            data['announcement_date'] = cell_text\n                    elif self._is_amount_format(cell_text):\n                        if not data.get('budget_amount'):\n                            data['budget_amount'] = cell_text\n                    elif len(cell_text) > 15 and not data.get('title'):\n                        data['title'] = cell_text\n            \n            # 提取详情链接\n            link = row.find('a', href=True)\n            if link:\n                href = link.get('href')\n                if href:\n                    data['detail_url'] = urljoin(base_url, href) if href.startswith('/') else href\n                    data['detail_link_text'] = self._clean_text(link.get_text())\n            \n            # 提取更多可能的字段\n            all_text = ' '.join([self._clean_text(cell.get_text()) for cell in cells])\n            self._extract_additional_fields(all_text, data)\n            \n            return data if data.get('title') or data.get('announcement_id') else None\n            \n        except Exception as e:\n            logger.warning(f\"提取行数据失败: {e}\")\n            return None\n    \n    def _extract_additional_fields(self, text: str, data: Dict):\n        \"\"\"从文本中提取额外字段\"\"\"\n        # 采购金额级距\n        amount_range_patterns = [\n            r'(\\d+萬?以上未達\\d+萬?)',\n            r'(未達\\d+萬?)',\n            r'(\\d+萬?元以上)',\n        ]\n        for pattern in amount_range_patterns:\n            match = re.search(pattern, text)\n            if match:\n                data['procurement_amount_range'] = match.group(1)\n                break\n        \n        # 采购性质\n        if re.search(r'工程', text):\n            data['procurement_type'] = '工程'\n        elif re.search(r'財物', text):\n            data['procurement_type'] = '財物'\n        elif re.search(r'勞務', text):\n            data['procurement_type'] = '勞務'\n        \n        # 招标方式\n        tender_methods = ['公開招標', '選擇性招標', '限制性招標']\n        for method in tender_methods:\n            if method in text:\n                data['tendering_method'] = method\n                break\n    \n    def _parse_alternative_structures(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, Any]]:\n        \"\"\"解析备选页面结构\"\"\"\n        results = []\n        \n        try:\n            # 查找列表项\n            list_items = soup.find_all(['li', 'div', 'tr'])\n            \n            for item in list_items:\n                text = item.get_text().strip()\n                \n                # 检查是否包含采购相关内容\n                if len(text) > 30 and any(keyword in text for keyword in ['案號', '標案', '機關', '公告', '決標']):\n                    result = {\n                        'title': text[:100] + '...' if len(text) > 100 else text,\n                        'content_summary': text,\n                        'source_type': 'alternative_parse'\n                    }\n                    \n                    # 查找链接\n                    link = item.find('a', href=True)\n                    if link:\n                        result['detail_url'] = urljoin(base_url, link.get('href'))\n                    \n                    # 智能提取字段\n                    self._smart_field_extraction(text, result)\n                    \n                    results.append(result)\n                    \n                    if len(results) >= 20:  # 限制数量\n                        break\n        \n        except Exception as e:\n            logger.error(f\"备选结构解析失败: {e}\")\n        \n        return results\n    \n    def _smart_field_extraction(self, text: str, result: Dict):\n        \"\"\"智能字段提取\"\"\"\n        # 案号提取\n        id_match = re.search(r'案號[：\\s]*([A-Z0-9\\-]{6,})', text)\n        if id_match:\n            result['announcement_id'] = id_match.group(1)\n        \n        # 机关名称提取\n        org_match = re.search(r'(\\S*[部局署處]\\S*|\\S*市政府\\S*|\\S*縣政府\\S*)', text)\n        if org_match:\n            result['organization'] = org_match.group(1)\n        \n        # 日期提取\n        date_match = re.search(r'(\\d{2,3}/\\d{1,2}/\\d{1,2})', text)\n        if date_match:\n            result['announcement_date'] = date_match.group(1)\n        \n        # 金额提取\n        amount_match = re.search(r'([0-9,]+)\\s*元', text)\n        if amount_match:\n            result['budget_amount'] = amount_match.group(1) + '元'\n    \n    def _is_announcement_id(self, text: str) -> bool:\n        \"\"\"判断是否为案号\"\"\"\n        return bool(re.match(r'^[A-Z0-9\\-]{6,20}$', text.strip()))\n    \n    def _is_organization_name(self, text: str) -> bool:\n        \"\"\"判断是否为机关名称\"\"\"\n        return any(keyword in text for keyword in ['部', '局', '署', '處', '市政府', '縣政府', '公司', '學校'])\n    \n    def _is_date_format(self, text: str) -> bool:\n        \"\"\"判断是否为日期格式\"\"\"\n        date_patterns = [\n            r'\\d{2,3}/\\d{1,2}/\\d{1,2}',\n            r'\\d{2,3}年\\d{1,2}月\\d{1,2}日'\n        ]\n        return any(re.search(pattern, text) for pattern in date_patterns)\n    \n    def _is_amount_format(self, text: str) -> bool:\n        \"\"\"判断是否为金额格式\"\"\"\n        return bool(re.search(r'[0-9,]+\\s*元', text))\n    \n    def _clean_text(self, text: str) -> str:\n        \"\"\"清理文本\"\"\"\n        if not text:\n            return ''\n        \n        text = re.sub(r'\\s+', ' ', text.strip())\n        text = re.sub(r'[\\r\\n\\t]', '', text)\n        text = text.replace('\\xa0', ' ')\n        \n        return text\n    \n    def fetch_detailed_info(self, basic_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:\n        \"\"\"获取详细信息\"\"\"\n        logger.info(f\"开始获取 {len(basic_results)} 条记录的详细信息\")\n        \n        detailed_results = []\n        \n        with ThreadPoolExecutor(max_workers=3) as executor:\n            future_to_item = {}\n            \n            for item in basic_results:\n                if item.get('detail_url'):\n                    future = executor.submit(self._fetch_single_detail, item)\n                    future_to_item[future] = item\n            \n            for future in as_completed(future_to_item):\n                try:\n                    detailed_item = future.result()\n                    if detailed_item:\n                        detailed_results.append(detailed_item)\n                        self.stats['total_items'] += 1\n                \n                except Exception as e:\n                    logger.error(f\"获取详细信息失败: {e}\")\n        \n        logger.info(f\"获取详细信息完成，共 {len(detailed_results)} 条记录\")\n        return detailed_results\n    \n    def _fetch_single_detail(self, basic_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:\n        \"\"\"获取单条记录的详细信息\"\"\"\n        try:\n            detail_url = basic_info['detail_url']\n            \n            # 发送详情页请求\n            response = self._make_request(detail_url)\n            if not response:\n                return basic_info\n            \n            # 解析详情页\n            detailed_info = self._parse_detail_page(response.text)\n            \n            # 合并信息\n            result = basic_info.copy()\n            result.update(detailed_info)\n            \n            return result\n            \n        except Exception as e:\n            logger.warning(f\"获取单条详情失败: {e}\")\n            return basic_info\n    \n    def _parse_detail_page(self, html_content: str) -> Dict[str, Any]:\n        \"\"\"解析详情页面\"\"\"\n        try:\n            soup = BeautifulSoup(html_content, 'html.parser')\n            detail_info = {}\n            \n            # 解析表格形式的详情\n            tables = soup.find_all('table')\n            for table in tables:\n                self._parse_detail_table(table, detail_info)\n            \n            # 解析文本形式的详情\n            all_text = soup.get_text()\n            self._parse_detail_text(all_text, detail_info)\n            \n            return detail_info\n            \n        except Exception as e:\n            logger.error(f\"解析详情页面失败: {e}\")\n            return {}\n    \n    def _parse_detail_table(self, table, detail_info: Dict):\n        \"\"\"解析详情表格\"\"\"\n        try:\n            rows = table.find_all('tr')\n            \n            for row in rows:\n                cells = row.find_all(['td', 'th'])\n                \n                if len(cells) >= 2:\n                    key = self._clean_text(cells[0].get_text())\n                    value = self._clean_text(cells[1].get_text())\n                    \n                    # 清理字段名\n                    key_clean = key.replace('：', '').replace(':', '').strip()\n                    \n                    # 字段映射\n                    if key_clean in self.field_mapping:\n                        field_name = self.field_mapping[key_clean]\n                        detail_info[field_name] = value\n        \n        except Exception as e:\n            logger.warning(f\"解析详情表格失败: {e}\")\n    \n    def _parse_detail_text(self, text: str, detail_info: Dict):\n        \"\"\"解析详情文本\"\"\"\n        # 基于图片分析的关键字段提取模式\n        field_patterns = {\n            'contact_person': r'聯絡人[：\\s]*([^：\\n]+)',\n            'contact_phone': r'電話[：\\s]*\\(?(\\d+)\\)?[\\s\\-]*(\\d+)',\n            'fax_number': r'傳真[：\\s]*\\(?(\\d+)\\)?[\\s\\-]*(\\d+)',\n            'email': r'電子郵件[：\\s]*([\\w\\.-]+@[\\w\\.-]+)',\n            'performance_location': r'履約地點[：\\s]*([^：\\n]+)',\n            'number_of_bidders': r'投標廠商家數[：\\s]*(\\d+)',\n            'tender_status': r'標案狀態[：\\s]*([^：\\n]+)'\n        }\n        \n        for field_name, pattern in field_patterns.items():\n            if field_name not in detail_info:\n                match = re.search(pattern, text)\n                if match:\n                    if field_name in ['contact_phone', 'fax_number']:\n                        detail_info[field_name] = f\"({match.group(1)}) {match.group(2)}\"\n                    else:\n                        detail_info[field_name] = match.group(1).strip()\n    \n    def save_results(self, data: List[Dict[str, Any]]) -> Dict[str, str]:\n        \"\"\"保存结果\"\"\"\n        if not data:\n            return {}\n        \n        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n        saved_files = {}\n        \n        # 保存CSV\n        csv_path = f'/workspace/data/fixed_crawler/fixed_procurement_{timestamp}.csv'\n        \n        # 获取所有字段名\n        all_fields = set()\n        for item in data:\n            all_fields.update(item.keys())\n        all_fields = sorted(list(all_fields))\n        \n        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:\n            writer = csv.DictWriter(csvfile, fieldnames=all_fields, extrasaction='ignore')\n            writer.writeheader()\n            writer.writerows(data)\n        \n        saved_files['csv'] = csv_path\n        \n        # 保存JSON\n        json_path = f'/workspace/data/fixed_crawler/fixed_procurement_{timestamp}.json'\n        with open(json_path, 'w', encoding='utf-8') as jsonfile:\n            json.dump(data, jsonfile, ensure_ascii=False, indent=2, default=str)\n        \n        saved_files['json'] = json_path\n        \n        logger.info(f\"结果已保存: {len(data)} 条记录\")\n        return saved_files\n    \n    def run_fixed_crawl(self, keyword: str = '', \n                       search_types: List[str] = None,\n                       max_pages: int = 2) -> Dict[str, Any]:\n        \"\"\"运行修正版爬虫\"\"\"\n        logger.info(\"🚀 修正版完美爬虫启动\")\n        logger.info(f\"🔍 搜索关键词: {keyword or '全部'}\")\n        \n        if search_types is None:\n            search_types = ['basic', 'award']\n        \n        try:\n            all_results = []\n            \n            # 多种搜索方式\n            for search_type in search_types:\n                logger.info(f\"执行 {search_type} 搜索\")\n                search_results = self.search_procurement_announcements(search_type, keyword, max_pages)\n                all_results.extend(search_results)\n            \n            # 去重\n            seen_items = set()\n            unique_results = []\n            for item in all_results:\n                # 使用多个字段组合作为唯一标识\n                item_key = (\n                    item.get('announcement_id', ''),\n                    item.get('title', '')[:50],\n                    item.get('organization', '')\n                )\n                \n                if item_key not in seen_items:\n                    seen_items.add(item_key)\n                    unique_results.append(item)\n            \n            logger.info(f\"去重后获得 {len(unique_results)} 条基础记录\")\n            \n            # 获取详细信息\n            detailed_results = self.fetch_detailed_info(unique_results)\n            \n            # 保存结果\n            saved_files = self.save_results(detailed_results)\n            \n            # 生成简化报告\n            field_coverage = self._calculate_field_coverage(detailed_results)\n            \n            return {\n                'status': 'success' if detailed_results else 'no_data',\n                'total_items': len(detailed_results),\n                'field_coverage': field_coverage,\n                'saved_files': saved_files,\n                'data_sample': detailed_results[:3] if detailed_results else [],\n                'stats': self.stats\n            }\n            \n        except Exception as e:\n            logger.error(f\"修正版爬虫执行出错: {e}\")\n            return {\n                'status': 'error',\n                'error': str(e),\n                'total_items': 0,\n                'stats': self.stats\n            }\n    \n    def _calculate_field_coverage(self, data: List[Dict[str, Any]]) -> Dict[str, float]:\n        \"\"\"计算字段覆盖率\"\"\"\n        if not data:\n            return {}\n        \n        field_coverage = {}\n        total_records = len(data)\n        \n        # 计算核心字段覆盖率\n        core_fields = [\n            'announcement_id', 'title', 'organization', 'announcement_date',\n            'budget_amount', 'contact_person', 'contact_phone', 'tendering_method'\n        ]\n        \n        for field in core_fields:\n            filled_count = sum(1 for item in data if item.get(field))\n            coverage = (filled_count / total_records) * 100\n            field_coverage[field] = coverage\n        \n        return field_coverage\n\ndef main():\n    \"\"\"主函数\"\"\"\n    print(\"🌟 修正版完美爬虫 - 解决数据源访问问题\")\n    print(\"=\" * 80)\n    \n    try:\n        crawler = PerfectCrawlerFixed()\n        \n        # 运行修正版爬虫\n        result = crawler.run_fixed_crawl(\n            keyword='',  # 可以设置关键词\n            search_types=['basic'],  # 从基础搜索开始\n            max_pages=2\n        )\n        \n        print(\"\\n\" + \"=\"*80)\n        print(\"               🎯 修正版爬取结果\")\n        print(\"=\"*80)\n        print(f\"📊 状态: {result['status']}\")\n        print(f\"📈 总数据量: {result['total_items']} 条\")\n        \n        if result.get('data_sample'):\n            print(\"\\n📋 数据样本 (前3条):\")\n            for i, item in enumerate(result['data_sample'], 1):\n                print(f\"\\n   [{i}] {item.get('announcement_id', 'N/A')} - {item.get('title', '无标题')[:50]}...\")\n                print(f\"       机关: {item.get('organization', 'N/A')}\")\n                print(f\"       日期: {item.get('announcement_date', 'N/A')}\")\n                print(f\"       金额: {item.get('budget_amount', 'N/A')}\")\n                print(f\"       联络: {item.get('contact_phone', 'N/A')}\")\n        \n        print(\"\\n📊 字段覆盖率:\")\n        for field, coverage in result.get('field_coverage', {}).items():\n            status = \"✅\" if coverage >= 70 else \"⚠️\" if coverage >= 30 else \"❌\"\n            print(f\"   {status} {field}: {coverage:.1f}%\")\n        \n        print(\"\\n💾 文件位置:\")\n        for format_type, path in result.get('saved_files', {}).items():\n            print(f\"   {format_type.upper()}: {path}\")\n        \n        return result\n        \n    except Exception as e:\n        logger.error(f\"主程序执行出错: {e}\")\n        return {'status': 'error', 'error': str(e)}\n\nif __name__ == \"__main__\":\n    result = main()\n
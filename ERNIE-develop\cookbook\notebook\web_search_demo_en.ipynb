{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Online Search Demo\n", "\n", "This tutorial demonstrates how to implement a question-and-answer system that supports networked search, and the system is based on the ERNIE models.\n", "\n", "The system automatically retrieves the latest network information by calling AI search tools, meanwhile leverage the semantic understanding capabilities of large language models (LLMs) to get a better experience to users."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environmental Setup\n", "Before starting, ensure your system meets these requirements:\n", "- Python version 3.10-3.12 is installed\n", "- Ensure the following Python libraries are included: `openai`, `json`, `textwrap`, `jieba`, `requests`, `crawl4ai`、`asyncio`、`nest_asyncio`\n", "    - The installation steps of `crawl4ai` can be found in [crawl4ai installation instructions](https://github.com/unclecode/crawl4ai)\n", "- Deploy [ERNIE-4.5](https://github.com/PaddlePaddle/FastDeploy) series model services and correctly configure the corresponding service address `host_url`\n", "- Set the API key `qianfan_api_key`. You can log in to [<PERSON><PERSON><PERSON>](https://console.bce.baidu.com/iam/#/iam/apikey/list) to create your API key. API keys are sensitive information and should be kept properly"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Model service address configuration\n", "host_url = \"http://localhost:port/v1\"\n", "\n", "# AI Search Tool Configuration\n", "qianfan_api_key = \"bce-v3/xxx\"  # Replace with your real API key\n", "web_search_service_url = \"https://qianfan.baidubce.com/v2/ai_search/chat/completions\"  # AI Search Tool URL\n", "model_api_key = \"api_key\" # the API key for model, which can be disregarded and replaced with any arbitrary value when using the model deployed locally."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.1. Install Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install openai jieba requests asyncio nest_asyncio\n", "\n", "# Install crawl4ai\n", "!pip install -U crawl4ai\n", "!pip install crawl4ai --pre\n", "!crawl4ai-setup"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Main Search Structure Overview\n", "\n", "- **Step 1: Search Query Rewriting**\n", "    \n", "    First, analyze whether the user's query needs obtain the latest information from the internet. When searching, rewrite the user's query to get the queries that be searched.\n", "\n", "- **Step 2: Get the Full Search Results**\n", "\n", "    crawl the complete web page content from the `URL` in the search results based on AI search tools.\n", "\n", "- **Step 3: Generate the final answer**\n", "\n", "    Organize the search results and call the model's interface to generate answers.\n", "\n", "## 3. Search Query Rewriting\n", "This step requires the model to determine whetherthe user's query needs obtain the latest information from the internet, and rewrite the user's query to get the queries that be searched.\n", "\n", "A prompt is required to guide the model to complete the task and return the standardized JSON format results."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## 当前时间\n", "2025-06-25 14:52:18\n", "\n", "## 对话\n", "user:\n", "你好\n", "assistant:\n", "你好，我是你的智能搜索助手，请问有什么我可以帮助你的？\n", "\n", "问题：上海有什么美食\n", "\n", "根据当前时间和对话完成以下任务：\n", "1. 查询判断：是否需要借助搜索引擎查询外部知识回答用户当前问题。\n", "2. 问题改写：改写用户当前问题，使其更适合在搜索引擎查询到相关知识。\n", "注意：只在**确有必要**的情况下改写，输出不超过 5 个改写结果，不要为了凑满数量而输出冗余问题。\n", "\n", "## 输出如下格式的内容（只输出 JSON ，不要给出多余内容）：\n", "```json\n", "{\n", "    \"is_search\":true/false,\n", "    \"query_list\":[\"改写问题1\"，\"改写问题2\"...]\n", "}```\n", "\n"]}], "source": ["import textwrap\n", "from datetime import datetime\n", "\n", "SEARCH_INFO_PROMPT = textwrap.dedent(\n", "    \"\"\"\\\n", "## Current time\n", "    {date}\n", "\n", "## Conversation\n", "    {context}\n", "    问题：{query}\n", "\n", "    根据当前时间和对话完成以下任务：\n", "    1. 查询判断：是否需要借助搜索引擎查询外部知识回答用户当前问题。\n", "    2. 问题改写：改写用户当前问题，使其更适合在搜索引擎查询到相关知识。\n", "    注意：只在**确有必要**的情况下改写，输出不超过 5 个改写结果，不要为了凑满数量而输出冗余问题。\n", "\n", "## Output content in the following format (only output JSON, do not give extra content):\n", "    ```json\n", "    {{\n", "        \"is_search\":true/false,\n", "        \"query_list\":[\"改写问题1\"，\"改写问题2\"...]\n", "    }}```\n", "    \"\"\"\n", ")\n", "history = \"user:\\n你好\\nassistant:\\n你好，我是你的智能搜索助手，请问有什么我可以帮助你的？\\n\"\n", "query = \"上海有什么美食\"\n", "search_content = SEARCH_INFO_PROMPT.format(\n", "    date=datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    context=history,\n", "    query=query\n", ")\n", "print(search_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Call the model interface for judgment."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'chatcmpl-af6a1428-1dc0-41f4-860a-9786b741005f', 'choices': [{'finish_reason': 'stop', 'index': 0, 'logprobs': None, 'message': {'content': '```json\\n{\\n    \"is_search\": true,\\n    \"query_list\": [\"上海美食推荐\", \"上海特色美食\", \"上海必吃美食\", \"上海美食排行榜\"]\\n}\\n```</s></s>', 'refusal': None, 'role': 'assistant', 'annotations': None, 'audio': None, 'function_call': None, 'tool_calls': None, 'reasoning_content': None}}], 'created': 1750768029, 'model': 'default', 'object': 'chat.completion', 'service_tier': None, 'system_fingerprint': None, 'usage': {'completion_tokens': 46, 'prompt_tokens': 201, 'total_tokens': 247, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}}\n"]}], "source": ["from openai import OpenAI\n", "\n", "judge_search_messages = [{\"role\": \"user\", \"content\": search_content}]\n", "\n", "client = OpenAI(base_url=host_url, api_key=model_api_key)\n", "response = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=judge_search_messages\n", ")\n", "\n", "response = response.model_dump()\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["parse the model's results to json format.\n", "- `is_search`: Whether to search online.\n", "- `query_list`: List of queries to be searched."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'is_search': True, 'query_list': ['上海美食推荐', '上海特色美食', '上海必吃美食', '上海美食排行榜']}\n"]}], "source": ["import json\n", "import re\n", "\n", "search_query = response[\"choices\"][0][\"message\"][\"content\"]\n", "json_match = re.search(r'```json\\n(.*?)\\n```', search_query, re.DOTALL)\n", "json_str = json_match.group(1)\n", "search_query = json.loads(json_str)\n", "\n", "print(search_query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Get the Full Search Results\n", "\n", "### 4.1. AI Search\n", "\n", "If previous results show the query is needed to be searched online, use the AI ​​search tool to obtain the search results.\n", "\n", "AI search tool request parameters:\n", "- `messages`: Search for input\n", "    - `role`: The role of the message sender, which can be a `user` user message, indicating the user's input; or an `assistant` model message, which can represent the model's reply\n", "    - `content`: The content text of the message\n", "- `resource_type_filter`: search result filtering\n", "    - `type`: Search type. In this example, only the setting of web search modals is supported.\n", "    - `top_k`: Maximum number of the result\n", "\n", "For more request parameters in AI search, please refer to [Baidu AI Search](https://cloud.baidu.com/doc/AppBuilder/s/zm8pn5cju)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'content': ' 晚上7点一开市,设在露天广场上的夜集市迎来当天的首批食客。徐州烧烤、淄博烧烤、阜阳卷馍、东北大饭包、青岛酱蘸鲍鱼、江西炒粉……随着一个个摊位忙碌起来,各种美食的香气随之在空气中弥漫,引得食客胃口大开。 “这是东北大饭包,你看,白菜叶子里面包了好多料,土豆泥啊什么的,想加什么给你加,好大一只。” “之前就在路边摆摊,一天到晚提心吊胆的,我有5个顾客群,大概2000人左右,我每天出摊的时候就发定位告诉大家,我今天会在哪条路边摆,不时换地方,今天不知道明天咋样……这感觉不好受。现在好了。” 经前期调研发现,在罗店自发形成的占道经营摊点日均达100余处。像陶晓哲这样的流动餐饮摊贩带来的噪声、垃圾、油烟等各种扰民问题,是罗店镇城运中心最常接到的市民投诉。 流动摊贩集中规范运营便民点应运而生。', 'date': '2025-06-21 21:16:06', 'icon': None, 'id': 1, 'image': None, 'title': '开市仅一个多月,北上海这个美食夜集市怎么就火了……', 'type': 'web', 'url': 'https://www.jfdaily.com/sgh/detail?id=1597987', 'video': None, 'web_anchor': '开市仅一个多月,北上海这个美食夜集市怎么就火了……'}, {'content': ' 摩洛哥风情海鲜总汇塔吉锅 20元可以升级 红鱼柳 黑虎虾 鱿鱼 鲍鱼 连蛤蜊都饱满鲜嫩 酱汁酸辣带点奶香 法棍蘸着吃直接嗦手指 满满一锅海鲜的快乐太过瘾了 辣芝士雪峰牛排牛骨髓 太会了 五分熟牛排一刀切爆汁 牛骨髓挖出来胶质感裹着肉香搭配着牛排 再蘸点旁边的辣酱和土豆泥 口感层次丰富 加量不加价 以前套餐里半只的墨西哥风味烤鸡现在直接上整只 鸡皮烤得焦香滴油 鸡胸肉都不带柴的 配的香脆薯角 一口鸡一口薯 快乐double 仙人掌盆栽蛋糕 很萌 奶冻打底 巧克力碎 小饼干 挖一勺入口 冰凉细腻带点可可香 吃完连花盆都想舔干净 这套餐量也太实诚了 我们俩人撑到实在吃不下只能打包 在世纪大道这地段 性价比直接杀疯了 AMINO AMIGO 世纪汇店 世纪大道1192号世纪汇广场LG1层020 世纪大道地铁8号口出口 户外广场', 'date': '2025-06-11 00:00:00', 'icon': None, 'id': 2, 'image': None, 'title': '上海美食推荐清单|世纪大道地铁口!终于西餐也能吃到过瘾了!', 'type': 'web', 'url': 'https://m.ctrip.com/webapp/you/tripshoot/paipai/detail/detail?articleId=158732057&isHideNavBar=YES&seo=0&twojumpwakeup=0&allianceId=1049189&sid=19855591', 'video': None, 'web_anchor': '上海美食推荐清单|世纪大道地铁口!终于西餐也能吃到过瘾了!'}, {'content': '\\U000f098e 上海\\uf045 旬味会 外滩 旬味会 外滩 热搜: 美食试吃官2折美食套餐旬味会 美食林榜单 特色菜 本周热销 旬味会 免费订座 美食必打卡榜 当地风味餐厅榜 特色小吃店榜 奢华餐厅榜 美景体验餐厅榜 酒吧榜 夜店榜 位置\\uf045 菜系\\uf045 筛选\\uf045 智能排序\\uf045 2024榜单餐厅 在线订座 套餐/代金券 本帮菜 外滩 人均100-200 \\U000f0769 上海和平饭店·龙凤厅 \\ue933 上海和平饭店 内餐厅 2024亚洲100当地风味餐厅 4.8分|636条点评|￥674/人 本帮菜 近外滩 距市中心1.8km 莫尔顿牛排坊(浦东ifc店) 订 \"源自波士顿的牛排餐厅,品味美食的同时还能欣赏陆家嘴的繁华景象。\" 4.7分|146条点评|￥898/人 西餐近陆家嘴 距市中心2.7km 上海浦东丽思卡尔顿酒店·金轩中餐厅 订 \\ue933 上海浦东丽思卡尔顿酒店 内餐厅 \\U000f0885 铂金 2025全球100美景体验餐厅 4.8分|259条点评|￥773/人 粤菜近陆家嘴 距市中心2.7km 上海璞丽酒店·LONG BAR长吧 \\ue933 上海璞丽酒店 内餐厅 \"菜品可依个人喜好搭配,尽情享受定制美食。', 'date': '2023-03-05 00:00:00', 'icon': None, 'id': 3, 'image': None, 'title': '上海热门美食攻略/排行榜单/大全/介绍_必吃特色小吃/菜_推荐/介绍_上海好吃的旅行餐厅【携程美食林】', 'type': 'web', 'url': 'https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html', 'video': None, 'web_anchor': '上海热门美食攻略/排行榜单/大全/介绍_必吃特色小吃/菜_推荐/介绍_上海好吃的旅行餐厅【携程美食林】'}]\n"]}], "source": ["import requests\n", "\n", "max_search_results_num = 15\n", "if search_query.get(\"is_search\", False) and search_query.get(\"query_list\", []):\n", "    headers = {\n", "        \"Authorization\": \"Bearer \" + qianfan_api_key,\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "\n", "    search_result = []\n", "    top_k = max_search_results_num // len(search_query[\"query_list\"])\n", "    for query in search_query[\"query_list\"]:\n", "        payload = {\n", "            \"messages\": [{\"role\": \"user\", \"content\": query}],\n", "            \"resource_type_filter\": [{\"type\": \"web\", \"top_k\": top_k}]\n", "        }\n", "        response = requests.post(web_search_service_url, headers=headers, json=payload)\n", "        response = response.json()\n", "\n", "        search_result.append(response[\"references\"])\n", "    print(search_result[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The results returned by the search mainly include:\n", "- `content`: Web page summary\n", "- `title`: web page title\n", "- `url`: Web page URL\n", "\n", "For more response parameters descriptions, please refer to [Baidu AI Search](https://cloud.baidu.com/doc/AppBuilder/s/zm8pn5cju)\n", "\n", "### 4.2. Crawl the Complete Web Page Content\n", "\n", "Since `content` only contains part of the web page content, you need to crawl the text in the URL to obtain the complete search results.\n", "\n", "Use the `crawl4ai` tool to crawl the web page text data and replace the original `content`"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">31s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m31s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">15s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m15s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">47s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m47s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["进入词条全站搜索\n", "进入词条全站搜索\n", "播报编辑收藏赞\n", "登录\n", "# 上海菜\n", "播报编辑上传视频\n", "江南地区传统饮食文化的一种流派\n", "许魏洲邀你吃“不要太灵的”上海本帮菜\n", "03:35\n", "上海传统美食大合集!生煎小笼本帮菜,根本吃不够!\n", "17:59\n", "吃了30多年红烧肉，只服这种特色做法，色泽红亮肥而不腻，真过瘾\n", "10:08\n", "上海菜十大名菜有哪些：让你领略上海独特美味！\n", "01:49\n", "去北京开的上海老饭店吃本帮菜，这几个菜就花了500多，烤菜吃不惯\n", "03:41\n", "外国丈母娘第一次来中国吃上海本帮菜，被震撼到，中国菜这么好吃\n", "08:58\n", "上海外滩吃顿本帮菜，不知道正宗不\n", "00:18\n", "老洋房里快40年的上海菜，很低调，要不是排队谁会以为是饭店\n", "02:07\n", "开在古镇里面的本帮菜老店\n", "01:55\n", "老弄堂里30多年上海菜，老板“大半个香港娱乐圈都来过”\n", "01:46\n", "上海弄堂里的本帮菜馆，店不大生意爆棚，没开门就得排队\n", "05:38\n", "这才是上海本帮菜的水平，红烧划水太惊艳，小店6张桌座无虚席\n", "04:20\n", "收藏\n", "查看\n", "585有用+1\n", "102\n", "沪菜，即上海菜，是其常用的烹调方法以红烧、煨、糖为主，以浓油赤酱、咸淡适中、保持原味、醇厚鲜美为其特色。 [4]\n", "上海菜的别称为本帮菜，是江南地区传统饮食文化的一个重要流派。 自1843年上海开埠以来，随着工商业的发展，四方商贾云集，饭店酒楼应运而生。到20世纪三四十年代，有京、广、苏、扬、锡、甬、杭、闽、川、徽、潮、湘、以及上海本地菜等十六个帮别，同时还有素菜、清真菜，各式西菜、西点。 此时，上海菜原以红烧、生煸见长，后来吸取无锡、苏州、宁波等地方菜的特点，为适应上海人喜食清淡爽口的口味，菜肴渐由原来的重油赤酱趋向淡雅爽口，同时，兼及西菜、西点之法，使花色品种有了很大的发展，拥有鱼下巴甩水、腌川红烧圈子、白斩鸡、松江鲈鱼等名菜。 [4]\n", "2014年，上海本帮菜肴传统烹饪技艺被列入国家级非物质文化遗产代表性项目名录。 [5]2018年9月，八宝鸭被评为“中国菜”之上海十大经典名菜。 [6]\n", "## 相关星图\n", "常见的中国菜系\n", "共36个词条23.3万阅读中文名\n", "上海菜别 名\n", "沪菜、本帮菜 [4]产 地\n", "上海 [4]名 菜\n", "鱼下巴甩水、腌川红烧圈子、白斩鸡、松江鲈鱼 [4]烹调方法\n", "以红烧、煨、糖为主 [4]特 点\n", "浓油赤酱、咸淡适中、保持原味、醇厚鲜美 [4]概 念\n", "食品，烹饪口 味\n", "淡雅爽口 [4]\n", "## 目录\n", "1. 1烹饪特色\n", "2. 2历史发展\n", "3. 3经典名菜\n", "4. 4著名食馆\n", "5. ▪德兴菜馆1. ▪上海美食街\n", "2. ▪王宝和酒家\n", "3. 5名菜制作\n", "4. ▪白斩鸡\n", "5. ▪猪油百果松糕\n", "6. ▪醉鸡1. ▪葡萄鱼\n", "2. ▪河虾争妍\n", "3. ▪蟹肉大排翅\n", "4. ▪碧玉牛筋\n", "5. ▪八宝鸭\n", "6. ▪沪江排骨1. ▪鹅肝酱片\n", "2. ▪清蒸大闸蟹\n", "3. 6沪菜现状## 烹饪特色\n", "播报\n", "编辑\n", "上海菜原以红烧、生煸见长。后来，吸取了无锡、苏州、宁波等地方菜的特点，参照上述十六帮别的烹调技术，兼及西菜、西点之法，使花色品种有了很大的发展。\n", "菜肴风味的基本特点：汤卤醇厚，浓油赤酱，糖重色艳，咸淡适口。选料注重活、生、寸、鲜；调味擅长咸、甜、糟、酸。名菜如“红烧蛔鱼”，巧用火候，突出原味，色泽红亮，卤汁浓厚，肉质肥嫩，负有盛誉。\n", "“糟钵头”则是上海本地菜善于在烹调中加“糟”的代表，把陈年香糟加工复制成糟卤，在烧制中加入，使菜肴糟香扑鼻，鲜味浓郁。\n", "“生煸草头”，摘梗留叶，重油烹酒，柔软鲜嫩，蔚成一格。而各地方风味的菜肴也逐步适应上海的特点，发生了不同的变革，如川菜从重辣转向轻辣，锡菜从重甜改为轻甜，还有不少菜馆吸取外地菜之长。经过长期的实践，在取长补短的基础上。改革了烹调方法，上海菜达到了品种多样，别具一格，形成了上海菜的独特风味。\n", "由于上海本地菜（包括苏锡菜）与外地菜长期共存，相互影响，便在原本地菜的基础上，逐渐发展成以上海和苏锡风味为主体并兼有各地风味的上海风味菜体系。\n", "上海菜具有许多与众不同的特点：\n", "首先讲究选料新鲜。它选用四季时令蔬菜，鱼是以江浙两省产品为主，取活为上，一年四季都有活鱼供客选择，当场活杀烹制。\n", "第二菜肴品种多，四季有别。\n", "第三讲究烹调方法并不断加以改进。上海菜原来以烧、蒸、煨、窝、炒并重，逐渐转为以烧、生煸、滑炒、蒸为主，其中以生煸、滑炒为最多特别善烹四季河鲜。\n", "第四口味也有了很大变化。原来上海菜以浓汤、浓汁厚味为主，后来逐步变为卤汁适中，有清淡素雅，也有浓油赤酱，讲究鲜嫩、色调，鲜咸适口。特别是夏秋季节的糟味菜肴，香味浓郁，颇有特色。\n", "如今，上海菜进一步具有选料新鲜、品质优良、刀工精细、制作考究、火候恰当、清淡素雅、咸鲜适中、口味多样、适应面广、风味独特等优点。其主要名菜有“青鱼下巴甩水”、“青鱼秃肺”、“腌川红烧圈子”...\n"]}], "source": ["import asyncio\n", "import re\n", "\n", "import nest_asyncio\n", "from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig, DefaultMarkdownGenerator, PruningContentFilter\n", "\n", "\n", "async def get_webpage_text(url):\n", "    content_filter = PruningContentFilter(\n", "        threshold=0.48,\n", "        threshold_type=\"fixed\",\n", "        min_word_threshold=0\n", "    )\n", "    # Config makedown generator\n", "    md_generator = DefaultMarkdownGenerator(\n", "        content_filter=content_filter\n", "    )\n", "    run_config = CrawlerRunConfig(\n", "        # 20 seconds page timeout\n", "        page_timeout=20000,\n", "\n", "        # Filtering\n", "        word_count_threshold=10,\n", "        excluded_tags=[\"nav\", \"footer\", \"aside\", \"header\", \"script\", \"style\", \"iframe\", \"meta\"],\n", "        exclude_external_links=True,\n", "        exclude_internal_links=True,\n", "        exclude_social_media_links=True,\n", "        exclude_external_images=True,\n", "        only_text=True,\n", "\n", "        # Markdown generation\n", "        markdown_generator=md_generator,\n", "\n", "        # Cache\n", "        cache_mode=CacheMode.BYPASS\n", "    )\n", "    try:\n", "        async with AsyncWebCrawler() as crawler:\n", "            result = await crawler.arun(\n", "                url=url,\n", "                config=run_config\n", "            )\n", "\n", "        webpage_text = result.markdown.fit_markdown\n", "\n", "        # Clean up the text\n", "        cleaned_text = webpage_text.replace(\"undefined\", \"\")\n", "        cleaned_text = re.sub(r'(\\n\\s*){3,}', '\\n\\n', cleaned_text)\n", "        cleaned_text = re.sub(r'[\\r\\t]', '', cleaned_text)\n", "        cleaned_text = re.sub(r' +', ' ', cleaned_text)\n", "        cleaned_text = re.sub(r'^\\s+|\\s+$', '', cleaned_text, flags=re.MULTILINE)\n", "        return cleaned_text.strip()\n", "\n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "\n", "nest_asyncio.apply()\n", "loop = asyncio.get_event_loop()\n", "\n", "web_url = \"https://baike.baidu.com/item/上海菜/672867\"\n", "web_page_text = loop.run_until_complete(get_webpage_text(web_url))\n", "print(web_page_text[: 2000] + \"...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Integrate complete search results. To avoid too long search results, you need to limit the length of search content characters by `max_search_results_char`."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.jfdaily.com/sgh/detail?id=1597987</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                        |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">13s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://www.jfdaily.com/sgh/detail?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1597987\u001b[0m\u001b[32m                                                        |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m13s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.jfdaily.com/sgh/detail?id=1597987</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                        |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">04s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://www.jfdaily.com/sgh/detail?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1597987\u001b[0m\u001b[32m                                                        |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m04s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.jfdaily.com/sgh/detail?id=1597987</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                        |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">18s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://www.jfdaily.com/sgh/detail?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1597987\u001b[0m\u001b[32m                                                        |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m18s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache /var/folders/gk/66ypgljn2gg7971dkhnm1q5w0000gn/T/jieba.cache\n", "Loading model cost 0.333 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.ctrip.com/webapp/you/tripshoot/paipai/...&amp;twojumpwakeup=0&amp;allianceId=1049189&amp;sid=19855591</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">07s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://m.ctrip.com/webapp/you/tripshoot/paipai/...&\u001b[0m\u001b[4;32mtwojumpwakeup\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m0\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mallianceId\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1049189\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32msid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m19855591\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m07s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.ctrip.com/webapp/you/tripshoot/paipai/...&amp;twojumpwakeup=0&amp;allianceId=1049189&amp;sid=19855591</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">00s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://m.ctrip.com/webapp/you/tripshoot/paipai/...&\u001b[0m\u001b[4;32mtwojumpwakeup\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m0\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mallianceId\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1049189\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32msid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m19855591\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m00s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.ctrip.com/webapp/you/tripshoot/paipai/...&amp;twojumpwakeup=0&amp;allianceId=1049189&amp;sid=19855591</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">08s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://m.ctrip.com/webapp/you/tripshoot/paipai/...&\u001b[0m\u001b[4;32mtwojumpwakeup\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m0\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mallianceId\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1049189\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32msid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m19855591\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m08s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html</span><span style=\"color: #008000; text-decoration-color: #008000\">                                         |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">50s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html\u001b[0m\u001b[32m                                         |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m50s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html</span><span style=\"color: #008000; text-decoration-color: #008000\">                                         |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">02s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html\u001b[0m\u001b[32m                                         |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m02s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html</span><span style=\"color: #008000; text-decoration-color: #008000\">                                         |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">52s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html\u001b[0m\u001b[32m                                         |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m52s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.thepaper.cn/newsDetail_forward_31025474</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                    |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">91s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://m.thepaper.cn/newsDetail_forward_31025474\u001b[0m\u001b[32m                                                    |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m91s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.thepaper.cn/newsDetail_forward_31025474</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                    |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">01s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://m.thepaper.cn/newsDetail_forward_31025474\u001b[0m\u001b[32m                                                    |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m01s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.thepaper.cn/newsDetail_forward_31025474</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                    |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">93s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://m.thepaper.cn/newsDetail_forward_31025474\u001b[0m\u001b[32m                                                    |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m93s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">27s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m27s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">09s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m09s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">36s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m36s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828711351827613727&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">89s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828711351827613727\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m89s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828711351827613727&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">03s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828711351827613727\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m03s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828711351827613727&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">92s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828711351827613727\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m92s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1834297223163493130&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">07s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1834297223163493130\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m07s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1834297223163493130&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">03s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1834297223163493130\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m03s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1834297223163493130&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">11s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1834297223163493130\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m11s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1835639030190648447&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">04s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1835639030190648447\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m04s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1835639030190648447&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">03s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1835639030190648447\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m03s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1835639030190648447&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">08s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1835639030190648447\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m08s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://new.qq.com/rain/a/20250620A04RMZ00</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                           |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">19s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://new.qq.com/rain/a/20250620A04RMZ00\u001b[0m\u001b[32m                                                           |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m19s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://new.qq.com/rain/a/20250620A04RMZ00</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                           |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">02s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://new.qq.com/rain/a/20250620A04RMZ00\u001b[0m\u001b[32m                                                           |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m02s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://new.qq.com/rain/a/20250620A04RMZ00</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                           |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">21s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://new.qq.com/rain/a/20250620A04RMZ00\u001b[0m\u001b[32m                                                           |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m21s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828709688765599122&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">06s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828709688765599122\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m06s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828709688765599122&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">03s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828709688765599122\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m03s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828709688765599122&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">10s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828709688765599122\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m10s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import jieba\n", "\n", "\n", "def cut_chinese_english(text):\n", "    words = jieba.lcut(text)\n", "    en_ch_words = []\n", "\n", "    for word in words:\n", "        if word.isalpha() and not any(\"\\u4e00\" <= char <= \"\\u9fff\" for char in word):\n", "            en_ch_words.append(word)\n", "        else:\n", "            en_ch_words.extend(list(word))\n", "    return en_ch_words\n", "\n", "async def get_complete_search_content(search_results: list, max_search_results_char: int=18000) -> str:\n", "    results = []\n", "    for search_res in search_results:\n", "        for item in search_res:\n", "            new_content = await get_webpage_text(item[\"url\"])\n", "            if not new_content:\n", "                continue\n", "            item_text = \"Title: {title} \\nURL: {url} \\nContent:\\n{content}\\n\".format(title=item[\"title\"], url=item[\"url\"], content=new_content)\n", "\n", "# Truncate the search result to max_search_results_char characters\n", "            search_res_words = cut_chinese_english(item_text)\n", "            res_words = cut_chinese_english(\"\".join(results))\n", "            if len(search_res_words) + len(res_words) > max_search_results_char:\n", "                break\n", "\n", "            results.append(f\"参考资料[{len(results) + 1}]:\\n{item_text}\\n\")\n", "\n", "    return \"\".join(results)\n", "\n", "complete_search_result = loop.run_until_complete(get_complete_search_content(search_result))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Generate Final Answer\n", "### 5.1. Model Input\n", "The model input is a message list that represents the context history of the conversation. Each message is a dictionary containing the following fields:\n", "- `role`: Represents the role of the message sender, which can be:\n", "    - `user`: User message, indicating user input\n", "    - `assistant`: Model message, indicating the model's reply\n", "- `content`: Specific text content\n", "\n", "the input has the following characteristics:\n", "- Network Search: splice the complete search results to `ANSWER_PROMPT` and provide it to the model as a context.\n", "- Multiple Rounds of Dialogue: Supporting the preservation of historical dialogue context"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'user', 'content': '下面你会收到多段参考资料和一个问题。你的任务是阅读参考资料，并根据参考资料中的信息回答对话中的问题。\\n以下是当前时间和参考资料：\\n---------\\n## 当前时间\\n2025-06-24 19:31:40\\n\\n## 参考资料\\n参考资料[1]:\\nTitle: 开市仅一个多月,北上海这个美食夜集市怎么就火了…… \\nURL: https://www.jfdaily.com/sgh/detail?id=1597987 \\nContent:\\n用户名：\\n---\\n密 码：\\n验证码： | 看不清\\n2025-06-24星期二\\n模糊搜索 作者搜索 标题搜索 正文搜索 摘要搜索\\n我的位置： > > 文章详情\\n# 开市仅一个多月，北上海这个美食夜集市怎么就火了……\\n转自：上海宝山 2025-06-21 21:16:06\\n位于宝山区罗店镇7号线罗南新村地铁站外的\\n流动摊贩集中规范运营（临时）便民点\\n最近在社交平台上火了起来\\n一百多个摊位\\n汇聚了全国各地的特色小吃\\n成了人们体验城市烟火气的一处网红夜集市\\n点燃城市烟火气的同时\\n如何让城市管理文明有序\\n罗店镇的这一探索\\n试图寻找治理的新路径\\nPART.1\\n晚上7点一开市，设在露天广场上的夜集市迎来当天的首批食客。徐州烧烤、淄博烧烤、阜阳卷馍、东北大饭包、青岛酱蘸鲍鱼、江西炒粉……随着一个个摊位忙碌起来，各种美食的香气随之在空气中弥漫，引得食客胃口大开。\\n“这种烟火气还能找得到，挺好的，以前我们俩都是晚上开车去昆山去吃夜宵。”\\n“我正好地铁从市里下班回来，就顺便过来吃点东西，这里想吃什么都有，而且有统一管理，蛮规范的，吃起来也放心吧。”\\n“这是东北大饭包，你看，白菜叶子里面包了好多料，土豆泥啊什么的，想加什么给你加，好大一只。”\\n自5月初运营以来，夜集市备受周边居民和食客的青睐，晚上7点到次日凌晨2点运营期间，每天平均迎来食客一千多人次，节假日高峰时甚至可达四五千人次。吉林小串的摊主陶晓哲忙个不停。\\n“这里人流量大，而且不用提心吊胆，营业额也挺可观的，原来的话一天营业额在3000~5000元，现在好的时候就1万多。”\\n两年前，为了陪在上海工作的女儿，快50岁的陶晓哲和老公来到罗店，开始摆起了烧烤摊。在老家有过10多年开烧烤店的经验，夫妻俩的小摊很快就吸引了不少食客光顾。但路边摆摊的经历并不好受。\\n“之前就在路边摆摊，一天到晚提心吊胆的，我有5个顾客群，大概2000人左右，我每天出摊的时候就发定位告诉大家，我今天会在哪条路边摆，不时换地方，今天不知道明天咋样……这感觉不好受。现在好了。”\\nPART.2\\n经前期调研发现，在罗店自发形成的占道经营摊点日均达100余处。像陶晓哲这样的流动餐饮摊贩带来的噪声、垃圾、油烟等各种扰民问题，是罗店镇城运中心最常接到的市民投诉。\\n但作为上海北部人口集中导入区域，近年来，美兰湖和罗店大居片区年轻住户和租客人数上升，而他们，正是夜集市消费的主力军。原有配套公建设施已无法满足居民日常需求，特别是受经济大环境影响，小餐饮、便民服务等基础保障方面存在明显缺口。\\n一边是客观存在的消费需求，一边是实实在在的管理问题，如何在二者之间寻求平衡？\\n流动摊贩集中规范运营便民点应运而生。罗店镇城运中心相关负责人说，沪太路杨南路西北侧，原金岷大厦西侧空地这一点位的选择，是经过对周边居民影响最小、交通便利等多方面仔细研判而定。\\n“这个地块离地铁站近，又属于储备地块，就是规土部门交由罗店镇代为管理的，大约4000平方米左右，此前是用于居民非机动车临时停放。地块西边和北边都是储备地块，只有一面靠近居民区，但也有150多米的直线距离，且居民大多是租户，其中，二三十岁的年轻租户达九成以上。”\\n地点确定后，实现有序规范运行，后续管理必须跟上。于是，管理方制定了详细的管理规定和操作规程，明确摊位经营时间、消防安全要求。同时，还统一提供基础水电，消防设施、移动厕所、机动车停放区域，安排多名保洁人员清理现场垃圾、维持场地整洁。\\n现场管理方负责人说，为确保食品安全和消费者权益，对摊贩的入场资格也有严格的筛选机制。\\n“打个比方，烧烤摊必须有油烟净化器，所有的刀具锅铲等需要配备一个索道链，把它们控制在自己的操作范围之内，防止伤害他人。每日的进货单、健康证这是必备的。一些需要冷鲜柜的必须得配备，比如说肉类食品，天慢慢热了之后可能会容易变质。”\\n管理方还设立了退出机制：对违反管理规定的摊主，首次警告并限期整改，二次违规将暂停营业一周，三次违规则取消经营资格。同时，设立线下居民投诉点，及时处理反馈问题，确保居民权益得到有效保障。建立了针对摊主的负面清单制度，记录违规行为，定期公示。\\n通过多维度监管，既满足居民生活需求，又维护市容秩序，实现双赢。\\nPART.3\\n管理规范、公平有序、交通便利、每天最多百余元的管理费、零租金零押金……便民点一开市，就吸引流动摊贩争相报名。进场经营的摊主从最初的130家增加到现在的150家，便民点也在食客的口口相传，在社交平台的助推下，成为“网红夜集市”。\\n“摊主挣得到钱，老百姓觉得这个地方又卫生又规范，也愿意来。再加上管理方日常的规范管理，我认为，这几个方面最终促成了这个点位能够做到良性的循环。”\\n这一切在罗店镇城运中心相关负责人看来，意料之外，情理之中。\\n经营场所和客流有了保障后，经营奶茶和烧烤的“90后”摊主杨琦有了更长远打算的底气。\\n“现在有政府托底以后，我们就更有信心去做这件事情了，把它做好，让更多的顾客过来，然后把它当一个事业去干了，我们也想看看我们能不能像一个小的连锁一样，在网上招募一些志同道合的朋友一起把这个事情给做大。”\\n日前，上海发布《关于进一步优化设摊治理 提升城市“烟火气”工作方案》，引发人们对夜集市经济的关注。热气腾腾的夜间消费景象背后，映射出人们的生活需求。但一些曾经爆红的夜集市如今已风光不再，也值得反思。\\n罗店夜集市如何从网红做到长红？\\n“罗店的夜集市与安义夜巷、泗泾夜集市有什么不一样？”在上海师范大学副教授刘德艳看来，绝不能仅仅依赖“烟火气”，挖掘地域特色与文化，打造专属IP才是助推夜集市经济长红的“软实力”。\\n“我觉得，罗店夜集市上要有特别罗店，特别美兰湖的东西，比如说罗店的非遗、罗店当地的民俗、罗店当地的土特产品，把这些东西融合在一起，慢慢形成一个集聚效应，变成除了美食之外的一个综合IP。文化、娱乐、社交等元素都能够有的话，它就变成了罗店镇的一张名片，也就具备了非去不可的理由。”\\n编辑：张思源\\n资料：话匣子\\n*转载请注明来自上海宝山官方微信\\n\\n参考资料[2]:\\nTitle: 上海美食推荐清单|世纪大道地铁口!终于西餐也能吃到过瘾了! \\nURL: https://m.ctrip.com/webapp/you/tripshoot/paipai/detail/detail?articleId=158732057&isHideNavBar=YES&seo=0&twojumpwakeup=0&allianceId=1049189&sid=19855591 \\nContent:\\n加载中\\n\\n参考资料[3]:\\nTitle: 上海热门美食攻略/排行榜单/大全/介绍_必吃特色小吃/菜_推荐/介绍_上海好吃的旅行餐厅【携程美食林】 \\nURL: https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html \\nContent:\\n忻府区\\uf045\\n\\ue69b\\n旬味会\\n海底捞订座福利\\n旬味会\\n海底捞订座福利\\n热搜：\\n海底捞福利美食试吃官2折美食套餐旬味会美食林榜单\\n海底捞\\n菜系\\n\\uf045\\n筛选\\n\\uf045\\n智能排序\\n\\uf045\\n* 套餐/代金券\\n* 人均100以内大福黑牛料理烤肉·烤鳗鱼\\n5.0分|2条点评|￥102/人\\n烧烤 忻州\\n距市中心4km\\n忻州云中河芳草地房车营地·河畔餐厅\\n\\ue933\\n忻州云中河芳草地房车营地 内餐厅\\n暂无点评，抢首评赢积分\\n山西菜 忻州\\n距市中心8.5km\\n惠538元 5-6人火锅/烧烤食材套餐（二选一）\\n忻州科澜酒店·科澜茶趣\\n\\ue933\\n忻州科澜酒店（忻府区店） 内餐厅\\n暂无点评，抢首评赢积分￥50/人\\n咖啡店 忻州\\n距市中心6.2km\\n王婆大虾(阳曲店)\\n5.0分|1条点评|￥51/人\\n海鲜 阳曲\\n距市中心38.7km\\n呷哺呷哺(大欣城店)\\n\"虾滑很好吃蔓越莓饮料也好喝哦\"\\n4.5分|136条点评|￥57/人\\n火锅 忻州\\n距市中心900m\\n必胜客(长征店)\\n4.3分|116条点评|￥68/人\\n快餐简餐 忻州\\n距市中心1.6km\\n海底捞火锅(开来欣悦店)\\n订\\n暂无点评，抢首评赢积分￥114/人\\n火锅 忻州\\n距市中心3km\\n秀容宴\\n1条点评|￥115/人\\n忻州\\n距市中心2.1km\\n口口香铁锅焖面(总店)\\n5.0分|3条点评|￥43/人\\n山西菜 忻州\\n距市中心2.2km\\n泛华大酒店中餐厅\\n\\ue933\\n忻州泛华大酒店 内餐厅\\n暂无点评，抢首评赢积分\\n山西菜 忻州\\n距市中心2.5km\\n\\n参考资料[4]:\\nTitle: 开市仅一个多月,北上海这个美食夜集市怎么就火了…… \\nURL: https://m.thepaper.cn/newsDetail_forward_31025474 \\nContent:\\n# 开市仅一个多月，北上海这个美食夜集市怎么就火了……\\n2025-06-21 20:33\\n上海\\n位于宝山区罗店镇7号线罗南新村地铁站外的\\n流动摊贩集中规范运营（临时）便民点\\n最近在社交平台上火了起来\\n一百多个摊位\\n汇聚了全国各地的特色小吃\\n成了人们体验城市烟火气的一处网红夜集市\\n点燃城市烟火气的同时\\n如何让城市管理文明有序\\n罗店镇的这一探索\\n试图寻找治理的新路径\\nPART.1\\n晚上7点一开市，设在露天广场上的夜集市迎来当天的首批食客。徐州烧烤、淄博烧烤、阜阳卷馍、东北大饭包、青岛酱蘸鲍鱼、江西炒粉……随着一个个摊位忙碌起来，各种美食的香气随之在空气中弥漫，引得食客胃口大开。\\n“这种烟火气还能找得到，挺好的，以前我们俩都是晚上开车去昆山去吃夜宵。”\\n“我正好地铁从市里下班回来，就顺便过来吃点东西，这里想吃什么都有，而且有统一管理，蛮规范的，吃起来也放心吧。”\\n“这是东北大饭包，你看，白菜叶子里面包了好多料，土豆泥啊什么的，想加什么给你加，好大一只。”\\n自5月初运营以来，夜集市备受周边居民和食客的青睐，晚上7点到次日凌晨2点运营期间，每天平均迎来食客一千多人次，节假日高峰时甚至可达四五千人次。吉林小串的摊主陶晓哲忙个不停。\\n“这里人流量大，而且不用提心吊胆，营业额也挺可观的，原来的话一天营业额在3000~5000元，现在好的时候就1万多。”\\n两年前，为了陪在上海工作的女儿，快50岁的陶晓哲和老公来到罗店，开始摆起了烧烤摊。在老家有过10多年开烧烤店的经验，夫妻俩的小摊很快就吸引了不少食客光顾。但路边摆摊的经历并不好受。\\n“之前就在路边摆摊，一天到晚提心吊胆的，我有5个顾客群，大概2000人左右，我每天出摊的时候就发定位告诉大家，我今天会在哪条路边摆，不时换地方，今天不知道明天咋样……这感觉不好受。现在好了。”\\nPART.2\\n经前期调研发现，在罗店自发形成的占道经营摊点日均达100余处。像陶晓哲这样的流动餐饮摊贩带来的噪声、垃圾、油烟等各种扰民问题，是罗店镇城运中心最常接到的市民投诉。\\n但作为上海北部人口集中导入区域，近年来，美兰湖和罗店大居片区年轻住户和租客人数上升，而他们，正是夜集市消费的主力军。原有配套公建设施已无法满足居民日常需求，特别是受经济大环境影响，小餐饮、便民服务等基础保障方面存在明显缺口。\\n一边是客观存在的消费需求，一边是实实在在的管理问题，如何在二者之间寻求平衡？\\n流动摊贩集中规范运营便民点应运而生。罗店镇城运中心相关负责人说，沪太路杨南路西北侧，原金岷大厦西侧空地这一点位的选择，是经过对周边居民影响最小、交通便利等多方面仔细研判而定。\\n“这个地块离地铁站近，又属于储备地块，就是规土部门交由罗店镇代为管理的，大约4000平方米左右，此前是用于居民非机动车临时停放。地块西边和北边都是储备地块，只有一面靠近居民区，但也有150多米的直线距离，且居民大多是租户，其中，二三十岁的年轻租户达九成以上。”\\n地点确定后，实现有序规范运行，后续管理必须跟上。于是，管理方制定了详细的管理规定和操作规程，明确摊位经营时间、消防安全要求。同时，还统一提供基础水电，消防设施、移动厕所、机动车停放区域，安排多名保洁人员清理现场垃圾、维持场地整洁。\\n现场管理方负责人说，为确保食品安全和消费者权益，对摊贩的入场资格也有严格的筛选机制。\\n“打个比方，烧烤摊必须有油烟净化器，所有的刀具锅铲等需要配备一个索道链，把它们控制在自己的操作范围之内，防止伤害他人。每日的进货单、健康证这是必备的。一些需要冷鲜柜的必须得配备，比如说肉类食品，天慢慢热了之后可能会容易变质。”\\n管理方还设立了退出机制：对违反管理规定的摊主，首次警告并限期整改，二次违规将暂停营业一周，三次违规则取消经营资格。同时，设立线下居民投诉点，及时处理反馈问题，确保居民权益得到有效保障。建立了针对摊主的负面清单制度，记录违规行为，定期公示。\\n通过多维度监管，既满足居民生活需求，又维护市容秩序，实现双赢。\\nPART.3\\n管理规范、公平有序、交通便利、每天最多百余元的管理费、零租金零押金……便民点一开市，就吸引流动摊贩争相报名。进场经营的摊主从最初的130家增加到现在的150家，便民点也在食客的口口相传，在社交平台的助推下，成为“网红夜集市”。\\n“摊主挣得到钱，老百姓觉得这个地方又卫生又规范，也愿意来。再加上管理方日常的规范管理，我认为，这几个方面最终促成了这个点位能够做到良性的循环。”\\n这一切在罗店镇城运中心相关负责人看来，意料之外，情理之中。\\n经营场所和客流有了保障后，经营奶茶和烧烤的“90后”摊主杨琦有了更长远打算的底气。\\n“现在有政府托底以后，我们就更有信心去做这件事情了，把它做好，让更多的顾客过来，然后把它当一个事业去干了，我们也想看看我们能不能像一个小的连锁一样，在网上招募一些志同道合的朋友一起把这个事情给做大。”\\n日前，上海发布《关于进一步优化设摊治理 提升城市“烟火气”工作方案》，引发人们对夜集市经济的关注。热气腾腾的夜间消费景象背后，映射出人们的生活需求。但一些曾经爆红的夜集市如今已风光不再，也值得反思。\\n罗店夜集市如何从网红做到长红？\\n“罗店的夜集市与安义夜巷、泗泾夜集市有什么不一样？”在上海师范大学副教授刘德艳看来，绝不能仅仅依赖“烟火气”，挖掘地域特色与文化，打造专属IP才是助推夜集市经济长红的“软实力”。\\n“我觉得，罗店夜集市上要有特别罗店，特别美兰湖的东西，比如说罗店的非遗、罗店当地的民俗、罗店当地的土特产品，把这些东西融合在一起，慢慢形成一个集聚效应，变成除了美食之外的一个综合IP。文化、娱乐、社交等元素都能够有的话，它就变成了罗店镇的一张名片，也就具备了非去不可的理由。”\\n往期推荐\\n原标题：《开市仅一个多月，北上海这个美食夜集市怎么就火了……》\\n特别声明\\n本文为澎湃号作者或机构在澎湃新闻上传并发布，仅代表该作者或机构观点，不代表澎湃新闻的观点或立场，澎湃新闻仅提供信息发布平台。申请澎湃号请用电脑访问https://renzheng.thepaper.cn。\\n\\n参考资料[5]:\\nTitle: 百科 \\nURL: https://baike.baidu.com/item/上海菜/672867 \\nContent:\\n进入词条全站搜索\\n进入词条全站搜索\\n播报编辑收藏赞\\n登录\\n# 上海菜\\n播报编辑上传视频\\n江南地区传统饮食文化的一种流派\\n许魏洲邀你吃“不要太灵的”上海本帮菜\\n03:35\\n上海传统美食大合集!生煎小笼本帮菜,根本吃不够!\\n17:59\\n吃了30多年红烧肉，只服这种特色做法，色泽红亮肥而不腻，真过瘾\\n10:08\\n上海菜十大名菜有哪些：让你领略上海独特美味！\\n01:49\\n去北京开的上海老饭店吃本帮菜，这几个菜就花了500多，烤菜吃不惯\\n03:41\\n外国丈母娘第一次来中国吃上海本帮菜，被震撼到，中国菜这么好吃\\n08:58\\n上海外滩吃顿本帮菜，不知道正宗不\\n00:18\\n老洋房里快40年的上海菜，很低调，要不是排队谁会以为是饭店\\n02:07\\n开在古镇里面的本帮菜老店\\n01:55\\n老弄堂里30多年上海菜，老板“大半个香港娱乐圈都来过”\\n01:46\\n上海弄堂里的本帮菜馆，店不大生意爆棚，没开门就得排队\\n05:38\\n这才是上海本帮菜的水平，红烧划水太惊艳，小店6张桌座无虚席\\n04:20\\n收藏\\n查看\\n585有用+1\\n102\\n沪菜，即上海菜，是其常用的烹调方法以红烧、煨、糖为主，以浓油赤酱、咸淡适中、保持原味、醇厚鲜美为其特色。 [4]\\n上海菜的别称为本帮菜，是江南地区传统饮食文化的一个重要流派。 自1843年上海开埠以来，随着工商业的发展，四方商贾云集，饭店酒楼应运而生。到20世纪三四十年代，有京、广、苏、扬、锡、甬、杭、闽、川、徽、潮、湘、以及上海本地菜等十六个帮别，同时还有素菜、清真菜，各式西菜、西点。 此时，上海菜原以红烧、生煸见长，后来吸取无锡、苏州、宁波等地方菜的特点，为适应上海人喜食清淡爽口的口味，菜肴渐由原来的重油赤酱趋向淡雅爽口，同时，兼及西菜、西点之法，使花色品种有了很大的发展，拥有鱼下巴甩水、腌川红烧圈子、白斩鸡、松江鲈鱼等名菜。 [4]\\n2014年，上海本帮菜肴传统烹饪技艺被列入国家级非物质文化遗产代表性项目名录。 [5]2018年9月，八宝鸭被评为“中国菜”之上海十大经典名菜。 [6]\\n## 相关星图\\n常见的中国菜系\\n共36个词条23.3万阅读中文名\\n上海菜别 名\\n沪菜、本帮菜 [4]产 地\\n上海 [4]名 菜\\n鱼下巴甩水、腌川红烧圈子、白斩鸡、松江鲈鱼 [4]烹调方法\\n以红烧、煨、糖为主 [4]特 点\\n浓油赤酱、咸淡适中、保持原味、醇厚鲜美 [4]概 念\\n食品，烹饪口 味\\n淡雅爽口 [4]\\n## 目录\\n1. 1烹饪特色\\n2. 2历史发展\\n3. 3经典名菜\\n4. 4著名食馆\\n5. ▪德兴菜馆1. ▪上海美食街\\n2. ▪王宝和酒家\\n3. 5名菜制作\\n4. ▪白斩鸡\\n5. ▪猪油百果松糕\\n6. ▪醉鸡1. ▪葡萄鱼\\n2. ▪河虾争妍\\n3. ▪蟹肉大排翅\\n4. ▪碧玉牛筋\\n5. ▪八宝鸭\\n6. ▪沪江排骨1. ▪鹅肝酱片\\n2. ▪清蒸大闸蟹\\n3. 6沪菜现状## 烹饪特色\\n播报\\n编辑\\n上海菜原以红烧、生煸见长。后来，吸取了无锡、苏州、宁波等地方菜的特点，参照上述十六帮别的烹调技术，兼及西菜、西点之法，使花色品种有了很大的发展。\\n菜肴风味的基本特点：汤卤醇厚，浓油赤酱，糖重色艳，咸淡适口。选料注重活、生、寸、鲜；调味擅长咸、甜、糟、酸。名菜如“红烧蛔鱼”，巧用火候，突出原味，色泽红亮，卤汁浓厚，肉质肥嫩，负有盛誉。\\n“糟钵头”则是上海本地菜善于在烹调中加“糟”的代表，把陈年香糟加工复制成糟卤，在烧制中加入，使菜肴糟香扑鼻，鲜味浓郁。\\n“生煸草头”，摘梗留叶，重油烹酒，柔软鲜嫩，蔚成一格。而各地方风味的菜肴也逐步适应上海的特点，发生了不同的变革，如川菜从重辣转向轻辣，锡菜从重甜改为轻甜，还有不少菜馆吸取外地菜之长。经过长期的实践，在取长补短的基础上。改革了烹调方法，上海菜达到了品种多样，别具一格，形成了上海菜的独特风味。\\n由于上海本地菜（包括苏锡菜）与外地菜长期共存，相互影响，便在原本地菜的基础上，逐渐发展成以上海和苏锡风味为主体并兼有各地风味的上海风味菜体系。\\n上海菜具有许多与众不同的特点：\\n首先讲究选料新鲜。它选用四季时令蔬菜，鱼是以江浙两省产品为主，取活为上，一年四季都有活鱼供客选择，当场活杀烹制。\\n第二菜肴品种多，四季有别。\\n第三讲究烹调方法并不断加以改进。上海菜原来以烧、蒸、煨、窝、炒并重，逐渐转为以烧、生煸、滑炒、蒸为主，其中以生煸、滑炒为最多特别善烹四季河鲜。\\n第四口味也有了很大变化。原来上海菜以浓汤、浓汁厚味为主，后来逐步变为卤汁适中，有清淡素雅，也有浓油赤酱，讲究鲜嫩、色调，鲜咸适口。特别是夏秋季节的糟味菜肴，香味浓郁，颇有特色。\\n如今，上海菜进一步具有选料新鲜、品质优良、刀工精细、制作考究、火候恰当、清淡素雅、咸鲜适中、口味多样、适应面广、风味独特等优点。其主要名菜有“青鱼下巴甩水”、“青鱼秃肺”、“腌川红烧圈子”、“生煸草头”、“白斩鸡”、“鸡骨酱”、“糟钵头”、“虾子大乌参”、“松江鲈鱼”、“枫泾丁蹄”等一二百种菜肴。\\n## 历史发展\\n播报\\n编辑\\n上海在唐代称为海滨渔村，北宋末期(一二九二年)改称上海商埠，此时始对饮食文化的发展有些认识。\\n三十年代，吸纳各地方菜肴风味，尤是苏、杭、浙的风味融合一体系而成为今日上海的主体风味。上海菜的烹饪特点：四季有别，以生煸、滑炒、蒸、煨、炖、红烧……见称，有诗句形容：\\n尖椒笃菜任君尝\\n百店千菜皆于杭\\n浓妆淡抹总相宜\\n自1843年上海开埠以来，随着工商业的发展，四方商贾云集，饭店酒楼应运而生。到本世纪三四十年代，各种地方菜馆林立，有京、广、苏、扬、锡、甬、杭、闽、川、徽、潮、湘、以及上海本地菜等十六个帮别，同时还有素菜、清真菜，各式西菜、西点。这些菜在上海各显神通，激烈竞争，又相互取长补短，融会贯通，这为博采众长，发展有独特风味的上海菜创造了有利条件。\\n## 经典名菜\\n播报\\n编辑\\n中国独特的“沪菜”代表名菜----白斩鸡 [2]、虾子大乌参、青鱼下巴甩水、松江钙鱼、鸡骨酱、桂花肉、八宝鸡、枫泾丁蹄、糟钵头、生煸草头、椒盐蹄胖、五味鸡腿、双包鸭片、卤糟猪脚、肉丝黄豆汤、四鲜白菜墩、蜜枣扒山药、口蘑锅巴汤、炒毛蟹、腌笃鲜 [1]等。\\n此外还有其他美食：上海老大房糕、五芳斋点心、鸽蛋圆子、素菜包、大壶春生煎馒头、蟹壳黄、南翔小笼馒头、四鲜烤麸、四季糕团、桂花糯米糖粥、开洋葱油面、五香豆、奶糖、梨膏糖、红烧生煸滑炒等。\\n## 著名食馆\\n播报\\n编辑\\n### 德兴菜馆\\n该菜馆经营上海菜肴，口味正宗，以汁浓色显、重油、汤鲜、味香著称，开业于1890年前后。德兴菜馆烹制的著名菜肴有虾子大乌参、鸡骨酱、鸡圈肉、八宝辣酱等。其中以虾子大乌参最有特色，是德兴菜馆的代表菜肴，极享盛誉。\\n### 上海美食街\\n该街地处黄浦区中心，位于延安东路，云南南路大世界娱乐中心旁，开设至今已有60余年的历史。上海美食街经营具有上海传统风味小吃、点心和全国各地风味小吃、著名菜肴，并提供优质服务。\\n### 王宝和酒家\\n该酒家是上海最早的酒家之一，创建于1744年。是以专营绍兴陈年黄酒而著称，供应的花雕、太雕、陈加饭、金波等优质黄酒香气浓郁，酒味醇厚。王宝和酒家的菜肴集各帮之长，尤以经营清水大河蟹闻名。该酒家经营的蟹筵，以特、新、优取胜，烹制的蟹菜风味独特，其中“芙蓉蟹粉”、“翡翠虾蟹”、“流黄蟹斗”、“阳澄蟹卷”尤为著名。\\n## 名菜制作\\n播报\\n编辑\\n### 白斩鸡\\n白斩鸡\\n白斩鸡是正牌嫡出的上海菜，上海人过年必定要吃白斩鸡，白斩鸡的烹饪方法是最简单不过的了，不过也考火候。两斤半上下光鸡一只，冷水淹过四分之三鸡身，加葱姜、料酒，大火煮沸，转小火十分钟，中间可翻一面鸡身，熄火后焖十分钟。这两个十分钟，即是要诀。这样焖出来的鸡肉极嫩，鸡腿骨中骨髓仍然鲜红，甚至带有少许血水，就像三分熟的上等牛排，是白斩鸡的最佳状态。 [2-3]\\n焖过十分钟后的鸡仍然很烫，小心捞出，在大量凉水中冲透，使鸡皮紧致，口感韧中带脆，如果任其在鸡汤中泡至冷却，则鸡皮熟烂，口感尽失。这时的鸡余温尚在，还是不能斩切，须谨记所有肉类家禽都不能在它还是热的时候就切，会散碎无法成型。必须俟它彻底冷透，表面略干，才可动刀斩件。白斩鸡蘸清酱油才是至味。 [2]\\n### 猪油百果松糕\\n猪油百果松糕\\n原 料：糯米、粳米、赤豆、莲子、蜜枣、核桃\\n做 法：将糯米、粳米掺合磨干粉，赤豆洗净煮过加白糖，制成干豆沙，另将莲心、核桃仁、蜜枣（会核）均切成小块，拌匀，加白糖和水为干粉吸收，然后在笼屉内侧刷一层芝麻油，把糕粉铺上，再铺于豆沙，最后将莲子、蜜枣、核桃块撒匀放在糕面上，笼屉放锅上，用大火沸水蒸熟，待呈紫色时，再放糖猪油丁、玫瑰花，至糕熟取出即成。 成品糯韧而香。\\n### 醉鸡\\n原 料：鸡腿2只、 绍兴酒2杯\\n调 料：盐1大匙 味精、胡椒粉各少许\\n醉鸡\\n做 法 ：1、鸡腿洗净，用调味料腌2小时，使之入味。\\n2、将鸡腿入蒸具中蒸15分钟，取出入冰水中漂凉（经过冷却，外皮较脆）。\\n3、将鸡腿沥干水分、剁块，加入绍兴酒浸泡一天，即可食用。\\n### 葡萄鱼\\n葡萄鱼\\n原 料：带皮青鱼肉350克,青菜叶4片.鸡蛋一个,咸面包屑75克,葡萄汁100克 做 法：鱼肉治净修成梯形，剞花刀至皮，放调料腌20分钟，粘一层蛋糊，在粘一层面包屑，下7成油锅中炸至金黄色，呈葡萄粒状时捞出装盘； 菜叶焯水修成葡萄叶状，贴在鱼肉旁，用白糖、白醋、盐、葡萄汁勾芡淋上即成。\\n### 河虾争妍\\n原 料：河虾100克，花雕酒\\n调 料：美极酱油、鲜柠檬、话梅、盐、味精、葱姜各适量\\n做 法：适用大小均匀的活鲜河虾剪须洗净，锅置灶上放入适量水，加花雕酒、盐、味精、话梅、葱、姜、美极酱油、鲜柠檬，放入河虾煮熟，冷却后去头壳摆放成形即可。\\n特点：色泽明快，壳脆柔嫩，有酒香味。\\n### 蟹肉大排翅\\n原 料：水发排翅80克，花蟹钳4只，银芽10克，上汤50克，湿生粉10克\\n蟹肉大排翅\\n调 料：大红浙醋50克，盐、味精、鸡粉、葱油、香菜叶少许\\n做 法：1、花蟹钳上笼蒸后，去壳备用；\\n2、水发大排翅上笼蒸10分钟后拿出，加入上汤50克和调味后勾芡，淋上少许葱油；\\n3、盆中摆上蟹钳肉，把芡汁浇在鱼翅上，大红浙醋、香菜、银芽一并跟上即可。\\n特 点：软糯腴润，汤汁鲜美。\\n### 碧玉牛筋\\n原 料：牛筋250克，胡萝卜150克，长白萝卜250克\\n调 料：荷兰芥末、葱、姜、花生酱、味精、生抽、XO酱各适量\\n做 法：新鲜牛筋洗净，加葱、姜、料酒、胡萝卜、长白萝卜煮酥；然后再另起锅放入煮酥的牛筋加调味料、花生酱、生抽、XO酱、荷兰芥末同煮，冷却后切片装盆即可。\\n特点：配色鲜明、协调，味香，有韧性。\\n### 八宝鸭\\n原 料：肥鸭，笋丁，肉丁，火腿丁，栗子丁，鸡肫丁，冬菇丁，莲子，虾米，糯米饭 调 料：绍酒，酱油，白糖，味精，虾仁，湿淀粉，熟青豆，猪油。\\n八宝鸭\\n做 法： 将肥壮嫩鸭宰杀治净，劈开背脊，剪去鸭脚，入沸水锅焯水后捞出洗净，沥干，在鸭身上抹上酱油；将笋丁，肉丁，火腿丁，栗子丁，鸡肫丁，冬菇丁，莲子，虾米，糯米饭放入碗内，加绍酒，酱油，白糖，味精，拌和成馅放入鸭肚内，背朝上放入盛器，上笼蒸三四小时，至鸭肉酥烂时取出翻扣在盘中；炒锅烧热，下猪油，将虾仁滑熟取出，锅内留油少许，放笋片，冬菇片，加酱油少许，蒸鸭原汁适量，烧沸后放虾仁和熟青豆，下湿淀粉少许勾芡，淋上猪油，出锅浇在鸭身上即成。\\n特 点：成菜色泽红润，形状完整，鸭肉酥烂，腴香浓溢，汁浓味鲜。\\n### 沪江排骨\\n原 料：肋排300克\\n调料：生粉，奶粉，吉士粉，食粉，鸡蛋\\n调 料：盐，味精，糖，精制油，黄酒，蒜泥，葱花，干辣椒末。 制 作：将肋排剁成拇指大小方块，漂净血水。把鸡蛋、食粉、水放入排骨里打上劲，然后再放奶粉、士粉、生粉打匀。将油锅烧至五成热左右，倒入排骨，炸至金黄色，倒入笊篱沥干油分，最后放入糖、盐、黄酒、蒜泥、葱花、干辣椒末，翻炒出锅，装盆。 特 点：外脆里嫩，香辣微甜，美味可口。\\n### 鹅肝酱片\\n原 料：鹅肝500克\\n鹅肝酱片\\n调 料：食油，黄油，葱，姜，生抽，味精，胡椒粉，糖，黄酒。 做 法：将鹅肝洗净，再焯水：取一炒锅，锅上火下油，用葱、姜煸炒，再加入上上述调料，放入鹅肝翻炒至熟盛起，等冷却待用。炒好的鹅肝用粉碎机搅成茸，再取模型，两面擦黄油，放入鹅肝茸置冰箱冷藏3～4小时至凝固，改刀装盘即可。 特 点：香糯可口，回味悠长，中西结合，营养丰富。\\n### 清蒸大闸蟹\\n原 料：螃蟹1000克\\n调 料：黄酒15克、姜末30克酱油20克、白糖、味精各少许、麻油 15克、香醋50克\\n清蒸大闸蟹\\n做 法：1.将螃蟹用清水流净，放在盛器里；\\n2.将姜末放在小酒碗内，加熬熟的酱油、白糖、味精、黄酒、麻油搅和；\\n3.另取一小碗，放醋待用；\\n4.将螃蟹上笼用火蒸15-20分钟，至蟹壳呈鲜红色，蟹肉成熟时取出。上桌时随带油调味和醋。\\n特 点：肉嫩鲜美。\\n## 沪菜现状\\n播报\\n编辑\\n所谓的上海菜，内容洋洋大观，举凡大菜、点心、小吃和零食等无所不包，花样又多，常常把外地人搞得一头雾水，不知道自己到底在吃啥。这是因为上海菜其实是杭州、宁波、徽州、扬州、苏州和无锡菜的综合体，四方交集，五味杂陈，早已失去了它的纯粹性的缘故。此外，上海那华洋杂处的环境，使它的西餐水准在中国独占鳌头，已成为上海菜的一部分。几乎每个到上海的人都得到“红房子”瞻仰一番，尝尝它的海派法式西菜。他们的奶油烤蛤蜊、虾二杯、牛尾汤脍炙人口，仍保持着原有的水准。还有“德大西菜社”的德国菜和“天鹅阁面包房”的法式小圆面包，在上海都算是头一份儿。\\n分享你的世界查看更多\\n上海人吃“辣”，鲜过云贵川！\\n这个拥有强大包容性的菜系不断紧跟城市融合的步伐，一如老一辈上海人的款款深情，永远向着那些“雷声大雨点小”，却对家常鲜味大有作为的“上海辣椒”。\\n地道风物地道风物官方账号\\n上海，到底有多“油腻”？\\n都说江南清丽，上海精致，而这些令人垂涎的吃食，竟大多油宽色重、缱绻甜腻，总结起来就是两个字——油、腻。 上海本帮菜呈现出与这里的语言一样鲜明的特点：浓油赤酱，不甜不欢。\\n那一座城\\n分量小？吃不饱？你可能吃了一顿假的上海菜\\n吃惯了生长地的味道，要打开另一个地方的菜系不容易，除了有冒险意愿的味蕾，还和餐馆、点菜、吃饭的人密切相关。\\n地道风物地道风物官方账号\\n参考资料\\n* 1．本地宝．2016-08-31 [引用日期2017-07-7]\\n* 2．经济参考报 [引用日期2025-02-03]\\n* 3．解放日报 [引用日期2025-02-03]\\n* 4．华夏经纬网 [引用日期2025-04-01]\\n* 5．看看新闻 [引用日期2025-04-01]\\n* 6．手机广西网 [引用日期2025-04-01]上海菜的概述图（4张）\\n分享你的世界查看更多\\n上海，到底有多“油腻”？\\n都说江南清丽，上海精致，而这些令人垂涎的吃食，竟大多油宽色重、缱绻甜腻，总结起来就是两个字——油、腻。 上海本帮菜呈现出与这里的语言一样鲜明的特点：浓油赤酱，不甜不欢。\\n修童彤0J7在这里，发现城市的骄傲。\\n分量小？吃不饱？你可能吃了一顿假的上海菜\\n吃惯了生长地的味道，要打开另一个地方的菜系不容易，除了有冒险意愿的味蕾，还和餐馆、点菜、吃饭的人密切相关。\\n地道风物地道风物官方账号\\n上海人吃“辣”，鲜过云贵川！\\n这个拥有强大包容性的菜系不断紧跟城市融合的步伐，一如老一辈上海人的款款深情，永远向着那些“雷声大雨点小”，却对家常鲜味大有作为的“上海辣椒”。\\n地道风物地道风物官方账号\\n上海，到底有多“油腻”？\\n都说江南清丽，上海精致，而这些令人垂涎的吃食，竟大多油宽色重、缱绻甜腻，总结起来就是两个字——油、腻。 上海本帮菜呈现出与这里的语言一样鲜明的特点：浓油赤酱，不甜不欢。\\n修童彤0J7在这里，发现城市的骄傲。\\n分量小？吃不饱？你可能吃了一顿假的上海菜\\n吃惯了生长地的味道，要打开另一个地方的菜系不容易，除了有冒险意愿的味蕾，还和餐馆、点菜、吃饭的人密切相关。\\n地道风物地道风物官方账号\\n词条统计\\n浏览次数：1584553次\\n编辑次数：99次\\n最近更新：（2天前）\\n突出贡献榜\\n上海菜\\n选择朗读音色\\n成熟女声\\n成熟男声\\n磁性男声\\n年轻女声\\n情感男声\\n2x\\n1.5x\\n1.25x\\n1x\\n0.75x\\n0.5x\\n分享到微信朋友圈\\n打开微信“扫一扫”即可将网页分享至朋友圈\\n新手上路\\n我有疑问\\n投诉建议\\n©2025 Baidu | | | |\\n\\n参考资料[6]:\\nTitle: 上海十大必吃美食 \\nURL: https://baijiahao.baidu.com/s?id=1828711351827613727&wfr=spider&for=pc \\nContent:\\nicon_voice_on icon_voice\\n上海十大必吃美食\\n2025-04-07 11:04广东\\n导读\\n•AI导读带你速览精华\\n从非遗南翔小笼的0.1毫米面皮到老正兴油爆虾15秒的舌尖杂技，百年老字号在铜招牌里镌刻城市密码。清晨粢饭团裹着四白三黑的乡愁，深夜油爆虾映着霓虹，每一口都吞吐着中西交融的烟火气。张爱玲笔下的葱油香仍萦绕弄堂，创新芝士拉丝年糕已排起长队，这座城总能在酥脆与软糯间找到味觉的黄金分割。\\n内容由AI智能生成\\n有用\\n上海，这座中西交融的国际都市，每一道美食都是一部鲜活的城市史。从清晨的粢饭团到深夜的油爆虾，十大必吃美味串联起百年风情，让舌尖在时光中穿梭。\\n一、南翔小笼包：舌尖上的非遗传奇\\n作为国家级非遗技艺的承载者，南翔小笼包的历史可追溯至 1871 年。第三代传人李建钢将面皮擀至 0.1 毫米薄度，包入 30 克夹心腿肉与肉皮冻，经 20 分钟蒸制，形成 \"皮薄如纸、汁多如泉\" 的奇观。咬开半透明的外皮，25℃的蟹粉汤汁瞬间在口腔迸发，搭配姜丝香醋，鲜得人眉梢颤动。古猗园分店每日卖出 2 万只，游客为尝现蒸美味甘愿排队 2 小时，连《舌尖上的中国》也用 4 分钟镜头记录这道 \"会跳舞的小笼\"。\\n二、生煎馒头：老城厢的黄金脆响\\n1920 年，实业家黄楚九在萝春阁茶馆创制生煎，从此 \"混水生煎\" 与 \"清水生煎\" 两大流派争奇斗艳。大壶春的半发酵面皮蓬松如棉，25 克剂子包出 12 道褶子，煎至金黄的 \"金脊背\" 酥脆作响，咬开后汤汁如涌泉。小杨生煎则革新为全发酵皮，搭配藤椒大虾等创新馅料，年销量突破 1 亿只。老食客深谙 \"先开窗、后喝汤\" 的门道，就着咖喱牛肉汤，感受 \"底脆、汤鲜、肉嫩\" 的三重奏。\\n三、蟹粉豆腐：金秋限定的味觉盛宴\\n每年 9-11 月，阳澄湖大闸蟹与苏北豆腐碰撞出极致鲜美。老正兴的蟹粉豆腐坚持 \"三现\" 原则：现拆蟹粉、现磨豆浆、现点卤水。将 3 两蟹粉与 2 斤嫩豆腐同煮，撒上白胡椒粉提鲜，蟹黄的橘红与豆腐的雪白交织成 \"金沙埋玉\"。张爱玲在《谈吃》中写道：\"蟹粉豆腐要趁热吃，连汤汁拌饭能吃三碗\"，如今这道菜仍是米其林餐厅的招牌，2023 年入选 \"亚洲 50 大美食\"。\\n四、红烧肉：浓油赤酱的上海灵魂\\n上海红烧肉的奥秘藏在 \"三肥两瘦\" 的肋条肉里。德兴馆采用 \"四步烹调法\"：焯水去腥、冰糖炒色、绍酒焖烧、收汁勾芡，经 4 小时慢炖，肉皮呈琥珀色，入口即化却不失弹性。李伯荣大师改良的 \"毛氏红烧肉\" 加入湘式辣椒，成为国宴佳肴。老克勒们偏爱 \"红烧肉配草头\"，甜糯的肉香与草头的清香在舌尖交响，这道菜年销量超百万份，是上海人年夜饭的 \"镇桌之宝\"。\\n五、排骨年糕：市井烟火的黄金组合\\n1947 年，\"鲜得来\" 首创排骨年糕，将猪大排拍松腌制，与年糕同炸至金黄，浇上甜辣酱汁。排骨外脆里嫩，年糕软糯弹牙，1997 年获 \"中华名小吃\" 称号。如今 \"小常州\" 排骨年糕推出 \"芝士拉丝\" 版本，年轻人排队 2 小时只为尝新。老食客则钟情 \"排骨年糕汤\"，用炸制余油熬出浓白高汤，撒上葱花，堪称 \"上海版味噌汤\"。\\n六、葱油拌面：老弄堂的极简主义\\n看似简单的葱油拌面，实则考验 \"熬油三时辰\" 的功夫。王家沙选用崇明金瓜丝与嘉定小葱，以 2:1 的猪油与菜籽油熬制，葱香渗入每根面条。张爱玲在《公寓生活记趣》中写道：\"葱油的香气是上海的味道\"，如今这道平民美食登上米其林餐盘，改良版加入松露、蟹粉，价格从 5 元飙升至 88 元，却依然供不应求。\\n七、糟钵斗：老上海的味觉密码\\n邵万生的糟钵斗堪称 \"冷菜之王\"，用五年陈糟卤浸泡鸡、虾、毛豆等食材。其糟黄泥螺采用宁波黄泥螺，经 \"三腌三晒\" 工艺，螺肉如水晶般透亮，咸鲜中带着米糟的回甘。夏季冰镇后食用，酒香沁脾，杜月笙曾用糟钵斗宴请黄金荣，赞其 \"比绍兴醉蟹更有腔调\"。2023 年推出的 \"糟香冰淇淋\"，将传统风味与现代甜品结合，成为网红爆款。\\n八、鲜肉月饼：中秋限定的酥皮暴击\\n杏花楼的鲜肉月饼采用 \"水油皮包酥\" 工艺，20 层酥皮包裹 3:7 肥瘦相间的猪肉馅，经 230℃高温烘烤，形成 \"金圈银底\" 的完美品相。每日卖出 15 万只，排队者从福州路延伸至外滩。沈大成的 \"蟹粉鲜肉月饼\" 创新加入 10% 蟹黄，售价 38 元 / 只仍被抢购一空。老食客发明 \"冰火两重天\" 吃法：刚出炉的月饼蘸镇江香醋，酥脆与酸爽在口腔爆炸。\\n九、油爆虾：本帮菜的火与速度\\n老正兴的油爆虾选用 20 只 / 斤的河虾，经 \"一炸二炒三收汁\"，虾壳红亮如玛瑙，虾肉弹牙带甜。李伯荣大师改良的 \"双味油爆虾\"，一半保持原味，一半加入芥末酱，成为 APEC 峰会指定菜品。这道菜对火候要求苛刻，280℃油温下，虾身须在 15 秒内完成变色，堪称 \"舌尖上的杂技\"。\\n十、粢饭团：清晨的能量炸弹\\n老上海的粢饭团讲究 \"四白三黑\"：白糯米、白芝麻、白糖、白油条，搭配黑芝麻、黑洋酥、黑米。阿婆粢饭团坚持用木桶蒸饭，油条现炸现包，裹上肉松、咸蛋黄等馅料，重达 300 克。清晨 6 点，长乐路的摊前已排起长队，上班族捧着 \"巨无霸\" 粢饭团边走边吃，这道传承百年的早餐，年销量突破 500 万只，成为上海人的 \"续命神器\"。\\n寻味地图\\n老字号集聚地：南京东路（沈大成、泰康食品）、福州路（老正兴、杏花楼）、城隍庙（南翔馒头店）\\n夜市必去：黄河路美食街（佳家汤包）、云南南路（鲜得来排骨年糕）\\n创新体验：大董餐厅（分子料理版油爆虾）、南翔小笼文化展（非遗技艺体验）\\n隐藏菜单：老正兴的 \"三丁鲜肉月饼\"、德兴馆的 \"糟香扣肉\"\\n上海的美食密码，藏在百年老字号的铜招牌里，融在浓油赤酱的镬气中，更流淌在弄堂口阿婆的吴侬软语间。从米其林餐厅到街边小摊，每一口都在讲述这座城市的包容与创新，值得用整个旅程去细细品味。\\n作者声明：作品含AI生成内容举报/反馈\\n4.1万获赞 2673粉丝\\n读书、游学、运动，期待和大家每天进步一点\\n## 作者最新文章\\n2小时前4阅读\\n3小时前3阅读\\n7小时前12阅读\\n## 相关推荐\\n换一换\\n* 1热\\n* 2新\\n* 3\\n* 4\\n* 5热\\n* 6热\\n* 7\\n* 8\\n* 9新\\n* 10热分享\\n微信好友\\n新浪微博\\n复制链接\\n扫码分享至微信\\n© Baidu 京ICP证030173号\\n\\n参考资料[7]:\\nTitle: 上海美食地图:十大必吃经典,从弄堂小吃到本帮盛宴! \\nURL: https://baijiahao.baidu.com/s?id=1834297223163493130&wfr=spider&for=pc \\nContent:\\nicon_voice_on icon_voice\\n上海美食地图：十大必吃经典，从弄堂小吃到本帮盛宴！\\n2025-06-08 07:30挪威\\n导读\\n•AI导读带你速览精华\\n上海美食地图：从皮薄馅大的南翔小笼到焦脆底部的生煎包，从浓油赤酱的红烧肉到夏日清爽的糟货，十大必吃经典带你尝遍魔都的浓醇本帮味与市井烟火气。\\n内容由AI智能生成\\n有用\\n上海美食地图：十大必吃经典，从弄堂小吃到本帮盛宴！\\n上海作为中国最具国际化的都市之一，美食融合了本帮菜的浓油赤酱、江南风味的精致以及各地乃至全球的风味。以下是到上海必尝的十大经典美食，既有传统特色，也有市井烟火气。#图文打卡计划#\\n1. 小笼包（南翔小笼）\\n- 特色：皮薄馅大，汤汁饱满，以城隍庙的南翔馒头店最负盛名。\\n- 吃法：先咬破皮吸汤汁，再蘸姜丝醋食用。\\n2. 生煎馒头（生煎包）\\n- 推荐：大壶春（全发酵面皮，酥脆底）和小杨生煎（汤汁多）。\\n- 灵魂：焦脆的底部搭配鲜肉或虾仁馅，趁热吃最佳。\\n3. 葱油拌面\\n- 精髓：熬香的葱油+酱油糖汁，简单却滋味浓郁，老字号\"老地方面馆\"或街头小店都可尝试。\\n4. 红烧肉（本帮特色）\\n- 经典做法：浓油赤酱，肥而不腻，常配卤蛋或百叶结，推荐\"老吉士酒家\"。\\n5. 粢饭团\\n- 市井早餐：糯米包裹油条、肉松、榨菜，咸甜皆宜，南阳路粢饭团是网红款。\\n6. 白斩鸡（小绍兴鸡粥）\\n- 特色：皮黄肉嫩，蘸特制酱油，配鸡粥更地道，\"小绍兴\"是老字号代表。\\n7. 鲜肉月饼\\n- 季节限定：中秋前后最火，酥皮包裹鲜肉馅，光明邨大酒家常年排队。\\n8. 糟货（糟钵头）\\n- 夏日风味：用酒糟卤制的毛豆、门腔（猪舌）、虾等，清凉爽口，老字号\"老人和\"值得一试。\\n9. 油爆虾\\n- 本帮甜口：虾壳酥脆，酱汁甜中带咸，推荐\"上海老饭店\"。\\n10. 咸豆浆+大饼油条\\n- 老上海早餐：咸豆浆加紫菜、虾皮、油条，搭配酥脆大饼，体验本地烟火气。\\n其他推荐：\\n- 蟹粉类：秋季必吃蟹粉捞饭、蟹粉豆腐（如\"成隆行蟹王府\"）。\\n- 草头圈子：酒香草头配肥糯猪大肠，本帮菜经典组合。\\n- 国际范：外滩高端餐厅或法租界Brunch，感受中西融合。\\nTips：\\n- 地道味道往往藏在老字号或弄堂小店，比如\"兰心餐厅\"\"海金滋\"。\\n- 甜口预警：本帮菜偏甜，北方游客可搭配茶水解腻。\\n总结：\\n上海是一座美食天堂，既有浓油赤酱的本帮经典，也有市井烟火气的街头小吃。从汤汁丰盈的小笼包、焦香酥脆的生煎，到甜咸交织的红烧肉、清爽糟货，再到早餐必吃的粢饭团和咸豆浆，每一道都承载着上海独特的风味记忆。本文精选十大必吃美食，带你尝遍老字号与网红店，体验舌尖上的魔都魅力！上海的美食版图既有百年传承，也有创新融合，从路边摊到米其林，总有一种味道能打动你！\\n举报/反馈\\n44.1万获赞 5.3万粉丝\\n用心做美食 用心分享交流\\n美食领域创作者\\n## 作者最新文章\\n17天前52阅读\\n18天前73阅读\\n19天前56阅读\\n## 相关推荐\\n换一换\\n* 1热\\n* 2新\\n* 3\\n* 4\\n* 5热\\n* 6热\\n* 7\\n* 8\\n* 9新\\n* 10热分享\\n微信好友\\n新浪微博\\n复制链接\\n扫码分享至微信\\n© Baidu 京ICP证030173号\\n\\n参考资料[8]:\\nTitle: 上海美食 | 醉庐:环球港里的顶流本帮餐厅 \\nURL: https://new.qq.com/rain/a/20250620A04RMZ00 \\nContent:\\n正在浏览：上海美食 | 醉庐：环球港里的顶流本帮餐厅\\n魏无心\\n搜索\\n客户端\\n无障碍\\n快捷访问\\n+关注\\n评论\\n6\\n复制链接\\n微信好友\\n用微信扫描二维码\\nQQ好友\\n用QQ扫描二维码\\n手机看\\n微信扫一扫，随时随地看\\n# 上海美食 | 醉庐：环球港里的顶流本帮餐厅\\n2025-06-20 12:47发布于上海时尚领域创作者\\n+关注\\n醉庐应该是环球港顶流本帮餐厅了，每次有外地朋友过来上海玩，找我约饭，我都选这里，每次大家都吃得很满意。\\n环境是摩登现代感的老上海风情，服务也很不错。\\n收藏打卡有送冰淇淋或布丁，我们这次总共全家5个人，老人小孩没有APP，3个成人操作了，但还是有送5份哦。\\n而且还加送了花束一样美的杨枝甘露，大家都超开心。\\n点了个双人套餐，实际上足够三个成人吃。另外再加点几样家人爱吃的，人均才70左右，性价比很高。\\n套餐里的青柠海蜇金瓜丝，清新爽脆，很开胃。\\n剁椒鲽鱼可以让店家做成不辣的，小朋友很爱吃，鱼肉嫩嫩的。\\n香酥蹄髈是我超爱的一道，本来我是不爱吃猪蹄这种软软腻腻口感的食物，但是烤蹄髈的外皮超酥脆，一点不腻啦，非常香。配的薯条，又是小朋友最爱。\\n石锅豆腐和蒜香秋葵不用多说了，水准之上。\\n荠菜黄鱼春卷，里面的黄鱼肉很细嫩，也不腥，老爸很喜欢。\\n甜品是红豆沙小圆子，红豆沙很浓稠，小圆子也不少，撒上桂花香香甜甜的。\\n另外加点了小朋友爱吃的炖蛋，还有一道白汤肠肺。肠肺都处理得很干净，加上白胡椒粉，汤味鲜浓。\\n【总结一下】\\n这里是改良的本帮菜，做得比较精致，又不会过于形式化。\\n不论是朋友约饭，还是家人小聚都挺合适的，请客不掉价。\\n套餐的性价比非常高，味道也都没有踩雷。总之，很值得推荐。\\n--------------------------------\\n醉庐\\n地址：上海市中山北路环球港B1层\\n免责声明：本内容来自腾讯平台创作者，不代表腾讯新闻或腾讯网的观点和立场。\\n举报\\n评论 0文明上网理性发言，请遵守《新闻评论服务协议》\\n请先登录后发表评论~\\n加载中...\\n| | | | | | | | | |\\nCopyright © 1998 - 2025 Tencent. All Rights Reserved\\n刷新\\n反馈\\n* 提示当前您处于未登录状态，未登录状态下腾讯广告无法为您在PC网站上提供个性化广告推荐服务，请登录后进行广告设置。广告设置更多\\n顶部\\n\\n\\n\\n请严格遵守以下规则：\\n1. 回答必须结合问题需求和当前时间，对参考资料的可用性进行判断，避免在回答中使用错误或过时的信息。\\n2. 当参考资料中的信息无法准确地回答问题时，你需要在回答中提供获取相应信息的建议，或承认无法提供相应信息。\\n3. 你需要优先根据百度高权威信息、百科、官网、权威机构、专业网站等高权威性来源的信息来回答问题，\\n   但务必不要用“（来源：xx）”这类格式给出来源，\\n   不要暴露来源网站中的“_百度高权威信息”，\\n   也不要出现\\'根据参考资料\\'，\\'根据当前时间\\'等表述。\\n4. 更多地使用参考文章中的相关数字、案例、法律条文、公式等信息，让你的答案更专业。\\n5. 只要使用了参考资料中的任何内容，必须在句末或段末加上资料编号，如 \"[1]\" 或 \"[2][4]\"。不要遗漏编号，也不要随意编造编号。编号必须来源于参考资料中已有的标注。\\n---------\\n下面请结合以上信息，回答问题，补全对话:\\n## 对话\\nuser:\\n你好\\nassistant:\\n你好，我是你的智能搜索助手，请问有什么我可以帮助你的？\\n\\n问题：上海美食攻略\\n\\n直接输出回复内容即可。\\n'}]\n"]}], "source": ["ANSWER_PROMPT = textwrap.dedent(\n", "    \"\"\"\\\n", "    下面你会收到多段参考资料和一个问题。你的任务是阅读参考资料，并根据参考资料中的信息回答对话中的问题。\n", "    以下是当前时间和参考资料：\n", "    ---------\n", "## Current time\n", "    {date}\n", "\n", "## References\n", "    {search_result}\n", "\n", "    请严格遵守以下规则：\n", "    1. 回答必须结合问题需求和当前时间，对参考资料的可用性进行判断，避免在回答中使用错误或过时的信息。\n", "    2. 当参考资料中的信息无法准确地回答问题时，你需要在回答中提供获取相应信息的建议，或承认无法提供相应信息。\n", "    3. 你需要优先根据百度高权威信息、百科、官网、权威机构、专业网站等高权威性来源的信息来回答问题，\n", "       但务必不要用“（来源：xx）”这类格式给出来源，\n", "       不要暴露来源网站中的“_百度高权威信息”，\n", "       也不要出现'根据参考资料'，'根据当前时间'等表述。\n", "    4. 更多地使用参考文章中的相关数字、案例、法律条文、公式等信息，让你的答案更专业。\n", "    5. 只要使用了参考资料中的任何内容，必须在句末或段末加上资料编号，如 \"[1]\" 或 \"[2][4]\"。不要遗漏编号，也不要随意编造编号。编号必须来源于参考资料中已有的标注。\n", "    ---------\n", "    下面请结合以上信息，回答问题，补全对话:\n", "## Conversation\n", "    {context}\n", "    问题：{query}\n", "\n", "    直接输出回复内容即可。\n", "    \"\"\"\n", ")\n", "\n", "query = ANSWER_PROMPT.format(\n", "    date=datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    search_result=complete_search_result,\n", "    context=history,\n", "    query=query\n", ")\n", "web_search_messages = [{\"role\": \"user\", \"content\": query}]\n", "print(web_search_messages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2. Non-Streaming Request\n", "#### 5.2.1. Request Model\n", "When sending a request to the API, the following main parameters need to be considered:\n", "- `messages` (must): List of conversation messages\n", "- `max_tokens` (optional): configuration parameter for maximum number of generated tokens\n", "- `temperature` (optional): configuration parameter for controlling randomness in generated results\n", "- `top_p` (optional): configuration parameter for nucleus sampling"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'chatcmpl-ebe32a15-d82a-4153-8a36-888b36f440eb', 'choices': [{'finish_reason': 'stop', 'index': 0, 'logprobs': None, 'message': {'content': '上海作为国际大都市，美食种类繁多，融合了本帮菜的浓油赤酱、江南风味的精致以及各地乃至全球的风味。以下是一份详尽的上海美食攻略：\\n\\n### 一、经典本帮菜\\n\\n1. **小笼包（南翔小笼）**\\n\\n   * 特色：皮薄馅大，汤汁饱满，以城隍庙的南翔馒头店最负盛名。\\n   * 吃法：先咬破皮吸汤汁，再蘸姜丝醋食用。[6][7]\\n\\n2. **生煎馒头（生煎包）**\\n\\n   * 推荐：大壶春（全发酵面皮，酥脆底）和小杨生煎（汤汁多）。\\n   * 灵魂：焦脆的底部搭配鲜肉或虾仁馅，趁热吃最佳。[6][7]\\n\\n3. **葱油拌面**\\n\\n   * 精髓：熬香的葱油+酱油糖汁，简单却滋味浓郁。\\n   * 推荐：老字号“老地方面馆”或街头小店。[6][7]\\n\\n4. **红烧肉（本帮特色）**\\n\\n   * 经典做法：浓油赤酱，肥而不腻，常配卤蛋或百叶结。\\n   * 推荐：“老吉士酒家”。[6][7]\\n\\n5. **粢饭团**\\n\\n   * 市井早餐：糯米包裹油条、肉松、榨菜，咸甜皆宜。\\n   * 推荐：南阳路粢饭团。 [6][7]\\n\\n6. **白斩鸡（小绍兴鸡粥）**\\n\\n   * 特色：皮黄肉嫩，蘸特制酱油，配鸡粥更地道。\\n   * 推荐：“小绍兴”。[6][7]\\n\\n7. **油爆虾**\\n\\n   * 特色：本帮甜口，虾壳酥脆，酱汁甜中带咸。\\n   * 推荐：“上海老饭店”。[6][7]\\n\\n### 二、特色小吃与夜市\\n\\n1. **鲜肉月饼**\\n\\n   * 季节限定：中秋前后最火，酥皮包裹鲜肉馅。\\n   * 推荐：光明邨大酒家。[6][7]\\n\\n2. **糟货（糟钵头）**\\n\\n   * 夏日风味：用酒糟卤制的毛豆、门腔（猪舌）、虾等，清凉爽口。\\n   * 推荐：老字号“老人和”。[6][7]\\n\\n3. **咸豆浆+大饼油条**\\n\\n   * 老上海早餐：咸豆浆加紫菜、虾皮、油条，搭配酥脆大饼，体验本地烟火气。[6][7]\\n\\n4. **夜市推荐**：宝山区罗店镇7号线罗南新村地铁站外的流动摊贩集中规范运营（临时）便民点，汇聚了全国各地的特色小吃，成了人们体验城市烟火气的一处网红夜集市。[1][4]\\n\\n### 三、其他美食推荐\\n\\n1. **醉庐**：环球港顶流本帮餐厅，环境是摩登现代感的老上海风情，服务也很不错，套餐性价比很高。[8]\\n2. **蟹粉类**：秋季必吃蟹粉捞饭、蟹粉豆腐，推荐“成隆行蟹王府”。[7]\\n3. **草头圈子**：酒香草头配肥糯猪大肠，本帮菜经典组合。[7]\\n\\n上海美食既有浓油赤酱的本帮经典，也有市井烟火气的街头小吃，从路边摊到米其林餐厅，总有一种味道能打动你。</s></s>', 'refusal': None, 'role': 'assistant', 'annotations': None, 'audio': None, 'function_call': None, 'tool_calls': None, 'reasoning_content': None}}], 'created': 1750764713, 'model': 'default', 'object': 'chat.completion', 'service_tier': None, 'system_fingerprint': None, 'usage': {'completion_tokens': 821, 'prompt_tokens': 13932, 'total_tokens': 14753, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}}\n"]}], "source": ["client = OpenAI(base_url=host_url, api_key=model_api_key)\n", "response = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=web_search_messages,\n", "    temperature=1.0,\n", "    max_tokens=2048,\n", "    top_p=0.7\n", ")\n", "response = response.model_dump()\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5.2.2. Model Output\n", "- `content`: Final answer"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["上海作为国际大都市，美食种类繁多，融合了本帮菜的浓油赤酱、江南风味的精致以及各地乃至全球的风味。以下是一份详尽的上海美食攻略：\n", "\n", "### 一、经典本帮菜\n", "\n", "1. **小笼包（南翔小笼）**\n", "\n", "   * 特色：皮薄馅大，汤汁饱满，以城隍庙的南翔馒头店最负盛名。\n", "   * 吃法：先咬破皮吸汤汁，再蘸姜丝醋食用。[6][7]\n", "\n", "2. **生煎馒头（生煎包）**\n", "\n", "   * 推荐：大壶春（全发酵面皮，酥脆底）和小杨生煎（汤汁多）。\n", "   * 灵魂：焦脆的底部搭配鲜肉或虾仁馅，趁热吃最佳。[6][7]\n", "\n", "3. **葱油拌面**\n", "\n", "   * 精髓：熬香的葱油+酱油糖汁，简单却滋味浓郁。\n", "   * 推荐：老字号“老地方面馆”或街头小店。[6][7]\n", "\n", "4. **红烧肉（本帮特色）**\n", "\n", "   * 经典做法：浓油赤酱，肥而不腻，常配卤蛋或百叶结。\n", "   * 推荐：“老吉士酒家”。[6][7]\n", "\n", "5. **粢饭团**\n", "\n", "   * 市井早餐：糯米包裹油条、肉松、榨菜，咸甜皆宜。\n", "   * 推荐：南阳路粢饭团。 [6][7]\n", "\n", "6. **白斩鸡（小绍兴鸡粥）**\n", "\n", "   * 特色：皮黄肉嫩，蘸特制酱油，配鸡粥更地道。\n", "   * 推荐：“小绍兴”。[6][7]\n", "\n", "7. **油爆虾**\n", "\n", "   * 特色：本帮甜口，虾壳酥脆，酱汁甜中带咸。\n", "   * 推荐：“上海老饭店”。[6][7]\n", "\n", "### 二、特色小吃与夜市\n", "\n", "1. **鲜肉月饼**\n", "\n", "   * 季节限定：中秋前后最火，酥皮包裹鲜肉馅。\n", "   * 推荐：光明邨大酒家。[6][7]\n", "\n", "2. **糟货（糟钵头）**\n", "\n", "   * 夏日风味：用酒糟卤制的毛豆、门腔（猪舌）、虾等，清凉爽口。\n", "   * 推荐：老字号“老人和”。[6][7]\n", "\n", "3. **咸豆浆+大饼油条**\n", "\n", "   * 老上海早餐：咸豆浆加紫菜、虾皮、油条，搭配酥脆大饼，体验本地烟火气。[6][7]\n", "\n", "4. **夜市推荐**：宝山区罗店镇7号线罗南新村地铁站外的流动摊贩集中规范运营（临时）便民点，汇聚了全国各地的特色小吃，成了人们体验城市烟火气的一处网红夜集市。[1][4]\n", "\n", "### 三、其他美食推荐\n", "\n", "1. **醉庐**：环球港顶流本帮餐厅，环境是摩登现代感的老上海风情，服务也很不错，套餐性价比很高。[8]\n", "2. **蟹粉类**：秋季必吃蟹粉捞饭、蟹粉豆腐，推荐“成隆行蟹王府”。[7]\n", "3. **草头圈子**：酒香草头配肥糯猪大肠，本帮菜经典组合。[7]\n", "\n", "上海美食既有浓油赤酱的本帮经典，也有市井烟火气的街头小吃，从路边摊到米其林餐厅，总有一种味道能打动你。</s></s>\n"]}], "source": ["content = response[\"choices\"][0][\"message\"][\"content\"]\n", "print(content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3. Streaming Request\n", "#### 5.3.1. Request Model\n", "When sending a request to the API, the following main parameters need to be considered:\n", "- `messages` (must): List of conversation messages\n", "- `max_tokens` (optional): configuration parameter for maximum number of generated tokens\n", "- `temperature` (optional): configuration parameter for controlling randomness in generated results\n", "- `top_p` (optional): configuration parameter for nucleus sampling\n", "- `stream` (optional): configuration parameter for enabling/disabling streamed return"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'id': 'chatcmpl-e8f5e3dd-0a30-4788-8a84-efb10cbd696d', 'choices': [{'delta': {'content': '', 'function_call': None, 'refusal': None, 'role': 'assistant', 'tool_calls': None, 'reasoning_content': ''}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1750764749, 'model': 'default', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}, {'id': 'chatcmpl-e8f5e3dd-0a30-4788-8a84-efb10cbd696d', 'choices': [{'delta': {'content': '上海', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None, 'token_ids': [4827], 'reasoning_content': None}, 'finish_reason': None, 'index': 0, 'logprobs': None, 'arrival_time': 0.8883872032165527}], 'created': 1750764749, 'model': 'default', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}, {'id': 'chatcmpl-e8f5e3dd-0a30-4788-8a84-efb10cbd696d', 'choices': [{'delta': {'content': '作为', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None, 'token_ids': [2610], 'reasoning_content': None}, 'finish_reason': None, 'index': 0, 'logprobs': None, 'arrival_time': 0.9165420532226562}], 'created': 1750764749, 'model': 'default', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}, {'id': 'chatcmpl-e8f5e3dd-0a30-4788-8a84-efb10cbd696d', 'choices': [{'delta': {'content': '国际化', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None, 'token_ids': [40766], 'reasoning_content': None}, 'finish_reason': None, 'index': 0, 'logprobs': None, 'arrival_time': 0.9406707286834717}], 'created': 1750764749, 'model': 'default', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}, {'id': 'chatcmpl-e8f5e3dd-0a30-4788-8a84-efb10cbd696d', 'choices': [{'delta': {'content': '大', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None, 'token_ids': [94025], 'reasoning_content': None}, 'finish_reason': None, 'index': 0, 'logprobs': None, 'arrival_time': 0.96445631980896}], 'created': 1750764749, 'model': 'default', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}]\n"]}], "source": ["client = OpenAI(base_url=host_url, api_key=model_api_key)\n", "response = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=web_search_messages,\n", "    temperature=1.0,\n", "    max_tokens=2048,\n", "    top_p=0.7,\n", "    stream=True\n", ")\n", "response_stream = []\n", "for chunk in response:\n", "    if not chunk.choices:\n", "        continue\n", "    response_stream.append(chunk.model_dump())\n", "\n", "print(response_stream[:5])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5.3.2. Model Output\n", "The model's output will be delivered via streaming return.\n", "- `content`: Final answer"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["上海作为国际化大都市，美食种类繁多，融合了本帮菜的浓油赤酱、江南风味的精致以及各地乃至全球的风味。以下是一份详尽的上海美食攻略：\n", "\n", "### 一、经典本帮菜推荐\n", "\n", "1. **小笼包（南翔小笼）**\n", "\n", "   * 特色：皮薄馅大，汤汁饱满，以城隍庙的南翔馒头店最负盛名。\n", "   * 吃法：先咬破皮吸汤汁，再蘸姜丝醋食用。[6][7]\n", "\n", "2. **生煎馒头（生煎包）**\n", "\n", "   * 推荐：大壶春（全发酵面皮，酥脆底）和小杨生煎（汤汁多）。\n", "   * 灵魂：焦脆的底部搭配鲜肉或虾仁馅，趁热吃最佳。[6][7]\n", "\n", "3. **葱油拌面**\n", "\n", "   * 精髓：熬香的葱油+酱油糖汁，简单却滋味浓郁。\n", "   * 推荐：老字号“老地方面馆”或街头小店。[6][7]\n", "\n", "4. **红烧肉（本帮特色）**\n", "\n", "   * 经典做法：浓油赤酱，肥而不腻，常配卤蛋或百叶结。\n", "   * 推荐：“老吉士酒家”。[6][7]\n", "\n", "5. **白斩鸡**\n", "\n", "   * 特色：皮黄肉嫩，蘸特制酱油，配鸡粥更地道。\n", "   * 推荐：“小绍兴”是老字号代表。[7]\n", "\n", "6. **油爆虾**\n", "\n", "   * 特色：本帮甜口，虾壳酥脆，酱汁甜中带咸。\n", "   * 推荐：“上海老饭店”。[7]\n", "\n", "### 二、市井小吃推荐\n", "\n", "1. **粢饭团**\n", "\n", "   * 市井早餐：糯米包裹油条、肉松、榨菜，咸甜皆宜。\n", "   * 推荐：南阳路粢饭团是网红款。[7]\n", "\n", "2. **鲜肉月饼**\n", "\n", "   * 季节限定：中秋前后最火，酥皮包裹鲜肉馅。\n", "   * 推荐：光明邨大酒家常年排队。[7]\n", "\n", "3. **糟货（糟钵头）**\n", "\n", "   * 夏日风味：用酒糟卤制的毛豆、门腔（猪舌）、虾等，清凉爽口。\n", "   * 推荐：老字号“老人和”。[7]\n", "\n", "4. **咸豆浆+大饼油条**\n", "\n", "   * 老上海早餐：咸豆浆加紫菜、虾皮、油条，搭配酥脆大饼，体验本地烟火气。[7]\n", "\n", "### 三、其他特色美食推荐\n", "\n", "1. **蟹粉豆腐**\n", "\n", "   * 特色：金秋限定的味觉盛宴，蟹粉与豆腐的完美结合。\n", "   * 推荐：老正兴的蟹粉豆腐坚持“三现”原则，现拆蟹粉、现磨豆浆、现点卤水。[6]\n", "\n", "2. **排骨年糕**\n", "\n", "   * 特色：市井烟火的黄金组合，猪大排与年糕的完美搭配。\n", "   * 推荐：“鲜得来”首创排骨年糕。[6]\n", "\n", "3. **醉庐（环球港店）**\n", "\n", "   * 特色：摩登现代感的老上海风情，改良的本帮菜，精致而不失风味。\n", "   * 推荐理由：环境优雅，服务周到，性价比高，适合朋友约饭或家人小聚。[8]\n", "\n", "上海的美食版图既有百年传承，也有创新融合，从路边摊到米其林，总有一种味道能打动你，希望这份攻略能帮助你找到心仪的美食。</s>\n"]}], "source": ["content_stream = \"\"\n", "for res in response_stream:\n", "    content_stream += res[\"choices\"][0][\"delta\"][\"content\"]\n", "print(content_stream)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}
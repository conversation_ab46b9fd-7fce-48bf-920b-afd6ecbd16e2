﻿# 全球要闻|美股涨跌互现，英伟达财报或决定市场走向

**发布日期**: 2024年11月19日

**原文链接**: https://stock.10jqka.com.cn/usstock/20241119/c663644721.shtml

## 📄 原文内容

脠芦脟貌脪陋脦脜|脙脌鹿脡脮脟碌酶禄楼脧脰拢卢脫垄脦掳麓茂虏脝卤篓禄貌戮枚露篓脢脨鲁隆脳脽脧貌
						                    脥卢禄篓脣鲁Knews
11脭脗18脠脮拢卢禄霉脳录碌脛10脛锚脝脷脙脌脮庐脢脮脪忙脗脢脦陋4.42%拢卢露脭脙脌脕陋麓垄脮镁虏脽脌没脗脢脳卯脙么赂脨碌脛2脛锚脝脷脙脌脮庐脢脮脪忙脗脢脦陋4.29%隆拢
脠脠脙脜脙脌鹿脡脰脨拢卢脝禄鹿没脮脟1.34%拢卢脫垄脦掳麓茂碌酶1.29%拢卢脦垄脠铆脮脟0.18%拢卢鹿脠赂猫C脮脟1.67%拢卢鹿脠赂猫A脮脟1.63%拢卢脩脟脗铆脩路碌酶0.45%拢卢Meta脮脟0.06%拢卢脤脴脣鹿脌颅脮脟5.62%拢卢脤篓禄媒碌莽脮脟0.77%拢卢鲁卢脦垄掳毛碌录脤氓脮脟2.99%拢卢脫垄脤脴露没脮脟2.01%隆拢
11脭脗18脠脮拢卢赂么脪鹿潞茫脡煤驴脝录录脰赂脢媒脝脷禄玫脡脧脮脟0.81%拢卢脛脡脣鹿麓茂驴脣脰脨鹿煤陆冒脕煤脰赂脢媒脡脧脮脟1.71%拢卢赂禄脢卤脰脨鹿煤A50脰赂脢媒脡脧脮脟0.34%隆拢
脠脠脙脜脰脨赂脜鹿脡路陆脙忙拢卢脤脷脩露驴脴鹿脡(赂脹鹿脡)脮脟0.80%拢卢掳垄脌茂掳脥掳脥脮脟0.88%拢卢脝麓露脿露脿脮脟2.90%拢卢脥酶脪脳脮脟3.24%拢卢脨炉鲁脤脮脟3.08%拢卢掳脵露脠脮脟0.99%拢卢脌铆脧毛脝没鲁碌脮脟1.14%拢卢脨隆脜么脝没鲁碌脮脟2.20%拢卢脦碌脌麓脮脟3.90%隆拢
脙脌鹿煤脣戮路篓虏驴陆芦脥脝露炉鹿脠赂猫鲁枚脢脹Chrome拢卢脪脭麓貌脝脝脗垄露脧
赂脽脢垄拢潞脭陇录脝脙脌脕陋麓垄陆芦脫脷2025脛锚脪禄录戮露脠脥拢脰鹿脣玫卤铆隆拢
赂脽脢垄拢潞脭陇录脝脙脌脕陋麓垄陆芦脫脷2025脛锚脪禄录戮露脠脥拢脰鹿脣玫卤铆隆拢
脫垄脦掳麓茂脮媒掳茂脰煤鹿脠赂猫脡猫录脝脕驴脳脫录脝脣茫麓娄脌铆脝梅
脫垄脦掳麓茂脮媒掳茂脰煤鹿脠赂猫脡猫录脝脕驴脳脫录脝脣茫麓娄脌铆脝梅隆拢
脙脌脪酶脰陇脠炉卤铆脢戮脫垄脦掳麓茂虏脝卤篓禄貌戮枚露篓鹿脡脢脨路麓碌炉鲁脡掳脺
陆脳脭戮脨脟鲁陆脰脟脛脺脰煤脢脰隆掳脭戮脦脢隆卤 脰脟脛脺脢脫戮玫脣脩脣梅鹿娄脛脺陆脫脠毛iPhone 16
鹿煤脛脷脥路虏驴麓贸脛拢脨脥麓麓脪碌鹿芦脣戮陆脳脭戮脨脟鲁陆脝矛脧脗虏煤脝路隆掳脭戮脦脢隆卤拢卢脪脩戮颅陆芦脝盲脰脟脛脺脢脫戮玫脣脩脣梅鹿娄脛脺隆掳脜脛脮脮脦脢隆卤陆脫脠毛脕脣 iPhone 16 脧脿禄煤驴脴脰脝掳麓脜楼隆拢脰搂鲁脰脫脙禄搂脪禄录眉碌梅脫脙拢卢脥篓鹿媒脜脛脪禄脮脜脮脮脝卢脢鹿脫脙脰脟脛脺脦脢麓冒脣脩脣梅隆拢鲁媒 iPhone 16 脥芒拢卢脝盲脣没禄煤脨脥碌脛脝禄鹿没脢脰禄煤脫脙禄搂脰禄脪陋陆芦脧碌脥鲁脡媒录露碌陆 iOS 18拢卢脪虏驴脡脪脭麓脫驴脴脰脝脰脨脨脛隆垄脣酶脝脕碌脠脠毛驴脷驴矛脣脵脢鹿脫脙隆掳脜脛脮脮脦脢隆卤隆拢戮脻脕脣陆芒拢卢脭戮脦脢隆掳脜脛脮脮脦脢隆卤卤鲁潞贸脢脟陆脳脭戮脨脟鲁陆脳脭脩脨碌脛 Step-1.5V 露脿脛拢脤卢脌铆陆芒麓贸脛拢脨脥隆拢(驴脝麓麓掳氓脠脮卤篓)
脫垄脦掳麓茂脨脗脪禄麓煤AI脨戮脝卢卤禄脝脴鹿媒脠脠驴脰脩脫鲁脵陆禄赂露拢卢鹿芦脣戮禄脴脫娄鲁脝隆掳驴脥禄搂禄鹿脭脷脟脌隆卤
脫毛脡脧脝没脤脰脗脹FSD脢脷脠篓拢驴脤脴脣鹿脌颅脰脨鹿煤拢潞脙禄脫脨赂煤脠脦潞脦脝贸脪碌戮脥麓脣陆禄脕梅鹿媒
脪脩脫毛脡脧脝没脤脰脗脹脕陆脗脰FSD脢脷脠篓拢驴脤脴脣鹿脌颅脰脨鹿煤拢潞脧没脧垄虏禄脢碌
脤脴脌脢脝脮禄貌脩掳脟贸路脜驴铆脳脭露炉录脻脢禄脝没鲁碌脧脼脰脝 脗铆脣鹿驴脣脮媒虏录戮脰隆掳脥锚脠芦脦脼脠脣录脻脢禄隆卤
(function(){var obj = null;if(document.getElementById('arctTailMark')) {obj = document.getElementById('arctTailMark');}if(obj!=null){var str = '<a href="http://www.10jqka.com.cn" target="_blank" id="back_web" style="font-weight:bold" rel="nofollow" class="backweb"><img src="http://i.thsi.cn/images/article/logo.jpg" style="height:15px">  路碌禄脴脢脳脪鲁</a><a href="https://t.10jqka.com.cn/m/game/getActivityPageById/?id=295" target="_blank" style="font-weight: bold; font-size: 14px; padding-left: 5px; margin-left: 5px; border-left: 2px solid">戮脵卤篓 &gt;</a>';var showed = 0;try{var oo = obj.previousSibling;while(oo.nodeType!=1 || oo.innerHTML.replace(/\s|\u3000/g,'').length==0){oo = oo.previousSibling;}if(oo.nodeType==1 && oo.tagName.toLowerCase()=='p') {var span = document.createElement('span');span.innerHTML =str;span.style.width = '130px';oo.appendChild(span);showed = 1;}}catch (e){}if(showed == 0){obj.innerHTML =str;}}})();
var hotstocks = new Array();

	        	        hotstocks['000716'] = '000716';

	    	    	        hotstocks['002456'] = '002456';

	    	    	        hotstocks['601216'] = '601216';

	    	    	        hotstocks['603005'] = '603005';

	    	    	        hotstocks['600206'] = '600206';

	    	    	        hotstocks['000795'] = '000795';

	    	    	        hotstocks['000564'] = '000564';

	    	    	        hotstocks['002510'] = '002510';
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄脗貌脠毛脟掳10鹿脡
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄陆酶脡垄禄搂脤脫脟掳10鹿脡
露脌录脪脳脢陆冒拢潞陆帽脠脮脰梅脕娄脗貌陆酶脟掳10鹿脡
CPO赂脜脛卯脮冒碌麓禄脴脡媒 脨脗脪脳脢垄脮脟鲁卢8%
脧没路脩碌莽脳脫赂脜脛卯鹿脡脮冒碌麓脳脽碌脥 脜路路脝鹿芒碌脠露脿鹿脡碌酶鲁卢5%
A鹿脡脠媒麓贸碌莽脨脜脭脣脫陋脡脤录炉脤氓脳脽脟驴 脰脨鹿煤碌莽脨脜脮脟鲁卢5%
脡脧潞拢脢脨脮镁赂庐鲁拢脦帽禄谩脪茅虏驴脢冒拢卢脰搂鲁脰脡脧脢脨鹿芦脣戮虏垄鹿潞脰脴脳茅拢卢脤谩脡媒鹿芦脣戮脰脢脕驴脜脿脫媒脕煤脥路脝贸脪碌
脰脨脨脜脰陇脠炉脨脗脠脦脳脺戮颅脌铆 脢脳麓脦鹿芦驴陋路垄脡霉
GPU露脌陆脟脢脼脛娄露没脧脽鲁脤陆芦鲁氓麓脤IPO拢隆虏脦鹿脡隆垄潞脧脳梅碌脠赂脜脛卯鹿脡脙没碌楼脪禄脌脌
脭酶脴鹿脠潞拢潞脠么脤脴脌脢脝脮隆掳驴陋脗脤碌脝隆卤拢卢脛镁碌脗脢卤麓煤陆芦驴录脗脟赂掳脙脌陆篓鲁搂
赂脮赂脮拢隆脩脟脤芦鹿脡脢脨拢卢脠芦脧脽脤酶脣庐拢隆A50脰卤脧脽路颅脗脤拢卢赂脹鹿脡脡卯碌梅拢隆路垄脡煤脕脣脢虏脙麓
脦脢陆莽M7脢脗鹿脢脨脗陆酶脮鹿拢潞鲁碌脰梅鲁脝隆掳鹿媒脫脷脧脿脨脜脫脿鲁脨露芦虏脜鹿潞脗貌隆卤虏垄脝冒脣脽禄陋脦陋
脌毛隆掳虏禄脗么戮脥陆没隆卤脰禄脢拢脕陆赂枚露脿脭脗拢隆脤脴脌脢脝脮脥脜露脫拢潞陆芦露脪脧脰鲁脨脜碌 脮眉戮脠TikTok
脡卯陆禄脣霉拢潞11脭脗16脠脮陆芦陆酶脨脨脡卯脢脨陆禄脪脳脧碌脥鲁虏芒脢脭
		var s = "_" + Math.random().toString(36).slice(2);
		document.write('<div id="' + s + '"></div>');
		(window.slotbydup=window.slotbydup || []).push({
			display: 'inlay-fix'
document.addEventListener('DOMContentLoaded', function () {
      var userid = 'nouser';
        // 忙拢聙忙聼楼 Vue 氓聮聦 AdvertCommon 忙聵炉氓聬娄氓颅聵氓聹篓
        if (Vue && window['mobileweb_AdvertCommon@1.0.4'].default) {
          Vue.use(window['mobileweb_AdvertCommon@1.0.4'].default);
          // 氓掳聺猫炉聲盲禄聨 cookie 盲赂颅忙聫聬氓聫聳 userid
          var match = document.cookie.match(/userid=([^;]*)/);
          if (match) {
            userid = match[1] || 'nouser';
      } catch (error) {
        console.error(error);
      // 氓聢聺氓搂聥氓聦聳 Vue 氓庐聻盲戮聥
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
脡忙脦麓鲁脡脛锚脠脣脦楼鹿忙脛脷脠脻戮脵卤篓
虏禄脕录脨脜脧垄戮脵卤篓碌莽禄掳拢潞(0571)88933003
戮脵卤篓脫脢脧盲拢潞*****************
脮茫陆颅脥卢禄篓脣鲁禄楼脕陋脨脜脧垄录录脢玫脫脨脧脼鹿芦脣戮掳忙脠篓脣霉脫脨
var newsid = '663644721';
    var id='c_663644721';
	var initctime = '1731970808';
	var stitle = encodeURIComponent('脠芦脟貌脪陋脦脜|脙脌鹿脡脮脟碌酶禄楼脧脰拢卢脫垄脦掳麓茂虏脝卤篓禄貌戮枚露篓脢脨鲁隆脳脽脧貌');
	var artStock = new Array();
	var cotitle = '脠芦脟貌脪陋脦脜|脙脌鹿脡脮脟碌酶禄楼脧脰拢卢脫垄脦掳麓茂虏脝卤篓禄貌戮枚露篓脢脨鲁隆脳脽脧貌';
	var codata = cocontent.replace(/<[^>]*>/g, '').substr(0,250);
	var courl = 'http://stock.10jqka.com.cn/usstock/20241119/c663644721.shtml';
        newRobotRecList : [],
		cid: ''  //脳卯潞贸脪禄脤玫脝脌脗脹碌脛ID
	ARTINFO.tday = $.timeFormat(ARTINFO.tday, 'yyyyMMddHHmm');
	ARTINFO.tday = parseInt(ARTINFO.tday);
	ARTINFO.userid = WEB.basic.getUserid();
$(document).ready(function(){
	    	    TA.log({id:id, ld:'browser',fid:'info_gather,zx_n_all,zx_wzb_total,zx_ll_684,zxusstock,ch_stock', pt:2,_sid:'imgzx'});
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?78c58f01938e4d85eaf619eae71b4ed1";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?f79b64788a4e377c608617fba4c736e2";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
// 麓娄脌铆脥录脝卢src脦陋undefined
  var imageDomList = document.querySelectorAll('img');
  if (imageDomList.length > 0) {
      imageDomList.forEach(ele => {
          var imgSrc = ele.src;
          if (imgSrc.endsWith('/undefined')) {
              ele.remove();
  var srcVideoDom = document.querySelector('input.video-insert');
    var videoUrl = srcVideoDom.getAttribute('data-src');
    var videoThumb = srcVideoDom.getAttribute('data-thumb');
    var videoDom = document.createElement('video');
    var parent = srcVideoDom.parentNode;
    videoDom.src = videoUrl;
    videoDom.poster = videoThumb;
    videoDom.setAttribute("controls", true);
    videoDom.setAttribute("style", "width:627px;height:353px");
    parent.insertBefore(videoDom, srcVideoDom);
    srcVideoDom.remove();
  $('.self-stock-con').css({'height':'206px'})
// 脧脼脰脝碌脛禄煤脝梅脠脣脳茅录镁掳脳脙没碌楼
    const LIMIT_ROBOT_VISUAL_COMS = [
        'thsf2e_common-long-hu-rank',
        'thsf2e_ai-table'
    const checkLimitCom = (dataType) => {
        return LIMIT_ROBOT_VISUAL_COMS.includes(dataType);
    // 脕煤禄垄掳帽脤脴脢芒麓娄脌铆
    const LONGHU_TYPE = 'thsf2e_common';
    // 禄帽脠隆dom陆脷碌茫脨脜脧垄
    const getDomAttrData = (robotParse) => {
        const dataType = robotParse.getAttribute('data-type');
        const dataProps = robotParse.getAttribute('data-props');
        const dataComurl = robotParse.getAttribute('data-comurl');
        const dataStyle = robotParse.getAttribute('data-style');
        const dataVersion = robotParse.getAttribute('data-version');
            dataType,
            dataProps,
            dataComurl,
            dataStyle,
            dataVersion,
    const loadScript = (url, callback) => {
        const originalDefine = window.define;
        window.define = undefined;
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.charset = 'utf-8';
        script.src = url;
        script.onload = function() {
            callback(null)
        script.onerror = function() {
            callback(`Failed to load script: ${url}`);
        document.body.appendChild(script);
    // 脜脨露脧window脌茂脢脟路帽脫脨脮芒赂枚脳茅录镁拢卢脠莽鹿没脫脨戮脥脰卤陆脫脢鹿脫脙拢卢脠莽鹿没脙禄脫脨戮脥录脫脭脴
    const getComponent = (id, url, callback) => {
        return new Promise((resolve, reject) => {
            if (window[id]) {
                return resolve(window[id].default)
            loadScript(url, (err) => {
                if (err || !window[id]) {
                    reject(err || '脳茅录镁id麓铆脦贸')
                } else {
                    resolve(window[id].default)
    // 陆芒脦枚禄煤脝梅脠脣脛脷脠脻
    const parseRobot = (ele) => {
        const { dataType, dataProps, dataComurl, dataStyle, dataVersion } = getDomAttrData(ele);
        const props = JSON.parse(decodeURIComponent(dataProps));
        const style = JSON.parse(decodeURIComponent(dataStyle));
        // 脜脨露脧脢脟路帽虏禄脭脷limit盲脰脠戮脰脨
        if (!checkLimitCom(dataType)) {
        const id = `${dataType}_index@${dataVersion}`;
        getComponent(id, dataComurl).then((com) => {
            const placeholder = document.createElement('div');
            placeholder.className = 'robot-visual-container';
            ele.replaceWith(placeholder);
            const comInstance = new Vue({
                el: placeholder,
                render: h => h('div', { attr: { id: 'robotVisual' }, style }, [
                    h(com, { props })
            }).$mount();
        }).catch(err => {
            console.error(err)
    const parseContentMain = () => {
        const eles = document.getElementById('contentApp').querySelectorAll('robot-parse');
        eles.forEach(ele => {
            parseRobot(ele)
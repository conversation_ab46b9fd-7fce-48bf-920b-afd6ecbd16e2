#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境测试脚本
检查所有依赖包是否正确安装
"""

import sys
import os

def test_imports():
    """测试所有必要的包是否可以正常导入"""
    print("正在测试依赖包...")
    
    try:
        import flask
        print("✓ flask 导入成功")
    except ImportError as e:
        print(f"✗ flask 导入失败: {e}")
        return False

    try:
        import flask_cors
        print("✓ flask_cors 导入成功")
    except ImportError as e:
        print(f"✗ flask_cors 导入失败: {e}")
        return False
    
    try:
        import crawl4ai
        print("✓ crawl4ai 导入成功")
    except ImportError as e:
        print(f"✗ crawl4ai 导入失败: {e}")
        return False
    
    try:
        import requests
        print("✓ requests 导入成功")
    except ImportError as e:
        print(f"✗ requests 导入失败: {e}")
        return False
    
    try:
        import json
        print("✓ json 导入成功")
    except ImportError as e:
        print(f"✗ json 导入失败: {e}")
        return False
    
    try:
        import asyncio
        print("✓ asyncio 导入成功")
    except ImportError as e:
        print(f"✗ asyncio 导入失败: {e}")
        return False
    
    return True

def test_modules():
    """测试自定义模块是否可以正常导入"""
    print("\n正在测试自定义模块...")
    
    # 添加父目录到路径
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    
    try:
        from cookbook.crawl_utils import CrawlUtils
        print("✓ CrawlUtils 导入成功")
    except ImportError as e:
        print(f"✗ CrawlUtils 导入失败: {e}")
        return False
    
    try:
        from baidu_search_utils import BaiduSearchUtils
        print("✓ BaiduSearchUtils 导入成功")
    except ImportError as e:
        print(f"✗ BaiduSearchUtils 导入失败: {e}")
        return False
    
    try:
        from deepseek_client import DeepSeekClient
        print("✓ DeepSeekClient 导入成功")
    except ImportError as e:
        print(f"✗ DeepSeekClient 导入失败: {e}")
        return False
    
    return True

def test_api_connection():
    """测试API连接"""
    print("\n正在测试API连接...")
    
    try:
        from deepseek_client import DeepSeekClient
        client = DeepSeekClient()
        
        # 简单的测试消息
        test_messages = [{"role": "user", "content": "你好"}]
        
        # 这里只是测试客户端初始化，不实际发送请求
        print("✓ DeepSeek客户端初始化成功")
        return True
        
    except Exception as e:
        print(f"✗ API连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("DeepSeek Web搜索聊天机器人 - 环境测试")
    print("=" * 50)
    
    # 测试基础包导入
    if not test_imports():
        print("\n❌ 基础依赖包测试失败")
        return False
    
    # 测试自定义模块
    if not test_modules():
        print("\n❌ 自定义模块测试失败")
        return False
    
    # 测试API连接
    if not test_api_connection():
        print("\n❌ API连接测试失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！环境配置正确。")
    print("现在可以运行 'python run.py' 启动应用。")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

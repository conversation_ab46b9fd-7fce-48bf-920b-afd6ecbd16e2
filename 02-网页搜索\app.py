from flask import Flask, request, jsonify, Response, send_from_directory, make_response, session
from flask_cors import CORS
import json
import threading
import queue
import time
import requests
from crawler import WebCrawler
import os
import uuid
import datetime
from database import init_database, create_user, authenticate_user, get_user_by_id, consume_credits, recharge_vip_with_card, get_all_cards, get_card_usage_records, archive_used_cards, delete_vip_card, get_card_stats, create_vip_cards, batch_delete_unused_cards
from auth import create_session, get_user_from_session, delete_session, require_auth, check_credits, calculate_token_cost, count_input_tokens, count_output_tokens

app = Flask(__name__)
CORS(app)

# 初始化数据库
init_database()

# DeepSeek API 配置
DEEPSEEK_API_URL = "http://*************:8080/v1/chat/completions"
DEEPSEEK_API_KEY = "sjyun-7d2a330b15178863788bea4806a3f65a"
DEEPSEEK_MODEL = "DeepSeek-R1-Distill-Qwen-32B"

# 全局变量存储搜索状态
search_status = {}

# 管理员账号配置
ADMIN_ACCOUNTS = {
    'admin': 'admin123',  # 用户名: 密码
    'root': 'root123'
}

def is_admin(username):
    """检查是否为管理员"""
    return username in ADMIN_ACCOUNTS

# 删除重复的函数定义，使用auth.py中的版本

def require_admin(f):
    """管理员权限装饰器"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 先检查用户认证
        from auth import get_user_from_session
        session_token = request.headers.get('Authorization')
        if session_token and session_token.startswith('Bearer '):
            session_token = session_token[7:]
        else:
            session_token = request.cookies.get('session_id')

        user = get_user_from_session(session_token)
        if not user:
            return jsonify({'error': '需要登录'}), 401

        if not is_admin(user['username']):
            return jsonify({'error': '需要管理员权限'}), 403

        # 将用户信息添加到request中
        request.current_user = user
        return f(*args, **kwargs)
    return decorated_function

def call_deepseek_api(message_or_messages, max_retries=1, stream=False, system_prompt=None):
    """调用DeepSeek API进行对话，支持重试和流式输出，支持单消息或消息列表"""
    for attempt in range(max_retries + 1):
        try:
            print(f"尝试调用DeepSeek API (第{attempt + 1}次)...")
            print(f"API URL: {DEEPSEEK_API_URL}")
            print(f"API Key: {DEEPSEEK_API_KEY[:10]}...")

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
            }

            # 处理消息格式
            if isinstance(message_or_messages, list):
                # 如果是消息列表，直接使用
                messages = message_or_messages.copy()
                # 如果有系统提示词，添加到开头
                if system_prompt:
                    messages.insert(0, {"role": "system", "content": system_prompt})
            else:
                # 如果是单个消息，转换为消息列表格式
                messages = []
                if system_prompt:
                    messages.append({"role": "system", "content": system_prompt})
                messages.append({"role": "user", "content": message_or_messages})

            data = {
                "model": DEEPSEEK_MODEL,
                "messages": messages,
                "max_tokens": 8192,  # 增加到8192
                "temperature": 0.7,
                "stream": stream  # 支持流式输出
            }

            print(f"请求头: {headers}")
            print(f"请求数据: {data}")

            # 先尝试较短的超时时间
            response = requests.post(
                DEEPSEEK_API_URL,
                headers=headers,
                json=data,
                timeout=15,  # 15秒超时，基于实际测试响应时间约3秒
                verify=False,  # 忽略SSL证书验证
                stream=stream  # 启用流式响应
            )

            print(f"API响应状态码: {response.status_code}")
            print(f"API响应头: {response.headers}")

            if response.status_code == 200:
                if stream:
                    # 流式响应处理
                    return response
                else:
                    # 非流式响应处理
                    try:
                        result = response.json()
                        print(f"API响应内容: {result}")

                        if 'choices' in result and len(result['choices']) > 0:
                            answer = result['choices'][0]['message']['content']
                            print(f"提取的回答: {answer}")
                            return answer
                        else:
                            print("API响应中没有有效的choices")
                            return "抱歉，AI模型没有返回有效回答。"
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {e}")
                        print(f"原始响应: {response.text}")
                        return "抱歉，AI服务返回了无效的响应格式。"
            else:
                print(f"DeepSeek API错误: {response.status_code}")
                print(f"错误响应: {response.text}")
                if attempt < max_retries:
                    print(f"将在3秒后重试...")
                    time.sleep(3)
                    continue
                else:
                    return f"抱歉，AI服务返回错误。状态码: {response.status_code}"

        except requests.exceptions.Timeout:
            print(f"API调用超时 (第{attempt + 1}次) - 15秒超时")
            if attempt < max_retries:
                print(f"将在3秒后重试...")
                time.sleep(3)
                continue
            else:
                return "抱歉，AI服务响应超时，请检查网络连接或稍后重试。"
        except requests.exceptions.ConnectionError as e:
            print(f"连接错误 (第{attempt + 1}次): {e}")
            if attempt < max_retries:
                print(f"将在3秒后重试...")
                time.sleep(3)
                continue
            else:
                return "抱歉，无法连接到AI服务，请检查网络连接。"
        except Exception as e:
            print(f"调用DeepSeek API时发生未知错误 (第{attempt + 1}次): {str(e)}")
            if attempt < max_retries:
                print(f"将在3秒后重试...")
                time.sleep(3)
                continue
            else:
                return f"抱歉，AI服务出现未知错误: {str(e)}"

    return "抱歉，多次尝试后仍无法获取AI回答，请稍后重试。"

@app.route('/')
def index():
    """提供主页"""
    return send_from_directory('.', 'douban_sidebar_layout.html')

@app.route('/admin')
def admin_panel():
    """管理员面板"""
    return send_from_directory('.', 'admin.html')

@app.route('/test-admin')
def test_admin():
    """管理员功能测试页面"""
    return send_from_directory('.', 'test_admin_simple.html')

# 删除重复的管理员API路由，使用下面统一的版本

@app.route('/douban')
def douban_layout():
    """提供豆瓣布局页面"""
    return send_from_directory('.', 'douban_sidebar_layout.html')

@app.route('/simple')
def simple_index():
    """提供简洁版主页"""
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """提供静态文件"""
    return send_from_directory('.', filename)

@app.route('/api/search', methods=['POST'])
@require_auth
def search():
    """问答搜索API端点"""
    data = request.get_json()
    question = data.get('question', '')
    max_pages = data.get('max_pages', 3)

    if not question:
        return jsonify({'error': '问题不能为空'}), 400

    # 获取当前用户（已通过@require_auth验证）
    user = request.current_user

    # 检查积分（搜索功能消耗50积分）
    search_cost = 50

    # VIP用户有无限积分
    if user['is_vip'] and user['vip_expire_date']:
        from datetime import datetime
        vip_expire = datetime.fromisoformat(user['vip_expire_date'])
        if vip_expire >= datetime.now():
            print(f"[SEARCH] VIP用户 {user['username']} 使用搜索功能")
        else:
            # VIP已过期，检查积分
            if user['credits'] < search_cost:
                return jsonify({'error': f'积分不足，搜索需要{search_cost}积分，当前积分：{user["credits"]}'}), 402
    else:
        # 普通用户检查积分
        if user['credits'] < search_cost:
            return jsonify({'error': f'积分不足，搜索需要{search_cost}积分，当前积分：{user["credits"]}'}), 402
    
    # 创建消息队列
    message_queue = queue.Queue()
    search_id = f"search_{int(time.time())}"
    
    # 启动搜索线程
    crawler = WebCrawler(message_queue, user_id=user['id'])
    search_thread = threading.Thread(
        target=crawler.start_search,
        args=(question, max_pages)
    )
    search_thread.daemon = True
    search_thread.start()
    
    def generate():
        """生成服务器发送事件流"""
        search_completed = False
        try:
            while True:
                try:
                    # 从队列获取消息，超时5秒
                    message = message_queue.get(timeout=5)

                    if message is None:  # 搜索完成信号
                        search_completed = True
                        # 发送最终完成信号
                        yield f"data: {json.dumps({'type': 'search_complete', 'done': True}, ensure_ascii=False)}\n\n"
                        break

                    # 发送SSE格式的数据
                    yield f"data: {json.dumps(message, ensure_ascii=False)}\n\n"

                except queue.Empty:
                    # 发送心跳包保持连接
                    yield f"data: {json.dumps({'type': 'heartbeat'}, ensure_ascii=False)}\n\n"
                    continue
                except Exception as e:
                    yield f"data: {json.dumps({'type': 'error', 'message': str(e)}, ensure_ascii=False)}\n\n"
                    break
        except Exception as e:
            yield f"data: {json.dumps({'type': 'error', 'message': f'服务器错误: {str(e)}'}, ensure_ascii=False)}\n\n"
        finally:
            # 搜索完成后扣除积分
            if search_completed:
                print(f"[DEBUG] 搜索完成，开始扣除积分")
                consume_credits(user['id'], search_cost, 0, f"搜索消费: {question[:50]}...")
                print(f"[DEBUG] 搜索积分扣除完成")
    
    return Response(
        generate(),
        mimetype='text/plain',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )

@app.route('/api/chat', methods=['POST'])
def chat():
    """直接聊天API端点（模型基底模式）"""
    data = request.get_json()
    message = data.get('message', '')

    if not message:
        return jsonify({'error': '消息不能为空'}), 400

    try:
        print(f"收到聊天请求: {message}")

        # 调用DeepSeek API进行对话
        answer = call_deepseek_api(message)

        # 如果API调用失败，检查是否是超时或连接错误，提供备用回答
        if "超时" in answer or "连接" in answer or "错误" in answer:
            print(f"DeepSeek API不可用，使用备用回答")
            answer = generate_fallback_answer(message)

        print(f"最终回答: {answer}")

        return jsonify({
            'answer': answer,
            'model': DEEPSEEK_MODEL,
            'timestamp': time.time()
        })
    except Exception as e:
        print(f"聊天API错误: {str(e)}")
        return jsonify({'error': f'聊天失败: {str(e)}'}), 500

@app.route('/api/chat/stream', methods=['POST'])
@require_auth
@check_credits(1)  # 至少需要1积分
def chat_stream():
    """流式聊天API端点（模型基底模式）"""
    data = request.get_json()
    messages = data.get('messages', [])
    system_prompt = data.get('systemPrompt', '')

    # 兼容旧版本的单消息格式
    if not messages and data.get('message'):
        messages = [{'role': 'user', 'content': data.get('message')}]

    if not messages:
        return jsonify({'error': '消息不能为空'}), 400

    user = request.current_user

    def generate_stream():
        full_response = ""
        # 计算输入token数（基于所有消息）
        messages_text = ' '.join([msg.get('content', '') for msg in messages])
        input_tokens = count_input_tokens(messages_text)
        output_tokens = 0

        print(f"[CHAT_STREAM] 对话历史长度: {len(messages)}")
        print(f"[CHAT_STREAM] 输入token数: {input_tokens}")

        try:
            print(f"收到流式聊天请求，消息数量: {len(messages)}")

            # 调用DeepSeek API进行流式对话，传入完整的消息历史
            response = call_deepseek_api(messages, stream=True, system_prompt=system_prompt)

            if isinstance(response, str):
                # 如果返回的是错误字符串，使用备用回答
                print(f"DeepSeek API不可用，使用备用回答")
                # 使用最后一条用户消息生成备用回答
                last_user_message = next((msg['content'] for msg in reversed(messages) if msg['role'] == 'user'), '')
                fallback_answer = generate_fallback_answer(last_user_message)
                full_response = fallback_answer
                output_tokens = count_output_tokens(fallback_answer)

                # 模拟流式输出备用回答
                for char in fallback_answer:
                    yield f"data: {json.dumps({'content': char, 'done': False})}\n\n"
                    time.sleep(0.05)  # 50ms延迟，模拟打字效果

                yield f"data: {json.dumps({'content': '', 'done': True})}\n\n"

                # 扣除积分
                cost = calculate_token_cost(input_tokens, output_tokens)
                consume_credits(user['id'], cost, input_tokens + output_tokens, f"聊天消费: {last_user_message[:50]}...")
                return

            # 处理流式响应
            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            data_str = line[6:]  # 去掉 'data: ' 前缀
                            if data_str.strip() == '[DONE]':
                                # 流式输出完成，扣除积分
                                output_tokens = count_output_tokens(full_response)
                                print(f"[CHAT_STREAM] 输出token数: {output_tokens}")
                                print(f"[CHAT_STREAM] 完整响应长度: {len(full_response)}字符")

                                cost = calculate_token_cost(input_tokens, output_tokens)
                                last_user_message = next((msg['content'] for msg in reversed(messages) if msg['role'] == 'user'), '')
                                consume_credits(user['id'], cost, input_tokens + output_tokens, f"聊天消费: {last_user_message[:50]}...")

                                yield f"data: {json.dumps({'content': '', 'done': True})}\n\n"
                                break
                            try:
                                data_obj = json.loads(data_str)
                                if 'choices' in data_obj and len(data_obj['choices']) > 0:
                                    delta = data_obj['choices'][0].get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        full_response += content
                                        yield f"data: {json.dumps({'content': content, 'done': False})}\n\n"
                            except json.JSONDecodeError:
                                continue
            else:
                # API错误，使用备用回答
                fallback_answer = generate_fallback_answer(message)
                full_response = fallback_answer
                output_tokens = count_output_tokens(fallback_answer)

                for char in fallback_answer:
                    yield f"data: {json.dumps({'content': char, 'done': False})}\n\n"
                    time.sleep(0.05)
                yield f"data: {json.dumps({'content': '', 'done': True})}\n\n"

                # 扣除积分
                cost = calculate_token_cost(input_tokens, output_tokens)
                last_user_message = next((msg['content'] for msg in reversed(messages) if msg['role'] == 'user'), '')
                consume_credits(user['id'], cost, input_tokens + output_tokens, f"聊天消费: {last_user_message[:50]}...")

        except Exception as e:
            print(f"流式聊天API错误: {str(e)}")
            error_msg = f"抱歉，发生了错误: {str(e)}"
            full_response = error_msg
            output_tokens = count_output_tokens(error_msg)

            for char in error_msg:
                yield f"data: {json.dumps({'content': char, 'done': False})}\n\n"
                time.sleep(0.05)
            yield f"data: {json.dumps({'content': '', 'done': True})}\n\n"

            # 即使出错也要扣除积分
            cost = calculate_token_cost(input_tokens, output_tokens)
            last_user_message = next((msg['content'] for msg in reversed(messages) if msg['role'] == 'user'), '')
            consume_credits(user['id'], cost, input_tokens + output_tokens, f"聊天消费(错误): {last_user_message[:50]}...")

    return Response(
        generate_stream(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )

def generate_fallback_answer(message):
    """生成备用回答（当DeepSeek API不可用时）"""
    # 简单的关键词匹配回答
    message_lower = message.lower()

    if any(word in message_lower for word in ['你好', 'hello', 'hi', '您好']):
        return "您好！我是基于DeepSeek-R1-Distill-Qwen-32B模型的AI助手。虽然当前无法连接到远程API服务，但我仍然可以为您提供一些基本的帮助和信息。请问有什么我可以为您做的吗？"

    elif any(word in message_lower for word in ['介绍', '自己', '你是谁', '什么是']):
        return "我是一个AI智能助手，基于DeepSeek-R1-Distill-Qwen-32B模型构建。我可以帮助您回答问题、提供信息、协助思考问题等。目前我正在本地模式下运行，如果您需要最新的网络信息，建议切换到'联网搜索'模式。"

    elif any(word in message_lower for word in ['算家云', '算家计算']):
        return "算家云是一个云计算平台，提供高性能计算服务。算家计算则是相关的计算服务提供商。它们主要为用户提供云端的计算资源和服务。如果您需要更详细和最新的信息，建议使用'联网搜索'模式来获取实时资料。"

    elif any(word in message_lower for word in ['帮助', 'help', '功能']):
        return "我可以为您提供以下帮助：\n\n1. 回答各种问题和提供信息\n2. 协助分析和思考问题\n3. 提供建议和指导\n4. 进行对话交流\n\n当前我在模型基底模式下运行，基于训练数据回答问题。如果您需要最新信息，请切换到'联网搜索'模式。"

    else:
        return f"感谢您的问题：{message}\n\n我理解您的询问，但目前无法连接到远程AI服务来提供详细回答。建议您：\n\n1. 稍后重试模型基底模式\n2. 切换到'联网搜索'模式获取最新信息\n3. 重新表述问题，我会尽力基于本地知识为您回答\n\n如有其他问题，请随时告诉我！"

@app.route('/api/status')
def status():
    """获取服务状态"""
    return jsonify({
        'status': 'running',
        'message': '搜索服务正常运行'
    })

@app.route('/api/files/<path:filepath>')
def download_file(filepath):
    """下载生成的文件"""
    try:
        directory = os.path.dirname(filepath)
        filename = os.path.basename(filepath)
        return send_from_directory(directory, filename, as_attachment=True)
    except Exception as e:
        return jsonify({'error': f'文件下载失败: {str(e)}'}), 404

# ==================== 用户系统API ====================

@app.route('/api/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '').strip()

        if not username or not email or not password:
            return jsonify({'error': '用户名、邮箱和密码不能为空'}), 400

        if len(password) < 6:
            return jsonify({'error': '密码长度至少6位'}), 400

        user_id = create_user(username, email, password)
        return jsonify({
            'success': True,
            'message': '注册成功',
            'user_id': user_id
        })

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        return jsonify({'error': '注册失败，请稍后重试'}), 500

@app.route('/api/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()

        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400

        # 检查是否为管理员账号
        if username in ADMIN_ACCOUNTS and ADMIN_ACCOUNTS[username] == password:
            # 管理员登录，创建临时用户对象
            user = {
                'id': f'admin_{username}',
                'username': username,
                'email': f'{username}@admin.local',
                'is_vip': True,
                'credits': 999999,
                'vip_expire_date': '2099-12-31T23:59:59',
                'created_at': datetime.datetime.now().isoformat()
            }
        else:
            # 普通用户登录
            user = authenticate_user(username, password)
            if not user:
                return jsonify({'error': '用户名或密码错误'}), 401

        # 创建会话
        from database import create_session
        session_token = create_session(user['id'])

        # 创建响应
        response = make_response(jsonify({
            'success': True,
            'message': '登录成功',
            'session_id': session_token,  # 返回session_id给前端
            'token': session_token,  # 前端期望的token字段
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'is_vip': user['is_vip'],
                'credits': user['credits']
            }
        }))

        # 设置cookie
        response.set_cookie('session_id', session_token,
                          max_age=7*24*60*60,  # 7天
                          httponly=True,
                          secure=False)  # 开发环境设为False

        return response

    except Exception as e:
        return jsonify({'error': '登录失败，请稍后重试'}), 500

@app.route('/api/logout', methods=['POST'])
@require_auth
def logout():
    """用户登出"""
    try:
        session_token = request.cookies.get('session_token')
        if session_token:
            from database import delete_session
            delete_session(session_token)

        response = make_response(jsonify({
            'success': True,
            'message': '登出成功'
        }))
        response.set_cookie('session_token', '', expires=0)

        return response

    except Exception as e:
        return jsonify({'error': '登出失败'}), 500

@app.route('/api/user/info', methods=['GET'])
@require_auth
def get_user_info():
    """获取用户信息"""
    try:
        user = request.current_user
        return jsonify({
            'success': True,
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'is_vip': user['is_vip'],
                'credits': user['credits'],
                'vip_expire_date': user.get('vip_expire_date'),
                'created_at': user['created_at']
            }
        })
    except Exception as e:
        return jsonify({'error': '获取用户信息失败'}), 500

@app.route('/api/chat/history', methods=['GET'])
@require_auth
def get_chat_history():
    """获取聊天历史"""
    try:
        user = request.current_user
        from database import get_user_chat_history

        chat_history = get_user_chat_history(user['id'])

        return jsonify({
            'success': True,
            'history': chat_history
        })
    except Exception as e:
        return jsonify({'error': f'获取聊天历史失败: {str(e)}'}), 500

@app.route('/api/chat/history', methods=['POST'])
@require_auth
def save_chat_history():
    """保存聊天记录"""
    try:
        user = request.current_user
        data = request.get_json()

        chat_record = {
            'id': data.get('id', str(uuid.uuid4())),
            'title': data.get('title', '新对话'),
            'mode': data.get('mode', 'default'),
            'messages': data.get('messages', []),
            'created_at': data.get('created_at', datetime.datetime.now().isoformat()),
            'updated_at': datetime.datetime.now().isoformat()
        }

        from database import add_chat_record
        saved_record = add_chat_record(user['id'], chat_record)

        return jsonify({
            'success': True,
            'chat': saved_record
        })
    except Exception as e:
        return jsonify({'error': f'保存聊天记录失败: {str(e)}'}), 500

@app.route('/api/chat/history/<chat_id>', methods=['PUT'])
@require_auth
def update_chat_history(chat_id):
    """更新聊天记录"""
    try:
        user = request.current_user
        data = request.get_json()

        updates = {
            'updated_at': datetime.now().isoformat()
        }

        if 'title' in data:
            updates['title'] = data['title']
        if 'messages' in data:
            updates['messages'] = data['messages']
        if 'mode' in data:
            updates['mode'] = data['mode']

        from database import update_chat_record
        update_chat_record(user['id'], chat_id, updates)

        return jsonify({
            'success': True,
            'message': '聊天记录更新成功'
        })
    except Exception as e:
        return jsonify({'error': f'更新聊天记录失败: {str(e)}'}), 500

@app.route('/api/chat/history/<chat_id>', methods=['DELETE'])
@require_auth
def delete_chat_history(chat_id):
    """删除聊天记录"""
    try:
        user = request.current_user

        from database import delete_chat_record
        delete_chat_record(user['id'], chat_id)

        return jsonify({
            'success': True,
            'message': '聊天记录删除成功'
        })
    except Exception as e:
        return jsonify({'error': f'删除聊天记录失败: {str(e)}'}), 500

@app.route('/api/user/credit-records', methods=['GET'])
@require_auth
def get_credit_records():
    """获取用户积分记录"""
    try:
        user = request.current_user
        from database import get_user_credit_records
        records = get_user_credit_records(user['id'])

        return jsonify({
            'success': True,
            'records': records,
            'total': len(records)
        })
    except Exception as e:
        return jsonify({'error': '获取积分记录失败'}), 500

@app.route('/api/user/upgrade-vip', methods=['POST'])
@require_auth
def upgrade_vip():
    """升级为VIP用户"""
    try:
        data = request.get_json()
        months = data.get('months', 1)  # 默认1个月

        if months not in [1, 3, 6, 12]:
            return jsonify({'error': '无效的VIP时长'}), 400

        user = request.current_user

        # 计算VIP过期时间
        now = datetime.datetime.now()
        if user['is_vip'] and user['vip_expire_date']:
            # 如果已经是VIP，在现有基础上延长
            current_expire = datetime.datetime.fromisoformat(user['vip_expire_date'])
            if current_expire > now:
                new_expire = current_expire + datetime.timedelta(days=30 * months)
            else:
                new_expire = now + datetime.timedelta(days=30 * months)
        else:
            # 新VIP用户
            new_expire = now + datetime.timedelta(days=30 * months)

        # 更新用户VIP状态
        from database import get_users, save_users
        users = get_users()
        users[user['id']]['is_vip'] = True
        users[user['id']]['vip_expire_date'] = new_expire.isoformat()
        users[user['id']]['credits'] = 10000000  # 设置为1000万积分
        users[user['id']]['updated_at'] = now.isoformat()
        save_users(users)

        # 记录充值记录（这里简化处理，实际应该对接支付系统）
        from database import load_json_file, save_json_file
        recharge_records = load_json_file('data/recharge_records.json', [])
        recharge_records.append({
            'id': str(uuid.uuid4()),
            'user_id': user['id'],
            'amount': months * 30.0,  # 假设每月30元
            'months': months,
            'payment_method': 'demo',
            'status': 'success',
            'order_id': f"VIP_{user['id']}_{int(time.time())}",
            'created_at': now.isoformat()
        })
        save_json_file('data/recharge_records.json', recharge_records)

        return jsonify({
            'success': True,
            'message': f'成功升级为VIP用户，有效期至 {new_expire.strftime("%Y-%m-%d")}',
            'vip_expire_date': new_expire.isoformat(),
            'credits': 10000000
        })

    except Exception as e:
        return jsonify({'error': '升级VIP失败，请稍后重试'}), 500

@app.route('/api/user/credits', methods=['GET'])
@require_auth
def get_credits():
    """获取用户积分信息"""
    try:
        user = request.current_user

        # 检查VIP状态
        is_unlimited = False
        if user['is_vip'] and user['vip_expire_date']:
            vip_expire = datetime.datetime.fromisoformat(user['vip_expire_date'])
            if vip_expire >= datetime.datetime.now():
                is_unlimited = True

        return jsonify({
            'success': True,
            'credits': user['credits'],
            'is_vip': user['is_vip'],
            'is_unlimited': is_unlimited,
            'vip_expire_date': user.get('vip_expire_date')
        })

    except Exception as e:
        return jsonify({'error': '获取积分信息失败'}), 500

# ==================== VIP充值API ====================

@app.route('/api/user/recharge', methods=['POST'])
@require_auth
def recharge_vip():
    """VIP充值"""
    try:
        data = request.get_json()
        card_key = data.get('card_key', '').strip()

        if not card_key:
            return jsonify({'error': '请输入卡密'}), 400

        user = request.current_user
        success, result = recharge_vip_with_card(user['id'], card_key)

        if success:
            return jsonify({
                'success': True,
                'message': result['message'],
                'expire_date': result['expire_date'],
                'days': result['days']
            })
        else:
            return jsonify({'error': result}), 400

    except Exception as e:
        return jsonify({'error': f'充值失败: {str(e)}'}), 500

# ==================== 管理员API ====================

@app.route('/api/admin/cards', methods=['GET'])
@require_auth
@require_admin
def admin_get_cards():
    """获取所有卡密"""
    try:
        cards = get_all_cards()
        stats = get_card_stats()
        return jsonify({
            'success': True,
            'cards': cards,
            'stats': stats
        })
    except Exception as e:
        return jsonify({'error': f'获取卡密失败: {str(e)}'}), 500

@app.route('/api/admin/cards', methods=['POST'])
@require_auth
@require_admin
def admin_create_card():
    """创建卡密"""
    try:
        data = request.get_json()
        days = data.get('days', 30)
        description = data.get('description', 'VIP充值卡')
        count = data.get('count', 1)

        # 使用database.py中的创建函数
        created_cards = create_vip_cards(count, days, description)

        if created_cards:
            return jsonify({
                'success': True,
                'message': f'成功创建{len(created_cards)}张卡密',
                'cards': created_cards
            })
        else:
            return jsonify({'error': '创建卡密失败'}), 400

    except Exception as e:
        return jsonify({'error': f'创建卡密失败: {str(e)}'}), 500

@app.route('/api/admin/cards/<card_key>', methods=['DELETE'])
@require_auth
@require_admin
def admin_delete_card(card_key):
    """删除卡密"""
    try:
        success, message = delete_vip_card(card_key)
        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'error': message}), 400
    except Exception as e:
        return jsonify({'error': f'删除卡密失败: {str(e)}'}), 500

@app.route('/api/admin/cards/batch-delete', methods=['POST'])
@require_auth
@require_admin
def admin_batch_delete_cards():
    """批量删除未使用的卡密"""
    try:
        success, message = batch_delete_unused_cards()
        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'error': message}), 400
    except Exception as e:
        return jsonify({'error': f'批量删除失败: {str(e)}'}), 500

@app.route('/api/admin/usage', methods=['GET'])
@require_auth
@require_admin
def admin_get_usage():
    """获取卡密使用记录"""
    try:
        records = get_card_usage_records()
        return jsonify({
            'success': True,
            'records': records
        })
    except Exception as e:
        return jsonify({'error': f'获取使用记录失败: {str(e)}'}), 500

@app.route('/api/admin/archive-used-cards', methods=['POST'])
@require_auth
@require_admin
def admin_archive_used_cards():
    """批量归档已使用的卡密"""
    try:
        data = request.get_json()
        card_keys = data.get('card_keys', [])

        if not card_keys:
            return jsonify({'error': '没有指定要归档的卡密'}), 400

        success, message = archive_used_cards(card_keys)
        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'error': message}), 400
    except Exception as e:
        return jsonify({'error': f'批量归档失败: {str(e)}'}), 500

@app.route('/api/admin/archived-cards', methods=['GET'])
@require_auth
@require_admin
def admin_get_archived_cards():
    """获取归档的卡密"""
    try:
        import os
        import glob
        from datetime import datetime

        archived_cards = []
        archived_files = 0

        # 查找所有归档文件
        archive_pattern = 'data/archived_cards_*.json'
        archive_files = glob.glob(archive_pattern)
        archived_files = len(archive_files)

        for archive_file in archive_files:
            try:
                # 从文件名提取日期
                filename = os.path.basename(archive_file)
                date_str = filename.replace('archived_cards_', '').replace('.json', '')
                archive_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"

                # 读取归档文件
                from database import load_json_file
                archived_data = load_json_file(archive_file, {})

                for card_key, card_data in archived_data.items():
                    card_info = {
                        'card_key': card_key,
                        'days': card_data.get('days', 30),
                        'status': card_data.get('status', 'used'),
                        'description': card_data.get('description', ''),
                        'created_at': card_data.get('created_at'),
                        'used_by': card_data.get('used_by'),
                        'used_at': card_data.get('used_at'),
                        'archive_date': archive_date
                    }
                    archived_cards.append(card_info)
            except Exception as e:
                print(f"读取归档文件失败 {archive_file}: {e}")
                continue

        # 按使用时间倒序排列
        archived_cards.sort(key=lambda x: x.get('used_at', ''), reverse=True)

        stats = {
            'total_archived': len(archived_cards),
            'archived_files': archived_files
        }

        return jsonify({
            'success': True,
            'archived_cards': archived_cards,
            'stats': stats
        })

    except Exception as e:
        return jsonify({'error': f'获取归档卡密失败: {str(e)}'}), 500

# ==================== 用户管理API ====================

@app.route('/api/admin/users', methods=['GET'])
@require_auth
@require_admin
def admin_get_users():
    """获取所有用户"""
    try:
        from database import get_all_users
        users = get_all_users()
        return jsonify({
            'success': True,
            'users': users
        })
    except Exception as e:
        return jsonify({'error': f'获取用户失败: {str(e)}'}), 500

@app.route('/api/admin/users', methods=['POST'])
@require_auth
@require_admin
def admin_create_user():
    """创建用户"""
    try:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        credits = data.get('credits', 1000)
        is_vip = data.get('is_vip', False)
        vip_expire_date = data.get('vip_expire_date')

        if not username or not email or not password:
            return jsonify({'error': '用户名、邮箱和密码不能为空'}), 400

        from database import create_user_admin
        success, result = create_user_admin(username, email, password, credits, is_vip, vip_expire_date)

        if success:
            return jsonify({'success': True, 'message': '用户创建成功', 'user': result})
        else:
            return jsonify({'error': result}), 400

    except Exception as e:
        return jsonify({'error': f'创建用户失败: {str(e)}'}), 500

@app.route('/api/admin/users/<user_id>', methods=['PUT'])
@require_auth
@require_admin
def admin_update_user(user_id):
    """更新用户"""
    try:
        data = request.get_json()

        from database import update_user_admin
        success, result = update_user_admin(user_id, data)

        if success:
            return jsonify({'success': True, 'message': '用户更新成功', 'user': result})
        else:
            return jsonify({'error': result}), 400

    except Exception as e:
        return jsonify({'error': f'更新用户失败: {str(e)}'}), 500

@app.route('/api/admin/users/<user_id>', methods=['DELETE'])
@require_auth
@require_admin
def admin_delete_user(user_id):
    """删除用户"""
    try:
        from database import delete_user_admin
        success, message = delete_user_admin(user_id)

        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'error': message}), 400

    except Exception as e:
        return jsonify({'error': f'删除用户失败: {str(e)}'}), 500

@app.route('/api/admin/stats', methods=['GET'])
@require_auth
@require_admin
def admin_get_stats():
    """获取系统统计"""
    try:
        from database import get_system_stats
        stats = get_system_stats()
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        return jsonify({'error': f'获取统计失败: {str(e)}'}), 500

if __name__ == '__main__':
    print("启动网页搜索服务...")
    print("访问地址: http://localhost:8080")
    print("局域网访问地址: http://*************:8080")
    app.run(debug=True, host='0.0.0.0', port=8080, threaded=True)

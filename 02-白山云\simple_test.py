#!/usr/bin/env python3
"""
EdgeFN API 简单示例
使用您提供的代码格式，修正语法错误
"""

import requests

# 基础示例（修正版）
url = "https://api.edgefn.net/v1/chat/completions"
headers = {
    "Authorization": "Bearer sk-JGGCOW4Ye6fhyVFK45Fe46734bF84d1a934dFa3cCfD7337b",
    "Content-Type": "application/json"
}
data = {
    "model": "DeepSeek-R1-0528-Qwen3-8B",
    "messages": [{"role": "user", "content": "Hello, how are you?"}]  # 修正了语法错误
}

def simple_chat_test():
    """简单的聊天测试"""

    print("=" * 60)
    print("🤖 EdgeFN API 简单示例")
    print("=" * 60)
    print(f"📡 API地址: {url}")
    print(f"🤖 模型: {data['model']}")
    print(f"💬 发送消息: {data['messages'][0]['content']}")
    print("-" * 60)

    try:
        print("🔄 正在发送请求...")

        # 使用您提供的代码格式
        response = requests.post(url, headers=headers, json=data)
        result = response.json()

        print(f"📊 HTTP状态码: {response.status_code}")

        if response.status_code == 200:
            if 'choices' in result and len(result['choices']) > 0:
                reply = result['choices'][0]['message']['content']
                print("✅ 请求成功！")
                print(f"🤖 AI回复: {reply}")

                # 显示使用统计
                if 'usage' in result:
                    usage = result['usage']
                    print(f"\n📈 使用统计:")
                    print(f"   输入tokens: {usage.get('prompt_tokens', 'N/A')}")
                    print(f"   输出tokens: {usage.get('completion_tokens', 'N/A')}")
                    print(f"   总tokens: {usage.get('total_tokens', 'N/A')}")

                print("\n🎉 测试完成！API工作正常")
                return True
            else:
                print("❌ 响应格式异常")
                print("完整响应:", result)
                return False
        else:
            print(f"❌ API调用失败 (状态码: {response.status_code})")
            print("错误信息:", result)
            return False

    except Exception as e:
        print(f"❌ 发生错误: {e}")
        try:
            print("响应内容:", response.text if 'response' in locals() else "无响应")
        except:
            pass
        return False
    finally:
        print("=" * 60)

# 直接执行您提供的代码格式
def basic_example():
    """基础示例 - 直接使用您提供的代码格式"""
    print("\n" + "=" * 60)
    print("📝 基础示例（您提供的代码格式）")
    print("=" * 60)

    response = requests.post(url, headers=headers, json=data)
    print("📋 原始响应:")
    print(response.json())

if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 详细测试（推荐）")
    print("2. 基础示例（您的代码格式）")

    choice = input("请选择 (1/2): ").strip()

    if choice == "2":
        basic_example()
    else:
        simple_chat_test()

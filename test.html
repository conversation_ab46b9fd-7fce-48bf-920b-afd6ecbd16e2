<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>冒泡排序可视化</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      text-align: center;
    }
    h1 {
      color: #333;
    }
    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }
    #array-input {
      padding: 8px;
      font-size: 16px;
      width: 200px;
    }
    button {
      padding: 10px 20px;
      font-size: 16px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    #array-display {
      display: flex;
      justify-content: center;
      gap: 5px;
      margin: 20px 0;
    }
    .bar {
      width: 30px;
      background-color: #3498db;
      transition: height 0.3s ease;
    }
    .sorted {
      background-color: #2ecc71;
    }
    #status {
      font-size: 18px;
      margin-top: 10px;
    }
    .controls {
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>冒泡排序可视化</h1>
    <div>
      <label for="array-input">输入数组（用逗号分隔）：</label>
      <input type="text" id="array-input" placeholder="例如：5,3,8,6,2">
    </div>
    <button id="sort-btn">开始排序</button>
    <div id="array-display"></div>
    <div id="status"></div>
    <div class="controls">
      <label>
        <input type="checkbox" id="optimized"> 启用优化（提前退出）
      </label>
    </div>
  </div>

  <script>
    const arrayDisplay = document.getElementById('array-display');
    const statusDisplay = document.getElementById('status');
    const sortBtn = document.getElementById('sort-btn');
    const arrayInput = document.getElementById('array-input');
    const optimized = document.getElementById('optimized');

    let array = [];

    // 初始化数组显示
    function renderArray(arr) {
      arrayDisplay.innerHTML = arr.map(num => (
        `<div class="bar" style="height: ${num * 30}px;"></div>`
      )).join('');
    }

    // 生成随机数组
    function generateRandomArray(size = 10) {
      return Array.from({ length: size }, () => Math.floor(Math.random() * 50) + 1);
    }

    // 冒泡排序主函数
    function bubbleSort(arr, optimizedFlag) {
      array = [...arr];
      renderArray(array);
      statusDisplay.textContent = "排序中...";

      for (let i = 0; i < array.length; i++) {
        let swapped = false;
        for (let j = 0; j < array.length - i - 1; j++) {
          if (array[j] > array[j + 1]) {
            // 交换元素
            [array[j], array[j + 1]] = [array[j + 1], array[j]];
            swapped = true;
            // 渲染动画
            renderArray(array);
            statusDisplay.textContent = `比较第 ${i + 1}/${array.length} 轮，当前最大值: ${array[j]}`;
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        }
        if (!swapped && optimizedFlag) {
          break; // 提前退出优化
        }
      }

      statusDisplay.textContent = "排序完成！";
      renderArray(array);
      arrayDisplay.querySelectorAll('.bar').forEach(bar => bar.classList.add('sorted'));
    }

    // 事件监听
    sortBtn.addEventListener('click', () => {
      try {
        const input = arrayInput.value.trim();
        if (!input) {
          throw new Error("请输入数组！");
        }
        const arr = input.split(',').map(Number).filter(num => !isNaN(num));
        bubbleSort(arr, optimized.checked);
      } catch (e) {
        statusDisplay.textContent = `错误: ${e.message}`;
      }
    });

    // 测试随机数组
    arrayInput.addEventListener('input', () => {
      if (arrayInput.value.trim() === '') {
        array = [];
        renderArray(array);
        statusDisplay.textContent = "";
      }
    });
  </script>
</body>
</html>
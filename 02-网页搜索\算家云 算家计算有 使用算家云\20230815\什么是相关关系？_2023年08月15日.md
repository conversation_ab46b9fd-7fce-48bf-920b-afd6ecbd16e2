﻿# 什么是相关关系？

**发布日期**: 2023年08月15日

**原文链接**: http://www.fujian.gov.cn/hdjl/hdjlzsk/tjj/tjzs/zh/202307/t20230712_6204125.htm

## 📄 原文内容

现象与现象之间关系的方式及关系的密切程度各不相同。其中一种极端的情况是一个现象（或变量）的变化完全决定另一个现象（或变量）的变化，这种关系就是函数关系。当一个或几个变量取一定的值时，另一个变量有唯一确定的值与之对应，则称这种关系为确定性的函数关系，记为
称为因变量。例如某商业银行的一年期存款利率是
。这里，本利和与本金之间是一种确定性的函数关系，在利率不变的情况下，本利和本金的大小可以完全决定一年期的本利和。
函数关系是一一确定的关系，即一个变量发生变动，另一个变量会严格按照函数关系发生变动。实际问题中变量之间的关系往往更复杂，变量的变动会受到很多因素的影响，有的因素可能超过了我们目前的认知，有的无法量化，所以真实世界中的变量关系往往不是函数关系。
比如，身高与体重的关系就不能用函数关系来描述。通常情况下，一个人身高比较高，其体重也会相应比较重，但是体重不是唯一由身高确定的，有些身高很高但是比较瘦的人，其体重反而不如身高低的人体重重。身高和体重之间的关系就是一种相关关系。
再例如，个人的经济地位与父辈的经济地位之间有相关关系，但是不是唯一确定的。一般情况下，父辈经济地位更高的，子辈的经济地位也更高，在高度不平等的国家二者的相关性越强，即收入的代际流动性较低，子女处于父辈的经济阶层的可能性就越高。
我们把这种相互依存的，又不是严格确定的关系称之为相关关系（
correlation）。相关分析（correlation analysis）就是两个变量之间的关系的描述与度量。
国家林业和草原局驻福州森林资源监督专员办事处
国家林业和草原局驻福州森林资源监督专员办事处
document.write(unescape("%3Cspan id='_ideConac' class='foot-icon02 pho-none' %3E%3C/span%3E%3Cscript src='https://dcs.conac.cn/js/14/000/0000/40445163/CA140000000404451630001.js' type='text/javascript'%3E%3C/script%3E"));
为确保最佳浏览效果，建议您使用以下浏览器版本：IE浏览器9.0版本及以上； 谷歌浏览器 63版本及以上； 360浏览器9.1版本及以上，且IE内核9.0及以上。
var trt_userAgent = navigator.userAgent;
    if (!/miniProgram/i.test(trt_userAgent) && !/micromessenger/i.test(trt_userAgent) && !/AlipayClient/i.test(trt_userAgent)  ){
        document.write('<script type="text/javascript" src = "https://mztapp.fujian.gov.cn:8190/mztAppWeb/app/js/cordova.js"><\/script>');
window.onload = function() {
	    	var _currentUrl = window.location.href;
	    	if(_currentUrl.indexOf("/english/")<0){
	    		var _script = document.createElement ("script")
	    		_script.type = "text/javascript";
	    		_script.src = 'https://zfwzgl.www.gov.cn/exposure/jiucuo.js';
	    		_script.onload = function(){
	    			$('#_span_jiucuo img').attr('alt','链接政府网站找错');
	    			$('#_span_jiucuo img').css('width','90px');
	    		document.getElementsByTagName('head')[0].appendChild(_script);
var SiteInfo = { //站点频道
        , was4doc : 291575 //检索
        , was4bmdt : 206468  //253334 //部门动态
        , was4bmwj : 291675 //部门文件
        , was4sjjd : 282572 //数据解读
        , was4zcjd : 211138 //政策解读
        , was4rel : 262249//相关文档
        , itp4poll: 943   //调查
        , itp4advice: 961 //征集
        , itp4ftzj: 975   //访谈征集
        , itp4apply: 964  //依申请
        , itp4site: 301   //互动站点
        , siteHost: "www.fujian.gov.cn"
        , bjtjUrl: "/static/sybjtj.json"
        ,visitHost:"www.fujian.gov.cn"   //流量域名地址，避免后续有变更
        //,jt_set:"2022/09/29 00:00:00-2022/10/25 23:59:59"  //静态日期段 zhxr
		,jt_set:"2024/5/24 00:00:00-2024/5/25 06:00:00"  //静态日期段 zhxr
        ,jt_hour:"0-6" //静态时间段 zhxr
		,mapkey:"3aca6a1cc1bcd522e28842a31ae132c3"
    avalon.filters.repHtml = function(str){
		return str.replace(/<\/?[a-z0-9]{1,10}[^>]*>/img,'')
 	//加载外部css文件zhxr，增补动态加载js，由type控制
	function dynamicLoadCss(url,type) {
		if(typeof type =="undefined"){
			var head = document.getElementsByTagName('head')[0];
			var link = document.createElement('link');
			link.type='text/css';
			link.rel = 'stylesheet';
			head.appendChild(link);
			var script = document.createElement ("script")
			script.type = "text/javascript";
			document.getElementsByTagName('head')[0].appendChild(script);
    require(["jquery","tools"], function (jquery,tools) {
        var dblogin = avalon.define({
            $id: "Login",
            isLogin: false, //判断是否登录
            username: '',usertype:'',
            isfav: false, //判断是否收藏,默认未收藏
            uext:false,uext2:'',uext3:'',
            addFav: function (title, url) { //收藏
                if (!dblogin.isLogin) {
                    var mymessage = confirm("请先进行登录！");
                    if (mymessage == true) { //点击确定，跳转登录页
                        window.location.href = "/suc/login/login?redirtUrl=http://" + encodeURI( encodeURI(window.location.host) ) + "/suc/login/noseLogin?redirtUrl=" + encodeURI( encodeURI(window.location.href) );
                    } else if (mymessage == false) {
                    }
                if (!dblogin.isfav && dblogin.isLogin) {
                    jquery.ajax({
                        url: "/suc/apis/collect",
                        dataType: "json",
                        type: "post",
                        data: {
                            title: PageInfo.title,
                            url: window.location.href,
                            random: Math.random()
                        },
                        //jsonpCallback:"jsonpCallback",
                        success: function (data) {
                            //avalon.log(data);
                            if (!data.error) {
                                alert("收藏成功！")
                                dblogin.isfav = true;
                            }
                        },
                        error: function (data) {
                            avalon.log("请求失败！");
                        }
                    });
            canceladdFav: function () { //取消收藏
                var mymessage = confirm("确定是否要取消收藏？");
                if (mymessage == true) { //点击确定，取消收藏
                    jquery.ajax({
                        url: "/suc/apis/canceCollect",
                        dataType: "json",
                        type: "post",
                        data: {
                            url: window.location.href,
                            random: Math.random()
                        },
                        //jsonpCallback:"jsonpCallback",
                        success: function (data) {
                            //avalon.log(data);
                            if (!data.error) {
                                alert("取消收藏成功！")
                                dblogin.isfav = false;
                            }
                        },
                        error: function (data) {
                            avalon.log("请求失败！");
                        }
                    });
                } else if (mymessage == false) {
        setTimeout(function () {
            var cspucToken = jquery.cookie("cspucToken");
            if (cspucToken) {
                setTimeout(function () {
                    jquery.ajax({
                        url: "/suc/login/isLogin",
                        dataType: "json",
                        type: "get",
                        //jsonpCallback:"jsonpCallback",
                        success: function (data) {
                            if (data.data) { //登录
                                dblogin.isLogin = true;
                                dblogin.username = data.data.user_account;
                                dblogin.usertype = data.data.user_type;
                                dblogin.user = data.user;
                                jquery(".wmbss_login").css("display","none");
                            } else {
                                dblogin.isLogin = false;
                                jquery(".wmbss_login").css("display","block");
                            }
                        },
                        error: function (data) {
                            avalon.log("请求失败！");
                        }
                    });
                }, 1000);
                setTimeout(function () {
                    //判断是否收藏
                    if (!dblogin.isLogin) {
                        //avalon.log("未登录状态")
                    } else {
                        //avalon.log("登录状态")
                        jquery.ajax({
                            url: "/suc/apis/isConllect",
                            dataType: "json",
                            type: "post",
                            data: {
                                url: window.location.href,
                                random: Math.random()
                            },
                            success: function (data) {
                                //avalon.log(data);
                                if (data.isConllect) { //已收藏
                                    dblogin.isfav = true;
                                }
                            },
                            error: function (data) {
                                avalon.log("请求失败！");
                            }
                        });
                    }
                }, 3000);
                jquery(".wmbss_login").css("display","block");
	var u = navigator.userAgent;
	var idx = u.indexOf("mzt/");
		$(".tuichu").hide()
           var mzt_html = "<iframe src='/inc/login.htm' ignoreapd='1'></iframe>";
                $("#mzt_iframe").html(mzt_html);
	function getUserInfos(){
		app.page.onLoad=function(){
			app.link.getLoginInfo(function(result){ 
				//app.alert(result)
				setUserInfos(result);	
	function setUserInfos(result){
		var _time = new Date().getTime();
			type : "POST",//请求方式
			dataType : "json",//请求的媒体类型
			url : "/suc/login/mztapplogin? _time="+ _time,//请求地址
			data : {mazappMap:JSON.stringify(result)},//数据，json字符串JSON.stringify()
			success : function(result) {
			   dblogin.isLogin = true;
			error : function(e){
				// console.log(e.status);
				// console.log(e.responseText);
        $('.xzk_2021>ul>li').click(function(){
            if ($(this).hasClass('on')) {
                return
                $(this).addClass('on').siblings().removeClass('on');
                $(this).siblings().children('.mark_2021').slideUp()
                $(this).children('.mark_2021').slideDown();
            return false;   //阻止冒泡
        $(document).click(function(){
            $('.mark_2021').slideUp()
            $('.xzk_2021>ul>li').removeClass('on');
if(avalon.vmodels.windowRoot.Statics.all){
if($(".xl_con .xl_con4 p").last().text().indexOf("纠错")>-1){
$(".xl_con .xl_con4 p").last().hide();
   $(".con_bg .gl_tit3 .gl_tit3_r input[value='搜索'] ").click(function () {
    alert("系统正在升级，为此对您造成的不便敬请谅解！")
    return false; //阻止冒泡
        var static = avalon.define({
            $id: "Static",
            //list: [],
            error: "",
            showStatic: 1//1读取接口数据，2静态数据
            ,showStaticMail: false
        //    url: "/itp/interface/poll/search.action?chnlIds=943&order=-endTime&page.pagesize=7",
        //    type: "get",
        //   dataType: "json",
        //    success: function (d) {
        //        static.showStatic = 1;
        //    error: function (e) {
        //        avalon.log("互动系统已关闭，页面展示静态数据。")
        //        static.showStatic = 2;
		var hours = new Date().getHours();
		if( Number(hours)>=0 && Number(hours)<6 ){
			static.showStaticMail = true ;　
			$("#ftb").hide();　　
        function compareTime(timestr1,timestr2){
            var time1 = new Date(timestr1);
            var time2 = new Date(timestr2);
            if(time1 > time2){
                return true;
                return false
        function isGdLdhd(){
            var docreltime = $(".xl_tit6_l span").eq(1).text().slice(0,10) ;
            var GdImgTag = '<i class="ygd-xl"><img src="/images/20201027-ygd.png" alt="" /></i>';
            if(window.location.href.indexOf("/szf/hd/") > -1 && compareTime("2020-04-12",docreltime) ){
                dynamicLoadCss("/images/20210429-qzbxl.css")
                $(".xl_tit4").append(GdImgTag);
    function tabWza(id, className, hdClassName) {
        var $hdLi = $(id).find(hdClassName || '.hd li')
        className = className || '.bd .wza-list'
        var $bdList = $(id).find(className)
        if($bdList.length != $hdLi.length) return;
        $hdLi.each(function(index) {
            var $a = $(this).find('a:eq(0)')
            if($a.length > 0) {
                $a.focus(function(e) {
                    e = $(this).mouseover()
                    $(this).bind('keydown.wza_tab', function(e) {
                        if(!e.ctrlKey && !e.altKey && e.keyCode === 9) {
                            var $curList = $bdList.eq(index)
                            console.log($curList.find('a').length)
                            if($curList.find('a').length < -1) {
                                $hdLi.eq(index + 1).find('a:eq(0)').focus()
                                return false
                            }
                            $curList.find('a:eq(0)').focus()
                            return false
                        }
                    })
                $a.blur(function() {
                    $(this).unbind('keydown.wza_tab')
        $bdList.each(function(index) {
            if(index + 1 == $bdList.length) return;
            var $curList = $(this)
            var $li = $curList.find('li')
            var endTabLen = $li.length;
            $li.eq(endTabLen - 1).find('a:eq(0)').focus(function() {
                console.log($(this))
                $(this).bind('keydown.wza_tab_leave', function(e) {
                    if(!e.ctrlKey && !e.altKey && e.keyCode === 9) {
                        $hdLi.eq(index + 1).find('a:eq(0)').focus()
                        return false;
                    }
            $li.eq(endTabLen - 1).find('a:eq(0)').blur(function() {
                $(this).unbind('keydown.wza_tab_leave')
    function tabWza2($bindId, $firstId, $lastId, callback) {
        $bindId.focus(function(e) {
            e = $(this).mouseover()
            $(this).bind('keydown.wza_tab', function(e) {
                if(!e.ctrlKey && !e.altKey && e.keyCode === 9) {
                    if($firstId.length > 0) $firstId.focus()
                    return false;
        $bindId.blur(function() {
            $(this).unbind('keydown.wza_tab')
        if(typeof callback != 'function') return
        $lastId.focus(function(e) {
            e = $(this).mouseover()
            $(this).bind('keydown.wza_tab_leave', function(e) {
                if(!e.ctrlKey && !e.altKey && e.keyCode === 9) {
                    callback()
                    return false
        $lastId.blur(function() {
            $(this).unbind('keydown.wza_tab_leave')
    function tabWza3($bindId, $nextId, eventModel) {
        eventModel = eventModel || 'wza_tab_leave'
        eventModel = 'keydown.' + eventModel
        $bindId.focus(function(e) {
            e = $(this).mouseover()
            $(this).bind('keydown.wza_tab_leave', function(e) {
                if(!e.ctrlKey && !e.altKey && e.keyCode === 9) {
                    $nextId.focus()
                    return false
        $bindId.blur(function() {
            $(this).unbind('keydown.wza_tab_leave')
        docid : '6204125',
        chnlid : '26849',
        siteid : '51',
        title : "什么是相关关系？",
        chnlname : "综合",
        sitename : "省政府"
	var gkmlId = "22053";
	var gkmlName = "省政府文件";
	var gkmlUrl = "http://www.fujian.gov.cn/zwgk/zfxxgk/szfwj/";
	var chnlUrl = "http://www.fujian.gov.cn/hdjl/hdjlzsk/tjj/tjzs/zh/";
	if( chnlUrl.indexOf( gkmlUrl) ==0 ){
		PageInfo.chnlid = gkmlId ;  //强制设置为公开目录的编号
		PageInfo.chnlname = gkmlName ;  
    if( "什么是相关关系？" == "" ){
        PageInfo.title = "什么是相关关系？";
    if( PageInfo.title == "" ){
        PageInfo.title = PageInfo.chnlname;
    if( PageInfo.title == "" ){
        PageInfo.title = PageInfo.sitename;
    require(["pageview"],function( objModel ){
		if( window.location.href.indexOf("/preview")==-1){
			var pageview = objModel( PageInfo );
			pageview.view( PageInfo , function(d){
					avalon.vmodels.windowRoot.pvCount = d.pvt ;
					avalon.vmodels.windowRoot.siteCount = d.pvst ;
    function slide(flag) {
    //初始化superslider的方法
    function initSlider() {
$(".share").mouseover(function(){
		$(this).children('.share_con').show();
	$(".share").mouseleave(function(){
		$(this).children('.share_con').hide();
			title: document.title //
			,desc :(typeof document.getElementsByTagName('meta').Desctiption!= 'undefined')?document.getElementsByTagName('meta').Description.content:""
			,url :window.location.href
			,site:window.location.host
			,pic: (typeof document.getElementsByTagName('meta').Image!= 'undefined')?document.getElementsByTagName('meta').Image.content:""
			,tsina:"http://service.weibo.com/share/share.php?url="
			,qzone:"http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url="
			,qq:'http://connect.qq.com/widget/shareqq/index.html?url='
			,douban:'http://shuo.douban.com/!service/share?href='
		,weixin:function(){
			$(".wx_dialog").remove();
			var Wx_dialog = '<div class="wx_dialog"><div class="bd_weixin_popup_head"><span>分享到微信</span><a href="javascript:;"  class="bd_weixin_popup_close">×</a></div><div class="wx_erm" id="wx_erm"></div><div class="bd_weixin_popup_foot"><p>若想获得更好的卡片式分享效果，可使用以下两种方式：</p><p>1、使用手机浏览器扫码打开页面后再分享至微信；</p><p>2、使用微信扫码，在微信中选择“在浏览器打开”后再从手机浏览器分享至微信。</p></div></div>';
			$("body").append(Wx_dialog);
			$(".bd_weixin_popup_close").click(function(){
			   $(".wx_dialog").remove();
			new QRCode("wx_erm", {text: this.config.url ,width: 180,height: 190,colorDark : "#000000",colorLight : "#ffffff",correctLevel : QRCode.CorrectLevel.H});
			var _url = this.config.tsina + this.config.url + '&title=' + this.config.title + '&pic=' + this.config.pic + '&ralateUid=' + this.config.uid + '&searchPic=true';
			window.open(_url);
		    var _url = this.config.qzone + this.config.url +'&title=' + this.config.title +'&desc='+this.config.desc +'&pics=' + this.config.pic; 
			window.open(_url);
		    var _url = this.config.qq + this.config.url +'&sharesource=qzone&title=' + this.config.title +'&pics=' + this.config.pic + '&summary='+this.config.desc +'&desc='+this.config.desc
			window.open(_url);
		,douban:function(){
			 var _url = this.config.douban + this.config.url +'&name=' + this.config.title +'&image=' + this.config.pic + '&text='+this.config.desc
			window.open(_url);
	if($(".share-box").length>0){
		$(".share-box i").click(function(){ 
		 var type = $(this).attr("data-cmd");
		$(".share-box li a").click(function(){ 
		 var type = $(this).attr("data-cmd");
	if($(".share-box-r").length>0){
		$(".share-box-r i").click(function(){ 
		 var type = $(this).attr("data-cmd");
		$(".share-box-r li a").click(function(){ 
		 var type = $(this).attr("data-cmd");
		var subdoctitle = ""
require(["jquery", "add", "list"], function ($, add, listModel) {
			avalon.vmodels.Login.$watch("username", function (newVal, oldVal) {
					avalon.vmodels.add.addForm.sender = newVal;
					$("#xlFrom .username").attr("disabled", "disabled");
					$("#xlFrom .username").css('cursor', 'not-allowed')
			$("#review").click(function () {
				$(".znwd_pj").slideDown();
				avalon.vmodels.add.$getImg();
var s = document.getElementById('detailCont2');
		var s1 = document.getElementById('detailCont2');
		//s1.innerHTML=s.innerHTML.replace( /style\s*?=\s*?(['"])[\s\S]*?\1/g,'').replace( /face\s*?=\s*?(['"])[\s\S]*?\1/g,'');
		require(["jquery", "list", "filters"], function ($, listModel) {
			avalon.filters.repbr = function (e) { return e.replace(/<br[ \/]?>/img, "") };
			var pattern = /[\s\(\)\[\]〔〕【】]/img;
			subdoctitle = subdoctitle.replace(pattern, '');
			fileno = fileno.replace(pattern, '');
			if (subdoctitle == fileno) {
				avalon.vmodels.windowRoot.flag = true;
			/**细览相关文档调整，1、双向关联  2、上面展示文件 3、下面其他相关文档**/
						channelid: SiteInfo.was4rel,
						classsql: "(docid2=6204125)+(reldocid=6204125)",
						templet: 'reldoc.jsp',
				showStatic: false,
				injAfterGetData: function (d) {
					var data = d.chnls;
					for (var i = 0; i < d.count; i++) {
						if (data[i].reldocid == "6204125") {
							// docs.push(data[i].docid2)
							docs.push(data[i].docid)
							docs.push(data[i].reldocid)
					//avalon.log(docs)
					//if (docs.length > 0) {
					//	getRel(docs);
			var getRel = function (docs) {
				var flag = (docs.length>0)?1:false;  
				var rellist = listModel({
					objId: 'rellist',
							channelid: SiteInfo.was4doc,
							classsql: "modal=1*docid2=" + docs.join(",")
					autoGetPage: flag
						// showJd: false,
						list_video: [],
						isright: false,
						isshow_Xglj: false,
						isShow_zcwj:false,
						isShow_xwfb:false,
						isShow_tj: false,
						isShow_sp: false,
						isShow_mtjd: false,
						isShow_xgbd:false,
						isShow_ft:false,
						isShow_mtpl:false,
					, injAfterGetData: function (d) {
						var data = d.data;
						for (var i = 0; i < d.count; i++) {
							if (data[i].extselect1 == 2||data[i].chnlid==21407||data[i].chnlid==21424) {  //图片解读id21424
								var obj = {};
								var rooturl = data[i].chnldocurl.replace(/\/[^\/]+$/, '/');
								var _i = data[i].img.split(";");
								if (data[i].type != '30') { //排除掉链接类型
									if (_i[0] != '') { _img = _i[0]; }
								} else {  //链接型拼接成/webpic/的路径，从WCM的目录中代理
									if (/W0(\d{6})(\d{2})(\d{12,})\.(jpg|png|gif)/.test(_i[0])) {
										_img = "/webpic/W0" + RegExp.$1 + "/W0" + RegExp.$1 + RegExp.$2 + "/" + _i[0];
								obj = { "docid": data[i].docid, "title": data[i].title, "title2": data[i].title2, "chnldocurl": data[i].chnldocurl, "chnl": data[i].chnl, "img": _img }
								rellist.$vm.list_img.push(obj);
								// $("#tpjd").css("background-image", 'url(' + rellist.$vm.list_img[0].img + ')');
								rellist.$vm.isShow_tj = true;
								rellist.$vm.isright = true; //右侧展示
							} else if (data[i].extselect1 == 3||data[i].chnlid==27770) {  //视频解读id
								var obj = {};
								obj = { "docid": data[i].docid, "title": data[i].title, "title2": data[i].title2, "chnldocurl": data[i].chnldocurl, "chnl": data[i].chnl, "videourl": data[i].videourl }
								if (data[i].videourl != '') {
									getVideo(data[i].videourl);
								rellist.$vm.list_video.push(obj);
								rellist.$vm.isShow_sp = true;
								rellist.$vm.isright = true; //右侧展示
							}else if (data[i].extselect1 == 4) {  //媒体解读
								var obj = {};
								obj = { "docid": data[i].docid, "title": data[i].title, "title2": data[i].title2, "chnldocurl": data[i].chnldocurl, "chnl": data[i].chnl }
								rellist.$vm.list_mtjd.push(obj);
								rellist.$vm.isShow_mtjd = true;
								rellist.$vm.isright = true; //右侧展示
							}else if (data[i].extselect1 == 6||data[i].chnlid==41079||data[i].chnlid==58110||data[i].chnlid==41085||data[i].chnlid==21442||data[i].chnlid==21406) {  //相关报道
								var obj = {};
								obj = { "docid": data[i].docid, "title": data[i].title, "title2": data[i].docpeople, "chnldocurl": data[i].chnldocurl, "chnl": data[i].chnl }
								rellist.$vm.list_xgbd.push(obj); 
								rellist.$vm.isShow_xgbd = true;
								rellist.$vm.isright = true; //右侧展示
							}else if (data[i].extselect1 == 5) {  //新闻发布
								var obj = {};
								obj = { "docid": data[i].docid, "title": data[i].title, "title2": data[i].title2, "chnldocurl": data[i].chnldocurl, "chnl": data[i].chnl }
								rellist.$vm.list_xwfb.push(obj);
								rellist.$vm.isShow_xwfb = true;
								rellist.$vm.isright = true; //右侧展示
							}else if (data[i].chnlid==22331||data[i].chnlid==27771) {  //在线访谈
								var obj = {};
								obj = { "docid": data[i].docid, "title": data[i].title, "title2": data[i].title2, "chnldocurl": data[i].chnldocurl, "chnl": data[i].chnl }
								rellist.$vm.list_ft.push(obj);
								rellist.$vm.isShow_ft = true;
								rellist.$vm.isright = true; //右侧展示
							}else if (data[i].url.indexOf('/zfxxgk/szfwj/') > -1 ||data[i].chnlid==22119||data[i].chnlid==22118||data[i].chnlid==55768) {  //政策文件
								var obj = {};
								obj = { "docid": data[i].docid, "title": data[i].title, "title2": data[i].title2, "chnldocurl": data[i].chnldocurl, "chnl": data[i].chnl }
								rellist.$vm.list_zcwj.push(obj);
								rellist.$vm.isShow_zcwj = true;
								rellist.$vm.isright = true; //右侧展示
							}else if ( data[i].extselect1 == 7) {  
								var obj = {};
								obj = { "docid": data[i].docid, "title": data[i].title, "title2": data[i].title2, "chnldocurl": data[i].chnldocurl, "chnl": data[i].chnl }
								rellist.$vm.list_mtpl.push(obj);
								rellist.$vm.isShow_mtpl = true;
								rellist.$vm.isright = true; //右侧展示
								var obj = {};
								obj = { "docid": data[i].docid, "title": data[i].title, "title2": data[i].title2, "chnldocurl": data[i].chnldocurl, "chnl": data[i].chnl }
								rellist.$vm.list_Xglj.push(obj);
								rellist.$vm.isshow_Xglj = true; 
								rellist.$vm.isright = true;
							// $("#tpjd").css("background-image",'url('+avalon.vmodels.rellist.$model.list_img[0].img+')');
							// $("#xwfbh").css("background-image",'url('+ _xw_img +')');
						for (var i = 0; i < avalon.vmodels.rellist.list_img.length ; i++){
							$(".tpjds").eq(i).css("background-image", 'url(' + avalon.vmodels.rellist.list_img[i].img + ')');
			var host = '/masvod/public/';
			function getVideo(id) {
					url: "/was5/web/search?channelid=287418&classsql=videoid%3D" + id,
					dataType: "text",
					success: function (data) {
						var str = $.trim(data);
						str = str.replace(new RegExp("[\n\r]", "gm"), "");//过滤可能存在的换行
						var res = (new Function('return' + str))()
						for (var i = 0; i < res.docs.length - 1; i++) {
							_video_img = host + res.docs[i].subpath + res.docs[i].filename;
						$("#video").css("background-image", 'url(' + _video_img + ')');
					error: function (e) {
						avalon.log("请求失败")
			var hvFile = false;
			var hvOfd = false;
			for (var i = 0; i < fLinks.length; i++) {
				var ext = fLinks[i].link.substr(fLinks[i].link.lastIndexOf(".") + 1);
				if (ext == "ofd") {
					fLinks[i].filename = "文件下载";
					fLinks[i].filename = fNames[i].name;
				fLinks[i].typeN = ext;
			var fileDown = avalon.define({
				fileLink: fLinks,
                        if($(".xl_tit8 p").length>0){
                          $(".xl_tit8").css("text-align","inherit")
                         }
var qrcode = new QRCode(document.getElementById("qrcode"), {
			text: "http://www.fujian.gov.cn/hdjl/hdjlzsk/tjj/tjzs/zh/202307/t20230712_6204125.htm".replace(/http:\/\/[^\/]*\//im, window.location.protocol + "//" + window.location.hostname + "/"), //任意内容
			colorDark: "#000000",
			colorLight: "#ffffff",
			correctLevel: QRCode.CorrectLevel.H
 $(".StranLink").click(function(){
                let flag = localStorage.getItem("isfanti");
                const converter = OpenCC.Converter({ from: 'cn', to: 'tw' });
                const converter1 = OpenCC.Converter({ from: 'tw', to: 'cn' });
                const rootNode = document.documentElement;
                const HTMLConvertHandler = OpenCC.HTMLConverter(converter, rootNode, 'zh-CN', 'zh-TW');
                    if ($(".StranLink").eq(0).text() == "繁體版") {
                        localStorage.setItem('isfanti', 'true');
                        HTMLConvertHandler.convert(); // 开始转换  -> 漢語
                        $("input").each(function(i, v) {
                            if ($(this).attr("placeholder")) {
                                $(this).attr("placeholder", converter($(this).attr("placeholder")));
                            }
                        })
                        $(".StranLink").text("简体版");
                    } else if ($(".StranLink").eq(0).text() == "简体版") {
                        localStorage.setItem('isfanti', 'false');
                        HTMLConvertHandler.restore(); // 复原      -> 汉语
                        $("input").each(function(i, v) {
                            if ($(this).attr("placeholder")) {
                                $(this).attr("placeholder", converter1($(this).attr("placeholder")));
                            }
                        })
                        $(".StranLink").text("繁體版");
                    }
// $(document).ready(function() {
//if(localStorage.getItem("isfanti")=="true"){
//	 setTimeout(function() { $(".StranLink")[0].click();  },300)
	  require(["jquery"],function($){
		   var Weather = avalon.define({//天气
			   $id: "Weather",
		   var mon = new Date().getMonth() + 1;
		   var toDay = new Date().getFullYear() + "-" + (mon > 9 ? mon : '0' + mon) + "-" + new Date().getDate() + " " + new Date().getHours() + ":00:00" ;
		   var timestamp = new Date().getTime();//当前的时间戳
		   timestamp = timestamp + 12 * 60 * 60 * 1000;
		   var dateAfter = new Date(timestamp);
		   var mon1 = dateAfter.getMonth() + 1;
		   //this.setState((prevState) => ({stopTime: dateAfter.getFullYear() + '-' + (dateAfter.getMonth() + 1) + '-' + dateAfter.getDate() + ' ' + dateAfter.getHours() + ':' + dateAfter.getMinutes()}));
		   var toDay1 = dateAfter.getFullYear() + "-" + (mon > 9 ? mon : '0' + mon) + "-" + dateAfter.getDate() + " " + dateAfter.getHours() + ":00:00";
		   var weatherData = {"authorizationCode":"ba14d766ddf34b5b9b12481b36734a60","requiredItems":{"items":["MAX_TEMPERATURE_24","MIN_TEMPERATURE_24","WEATHER_PHENOMENA_24","WEATHER_DESCRIBE_24","WINDDIR_PHENOMENA_24","WINDDIR_DESCRIBE_24","WINDSPEED_PHENOMENA_24","WINDSPEED_DESCRIBE_24"]},"serviceCode":"d7a8c20d6ce34678ba6a0754271398c8","serviceCondition":{"firstResult":0,"item":{"SCANSTART_TIME":"","STATION_NO":"58847"},"maxResult":1}};
		   weatherData.serviceCondition.item.SCANSTART_TIME = toDay + '`!`'+ toDay1 ;
		   var v =  zqztc_encode(JSON.stringify(weatherData));
				   encryptType: "sm4",
				   callType:"OTHER",
			   dataType: "text",
			   contentType: "text/plain",
			   url: "/apigateway/fjqx/requestData",
			   success: function (data) {
				   var data = zqztc_decode(data);
				   avalon.log(data);
				   //var data = data.replace(/[\n\r\t]/g, '');
				   var json = JSON.parse(data)
				   var serviceResultList = json.serviceResultList;
				   //var dedata = JSON.parse( serviceResultList );
				   avalon.log(typeof serviceResultList[0].WEATHER_DESCRIBE_24);
				   Weather.tq = serviceResultList[0].WEATHER_DESCRIBE_24;
				   Weather.wd = serviceResultList[0].MIN_TEMPERATURE_24 + "℃~" + serviceResultList[0].MAX_TEMPERATURE_24 + "℃";
				   Weather.fx = serviceResultList[0].WINDDIR_DESCRIBE_24 + serviceResultList[0].WINDSPEED_DESCRIBE_24 + "级";
require(["jquery","tools"],function($,tools){
	 function WeatherPlayB(){
	   var B = {WEATHER_DESCRIBE_24:'',MIN_TEMPERATURE_24:'',MAX_TEMPERATURE_24:'',WINDDIR_DESCRIBE_24:''};
			   url: "/static/fztq.json",
			   dataType: "json",
			   success: function (msg) {
				   B.WEATHER_DESCRIBE_24 = (msg.results[0].daily[0].text_day == msg.results[0].daily[0].text_night) ? msg.results[0].daily[0].text_day : msg.results[0].daily[0].text_day + "转" + msg.results[0].daily[0].text_night;
				   B.MIN_TEMPERATURE_24 = msg.results[0].daily[0].low ;
				   B.MAX_TEMPERATURE_24 = msg.results[0].daily[0].high ;
				   B.WINDDIR_DESCRIBE_24 = msg.results[0].daily[0].wind_direction;
		   var Weather = avalon.define({    //天气
			   $id: "Weather",
			   url: "/static/fjqx.json",
			   dataType: "json",
			   success: function (data) {
				   avalon.log(  data );
	 W = data.serviceResultList[0];
	  W = WeatherPlayB();
				   Weather.tq = W.WEATHER_DESCRIBE_24;
				   Weather.wd = W.MIN_TEMPERATURE_24 + "℃~" + W.MAX_TEMPERATURE_24 + "℃";
				   Weather.fx = W.WINDDIR_DESCRIBE_24 + W.WINDSPEED_DESCRIBE_24 + "级";
			   },error: function (data) {
				  var W = WeatherPlayB();
				   Weather.tq = W.WEATHER_DESCRIBE_24;
				   Weather.wd = W.MIN_TEMPERATURE_24 + "℃~" + W.MAX_TEMPERATURE_24 + "℃";
				   Weather.fx = W.WINDDIR_DESCRIBE_24 + W.WINDSPEED_DESCRIBE_24 + "级";
   $('#imgConac').attr('alt','党政机关');
   $('#imgConac').css('width','70px');
   $('#_span_jiucuo img').attr('alt','链接政府网站找错');
   $('#_span_jiucuo img').css('width','90px');
		   /*滚动条滚到一部分菜单固定浮动在顶部*/
		   $(window).scroll(function() {
			   //$(this).scrollTop() > 435 ? $(".nav").addClass('fixed') : $(".nav").removeClass('fixed');
			   if($(this).scrollTop()>435){
				   $("#header .container").css("height","211");
				   $("#header .nav").addClass('fixed')
			   if($(this).scrollTop()<435){
				   $("#header .container").css("height","auto");
				   $("#header .nav").removeClass('fixed')
			   var ua = navigator.userAgent.toLowerCase();
			   if(ua.match(/iPad/i)=="ipad" || ua.match(/Mobile/i)=="mobile") {
				   //$(this).scrollTop() > 275 ? $("#top-link").addClass('fixed') : $("#top-link").removeClass('fixed');
				   $(this).scrollTop() > 275 ? function() {
					   if(typeof window.bf !== undefined && window.bf.state !== 1) {
						   $("#top-link").addClass('fixed')
				   } : $("#top-link").removeClass('fixed');
		   var w = $(window).innerWidth();
			   //手机版菜单隐藏，点击出现侧边菜单栏
			   $('.header-menu-mb').click(function(){
				   if(!$(this).hasClass('show-nav')){
					   $(this).addClass('show-nav');
					   $('.nav').stop(true,true).animate({"right":0},500)
					   $(this).removeClass('show-nav');
					   $('.nav').stop(true,true).animate({"right":"-50%"},500)
			   //手机版菜单隐藏，点击出现侧边菜单栏
			   $('.pho-menu_2021').click(function(){
				   $('.pho-fix_2021').show()
			   $('.close_2021').click(function(){
				   $('.pho-fix_2021').hide()
			   //手机版菜单隐藏，点击出现侧边菜单栏
			   $('.pho-menu-engl').click(function(){
				   $('.nav-eng').show()
			   $('.menu-close-eng').click(function(){
				   $('.nav-eng').hide()
		   var HisString = typeof window.localStorage == 'unknown' ? '' : window.localStorage.getItem("HistoryList"),
		   if(HisString != null){HisArr = HisString.split(",")};
		   var Rs = avalon.define({
			   historyKeys: HisArr,
			   showlist:false,
			   LockLink:false,
			   setPost:function(key,e){
						 e.preventDefault();
						 avalon.log("e.preventDefault")
				 $("#header form #selecttags").val(key);
				   $("#header form")[0].submit();
				   Rs.LockLink = false;
				   Rs.showlist = false;
				   avalon.log("submit")
   window.event.returnValue = false;
			   $getLinkKeys:function(val){ //获取联想词
					   url:"/ssp/search/api/word",
					   method:"post",
						   keyWord: val,
						   siteId : "ff808081624641aa0162476c0e0e0055"
					   success:function(res){
						   function json(data){
							   var str = $.trim(data);
							   str = str.replace(new RegExp("[\n\r]","gm"),"");//过滤可能存在的换行
							   var s = (new Function('return'+str))();
						   data = json(res);
						   Rs.error = data.error;
						   Rs.linkKeys = data.datas;
					   error:function(err){
						   // avalon.log(err);
						   Rs.error = err;
			   $togLinkkey:function(){
				   Rs.showlist = false;
			   $checkLock:function(){
				   Rs.LockLink = true;
				cannelBox:function(){
				   Rs.showlist = false;
		   Rs.$watch("serKey",function(newVal,oldVal){
			   if(newVal==""){
				   Rs.showlist = false;
				   // Rs.LockLink = false;
				   Rs.$getLinkKeys(newVal);
				   Rs.showlist = true;
		   function pushkeys(dataArr){
			   for( var i = 0 ; i < dataArr.length; i++ ){
					   keyword : dataArr[i],
					   surl: "/ssp/main/index.html?siteId=ff808081624641aa0162476c0e0e0055&key=" + encodeURIComponent(dataArr[i])
				   Rs.keys.push(item);
		   var defKeywords = ["国家公园","垃圾分类","水质","排行榜","社保"];
		   var hotKeyArr = [];
		   var keystr = "公司设立,个体工商户,社保,公积金,医保";
		   hotKeyArr = keystr.split(",");
		   if(hotKeyArr !== undefined && hotKeyArr.length>0 ){defKeywords = hotKeyArr};
		   pushkeys(defKeywords);
		   var clickOdd = false;
		   $('.picker_2021').on("click",function(){
			   var showInput =  $('.picker_2021 input');
			   var soTab = $('.ss_box2_2021 #tab');
			//    var siteId =  $('.ss_box2_2021 #siteId');
			//    var isMain =  $('.ss_box2_2021 #isMain');
				   showInput.val('本站');
				//    siteId.val('ff808081624641aa0162476c0e0e0055');
				//    isMain.val('');
					soTab.val('bz');
				   clickOdd = false
				   if(typeof window.bf != undefined) {
					   window.bf.text = '您已切换本站搜索'
					   window.bf.work()
				   showInput.val('全省');
				//    siteId.val('ff808081624641aa0162476c0e0e0055');
				//    isMain.val('1');
				   soTab.val('all');
				   clickOdd = true
				   window.bf.text = '您已切换站群搜索'
				   window.bf.work()
		   $('.picker_2021 .upDown_2021').focus(function(e) {
			   $(this).bind('keydown.wza_tab', function(e) {
				   if(!e.ctrlKey && !e.altKey && !e.shiftKey && (e.keyCode === 38 || e.keyCode === 40)) {
					   $(this).parent('.picker_2021').trigger('click')
		   $('.picker_2021 .upDown_2021').blur(function() {
				   $(this).unbind('keydown.wza_tab')
		   $.fn.selectCity = function(targetId) {
			   var _seft = this;
			   var targetId = $(targetId);
			   this.click(function(){
				   var A_top = $(this).offset().top + $(this).outerHeight(true);  //  1
				   var A_left =  $(this).offset().left;
				   targetId.show();
			   targetId.find("#tagClose").click(function(){
				   targetId.hide();
			   $(document).click(function(event){
				   if(event.target.id!=_seft.selector.substring(1)){
					   targetId.hide();
			   targetId.click(function(e){
				   e.stopPropagation(); //  2
			   $("#selecttags").selectCity("#searchTag");
		   avalon.scan($(".ss_box2").get(0));
		   $('.yqlj_tit1_2021>ul>li').click(function(){
			   if ($(this).hasClass('on')) {
				   $(this).addClass('on').siblings().removeClass('on');
				   $(this).siblings().children('.yqlj_con1_2021').slideUp()
				   $(this).children('.yqlj_con1_2021').slideDown();
			   return false;   //阻止冒泡
		   $(document).click(function(){
			   $('.yqlj_con1_2021').slideUp()
			   $('.yqlj_tit1_2021>ul>li').removeClass('on');
		   $(".fhdb").on("click",function(){
			   $('body,html').animate({ scrollTop: 0 }, 800);
				  $('.nav_2021 li').hover(function() {
			   if($(window).scrollTop() > 275) return;
			   $(this).addClass('active_2021_temp').find('.nav_con_2021').show()
			   $(this).find('.nav_con1_2021').show()
			   if($(window).scrollTop() > 275) return;
			   $(this).removeClass('active_2021_temp').find('.nav_con_2021').hide()
			   $(this).find('.nav_con1_2021').hide()
		   $('.nav_2021 li').each(function(index) {
			   var $this = $(this)
			   var $liA = $(this).children('a');
			   var $liWrap = $(this).find('.nav_warp_2021');
			   $liA.focus(function(e) {
				   $('.nav_2021 li').eq(index - 1).trigger('mouseleave')
				   e = $this.trigger('mouseover')
				   if($liWrap.length > 0) {
					   $(this).bind('keydown.wza_tab', function (e) {
						   if (!e.ctrlKey && !e.altKey && e.keyCode === 9) {
							   //$liWrap.find('a:eq(0)').focus()
							   if($liWrap.find('b').length > 0) {
								   $liWrap.find('b:eq(0)').focus()
								   $liWrap.find('a:eq(0)').focus()
							   return false
			   $liA.blur(function() {
				   $(this).unbind('keydown.wza_tab')
			   var $curA = $liWrap.find('a')
			   var endTabLen = $curA.length - 1;
			   $curA.eq(endTabLen).focus(function () {
				   $(this).bind('keydown.wza_tab_leave', function (e) {
					   if (!e.ctrlKey && !e.altKey && e.keyCode === 9) {
						   $('.nav_2021 li').eq(index + 1).children('a').focus()
						   return false;
			   $curA.eq(endTabLen).blur(function() {
				   $(this).unbind('keydown.wza_tab_leave')
		   var $yqljLi = $('.yqlj_tit1_2021').children('ul').children('li')
		   $yqljLi.children('p').attr('tabindex', 0)
		   $yqljLi.children('p').click(function(e) {
		   e.stopPropagation()
		   $yqljLi.each(function(index) {
			   var $this = $(this)
			   var $yqljP = $this.children('p')
			   var $link = $this.find('li')
			   var endTabLen = $link.length - 1;
			   $yqljP.focus(function(e) {
				   e = $this.click()
				   if($link.length > 0) {
					   $(this).bind('keydown.wza_tab', function (e) {
						   if (!e.ctrlKey && !e.altKey && e.keyCode === 9) {
							   $link.eq(0).find('a:eq(0)').focus()
							   return false
			   $link.eq(endTabLen).focus(function() {
				   $(this).bind('keydown.wza_tab_leave', function (e) {
					   if (!e.ctrlKey && !e.altKey && e.keyCode === 9) {
						   if(index != $yqljLi.length - 1) {
							   $yqljLi.eq(index + 1).children('p').focus()
							   $this.removeClass('on').children('.yqlj_con1_2021').slideUp()
						   return false;
   $('input[type="checkbox"]').each(function() {
		   var $this = $(this)
		   $this.focus(function(e) {
			   $this.bind('keydown.wza', function(e) {
				   if(!e.ctrlKey && !e.altKey && !e.shiftKey && e.keyCode == 13) {
					   //$this.attr('checked', true)
   $this.prop('checked', true)
		   $this.blur(function(){
			   $this.unbind('keydown.wza')
		   setTimeout(function() {
			   tabWza('#tabFjyy')
			   //tabWza3($('#tabFjyy .wza-list:last li:last a:eq(0)'), $('#tabFjyy .sp_btn a:eq(0)'))
			   //tabWza3($('#tabFjyy .wza-list:last li:last a:eq(0)'), $('#tabFjyy .sp_btn a:eq(0)'))
			  // tabWza3($('#tabFjyy .yqfk-icon'), $('#tabFjyy .xdgdd:eq(1)'))
		   setTimeout(function(){
			   tabWza('#zw_tab_sjkf', '.bd .inner_con')
			   $('#zw_tab_sjkf .hd li:eq(0) a:eq(0)').focus(function(e) {
				   e = $(this).mouseover()
				   $(this).bind('keydown.wza_tab', function(e) {
					   if(!e.ctrlKey && !e.altKey && e.keyCode === 9) {
						   $('#searchAllKey').focus()
						   return false;
		   tabWza('#tabGrbs')
		   tabWza('#tabZtjc')
		   tabWza('#tabZxwj', '.bd .sec-list-box')
		   tabWza('#tabZctj')
	   var _cookie_BFree = $.cookie("BFree") || "";//获取适老版的cookie
	   window._cookie_BFree = _cookie_BFree;
		   window.bf = new BarrierFree({
			   callback: function(state) {
				   // state 1: 无障碍
				   // state 2: 适老版
				   if(state == 1) {
					   $(".sjdwzz-a").hide();
				   }else if(state == 2) {
				   $("html").addClass("slb");
					   tabWza3($('#tabGrbs .hd li a:eq(0)'), $('#tabGrbs .grbs-box1 li a:eq(0)'), 'wza_tab')
					   tabWza3($('#tabGrbs .grbs-box1 li a:last'), $('#tabGrbs .hd li:eq(1) a:eq(0)'))
					   tabWza3($('#tabZtjc .hd li a:eq(0)'), $('#tabZtjc .bmfw-list1 li a:eq(0)'), 'wza_tab')
					   tabWza3($('#tabZtjc .bmfw-list1 li a:last'), $('#tabZtjc .hd li:eq(1) a:eq(0)'))
					   $(".sjdwzz-a").hide();
					   var oldModeMenu="";
					   oldModeMenu += "<div class=\"rig-fix2 bf-pass\" id=\"oldOtherEvent\"  style=\"display:none;\"><ul class=\"wxmz wxmz1\" ><li event-type=\"zoomIn\"><a href=\"javascript:;\" ><span class=\"slb-rig01\"><\/span><p>页面放大<\/p><\/a><\/li><li event-type=\"zoomOut\"><a href=\"javascript:;\" >";
					   oldModeMenu += "            <span class=\"slb-rig02\"><\/span>";
					   oldModeMenu += "            <p>页面缩小<\/p>";
					   oldModeMenu += "        <\/a><\/li>";
					   oldModeMenu += "        <li event-type=\"caption\"><a href=\"javascript:;\" >";
					   oldModeMenu += "            <span class=\"slb-rig03\"><\/span>";
					   oldModeMenu += "            <p>显示屏<\/p>";
					   oldModeMenu += "        <\/a><\/li>";
					   oldModeMenu += "        <li event-type=\"readThrough\"><a href=\"javascript:;\" >";
					   oldModeMenu += "            <span class=\"slb-rig04\"><\/span>";
					   oldModeMenu += "            <p>阅读方式<\/p>";
					   oldModeMenu += "        <\/a><\/li>";
					   oldModeMenu += "        <li event-type=\"close\"><a href=\"javascript:;\">";
					   oldModeMenu += "            <span class=\"slb-rig05\"><\/span>";
					   oldModeMenu += "            <p>退出<\/p>";
					   oldModeMenu += "        <\/a><\/li>";
					   oldModeMenu += "        <li><a href=\"/zzb/\">";
					   oldModeMenu += "            <span class=\"slb-rig06\"><\/span>";
					   oldModeMenu += "            <p>长者专题<\/p>";
					   oldModeMenu += "        <\/a><\/li>";
					   oldModeMenu += "    <\/ul>";
					   oldModeMenu += "<\/div>";
					   oldModeMenu += "";
					   $(document.body).append($(oldModeMenu))
					   $("#oldOtherEvent li").on('click', function(e) {
						   e.stopPropagation()
						   if(!bf) return;
						   var eventType = $(this).attr("event-type")
						   if(eventType == 'close') {
						   bf.register(eventType)
					   $(window).scroll(function() {
						   $(this).scrollTop() > 126 ? $('.rig-fix2').show() : $('.rig-fix2').hide();
					   dynamicLoadCss("/images/20210616-nav-slh.css");
					   dynamicLoadCss("/images/20211113-szf-style-slh.css");  //改版新增20211116
					   dynamicLoadCss("/images/20220413-szf-yqfk-slh.css"); // 改版新增20220413
					   if( window.location.href.indexOf("/zzb/") >-1 || window.location.href.indexOf("/zzb.htm") > -1 || window.location.href.indexOf("/zzb_") >-1 ){//适老版专题
						   //dynamicLoadCss("/images/20210616-qt-slh.css");
						   dynamicLoadCss("/images/20210616-qt-slh.css");
					   if( window.location.href.indexOf("/zwgk/ztzl/sczl/") > -1 || window.location.href.indexOf("/zwgk/ztzl/qmtjjczwgkbzhgfhgz/") > -1 || window.location.href.indexOf("/zwgk/ztzl/flwzwfw/") > -1 || window.location.href.indexOf("/zwgk/ztzl/ydxxfy/") > -1|| window.location.href.indexOf("/zwgk/ztzl/lwlb/") > -1){//21世纪海上丝绸之路核心区，福建省全面推进基层政务公开标准化规范化工作，聚焦福建互联网+政务服务，统筹推进疫情防控和经济社会发展，做好“六稳”工作  落实“六保”任务
						   dynamicLoadCss("/images/20210726-slbzt-yqfk.css")
					   }else if( window.location.href.indexOf("/zwgk/ztzl/qszfwzndbb/") > -1 || window.location.href.indexOf("/zwgk/ztzl/zdjsxm/") > -1 ){//福建省全省政府网站年度报表，重点建设项目
						   dynamicLoadCss("/images/20210726-slbzt-zdjs.css")
					   }else if( window.location.href.indexOf("/zwgk/ztzl/zfwzjsygl/") > -1 ){//政府网站与政务新媒体建设
						   dynamicLoadCss("/images/20210726-slbzt-zwxmt.css")
					   }else if( window.location.href.indexOf("/zwgk/ztzl/gjcjgxgg/") > -1 ){//奋力谱写全面建设社会主义现代化国家的福建篇章
						   dynamicLoadCss("/images/20210726-slbzt.css")
					   }else if( window.location.href.indexOf("/zwgk/ztzl/yqfk/") > -1 ){//疫情防控 福建在行动
						   //dynamicLoadCss("/images/202108012-szfyq-slh.css")
						   dynamicLoadCss("/images/20210915-yqzt-slh.css")
					   }else if( window.location.href.indexOf("/bsfw/mztapp/") > -1 ){//闽政通APP
						   dynamicLoadCss("/images/20210722-mzt.css")
					   }else if( window.location.href.indexOf("/bsfw/bmfw/") > -1 ){//便民服务
						   dynamicLoadCss("/images/20210727-bmfw-slh.css")
					   }else if( window.location.href.indexOf("/english/") > -1 ){//英文版
						   dynamicLoadCss("/images/20210730-ywb-slh.css")
					   }else if( window.location.href.indexOf("/zwgk/zcwjk/") > -1 ){//政策文件库
						   dynamicLoadCss("/images/20210917-zcwjk-slh.css")
					   }else if( window.location.href.indexOf("/zwgk/ztzl/hqzczq/") > -1 ){//惠企政策专区
						   dynamicLoadCss("/images/21fjs_hqzc_zzms_style.css")
					   }else if( window.location.href.indexOf("/zwgk/ztzl/zfjg/") > -1 || window.location.href.indexOf("/zwgk/ztzl/sswfjzhxlt/") > -1 ){//执法监管，“十四五”规划
						   dynamicLoadCss("/images/20210827-jgzf-slh.css")
					   }else if( window.location.href.indexOf("/zwgk/ztzl/11ddh/") > -1 || window.location.href.indexOf("/zwgk/ztzl/sjjlzqh/") > -1 ){//党代会，十九届六中全会
						   dynamicLoadCss("/images/20211207-ztzt-slh.css")
					   }else if( window.location.href.indexOf("/zwgk/zfxxgk/zfxxgkzc/") > -1 ){//规章库
						   dynamicLoadCss("/images/20211111-gzk-slh.css")
					   }else if( window.location.href.indexOf("/zwgk/ztzl/2022wmbss/") > -1 ){//2020年为民办实事
						   dynamicLoadCss("/images/202201-fjszf-slb.css")
					   }else if( window.location.href.indexOf("/zwgk/ztzl/tgxltsxntzxy/") > -1 ){//提高效率、提升效能、提增效益
						   dynamicLoadCss("/images/20220215-tgzt-slh.css")
					   }else if( window.location.href.indexOf("/zwgk/zfxxgk/szfwj/jgzz/xzgfxwj/") > -1 ){//行政规范性文件
						   dynamicLoadCss("/images/20211111-gzk-slh.css")
					   }else if( window.location.href.indexOf("/gnfblm/") > -1 ){//行政规范性文件测试地址
						   dynamicLoadCss("/images/20211111-gzk-slh.css")
				   avalon.log($.cookie("BFree"))
					   link: '/bz/fjszf-wza.url',
					   tip: '福建省人民政府门户网站_无障碍浏览.url'
					   link: '/bz/fjszf-slb.url',
					   tip: '福建省人民政府门户网站_长者模式.url'
			* bf.register 包含4中方法 zoomIn放大，zoomOut缩小，caption显示屏，连读readThrough
			* bf.change(2) 打开适老版本
			* bf.change(1) 打开无障碍版本
		   $("#close").click(function() {
			   bf.register('readThrough')
		   var WinSlb = avalon.define({
			   inSlb:function(){
	 //if (!/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent)) {
				   WinSlb.isSlb = true;
				   if ($.isFunction(window.slide)){
					   window.location.reload();
				   $(".pho-search_2021 input[name='key']").removeAttr("required");
				   $(".zzbs_2021 .slb").hide();
				   $(".zzbs_2021 .outslb").css("display","inline-block");
				   $(".sjdwzz-a").hide();
   //window.open("/zzb/"); 
			   outSlb:function(){
				   avalon.log("退出");
				   WinSlb.isSlb = false;
				   window.location.reload();
				   $(".zzbs_2021 .outslb").hide();
				   $(".zzbs_2021 .slb").css("display","inline-block");
			   inWza:function(){
				   $(".sjdwzz-a").hide();
		   if($.cookie("BFree")=='2'){  //判断是否是适老版状态
			   //WinSlb.inSlb();
			   WinSlb.isSlb = true;
			   if ($.isFunction(window.slide)){
			   $('.bsfw-wsbs').attr('href','https://zwfw.fujian.gov.cn/oldman?from=1')
			   $(".pho-search_2021 input[name='key']").removeAttr("required");
			   $(".zzbs_2021 .slb").hide();
		   }else if($.cookie("BFree")=='1'){
			   $(".zzbs_2021 .outslb").hide();
			   $(".zzbs_2021 .slb").css("display","inline-block");
			   $('.bsfw-wsbs').attr('href','https://zwfw.fujian.gov.cn/Helpdisabled')
			   $(".zzbs_2021 .outslb").hide();
		   /*if( window.location.href.indexOf("/zzb/") > -1 ){//适老版专题
			   WinSlb.isSlb = true;
			   if ($.isFunction(window.slide)){
			   $(".pho-search_2021 input[name='key']").removeAttr("required");
			   $(".zzbs_2021 .slb").hide();
			   $(".zzbs_2021 .outslb").css("display","inline-block");
		   $(window.document).scroll(function () {
			   var scrolltop = $(document).scrollTop() || document.documentElement.scrollTop;
			   WinSlb.isSlb_L =  ( scrolltop > 126 && $.cookie("BFree")=='2') ? true : false;
	   var webSearchOn = false;
	   var title = "人社福精灵乐保为您服务"
	   var unknow = "对不起，我还无法识别您的问题，您可以换一种问法进行咨询。";
	   var robotName = '智能机器人';
	   var overtime = "您已经很久没有提问了。";
	   var solve = "感谢您的肯定，我会继续努力的! ";
	   var unsolve = "很抱歉！没有能够帮到您。 您的问题我还没有学会但我已经记下啦，稍后我会尽快学习补充哦！ " +
		   "或者您还可以通过点击“<a href='http://12345.fujian.gov.cn/' target='_blank' style='text-decoration: underline;color: blue'>福建省12345平台</a>”进行咨询，后续会有业务部门人员进行回复哦。";
	   var outTime = 300;
	   //用户无响应时间定时器 用于超期退出
	   var status = 200;
	   var siteUrl = "";
	   var voiceRefMap = new Map();
	   var secs = 20; //倒计时
	   var switch2Text = false;
	   var timeliness = 0;//响应及时性
	   var accuracy = 0;//答案准确性
	   var experience = 0;//互动体验性
	   var knowledgeNum = 0;//业务知识量
	   var suggest = "";//意见
	   var userLastInput = "";
	   var isRstSearch = false;
	   var evaluateShow = true;//是否显示评价
	   // var listener = new window.keypress.Listener();
	   var queTemplate = Handlebars.compile($("#que_template").html());
	   var keyStatus = 0 //点击按键状态 1代表上键 2代表下键
	   var userOldInput = "";
	   $('#start_record').on('click', function () {
		   $("#answerList").html(" ")
		   if(window.bf && window.bf._voice) {
			   if(window.bf._voice.closeState == 0) {
				   window.bf._voice.closeState = 1;
				   window.bf.getState('voiceClose', 1)
	   $('#stop_record').on('click', function () {
		   $(".h-start1").hide()
		   if(window.bf && window.bf._voice) {
			   if(window.bf._voice.closeState == 1) {
				   window.bf._voice.closeState = 0;
				   window.bf.getState('voiceClose', 0)
	   function onmouseover_(obj, e) {
			   $(obj).css('color', 'blue');
			   $(obj.childNodes[0]).attr('src', 'img/ishelp1.png');
			   $(obj).css('color', 'black');
			   $(obj.childNodes[0]).attr('src', 'img/ishelp3.png');
	   function onmouseout_(obj, e) {
			   $(obj).css('color', '#999');
			   $(obj.childNodes[0]).attr('src', 'img/ishelp.png');
			   $(obj).css('color', '#999');
			   $(obj.childNodes[0]).attr('src', 'img/ishelp2.png');
	   function timecount() {
		   $('#lisening').html("（点击结束 " + secs + " 秒）");
		   secs = secs - 1;
			   clearTimeout(param);
		   param = setTimeout(function () {
	   var btnStartRecording = document.getElementById('start_record');
	   var btnStopRecording = document.getElementById('stop_record');
	   var isEdge = navigator.userAgent.indexOf('Edge') !== -1 && (!!navigator.msSaveOrOpenBlob || !!navigator.msSaveBlob);
	   var isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
	   var recorder; // globally accessible
	   function startRec() {
		   $("#voice-label").html("<span style=\"color: red;font-weight: bold\">（录音功能正在准备中）</span>")
		   $("audio").each(function (index, item) {
			   $(this)[0].pause();
		   $('.yy_btn_close').each(function (index, item) {
			   $(this).removeClass().addClass("yy_btn");
		   clearTimeout(param);
		   if (!microphone) {
			   captureMicrophone(function (mic) {   //捕获传声器captureMicrophone
				   microphone = mic;
				   click(btnStartRecording);
			   numberOfAudioChannels: 1,
			   checkForInactiveTracks: false,
			   bufferSize: 4096,
			   recorderType: StereoAudioRecorder
			   recorder.destroy();
			   recorder = null;
		   $('.h-start2').hide();
		   $('.h-close').show();
		   $("#voice-label").text("（点击开始说话）");
		   recorder = RecordRTC(microphone, options);
		   recorder.startRecording();
	   function stopRecordingCallback() {
		   if (!switch2Text) {
			   var internalRecorder = recorder.getInternalRecorder();
			   var leftchannel = internalRecorder.leftchannel;
			   var rightchannel = internalRecorder.rightchannel;
			   // ------------------------------------------------------------
			   mergeLeftRightBuffers({
				   desiredSampRate: 8000,
				   sampleRate: internalRecorder.sampleRate,
				   numberOfAudioChannels: internalRecorder.numberOfAudioChannels,
				   internalInterleavedLength: internalRecorder.recordingLength,
				   leftBuffers: leftchannel,
				   rightBuffers: internalRecorder.numberOfAudioChannels === 1 ? [] : rightchannel
			   }, function (buffer, view) {
				   var wavBlob = new Blob([buffer], {
					   type: 'audio/wav'
				   var blob = changeFormat(buffer);
				   clearTimeout(param);
				   var form = new FormData();
				   var filleName = rondomNum(true, 32, 32);
				   form.append("siteId", siteId);
				   form.append("random", rondom);
				   form.append("upfile", blob, filleName + ".mp3"); //和普通form表单并无二致，后端接收到upfile参数的文件，文件名为recorder.mp3
				   var audio = document.createElement("audio");
				   audio.id = filleName + "_";
				   audio.style.cssFloat = "right";
				   audio.style.height = "32px";
				   audio.controls = true;
				   audio.style.display = "none";
				   audio.src = URL.createObjectURL(wavBlob);
				   $("#answerList").append(audio);
				   var values = {value: filleName, isvoice: true};
				   appendHtml(queTemplate(values), 3);
				   // audio.play();
				   isVoiceAsk = true;
					   type: 'POST',
					   url: "/isr/public/talk/uploadMp3?siteId=2c9bc5436f844983016f84499f1b00ac",
					   cache: false,
					   processData: false,
					   contentType: false,
					   success: function (ret) {
						   //console.log("resresres",ret);
						   //console.log(importVoice,"2202");
						   var json = $.parseJSON(ret);
						   $("#" + json.id).val(json.result);
						   if (json.qastatus == 1) {
							   qaResult(json.result);
				   count = outTime;
			   switch2Text = false;
		   // if (isSafari) {
		   //     click(btnReleaseMicrophone);
	   function uploadRec() {
		   $('.h-start2').show();
		   $('.h-close').hide();
		   recorder.stopRecording(stopRecordingCallback);
	   function isSolve(e) {
				   'Accept': 'application/json',
				   'Content-Type': 'application/json'
			   url: isSolveSessionUrl,
			   data: "{\"status\":\"" + status + "\",\"rondom\":\"" + rondom + "\"}",
			   success: function (result) {
	   function captureMicrophone(callback) {
		   // btnReleaseMicrophone.disabled = false;
		   if (microphone) {
			   callback(microphone);
		   if (typeof navigator.mediaDevices === 'undefined' || !navigator.mediaDevices.getUserMedia) {
			   $('.h-start2').show();
			   $('.h-close').hide();
			   var sys = isAndroidOrIOSOrPc();
			   if (sys == "pc") {
				   alert("该浏览器不支持语音录入，<br>请使用谷歌、火狐等主流浏览器。", {area: ['320px', '80px']});
			   } else if (sys == "ios") {
				   alert("该浏览器不支持语音录入，<br>请将您的IOS操作系统升级最新版本，并使用safari浏览器打开使用。", {area: ['320px', '80px']});
			   } else if (sys == "android") {
				   alert("该浏览器不支持语音录入，<br>请使用系统自带浏览器打开使用。", {area: ['320px', '80px']});
				   alert("您当前的操作系统不支持语音录入。", {area: ['300px', '50px']});
		   navigator.mediaDevices.getUserMedia(
				   audio: isEdge ? true : {echoCancellation: false}
		   ).then(function (mic) {
		   }).catch(function (error) {
			   alert("该浏览器不支持语音录入。<br>或您拒绝了语音授权", {area: ['300px', '60px']});
			   $("#voice-label").html("（点击开始说话）");
			   $('.h-start2').show();
			   $('.h-close').hide();
		* randomWord 产生任意长度随机字母数字组合
		* randomFlag-是否任意长度 min-任意长度最小位[固定位数] max-任意长度最大位
		* @param randomFlag
		* @returns {string}
	   function rondomNum(randomFlag, min, max) {
			   arr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 'r', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'U', 'V', 'W', 'X', 'Y', 'Z'];
		   if (randomFlag) {
			   range = Math.round(Math.random() * (max - min)) + min;
		   for (var i = 0; i < range; i++) {
			   pos = Math.round(Math.random() * (arr.length - 1));
			   str += arr[pos];
	   function qaResult(keyWord) {
		   $("#completionQues").html("");
		   $("#tc_Big_box").hide();
		   $("#keyLength").html(50);
		   var url = answerUrl;
		   if (isRstSearch) {
			   url = rstAnswerUrl;
				   'Accept': 'application/json',
				   'Content-Type': 'application/json'
			   dataType: "json",
			   data: "{\"question\":\"" + keyWord + "\",\"siteId\":\"" + siteId + "\",\"rondom\":\"" + rondom + "\"}",
			   success: function (data) {
				   // console.log(data);
				   if (!data.error) {
					   if (data.msg) {
						   appendHtml(data.msg, 1);
					   recordId = data.recordId;
					   var isVoice = data.isVoice;
					   var match = data.result.matchQuestions;
					   var like = data.result.likeQuestions;
					   var fill = data.result.fillQuestions;
					   var filter = data.result.filterList;
					   var ischat = data.result.ischat;
					   var rstdata = data.result.rstdata;
					   parseData(match, like, filter, ischat, keyWord, rstdata, isVoice);
			   //请求失败，包含具体的错误信息
			   error: function (e) {
				   if (e.responseText.indexOf('script') > 0) {
					   var msg = e.responseText.substring(e.responseText.indexOf('\'') + 1, e.responseText.lastIndexOf('\''));
					   appendHtml(msg, 1)
		   $("#keyWord").val("");
	   function parseData(match, like, filter, ischat, textValue, rstdata, isVoice) {
		   voiceBtnId = genUuid();
		   isRstData = false;
		   $("#tc_Big_box").hide();
		   if (rstdata && rstdata.list.length > 0) {
				   robotName: robotName,
				   img: robotImage,
				   rstdata: rstdata,
				   unknow: unknow,
				   isWebSearch: isRstSearch,
				   voiceBtnId: voiceBtnId
			   appendHtml(rstTemplate(values), 8);
			   var smartAnswer = rstDataAddIndex(rstdata.list);
			   smartList = getSmartList(smartAnswer);
			   isRstData = true;
			   isRstSearch = false;
			   playVoiceAsk();
		   if (filter && filter.length > 0) {
			   var words = "";
			   jQuery.each(filter, function (i, item) {
				   var obj = JSON.parse(item);
				   words += obj.word + ",";
			   if (words.length > 0)
				   words = words.substring(0, words.length - 1);
				   robotName: robotName,
				   isVoice: isVoice,
				   img: robotImage,
				   voiceBtnId: voiceBtnId
			   appendHtml(othTemplate(values), 8);
			   playVoiceAsk();
			   if (match.length > 0 && ischat == 1) {
				   var values = {
					   question: match[0].question,
					   robotName: robotName,
					   img: robotImage,
					   answer: match[0].answer,
					   answerId: match[0].answerId,
					   uuid: match[0].uuid,
					   index: ++num,
					   voiceTime: match[0].voiceTime,
					   isVoice: isVoice,
					   isTime: match[0].voiceTime === "undefined" || match[0].voiceTime === "" ? false : true,
					   iswenda: true,
					   voiceBtnId: voiceBtnId
				   appendHtml(ansTemplate(values), 8);
			   } else if (match.length > 0 && ischat == 0) {
				   var values = {
					   robotName: robotName,
					   img: robotImage,
					   answer: match[0].answer,
					   answerId: match[0].answerId,
					   uuid: match[0].uuid,
					   isVoice: isVoice,
					   index: ++num,
					   voiceTime: match[0].voiceTime,
					   isTime: match[0].voiceTime === "undefined" || match[0].voiceTime === "" ? false : true,
					   iswenda: false,
					   voiceBtnId: voiceBtnId
				   appendHtml(ansTemplate(values), 8);
			   } else if (match.length == 0 && like.length > 0) {
				   if (textValue != null) {
					   if (textValue == like[0].question.replace(/\n/g, "")) {
						   //   console.log("匹配第一条:" + like[0].question.replace(/\n/g, ""));
						   var values = {
							   robotName: robotName,
							   img: robotImage,
							   answer: like[0].answer,
							   uuid: like[0].uuid,
							   index: ++num,
							   isVoice: isVoice,
							   iswenda: true,
							   voiceBtnId: voiceBtnId,
						   appendHtml(ansTemplate(values), 8);
						   playVoiceAsk();
				   var values = {
					   robotName: robotName,
					   img: robotImage,
					   keyWord: textValue,
					   isVoice: isVoice,
					   index: ++num,
					   voiceBtnId: voiceBtnId,
					   webSearch: webSearchOn
				   var smartAnswer = likeDataAddIndex(like);
				   smartList = getSmartList(smartAnswer);
				   appendHtml(simTemplate(values), 8);
			   } else if (match.length == 0 && like.length == 0) {
				   var values = {
					   robotName: robotName,
					   isVoice: isVoice,
					   isUnknow: true,
					   unknow: unknow,
					   index: ++num,
					   img: robotImage,
					   voiceBtnId: voiceBtnId
				   appendHtml(othTemplate(values), 8);
				   robotName: robotName,
				   isVoice: isVoice,
				   isUnknow: true,
				   unknow: "真的非常抱歉，从网站也未能检索到您需要的信息，您可以前往<a href=\"http://12345.fujian.gov.cn/\" target=\"_blank\">福建省12345平台</a>进行咨询。",
				   img: robotImage,
				   voiceBtnId: voiceBtnId
			   appendHtml(othTemplate(values), 8);
		* @param html 追加的html
		* @param type  类型 1:欢迎及警告 3：用户 8:完全匹配及寒暄库
		* @param isvoice 是否语音
	   function appendHtml(value, type) {
		   var commonHtml = "";
		   if (type == 1) {
			   var values = {robotName: robotName, isSys: true, value: value, img: robotImage};
			   commonHtml = othTemplate(values);
		   } else if (type == 8) {
			   commonHtml = value;
		   } else if (type == 3) {
			   commonHtml = value;
			   commonHtml = value;
		   $("#answerList").html(" ")
		   $("#answerList").append(commonHtml);
		   var div = document.getElementById('answerList');
		   div.scrollTop = div.scrollHeight;
	   $(window).keydown(function (e) {
		   if (e.keyCode == 38) {
			   index = (index-- < 0 ? size - 1 : index);
			   $(".completionQues").find("li").each(function (_index, item) {
				   if (index == _index) {
					   $(this).addClass("li_hover");
					   $(this).removeClass("li_hover");
		   } else if (e.keyCode == 40) {
			   index = (index++ > size - 1 ? 0 : index);
			   $(".completionQues").find("li").each(function (_index, item) {
				   if (index == _index) {
					   $(this).addClass("li_hover");
					   $(this).removeClass("li_hover");
		   } else if (e.keyCode == 13) {
	   function play(id, ele) {
		   $("audio").each(function (index, item) {
			   $(this)[0].pause();
		   var audio = document.getElementById(id);
		   console.log(audio,"0202");//测试
		   audio.loop = false;
		   audio.addEventListener('ended', function () {
			   $(ele).find("span").eq(0).removeClass().addClass("yy_btn");
		   var _class = $(ele).find("span").eq(0).attr("class");
		   if (_class == "yy_btn") {
			   $(".yy_btn_close").each(function (index, item) {
				   $(this).removeClass().addClass("yy_btn");
			   $(ele).find("span").eq(0).removeClass().addClass("yy_btn_close");
		   } else if (_class == "yy_btn_close") {
			   $(ele).find("span").eq(0).removeClass().addClass("yy_btn");
	   function isAndroidOrIOSOrPc() {
		   var u = navigator.userAgent, app = navigator.appVersion;
		   var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //Android终端
		   var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端
		   } else if (isAndroid) {
			   return "android";
		   } else if (isIOS) {
			   return "other";
	   function IsPC() {
		   var userAgentInfo = navigator.userAgent;
		   var Agents = ["Android", "iPhone",
			   "SymbianOS", "Windows Phone",
			   "iPad", "iPod", "MicroMessenger"];
		   var flag = true;
		   for (var v = 0; v < Agents.length; v++) {
			   if (userAgentInfo.indexOf(Agents[v]) > 0) {
	   $("#yuyin").click(function(){
		   $(".h-start1").show();
	   $("#jianpan").click(function(){
		   if(ifs){uploadRec()}
		   $(".h-start1").hide()
require(["jquery","dialog","url"],function($,dialog,urlModel){
		   $(document).ready(function() {
			   var whiteHost = ["fj.flyz12348.cn","************","*************","www.1.cn","www.gov.mo","www.gov.hk","www.fjsen.com","www.crt.com.cn","fj.people.com.cn","www.fujiansme.com","app3.hxdtw.net","www.eeafj.cn","zqztc.fujiansme.com","**************","szfb.fjeec.cn:444","fjaqi.fjemc.org.cn","www.fjjfypt.com","ifc.fujiansme.com","www.fjredcross.org.cn","**************","wryfb.fjemc.org.cn","szfb.fjeec.cn","**************","www.hxrc.com","**************","***********","***********","**************","**************","*************","pwq.hxee.com.cn","www.longyan.cn","www.zhangzhoutong.cn","fjggzyjy.cn","policy.fujiansme.com","mp.weixin.qq.com","live.hst.com","www.fjosta.org.cn","**************","*************","www.fjsbdcdj.com","**************","*************","**************","vr.fjta.com","*************","politics.cntv.cn","www.fjtv.net","fjdlkysp.cn","12306.cn","xmedu.cn","basic.fj.smartedu.cn","***************","pets.neea.edu.cn","ckcz.eeafj.cn","zsbzk.eeafj.cn","account.chsi.com.cn","jszk.eeafj.cn","ntce.neea.edu.cn","fujian12320.com","rmjk.people.cn","flbook.com.cn","passport.neea.edu.cn","jyfwyun.com","www.12371.cn","www.xuexi.cn","article.xuexi.cn","gxyq.xmedu.cn","book.yunzhan365.com"];
			   function showWarnDialog(url){
				   var d = dialog({
					   title: avalon.vmodels.windowRoot.paths[3]=='english'?"Notice":'提示信息',
					   content:avalon.vmodels.windowRoot.paths[3]=='english'? 'You are now leaving our website.':'您访问的链接是非政府网站链接，是否继续？',
					   okValue: avalon.vmodels.windowRoot.paths[3]=='english'?"Continue":"继续访问",
					   ok: function () {
						   window.open(url);
					   cancelValue: avalon.vmodels.windowRoot.paths[3]=='english'?"Go Back":"放弃",
					   cancel: function () {
			   $("a").click(function () {
				   var url = $(this).attr("href") || "";
				   var urlObj = urlModel.parse( url );
				   if (urlObj.protocol) {    //外链或完整的链接
					   if (/^[\d\.:]*$/.test(urlObj.hostname)) { //纯IP类型
						   if(whiteHost.indexOf(urlObj.hostname) == -1){ // 非白名单
							   showWarnDialog(url)
							   return false;
					   } else if (whiteHost.indexOf(urlObj.hostname) > -1) {    //白名单
					   } else if(url.indexOf("javascript")>-1){
					   } else if(url.indexOf("tel:")>-1){
					   } else if (url.indexOf(".gov.cn") == -1) {
						   showWarnDialog(url)
						   return false;
				   } else {  //本站点内连接
import gradio as gr
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import logging
import os
import sys

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ERNIEChatBot:
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.model_name = "baidu/ERNIE-4.5-21B-A3B-PT"
        self.is_loaded = False
        
    def load_model(self):
        """加载模型和分词器"""
        try:
            logger.info("正在加载模型，请稍候...")

            # 尝试不同的加载方式
            try:
                # 方法1：直接加载
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_name,
                    trust_remote_code=True,
                    use_fast=False  # 使用慢速分词器
                )
            except Exception as e1:
                logger.warning(f"方法1失败: {e1}")
                try:
                    # 方法2：强制重新下载
                    self.tokenizer = AutoTokenizer.from_pretrained(
                        self.model_name,
                        trust_remote_code=True,
                        use_fast=False,
                        force_download=True
                    )
                except Exception as e2:
                    logger.warning(f"方法2失败: {e2}")
                    # 方法3：使用通用分词器
                    logger.info("尝试使用通用分词器...")
                    self.tokenizer = AutoTokenizer.from_pretrained(
                        "baichuan-inc/Baichuan2-7B-Chat",  # 使用兼容的分词器
                        trust_remote_code=True,
                        use_fast=False
                    )

            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                trust_remote_code=True,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                low_cpu_mem_usage=True
            )
            self.is_loaded = True
            logger.info("模型加载完成！")
            return "✅ 模型加载成功！"
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            return f"❌ 模型加载失败: {str(e)}\n\n建议尝试以下解决方案：\n1. 检查网络连接\n2. 清理缓存后重试\n3. 使用其他兼容模型"
    
    def generate_response(self, prompt, max_new_tokens=1024, temperature=0.7, top_p=0.9):
        """生成回复"""
        if not self.is_loaded:
            return "❌ 请先加载模型！"
        
        if not prompt.strip():
            return "❌ 请输入有效的问题！"
        
        try:
            # 准备输入
            messages = [
                {"role": "user", "content": prompt}
            ]
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            model_inputs = self.tokenizer(
                [text], 
                add_special_tokens=False, 
                return_tensors="pt"
            ).to(self.model.device)
            
            # 生成回复
            with torch.no_grad():
                generated_ids = self.model.generate(
                    model_inputs.input_ids,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码输出
            output_ids = generated_ids[0][len(model_inputs.input_ids[0]):].tolist()
            generate_text = self.tokenizer.decode(
                output_ids, 
                skip_special_tokens=True
            ).strip("\n")
            
            return generate_text
            
        except Exception as e:
            logger.error(f"生成回复时出错: {str(e)}")
            return f"❌ 生成回复时出错: {str(e)}"

# 创建聊天机器人实例
chatbot = ERNIEChatBot()

def load_model_interface():
    """加载模型的接口函数"""
    return chatbot.load_model()

def chat_interface(message, history, max_tokens, temperature, top_p):
    """聊天接口函数"""
    if not chatbot.is_loaded:
        return history + [("请先点击'加载模型'按钮", "❌ 模型尚未加载")]
    
    # 生成回复
    response = chatbot.generate_response(
        message, 
        max_new_tokens=max_tokens,
        temperature=temperature,
        top_p=top_p
    )
    
    # 更新历史记录
    history.append((message, response))
    return history, ""

# 创建Gradio界面
with gr.Blocks(title="文心一言 ERNIE-4.5 聊天机器人", theme=gr.themes.Soft()) as demo:
    gr.Markdown(
        """
        # 🤖 文心一言 ERNIE-4.5 聊天机器人
        
        基于百度ERNIE-4.5-21B模型的智能对话系统
        
        **使用说明：**
        1. 首先点击"加载模型"按钮（首次加载需要一些时间）
        2. 模型加载完成后，在下方输入框中输入您的问题
        3. 调整右侧参数以控制生成效果
        """
    )
    
    with gr.Row():
        with gr.Column(scale=3):
            # 模型加载区域
            with gr.Group():
                gr.Markdown("### 📥 模型管理")
                load_btn = gr.Button("🔄 加载模型", variant="primary", size="lg")
                load_status = gr.Textbox(
                    label="加载状态", 
                    value="⏳ 模型未加载，请点击上方按钮加载",
                    interactive=False
                )
            
            # 聊天区域
            with gr.Group():
                gr.Markdown("### 💬 对话区域")
                chatbot_interface = gr.Chatbot(
                    label="对话历史",
                    height=400,
                    show_copy_button=True
                )
                
                with gr.Row():
                    msg_input = gr.Textbox(
                        label="输入消息",
                        placeholder="请输入您的问题...",
                        lines=2,
                        scale=4
                    )
                    send_btn = gr.Button("📤 发送", variant="primary", scale=1)
                
                clear_btn = gr.Button("🗑️ 清空对话历史", variant="secondary")
        
        with gr.Column(scale=1):
            gr.Markdown("### ⚙️ 生成参数")
            max_tokens = gr.Slider(
                minimum=50,
                maximum=2048,
                value=1024,
                step=50,
                label="最大生成长度",
                info="控制回复的最大长度"
            )
            
            temperature = gr.Slider(
                minimum=0.1,
                maximum=2.0,
                value=0.7,
                step=0.1,
                label="温度参数",
                info="控制生成的随机性，越高越随机"
            )
            
            top_p = gr.Slider(
                minimum=0.1,
                maximum=1.0,
                value=0.9,
                step=0.05,
                label="Top-p参数",
                info="控制生成的多样性"
            )
    
    # 事件绑定
    load_btn.click(
        fn=load_model_interface,
        outputs=load_status
    )
    
    send_btn.click(
        fn=chat_interface,
        inputs=[msg_input, chatbot_interface, max_tokens, temperature, top_p],
        outputs=[chatbot_interface, msg_input]
    )
    
    msg_input.submit(
        fn=chat_interface,
        inputs=[msg_input, chatbot_interface, max_tokens, temperature, top_p],
        outputs=[chatbot_interface, msg_input]
    )
    
    clear_btn.click(
        fn=lambda: ([], ""),
        outputs=[chatbot_interface, msg_input]
    )

if __name__ == "__main__":
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )

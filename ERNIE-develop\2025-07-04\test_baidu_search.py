#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试百度搜索功能
"""

import asyncio
import logging
from baidu_search_utils import BaiduSearchUtils

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_search():
    """测试百度搜索功能"""
    print("开始测试百度搜索功能...")

    try:
        search_utils = BaiduSearchUtils()
        print("BaiduSearchUtils 初始化成功")

        # 测试搜索
        query = "Python编程"
        print(f"正在搜索: {query}")

        results = await search_utils.search_baidu(query, max_results=5)
        print(f"搜索完成，获得 {len(results)} 个结果")

        # 如果结果很少，尝试另一个查询
        if len(results) < 2:
            print("\n尝试另一个查询...")
            query2 = "机器学习"
            results2 = await search_utils.search_baidu(query2, max_results=5)
            print(f"第二次搜索完成，获得 {len(results2)} 个结果")
            results.extend(results2)

        print(f"\n搜索结果 ({len(results)} 个):")
        for i, result in enumerate(results, 1):
            print(f"{i}. 标题: {result['title']}")
            print(f"   URL: {result['url']}")
            print()

        if not results:
            print("未找到搜索结果，可能是解析问题")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_search())

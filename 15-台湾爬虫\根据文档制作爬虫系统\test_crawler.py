#!/usr/bin/env python3
"""
独立的爬虫测试脚本
"""
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from src.crawler.procurement_crawler import ProcurementCrawler

def test_crawler():
    """测试爬虫功能"""
    print("开始测试爬虫功能...")
    
    crawler = ProcurementCrawler()
    
    # 测试搜索功能
    print("正在搜索'水电'相关的采购信息...")
    results = crawler.search_procurement(
        keyword="水电",
        tender_types=['招标', '决标'],
        max_pages=2,
        page_size=10
    )
    
    print(f"搜索完成，共找到 {len(results)} 条数据")
    
    # 显示前几条结果
    for i, result in enumerate(results[:3]):
        print(f"\n第 {i+1} 条:")
        print(f"标案名称: {result['title']}")
        print(f"机关名称: {result['agency']}")
        print(f"标案类型: {result['tender_type']}")
        print(f"公告日期: {result['announcement_date']}")
        print(f"案号: {result['case_number']}")
        print(f"链接: {result['url']}")
        print("-" * 50)
    
    return results

if __name__ == "__main__":
    results = test_crawler()
    print(f"\n测试完成！共获取 {len(results)} 条数据")


<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>豆包 - AI智能问答搜索</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        /* 头部样式 */
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(90deg, #3a7bd5, #00d2ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: flex;
            align-items: center;
        }
        
        .logo i {
            margin-right: 10px;
            font-size: 36px;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #4a6fa5;
            font-weight: 500;
            font-size: 16px;
            transition: color 0.3s;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
        }
        
        .nav-links a:hover {
            color: #2c5282;
            background-color: rgba(42, 117, 213, 0.1);
        }
        
        .nav-links a.active {
            color: #fff;
            background: linear-gradient(90deg, #3a7bd5, #00d2ff);
        }
        
        /* 搜索模式切换 */
        .mode-selector {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .mode-tabs {
            display: flex;
            background: white;
            border-radius: 50px;
            padding: 5px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .mode-tab {
            padding: 12px 25px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .mode-tab.active {
            background: linear-gradient(90deg, #3a7bd5, #00d2ff);
            color: white;
        }
        
        .mode-tab:not(.active) {
            color: #666;
        }
        
        .mode-tab:not(.active):hover {
            background: #f0f4f8;
        }
        
        /* 搜索区域 */
        .search-container {
            text-align: center;
            margin-bottom: 50px;
            padding: 0 20px;
        }
        
        .search-container h1 {
            font-size: 2.8rem;
            margin-bottom: 20px;
            background: linear-gradient(90deg, #2c3e50, #4a6fa5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .search-container p {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        
        .search-box {
            max-width: 700px;
            margin: 0 auto;
            position: relative;
        }
        
        .search-input-container {
            position: relative;
            margin-bottom: 15px;
        }
        
        .search-box textarea {
            width: 100%;
            padding: 18px 25px;
            padding-left: 60px;
            border: none;
            border-radius: 20px;
            font-size: 18px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
            line-height: 1.5;
        }
        
        .search-box textarea:focus {
            outline: none;
            box-shadow: 0 8px 25px rgba(58, 123, 213, 0.3);
        }
        
        .search-box i {
            position: absolute;
            left: 25px;
            top: 25px;
            color: #3a7bd5;
            font-size: 22px;
        }
        
        .search-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .search-options {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .search-options label {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }
        
        .search-options input[type="number"] {
            width: 60px;
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
        }
        
        .search-btn {
            background: linear-gradient(90deg, #3a7bd5, #00d2ff);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(58, 123, 213, 0.3);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(58, 123, 213, 0.4);
        }
        
        .search-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        /* 结果区域 */
        .results-section {
            display: none;
            margin-top: 40px;
        }
        
        .results-summary {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .progress-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3a7bd5, #00d2ff);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        
        /* AI回答区域 */
        .ai-answer-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .ai-answer-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f4f8;
        }
        
        .ai-answer-header h2 {
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .ai-answer-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #666;
        }
        
        .ai-answer-meta span {
            background: #f8fafd;
            padding: 5px 12px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .ai-answer-content {
            line-height: 1.8;
            color: #333;
        }

        /* 思考过程样式 */
        .thinking-process {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            margin: 15px 0;
            border-radius: 5px;
            overflow: hidden;
        }

        .thinking-process-header {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thinking-process-content {
            padding: 15px;
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }

        /* Markdown样式 */
        .ai-answer-content h1, .ai-answer-content h2, .ai-answer-content h3 {
            color: #333;
            margin: 20px 0 10px 0;
        }

        .ai-answer-content h1 {
            font-size: 24px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .ai-answer-content h2 {
            font-size: 20px;
            color: #667eea;
        }

        .ai-answer-content h3 {
            font-size: 16px;
            color: #555;
        }

        .ai-answer-content strong {
            color: #333;
            font-weight: 600;
        }

        .ai-answer-content em {
            color: #666;
            font-style: italic;
        }

        .ai-answer-content code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #d63384;
        }

        .ai-answer-content pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            margin: 15px 0;
        }

        .ai-answer-content pre code {
            background: none;
            padding: 0;
            color: #333;
        }

        .ai-answer-content ul {
            margin: 15px 0;
            padding-left: 20px;
        }

        .ai-answer-content li {
            margin: 5px 0;
            line-height: 1.6;
        }
        
        /* 文章列表 */
        .articles-section {
            display: none;
        }
        
        .article-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .article-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }
        
        .article-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .article-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .article-meta {
            color: #666;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .article-preview {
            color: #555;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .article-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .view-original {
            color: #3a7bd5;
            text-decoration: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: color 0.3s;
        }
        
        .view-original:hover {
            color: #2c5282;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            header {
                flex-direction: column;
                gap: 20px;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .search-container h1 {
                font-size: 2.2rem;
            }
            
            .search-controls {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-options {
                justify-content: center;
            }
            
            .ai-answer-meta {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header>
            <div class="logo">
                <i class="fas fa-seedling"></i>
                <span>豆包</span>
            </div>
            <div class="nav-links">
                <a href="#" class="active">AI问答</a>
                <a href="#" onclick="showSection('home')">首页</a>
                <a href="#" onclick="showSection('search')">全网搜索</a>
                <a href="#" onclick="showSection('browser')">浏览器导航</a>
                <a href="#" onclick="showSection('download')">下载中心</a>
                <a href="#" onclick="showSection('member')">会员服务</a>
            </div>
        </header>
        
        <!-- 搜索模式选择 -->
        <section class="mode-selector">
            <div class="mode-tabs">
                <div class="mode-tab active" onclick="switchMode('online')">
                    <i class="fas fa-globe"></i>
                    <span>联网搜索</span>
                </div>
                <div class="mode-tab" onclick="switchMode('offline')">
                    <i class="fas fa-brain"></i>
                    <span>本地AI</span>
                </div>
            </div>
        </section>
        
        <!-- 搜索区域 -->
        <section class="search-container">
            <h1 id="search-title">AI智能问答搜索</h1>
            <p id="search-subtitle">提出问题，AI为您搜索并智能回答</p>
            
            <div class="search-box">
                <div class="search-input-container">
                    <i class="fas fa-brain"></i>
                    <textarea id="questionInput" placeholder="请输入您的问题，例如：算家云在哪里？微短剧行业发展如何？" rows="3"></textarea>
                </div>
                
                <div class="search-controls">
                    <div class="search-options">
                        <label id="pages-label">
                            <input type="number" id="maxPages" value="3" min="1" max="20" />
                            <span>搜索页数</span>
                        </label>
                    </div>
                    <button id="searchBtn" class="search-btn">
                        <i class="fas fa-brain"></i>
                        <span>AI智能回答</span>
                    </button>
                </div>
            </div>
        </section>
        
        <!-- 进度显示 -->
        <section class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备中...</div>
        </section>
        
        <!-- 结果摘要 -->
        <section class="results-section" id="resultsSection">
            <div class="results-summary" id="resultsSummary"></div>
        </section>
        
        <!-- AI回答区域 -->
        <section class="ai-answer-section" id="aiAnswerSection">
            <div class="ai-answer-header">
                <h2><i class="fas fa-robot"></i> AI智能回答</h2>
            </div>
            <div class="ai-answer-meta" id="aiAnswerMeta"></div>
            <div class="ai-answer-content" id="aiAnswerContent"></div>
        </section>
        
        <!-- 文章列表 -->
        <section class="articles-section" id="articlesSection">
            <div id="articlesList"></div>
        </section>
    </div>

    <script>
        class DoubanAISearch {
            constructor() {
                this.currentMode = 'online';
                this.isSearching = false;
                this.initElements();
                this.bindEvents();
            }
            
            initElements() {
                this.searchBtn = document.getElementById('searchBtn');
                this.questionInput = document.getElementById('questionInput');
                this.maxPagesInput = document.getElementById('maxPages');
                this.progressContainer = document.getElementById('progressContainer');
                this.progressFill = document.getElementById('progressFill');
                this.progressText = document.getElementById('progressText');
                this.resultsSection = document.getElementById('resultsSection');
                this.resultsSummary = document.getElementById('resultsSummary');
                this.aiAnswerSection = document.getElementById('aiAnswerSection');
                this.aiAnswerMeta = document.getElementById('aiAnswerMeta');
                this.aiAnswerContent = document.getElementById('aiAnswerContent');
                this.articlesSection = document.getElementById('articlesSection');
                this.articlesList = document.getElementById('articlesList');
            }
            
            bindEvents() {
                this.searchBtn.addEventListener('click', () => this.startSearch());
                this.questionInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.startSearch();
                    }
                });
            }
            
            async startSearch() {
                const question = this.questionInput.value.trim();
                const maxPages = parseInt(this.maxPagesInput.value);
                
                if (!question) {
                    alert('请输入您的问题');
                    return;
                }
                
                if (this.isSearching) {
                    return;
                }
                
                this.isSearching = true;
                this.searchBtn.disabled = true;
                this.searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>AI思考中...</span>';
                
                // 重置界面
                this.resetResults();
                this.showProgress();
                
                try {
                    if (this.currentMode === 'online') {
                        await this.performOnlineSearch(question, maxPages);
                    } else {
                        await this.performOfflineSearch(question);
                    }
                } catch (error) {
                    console.error('搜索失败:', error);
                    this.showError('搜索失败，请稍后重试');
                } finally {
                    this.isSearching = false;
                    this.searchBtn.disabled = false;
                    this.searchBtn.innerHTML = '<i class="fas fa-brain"></i> <span>AI智能回答</span>';
                    this.hideProgress();
                }
            }
            
            async performOnlineSearch(question, maxPages) {
                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question: question,
                        max_pages: maxPages
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                this.handleSearchEvent(data);
                            } catch (e) {
                                console.warn('解析数据失败:', e);
                            }
                        }
                    }
                }
            }
            
            async performOfflineSearch(question) {
                // 模拟本地AI处理
                this.updateProgress(20, '本地AI模型加载中...');
                await this.sleep(1000);
                
                this.updateProgress(50, '分析问题中...');
                await this.sleep(1500);
                
                this.updateProgress(80, '生成回答中...');
                await this.sleep(2000);
                
                // 模拟本地AI回答
                const mockAnswer = `## 🎯 本地AI回答

基于本地AI模型分析，您的问题是："${question}"

由于这是本地AI模式，无法进行实时网络搜索，但我可以基于预训练知识为您提供一般性回答。

## 💡 建议

如需获取最新、最准确的信息，建议：
1. 切换到"联网搜索"模式
2. 查阅官方网站或权威资料
3. 咨询相关专业人士

*此回答由本地AI模型生成，仅供参考*`;
                
                this.updateProgress(100, '完成');
                
                this.showResults({
                    total_articles: 0,
                    success_count: 0,
                    relevant_articles: 0,
                    question: question,
                    keyword: '本地AI',
                    question_answer: mockAnswer
                });
            }
            
            handleSearchEvent(data) {
                switch (data.type) {
                    case 'progress':
                        this.updateProgress(data.progress, data.message);
                        break;
                    case 'article':
                        this.addArticle(data.article);
                        break;
                    case 'complete':
                        this.showResults(data.summary);
                        break;
                    case 'error':
                        this.showError(data.message);
                        break;
                }
            }
            
            updateProgress(progress, message) {
                this.progressFill.style.width = `${progress}%`;
                this.progressText.textContent = message;
            }
            
            showProgress() {
                this.progressContainer.style.display = 'block';
                this.updateProgress(0, '准备中...');
            }
            
            hideProgress() {
                setTimeout(() => {
                    this.progressContainer.style.display = 'none';
                }, 1000);
            }
            
            resetResults() {
                this.resultsSection.style.display = 'none';
                this.aiAnswerSection.style.display = 'none';
                this.articlesSection.style.display = 'none';
                this.articlesList.innerHTML = '';
            }
            
            addArticle(article) {
                const articleElement = document.createElement('div');
                articleElement.className = 'article-item';
                articleElement.innerHTML = `
                    <div class="article-header">
                        <div>
                            <div class="article-title">${article.title}</div>
                            <div class="article-meta">
                                <span><i class="fas fa-calendar"></i> ${article.pub_date}</span>
                                <span><i class="fas fa-file-text"></i> ${article.content.length} 字符</span>
                            </div>
                        </div>
                    </div>
                    <div class="article-preview">${article.content.substring(0, 200)}...</div>
                    <div class="article-actions">
                        <a href="${article.url}" target="_blank" class="view-original">
                            <span>查看原文</span>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                `;
                
                this.articlesList.appendChild(articleElement);
                this.articlesSection.style.display = 'block';
            }
            
            showResults(summary) {
                this.resultsSection.style.display = 'block';
                
                if (this.currentMode === 'online') {
                    this.resultsSummary.innerHTML = `
                        <i class="fas fa-check-circle" style="color: #28a745;"></i>
                        搜索完成！共找到 <strong>${summary.total_articles}</strong> 篇文章，
                        筛选出 <strong>${summary.relevant_articles || 0}</strong> 篇相关文章，
                        成功保存 <strong>${summary.success_count}</strong> 篇到本地文件
                        ${summary.question_answer ? '<br><i class="fas fa-robot" style="color: #667eea;"></i> 已生成AI智能回答' : ''}
                    `;
                } else {
                    this.resultsSummary.innerHTML = `
                        <i class="fas fa-brain" style="color: #667eea;"></i>
                        本地AI处理完成！
                        <br><i class="fas fa-robot" style="color: #667eea;"></i> 已生成AI回答
                    `;
                }
                
                if (summary.question_answer) {
                    this.showAIAnswer(summary);
                }
            }
            
            showAIAnswer(summary) {
                this.aiAnswerSection.style.display = 'block';

                this.aiAnswerMeta.innerHTML = `
                    <span><i class="fas fa-question-circle"></i> 问题: ${summary.question}</span>
                    <span><i class="fas fa-search"></i> 搜索关键词: ${summary.keyword}</span>
                    ${this.currentMode === 'online' ? `
                        <span><i class="fas fa-file-alt"></i> 总文章: ${summary.total_articles}篇</span>
                        <span><i class="fas fa-filter"></i> 相关文章: ${summary.relevant_articles || 0}篇</span>
                    ` : ''}
                    <span><i class="fas fa-robot"></i> AI模型: DeepSeek-R1-Distill-Qwen-32B</span>
                    <span><i class="fas fa-clock"></i> 生成时间: ${new Date().toLocaleString()}</span>
                `;

                // 使用innerHTML来支持Markdown格式渲染
                this.aiAnswerContent.innerHTML = this.formatMarkdown(summary.question_answer);

                this.aiAnswerSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }

            // 格式化Markdown内容
            formatMarkdown(text) {
                if (!text) return '';

                let formatted = text;

                // 处理思考过程 <think>...</think>
                formatted = formatted.replace(/<think>([\s\S]*?)<\/think>/g, (match, content) => {
                    return `<div class="thinking-process">
                        <div class="thinking-process-header">
                            <i class="fas fa-brain"></i>
                            <strong>思考过程</strong>
                        </div>
                        <div class="thinking-process-content">${content.trim().replace(/\n/g, '<br>')}</div>
                    </div>`;
                });

                // 处理代码块 ```language\ncode\n```
                formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
                    const lang = language || 'text';
                    return `<pre><code class="language-${lang}">${this.escapeHtml(code.trim())}</code></pre>`;
                });

                // 处理行内代码 `code`
                formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');

                // 处理标题
                formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');
                formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
                formatted = formatted.replace(/^# (.*$)/gm, '<h1>$1</h1>');

                // 处理粗体
                formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

                // 处理斜体
                formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

                // 处理列表
                formatted = formatted.replace(/^- (.*$)/gm, '<li>$1</li>');
                formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

                // 处理换行
                formatted = formatted.replace(/\n/g, '<br>');

                return formatted;
            }

            // HTML转义函数
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
            
            showError(message) {
                this.resultsSection.style.display = 'block';
                this.resultsSummary.innerHTML = `
                    <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                    ${message}
                `;
            }
            
            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }
        
        // 模式切换功能
        function switchMode(mode) {
            const tabs = document.querySelectorAll('.mode-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.closest('.mode-tab').classList.add('active');
            
            const searchTitle = document.getElementById('search-title');
            const searchSubtitle = document.getElementById('search-subtitle');
            const pagesLabel = document.getElementById('pages-label');
            const searchIcon = document.querySelector('.search-box i');
            
            if (mode === 'online') {
                searchTitle.textContent = 'AI智能问答搜索';
                searchSubtitle.textContent = '提出问题，AI为您搜索并智能回答';
                pagesLabel.style.display = 'flex';
                searchIcon.className = 'fas fa-brain';
                aiSearch.currentMode = 'online';
            } else {
                searchTitle.textContent = '本地AI智能问答';
                searchSubtitle.textContent = '基于本地AI模型，无需联网即可回答';
                pagesLabel.style.display = 'none';
                searchIcon.className = 'fas fa-microchip';
                aiSearch.currentMode = 'offline';
            }
        }
        
        // 导航功能（占位）
        function showSection(section) {
            alert(`${section} 功能正在开发中...`);
        }
        
        // 初始化
        let aiSearch;
        document.addEventListener('DOMContentLoaded', function() {
            aiSearch = new DoubanAISearch();
        });
    </script>
</body>
</html>

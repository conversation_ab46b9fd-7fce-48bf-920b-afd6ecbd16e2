# -*- coding: utf-8 -*-
"""
改进的百度搜索工具类，使用BeautifulSoup解析HTML
"""

import logging
import urllib.parse
from typing import List, Dict
import re

from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig

try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False


class ImprovedBaiduSearchUtils:
    """
    改进的百度搜索工具类
    """

    def __init__(self):
        """初始化百度搜索工具"""
        self.logger = logging.getLogger(__name__)
        
        # 配置爬虫参数
        self.run_config = CrawlerRunConfig(
            page_timeout=15000,
            word_count_threshold=10,
            excluded_tags=["script", "style", "iframe", "meta", "nav", "footer"],
            exclude_external_links=False,
            exclude_internal_links=True,
            exclude_social_media_links=True,
            exclude_external_images=True,
            only_text=False,
            cache_mode=CacheMode.BYPASS,
        )

    async def search_baidu(self, query: str, max_results: int = 10) -> List[Dict[str, str]]:
        """
        在百度搜索指定查询词并返回结果URL列表
        """
        try:
            # 构建百度搜索URL
            encoded_query = urllib.parse.quote(query)
            search_url = f"https://www.baidu.com/s?wd={encoded_query}&rn={max_results}"
            
            self.logger.info(f"搜索URL: {search_url}")
            
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(url=search_url, config=self.run_config)
            
            if not result.html:
                self.logger.warning("未获取到HTML内容")
                return []
            
            self.logger.info(f"获取到HTML内容，长度: {len(result.html)}")
            
            # 尝试使用BeautifulSoup解析
            if HAS_BS4:
                search_results = self._parse_with_bs4(result.html, max_results)
            else:
                search_results = self._parse_with_regex(result.html, max_results)
            
            self.logger.info(f"找到 {len(search_results)} 个搜索结果")
            return search_results
            
        except Exception as e:
            self.logger.error(f"百度搜索失败: {e}")
            return []

    def _parse_with_bs4(self, html_content: str, max_results: int) -> List[Dict[str, str]]:
        """使用BeautifulSoup解析HTML"""
        results = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找所有搜索结果容器
            result_containers = soup.find_all('div', class_=re.compile(r'result'))
            
            for container in result_containers[:max_results]:
                # 查找标题链接
                title_link = container.find('h3', class_='t')
                if title_link:
                    link = title_link.find('a')
                    if link and link.get('href'):
                        url = link.get('href')
                        title = link.get_text(strip=True)
                        
                        if self._is_valid_url(url) and title:
                            # 处理百度重定向URL
                            if url.startswith('/link?url='):
                                real_url = self._extract_real_url(url)
                                if real_url:
                                    url = real_url
                            
                            results.append({
                                'title': title,
                                'url': url
                            })
            
            # 如果没找到结果，尝试更宽松的查找
            if not results:
                all_links = soup.find_all('a', href=True)
                for link in all_links[:max_results * 2]:
                    href = link.get('href')
                    text = link.get_text(strip=True)
                    
                    if (self._is_valid_url(href) and 
                        text and len(text) > 5 and
                        not text.startswith('百度') and
                        '搜索' not in text):
                        
                        results.append({
                            'title': text,
                            'url': href
                        })
                        
                        if len(results) >= max_results:
                            break
                            
        except Exception as e:
            self.logger.error(f"BeautifulSoup解析失败: {e}")
        
        return results

    def _parse_with_regex(self, html_content: str, max_results: int) -> List[Dict[str, str]]:
        """使用正则表达式解析HTML（备用方法）"""
        results = []
        
        try:
            # 百度搜索结果的多种模式
            patterns = [
                r'<h3[^>]*class="[^"]*t[^"]*"[^>]*><a[^>]*href="([^"]*)"[^>]*>(.*?)</a></h3>',
                r'<h3[^>]*><a[^>]*href="([^"]*)"[^>]*>(.*?)</a></h3>',
                r'<a[^>]*href="([^"]*)"[^>]*><h3[^>]*>(.*?)</h3></a>',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                if matches:
                    for match in matches[:max_results]:
                        url, title = match
                        title = re.sub(r'<[^>]+>', '', title).strip()
                        
                        if self._is_valid_url(url) and title:
                            if url.startswith('/link?url='):
                                real_url = self._extract_real_url(url)
                                if real_url:
                                    url = real_url
                            
                            results.append({
                                'title': title,
                                'url': url
                            })
                    
                    if results:
                        break
                        
        except Exception as e:
            self.logger.error(f"正则表达式解析失败: {e}")
        
        return results

    def _extract_real_url(self, baidu_url: str) -> str:
        """从百度重定向URL中提取真实URL"""
        try:
            if 'url=' in baidu_url:
                url_part = baidu_url.split('url=')[1]
                if '&' in url_part:
                    url_part = url_part.split('&')[0]
                return urllib.parse.unquote(url_part)
        except Exception as e:
            self.logger.error(f"提取真实URL失败: {e}")
        return ""

    def _is_valid_url(self, url: str) -> bool:
        """检查URL是否有效"""
        if not url:
            return False
        
        invalid_patterns = [
            'javascript:', 'mailto:', '#', 'baidu.com', 'hao123.com',
            'tieba.baidu.com', 'zhidao.baidu.com', 'image.baidu.com'
        ]
        
        for pattern in invalid_patterns:
            if pattern in url.lower():
                return False
        
        return url.startswith(('http://', 'https://'))

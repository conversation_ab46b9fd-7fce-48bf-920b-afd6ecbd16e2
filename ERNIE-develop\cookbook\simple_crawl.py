#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版爬取工具

专注于快速、稳定的网页内容提取，去除复杂配置。
"""

import asyncio
import logging
import sys
import time
import re
from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig


def setup_logging():
    """配置简洁日志"""
    logging.basicConfig(
        level=logging.ERROR,  # 只显示错误
        format='%(levelname)s: %(message)s'
    )


class SimpleCrawler:
    """简化的爬虫类"""
    
    def __init__(self):
        self.config = CrawlerRunConfig(
            # 基本配置
            page_timeout=15000,  # 15秒超时
            word_count_threshold=1,
            only_text=True,
            cache_mode=CacheMode.BYPASS,
            # 简化的过滤
            excluded_tags=["script", "style"],
            exclude_external_images=True,
            # 快速等待策略
            wait_until="domcontentloaded",
            delay_before_return_html=500,  # 0.5秒延迟
        )
    
    async def get_text(self, url: str) -> str:
        """获取网页文本"""
        try:
            async with AsyncWebCrawler() as crawler:
                print(f"🔍 正在获取: {url}")
                
                result = await crawler.arun(url=url, config=self.config)
                
                if not result:
                    return None
                
                # 尝试多种提取方法
                text = None
                
                # 方法1: 尝试 markdown
                if result.markdown and hasattr(result.markdown, 'raw_markdown'):
                    raw_md = result.markdown.raw_markdown
                    if raw_md and len(raw_md.strip()) > 10:
                        text = raw_md
                        print(f"✅ 使用 raw_markdown 提取了 {len(text)} 字符")
                
                # 方法2: 尝试 cleaned_html
                if not text and result.cleaned_html:
                    cleaned = result.cleaned_html.strip()
                    if len(cleaned) > 10:
                        text = cleaned
                        print(f"✅ 使用 cleaned_html 提取了 {len(text)} 字符")
                
                # 方法3: 尝试原始HTML转文本
                if not text and result.html:
                    html_text = re.sub(r'<[^>]+>', '', result.html)
                    html_text = re.sub(r'\s+', ' ', html_text).strip()
                    if len(html_text) > 50:
                        text = html_text
                        print(f"✅ 使用 HTML转文本 提取了 {len(text)} 字符")
                
                if text:
                    # 简单清理
                    text = text.replace("undefined", "")
                    text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)  # 合并多个空行
                    text = text.strip()
                    return text
                else:
                    print("❌ 未能提取到有效内容")
                    return None
                    
        except Exception as e:
            print(f"❌ 爬取失败: {e}")
            return None


async def test_single_url():
    """测试单个URL"""
    
    url = "https://www.suanjiayun.com/help?id=6746dd84e254decae19ccdb5"
    
    print("🚀 简化版爬取测试")
    print("=" * 40)
    print(f"🎯 测试URL: {url}")
    
    crawler = SimpleCrawler()
    start_time = time.time()
    
    text = await crawler.get_text(url)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n⏱️  总耗时: {duration:.1f} 秒")
    
    if text:
        print(f"📊 文本长度: {len(text)} 字符")
        print(f"📊 单词数量: {len(text.split())} 个")
        
        # 显示前300字符预览
        preview = text[:300] + "..." if len(text) > 300 else text
        print(f"\n📝 内容预览:")
        print("-" * 40)
        print(preview)
        print("-" * 40)
        
        return True
    else:
        print("❌ 测试失败")
        return False


async def test_multiple_urls():
    """测试多个URL"""
    
    urls = [
        "https://www.suanjiayun.com/help?id=6746dd84e254decae19ccdb5",  # 计费说明
        "https://www.suanjiayun.com/help?id=684fe3c9071c181fe93c7d74",  # 学术加速
        "https://www.suanjiayun.com/help?id=6746dd17e254decae19ccdb1",  # 其他文档
    ]
    
    print(f"\n🔄 批量测试 {len(urls)} 个URL")
    print("=" * 40)
    
    crawler = SimpleCrawler()
    results = []
    total_start = time.time()
    
    for i, url in enumerate(urls, 1):
        print(f"\n[{i}/{len(urls)}] 处理中...")
        
        start_time = time.time()
        text = await crawler.get_text(url)
        end_time = time.time()
        duration = end_time - start_time
        
        if text:
            results.append({
                'url': url,
                'success': True,
                'length': len(text),
                'duration': duration,
                'preview': text[:100] + "..." if len(text) > 100 else text
            })
            print(f"✅ 成功 - {duration:.1f}秒")
        else:
            results.append({
                'url': url,
                'success': False,
                'duration': duration
            })
            print(f"❌ 失败 - {duration:.1f}秒")
        
        # 短暂延迟
        if i < len(urls):
            await asyncio.sleep(1)
    
    total_end = time.time()
    total_duration = total_end - total_start
    
    # 统计结果
    successful = sum(1 for r in results if r['success'])
    
    print(f"\n📊 批量测试结果:")
    print(f"  ✅ 成功: {successful}/{len(urls)}")
    print(f"  ⏱️  总耗时: {total_duration:.1f} 秒")
    print(f"  ⚡ 平均耗时: {total_duration/len(urls):.1f} 秒/URL")
    
    # 显示成功结果
    print(f"\n📚 成功获取的内容:")
    for i, result in enumerate([r for r in results if r['success']], 1):
        print(f"  {i}. 长度: {result['length']} 字符 ({result['duration']:.1f}s)")
        print(f"     预览: {result['preview']}")
    
    # 保存结果
    if successful > 0:
        filename = "simple_crawl_results.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("简化版爬取结果\n")
                f.write("=" * 30 + "\n\n")
                
                for result in results:
                    if result['success']:
                        f.write(f"URL: {result['url']}\n")
                        f.write(f"长度: {result['length']} 字符\n")
                        f.write(f"耗时: {result['duration']:.1f} 秒\n")
                        f.write(f"内容:\n{result['preview']}\n")
                        f.write("\n" + "-" * 30 + "\n\n")
            
            print(f"\n📁 结果已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    return results


async def main():
    """主函数"""
    setup_logging()
    
    print("⚡ 简化版网页爬取工具")
    print("专注于快速、稳定的内容提取")
    print("=" * 50)
    
    try:
        # 测试1: 单URL快速验证
        success = await test_single_url()
        
        if success:
            # 测试2: 批量处理
            await test_multiple_urls()
        
        print(f"\n🎉 测试完成!")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"❌ 程序错误: {e}")


if __name__ == "__main__":
    try:
        # Windows事件循环优化
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        asyncio.run(main())
        
    except KeyboardInterrupt:
        print("\n👋 已退出")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

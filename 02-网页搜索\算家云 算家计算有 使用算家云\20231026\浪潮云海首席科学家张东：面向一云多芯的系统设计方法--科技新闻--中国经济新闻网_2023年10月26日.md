﻿# 浪潮云海首席科学家张东：面向一云多芯的系统设计方法--科技新闻--中国经济新闻网

**发布日期**: 2023年10月26日

**原文链接**: https://www.cet.com.cn/itpd/itxw/3465583.shtml

## 📄 原文内容

近日， 浪潮云海首席科学家张东、资深研究员亓开元 在《中国计算机学会通讯》2023年第9期发表论文《面向一云多芯的系统设计方法》， 深入分析一云多芯背后的关键挑战问题，阐释面向一云多芯的系统设计方法和关键技术路线， 并在此基础上，描绘了 一云多芯三阶段发展路线图 ，为促进一云多芯向应用感知、架构无感知的目标迭代演进提供新思路。

近年来，巨大的市场需求加速了我国云计算软硬件的发展，从芯片、整机、云操作系统、中间件到应用软件的云计算创新链、产业链已初步形成。随着行业“上云用数赋智”进程的不断加速和深化，应用场景呈现多样化趋势，越来越多的数据中心选择多元化的算力构建，给融合池化管理和灵活弹性调度带来了新的挑战。

中央处理器(CPU)作为应用最广泛的算力器件，多厂商、不同架构叠加组合造成的多元异构现象尤为突出。Intel、AMD等x86架构仍是数据中心的主导力量，但占比逐步缩减;ARM架构凭借运算核心多、功耗低等优势，发展势头强劲;开源的RISC-V架构也逐步兴起。同时，在全球产业链重构的背景下，我国核心器部件的研发和生产也进入蓬勃发展阶段，但因起步较晚、技术路线各异、发展水平不一，多元异构处理器将会长期并存发展。

云计算作为一种追求性价比的算力供给模式，其处理器的升级、换代和扩容正从单一架构向多元异构转变。在多元异构处理器功能、性能和可靠性存在差异的情况下，为满足高效稳定的技术需求，实现应用跨处理器低成本或自由切换，规避供应风险，保障关键业务长期稳定运行，“一云多芯”成为云计算发展的必然趋势。

互联网行业面向公有云的一云多芯工作开始得较早，凭借技术和资金储备自研高性价比的处理器，如亚马逊推出基于ARM的Graviton处理器，打破了对x86架构的依赖。针对行业私有云南向资源多样性与北向应用复杂性的矛盾，目前国内金融、电信、能源等行业也已开始进行一云多芯的研究和建设，早期通过云管理层实现纳管多个异构资源池，虽然能形成统一入口，但由于资源池割裂、应用无法跨架构编排，造成资源供给效率低下。

云海云操作系统（ InCloud OS ） 、Apsara  Stack、EasyStack等通过单一资源池实现异构资源的统一调度和互联互通，但当前阶段主要解决“多芯”的混部问题，距离以应用为中心的跨架构运行和低成本切换尚有较大差距。为满足多芯共存条件下业务的稳定运行、平滑切换和弹性伸缩，如下科学问题和技术难题亟待解决。

1. 应用跨架构可移植及运行环境等价性问题。 应用程序运行在多芯系统不同处理器架构的节点上，首先需要确保程序本身的跨架构可移植。进一步，层次化、模块化的复杂应用在异构节点间进行动态迁移、远程调用或水平扩展，如何保障运行环境(操作系统、运行时、依赖库等)的跨架构等价可执行成为挑战(见图1)。

2. 多元异构算力量化分析和负载感知调度问题。 多元异构CPU性能差异达2~10倍，附加异构加速单元的节点间计算能力的差异更是数量级的。应用在异构节点间迁移、切换或伸缩时，需要保证用户体验前后一致，遵守业务的服务水平协议(Service  Level Agreement，SLA)。如何针对多元异构算力等价关系进行评估建模和量化分析，实现负载感知均衡调度和自适应弹性伸缩成为关键科学问题。

3. 非对等架构下分布式应用的状态一致性保证问题。 相比于传统分布式节点的对等性，一云多芯应用分布的异构节点非对等性不容忽视。针对非对等分布式云原生应用，实现有状态任务在异构节点间高效一致性共识协商和数据同步，以及无状态任务的非侵入流量动态控制和平滑切分，成为跨架构云原生应用编排的关键技术难点。

ACM图灵奖获得者尼古拉斯·沃斯(Niklaus  Wirth)提出了著名的公式“程序=数据结构+算法”，揭示了程序的时间和空间本质。一云多芯系统作为一种软件定义外延，除了数据平面的指令逻辑和数据状态两个时空要素外，还包括控制平面对多元异构资源的管控。因此，一云多芯系统可以抽象为“资源管理+运行程序+数据状态”。

其中，资源管理通过软件定义提供计算、存储、网络和安全等硬件资源抽象，以虚拟机、容器、裸金属(bare  metal)等粒度为应用提供资源封装及运行环境;运行程序按照分层解耦分为资源层、平台层和应用层，例如，承载用户业务的应用程序、资源管理程序;数据状态是指程序运行所依赖的内存瞬时数据、数据库持久化数据及流量状态等。

根据上述定义，一云多芯系统应从 程序的可运行性、资源的可管理性及状态的可迁移性 三方面分别进行设计。

1. 程序的可运行性 程序的跨架构运行 在一云多芯系统中，程序的首要设计目标是可运行性，即可以在不同处理器架构的环境中移植运行，技术路线包含跨平台语言、跨平台编译以及指令翻译技术(见表1)。

跨平台语言以Java、Python为代表，通过跨平台语言实现程序架构无关部分的跨架构运行，但仍然存在一些架构相关的问题：(1)运行时环境依赖，例如，Java程序在多芯系统中运行需要提供不同架构的Java虚拟机(Java  Virtual Machine，JVM)运行时;(2)本地库依赖，例如Java本地接口(Java Native  Interface，JNI)需要跨平台移植。

跨平台编译即交叉编译，借助特定处理器架构环境及编译工具生成其他架构的可执行程序。交叉编译通过架构无关的源代码实现程序的跨平台二进制代码生成，但是对于可执行程序，仍然需要统一二进制代码与处理器架构。

二进制翻译即指令集翻译技术，是解决应用跨架构移植问题的研究热点，实现方法包括软件级二进制翻译和芯片级二进制翻译。无论软件级还是芯片级，均受到翻译系统的限制。软件级二进制翻译需要对应用运行环境进行改造，增加了运行环境的复杂性，而芯片级二进制翻译过程性能损耗严重。例如，目前针对纯运算类程序的翻译器效率是直接编译的60%~70%，如果涉及系统调用、锁等操作，效率将下降到30%~40%，并且二进制翻译过程依然存在指令集不兼容的问题，例如高级矢量扩展(Advanced  Vector Extensions，AVX)指令。

跨平台语言解决了应用的跨架构问题，但是需要提供跨架构的运行时;交叉编译解决了跨架构编译问题，但是仍然存在运行时的动态库依赖问题。因此，程序在多芯系统中运行不仅需要考虑自身的可运行性，对于现代化的复杂应用，还应综合考虑其依赖的运行时。可行的路线是结合标准化的容器方式对应用程序及其运行时依赖进行封装，作为实现应用的跨架构部署及切换的基础资源封装。

也就是说，基于同一套源代码，针对不同的架构构建不同的容器镜像，如果程序是基于跨平台的语言构建的，则将程序脚本或中间代码与运行时封装为容器;如果程序是基于非跨平台的语言构建的，则可以通过交叉编译，构建各种架构下的二进制文件，然后将其与依赖库等封装为容器，此流程可以通过一套流水线作业自动构建，并推送至镜像库。

综上所述，一云多芯程序可运行性设计包括三个方面，首先实现应用程序的跨架构编译及运行，其次构建标准容器化封装，最后通过云资源编排管理实现轻量化部署(见图2)。

2. 资源的可管理性 资源可管理性包括架构感知和算力量化分析，以及面向系统的资源均衡调度和面向业务的弹性伸缩。

架构感知技术 架构感知是一云多芯实现节点调度、界面功能自适应展示的关键，是支撑程序的可运行性、实现资源封装生命周期管理的基础，可通过收集器、调度器、拦截器实现(见图3)。(1)收集器采集并上报各节点的CPU架构、硬件特性等信息，建立包含架构特性的主机列表。(2)调度器为各种粒度的资源封装选择匹配的主机节点，采用级联过滤器机制，加载多个独立的过滤器，依次对创建请求与主机进行匹配。在一云多芯场景下，通过级联架构感知过滤器，识别资源封装创建请求中的镜像架构标签，根据CPU架构特性匹配结果过滤出主机节点。(3)拦截器用于建立可动态扩展的“架构-功能”映射矩阵，解析资源封装管理请求的动作及架构特征，执行拦截请求并将结果反馈展示，从而实现不同架构功能差异化的自动识别、动态扩展，屏蔽底层实现差异，提供统一的资源管理视图。

因不同架构的处理器计算能力不同，相同应用即便使用了同等规格的资源封装(例如相同的CPU核心、内存等)，在异构环境上运行的性能也存在差异。根据应用场景，算力可分为CPU通用算力和XPU异构算力。一云多芯系统目前面临的主要问题是CPU的多元异构，多厂商的ARM、x86架构处理器在指令集、核心数、生产工艺等方面均有所不同，因此在性能上也存在差异。这种差异性可以通过算力等价关系刻画，根据层次划分为规格算力、有效算力和业务算力(见表2)。

其中，规格算力的通用性最强，有效算力对特定负载类型更具针对性，业务算力更加贴近真实的应用场景，但由于负载和应用的多样性，有效算力、业务算力的测算需要联合上下游生态共同完成。

从资源层面，在为资源封装选择节点时，根据节点计算能力利用均衡策略对负载进行调度，这是一个以资源利用率最大化为目标的约束优化问题。均衡调度算法作用在级联过滤器之后，从过滤出的主机节点中选择负载相对最小的作为最终目标，对于一云多芯系统，此过程的关键是节点的算力量化分析。基于规格算力评估多类型资源的规格系数，再结合归一化、主资源公平等数值方法，能够测算出各节点的可用算力。基于归一化的算法如下：

节点j的得分Scorej是r种资源类型权重得分之和，包括CPU、内存、硬盘等，如式(1)。各资源类型权重得分算法如式(2)，其中，ResourceNormalizedji为节点j资源i可分配量的最小-最大正向归一化，如式(3)所示;WeighterMultiplieri为资源的权重，可根据负载的CPU、内存或IO密集类型调整权重，体现每种资源的重要程度，coefficientji为各资源的规格算力系数，例如，ARM型和x86型CPU的规格算力量化关系为1∶2，规格系数分别为1和2，可分配CPU核数相同的情况下，x86型节点被调度的优先级更高，从而实现一云多芯场景下基于算力量化的均衡调度。

为了支撑面向业务峰值和低谷的弹性伸缩，要做到资源封装的精准规划、快速调度和算力等价，保证应用服务弹得对、弹得快、弹得准(见图4)。(1)在资源规划方面，根据应用负载特定周期内的概率分布特征，基于历史数据时间序列建立负载趋势模型，刻画应用负载、服务质量与资源关系的负载画像和容量画像，通过负载趋势预测和应用异常反馈方式规划资源封装伸缩需求。(2)在快速调度方面，基于架构感知、均衡调度技术，在扩展资源封装时快速调度至最佳节点，并拉起应用服务，保障应用服务及时响应。(3)在弹性伸缩引发资源封装跨架构切换时，基于算力量化技术刻画不同架构的算力，根据有效算力、业务算力计算资源封装的等价关系，确保业务的服务质量随资源增减而线性伸缩。

3. 状态的可迁移性 资源层的应用状态迁移将持久化数据、内存瞬时状态、外设配置以及网络流量整体迁移至目标节点，涉及资源封装内所有相关数据状态。除了应用本身之外，还涉及操作系统、中间件等，迁移难度较大。为解决此问题，可进一步遵循资源层、平台层和应用层解耦的思路，采用基于云原生微服务治理的状态同步和流量切分方法。

虚拟机的在线热迁移技术已经相对成熟，通常通过预拷贝算法将源虚拟机的内存增量状态以迭代的方式传输到目的主机，也出现了后拷贝、混合拷贝等优化算法以及硬件压缩加速技术，加速内存拷贝收敛，减少停机时间，提升迁移效率。但是虚拟机迁移仍存在相同厂商CPU代际差距、不同厂商同架构兼容性、不同架构无法热迁移的限制。容器的在线迁移技术研究起步较晚，本质上是进程组的迁移，当前的研究主要基于用户空间的检查点和恢复(Checkpoint  and Restore In Userspace，CRIU)实现容器运行时状态的迁移，并衍生出了一系列缩短迁移时间、降低不可用时间的优化方法。

此外，自适应容器在线迁移通过动态调整压缩算法的加速因子实现CPU和网络带宽资源的匹配，减少容器快照的传输时间。以虚拟机和容器为资源封装粒度的整体迁移，虽然已经有了一些研究与应用，但是仍存在迁移数据量大、停机时间和总迁移时间长的问题，实现应用跨架构平滑切换难度较大。随着云原生技术的发展，结合服务治理方式成为可行路线，其中的关键技术包括有状态服务的数据同步、无状态服务的流量切换。

多副本的状态同步依赖于分布式一致性算法。ACM图灵奖获得者莱斯利·兰伯特(Leslie  Lamport)提出了基于消息传递且具有高容错性的Paxos共识算法，ZooKeeper的ZAB，MySQL的wsrep、Etcd，Redis的Raft协议都基于其核心思想实现了数据状态一致性。在此基础上，一云多芯平台层的数据状态同步需要进一步考虑节点非对称特征。下文以Raft协议为例进行说明。

选举(leader  election)过程：主节点(leader)向所有的从节点(follower)周期性发送心跳来保证主节点地位，当一个从节点在一个超时周期内没有收到心跳，则该节点转化为候选(candidate)节点参与选举。一云多芯系统中各节点的处理能力、网络条件等不同导致超时影响差异化，可采用基于极大似然估计的适应性方法，避免心跳延迟大、处理能力弱的节点频繁触发选举，同时保证处理能力强的节点可快速发起选举。对于投票策略，采用节点优先级或缩小随机超时取值范围机制，使强节点更容易获得多数票。

复制(log replication)过程：采用法定写入(quorum  write)机制，主节点接收来自客户端的请求，向从节点发起写入提议并接收反馈投票，每个提议获得的票数大于半数才能提交写入。在一云多芯中异构节点作为容灾可用区(Availability  Zone，AZ)设计，须保证各容灾可用区都被写入。

云原生应用通过网关或负载均衡器将流量分发至各无状态副本实例，流量就是无状态工作负载的状态。在多芯系统中，当应用在异构节点间迁移或弹性伸缩时，需要切分流量，并引流至对应节点的副本上。为保证服务质量不降级，根据有效算力、业务算力量化分析确定等价目标副本的规格和数量，并分配其承担的流量比例，流量切换应与业务逻辑充分解耦，可采用服务网格的思想实现。

控制面感知副本变化生成流量切分策略，下发至网络代理和网关。对于东西向流量，网络代理劫持流量并根据切分策略按比例转发到不同的副本。对于南北向流量，网关在流量转发时根据切分策略转发到不同副本(见图5)。在流量切分的瞬时过程中，受目标节点副本未启动、TCP连接延迟等因素的影响，会出现无法响应、丢包等应用服务质量下降的情况，可以通过预热、探针、重试、排水技术保障应用跨架构的平滑切换。

按照资源可管理性、程序可运行性、状态可迁移性系统设计，一云多芯可以分三个阶段逐步演进(见图6)。

阶段一：混合部署、统一管理、统一视图

第一阶段以可管理性为目标，实现异构处理器节点的统一池化管理、统一服务目录和统一监控运维，可运行性和可迁移性方面通过同源异构、离线迁移、手动切换、业务切分实现应用跨架构的部署和协同。目前国内外一云多芯建设主要处于这一阶段。遵循系统设计方法，笔者团队在InCloud  OS的研发实践中，提出了基于同源异构的持续集成、基于不可变基础设施的持续交付及架构感知调度方法，支持同一主线云操作系统源代码编译，构建异构节点的可执行程序，实现C/C++、Java、Python、Go多语言千万级代码在8种主流处理器上的分钟级构建，为各类型应用提供了参考指导方案(见图7)。

在基于InCloud  OS建设的云平台中，单资源池支持所有主流处理器架构，并按每个控制器1000个节点级联扩展，实现了相距超过1000公里的三地数据中心一云多芯跨域统一管理、互联互通，支撑云数智多样化业务需求，制定了技术规范和参考架构(见图8)。

阶段二：业务牵引、分层解耦、架构升级

在第一阶段的基础上，为进一步满足应用的低成本跨架构切换，第二阶段通过分层解耦和架构升级，实现应用的跨架构迁移、多架构混合部署和流量切分。笔者团队分别在资源层、平台层及应用层进行了初步探索。

1.在资源层，结合GuestOS感知应变机制进一步提升面向多元CPU的迁移适用性，提出了基于一致性快照的在线迁移方法。通过变更数据块追踪和多线程异步优化，实现10  TB大规格虚拟机的快速完整迁移。迁移后，系统启动初始化硬件检查，若不支持相关CPU特性则切换到应变(fallback)措施，保证系统正常运行，特别针对Windows虚拟机实现了CPU、固件自适应，兼容Win  XP以上桌面版和Win  2000以上服务器版，已在实际生产环境中展开应用。然而，虚拟机迁移的方式对应用是无感知的，迁移可能产生数据库和应用异常的风险，需要应用开发者配合，对虚拟机迁移后的可用性进行进一步验证。

2.在平台层，目前生产环境采用的方案是通过数据同步、业务切分实现有状态应用的跨架构运行(见图9)。基于InCloud  OS提供x86和ARM数据库集群服务及数据同步服务，数据同步服务根据源端数据库预写日志(Write Ahead  Log，WAL)捕获数据变化，在传输中通过加密压缩算法、事务合并、网络包封装优化网络协议开销和延迟，在目标端通过分组多任务并行和原生加载机制提升重放效率，实现亚秒级数据同步。应用基于读写分离架构设计，面向x86架构数据库读写、ARM架构数据库只读，实现一云多芯场景下数据库跨架构运行。

3.在应用层，InCloud OS于2023年1月完成首个一云多芯场景下SPEC  Cloud基准测试，验证了基于单一资源池承载多型x86、ARM处理器架构的资源可管理性、计算密集型聚类算法K-means的跨架构程序可运行性、IO密集型分布式数据库Cassandra的状态可迁移性，并结合均衡调度算法，实现了扩展性超过90%，性能超过SLA基线20%，平均上线时间超过世界纪录25%。

阶段三：软件定义、算力标准、全栈多芯

一云多芯是芯与云的融合，是平台和生态的协同。在第三阶段，通过处理器、整机、云操作系统、数据库、中间件和应用等产业链上下游的共同配合，实现应用与处理器架构的彻底解耦，保障业务长期稳定运行。

1.在算力资源层，提升处理器性能、可靠性的同时，通过系统设计定义处理器设计标准化和兼容性，同时推动二进制翻译技术在应用过程中不断优化。在支持处理器多芯的基础上，扩展对GPU、DPU等异构算力的统一抽象，实现异构加速协同。

2.在平台层，突破应用特征感知的可变粒度资源调度分配技术，解决应用类型与资源封装的自适应配置和编排问题，研究函数拓扑编排、高效调度和快速启动技术，解决大规模云原生应用的灵活构建和弹性扩展问题。

3.在应用层，促进应用支持多芯同源异构，完善云原生化转型升级最佳实践，与资源层和平台层相结合，实现应用感知、架构无感知的平滑切换和弹性伸缩。

4.在算力评估、标准和测评方面，研究多元异构有效算力的量化方法，联合专业测评机构及产业链上下游，建立一云多芯行业标准。

结束语： 一云多芯是解决数据中心多芯共存问题的必然趋势。为解决应用跨架构可运行、算力量化分析、负载感知调度、非对等架构分布式状态一致性的问题，笔者团队提出了一云多芯系统的核心设计理念和系统设计方法。

1.坚持系统观念，场景驱动、系统设计。从以CPU为核心向以系统为核心的设计模式转变，以应用为导向建立多元异构融合、软件定义和软硬协同的技术发展路线，持续提升计算效率和能效比。

2.加强生态协作，分层解耦、开放标准。处理器、整机、云操作系统、中间件、应用逐层解耦，通过生态协同消除单一技术路线带来的垂直封闭、生态离散问题，实现一云多芯标准化和规范化。

3.制定发展路线图，迭代创新、持续演进。从混合部署、离线迁移和手动切换，到基于架构升级的平滑切换和弹性伸缩，再到算力标准和全栈多芯迭代演进。

当前的研究和实践工作正处于第一阶段向第二阶段的过渡时期，围绕程序可运行性、资源可管理性和状态可迁移性技术进行了探索和布局，下一步需要加强产业链、创新链协作，向应用感知、架构无感知的目标迭代演进，推动一云多芯计算理论基础更加坚实完备，软硬协同和软件定义机制更加成熟有效，应用感知场景范式更加清晰可行，产业生态更加标准规范。
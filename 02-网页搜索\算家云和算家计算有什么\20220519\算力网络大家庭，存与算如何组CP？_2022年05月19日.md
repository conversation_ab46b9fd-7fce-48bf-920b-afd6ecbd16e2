﻿# 算力网络大家庭，存与算如何组CP？

**发布日期**: 2022年05月19日

**原文链接**: https://m.thepaper.cn/baijiahao_18168347

## 📄 原文内容

在数据大爆炸时代，小到每个人出生后的电子档案、上学时的学籍信息以及医疗、社保等信息，大到天文观测、生命科学研究、能源勘探等，数据已经毋庸置疑地成为了一种关键生产要素和战略资源。值得注意的是，从数据的产生、处理到消亡的全生命周期都离不开存储。可以说，存储是数据的载体，是整个ICT系统的基石。因此，要想真正实现变“数”为“宝”，让数据资产发挥最大价值，存储是第一块“敲门砖”。

“高质量的数字基础设施，首先要有强大的数据存储能力，即数据存力。”华为公司副总裁、数据存储与机器视觉产品线总裁周跃峰表示，“以数据存力为支撑，只有数据‘存得好’、算力‘算得快’、网络‘传得稳’，才能不断夯实数字基础设施的质量水平。”

“存、算、网”就是构建算力网络的三个关键环节。这其中，数据存储虽然不显山不露水，但不论是算力的实现，还是网络通信连接，都会被它所制约。也正因如此，存储成为了构建算力网络的基石。

回顾存储产业的发展，新变化不断涌现。一方面，从最早的纸张，到存储卡、字存储器、半导体存储器，再到今天的纳米存储、DNA存储等，存储介质在快速发生改变。另一方面，存储所扮演的角色也早已不同于以往。过去，存储只需简单扮演好记录数据或者记录信息的角色。而今天，存储已经成为一个与网络通信、计算甚至是芯片共融共生的角色，面向用户提供的是一套综合性解决方案。此外，能源可持续发展、碳达峰碳中和等，也对存储提出了更高的要求。

作为一个在存储行业从业23年的老兵，阿里巴巴集团研究员、阿里云基础产品资深产品总监陈起鲲对于这些年产业发生的改变感慨万千。“大数据时代，首先是数据量的增速发生了改变，非结构化数据和半结构化数据爆发式增长；其次，数据的产生点和存储点发生了改变，至少一半的数据开始转移至云端；最后，由于数据产生时间和分析需求的不同，数据价值也是不一样的。”陈起鲲对《中国电子报》记者说道，“存储的价值本质上就是让用户能够以更优的成本去存储、分析这些数据，给行业带来不一样的计算挑战。”

“对于企业级存储而言，容量空间只是一个方面，还需要从容量成本、性能、稳定性三大指标去做综合考量与平衡。”陈起鲲指出，“如果说以前的存储仅仅是一个‘盒子’的标准，那么今天则需要更加贴近客户使用需求，要把容量成本、可用性、稳定性等都考虑进来，存储标准正在进一步细化、智能化，同时也在不断升级。”

华为数据存储与机器视觉产品线副总裁张福鹏认为：“今天的数据存力应该是以存储容量为核心，同时包含了整体的性能表现、可靠程度、绿色效能等多个方面在内的综合能力。数据存力将承担未来数字高速公路的地基角色，是打造整个经济社会高质量发展的数字基石。”

算与存“若即若离”却又“相辅相成”

数据需要“存”，也需要“算”。存储与计算的边界其实是不清晰的。在云原生、大数据和人工智能蓬勃发展的背景下，存力和算力之间的配合主要体现在弹性、扩展性、性能三个方面。其中，弹性和扩展性的需求推动着存算分离的发展，而性能则要求存算一体。存与算之间看似“若即若离”，实则又是“相辅相成”的。

北京酷克数据科技有限公司CEO简丽荣认为，持久化存储部分采用存算分离与缓存部分采用存算一体，能够同时满足弹性、扩展性和性能需求，也是下一代存储的升级迭代的方向。例如云原生数据仓库HashData，为了追求极致的弹性和扩展性，HashData的计算集群和持久化存储是严格的物理分离，计算集群由类似AWS的EC2虚拟机组成，持久化存储则采用AWS S3为代表的对象存储。

数牍科技资深数据安全专家裴超认为，数据存力和算力之间永远是相互支撑、协同发展的关系。存储空间和读写数据性能的提升可以降低计算过程中对于存储管理的负担，如同一存储介质上的段页切换、不同存储介质的切换等；计算性能的提升可以并行使用更多存储的数据，对于分布式存储能力会提出更高要求。

在他看来，存储技术不会单一地走向“存算一体”或“存算分离”的局面。事实上，“存算一体”的设想本身也是在现有“存算分离”的基础上面向一定场景而提出的。二者区别在于“存”与“算”哪个是应用场景中的核心。

“在大部分的场景中，存算分离是最优架构，但一些特殊场景，比如边缘计算，也可以是存算融合架构。以自动驾驶场景为例，传感器、激光雷达、摄像头等车载设备收集来的数据无需进行太多分析，在车里就能进行实时处理辅助决策，这是存算一体架构；而数据上传至云端，通过网络传回到数据中心来做训练和仿真，则是存算分离架构。”陈起鲲说道。

此外，存算分离和存算一体对于数据安全方面也会带来不同的要求。裴超指出，存算分离可通过访问控制技术实现对于存储数据的读写安全，但是在由存储单元向计算单元进行数据传输过程会有数据泄露等安全风险；存算一体能够降低由于数据传输带来的安全风险，但是可能发生由于设计缺陷，产生越权访问的问题。

“未来存储技术的发展，存算分离还是存算一体都仍需要面对不同的场景，根据‘存’与‘算’之间的体系结构的特性，以及‘存’与‘算’的主导地位，提供不同的应用和服务。”裴超说道。

算力网络建设将带动存储产业新一轮升级

近年来，国家出台了一系列推进数据中心发展的指导政策，无论是新型数据中心，还是东数西算、东数西存、东数西备，都给中国存储产业带来了巨大的发展良机。“东数西算里面重要的一块就是东数西存和东数西备，因为我们要把东部的数据放到西部去计算，当然要牵涉存储问题。”中国信通院云计算与大数据研究所所长何宝宏表示，“尤其是对于时间延迟要求不太高的web应用、大数据分析、AI训练等而言，东数西存就变得非常有必要，且极具经济价值。另外，备份、归档等也带来了很大发展空间。”

不过，值得关注的是，目前国内存储产业仍面临不少挑战，如关键技术存在短板，闪存、先进存储技术的市场占比较低，存力发展不均衡等。华中科技大学教授、计算机科学与技术学院院长冯丹建议，首先要拉通整个产业链，加速发展先进存储技术，并以此为基础来构建存储设备、存储系统；其次要加快部署下一代存储技术，推动核心技术底层研发、技术攻关；同时要加强企业合作，进一步把技术转化为生产力，并通过联合产业界、学术界共同推动存储标准制定；此外还要重视高端人才的培养与引进，推动存储产业的创新升级。

“数据存储的重要性让它正在成为各国创新和竞争的一个技术焦点，包括非易失性存储、量子存储、DNA存储等在内的关键技术科学研究，已经成为了国家战略科技力量的重要组成部分，”中国科学院院士陈国良表示，“中国存储产业虽然起步较晚，但发展的势头强劲，在国际上已经拥有一定的话语权，期待未来存储产业大踏步前进，在政、产、学、研、用多方努力下发展得越来越好。”
# 🎬 Wan2.1-I2V-14B-480P 图像到视频模型微调训练完整指南

## 📋 文档概述

本文档详细介绍了如何使用DiffSynth-Studio框架对Wan-AI/Wan2.1-I2V-14B-480P模型进行LoRA微调训练。该模型专门用于图像到视频生成任务，支持从静态图像生成高质量的动态视频内容。

## 🎯 模型特点

- **模型名称**: Wan-AI/Wan2.1-I2V-14B-480P
- **模型类型**: 图像到视频生成 (Image-to-Video)
- **参数规模**: 14B (140亿参数)
- **输出分辨率**: 480P (480×832)
- **训练方式**: LoRA微调 (低秩适应)
- **支持硬件**: RTX 3090, RTX 4090等高端GPU

## 🔧 环境搭建

### 1. 系统要求

**硬件要求:**
- GPU: RTX 3090 24GB × 1-8块 (推荐8块用于分布式训练)
- 内存: 32GB+ 系统内存
- 存储: 200GB+ 可用磁盘空间
- CPU: 16核心+ (推荐)

**软件要求:**
- Ubuntu 20.04+ / CentOS 7+
- NVIDIA Driver 470+
- CUDA 11.8+
- Python 3.12
- Conda/Miniconda

### 2. 创建虚拟环境

```bash
# 创建Python 3.12环境
conda create -n wan_video_env python=3.12 -y

# 激活环境
conda activate wan_video_env

# 验证Python版本
python --version  # 应输出: Python 3.12.x
```

### 3. 安装依赖

```bash
# 进入DiffSynth-Studio目录
cd /path/to/DiffSynth-Studio

# 安装核心库
pip install -e .

# 安装训练相关依赖
pip install deepspeed peft accelerate

# 安装监控工具依赖
pip install psutil matplotlib pandas

# 验证安装
python -c "
import torch
import diffsynth
import accelerate
import deepspeed
import peft
print('✅ 所有依赖安装成功')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')
"
```

### 4. 配置代理 (如需要)

```bash
# 设置HTTP代理
export http_proxy="http://your-proxy:port"
export https_proxy="http://your-proxy:port"

# 验证代理设置
env | grep -i proxy
```

## 📥 数据准备

### 1. 下载示例数据集

```bash
# 下载官方示例数据集
modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset

# 验证数据集结构
ls -la data/example_video_dataset/
# 应包含: metadata.csv, video1.mp4, video2.mp4 等文件
```

### 2. 数据集格式说明

标准的I2V数据集应包含以下文件:

```
data/example_video_dataset/
├── metadata.csv          # 元数据文件
├── video1.mp4           # 训练视频1
├── video2.mp4           # 训练视频2
└── ...                  # 更多视频文件
```

**metadata.csv格式:**
```csv
video,prompt
video1.mp4,"一艘小船在海上航行，波浪起伏"
video2.mp4,"城市街道上的车辆和行人"
```

### 3. 创建自定义数据集

使用提供的数据集处理工具:

```bash
# 从视频文件创建I2V数据集
python tools/wan_i2v_dataset_processor.py \
    --action create_from_videos \
    --input_dir /path/to/your/videos \
    --output_dir ./data/custom_i2v_dataset \
    --prompts_file prompts.txt

# 验证数据集
python tools/wan_i2v_dataset_processor.py \
    --action validate \
    --input_dir ./data/custom_i2v_dataset
```

## 🚀 训练执行

### 1. 单GPU LoRA训练

适用于单块RTX 3090的基础训练:

```bash
# 给脚本添加执行权限
chmod +x examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P.sh

# 执行训练
bash examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P.sh
```

**训练参数说明:**
- `--height 480 --width 832`: 输出视频分辨率
- `--dataset_repeat 100`: 数据集重复次数
- `--learning_rate 1e-4`: 学习率
- `--num_epochs 5`: 训练轮数
- `--lora_rank 32`: LoRA秩大小
- `--gradient_accumulation_steps 4`: 梯度累积步数

### 2. 8×RTX 3090分布式训练

适用于多GPU高性能训练:

```bash
# 执行8×RTX 3090分布式训练
chmod +x examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_8x3090.sh
bash examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_8x3090.sh
```

**分布式训练特点:**
- 使用8块RTX 3090并行训练
- 自动创建accelerate配置
- 更高的LoRA rank (64)
- 更大的数据集重复次数 (200)

### 3. 显存优化训练

适用于显存受限的环境:

```bash
# 执行显存优化训练
chmod +x examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_memory_optimized.sh
bash examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_memory_optimized.sh
```

**优化配置:**
- 降低分辨率: 320×576
- 减小LoRA rank: 16
- 增加梯度累积: 8步
- 启用梯度检查点卸载

## 📊 训练监控

### 1. 实时GPU监控

```bash
# 启动GPU监控
python tools/gpu_monitor.py --action monitor --interval 2

# 查看GPU摘要
python tools/gpu_monitor.py --action summary

# 检查训练就绪状态
python tools/gpu_monitor.py --action check
```

### 2. 高级监控和优化

```bash
# 检查系统要求
python tools/wan_training_monitor.py --action check

# 监控训练过程 (持续1小时)
python tools/wan_training_monitor.py --action monitor --duration 3600 --interval 5

# 生成优化配置
python tools/wan_training_monitor.py --action optimize --output optimized_training.sh
```

## 🧪 推理测试

### 1. 基础推理测试

```bash
# 自动检测LoRA权重并测试
python examples/wanvideo/model_training/validate_lora/Wan2.1-I2V-14B-480P.py

# 指定LoRA权重路径
python examples/wanvideo/model_training/validate_lora/Wan2.1-I2V-14B-480P.py \
    --lora_path models/train/Wan2.1-I2V-14B-480P_lora/epoch-4.safetensors
```

### 2. 综合测试

```bash
# 执行多场景测试
python test_wan_i2v_lora.py --auto_detect --output_dir test_outputs

# 自定义图像测试
python test_wan_i2v_lora.py \
    --lora_path models/train/Wan2.1-I2V-14B-480P_lora/epoch-4.safetensors \
    --custom_image /path/to/your/image.jpg \
    --custom_prompt "描述你想要的视频内容"
```

## 🛠️ 故障排除

### 1. 常见错误及解决方案

**CUDA内存不足 (CUDA out of memory)**
```bash
# 解决方案1: 使用显存优化脚本
bash examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_memory_optimized.sh

# 解决方案2: 手动调整参数
# 减小batch_size, 增加gradient_accumulation_steps
# 降低分辨率和LoRA rank
```

**模型下载失败**
```bash
# 检查网络连接和代理设置
export HF_ENDPOINT=https://hf-mirror.com  # 使用镜像站点
export HF_HUB_ENABLE_HF_TRANSFER=1        # 启用快速传输
```

**训练中断恢复**
```bash
# 训练会自动保存检查点，可以从最后一个epoch继续
# 检查输出目录中的 epoch-*.safetensors 文件
ls -la models/train/Wan2.1-I2V-14B-480P_lora/
```

### 2. 性能优化建议

**显存优化:**
- 使用混合精度训练 (bfloat16)
- 启用梯度检查点
- 适当降低分辨率和帧数
- 使用较小的LoRA rank

**训练速度优化:**
- 使用多GPU分布式训练
- 增加数据加载进程数
- 使用SSD存储数据集
- 优化网络带宽

## 📈 训练结果分析

### 1. 模型文件说明

训练完成后，输出目录包含:
```
models/train/Wan2.1-I2V-14B-480P_lora/
├── epoch-0.safetensors    # 第1轮训练权重
├── epoch-1.safetensors    # 第2轮训练权重
├── ...
└── epoch-4.safetensors    # 最终训练权重
```

### 2. 选择最佳权重

通常最后几个epoch的权重效果最好，但建议测试多个权重文件:

```bash
# 测试不同epoch的权重
for epoch in {2..4}; do
    python examples/wanvideo/model_training/validate_lora/Wan2.1-I2V-14B-480P.py \
        --lora_path models/train/Wan2.1-I2V-14B-480P_lora/epoch-${epoch}.safetensors \
        --output video_epoch_${epoch}.mp4
done
```

## 🎯 最佳实践

### 1. 训练建议

- **数据质量**: 使用高质量、多样化的视频数据
- **训练时长**: 建议训练3-5个epoch，避免过拟合
- **学习率**: 从1e-4开始，根据损失曲线调整
- **验证频率**: 每个epoch后进行推理测试

### 2. 硬件配置建议

- **单GPU**: RTX 3090 24GB，适合小规模实验
- **多GPU**: 8×RTX 3090，适合生产级训练
- **存储**: 使用NVMe SSD存储数据集和模型
- **内存**: 至少32GB系统内存

### 3. 生产部署

训练完成的LoRA权重可以轻松集成到推理服务中:

```python
# 加载训练好的LoRA权重
pipe.load_lora(pipe.dit, "path/to/your/lora.safetensors", alpha=1)

# 生成视频
video = pipe(
    prompt="your prompt",
    input_image=input_image,
    height=480,
    width=832
)
```

## 📞 技术支持

如遇到问题，请检查:
1. GPU驱动和CUDA版本兼容性
2. 虚拟环境和依赖安装
3. 数据集格式和路径
4. 显存使用情况

更多技术细节请参考DiffSynth-Studio官方文档。

---

## 🚀 快速开始 (5分钟上手)

如果您想快速体验训练过程，请按以下步骤操作:

### 1. 环境准备
```bash
# 激活环境
conda activate wan_video_env

# 检查GPU状态
python tools/gpu_monitor.py --action check
```

### 2. 一键训练
```bash
# 执行显存优化训练 (推荐新手)
bash examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_memory_optimized.sh
```

### 3. 测试结果
```bash
# 自动测试训练结果
python test_wan_i2v_lora.py --auto_detect
```

完成！您的第一个I2V模型训练就完成了。

## 📋 训练检查清单

训练前请确认以下项目:

- [ ] ✅ 虚拟环境 `wan_video_env` 已激活
- [ ] ✅ GPU显存充足 (至少16GB可用)
- [ ] ✅ 数据集已下载到 `data/example_video_dataset/`
- [ ] ✅ 磁盘空间充足 (至少100GB可用)
- [ ] ✅ 网络连接正常 (用于下载模型)

训练后请验证:

- [ ] ✅ LoRA权重文件已生成 (`epoch-*.safetensors`)
- [ ] ✅ 推理测试成功生成视频
- [ ] ✅ 视频质量符合预期

## 🔗 相关资源

- **DiffSynth-Studio**: https://github.com/modelscope/DiffSynth-Studio
- **Wan模型系列**: https://modelscope.cn/models/Wan-AI/
- **技术论文**: 相关技术论文和文档
- **社区讨论**: ModelScope社区讨论区

---

*本文档最后更新: 2025-07-10*
*适用版本: DiffSynth-Studio latest*

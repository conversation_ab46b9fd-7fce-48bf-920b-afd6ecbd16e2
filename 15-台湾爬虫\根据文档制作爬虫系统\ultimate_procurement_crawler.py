#!/usr/bin/env python3
"""
終極政府採購爬蟲 - 完整爬取所有詳情頁數據
支持多頁爬取、自定義搜索條件、進度保存等功能
"""

from enhanced_procurement_crawler import EnhancedProcurementCrawler
import json
import time
from datetime import datetime
import os

def ultimate_procurement_crawler(keyword="國防部", 
                               tender_status="決標",
                               year="111",
                               max_pages=10,
                               page_size=100):
    """
    終極採購爬蟲 - 爬取所有詳情頁數據
    
    Args:
        keyword: 搜索關鍵詞
        tender_status: 標案狀態 (招標/決標/公開閱覽及公開徵求/政府採購預告)
        year: 年份 (111/112/113/114等)
        max_pages: 最大頁數
        page_size: 每頁數量 (建議100以提高效率)
    """
    
    print(f"🚀 開始終極採購數據爬取...")
    print(f"📋 搜索條件：關鍵詞='{keyword}', 狀態='{tender_status}', 年份={year}年")
    print(f"📄 最大頁數: {max_pages}, 每頁數量: {page_size}")
    
    # 創建爬蟲實例
    crawler = EnhancedProcurementCrawler()
    
    # 設置搜索參數
    search_params = {
        'querySentence': keyword,
        'tenderStatusType': tender_status,
        'sortCol': 'AWARD_NOTICE_DATE',
        'timeRange': year,
        'pron': 'true',
        'fuzzy': 'true',
        'pageSize': str(page_size)
    }
    
    all_list_results = []
    
    # 第一步：爬取所有列表頁數據
    print(f"\n📊 === 第一步：爬取所有列表頁數據 ===")
    
    for page in range(1, max_pages + 1):
        print(f"\n📄 正在爬取第 {page} 頁...")
        
        # 設置頁碼
        search_params['pageIndex'] = str(page)
        
        try:
            # 發送搜索請求
            search_response = crawler._make_request(
                crawler.search_action_url, 
                method='GET', 
                data=search_params
            )
            
            print(f"✅ 第 {page} 頁請求狀態碼: {search_response.status_code}")
            
            # 解析列表頁結果
            page_results = crawler._parse_search_results(search_response.content)
            
            if not page_results:
                print(f"⚠️ 第 {page} 頁沒有數據，停止爬取")
                break
            
            print(f"📋 第 {page} 頁獲取到 {len(page_results)} 筆數據")
            
            # 顯示前3筆數據預覽
            for i, result in enumerate(page_results[:3]):
                print(f"  {i+1}. {result.get('title', '')[:50]}... - {result.get('agency', '')}")
            
            if len(page_results) > 3:
                print(f"  ... 還有 {len(page_results) - 3} 筆數據")
            
            all_list_results.extend(page_results)
            
            # 添加延遲避免被封
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ 爬取第 {page} 頁時發生錯誤: {str(e)}")
            break
    
    print(f"\n✅ 列表頁爬取完成，共獲取 {len(all_list_results)} 筆數據")
    
    # 第二步：爬取所有詳細頁數據
    print(f"\n🔍 === 第二步：爬取所有詳細頁數據 ===")
    print(f"📊 將爬取全部 {len(all_list_results)} 筆的詳細數據")
    
    detailed_results = []
    failed_count = 0
    start_time = datetime.now()
    
    for i, result in enumerate(all_list_results):
        current_num = i + 1
        print(f"\n🔍 正在爬取第 {current_num}/{len(all_list_results)} 筆詳細數據...")
        print(f"📋 標案名稱: {result.get('title', '')[:60]}...")
        print(f"🏢 機關名稱: {result.get('agency', '')}")
        
        if result.get('detail_url'):
            try:
                # 獲取詳細頁面數據
                detail_info = crawler.extract_detail_fields(result['detail_url'])
                
                if detail_info:
                    # 合併數據
                    combined_result = {
                        'crawl_time': datetime.now().isoformat(),
                        'index': current_num,
                        'list_data': result,
                        'detail_data': {
                            'agency_info': detail_info.agency_info.__dict__,
                            'announcement_info': detail_info.announcement_info.__dict__,
                            'time_info': detail_info.time_info.__dict__,
                            'amount_info': detail_info.amount_info.__dict__,
                            'procurement_nature': detail_info.procurement_nature.__dict__,
                            'performance_info': detail_info.performance_info.__dict__,
                            'vendors': [vendor.__dict__ for vendor in detail_info.vendors],
                            'subject_classification': detail_info.subject_classification.__dict__,
                            'bidder_count': detail_info.bidder_count
                        }
                    }
                    
                    detailed_results.append(combined_result)
                    print(f"  ✅ 成功獲取詳細數據")
                    
                    # 顯示關鍵信息
                    if detail_info.agency_info.agency_name:
                        print(f"    🏢 機關: {detail_info.agency_info.agency_name}")
                    if detail_info.announcement_info.case_number:
                        print(f"    📄 案號: {detail_info.announcement_info.case_number}")
                    if detail_info.amount_info.budget_amount:
                        print(f"    💰 預算: {detail_info.amount_info.budget_amount}")
                    if detail_info.vendors:
                        print(f"    🏆 得標廠商: {len(detail_info.vendors)} 家")
                        for j, vendor in enumerate(detail_info.vendors[:2]):  # 只顯示前2家
                            print(f"      {j+1}. {vendor.vendor_name} - {vendor.award_amount}")
                    
                    # 每20筆保存一次，避免數據丟失
                    if len(detailed_results) % 20 == 0:
                        temp_file = f'temp_ultimate_data_{len(detailed_results)}.json'
                        with open(temp_file, 'w', encoding='utf-8') as f:
                            json.dump(detailed_results, f, ensure_ascii=False, indent=2)
                        print(f"    💾 已臨時保存 {len(detailed_results)} 筆數據")
                        
                else:
                    failed_count += 1
                    print(f"  ❌ 無法解析詳細數據")
                    
            except Exception as e:
                failed_count += 1
                print(f"  ❌ 獲取詳細數據時發生錯誤: {str(e)}")
        else:
            failed_count += 1
            print(f"  ❌ 沒有詳細頁連結")
        
        # 添加延遲避免被封
        time.sleep(3)
        
        # 每50筆顯示進度報告
        if current_num % 50 == 0:
            elapsed_time = datetime.now() - start_time
            success_rate = (len(detailed_results) / current_num) * 100
            estimated_total_time = elapsed_time * (len(all_list_results) / current_num)
            remaining_time = estimated_total_time - elapsed_time
            
            print(f"\n📊 === 進度報告 ===")
            print(f"⏱️ 已處理: {current_num}/{len(all_list_results)} 筆")
            print(f"✅ 成功獲取: {len(detailed_results)} 筆")
            print(f"❌ 失敗: {failed_count} 筆")
            print(f"📈 成功率: {success_rate:.1f}%")
            print(f"⏰ 已用時間: {str(elapsed_time).split('.')[0]}")
            print(f"⏳ 預估剩餘時間: {str(remaining_time).split('.')[0]}")
            print("🔄 繼續爬取...\n")
    
    # 保存最終結果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'ultimate_procurement_data_{keyword}_{year}年_{timestamp}.json'
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(detailed_results, f, ensure_ascii=False, indent=2)
    
    # 統計信息
    total_time = datetime.now() - start_time
    success_rate = (len(detailed_results) / len(all_list_results)) * 100 if all_list_results else 0
    
    print(f"\n🎉 === 爬取完成 ===")
    print(f"🔍 搜索關鍵詞: {keyword}")
    print(f"📅 年份範圍: {year}年")
    print(f"📋 列表頁數據: {len(all_list_results)} 筆")
    print(f"🔍 詳細頁數據: {len(detailed_results)} 筆")
    print(f"❌ 失敗數量: {failed_count} 筆")
    print(f"📈 成功率: {success_rate:.1f}%")
    print(f"⏰ 總用時: {str(total_time).split('.')[0]}")
    print(f"💾 數據已保存到: {output_file}")
    
    # 保存統計信息
    stats = {
        'crawl_time': datetime.now().isoformat(),
        'search_params': search_params,
        'total_list_results': len(all_list_results),
        'total_detail_results': len(detailed_results),
        'failed_count': failed_count,
        'success_rate': f"{success_rate:.1f}%",
        'total_time': str(total_time),
        'output_file': output_file
    }
    
    stats_file = f'ultimate_stats_{keyword}_{year}年_{timestamp}.json'
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"📊 統計信息已保存到: {stats_file}")
    
    # 清理臨時文件
    for temp_file in os.listdir('.'):
        if temp_file.startswith('temp_ultimate_data_') and temp_file.endswith('.json'):
            try:
                os.remove(temp_file)
                print(f"🗑️ 已清理臨時文件: {temp_file}")
            except:
                pass
    
    return detailed_results

def main():
    """主函數 - 可以自定義多種搜索條件"""
    print("🎯 === 終極政府採購數據爬蟲 ===")
    
    # 示例1：爬取國防部相關採購（111年）
    print("\n🔍 === 示例1：國防部採購數據（111年） ===")
    results1 = ultimate_procurement_crawler(
        keyword="國防部",
        tender_status="決標", 
        year="111",
        max_pages=3,        # 爬取3頁
        page_size=50        # 每頁50筆
    )
    
    print(f"\n✅ 國防部數據爬取完成：{len(results1)} 筆")

if __name__ == "__main__":
    main()

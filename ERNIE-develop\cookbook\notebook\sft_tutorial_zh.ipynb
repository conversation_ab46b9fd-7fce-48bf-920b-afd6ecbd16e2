{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 文心大模型 SFT 最佳实践"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1 SFT 简介\n", "### 1.1 什么是 SFT\n", "SFT（Supervised Fine-Tuning，监督精调）是定制化精调预训练大模型的关键技术。用**少量、高质量标注数据**继续训练模型，可以低成本、快速地提升模型在特定领域的表现。\n", "\n", "### 1.2 SFT 适用范围\n", "- ✅ 适用\n", "  - 任务适配，强化模型对特定任务的理解（如文本分类、信息抽取）；\n", "  - 强化已存在的能力效果或强调模型已具备的知识；\n", "  - 教会模型完成更复杂的组合任务（需要模型已具备完成相关子任务的能力）。\n", "\n", "- ❌ 不适用\n", "  - 给模型添加新的内化知识；\n", "  - 强迫模型学会不具备的能力；\n", "  - 完全纠正预训练模型中的固有偏差；\n", "  - 应对需要复杂推理或长期记忆的场景（需结合强化学习等）。\n", "\n", "\n", "### 1.3 SFT 的一般步骤\n", "1. 构造评估数据、评估标准，测试模型基础能力，分析其短板、问题。\n", "2. 进行充分的 prompt 工程。\n", "3. 判断是否适用 SFT；准备训练数据。\n", "4. 训练。结合任务特点、数据情况，设计训练参数。一般关注以下核心参数：\n", "    - 批处理大小\n", "    - 训练轮数、最大训练步数\n", "    - 学习率类型、学习率初始值\n", "    - 预热步数\n", "5. 效果评估。采用相同的评估数据、评估标准，评估训练后的模型表现。若效果不达标，考虑继续优化训练数据、调整训练参数。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2 最佳实践任务简介\n", "### 2.1 任务描述\n", "我们选择工具调用（function-call）任务作为最佳实践的示例：给定若干工具函数、多轮对话，模型根据对话语境，从若干工具函数中选择最合适的完成任务。简单起见，输出工具函数的名称即可。\n", "\n", "### 2.2 名词解释\n", "为了便于理解，我们定义如下概念。  \n", "- **session**：一组多轮对话，包括多个问答对\n", "- **round**：一个对话轮次，包括一问一答\n", "- **history**：OpenAI API 格式的 session 数据\n", "\n", "### 2.3 数据来源\n", "我们使用开源社区内较为流行的 glaive_toolcall 数据集（[链接](https://huggingface.co/datasets/llamafactory/glaive_toolcall_zh)）。该数据集有中、英文数据各1千条，采用 ShareGPT 格式，具有以下字段：\n", "- `conversations`：对话上下文，[{\"from\": \"\", \"value\": \"\"}] 格式，其中 `from` 字段包括\n", "  - `human`：用户输入\n", "  - `gpt`：模型输入（这里用 gpt 指代了大模对话助手）\n", "  - `function_call`：调用哪项工具以及工具参数\n", "  - `observation`：工具调用结果\n", "- `tools`：备选工具列表以及工具描述\n", "\n", "### 2.4 评估标准\n", "这是一个多选一任务，我们采用准确率、宏平均、微平均、加权平均指标评价模型表现。\n", "\n", "### 2.5 实验环境\n", "本教程采用以下环境：\n", "- 1张 80GB A800 GPU\n", "- CUDA 版本：12.3\n", "- CUDA 驱动：525.125.06\n", "- nvcc：12.3\n", "- gcc：12.2\n", "- Python 版本：3.10.12\n", "\n", "### 2.6 依赖项\n", "- **ERNIEKit**：文心大模型工具链，包含文心4.5系列模型训练-压缩-推理的全流程使用，基于飞桨框架v3.1可在多款主流国产芯片上进行训练。\n", "- **ERNIEKit WebUI**：可视化界面，支持训练、对话交互、性能评估、模型导出等功能。[文档](../../docs/cli_webui_usage.md)\n", "- **[可选]visualdl**：可视化 loss 等信息的工具，ERNIEKit 已包含。\n", "- **ERNIEKit 推理脚本**。\n", "- 本教程中的 **Python 依赖**。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 本教程中的 Python 依赖\n", "import json\n", "import random\n", "from collections import defaultdict\n", "from string import Template\n", "\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "RANDOM_SEED = 2025"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3 数据预处理\n", "\n", "首先观察 glaive_toolcall 数据集。注意到这里存在同一 session 多次调用函数的情况。对于这类数据，我们以工具调用为界限，拆分为多个 session。  \n", "\n", "进一步，我们依照 ERNIEKit 要求的训练、推理数据格式，整理 glaive_toolcall 数据集。\n", "  - **训练数据**：`jsonl` 文件，每行 `{\"system\": \"\", \"src\": [], \"tgt\": [], \"label\": []}`\n", "    - `system`(str, optional): 设定系统信息的  prompt，包括但不限于人物设定、任务说明、风格定义等等；\n", "    - `src` (list(str))：用户多轮对话内容，以对话轮次排列，与 `tgt`一一对应；\n", "    - `tgt` (list(str))：系统多轮回复内容，以对话轮次排列，与 `src` 一一对应；\n", "    - `label` (list(int), optional)：标记当前对话轮次是否训练，填`1` 表示本轮参与训练（计算 loss），`0` 表示不参与；若省略该字段，则表示所有轮次均参与训练。 \n", "  - **推理数据**：`jsonl` 文件，每行 `[{\"role\": \"\", \"content\": \"\"}]`, `role` 字段可填 `system`、`user`、`assistant`  \n", "\n", "综合以上各数据集格式，我们考虑将 `glaive_toolcall` 数据集整理为以下格式：`jsonl` 文件，每行 `[{\"role\": \"\", \"content\": \"\"}]`, `role` 字段可填 `system`、`user`、`assistant`，其中`user`与`assistant`按对话顺序成对出现，最后一个`assistant`记录调用的工具名称。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 准备原始数据\n", "首先下载开源数据集。保存在 `cookbook/data/glaive_toolcall_zh_1k.json`。\n", "\n", "![sft_download_dataset](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_download_dataset_zh.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["为了便于处理，这里将 ShareGPT 格式转为 OpenAI API 格式，并记录工具调用情况。我们定义了若干数据预处理的函数。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def parse_function(tools, func_name_with_desc: dict):\n", "    \"\"\"解析工具列表，获取工具名称和描述\n", "\n", "    Args:\n", "        tools: 工具列表\n", "        func_name_with_desc: 记录工具名称和描述对应关系的字典\n", "\n", "    Returns:\n", "        func_name_with_desc: 记录工具名称和描述对应关系的字典\n", "    \"\"\"\n", "    for tool in tools:\n", "        tool_name = tool.get(\"name\", \"\")\n", "        tool_desc = tool.get(\"description\", \"\")\n", "        func_name_with_desc[tool_name] = tool_desc\n", "    return func_name_with_desc\n", "\n", "\n", "def is_valid_history(history):\n", "    \"\"\"判断历史对话是否有效：user 和 assistant 成对出现, 且最后一轮为 assistant\n", "\n", "    Args:\n", "        history: OpenAI API 格式的多轮对话数据\n", "\n", "    Returns:\n", "        bool: 是否有效\n", "    \"\"\"\n", "    # 如果有 system prompt 需要剔除，便于计算\n", "    history = [h for h in history if h.get(\"role\", \"\") != \"system\"]\n", "    if len(history) < 2 or len(history) % 2 != 0:\n", "        return False\n", "    for i in range(1, len(history), 2):\n", "        if history[i][\"role\"] != \"assistant\" or history[i - 1][\"role\"] != \"user\":\n", "            return False\n", "    else:\n", "        return True\n", "\n", "\n", "def conversations_to_history(conversations):\n", "    \"\"\"将 ShareGPT 格式的对话数据转换为 OpenAI API 格式的多轮对话数据\n", "\n", "    Args:\n", "        conversations: ShareGPT 格式的对话数据\n", "\n", "    Yields:\n", "        history: OpenAI API 格式的多轮对话数据\n", "    \"\"\"\n", "    NO_FUNCTION_CALL_MARK = \"<|NO_FUNCTION_CALL|>\"\n", "    had_function_call = False\n", "    history = []\n", "    for conversation in conversations:\n", "        if conversation.get(\"from\", \"\") == \"human\":\n", "            history.append({\"role\": \"user\", \"content\": conversation.get(\"value\", \"\")})\n", "        elif conversation.get(\"from\", \"\") == \"function_call\":\n", "            history.append({\"role\": \"assistant\", \"content\": json.loads(conversation.get(\"value\", \"\")).get(\"name\", \"\")})\n", "            if is_valid_history(history):\n", "                yield history\n", "                had_function_call = True\n", "                history.pop()\n", "                # 如果还有更多轮对话，『assistant』字段用原数据的『gpt』内容（而不是『function_call』），因此删去当前的『assistant』，为下一轮数据做准备\n", "\n", "        elif conversation.get(\"from\", \"\") == \"gpt\":\n", "            if had_function_call:\n", "                history.append({\"role\": \"assistant\", \"content\": conversation.get(\"value\", \"\")})\n", "                had_function_call = False\n", "            else:\n", "                history.append({\"role\": \"assistant\", \"content\": NO_FUNCTION_CALL_MARK})\n", "                if is_valid_history(history):\n", "                    yield history\n", "                    history.pop()\n", "                    history.append({\"role\": \"assistant\", \"content\": conversation.get(\"value\", \"\")})\n", "        else:\n", "            pass\n", "\n", "\n", "def history_to_train(history, system_prompt: str = \"\"):\n", "    \"\"\"将 OpenAI API 格式多轮对话转换为 ERNIE 训练数据格式\n", "\n", "    Args:\n", "        history: OpenAI API 格式的多轮对话数据\n", "\n", "    Returns:\n", "        data: ERNIE 格式的训练数据\n", "    \"\"\"\n", "    data = {\"system\": system_prompt, \"src\": [], \"tgt\": [], \"label\": []}\n", "    if not is_valid_history(history):\n", "        return {}\n", "\n", "    for h in history:\n", "        role = h.get(\"role\", \"\")\n", "        content = h.get(\"content\", \"\")\n", "        if role == \"user\":\n", "            data[\"src\"].append(content)\n", "        elif role == \"assistant\":\n", "            data[\"tgt\"].append(content)\n", "        data[\"label\"] = [0] * (len(data[\"tgt\"]) - 1) + [1]\n", "    return data\n", "\n", "\n", "def history_to_test(history, system_prompt: str = \"\"):\n", "    \"\"\"将 OpenAI API 格式多轮对话转换为 ERNIE 测试数据格式\n", "\n", "    Args:\n", "        history: OpenAI API 格式的多轮对话数据\n", "\n", "    Returns:\n", "        data: ERNIE 格式的测试数据\n", "    \"\"\"\n", "    return [{\"role\": \"system\", \"content\": system_prompt}] + history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["调用以上函数处理数据。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["raw_data_path = \"../data/glaive_toolcall_zh_1k.json\"\n", "history_path = raw_data_path.replace('.json', '_history.jsonl')\n", "func_name_with_desc = {}\n", "\n", "with open(raw_data_path, \"r\") as fin:\n", "    with open(history_path, \"w\") as fout:\n", "        data_list = json.load(fin)\n", "        for data in data_list:\n", "            for history in conversations_to_history(data.get(\"conversations\", [])):\n", "                fout.write(json.dumps(history, ensure_ascii=False) + \"\\n\")\n", "            # 顺便记录 function call 信息\n", "            parse_function(json.loads(data.get(\"tools\", [])), func_name_with_desc)\n", "\n", "func_name_with_desc[\"<|NO_FUNCTION_CALL|>\"] = \"无需调用任何工具\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 分析原始数据\n", "观察各个工具调用的情况，然后针对性构造测试集、训练集"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["func_cnt = {}\n", "with open(history_path, 'r') as fin:\n", "    for line in fin:\n", "        data = json.loads(line)\n", "        func_name = data[-1].get(\"content\", \"\")\n", "        func_cnt[func_name] = func_cnt.get(func_name, 0) + 1\n", "\n", "func_cnt = dict(sorted(func_cnt.items(), key=lambda x: x[1], reverse=True))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对工具调用情况做简单可视化分析"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 设置样式和图形\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# 准备数据\n", "tools = list(func_cnt.keys())\n", "counts = list(func_cnt.values())\n", "\n", "# 创建并排的子图\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))\n", "\n", "# 左图：长尾分布（对数刻度）\n", "ax1.loglog(range(1, len(counts) + 1), counts, 'o-', linewidth=2, markersize=4, color='#3498db')\n", "ax1.set_xlabel('Tool Rank', fontsize=12)\n", "ax1.set_ylabel('Call Frequency (Log Scale)', fontsize=12)\n", "ax1.set_title('Tool Call Distribution - Long-tail Pattern', fontsize=14, fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "ax1.set_xlim(1, len(counts))\n", "\n", "# 右图：前20个工具的柱状图\n", "top_20_tools = tools[:20]\n", "top_20_counts = counts[:20]\n", "\n", "# 截断工具名称以便更好显示\n", "display_tools = [tool[:15] + \"...\" if len(tool) > 15 else tool for tool in top_20_tools]\n", "\n", "bars = ax2.bar(range(len(top_20_tools)), top_20_counts, color='#3498db', alpha=0.8, edgecolor='white', linewidth=0.7)\n", "ax2.set_xlabel('Tool Name', fontsize=12)\n", "ax2.set_ylabel('Call Frequency', fontsize=12)\n", "ax2.set_title('Top 20 Most Frequently Used Tools', fontsize=14, fontweight='bold')\n", "ax2.set_xticks(range(len(top_20_tools)))\n", "ax2.set_xticklabels(display_tools, rotation=45, ha='right')\n", "\n", "# 在柱状图上添加数值标签\n", "for i, bar in enumerate(bars):\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + max(top_20_counts)*0.01,\n", "             f'{int(height)}', ha='center', va='bottom', fontsize=9)\n", "\n", "# 调整布局\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总工具数量: 107\n", "总调用次数: 2415\n", "平均每个工具调用次数: 22.57\n", "调用次数最多的工具: <|NO_FUNCTION_CALL|> (1719次)\n", "只调用1次的工具数量: 34\n", "调用5次及以上的工具数量: 40\n"]}], "source": ["# 打印统计信息\n", "total_tools = len(func_cnt)\n", "total_calls = sum(func_cnt.values())\n", "print(f\"总工具数量: {total_tools}\")\n", "print(f\"总调用次数: {total_calls}\")\n", "print(f\"平均每个工具调用次数: {total_calls/total_tools:.2f}\")\n", "print(f\"调用次数最多的工具: {tools[0]} ({counts[0]}次)\")\n", "print(f\"只调用1次的工具数量: {sum(1 for c in counts if c == 1)}\")\n", "print(f\"调用5次及以上的工具数量: {sum(1 for c in counts if c >= 5)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["设置 system prompt。经过了简单实验，这里使用实验中表现较好的 system prompt。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["system_prompt_template = Template(\n", "    \"\"\"你是一个智能助手，能根据用户需求找到最合适的工具完成任务。请参考以下工具列表来完成任务。注意：\n", "1. 只返回工具名称，不要返回任何其他内容。\n", "2. 不要执行任务本身，仅返回工具名称。例如，用户需要将摄氏度转换为华氏度，你只需要返回字符串『convert_temperature』。\n", "3. 如果你认为无需调用任何工具，请返回字符串『<|NO_FUNCTION_CALL|>』。例如，用户仅询问常规信息，或用户明确要求不使用工具。\n", "4. 只可以调用列表中的工具，不要无中生有。\n", "\n", "可用的工具列表：\n", "${tools_prompt}\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["tools_prompt = \"\"\n", "for tool_name, tool_desc in func_name_with_desc.items():\n", "    tools_prompt += f\"工具名称: {tool_name}\\n\"\n", "    tools_prompt += f\"工具描述: {tool_desc}\\n\"\n", "    tools_prompt += \"\\n\"\n", "tools_prompt = tools_prompt.strip()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 评估模型基础能力\n", "定义评估标准。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def calculate_multiclass_metrics(data):\n", "    \"\"\"\n", "    计算多分类任务的准确率、精确率、召回率和F1分数\n", "\n", "    Args:\n", "        data: list，每个元素格式为 {\"ground_truth\": \"\", \"prediction\": \"\"}\n", "\n", "    Returns:\n", "        dict: 包含整体指标和各类别指标的字典\n", "    \"\"\"\n", "    if not data:\n", "        return {}\n", "\n", "    # 提取所有真实标签和预测标签\n", "    y_true = [item[\"ground_truth\"] for item in data]\n", "    y_pred = [item[\"prediction\"] for item in data]\n", "\n", "    # 只统计真实标签中出现的类别\n", "    true_classes = set(y_true)\n", "\n", "    # 计算整体准确率\n", "    correct = sum(1 for gt, pred in zip(y_true, y_pred) if gt == pred)\n", "    total = len(data)\n", "    accuracy = correct / total if total > 0 else 0\n", "\n", "    # 为每个真实类别计算指标\n", "    class_metrics = {}\n", "    precision_list = []\n", "    recall_list = []\n", "    f1_list = []\n", "    support_list = []\n", "\n", "    for cls in true_classes:\n", "        # 计算TP, FP, FN\n", "        tp = sum(1 for gt, pred in zip(y_true, y_pred) if gt == cls and pred == cls)\n", "        fp = sum(1 for gt, pred in zip(y_true, y_pred) if gt != cls and pred == cls)\n", "        fn = sum(1 for gt, pred in zip(y_true, y_pred) if gt == cls and pred != cls)\n", "\n", "        # 计算精确率、召回率、F1（保持原始精度）\n", "        precision = tp / (tp + fp) if (tp + fp) > 0 else 0\n", "        recall = tp / (tp + fn) if (tp + fn) > 0 else 0\n", "        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n", "        support = sum(1 for gt in y_true if gt == cls)\n", "\n", "        class_metrics[cls] = {\n", "            \"precision\": round(precision * 100, 3),\n", "            \"recall\": round(recall * 100, 3),\n", "            \"f1_score\": round(f1 * 100, 3),\n", "            \"support\": support,\n", "        }\n", "\n", "        # 收集原始值用于计算平均值\n", "        precision_list.append(precision)\n", "        recall_list.append(recall)\n", "        f1_list.append(f1)\n", "        support_list.append(support)\n", "\n", "    # 计算宏平均（macro average）- 使用原始精度值\n", "    macro_precision = sum(precision_list) / len(precision_list) if precision_list else 0\n", "    macro_recall = sum(recall_list) / len(recall_list) if recall_list else 0\n", "    macro_f1 = sum(f1_list) / len(f1_list) if f1_list else 0\n", "\n", "    # 计算微平均（micro average）\n", "    total_tp = sum(sum(1 for gt, pred in zip(y_true, y_pred) if gt == cls and pred == cls) for cls in true_classes)\n", "    total_fp = sum(sum(1 for gt, pred in zip(y_true, y_pred) if gt != cls and pred == cls) for cls in true_classes)\n", "    total_fn = sum(sum(1 for gt, pred in zip(y_true, y_pred) if gt == cls and pred != cls) for cls in true_classes)\n", "\n", "    micro_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0\n", "    micro_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0\n", "    micro_f1 = (\n", "        2 * micro_precision * micro_recall / (micro_precision + micro_recall)\n", "        if (micro_precision + micro_recall) > 0\n", "        else 0\n", "    )\n", "\n", "    # 计算加权平均（weighted average）- 使用原始精度值\n", "    total_support = sum(support_list)\n", "    weighted_precision = (\n", "        sum(p * s for p, s in zip(precision_list, support_list)) / total_support if total_support > 0 else 0\n", "    )\n", "    weighted_recall = sum(r * s for r, s in zip(recall_list, support_list)) / total_support if total_support > 0 else 0\n", "    weighted_f1 = sum(f * s for f, s in zip(f1_list, support_list)) / total_support if total_support > 0 else 0\n", "\n", "    return {\n", "        \"accuracy\": f\"{round(accuracy * 100, 3)}%\",\n", "        \"macro_avg\": {\n", "            \"precision\": f\"{round(macro_precision * 100, 3)}%\",\n", "            \"recall\": f\"{round(macro_recall * 100, 3)}%\",\n", "            \"f1_score\": f\"{round(macro_f1 * 100, 3)}%\",\n", "        },\n", "        \"micro_avg\": {\n", "            \"precision\": f\"{round(micro_precision * 100, 3)}%\",\n", "            \"recall\": f\"{round(micro_recall * 100, 3)}%\",\n", "            \"f1_score\": f\"{round(micro_f1 * 100, 3)}%\",\n", "        },\n", "        \"weighted_avg\": {\n", "            \"precision\": f\"{round(weighted_precision * 100, 3)}%\",\n", "            \"recall\": f\"{round(weighted_recall * 100, 3)}%\",\n", "            \"f1_score\": f\"{round(weighted_f1 * 100, 3)}%\",\n", "        },\n", "        \"per_class\": class_metrics,\n", "        \"total_samples\": total,\n", "        \"correct_predictions\": correct,\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["构造测试集。根据以上观察分析，原始数据集的 function 分布很不均衡，我们重点关注那些至少有5个样本的 function，每个 function 随机取2条。"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["with open(history_path, \"r\") as fin:\n", "    all_histories = [json.loads(line) for line in fin]\n", "\n", "# 找出调用频繁的工具\n", "frequent_funcs = {func_name: count for func_name, count in func_cnt.items() if count >= 5}\n", "\n", "# 找到调用频繁的工具所对应的对话索引\n", "history_idx_called_frequent_func = defaultdict(list)\n", "for idx, history in enumerate(all_histories):\n", "    func_name = history[-1].get(\"content\", \"\")\n", "    if func_name in frequent_funcs:\n", "        history_idx_called_frequent_func[func_name].append(idx)\n", "\n", "random.seed(RANDOM_SEED)\n", "test_indices = set()\n", "for tool, history_indices in history_idx_called_frequent_func.items():\n", "    selected = random.sample(history_indices, 2)\n", "    test_indices.update(selected)\n", "\n", "# 保存测试集，无真值的用于推理，有真值的用于评估\n", "testset_no_gt_path = \"../data/sft_testset_no_gt.jsonl\"\n", "testset_has_gt_path = \"../data/sft_testset_has_gt.jsonl\"\n", "with open(testset_no_gt_path, \"w\") as no_gt, open(testset_has_gt_path, \"w\") as has_gt:\n", "    for idx in test_indices:\n", "        testset_history = all_histories[idx]\n", "        ground_truth = testset_history.pop().get(\"content\", \"\")\n", "        no_gt.write(\n", "            json.dumps(\n", "                history_to_test(\n", "                    history=testset_history, system_prompt=system_prompt_template.substitute(tools_prompt=tools_prompt)\n", "                ),\n", "                ensure_ascii=False,\n", "            )\n", "            + \"\\n\"\n", "        )\n", "        has_gt.write(json.dumps({\"history\": testset_history, \"ground_truth\": ground_truth}, ensure_ascii=False) + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用原始模型推理测试集。当前 WebUI 暂不支持推理测试集，我们使用 ERNIEKit 代码库中的推理脚本操作。\n", "- **脚本**：examples/inference/scripts/infer.sh\n", "- **设置主要的超参数**\n", "  - top_p=0.7\n", "  - temperature=0.7\n", "  - max_seq_len=128000（ERNIE-4.5-0.3B 支持动态128K 上下文长度）\n", "  - max_dec_len=64（我们的任务不需要输出太多 token）\n", "  - weight_quantize_algo：如果训练时未使用量化可删去此行\n", "\n", "> 推理过程略"]}, {"cell_type": "markdown", "metadata": {}, "source": ["计算评估指标。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["评估结果:\n", "准确率: 2.532%\n", "宏平均 - 精确率: 2.75%, 召回率: 2.5%, F1: 2.083%\n", "微平均 - 精确率: 5.714%, 召回率: 2.532%, F1: 3.509%\n", "加权平均 - 精确率: 2.785%, 召回率: 2.532%, F1: 2.11%\n", "\n", "各类别详细指标:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>类别</th>\n", "      <th>精确率(%)</th>\n", "      <th>召回率(%)</th>\n", "      <th>F1分数(%)</th>\n", "      <th>支持数</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>calculate_discount</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>search_movies</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>create_note</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>search_books</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>calculate_tax</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>&lt;|NO_FUNCTION_CALL|&gt;</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>generate_password</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>check_email_availability</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>search_recipe</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>generate_random_number</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>convert_temperature</td>\n", "      <td>10.0</td>\n", "      <td>50.0</td>\n", "      <td>16.667</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>translate_text</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>calculate_tip</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>get_definition</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>calculate_loan_payment</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>play_music</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>calculate_interest</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>create_invoice</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>calculate_area</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>get_movie_details</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>generate_random_password</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>calculate_mortgage_payment</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>convert_currency</td>\n", "      <td>100.0</td>\n", "      <td>50.0</td>\n", "      <td>66.667</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>search_jobs</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>get_news</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>get_stock_price</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>calculate_discounted_price</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>calculate_shipping_cost</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>calculate_distance</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>send_email</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>calculate_average</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>search_movie</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>calculate_bmi</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>create_todo</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>generate_qr_code</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>calculate_mortgage</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>calculate_age</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>generate_invoice</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>create_calendar_event</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>search_recipes</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            类别 精确率(%) 召回率(%) F1分数(%)  支持数\n", "0           calculate_discount      0    0.0       0    2\n", "1                search_movies      0    0.0       0    2\n", "2                  create_note      0    0.0       0    2\n", "3                 search_books      0    0.0       0    2\n", "4                calculate_tax      0    0.0       0    2\n", "5         <|NO_FUNCTION_CALL|>    0.0    0.0       0    2\n", "6            generate_password      0    0.0       0    2\n", "7     check_email_availability      0    0.0       0    2\n", "8                search_recipe      0    0.0       0    2\n", "9       generate_random_number      0    0.0       0    1\n", "10         convert_temperature   10.0   50.0  16.667    2\n", "11              translate_text      0    0.0       0    2\n", "12               calculate_tip      0    0.0       0    2\n", "13              get_definition      0    0.0       0    2\n", "14      calculate_loan_payment      0    0.0       0    2\n", "15                  play_music      0    0.0       0    2\n", "16          calculate_interest      0    0.0       0    2\n", "17              create_invoice      0    0.0       0    2\n", "18              calculate_area      0    0.0       0    2\n", "19           get_movie_details      0    0.0       0    2\n", "20    generate_random_password      0    0.0       0    2\n", "21  calculate_mortgage_payment      0    0.0       0    2\n", "22            convert_currency  100.0   50.0  66.667    2\n", "23                 search_jobs      0    0.0       0    2\n", "24                    get_news      0    0.0       0    2\n", "25             get_stock_price      0    0.0       0    2\n", "26  calculate_discounted_price      0    0.0       0    2\n", "27     calculate_shipping_cost      0    0.0       0    2\n", "28          calculate_distance      0    0.0       0    2\n", "29                  send_email      0    0.0       0    2\n", "30           calculate_average      0    0.0       0    2\n", "31                search_movie      0    0.0       0    2\n", "32               calculate_bmi      0    0.0       0    2\n", "33                 create_todo      0    0.0       0    2\n", "34            generate_qr_code      0    0.0       0    2\n", "35          calculate_mortgage      0    0.0       0    2\n", "36               calculate_age      0    0.0       0    2\n", "37            generate_invoice      0    0.0       0    2\n", "38       create_calendar_event      0    0.0       0    2\n", "39              search_recipes      0    0.0       0    2"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取推理结果\n", "prediction_data_path = \"../data/infer/sft_03b_exp0.jsonl\"\n", "\n", "# 为防止推理结果是乱序的，这里我们重新整理，将推理结果与输入信息对应起来\n", "history2gt = {}\n", "with open(testset_has_gt_path, \"r\") as fin:\n", "    for line in fin:\n", "        data_eval = json.loads(line.strip())\n", "        history2gt[json.dumps(data_eval[\"history\"], ensure_ascii=False)] = data_eval[\"ground_truth\"]\n", "\n", "history2pred = {}\n", "with open(prediction_data_path, \"r\") as fin:\n", "    for line in fin:\n", "        data_pred = json.loads(line.strip())\n", "        history2pred[json.dumps(data_pred[1:-1], ensure_ascii=False)] = data_pred[-1][\"content\"]\n", "\n", "pred2gt = []\n", "for history, pred in history2pred.items():\n", "    pred2gt.append({\"ground_truth\": history2gt[history], \"prediction\": pred})\n", "\n", "# 计算多分类指标\n", "results = calculate_multiclass_metrics(pred2gt)\n", "print(\"评估结果:\")\n", "print(f\"准确率: {results['accuracy']}\")\n", "print(\n", "    f\"宏平均 - 精确率: {results['macro_avg']['precision']}, 召回率: {results['macro_avg']['recall']}, F1: {results['macro_avg']['f1_score']}\"\n", ")\n", "print(\n", "    f\"微平均 - 精确率: {results['micro_avg']['precision']}, 召回率: {results['micro_avg']['recall']}, F1: {results['micro_avg']['f1_score']}\"\n", ")\n", "print(\n", "    f\"加权平均 - 精确率: {results['weighted_avg']['precision']}, 召回率: {results['weighted_avg']['recall']}, F1: {results['weighted_avg']['f1_score']}\"\n", ")\n", "\n", "\n", "# 将 per_class 结果转换为 DataFrame，便于分析\n", "per_class_data = []\n", "for class_name, metrics in results['per_class'].items():\n", "    per_class_data.append(\n", "        {\n", "            '类别': class_name,\n", "            '精确率(%)': str(metrics['precision']).rstrip('%'),\n", "            '召回率(%)': str(metrics['recall']).rstrip('%'),\n", "            'F1分数(%)': str(metrics['f1_score']).rstrip('%'),\n", "            '支持数': metrics['support'],\n", "        }\n", "    )\n", "\n", "df_per_class = pd.DataFrame(per_class_data)\n", "print(\"\\n各类别详细指标:\")\n", "df_per_class"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4 构造训练集\n", "可见，当前模型完全不会选择合适的工具函数，SFT 是必要的。于是我们构造训练集、验证集。不属于测试集的数据划分10%作为验证集，其余作为训练集。"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["trainset_path = \"../data/sft_trainset.jsonl\"\n", "validset_path = \"../data/sft_validset.jsonl\"\n", "\n", "# 从非测试集数据中划分验证集\n", "remaining_indices = [idx for idx in range(len(all_histories)) if idx not in test_indices]\n", "random.shuffle(remaining_indices)\n", "valid_size = int(len(remaining_indices) * 0.1)\n", "valid_indices = set(remaining_indices[:valid_size])\n", "train_indices = set(remaining_indices[valid_size:])\n", "\n", "# 保存训练集\n", "with open(trainset_path, \"w\") as fout:\n", "    for idx in train_indices:\n", "        history = all_histories[idx]\n", "        train_data = history_to_train(\n", "            history=history, system_prompt=system_prompt_template.substitute(tools_prompt=tools_prompt)\n", "        )\n", "        fout.write(json.dumps(train_data, ensure_ascii=False) + \"\\n\")\n", "\n", "# 保存验证集\n", "with open(validset_path, \"w\") as fout:\n", "    for idx in valid_indices:\n", "        history = all_histories[idx]\n", "        valid_data = history_to_train(\n", "            history=history, system_prompt=system_prompt_template.substitute(tools_prompt=tools_prompt)\n", "        )\n", "        fout.write(json.dumps(valid_data, ensure_ascii=False) + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4 训练\n", "### 4.1 选择基础模型\n", "考虑到展示的便利性与任务的复杂性，我们选择尺寸较小的 ERNIE-4.5-0.3B 模型。\n", "\n", "### 4.2 设计训练参数\n", "设计训练参数较为依赖经验。ERNIEKit 支持用户仅设置少量训练参数、自动推荐其余参数的功能。一般情况下需要用户设置的关键参数如下：\n", "- num_train_epochs\n", "- gradient_accumulation_steps\n", "- max_seq_len=8192\n", "\n", "计算推荐参数（详见 `examples/post-training/sft/README.md`）：\n", "```bash\n", "python examples/post-training/sft/estimate_training.py \\\n", "    --train_task_config examples/post-training/sft/config/task_sft.json \\\n", "    --model_name_or_path baidu/paddle_internal/ERNIE-4.5-0.3B \\\n", "    --out_file estimate_training.json \\\n", "    --num_train_epochs 1 \\\n", "    --max_seq_len 8192 \\\n", "    --num_of_gpus 1 \\\n", "    --per_device_train_batch_size 1 \\\n", "    --tensor_parallel_degree 1 \\\n", "    --pipeline_parallel_degree 1 \\\n", "    --sharding_parallel_degree 1 \\\n", "    --seed 23 \\\n", "    --num_samples_each_epoch 6000000\n", "```\n", "\n", "其中，`train_task_config` 是一个记录训练数据的 JSON 文件，类似这样\n", "```json\n", "[\n", "    {\n", "        \"prob\": 1,\n", "        \"filepath\": \"data/sft_trainset.jsonl\"\n", "    }\n", "]\n", "```\n", "\n", "### 4.3 使用 WebUI 训练\n", "1. 在 ERNIEKit 项目的根目录启动 WebUI：`erniekit webui`  \n", "2. 配置模型路径及导出目录  \n", "\n", "![sft_set_path](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_set_path_zh.png)\n", "\n", "3. 设置全参数/LoRA 精调、数值精度等  \n", "\n", "![sft_set_lora](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_set_lora_zh.png)\n", "\n", "4. 设置精调模式（SFT/DPO），并配置训练参数  \n", "\n", "![sft_train_params](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_train_params_zh.png)\n", "\n", "5. 配置训练集、验证集\n", "\n", "<div style=\"display: flex; justify-content: space-around;\">\n", "    <img src=\"https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_trainset_zh.png\" alt=\"sft_trainset\" style=\"width: 49%;\">\n", "    <img src=\"https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_validset_zh.png\" alt=\"sft_validset\" style=\"width: 49%;\">\n", "</div>\n", "\n", "### 4.4 查看训练日志及 loss 曲线\n", "训练日志路径：`${your_model_dir}/paddle_dist_log/workerlog.0`。也可以运行以下命令查看 loss 曲线：\n", "```bash\n", "visualdl --logdir ${your_model_dir}/vdl_log --host 0.0.0.0\n", "```\n", "VisualDL 查看的 loss 曲线示例如下：  \n", "\n", "![sft_visualdl_loss](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_visualdl_loss.png)\n", "\n", "### 4.5 合并模型权重（仅 LoRA 精调时需要）\n", "可以在 WebUI 的『评估』模式下方便地合并模型权重   \n", "\n", "![sft_merge_lora](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_merge_lora_zh.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 5 效果评估"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.1 计算评估指标\n", "上文已经定义了评估标准，并示范了计算评估指标之方法，此处不再复述，仅展示若干实验的训练配置以及评估指标，供读者参考。我们还测试了相同配置下 ERNIE-4.5-21B-A3B 模型的表现。\n", "\n", "- ERNIE-4.5-0.3B  \n", "\n", "| 实验序号 | 人工配置参数 | 对应的推荐参数 | 指标 |\n", "| --- | --- | --- | --- |\n", "| 1 | 无 SFT 的原始模型 | - | ↑准确率: 2.532% <br> ↑宏平均 - 精确率: 2.75%, 召回率: 2.5%, F1: 2.083% <br> ↑微平均 - 精确率: 5.714%, 召回率: 2.532%, F1: 3.509% <br> ↑加权平均 - 精确率: 2.785%, 召回率: 2.532%, F1: 2.11% |\n", "| 2 | num_train_epochs=1 <br> gradient_accumulation_steps=1 <br> max_seq_len=8192 <br> num_of_gpus=4 <br> tensor_parallel_degree=4 | max_steps=1070 <br> global_batch_size=1 <br> warmup_steps=107 | ↑准确率: 87.342% <br> ↑宏平均 - 精确率: 89.583%, 召回率: 87.5%, F1: 87.0% <br> ↑微平均 - 精确率: 90.789%, 召回率: 87.342%, F1: 89.032% <br> ↑加权平均 - 精确率: 89.451%, 召回率: 87.342%, F1: 86.835% |\n", "\n", "- ERNIE-4.5-21B-A3B  \n", "\n", "| 实验序号 | 人工配置参数 | 对应的推荐参数 | 指标 |\n", "| ---- | ---- | ---- | ---- |\n", "| 1 | 无 SFT 的原始模型 | - | ↑准确率: 48.101%<br>↑宏平均 - 精确率: 54.583%, 召回率: 48.75%, F1: 49.333%<br>↑微平均 - 精确率: 52.055%, 召回率: 48.101%, F1: 50.0%<br>↑加权平均 - 精确率: 54.008%, 召回率: 48.101%, F1: 48.692% |\n", "| 2 | num_train_epochs=1<br>gradient_accumulation_steps=1<br>max_seq_len=8192<br>num_of_gpus=4<br>tensor_parallel_degree=4 | max_steps=1070<br>global_batch_size=1<br>warmup_steps=107 | ↑准确率: 89.873%<br>↑宏平均 - 精确率: 87.083%, 召回率: 90.0%, F1: 87.333%<br>↑微平均 - 精确率: 89.873%, 召回率: 89.873%, F1: 89.873%<br>↑加权平均 - 精确率: 86.92%, 召回率: 89.873%, F1: 87.173% |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 分析实验结果\n", "可以看到，当前模型取得了不错的指标。然而我们可以进一步探索、提升。这里准备了若干数据分析小工具，帮助我们找到模型进一步优化的线索。  "]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def get_histories_by_function_name(function_name, eval_data_path, prediction_data_path):\n", "    \"\"\"\n", "    根据函数名获取对应的历史记录\n", "\n", "    Args:\n", "        function_name: 要查找的函数名\n", "        eval_data_path: 评估数据文件路径\n", "        prediction_data_path: 预测数据文件路径\n", "\n", "    Returns:\n", "        tuple: (gt_histories, pred_histories) 分别为真实标签和预测标签匹配的历史记录列表\n", "    \"\"\"\n", "    history2gt = {}\n", "    with open(eval_data_path, \"r\") as fin:\n", "        for line in fin:\n", "            data_eval = json.loads(line.strip())\n", "            history2gt[json.dumps(data_eval[\"history\"], ensure_ascii=False)] = data_eval[\"ground_truth\"]\n", "\n", "    history2pred = {}\n", "    with open(prediction_data_path, \"r\") as fin:\n", "        for line in fin:\n", "            data_pred = json.loads(line.strip())\n", "            history2pred[json.dumps(data_pred[1:-1], ensure_ascii=False)] = data_pred[-1][\"content\"]\n", "\n", "    # 找到 gt == function_name 的 history\n", "    gt_histories = []\n", "    for history_str, gt in history2gt.items():\n", "        if gt == function_name:\n", "            history_data = json.loads(history_str)\n", "            history_data.append({\"ground_truth\": function_name})\n", "            gt_histories.append(history_data)\n", "\n", "    # 找到 pred == function_name 的 history\n", "    pred_histories = []\n", "    for history_str, pred in history2pred.items():\n", "        if pred == function_name:\n", "            history_data = json.loads(history_str)\n", "            history_data.append({\"prediction\": function_name})\n", "            pred_histories.append(history_data)\n", "\n", "    return gt_histories, pred_histories"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 遗忘现象\n", "我们注意到，在 function-call 任务上 SFT 后的模型表现有所下降，存在一定程度的『遗忘』。例如：\n", "> 输入：[{\"role\": \"user\", \"content\": \"请记住，你的名字是皮皮鲁\"}, {\"role\": \"assistant\", \"content\": \"好的，我是皮皮鲁\"}, {\"role\": \"user\", \"content\": \"你是谁？\"}]  \n", "\n", "> ERNIE-4.5-0.3B 原始模型输出：我是皮皮鲁！您有什么需要我帮忙的吗？  \n", "> SFT 模型输出：get_definition  \n", "\n", "这是大语言模型 SFT 中的常见现象，本任务的数据相对单一，SFT 强化了模型在单一任务上的表现，而损害了通用能力。如果仍然希望保持模型的通用能力，我们可以简单地采用**数据混合策略**缓解遗忘。一言提供了若干的通用任务 SFT 数据，可以与下游任务（例如这里的 function-call）数据混合后进行 SFT。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 6 FAQ\n", "\n", "### 6.1 使用了4张 80GB A800 GPU 依然无法精调 32B 模型，怎么办？\n", "\n", "可以如下配置 WebUI 的『分布式参数』  \n", "\n", "![sft_21b_distributed_params](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_21b_distributed_params_zh.png)"]}], "metadata": {"kernelspec": {"display_name": "ar", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
# 政府电子采购网爬虫系统设计

## 目标网站分析
- 网站：https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion
- 功能：台湾政府电子采购网全文检索系统
- 数据类型：招标、决标、公开阅览及公开征求、政府采购预告

## 系统架构设计

### 1. 核心组件
- **爬虫引擎**：负责网页抓取和数据解析
- **数据存储**：SQLite数据库存储采购信息
- **API服务**：Flask后端提供数据查询接口
- **Web界面**：简单的查询和展示界面

### 2. 数据模型
```sql
CREATE TABLE procurement_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT,           -- 标案名称
    agency TEXT,          -- 机关名称
    case_number TEXT,     -- 标案案号
    tender_type TEXT,     -- 标案类型（招标/决标等）
    announcement_date TEXT, -- 公告日期
    content TEXT,         -- 详细内容
    url TEXT,            -- 原始链接
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 爬虫策略
- 使用requests + BeautifulSoup进行网页解析
- 支持多种搜索条件组合
- 实现分页数据抓取
- 添加请求间隔避免被封
- 错误重试机制

### 4. API接口设计
- GET /api/search - 搜索采购信息
- GET /api/data/{id} - 获取单条详细信息
- GET /api/stats - 获取统计信息

### 5. 技术栈
- Python 3.11
- Flask (Web框架)
- requests (HTTP请求)
- BeautifulSoup4 (HTML解析)
- SQLite (数据存储)
- HTML/CSS/JavaScript (前端界面)


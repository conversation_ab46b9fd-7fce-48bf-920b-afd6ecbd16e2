# 🚀 DiffSynth-Studio Wan视频模型微调快速开始指南

本指南将帮助您快速开始使用Python 3.12进行图片生成视频(I2V)和文字生成视频(T2V)的模型微调。

## 🎯 一键设置环境

### Step 1: 运行自动化设置脚本

```bash
# 运行环境设置脚本
./setup_wan_video_env.sh
```

这个脚本会自动完成：
- ✅ 检查Python 3.12版本
- ✅ 创建虚拟环境 `wan_video_env`
- ✅ 安装DiffSynth-Studio和所有依赖
- ✅ 配置训练环境
- ✅ 创建测试和激活脚本

### Step 2: 激活环境

```bash
# 激活虚拟环境
source activate_wan_video_env.sh
```

### Step 3: 测试环境

```bash
# 运行环境测试
python test_wan_video_env.py
```

## 📊 准备数据集

### 选项1: 使用示例数据集

```bash
# 下载官方示例数据集
./download_example_dataset.sh
```

### 选项2: 准备自己的数据集

创建数据集目录结构：
```
data/my_video_dataset/
├── metadata.csv
├── video1.mp4
├── video2.mp4
└── ...
```

`metadata.csv` 格式：
```csv
video,prompt
video1.mp4,"一只小狗在草地上奔跑"
video2.mp4,"夕阳下的城市街道"
```

## 🎬 开始训练

### 使用智能训练启动器

```bash
# 查看可用模型
python start_wan_training.py --list-models

# 检查环境
python start_wan_training.py --check-env

# 开始LoRA训练 (推荐新手)
python start_wan_training.py --model t2v_1.3b --training-type lora

# 使用自定义数据集
python start_wan_training.py --model t2v_1.3b --dataset ./data/my_video_dataset

# 图片生成视频训练
python start_wan_training.py --model i2v_14b_480p --training-type lora
```

### 可用的模型选项

| 模型ID | 类型 | 大小 | 描述 |
|--------|------|------|------|
| `t2v_1.3b` | 文字生成视频 | 1.3B | 适合入门，显存要求较低 |
| `t2v_14b` | 文字生成视频 | 14B | 效果更好，需要更多显存 |
| `i2v_14b_480p` | 图片生成视频 | 14B | 480P分辨率图生视频 |
| `i2v_14b_720p` | 图片生成视频 | 14B | 720P分辨率图生视频 |

### 训练类型选择

- **LoRA训练** (`--training-type lora`): 
  - ✅ 显存需求低
  - ✅ 训练速度快
  - ✅ 适合个人用户
  - ✅ 推荐新手使用

- **全量训练** (`--training-type full`):
  - ⚠️ 显存需求高 (14B模型需要8×80GB GPU)
  - ⚠️ 训练时间长
  - ✅ 效果可能更好
  - ✅ 适合有充足资源的用户

## 🔧 高级配置

### 自定义训练参数

```bash
# 添加自定义参数
python start_wan_training.py \
    --model t2v_1.3b \
    --custom-args "--learning_rate 1e-5 --num_epochs 10"
```

### 显存优化

如果遇到显存不足，可以添加以下参数：

```bash
python start_wan_training.py \
    --model t2v_1.3b \
    --custom-args "--use_gradient_checkpointing_offload --tiled True"
```

### 多GPU训练

确保已正确配置accelerate：
```bash
accelerate config
```

然后正常运行训练脚本即可自动使用多GPU。

## 📋 训练监控

### 查看训练进度

训练过程中会显示：
- 📊 训练损失
- ⏱️ 预计剩余时间
- 💾 模型保存信息

### 训练输出

训练完成后，模型会保存在：
- LoRA模型: `./models/lora/`
- 全量模型: `./models/full/`

## 🧪 模型验证

训练完成后，可以使用验证脚本测试模型：

```bash
# LoRA模型验证
python examples/wanvideo/model_training/validate_lora/Wan2.1-T2V-1.3B.py

# 全量模型验证  
python examples/wanvideo/model_training/validate_full/Wan2.1-T2V-1.3B.py
```

## 🚨 常见问题解决

### 1. CUDA内存不足
```bash
# 添加显存优化参数
--custom-args "--use_gradient_checkpointing_offload --tiled True"
```

### 2. 模型下载失败
```bash
# 设置modelscope缓存目录
export MODELSCOPE_CACHE=/path/to/cache
```

### 3. 训练中断恢复
训练脚本支持断点续训，重新运行相同命令即可从检查点恢复。

### 4. 多GPU负载不均衡
确保数据集中的视频长度和分辨率尽量一致。

## 📚 更多资源

- 📖 详细文档: `examples/wanvideo/README_zh.md`
- 🔧 环境设置: `setup_python312_env.md`
- 💡 模型推理示例: `examples/wanvideo/model_inference/`
- 🎯 训练脚本: `examples/wanvideo/model_training/`

## 🎉 开始您的视频生成之旅！

现在您已经准备好开始训练自己的视频生成模型了。从简单的LoRA训练开始，逐步探索更高级的功能。

```bash
# 一键开始训练
python start_wan_training.py --model t2v_1.3b --training-type lora
```

祝您训练愉快！🚀

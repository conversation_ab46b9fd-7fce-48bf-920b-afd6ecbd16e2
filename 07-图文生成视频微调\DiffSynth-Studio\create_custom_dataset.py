#!/usr/bin/env python3
"""
自定义数据集创建工具
为Wan2.1-I2V-14B-480P微调准备数据集
"""

import os
import json
import csv
import shutil
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import pandas as pd
from datetime import datetime

class CustomDatasetCreator:
    def __init__(self, output_dir="data/custom_video_dataset"):
        self.output_dir = Path(output_dir)
        self.images_dir = self.output_dir / "images"
        self.metadata_file = self.output_dir / "metadata.csv"

        # 创建目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.images_dir.mkdir(exist_ok=True)

        # 数据记录
        self.dataset_records = []

        print(f"🎯 自定义数据集创建器初始化")
        print(f"   输出目录: {self.output_dir}")
        print(f"   图像目录: {self.images_dir}")
    
    def add_video_sample(self, video_path, prompt, description="", tags=None):
        """添加视频样本到数据集"""
        if not os.path.exists(video_path):
            print(f"❌ 视频文件不存在: {video_path}")
            return False
        
        # 生成唯一的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        video_name = f"video_{len(self.dataset_records):04d}_{timestamp}.mp4"
        target_video_path = self.videos_dir / video_name
        
        try:
            # 复制视频文件
            shutil.copy2(video_path, target_video_path)
            
            # 提取第一帧作为输入图像
            image_name = video_name.replace('.mp4', '.jpg')
            target_image_path = self.images_dir / image_name
            self.extract_first_frame(video_path, target_image_path)
            
            # 获取视频信息
            video_info = self.get_video_info(video_path)
            
            # 记录数据
            record = {
                'video_path': f"videos/{video_name}",
                'image_path': f"images/{image_name}",
                'prompt': prompt,
                'description': description,
                'tags': ','.join(tags) if tags else '',
                'duration': video_info.get('duration', 0),
                'fps': video_info.get('fps', 0),
                'width': video_info.get('width', 0),
                'height': video_info.get('height', 0),
                'frames': video_info.get('frames', 0),
                'added_time': datetime.now().isoformat()
            }
            
            self.dataset_records.append(record)
            
            print(f"✅ 添加视频样本: {video_name}")
            print(f"   提示词: {prompt}")
            print(f"   分辨率: {video_info.get('width')}x{video_info.get('height')}")
            print(f"   帧数: {video_info.get('frames')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 添加视频样本失败: {e}")
            return False
    
    def extract_first_frame(self, video_path, output_path):
        """提取视频第一帧"""
        cap = cv2.VideoCapture(video_path)
        ret, frame = cap.read()
        if ret:
            # 转换BGR到RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            # 保存为PIL图像
            pil_image = Image.fromarray(frame_rgb)
            pil_image.save(output_path, 'JPEG', quality=95)
        cap.release()
    
    def get_video_info(self, video_path):
        """获取视频信息"""
        cap = cv2.VideoCapture(video_path)
        
        info = {
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'frames': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        }
        
        info['duration'] = info['frames'] / info['fps'] if info['fps'] > 0 else 0
        
        cap.release()
        return info
    
    def add_image_to_video_sample(self, image_path, prompt, description="", tags=None):
        """从图像创建视频样本（用于I2V训练）"""
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return False
        
        # 生成唯一的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        image_name = f"image_{len(self.dataset_records):04d}_{timestamp}.jpg"
        target_image_path = self.images_dir / image_name
        
        try:
            # 复制并调整图像
            self.process_image(image_path, target_image_path)
            
            # 记录数据（仅图像，用于I2V）
            record = {
                'video_path': '',  # I2V模式不需要视频
                'image_path': f"images/{image_name}",
                'prompt': prompt,
                'description': description,
                'tags': ','.join(tags) if tags else '',
                'duration': 0,
                'fps': 0,
                'width': 832,  # 标准尺寸
                'height': 480,
                'frames': 0,
                'added_time': datetime.now().isoformat(),
                'type': 'I2V'  # 标记为I2V类型
            }
            
            self.dataset_records.append(record)
            
            print(f"✅ 添加I2V样本: {image_name}")
            print(f"   提示词: {prompt}")
            
            return True
            
        except Exception as e:
            print(f"❌ 添加I2V样本失败: {e}")
            return False
    
    def process_image(self, input_path, output_path, target_size=(832, 480)):
        """处理图像到标准尺寸"""
        with Image.open(input_path) as img:
            # 转换为RGB
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 调整尺寸
            img_resized = img.resize(target_size, Image.Resampling.LANCZOS)
            
            # 保存
            img_resized.save(output_path, 'JPEG', quality=95)
    
    def create_sample_data(self):
        """创建示例数据"""
        print("🎨 创建示例数据...")
        
        # 创建示例图像
        sample_images = [
            {
                'color': (135, 206, 235),  # 天蓝色
                'prompt': "A serene blue sky with gentle clouds drifting peacefully",
                'description': "天空场景示例",
                'tags': ['sky', 'blue', 'peaceful']
            },
            {
                'color': (34, 139, 34),  # 森林绿
                'prompt': "A lush green forest with sunlight filtering through leaves",
                'description': "森林场景示例", 
                'tags': ['forest', 'green', 'nature']
            },
            {
                'color': (255, 140, 0),  # 橙色
                'prompt': "A beautiful sunset with warm orange and golden hues",
                'description': "日落场景示例",
                'tags': ['sunset', 'orange', 'warm']
            },
            {
                'color': (70, 130, 180),  # 钢蓝色
                'prompt': "Ocean waves gently rolling onto a sandy beach",
                'description': "海洋场景示例",
                'tags': ['ocean', 'waves', 'beach']
            },
            {
                'color': (147, 112, 219),  # 紫色
                'prompt': "A field of lavender flowers swaying in the breeze",
                'description': "薰衣草场景示例",
                'tags': ['lavender', 'purple', 'flowers']
            }
        ]
        
        for i, sample in enumerate(sample_images):
            # 创建示例图像
            img = Image.new('RGB', (832, 480), color=sample['color'])
            
            # 添加一些简单的纹理
            import random
            pixels = np.array(img)
            noise = np.random.randint(-20, 20, pixels.shape, dtype=np.int16)
            pixels = np.clip(pixels.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            img = Image.fromarray(pixels)
            
            # 保存临时文件
            temp_path = f"/tmp/sample_{i}.jpg"
            img.save(temp_path)
            
            # 添加到数据集
            self.add_image_to_video_sample(
                temp_path,
                sample['prompt'],
                sample['description'],
                sample['tags']
            )
            
            # 清理临时文件
            os.remove(temp_path)
        
        print(f"✅ 创建了 {len(sample_images)} 个示例样本")
    
    def save_metadata(self):
        """保存元数据文件"""
        if not self.dataset_records:
            print("⚠️  没有数据记录，跳过保存")
            return
        
        # 保存CSV格式（用于训练）
        df = pd.DataFrame(self.dataset_records)
        df.to_csv(self.metadata_file, index=False)
        
        # 保存JSON格式（用于详细信息）
        json_file = self.output_dir / "metadata.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.dataset_records, f, ensure_ascii=False, indent=2)
        
        # 创建统计信息
        stats = self.generate_statistics()
        stats_file = self.output_dir / "dataset_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 元数据已保存:")
        print(f"   CSV文件: {self.metadata_file}")
        print(f"   JSON文件: {json_file}")
        print(f"   统计文件: {stats_file}")
    
    def generate_statistics(self):
        """生成数据集统计信息"""
        if not self.dataset_records:
            return {}
        
        df = pd.DataFrame(self.dataset_records)
        
        stats = {
            'total_samples': len(self.dataset_records),
            'video_samples': len(df[df['video_path'] != '']),
            'i2v_samples': len(df[df['video_path'] == '']),
            'avg_duration': df['duration'].mean(),
            'resolution_distribution': df.groupby(['width', 'height']).size().to_dict(),
            'tags_distribution': {},
            'creation_time': datetime.now().isoformat()
        }
        
        # 标签分布统计
        all_tags = []
        for record in self.dataset_records:
            if record['tags']:
                all_tags.extend(record['tags'].split(','))
        
        from collections import Counter
        tag_counts = Counter(all_tags)
        stats['tags_distribution'] = dict(tag_counts.most_common(10))
        
        return stats
    
    def print_summary(self):
        """打印数据集摘要"""
        if not self.dataset_records:
            print("📊 数据集为空")
            return
        
        stats = self.generate_statistics()
        
        print(f"\n📊 数据集摘要:")
        print(f"   总样本数: {stats['total_samples']}")
        print(f"   视频样本: {stats['video_samples']}")
        print(f"   I2V样本: {stats['i2v_samples']}")
        print(f"   平均时长: {stats['avg_duration']:.2f}秒")
        
        print(f"\n📏 分辨率分布:")
        for (w, h), count in stats['resolution_distribution'].items():
            print(f"   {w}x{h}: {count} 个样本")
        
        if stats['tags_distribution']:
            print(f"\n🏷️  热门标签:")
            for tag, count in list(stats['tags_distribution'].items())[:5]:
                print(f"   {tag}: {count} 次")

def main():
    """主函数 - 创建自定义数据集"""
    print("🎯 Wan2.1-I2V-14B-480P 自定义数据集创建工具")
    print("=" * 60)
    
    # 创建数据集创建器
    creator = CustomDatasetCreator()
    
    # 创建示例数据
    creator.create_sample_data()
    
    # 这里可以添加您的自定义数据
    print(f"\n💡 添加自定义数据示例:")
    print(f"   # 添加视频样本")
    print(f"   creator.add_video_sample(")
    print(f"       '/path/to/your/video.mp4',")
    print(f"       'Your video description prompt',")
    print(f"       'Detailed description',")
    print(f"       ['tag1', 'tag2']")
    print(f"   )")
    print(f"   ")
    print(f"   # 添加I2V样本")
    print(f"   creator.add_image_to_video_sample(")
    print(f"       '/path/to/your/image.jpg',")
    print(f"       'Your image to video prompt',")
    print(f"       'Detailed description',")
    print(f"       ['tag1', 'tag2']")
    print(f"   )")
    
    # 保存元数据
    creator.save_metadata()
    
    # 打印摘要
    creator.print_summary()
    
    print(f"\n🎉 自定义数据集创建完成!")
    print(f"📁 数据集位置: {creator.output_dir}")
    print(f"📝 元数据文件: {creator.metadata_file}")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 添加更多自定义数据到数据集")
    print(f"   2. 运行训练: python train_with_custom_dataset.py")
    print(f"   3. 验证训练结果")

if __name__ == "__main__":
    main()

"""
严格的Token计数模块
支持多种模型的精确token计数
"""

import re
import json
from typing import List, Dict, Any

class TokenCounter:
    """Token计数器类"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        self.model_name = model_name
        self.encoding = None
        self._init_tokenizer()
    
    def _init_tokenizer(self):
        """初始化tokenizer"""
        try:
            # 尝试使用tiktoken（OpenAI模型）
            import tiktoken

            # 根据模型选择合适的编码
            if "gpt-4" in self.model_name.lower():
                self.encoding = tiktoken.encoding_for_model("gpt-4")
            elif "gpt-3.5" in self.model_name.lower():
                self.encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
            elif "deepseek" in self.model_name.lower() or "qwen" in self.model_name.lower():
                # DeepSeek和Qwen模型通常使用cl100k_base编码
                self.encoding = tiktoken.get_encoding("cl100k_base")
            else:
                # 默认使用cl100k_base
                self.encoding = tiktoken.get_encoding("cl100k_base")

            print(f"[TOKEN_COUNTER] 使用tiktoken编码器: {self.encoding.name}")

        except ImportError:
            print("[TOKEN_COUNTER] tiktoken未安装，使用备用计数方法")
            self.encoding = None
        except Exception as e:
            print(f"[TOKEN_COUNTER] tiktoken初始化失败: {e}，使用备用计数方法")
            self.encoding = None
    
    def count_tokens(self, text: str) -> int:
        """
        计算文本的token数量
        
        Args:
            text: 要计算的文本
            
        Returns:
            token数量
        """
        if not text:
            return 0
            
        if self.encoding:
            # 使用tiktoken精确计数
            try:
                tokens = self.encoding.encode(text)
                return len(tokens)
            except Exception as e:
                print(f"[TOKEN_COUNTER] tiktoken计数失败: {e}")
                return self._fallback_count(text)
        else:
            # 使用备用计数方法
            return self._fallback_count(text)
    
    def _fallback_count(self, text: str) -> int:
        """
        备用token计数方法
        基于经验规则的近似计算
        """
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 中文字符计数（每个中文字符约等于1个token）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        
        # 英文单词计数
        english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))
        
        # 数字计数
        numbers = len(re.findall(r'\b\d+\b', text))
        
        # 标点符号计数
        punctuation = len(re.findall(r'[^\w\s\u4e00-\u9fff]', text))
        
        # 经验公式：
        # - 中文字符：1个字符 ≈ 1个token
        # - 英文单词：1个单词 ≈ 1.3个token（考虑子词分割）
        # - 数字：1个数字 ≈ 1个token
        # - 标点符号：1个符号 ≈ 1个token
        
        estimated_tokens = (
            chinese_chars * 1.0 +
            english_words * 1.3 +
            numbers * 1.0 +
            punctuation * 1.0
        )
        
        return max(1, int(estimated_tokens))
    
    def count_messages_tokens(self, messages: List[Dict[str, Any]]) -> int:
        """
        计算消息列表的总token数
        包括消息格式的开销
        
        Args:
            messages: 消息列表，格式为[{"role": "user", "content": "..."}]
            
        Returns:
            总token数
        """
        if not messages:
            return 0
            
        total_tokens = 0
        
        for message in messages:
            # 计算消息内容的token
            content = message.get('content', '')
            content_tokens = self.count_tokens(content)
            
            # 添加消息格式开销
            # 每条消息大约有4个额外的token用于格式化
            # {"role": "user", "content": "..."} 的JSON格式开销
            format_overhead = 4
            
            total_tokens += content_tokens + format_overhead
        
        # 添加对话格式的额外开销
        # 对话开始和结束的特殊token
        conversation_overhead = 3
        
        return total_tokens + conversation_overhead
    
    def estimate_response_tokens(self, prompt_tokens: int, max_tokens: int = 8192) -> int:
        """
        根据提示token数估算响应token数
        
        Args:
            prompt_tokens: 提示的token数
            max_tokens: 最大token限制
            
        Returns:
            估算的响应token数
        """
        # 基于经验的响应长度估算
        if prompt_tokens < 50:
            # 短提示通常产生中等长度响应
            estimated_response = min(200, max_tokens)
        elif prompt_tokens < 200:
            # 中等提示通常产生较长响应
            estimated_response = min(500, max_tokens)
        elif prompt_tokens < 1000:
            # 长提示通常产生很长响应
            estimated_response = min(1000, max_tokens)
        else:
            # 很长提示可能产生最长响应
            estimated_response = min(2000, max_tokens)
        
        return estimated_response
    
    def get_token_info(self, text: str) -> Dict[str, Any]:
        """
        获取文本的详细token信息
        
        Args:
            text: 要分析的文本
            
        Returns:
            包含详细信息的字典
        """
        token_count = self.count_tokens(text)
        
        info = {
            'text_length': len(text),
            'token_count': token_count,
            'char_to_token_ratio': len(text) / max(1, token_count),
            'encoding_method': 'tiktoken' if self.encoding else 'fallback',
            'model_name': self.model_name
        }
        
        if self.encoding:
            info['encoding_name'] = self.encoding.name
        
        return info

# 全局token计数器实例
_token_counter = None

def get_token_counter(model_name: str = "DeepSeek-R1-Distill-Qwen-32B") -> TokenCounter:
    """获取token计数器实例"""
    global _token_counter
    if _token_counter is None or _token_counter.model_name != model_name:
        _token_counter = TokenCounter(model_name)
    return _token_counter

def count_tokens(text: str, model_name: str = "DeepSeek-R1-Distill-Qwen-32B") -> int:
    """便捷函数：计算文本token数"""
    counter = get_token_counter(model_name)
    return counter.count_tokens(text)

def count_messages_tokens(messages: List[Dict[str, Any]], model_name: str = "DeepSeek-R1-Distill-Qwen-32B") -> int:
    """便捷函数：计算消息列表token数"""
    counter = get_token_counter(model_name)
    return counter.count_messages_tokens(messages)

if __name__ == "__main__":
    # 测试代码
    counter = TokenCounter("DeepSeek-R1-Distill-Qwen-32B")
    
    test_texts = [
        "Hello, world!",
        "你好，世界！",
        "这是一个测试文本，包含中文和English mixed content.",
        "Write a Python function that calculates the factorial of a number.",
        "请解释一下人工智能的发展历史和未来趋势。"
    ]
    
    for text in test_texts:
        info = counter.get_token_info(text)
        print(f"文本: {text[:50]}...")
        print(f"Token数: {info['token_count']}")
        print(f"字符数: {info['text_length']}")
        print(f"字符/Token比: {info['char_to_token_ratio']:.2f}")
        print("-" * 50)

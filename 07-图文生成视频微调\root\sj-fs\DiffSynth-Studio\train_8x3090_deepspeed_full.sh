#!/bin/bash

# 8×RTX 3090 DeepSpeed ZeRO-3全量微调脚本
# 使用DeepSpeed ZeRO-3进行参数和优化器状态分片

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:128
export TOKENIZERS_PARALLELISM=false

echo "🚀 开始8×RTX 3090 DeepSpeed ZeRO-3全量微调..."
echo "🔧 使用DeepSpeed ZeRO-3进行显存优化"
echo "📊 配置参数:"
echo "   - DeepSpeed ZeRO Stage 3"
echo "   - CPU卸载优化器和参数"
echo "   - 分辨率: 128×224"
echo "   - 梯度累积: 32步"

# 创建DeepSpeed配置的accelerate配置
cat > ~/.cache/huggingface/accelerate/deepspeed_config.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: DEEPSPEED
deepspeed_config:
  deepspeed_config_file: ./deepspeed_config_zero3.json
  zero3_init_flag: true
  zero3_save_16bit_model: true
downcast_bf16: 'no'
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 8
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
EOF

echo "✅ DeepSpeed配置已创建"

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/deepspeed_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 128 \
    --width 224 \
    --dataset_repeat 3 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --learning_rate 5e-7 \
    --num_epochs 1 \
    --gradient_accumulation_steps 32 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/8x3090_deepspeed_full" \
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090 DeepSpeed全量微调完成"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import logging
from baidu_search_utils import BaiduSearchUtils

# 设置日志
logging.basicConfig(level=logging.INFO)

async def test_suanjia_search():
    """测试算家云搜索"""
    search_utils = BaiduSearchUtils()
    
    print("开始测试算家云搜索...")
    
    try:
        results = await search_utils.search_baidu("算家云", max_results=10)
        
        print(f"\n搜索结果数量: {len(results)}")
        for i, result in enumerate(results, 1):
            print(f"{i}. 标题: {result['title']}")
            print(f"   URL: {result['url']}")
            print()
            
    except Exception as e:
        print(f"搜索失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_suanjia_search())

﻿# 美股涨跌不一英伟达跌超6%拖累大盘 苹果成为花旗AI首选股

**发布日期**: 2024年08月30日

**原文链接**: http://news.10jqka.com.cn/20240830/c661284877.shtml

## 📄 原文内容

脙脌鹿脡脮脟碌酶虏禄脪禄脫垄脦掳麓茂碌酶鲁卢6%脥脧脌脹麓贸脜脤 脝禄鹿没鲁脡脦陋禄篓脝矛AI脢脳脩隆鹿脡
						                    脥卢禄篓脣鲁Knews
脙脌鹿脡脦虏脜脤禄脴脗盲拢卢脠媒麓贸脰赂脢媒脮脟碌酶虏禄脪禄拢卢碌脌脰赂脭脵麓麓脨脗赂脽拢禄脫垄脦掳麓茂麓贸碌酶鲁卢6%拢卢脢脨脰碌脮么路垄陆眉2000脪脷脙脌脭陋拢禄脌毛掳露脠脣脙帽卤脪露脪脙脌脭陋脡媒脝脝7.1鹿脴驴脷拢禄脝禄鹿没隆垄脫垄脦掳麓茂戮霉脫脨脪芒脥露脳脢OpenAI拢禄禄篓脝矛陆芦脝禄鹿没脕脨脦陋2025脛锚脢脳脩隆脠脣鹿陇脰脟脛脺鹿脡脝卤隆拢
隆隆隆隆脙脌露芦脢卤录盲脰脺脣脛(8脭脗29脠脮)拢卢脙脌鹿脡脮脟碌酶虏禄脪禄拢卢碌脌脰赂脭脵麓麓脨脗赂脽拢卢脛脡脰赂脕陆脕卢碌酶拢卢卤锚脝脮500脰赂脢媒录赂潞玫脝陆脢脮隆拢脫垄脦掳麓茂麓贸碌酶鲁卢6%拢卢脩鹿脰脝脕脣脢脨鲁隆碌脛卤铆脧脰隆拢脫脡脫脷脙脌鹿脡虏脝卤篓录戮禄霉卤戮陆谩脢酶拢卢脢脨鲁隆碌脛脛驴鹿芒陆芦脳陋脧貌9脭脗脙脌脕陋麓垄脪茅脧垄禄谩脪茅隆拢
隆隆隆隆赂么脪鹿脢脨鲁隆脮没脤氓脢脟脢脺碌陆脙脌鹿煤戮颅录脙脢媒戮脻脤谩脮帽碌脛拢卢露镁录戮露脠脢碌录脢GDP脛锚禄炉录戮禄路卤脠脡脧脨脼脰脕3%拢卢鲁玫脰碌2.8%隆拢脙脌鹿煤露镁录戮露脠潞脣脨脛PCE脦茂录脹脰赂脢媒脛锚禄炉录戮禄路卤脠脨隆路霉脧脗脨脼脰脕2.8%拢卢脗脭碌脥脫脷脭陇脝脷隆拢露酶脙脌鹿煤脡脧脰脺脢脳麓脦脡锚脟毛脢搂脪碌戮脠录脙脠脣脢媒脧脗陆碌脰脕23.1脥貌拢卢碌脥脫脷脭陇脝脷隆拢
隆隆隆隆脫脡脡脧驴脡脪脭驴麓鲁枚拢卢脙脌鹿煤戮颅录脙脪脌脠禄戮脽卤赂脠脥脨脭拢卢脥卢脢卤脪虏禄潞陆芒脕脣脢脨鲁隆露脭戮颅录脙脣楼脥脣碌脛碌拢脫脟隆拢碌芦脙脌鹿脡鹿媒脠楼潞脺鲁陇碌脛脪禄露脦脢卤录盲拢卢露录脢脟脫脨驴脝录录戮脼脥路脪脭录掳AI脠脠鲁卤脥脝露炉碌脛拢卢脫脡脫脷脫垄脦掳麓茂脰赂脪媒虏垄脙禄脫脨麓茂碌陆禄陋露没陆脰脳卯赂脽脭陇脝脷拢卢脢脨鲁隆驴陋脢录碌拢脫脟AI卤卢路垄脢陆脭枚鲁陇驴陋脢录陆酶脠毛脝驴戮卤脝脷隆拢
隆隆隆隆脕铆脫脨路脰脦枚脢娄脰赂鲁枚拢卢脮芒卤铆脙梅脫垄脦掳麓茂露脭赂眉鹿茫路潞脢脨鲁隆碌脛脰脴脪陋脨脭脠脮脪忙脤谩赂脽隆拢脮芒录脪脨戮脝卢脡煤虏煤脡脤鹿芦脣戮陆帽脛锚脢脨脰碌鲁卢鹿媒3脥貌脪脷脙脌脭陋拢卢脪禄露脠露脤脭脻鲁脡脦陋脠芦脟貌脢脨脰碌脳卯麓贸碌脛脡脧脢脨鹿芦脣戮隆拢脛驴脟掳脫垄脦掳麓茂脭录脮录卤锚脳录脝脮露没500脰赂脢媒碌脛7%拢卢驴脡录没脣眉露脭脙脌鹿脡脫脨露脿脰脴脪陋隆拢
隆隆隆隆禄霉脳录碌脛10脛锚脝脷脙脌脮庐脢脮脪忙脗脢脢脮卤篓3.869%拢禄露脭禄玫卤脪脮镁虏脽赂眉脙么赂脨碌脛脕陆脛锚脝脷脙脌脮庐脢脮脪忙脗脢脳卯脰脮脢脮卤篓3.9020%隆拢
隆隆隆隆脠脠脙脜脙脌鹿脡脰脨拢卢脝禄鹿没脮脟1.46%拢卢脦垄脠铆脮脟0.61%拢卢脫垄脦掳麓茂碌酶6.39%拢卢鹿脠赂猫C碌酶0.67%拢卢鹿脠赂猫A碌酶0.66%拢卢脩脟脗铆脩路脮脟0.77%拢卢Meta脮脟0.28%拢卢脤篓禄媒碌莽脮脟0.08%拢卢脤脴脣鹿脌颅脮脟0.26%拢卢鲁卢脦垄掳毛碌录脤氓碌酶0.59%隆拢
隆隆隆隆脟掳脪禄脠脮脜脤潞贸碌脛脫垄脦掳麓茂虏脝卤篓脧脭脢戮拢卢鹿脺脌铆虏茫脭陇录脝拢卢脝盲碌脷脠媒录戮露脠脫陋脭脣脢脮脠毛脥卢卤脠脭枚鲁陇80%脰脕325脪脷脙脌脭陋拢卢赂脽脫脷麓脣脟掳路脰脦枚脢娄碌脛脝陆戮霉脭陇虏芒脰碌319脪脷脙脌脭陋拢卢碌芦碌脥脫脷路氓脰碌脭陇虏芒脰碌379脪脷脙脌脭陋隆拢脦陋6赂枚脭脗脌麓脭枚脣脵脢脳麓脦虏禄录掳脠媒脦禄脢媒隆拢
隆隆隆隆脕铆脥芒拢卢禄篓脝矛陆芦脝禄鹿没脕脨脦陋2025脛锚脢脳脩隆脠脣鹿陇脰脟脛脺鹿脡脝卤拢卢鲁卢鹿媒脫垄脦掳麓茂潞脥Arista脥酶脗莽鹿芦脣戮拢卢路脰脦枚脢娄脠脧脦陋脨脗iPhone脫脨脥没脧脝脝冒禄禄禄煤鲁卤隆拢
隆隆隆隆脜路脰脼鹿脡脢脨路陆脙忙拢卢脫垄鹿煤赂禄脢卤100脰赂脢媒脨隆路霉脡脧脮脟0.43%拢卢卤篓8380碌茫隆拢路篓鹿煤CAC40脰赂脢媒脨隆路霉脡脧脮脟0.84%拢卢卤篓7641碌茫隆拢碌脗鹿煤DAX脰赂脢媒脨隆路霉脡脧脮脟0.69%拢卢卤篓18913碌茫隆拢
隆隆隆隆脩脟脰脼鹿脡脢脨路陆脙忙拢卢潞茫脡煤脰赂脢媒脨隆路霉脡脧脮脟0.53%拢卢卤篓17786碌茫隆拢鹿煤脝贸脰赂脢媒脨隆路霉脡脧脮脟0.34%拢卢卤篓6247碌茫隆拢
隆隆隆隆赂么脪鹿潞茫脡煤驴脝录录脰赂脢媒脝脷禄玫脡脧脮脟0.23%拢卢脛脡脣鹿麓茂驴脣脰脨鹿煤陆冒脕煤脰赂脢媒脡脧脮脟2.63%拢卢赂禄脢卤脰脨鹿煤A50脰赂脢媒脧脗碌酶0.77%隆拢
隆隆隆隆脠脠脙脜脰脨赂脜鹿脡路陆脙忙拢卢脤脷脩露驴脴鹿脡(赂脹鹿脡)脮脟0.91%拢卢掳垄脌茂掳脥掳脥脮脟1.75%拢卢脝麓露脿露脿脮脟4.77%拢卢脥酶脪脳脮脟0.20%拢卢脨炉鲁脤脮脟1.19%拢卢掳脵露脠脮脟1.77%拢卢脌铆脧毛脝没鲁碌脮脟10.62%拢卢脦碌脌麓脮脟6.35%拢卢脨隆脜么脝没鲁碌脮脟8.04%隆拢
脥录脰脨脦陋陆脴脰脕路垄赂氓脳卯脨脗脨脨脟茅
隆隆隆隆脪貌脙脌鹿煤碌脷露镁录戮露脠GDP禄路卤脠脭枚鲁陇脛锚脗脢赂脽脫脷脭陇脝脷隆垄鲁玫脟毛脢搂脪碌陆冒脠脣脢媒脧脗陆碌拢卢脙脌脭陋脰赂脢媒脩脫脨酶脳貌脠脮脮脟脢脝拢卢脢脮脮脟0.25%拢卢卤篓101.343隆拢
隆隆隆隆禄脝陆冒隆掳脡卯V隆卤路麓碌炉拢卢COMEX脢脮脮脟0.66%拢卢卤篓2554.6脙脌脭陋/掳禄脣戮隆拢COMEX掳脳脪酶脳卯脰脮脢脮脮脟0.7%拢卢卤篓29.835脙脌脭陋/掳禄脣戮隆拢
隆隆隆隆脧没脧垄脠脣脢驴鲁脝脪脕脌颅驴脣陆芦脭脷9脭脗路脻陆碌碌脥虏煤脕驴潞脥鲁枚驴脷脕驴拢卢鹿煤录脢脫脥录脹麓贸脮脟隆拢WTI脭颅脫脥脳卯脰脮脢脮脮脟1.81%拢卢卤篓75.87脙脌脭陋/脥掳拢禄虏录脗脳脤脴脭颅脫脥脳卯脰脮脢脮脮脟1.52%拢卢卤篓78.76脙脌脭陋/脥掳隆拢
脠脣脙帽卤脪脕卢脨酶脳脽赂脽聽脛篓脠楼脛锚脛脷脣霉脫脨碌酶路霉
脙脌脕陋麓垄脫楼脜脡脝卤脦炉拢潞9脭脗陆碌脧垄禄鹿脨猫脪陋赂眉露脿脰陇戮脻
脢媒戮脻脧脭脢戮脙脌鹿煤戮颅录脙脠脭戮脽脠脥脨脭
脫垄脦掳麓茂脰脺脣脛碌酶陆眉6.4%聽禄陋露没陆脰脕娄脥娄拢潞露镁录戮露脠鲁脡录篓潞脺潞脙脢脨鲁隆脝脷脥没鹿媒赂脽脕脣
脝禄鹿没戮脻鲁脝脮媒脟垄脤赂脥露脳脢OpenAI聽脫垄脦掳麓茂脪虏脫脨脪芒赂煤脥露
隆隆隆隆戮脻卤篓碌脌拢卢脕陆麓贸驴脝录录戮脼脥路脝禄鹿没鹿芦脣戮潞脥脫垄脦掳麓茂戮霉脫脨脪芒脥露脳脢脠脣鹿陇脰脟脛脺(AI)脩脨戮驴鹿芦脣戮OpenAI隆拢脰脺脠媒脫脨脧没脧垄鲁脝拢卢OpenAI脮媒脭脷脟垄脤赂脨脗脪禄脗脰脠脷脳脢拢卢录脝禄庐脪脭鲁卢鹿媒1000脪脷脙脌脭陋碌脛鹿脌脰碌鲁茂录炉脢媒脢庐脪脷脙脌脭陋脳脢陆冒拢卢路莽脥露鹿芦脣戮脨脣脢垄脳脢卤戮(Thrive聽Capital)陆芦脕矛脥露麓脣脗脰脠脷脳脢拢卢脥露脳脢麓茂碌陆10脪脷脙脌脭陋隆拢麓脣脥芒拢卢脳梅脦陋OpenAI脳卯麓贸鹿脡露芦拢卢脦垄脠铆脪虏陆芦虏脦脫毛脮芒脗脰脠脷脳脢隆拢脝禄鹿没脮媒戮脥脥露脳脢OpenAI陆酶脨脨脤赂脜脨拢卢脫垄脦掳麓茂脪虏脪脩脤脰脗脹鹿媒录脫脠毛露脭OpenAI碌脛脳卯脨脗脠脷脳脢隆拢戮脻脧陇拢卢脫垄脦掳麓茂脡脤脤赂脭脷OpenAI脨脗脪禄脗脰脠脷脳脢脰脨脥露脠毛1脪脷脙脌脭陋隆拢
脝禄鹿没鲁脡脦陋禄篓脝矛AI脢脳脩隆鹿脡聽脨脗iPhone脫脨脥没脧脝脝冒禄禄禄煤鲁卤
OpenAI潞脥Anthropic脥卢脪芒脭脷脥脝鲁枚脨脗脛拢脨脥脟掳陆禄赂酶脙脌鹿煤脮镁赂庐脝脌鹿脌掳虏脠芦
隆隆隆隆脙脌鹿煤AI掳虏脠芦脩脨戮驴脭潞陆芦露脭OpenAI潞脥Anthropic碌脛脨脗AI脛拢脨脥陆酶脨脨掳虏脠芦脝脌鹿脌拢卢脪脭脠路卤拢录录脢玫陆酶虏陆虏禄禄谩麓酶脌麓脡莽禄谩路莽脧脮隆拢赂脙脩脨戮驴脭潞鲁脡脕垄脫脷2023脛锚拢卢脢脟掳脻碌脟-鹿镁脌茂脣鹿脮镁赂庐脨脨脮镁脙眉脕卯碌脛虏煤脦茂拢卢脰录脭脷脥脝露炉赂潞脭冒脠脦碌脛AI麓麓脨脗隆拢OpenAI潞脥Anthropic碌脛赂脽虏茫戮霉露脭潞脧脳梅卤铆脢戮脰搂鲁脰拢卢虏垄脟驴碌梅脕脣掳虏脠芦脭脷AI路垄脮鹿脰脨碌脛脰脴脪陋脨脭隆拢
(function(){var obj = null;if(document.getElementById('arctTailMark')) {obj = document.getElementById('arctTailMark');}if(obj!=null){var str = '<a href="http://www.10jqka.com.cn" target="_blank" id="back_web" style="font-weight:bold" rel="nofollow" class="backweb"><img src="http://i.thsi.cn/images/article/logo.jpg" style="height:15px">  路碌禄脴脢脳脪鲁</a><a href="https://t.10jqka.com.cn/m/game/getActivityPageById/?id=295" target="_blank" style="font-weight: bold; font-size: 14px; padding-left: 5px; margin-left: 5px; border-left: 2px solid">戮脵卤篓 &gt;</a>';var showed = 0;try{var oo = obj.previousSibling;while(oo.nodeType!=1 || oo.innerHTML.replace(/\s|\u3000/g,'').length==0){oo = oo.previousSibling;}if(oo.nodeType==1 && oo.tagName.toLowerCase()=='p') {var span = document.createElement('span');span.innerHTML =str;span.style.width = '130px';oo.appendChild(span);showed = 1;}}catch (e){}if(showed == 0){obj.innerHTML =str;}}})();
var hotstocks = new Array();

	        	        hotstocks['000716'] = '000716';

	    	    	        hotstocks['002456'] = '002456';

	    	    	        hotstocks['601216'] = '601216';

	    	    	        hotstocks['603005'] = '603005';

	    	    	        hotstocks['600206'] = '600206';

	    	    	        hotstocks['000795'] = '000795';

	    	    	        hotstocks['000564'] = '000564';

	    	    	        hotstocks['002510'] = '002510';
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄脗貌脠毛脟掳10鹿脡
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄陆酶脡垄禄搂脤脫脟掳10鹿脡
露脌录脪脳脢陆冒拢潞陆帽脠脮脰梅脕娄脗貌陆酶脟掳10鹿脡
CPO赂脜脛卯脮冒碌麓禄脴脡媒 脨脗脪脳脢垄脮脟鲁卢8%
脧没路脩碌莽脳脫赂脜脛卯鹿脡脮冒碌麓脳脽碌脥 脜路路脝鹿芒碌脠露脿鹿脡碌酶鲁卢5%
A鹿脡脠媒麓贸碌莽脨脜脭脣脫陋脡脤录炉脤氓脳脽脟驴 脰脨鹿煤碌莽脨脜脮脟鲁卢5%
脡脧潞拢脢脨脮镁赂庐鲁拢脦帽禄谩脪茅虏驴脢冒拢卢脰搂鲁脰脡脧脢脨鹿芦脣戮虏垄鹿潞脰脴脳茅拢卢脤谩脡媒鹿芦脣戮脰脢脕驴脜脿脫媒脕煤脥路脝贸脪碌
脰脨脨脜脰陇脠炉脨脗脠脦脳脺戮颅脌铆 脢脳麓脦鹿芦驴陋路垄脡霉
GPU露脌陆脟脢脼脛娄露没脧脽鲁脤陆芦鲁氓麓脤IPO拢隆虏脦鹿脡隆垄潞脧脳梅碌脠赂脜脛卯鹿脡脙没碌楼脪禄脌脌
脭酶脴鹿脠潞拢潞脠么脤脴脌脢脝脮隆掳驴陋脗脤碌脝隆卤拢卢脛镁碌脗脢卤麓煤陆芦驴录脗脟赂掳脙脌陆篓鲁搂
赂脮赂脮拢隆脩脟脤芦鹿脡脢脨拢卢脠芦脧脽脤酶脣庐拢隆A50脰卤脧脽路颅脗脤拢卢赂脹鹿脡脡卯碌梅拢隆路垄脡煤脕脣脢虏脙麓
脦脢陆莽M7脢脗鹿脢脨脗陆酶脮鹿拢潞鲁碌脰梅鲁脝隆掳鹿媒脫脷脧脿脨脜脫脿鲁脨露芦虏脜鹿潞脗貌隆卤虏垄脝冒脣脽禄陋脦陋
脌毛隆掳虏禄脗么戮脥陆没隆卤脰禄脢拢脕陆赂枚露脿脭脗拢隆脤脴脌脢脝脮脥脜露脫拢潞陆芦露脪脧脰鲁脨脜碌 脮眉戮脠TikTok
脡卯陆禄脣霉拢潞11脭脗16脠脮陆芦陆酶脨脨脡卯脢脨陆禄脪脳脧碌脥鲁虏芒脢脭
		var s = "_" + Math.random().toString(36).slice(2);
		document.write('<div id="' + s + '"></div>');
		(window.slotbydup=window.slotbydup || []).push({
			display: 'inlay-fix'
document.addEventListener('DOMContentLoaded', function () {
      var userid = 'nouser';
        // 忙拢聙忙聼楼 Vue 氓聮聦 AdvertCommon 忙聵炉氓聬娄氓颅聵氓聹篓
        if (Vue && window['mobileweb_AdvertCommon@1.0.4'].default) {
          Vue.use(window['mobileweb_AdvertCommon@1.0.4'].default);
          // 氓掳聺猫炉聲盲禄聨 cookie 盲赂颅忙聫聬氓聫聳 userid
          var match = document.cookie.match(/userid=([^;]*)/);
          if (match) {
            userid = match[1] || 'nouser';
      } catch (error) {
        console.error(error);
      // 氓聢聺氓搂聥氓聦聳 Vue 氓庐聻盲戮聥
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
脡忙脦麓鲁脡脛锚脠脣脦楼鹿忙脛脷脠脻戮脵卤篓
虏禄脕录脨脜脧垄戮脵卤篓碌莽禄掳拢潞(0571)88933003
戮脵卤篓脫脢脧盲拢潞*****************
脮茫陆颅脥卢禄篓脣鲁禄楼脕陋脨脜脧垄录录脢玫脫脨脧脼鹿芦脣戮掳忙脠篓脣霉脫脨
var newsid = '661284877';
    var id='c_661284877';
	var initctime = '1724975497';
	var stitle = encodeURIComponent('脙脌鹿脡脮脟碌酶虏禄脪禄脫垄脦掳麓茂碌酶鲁卢6%脥脧脌脹麓贸脜脤 脝禄鹿没鲁脡脦陋禄篓脝矛AI脢脳脩隆鹿脡');
	var artStock = new Array();
	var cotitle = '脙脌鹿脡脮脟碌酶虏禄脪禄脫垄脦掳麓茂碌酶鲁卢6%脥脧脌脹麓贸脜脤 脝禄鹿没鲁脡脦陋禄篓脝矛AI脢脳脩隆鹿脡';
	var cocontent = '脙脌鹿脡脦虏脜脤禄脴脗盲拢卢脠媒麓贸脰赂脢媒脮脟碌酶虏禄脪禄拢卢碌脌脰赂脭脵麓麓脨脗赂脽拢禄脫垄脦掳麓茂麓贸碌酶鲁卢6%拢卢脢脨脰碌脮么路垄陆眉2000脪脷脙脌脭陋拢禄脌毛掳露脠脣脙帽卤脪露脪脙脌脭陋脡媒脝脝7.1鹿脴驴脷拢禄脝禄鹿没隆垄脫垄脦掳麓茂戮霉脫脨脪芒脥露脳脢OpenAI拢禄禄篓脝矛陆芦脝禄鹿没脕脨脦陋2025脛锚脢脳脩隆脠脣鹿陇脰脟脛脺鹿脡脝卤隆拢';
	var codata = cocontent.replace(/<[^>]*>/g, '').substr(0,250);
	var courl = 'http://stock.10jqka.com.cn/usstock/20240830/c661284877.shtml';
        newRobotRecList : [],
		cid: ''  //脳卯潞贸脪禄脤玫脝脌脗脹碌脛ID
	ARTINFO.tday = $.timeFormat(ARTINFO.tday, 'yyyyMMddHHmm');
	ARTINFO.tday = parseInt(ARTINFO.tday);
	ARTINFO.userid = WEB.basic.getUserid();
$(document).ready(function(){
	    	    TA.log({id:id, ld:'browser',fid:'info_gather,zx_n_all,zx_wzb_total,zx_ll_684,zxusstock,ch_stock', pt:2,_sid:'imgzx'});
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?78c58f01938e4d85eaf619eae71b4ed1";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?f79b64788a4e377c608617fba4c736e2";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
// 麓娄脌铆脥录脝卢src脦陋undefined
  var imageDomList = document.querySelectorAll('img');
  if (imageDomList.length > 0) {
      imageDomList.forEach(ele => {
          var imgSrc = ele.src;
          if (imgSrc.endsWith('/undefined')) {
              ele.remove();
  var srcVideoDom = document.querySelector('input.video-insert');
    var videoUrl = srcVideoDom.getAttribute('data-src');
    var videoThumb = srcVideoDom.getAttribute('data-thumb');
    var videoDom = document.createElement('video');
    var parent = srcVideoDom.parentNode;
    videoDom.src = videoUrl;
    videoDom.poster = videoThumb;
    videoDom.setAttribute("controls", true);
    videoDom.setAttribute("style", "width:627px;height:353px");
    parent.insertBefore(videoDom, srcVideoDom);
    srcVideoDom.remove();
  $('.self-stock-con').css({'height':'206px'})
// 脧脼脰脝碌脛禄煤脝梅脠脣脳茅录镁掳脳脙没碌楼
    const LIMIT_ROBOT_VISUAL_COMS = [
        'thsf2e_common-long-hu-rank',
        'thsf2e_ai-table'
    const checkLimitCom = (dataType) => {
        return LIMIT_ROBOT_VISUAL_COMS.includes(dataType);
    // 脕煤禄垄掳帽脤脴脢芒麓娄脌铆
    const LONGHU_TYPE = 'thsf2e_common';
    // 禄帽脠隆dom陆脷碌茫脨脜脧垄
    const getDomAttrData = (robotParse) => {
        const dataType = robotParse.getAttribute('data-type');
        const dataProps = robotParse.getAttribute('data-props');
        const dataComurl = robotParse.getAttribute('data-comurl');
        const dataStyle = robotParse.getAttribute('data-style');
        const dataVersion = robotParse.getAttribute('data-version');
            dataType,
            dataProps,
            dataComurl,
            dataStyle,
            dataVersion,
    const loadScript = (url, callback) => {
        const originalDefine = window.define;
        window.define = undefined;
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.charset = 'utf-8';
        script.src = url;
        script.onload = function() {
            callback(null)
        script.onerror = function() {
            callback(`Failed to load script: ${url}`);
        document.body.appendChild(script);
    // 脜脨露脧window脌茂脢脟路帽脫脨脮芒赂枚脳茅录镁拢卢脠莽鹿没脫脨戮脥脰卤陆脫脢鹿脫脙拢卢脠莽鹿没脙禄脫脨戮脥录脫脭脴
    const getComponent = (id, url, callback) => {
        return new Promise((resolve, reject) => {
            if (window[id]) {
                return resolve(window[id].default)
            loadScript(url, (err) => {
                if (err || !window[id]) {
                    reject(err || '脳茅录镁id麓铆脦贸')
                } else {
                    resolve(window[id].default)
    // 陆芒脦枚禄煤脝梅脠脣脛脷脠脻
    const parseRobot = (ele) => {
        const { dataType, dataProps, dataComurl, dataStyle, dataVersion } = getDomAttrData(ele);
        const props = JSON.parse(decodeURIComponent(dataProps));
        const style = JSON.parse(decodeURIComponent(dataStyle));
        // 脜脨露脧脢脟路帽虏禄脭脷limit盲脰脠戮脰脨
        if (!checkLimitCom(dataType)) {
        const id = `${dataType}_index@${dataVersion}`;
        getComponent(id, dataComurl).then((com) => {
            const placeholder = document.createElement('div');
            placeholder.className = 'robot-visual-container';
            ele.replaceWith(placeholder);
            const comInstance = new Vue({
                el: placeholder,
                render: h => h('div', { attr: { id: 'robotVisual' }, style }, [
                    h(com, { props })
            }).$mount();
        }).catch(err => {
            console.error(err)
    const parseContentMain = () => {
        const eles = document.getElementById('contentApp').querySelectorAll('robot-parse');
        eles.forEach(ele => {
            parseRobot(ele)
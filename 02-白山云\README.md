# EdgeFN API 使用示例

这是一个使用 EdgeFN API 调用 DeepSeek-R1-0528 模型的 Python 示例程序。

## 功能特性

- ✅ 安全的 API Key 管理（支持环境变量）
- ✅ 完整的错误处理
- ✅ 单次对话和交互式聊天两种模式
- ✅ 使用统计显示
- ✅ 可配置的参数（温度、最大tokens等）

## 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装：

```bash
pip install requests
```

## 配置 API Key

### 方法1：环境变量（推荐）

**Windows:**
```cmd
set EDGEFN_API_KEY=sk-HH2NyMUwCGoE211d3b59E69970Fc4410B724996bA4409015
```

**Linux/Mac:**
```bash
export EDGEFN_API_KEY=sk-HH2NyMUwCGoE211d3b59E69970Fc4410B724996bA4409015
```

**永久设置（Windows）:**
```cmd
setx EDGEFN_API_KEY "sk-HH2NyMUwCGoE211d3b59E69970Fc4410B724996bA4409015"
```

### 方法2：直接在代码中设置

如果不想使用环境变量，程序会自动使用代码中预设的 API Key（不推荐用于生产环境）。

## 使用方法

### 运行程序

```bash
python edgefn_api_example.py
```

程序会提示您选择模式：
- **模式1**: 单次对话示例
- **模式2**: 交互式聊天

### 单次对话示例

```python
from edgefn_api_example import EdgeFNClient

# 创建客户端
client = EdgeFNClient()

# 发送消息
messages = [{"role": "user", "content": "你好，请介绍一下你自己"}]
response = client.chat_completion(messages=messages)

# 获取回复
if 'choices' in response:
    reply = response['choices'][0]['message']['content']
    print(reply)
```

### 交互式聊天

选择模式2后，您可以与AI进行连续对话，输入 `quit` 或 `exit` 退出。

## API 参数说明

### chat_completion 方法参数

- `messages`: 消息列表，格式为 `[{"role": "user", "content": "消息内容"}]`
- `model`: 模型名称，默认为 `"DeepSeek-R1-0528"`
- `temperature`: 温度参数，控制回复的随机性 (0-1)，默认 0.7
- `max_tokens`: 最大token数量，可选
- `stream`: 是否使用流式响应，默认 False

### 消息格式

```python
messages = [
    {"role": "system", "content": "你是一个有用的助手"},  # 系统提示（可选）
    {"role": "user", "content": "用户的问题"},
    {"role": "assistant", "content": "助手的回复"},
    {"role": "user", "content": "用户的新问题"}
]
```

## 错误处理

程序包含完整的错误处理：
- 网络请求错误
- API 响应错误
- JSON 解析错误
- API Key 缺失错误

## 安全提醒

⚠️ **重要**: 
- 不要将 API Key 直接写在代码中并提交到版本控制系统
- 建议使用环境变量或配置文件管理敏感信息
- 定期更换 API Key

## 示例输出

```
EdgeFN API 示例程序
1. 单次对话示例
2. 交互式聊天

请选择模式 (1/2): 1

发送请求到 EdgeFN API...
消息: 你好，请介绍一下你自己
--------------------------------------------------
AI 回复:
你好！我是DeepSeek开发的人工智能助手...

使用统计:
  输入tokens: 15
  输出tokens: 120
  总tokens: 135
```

## 故障排除

### 常见问题

1. **API Key 错误**
   - 检查 API Key 是否正确
   - 确认环境变量设置正确

2. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

3. **请求超时**
   - 程序默认超时时间为30秒
   - 可以在代码中调整 `timeout` 参数

## 许可证

本示例代码仅供学习和参考使用。

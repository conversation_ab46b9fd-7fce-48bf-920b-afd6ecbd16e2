#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P 多卡推理脚本
基于训练好的LoRA权重进行高效推理
"""

import torch
import torch.nn as nn
from torch.nn.parallel import DataParallel
import os
import json
import time
from PIL import Image
import numpy as np
from datetime import datetime
import argparse

# 导入DiffSynth相关模块
import sys
sys.path.append('/root/sj-tmp/DiffSynth-Studio')

from diffsynth import WanVideoPipeline
from diffsynth.models.model_manager import ModelManager
from peft import PeftModel, LoraConfig

class MultiGPUWanInference:
    def __init__(self, lora_path, device_ids=None):
        """
        初始化多GPU推理器
        
        Args:
            lora_path: LoRA权重文件路径
            device_ids: GPU设备ID列表，如[0,1]
        """
        self.lora_path = lora_path
        self.device_ids = device_ids or list(range(torch.cuda.device_count()))
        self.main_device = f"cuda:{self.device_ids[0]}"
        
        print(f"🚀 初始化多GPU推理器")
        print(f"   LoRA路径: {lora_path}")
        print(f"   使用GPU: {self.device_ids}")
        print(f"   主设备: {self.main_device}")
        
        # 加载训练配置
        self.load_training_config()
        
        # 初始化模型
        self.setup_model()
        
    def load_training_config(self):
        """加载训练时的配置"""
        config_path = os.path.join(os.path.dirname(self.lora_path), "training_args.json")
        
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                self.training_config = json.load(f)
            print(f"✅ 加载训练配置: {config_path}")
        else:
            # 默认配置
            self.training_config = {
                "height": 480,
                "width": 832,
                "lora_rank": 8,
                "lora_target_modules": "q,k,v,o,ffn.0,ffn.2"
            }
            print("⚠️  使用默认配置")
            
    def setup_model(self):
        """设置模型和LoRA权重"""
        print("📦 加载基础模型...")
        
        # 模型配置 - 基于训练时的配置
        model_configs = [
            {
                "model_id": "Wan-AI/Wan2.1-I2V-14B-480P",
                "model_name": "wan_video_dit",
                "model_class": "WanModel",
                "model_path": "diffusion_pytorch_model*.safetensors"
            },
            {
                "model_id": "Wan-AI/Wan2.1-T2V-1.3B", 
                "model_name": "wan_video_text_encoder",
                "model_class": "WanTextEncoder",
                "model_path": "models_t5_umt5-xxl-enc-bf16.pth"
            },
            {
                "model_id": "Wan-AI/Wan2.1-T2V-1.3B",
                "model_name": "wan_video_vae", 
                "model_class": "WanVideoVAE",
                "model_path": "Wan2.1_VAE.pth"
            },
            {
                "model_id": "Wan-AI/Wan2.1-I2V-14B-480P",
                "model_name": "wan_video_image_encoder",
                "model_class": "WanImageEncoder", 
                "model_path": "models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth"
            }
        ]
        
        # 初始化pipeline
        self.pipeline = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cpu",  # 先在CPU加载
            model_configs=model_configs
        )
        
        print("🔧 应用LoRA权重...")
        self.apply_lora_weights()
        
        print("🚀 设置多GPU并行...")
        self.setup_multi_gpu()
        
    def apply_lora_weights(self):
        """应用LoRA权重到DiT模型"""
        try:
            # 加载LoRA权重
            lora_state_dict = torch.load(self.lora_path, map_location="cpu", weights_only=False)
            print(f"✅ 加载LoRA权重: {len(lora_state_dict)} 个参数")
            
            # 获取DiT模型
            dit_model = self.pipeline.dit
            
            # 应用LoRA权重 (简化版本，实际可能需要更复杂的逻辑)
            compatible_weights = {}
            for name, param in lora_state_dict.items():
                # 移除前缀并匹配模型参数
                clean_name = name.replace("pipe.dit.", "").replace("module.", "")
                if hasattr(dit_model, clean_name.split('.')[0]):
                    compatible_weights[clean_name] = param
            
            # 加载兼容的权重
            dit_model.load_state_dict(compatible_weights, strict=False)
            print(f"✅ 应用LoRA权重: {len(compatible_weights)} 个参数")
            
        except Exception as e:
            print(f"⚠️  LoRA权重应用失败: {e}")
            print("   继续使用基础模型...")
    
    def setup_multi_gpu(self):
        """设置多GPU并行"""
        if len(self.device_ids) > 1:
            print(f"🔄 设置DataParallel，使用GPU: {self.device_ids}")
            
            # 将模型移动到主GPU
            self.pipeline = self.pipeline.to(self.main_device)
            
            # 设置DiT模型的DataParallel
            if hasattr(self.pipeline, 'dit'):
                self.pipeline.dit = DataParallel(
                    self.pipeline.dit, 
                    device_ids=self.device_ids
                )
                print("✅ DiT模型已设置DataParallel")
            
            # 其他组件保持在主GPU
            components = ['text_encoder', 'vae', 'image_encoder']
            for comp in components:
                if hasattr(self.pipeline, comp):
                    setattr(self.pipeline, comp, 
                           getattr(self.pipeline, comp).to(self.main_device))
        else:
            print(f"📱 单GPU模式，使用GPU: {self.device_ids[0]}")
            self.pipeline = self.pipeline.to(self.main_device)
    
    def generate_video(self, prompt, input_image_path=None, **kwargs):
        """
        生成视频
        
        Args:
            prompt: 文本提示
            input_image_path: 输入图像路径（I2V模式）
            **kwargs: 其他生成参数
        """
        print(f"🎬 开始生成视频...")
        print(f"   提示词: {prompt}")
        if input_image_path:
            print(f"   输入图像: {input_image_path}")
        
        start_time = time.time()
        
        try:
            # 准备输入
            generation_kwargs = {
                "prompt": prompt,
                "height": self.training_config.get("height", 480),
                "width": self.training_config.get("width", 832),
                "num_frames": kwargs.get("num_frames", 81),
                "num_inference_steps": kwargs.get("num_inference_steps", 50),
                "guidance_scale": kwargs.get("guidance_scale", 7.5),
                **kwargs
            }
            
            # 如果有输入图像，加载并处理
            if input_image_path and os.path.exists(input_image_path):
                input_image = Image.open(input_image_path).convert("RGB")
                # 调整图像尺寸
                input_image = input_image.resize((generation_kwargs["width"], generation_kwargs["height"]))
                generation_kwargs["input_image"] = input_image
            
            # 生成视频
            with torch.no_grad():
                result = self.pipeline(**generation_kwargs)
            
            generation_time = time.time() - start_time
            print(f"✅ 视频生成完成，耗时: {generation_time:.2f}秒")
            
            return result, generation_time
            
        except Exception as e:
            print(f"❌ 视频生成失败: {e}")
            import traceback
            traceback.print_exc()
            return None, 0
    
    def benchmark_performance(self, num_tests=3):
        """性能基准测试"""
        print(f"📊 开始性能基准测试 ({num_tests}次)")
        
        test_prompt = "A beautiful sunset over the ocean with gentle waves"
        times = []
        
        for i in range(num_tests):
            print(f"   测试 {i+1}/{num_tests}...")
            _, gen_time = self.generate_video(
                prompt=test_prompt,
                num_inference_steps=20,  # 减少步数以加快测试
                num_frames=25  # 减少帧数
            )
            if gen_time > 0:
                times.append(gen_time)
        
        if times:
            avg_time = sum(times) / len(times)
            print(f"📈 性能统计:")
            print(f"   平均时间: {avg_time:.2f}秒")
            print(f"   最快时间: {min(times):.2f}秒") 
            print(f"   最慢时间: {max(times):.2f}秒")
            print(f"   GPU数量: {len(self.device_ids)}")
            
            if len(self.device_ids) > 1:
                single_gpu_estimate = avg_time * len(self.device_ids) * 0.8
                speedup = single_gpu_estimate / avg_time
                print(f"   估计加速比: {speedup:.1f}x")
        
        return times

def main():
    parser = argparse.ArgumentParser(description="Wan2.1-I2V-14B-480P 多卡推理")
    parser.add_argument("--lora_path", type=str, required=True, help="LoRA权重文件路径")
    parser.add_argument("--prompt", type=str, required=True, help="生成提示词")
    parser.add_argument("--input_image", type=str, help="输入图像路径（I2V模式）")
    parser.add_argument("--output_path", type=str, default="./output_video.mp4", help="输出视频路径")
    parser.add_argument("--gpu_ids", type=str, default="0,1", help="GPU ID列表，如'0,1'")
    parser.add_argument("--num_frames", type=int, default=81, help="生成帧数")
    parser.add_argument("--num_inference_steps", type=int, default=50, help="推理步数")
    parser.add_argument("--guidance_scale", type=float, default=7.5, help="引导强度")
    parser.add_argument("--benchmark", action="store_true", help="运行性能基准测试")
    
    args = parser.parse_args()
    
    # 解析GPU ID
    gpu_ids = [int(x.strip()) for x in args.gpu_ids.split(",")]
    
    print("🎬 Wan2.1-I2V-14B-480P 多卡推理")
    print("=" * 50)
    
    # 检查GPU
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return
    
    available_gpus = torch.cuda.device_count()
    print(f"✅ 检测到 {available_gpus} 张GPU")
    
    # 验证GPU ID
    gpu_ids = [gid for gid in gpu_ids if gid < available_gpus]
    if not gpu_ids:
        print("❌ 没有有效的GPU ID")
        return
    
    # 初始化推理器
    inferencer = MultiGPUWanInference(
        lora_path=args.lora_path,
        device_ids=gpu_ids
    )
    
    if args.benchmark:
        # 运行基准测试
        inferencer.benchmark_performance()
    else:
        # 生成视频
        result, gen_time = inferencer.generate_video(
            prompt=args.prompt,
            input_image_path=args.input_image,
            num_frames=args.num_frames,
            num_inference_steps=args.num_inference_steps,
            guidance_scale=args.guidance_scale
        )
        
        if result is not None:
            # 保存结果
            print(f"💾 保存视频到: {args.output_path}")
            # 这里需要根据实际的result格式来保存视频
            # result.save(args.output_path)  # 示例
            
            print("🎉 推理完成!")
            print(f"   生成时间: {gen_time:.2f}秒")
            print(f"   使用GPU: {gpu_ids}")
            print(f"   输出文件: {args.output_path}")

if __name__ == "__main__":
    main()

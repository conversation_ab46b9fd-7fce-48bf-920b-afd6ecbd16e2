{"zero_optimization": {"stage": 2, "allgather_partitions": true, "allgather_bucket_size": 200000000.0, "overlap_comm": true, "reduce_scatter": true, "reduce_bucket_size": 200000000.0, "contiguous_gradients": true}, "gradient_accumulation_steps": "auto", "gradient_clipping": "auto", "steps_per_print": 10, "train_batch_size": "auto", "train_micro_batch_size_per_gpu": "auto", "wall_clock_breakdown": false, "bf16": {"enabled": "auto"}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import logging
import requests
from bs4 import BeautifulSoup
from typing import List, Dict

class ImprovedBaiduParser:
    """改进的百度搜索结果解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_search_results(self, html_content: str, max_results: int = 5) -> List[Dict[str, str]]:
        """
        解析百度搜索结果页面
        
        Args:
            html_content (str): 百度搜索结果页面的HTML内容
            max_results (int): 最大结果数量
            
        Returns:
            List[Dict[str, str]]: 搜索结果列表，每个结果包含title和url
        """
        results = []
        
        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 方法1: 查找搜索结果容器
            result_containers = soup.find_all('div', class_=re.compile(r'result'))
            self.logger.info(f"找到 {len(result_containers)} 个结果容器")
            
            for container in result_containers[:max_results * 2]:  # 多取一些，后面过滤
                # 查找标题链接
                title_link = container.find('h3', class_=re.compile(r't'))
                if not title_link:
                    title_link = container.find('h3')
                
                if title_link:
                    link = title_link.find('a')
                    if link and link.get('href'):
                        title = link.get_text(strip=True)
                        url = link.get('href')
                        
                        # 处理百度重定向URL
                        processed_url = self._process_url(url)
                        if processed_url and self._is_valid_result(title, processed_url):
                            results.append({
                                'title': title,
                                'url': processed_url
                            })
                            
                            if len(results) >= max_results:
                                break
            
            # 方法2: 如果方法1没有找到足够结果，使用更宽泛的搜索
            if len(results) < max_results:
                self.logger.info("使用备用解析方法")
                
                # 查找所有h3标签内的链接
                h3_tags = soup.find_all('h3')
                for h3 in h3_tags:
                    links = h3.find_all('a', href=True)
                    for link in links:
                        title = link.get_text(strip=True)
                        url = link.get('href')
                        
                        # 处理百度重定向URL
                        processed_url = self._process_url(url)
                        if processed_url and self._is_valid_result(title, processed_url):
                            # 检查是否已经存在
                            if not any(r['url'] == processed_url for r in results):
                                results.append({
                                    'title': title,
                                    'url': processed_url
                                })
                                
                                if len(results) >= max_results:
                                    break
                    
                    if len(results) >= max_results:
                        break
            
            # 方法3: 最后的备用方法，查找所有外部链接
            if len(results) < max_results:
                self.logger.info("使用最终备用解析方法")
                
                all_links = soup.find_all('a', href=True)
                for link in all_links:
                    url = link.get('href')
                    title = link.get_text(strip=True)
                    
                    # 只要外部链接且有意义的标题
                    if (url.startswith(('http://', 'https://')) and 
                        not any(domain in url for domain in ['baidu.com', 'hao123.com']) and
                        len(title) > 10 and
                        self._is_meaningful_title(title)):
                        
                        # 检查是否已经存在
                        if not any(r['url'] == url for r in results):
                            results.append({
                                'title': title,
                                'url': url
                            })
                            
                            if len(results) >= max_results:
                                break
            
            self.logger.info(f"解析完成，找到 {len(results)} 个有效结果")
            return results
            
        except Exception as e:
            self.logger.error(f"解析搜索结果失败: {e}")
            return []
    
    def _is_valid_result(self, title: str, url: str) -> bool:
        """检查搜索结果是否有效"""
        if not title or not url:
            return False
        
        # 过滤无效URL
        invalid_domains = [
            'baidu.com', 'hao123.com', 'javascript:', 'mailto:', '#'
        ]
        
        for domain in invalid_domains:
            if domain in url.lower():
                return False
        
        # 过滤无意义的标题
        if not self._is_meaningful_title(title):
            return False
        
        # 必须是HTTP/HTTPS链接
        return url.startswith(('http://', 'https://'))
    
    def _is_meaningful_title(self, title: str) -> bool:
        """检查标题是否有意义"""
        if len(title) < 5:
            return False
        
        # 过滤无意义的标题
        meaningless_patterns = [
            '百度', '搜索', '登录', '注册', 'hao123', '导航',
            '首页', '更多', '下一页', '上一页', '相关搜索'
        ]
        
        for pattern in meaningless_patterns:
            if pattern in title:
                return False
        
        return True

    def _process_url(self, url: str) -> str:
        """处理URL，解析百度重定向链接"""
        if not url:
            return ""

        # 如果是百度重定向链接，尝试获取真实URL
        if 'baidu.com/baidu.php?url=' in url:
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                }

                # 发送HEAD请求获取重定向URL
                response = requests.head(url, headers=headers, allow_redirects=True, timeout=5)
                final_url = response.url

                # 检查是否是有效的外部URL
                if final_url and final_url != url and not 'baidu.com' in final_url:
                    self.logger.debug(f"成功解析百度重定向: {url[:50]}... -> {final_url}")
                    return final_url
                else:
                    self.logger.debug(f"百度重定向解析失败: {final_url}")
                    return ""

            except Exception as e:
                self.logger.debug(f"解析百度重定向URL失败: {e}")
                return ""

        # 直接返回原URL
        return url

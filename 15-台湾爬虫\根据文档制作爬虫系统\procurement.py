from flask import Blueprint, request, jsonify
from src.models.procurement import db, ProcurementData
from src.crawler.procurement_crawler import ProcurementCrawler
import threading
import time

procurement_bp = Blueprint('procurement', __name__)

# 全局变量存储爬虫状态
crawler_status = {
    'is_running': False,
    'progress': 0,
    'total': 0,
    'message': ''
}

@procurement_bp.route('/search', methods=['GET'])
def search_procurement():
    """搜索采购数据"""
    try:
        keyword = request.args.get('keyword', '')
        tender_type = request.args.get('tender_type', '')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        # 构建查询
        query = ProcurementData.query
        
        if keyword:
            query = query.filter(
                (ProcurementData.title.contains(keyword)) |
                (ProcurementData.agency.contains(keyword)) |
                (ProcurementData.content.contains(keyword))
            )
        
        if tender_type:
            query = query.filter(ProcurementData.tender_type == tender_type)
        
        # 分页
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        results = [item.to_dict() for item in pagination.items]
        
        return jsonify({
            'success': True,
            'data': results,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/data/<int:data_id>', methods=['GET'])
def get_procurement_detail(data_id):
    """获取单条采购数据详情"""
    try:
        data = ProcurementData.query.get_or_404(data_id)
        return jsonify({
            'success': True,
            'data': data.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/stats', methods=['GET'])
def get_stats():
    """获取统计信息"""
    try:
        total_count = ProcurementData.query.count()
        
        # 按类型统计
        type_stats = db.session.query(
            ProcurementData.tender_type,
            db.func.count(ProcurementData.id)
        ).group_by(ProcurementData.tender_type).all()
        
        # 按机关统计（前10）
        agency_stats = db.session.query(
            ProcurementData.agency,
            db.func.count(ProcurementData.id)
        ).group_by(ProcurementData.agency).order_by(
            db.func.count(ProcurementData.id).desc()
        ).limit(10).all()
        
        return jsonify({
            'success': True,
            'data': {
                'total_count': total_count,
                'type_stats': [{'type': t[0], 'count': t[1]} for t in type_stats],
                'agency_stats': [{'agency': a[0], 'count': a[1]} for a in agency_stats]
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/crawl', methods=['POST'])
def start_crawl():
    """启动爬虫任务"""
    global crawler_status
    
    if crawler_status['is_running']:
        return jsonify({
            'success': False,
            'error': '爬虫正在运行中'
        }), 400
    
    try:
        data = request.get_json()
        keyword = data.get('keyword', '')
        tender_types = data.get('tender_types', ['招标', '决标'])
        max_pages = data.get('max_pages', 5)
        page_size = data.get('page_size', 20)
        
        # 启动后台爬虫任务
        thread = threading.Thread(
            target=run_crawler_task,
            args=(keyword, tender_types, max_pages, page_size)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': '爬虫任务已启动'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@procurement_bp.route('/crawl/status', methods=['GET'])
def get_crawl_status():
    """获取爬虫状态"""
    return jsonify({
        'success': True,
        'data': crawler_status
    })

def run_crawler_task(keyword, tender_types, max_pages, page_size):
    """后台爬虫任务"""
    global crawler_status
    
    crawler_status['is_running'] = True
    crawler_status['progress'] = 0
    crawler_status['total'] = 0
    crawler_status['message'] = '正在初始化爬虫...'
    
    try:
        crawler = ProcurementCrawler()
        
        crawler_status['message'] = '正在搜索数据...'
        results = crawler.search_procurement(
            keyword=keyword,
            tender_types=tender_types,
            max_pages=max_pages,
            page_size=page_size
        )
        
        crawler_status['total'] = len(results)
        crawler_status['message'] = f'找到 {len(results)} 条数据，正在保存...'
        
        # 保存到数据库
        saved_count = 0
        for i, result in enumerate(results):
            try:
                # 检查是否已存在
                existing = ProcurementData.query.filter_by(
                    title=result['title'],
                    agency=result['agency']
                ).first()
                
                if not existing:
                    procurement_data = ProcurementData(
                        title=result['title'],
                        agency=result['agency'],
                        case_number=result['case_number'],
                        tender_type=result['tender_type'],
                        announcement_date=result['announcement_date'],
                        content=result['content'],
                        url=result['url']
                    )
                    db.session.add(procurement_data)
                    saved_count += 1
                
                crawler_status['progress'] = i + 1
                
                # 每10条提交一次
                if (i + 1) % 10 == 0:
                    db.session.commit()
                    
            except Exception as e:
                print(f"保存数据时出错: {str(e)}")
                continue
        
        # 最终提交
        db.session.commit()
        
        crawler_status['message'] = f'爬虫完成！共保存 {saved_count} 条新数据'
        
    except Exception as e:
        crawler_status['message'] = f'爬虫出错: {str(e)}'
        
    finally:
        crawler_status['is_running'] = False


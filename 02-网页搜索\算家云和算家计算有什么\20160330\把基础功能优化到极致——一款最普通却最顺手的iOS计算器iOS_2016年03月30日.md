﻿# 把基础功能优化到极致——一款最普通却最顺手的iOS计算器#iOS

**发布日期**: 2016年03月30日

**原文链接**: https://www.geekpark.net/news/215018

## 📄 原文内容

手机里的计算器看起来似乎是个不起眼的功能，但从没哪款手机彻底砍掉它。iPhone 甚至将计算器放到了快速启动栏，可尴尬的是自带的计算器往往非常难用，相信很多人都是一个「凑合着用」的心态。 如果你需要顺手的原生计算器代替品，但又不想费神去找，那就别错过下面这款 App，因为它是我遇到过最好的原生计算器代替品！

「Calco」是一款刚上线的计算器应用，它没有扫描计算、手写算式等花哨的功能，但却把最基础的功能和最高频的操作需求做到了最完美。 多行计算、人性化的光标移动、多种运算机制切换等，让它成了我用过最简单却最顺手的计算器。

手机计算器这一工具在基础功能方面一直没有太好的完善，反而在针对特殊情境的扫描识别、手写等方面越玩越起劲。于是用户往往夹在花哨的第三方计算器和鸡肋的系统计算器之间凑合着……

「Calco」恰巧是一款没有任何高科技（花哨）功能，但在基础功能方面非常完善的计算器，具体可以从下面这几个「Calco」解决的计算器使用痛点看出来：

我们都知道 iPhone 在输入时的光标移动一直是个坑，要修改之前输入的内容时很不方便，而在计算其中更是彻底没了这功能。对于较复杂的数字，一旦输错了一位那就必须删掉从头输入，有时还不得不从头进行计算，简直反人类……而「Calco」则是借鉴一些第三方输入法的思路，在输入栏上方划出了一条窄窄的空白栏，用于控制光标，用起来顺手无比！

「Calco」它可以将用户每次不同计算过程分行显示，这样在进行当前计算的时候用户也可以对之前的计算过程、结果、算式和其中每个数字都一目了然。 在使用计算器的时候，很多情况下我们需要进行不只一次的计算，下一次的计算往往需要用到之前某次计算的结果，或者最后的计算需要综合之前所有的答案……

在「Calco」中进行计算，会将用户的每个输入的操作以算式的方式呈现出来，并且在得出答案后形成一个类似消息栏的等式，用户点击操作栏中的「New」就可以进行新的操作。并且之前的计算历史用户还可以通过右滑来将其删除，或者直对之前的计算式进行修改，答案也会随之改变。

在计算的过程中，对于一些特殊的结果和单位有时会有多种表示方式，就像对于弧度制和角度制之间的换算、小数点后显示的位数等等…… 「Calco」在计算界面接提供很方面的运算模式切换功能呢，运算符号左上角的两个小黑标，靠左是切换角度制和弧度制，靠右的是切换四舍五入方式、小数点精确位数等，这样的切换开关在计算使用中非常方便。

而「Calco」的众多计算公式也都可以通过滑动来切换寻找，且当用户需要查看更早些的计算过程时还可以直接把输入界面下拉隐藏起来，展示更多的计算过程。

「Calco」的界面干净清爽，没有太多精彩的交互和动效，也没有太多让人惊叹的科技内涵，但就是对这些看似不起眼的基本需求的满足，让「Calco」成了我用过最普通却又最顺手的计算器。
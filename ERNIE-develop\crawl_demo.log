2025-07-07 10:16:00,252 - crawl_utils - INFO - Webpage Text: 
Notice: While JavaScript is not essential for this website, your interaction with the content will be limited. Please turn JavaScript on for the full experience. 
## Get Started
Whether you're new to programming or an experienced developer, it's easy to learn and use Python.
## Download
Python source code and installers are available for download for all versions!
Latest: 
## Docs
Documentation for Python's standard library, along with tutorials and guides, are available online.
## Jobs
Looking for work or have a Python related position that you're trying to hire for? Our relaunched community-run job board is the place to go.
## Latest News
  * 2025-07-02 
  * 2025-06-17 
  * 2025-06-17 
  * 2025-06-12 
  * 2025-06-12 


## Upcoming Events
  * 2025-07-10 
  * 2025-07-14 
  * 2025-07-14 
  * 2025-07-15 
  * 2025-07-22 


## Success Stories
by <PERSON>  
---  
## Use Python for…
  * Web Development: Django, Pyramid, Bottle, Tornado, Flask, web2py
  * GUI Development: tkInter, PyGObject, PyQt, <PERSON>y<PERSON><PERSON>, <PERSON><PERSON>, wx<PERSON><PERSON><PERSON>, DearPyGui
  * Scientific and Numeric: SciPy, Pandas, <PERSON>ython
  * Software Development: Buildbot, Trac, Roundup
  * System Administration: Ansible, Salt, OpenStack, xonsh


##  >>>
The mission of the Python Software Foundation is to promote, protect, and advance the Python programming language, and to support and facilitate the growth of a diverse and international community of Python programmers. 

2025-07-07 10:16:07,125 - crawl_utils - INFO - Webpage Text: 
### Navigation
  * |
  * |
  * |
  * »
  * Greek | ΕλληνικάEnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文
dev (3.15)pre (3.14)***********.*************.***********.***********.72.6
  * [The Python Tutorial](https://docs.python.org/3/tutorial/)
  * Theme  Auto Light Dark |


# The Python Tutorial
Tip
This tutorial is designed for programmers that are new to the Python language, not beginners who are new to programming.
Python is an easy to learn, powerful programming language. It has efficient high-level data structures and a simple but effective approach to object-oriented programming. Python’s elegant syntax and dynamic typing, together with its interpreted nature, make it an ideal language for scripting and rapid application development in many areas on most platforms.
The Python interpreter and the extensive standard library are freely available in source or binary form for all major platforms from the Python web site, , and may be freely distributed. The same site also contains distributions of and pointers to many free third party Python modules, programs and tools, and additional documentation.
The Python interpreter is easily extended with new functions and data types implemented in C or C++ (or other languages callable from C). Python is also suitable as an extension language for customizable applications.
This tutorial introduces the reader informally to the basic concepts and features of the Python language and system. Be aware that it expects you to have a basic understanding of programming in general. It helps to have a Python interpreter handy for hands-on experience, but all examples are self-contained, so the tutorial can be read off-line as well.
For a description of standard objects and modules, see . gives a more formal definition of the language. To write extensions in C or C++, read and . There are also several books covering Python in depth.
This tutorial does not attempt to be comprehensive and cover every single feature, or even every commonly used feature. Instead, it introduces many of Python’s most noteworthy features, and will give you a good idea of the language’s flavor and style. After reading it, you will be able to read and write Python modules and programs, and you will be ready to learn more about the various Python library modules described in .
The is also worth going through.
#### Previous topic
#### Next topic
### This page
« 
### Navigation
  * |
  * |
  * |
  * »
  * Greek | ΕλληνικάEnglishSpanish | españolFrench | françaisItalian | italianoJapanese | 日本語Korean | 한국어Polish | polskiBrazilian Portuguese | Português brasileiroTurkish | TürkçeSimplified Chinese | 简体中文Traditional Chinese | 繁體中文
dev (3.15)pre (3.14)***********.*************.***********.***********.72.6
  * [The Python Tutorial](https://docs.python.org/3/tutorial/)
  * Theme  Auto Light Dark |


© 2001-2025, Python Software Foundation. This page is licensed under the Python Software Foundation License Version 2. Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License. See for more information. The Python Software Foundation is a non-profit corporation. Last updated on Jul 07, 2025 (01:46 UTC). ? Created using 8.2.3. 

2025-07-07 10:16:14,960 - crawl_utils - INFO - Webpage Text: 
You signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert
python / cpython Public 
  * Sponsor 
#  Sponsor python/cpython 
##### GitHub Sponsors
##### External links
  * You must be signed in to change notification settings


The Python programming language 
### License
You must be signed in to change notification settings
# python/cpython
main
Go to file
Code
Open more actions menu
## Folders and files
Name| Name| Last commit message| Last commit date  
---|---|---|---  
## Latest commit
## History  
View all files  
## Repository files navigation
# This is Python version 3.15.0 alpha 0
Copyright © 2001 Python Software Foundation. All rights reserved.
See the end of this file for further copyright and license information.
Contents
  * Website: 
  * Source code: 
  * Issue tracker: 
  * Documentation: 
  * Developer's Guide: 


For more complete instructions on contributing to CPython development, see the .
Installable Python kits, and information about using Python, are available at .
On Unix, Linux, BSD, macOS, and Cygwin:
```
./configure
make
make test
sudo make install

```

This will install Python as python3.
You can pass many options to the configure script; run ./configure --help to find out more. On macOS case-insensitive file systems and on Cygwin, the executable is called python.exe; elsewhere it's just python.
Building a complete Python installation requires the use of various additional third-party libraries, depending on your build platform and configure options. Not all standard library modules are buildable or usable on all platforms. Refer to the section of the for current detailed information on dependencies for various Linux distributions and macOS.
On macOS, there are additional configure and build options related to macOS framework and universal builds. Refer to .
On Windows, see .
To build Windows installer, see .
If you wish, you can create a subdirectory and invoke configure from there. For example:
```
mkdir debug
cd debug
../configure --with-pydebug
make
make test

```

(This will fail if you also built at the top-level directory. You should do a make clean at the top-level first.)
To get an optimized build of Python, configure --enable-optimizations before you run make. This sets the default make targets up to enable Profile Guided Optimization (PGO) and may be used to auto-enable Link Time Optimization (LTO) on some platforms. For more details, see the sections below.
PGO takes advantage of recent versions of the GCC or Clang compilers. If used, either via configure --enable-optimizations or by manually running make profile-opt regardless of configure flags, the optimized build process will perform the following steps:
The entire Python directory is cleaned of temporary files that may have resulted from a previous compilation.
An instrumented version of the interpreter is built, using suitable compiler flags for each flavor. Note that this is just an intermediary step. The binary resulting from this step is not good for real-life workloads as it has profiling instructions embedded inside.
After the instrumented interpreter is built, the Makefile will run a training workload. This is necessary in order to profile the interpreter's execution. Note also that any output, both stdout and stderr, that may appear at this step is suppressed.
The final step is to build the actual interpreter, using the information collected from the instrumented one. The end result will be a Python binary that is optimized; suitable for distribution or production installation.
Enabled via configure's --with-lto flag. LTO takes advantage of the ability of recent compiler toolchains to optimize across the otherwise arbitrary .o file boundary when building final executables or shared libraries for additional performance gains.
We have a comprehensive overview of the changes in the document. For a more detailed change log, read , but a full accounting of changes can only be gleaned from the .
If you want to install multiple versions of Python, see the section below entitled "Installing multiple versions".
is online, updated daily.
It can also be downloaded in many formats for faster access. The documentation is downloadable in HTML, PDF, and reStructuredText formats; the latter version is primarily for documentation authors, translators, and people with special formatting requirements.
For information about building Python's documentation, refer to .
To test the interpreter, type make test in the top-level directory. The test set produces some output. You can generally ignore the messages about skipped tests due to optional features which can't be imported. If a message is printed about a failed test or a traceback or core dump is produced, something is wrong.
By default, tests are prevented from overusing resources like disk space and memory. To enable these tests, run make buildbottest.
If any tests fail, you can re-run the failing test(s) in verbose mode. For example, if test_os and test_gdb failed, you can run:
```
make test TESTOPTS="-v test_os test_gdb"

```

If the failure persists and appears to be a problem with Python rather than your environment, you can and include relevant output from that command to show the issue.
See for more on running tests.
On Unix and Mac systems if you intend to install multiple versions of Python using the same installation prefix (--prefix argument to the configure script) you must take care that your primary python executable is not overwritten by the installation of a different version. All files and directories installed using make altinstall contain the major and minor version and can thus live side-by-side. make install also creates ${prefix}/bin/python3 which refers to ${prefix}/bin/python3.X. If you intend to install multiple versions using the same prefix you must decide which version (if any) is your "primary" version. Install that version using make install. Install all other versions using make altinstall.
For example, if you want to install Python 2.7, 3.6, and 3.15 with 3.15 being the primary version, you would execute make install in your 3.15 build directory and make altinstall in the others.
See for Python 3.15 release details.
Copyright © 2001 Python Software Foundation. All rights reserved.
Copyright © 2000 BeOpen.com. All rights reserved.
Copyright © 1995-2001 Corporation for National Research Initiatives. All rights reserved.
Copyright © 1991-1995 Stichting Mathematisch Centrum. All rights reserved.
See the for information on the history of this software, terms & conditions for usage, and a DISCLAIMER OF ALL WARRANTIES.
This Python distribution contains no GNU General Public License (GPL) code, so it may be used in proprietary projects. There are interfaces to some GNU code but these are entirely optional.
All trademarks referenced herein are property of their respective holders.
## About
The Python programming language 
### Resources
### License
### Code of conduct
### Security policy
###  Uh oh! 
There was an error while loading. [Please reload this page](https://github.com/python/cpython).
### Stars
### Watchers
### Forks
## Sponsor this project
No packages published 
## Languages
  * Other 0.4% 


You can’t perform that action at this time. 

2025-07-07 10:16:21,445 - crawl_utils - INFO - Webpage Text: 
Switch to mobile version
2025 Python Packaging Survey is now live! Take the survey now 
# Find, install and publish Python packages with the Python Package Index
Or 
655,892 projects 
7,139,475 releases 
14,772,326 files
942,528 users
The Python Package Index (PyPI) is a repository of software for the Python programming language. 
PyPI helps you find and install software developed and shared by the Python community. . 
Package authors use PyPI to distribute their software. . 
Supported by

2025-07-07 10:21:33,705 - crawl_utils - INFO - Webpage Text: 
算家云学术加速服务使用文档
1 公开服务推荐
2 算家云内置加速服务
2.1 支持的学术资源
2.2 终端快速使用方法
2.2.1 终端启动代理：
2.2.2 示例操作：克隆 GitHub 仓库
2.3 终端关闭加速：
2.4 JupyterLab 快速使用：
2.5 加速效果对比
3 服务配置准备
3.1 实例与区域要求
3.2 连接方式
3.3 连通性测试（建议执行）
4 启用/关闭代理方法 code
4.1 加载脚本
4.2 控制命令一览
4.3 检查当前代理状态
5 完整使用示例
5.1 终端操作流程（推荐）
5.2 下载加速对比示例（GitHub 文件）
5.3 JupyterLab 使用流程（Python 方式）
6 最佳实践建议
6.1 一条命令启用代理、操作、关闭代理：
6.2 下载 HuggingFace 文件：
7 常见问题排查
7.1 Git 克隆失败或很慢？
7.2 pip 安装变慢？
7.3 代理状态无法生效？
8 联系与支持
# 算家云学术加速服务使用文档
## 1 公开服务推荐
以下公开服务可直接访问：
  * Github加速：（点击查看可用域名及文档）
  * HuggingFace镜像站：


## 2 算家云内置加速服务
> 声明：本服务仅限于解决学术资源访问问题，不承诺稳定性保证。如遇恶意攻击等情况可能随时停止服务。
### 2.1 支持的学术资源
  * github.com
  * githubusercontent.com
  * githubassets.com
  * huggingface.co


### 2.2 终端快速使用方法
#### 2.2.1 终端启动代理：
```
# 启用代理
source /root/sj-data/Script/SJ-proxy.sh && proxy_on

```

#### 2.2.2 示例操作：克隆 GitHub 仓库
```
# 示例操作：克隆 GitHub 仓库
git clone https://github.com/suanjia/DeepSeek-R1-Distill-Qwen-32B-GuiyangGuian-lora-v1

```

### 2.3 终端关闭加速：
> 声明：如果不再需要建议关闭学术加速，因为该加速可能对正常网络造成一定影响。
```
# 关闭代理
source /root/sj-data/Script/SJ-proxy.sh && proxy_off

```

### 2.4 JupyterLab 快速使用：
```
import subprocess
import os
result = subprocess.run(
   f"bash -c 'source /root/sj-data/Script/SJ-proxy.sh && proxy_on && env | grep -i proxy'",
      shell=True, capture_output=True, text=True
)
if result.returncode == 0:
  for line in result.stdout.splitlines():
    if '=' in line:
      var, value = line.split('=', 1)
      os.environ[var] = value
  print(f"✅ 代理已生效 HTTP_PROXY={os.environ.get('HTTP_PROXY')}")
else:
  print("❌ 代理启用失败:", result.stderr)

```

### 2.5 加速效果对比
## 3 服务配置准备
### 3.1 实例与区域要求
区域名称 | 实例类型 | 是否支持  
---|---|---  
西南 A 区 | 专业版 | ✅  
华东 A 区 | 专业版 | ✅  
西南 B 区 | 青春版 | ✅  
其他区域 | 任意 | ❌  
### 3.2 连接方式
  * ✅ 支持：WebShell 终端、SSH 终端、JupyterLab
  * ❌ 不支持：浏览器访问代理等


### 3.3 连通性测试（建议执行）
```
# 安装基础工具（计算器、下载工具、Python 包管理）
apt update && apt install -y bc wget python3-pip iputils-ping curl
# 安装网速测试工具
pip3 install speedtest-cli
# 测试网络连通性（ping GitHub）
ping -c 4 github.com
# 测试 HTTP 连接（访问 Hugging Face）
curl -I https://huggingface.co
# 运行网速测试
speedtest-cli

```

## 4 启用/关闭代理方法 code
### 4.1 加载脚本
首次使用前，需加载环境配置脚本：
```
source /root/sj-data/Script/SJ-proxy.sh

```

### 4.2 控制命令一览
命令 | 功能 | 示例输出  
---|---|---  
proxy_on | 启用代理 | [√] 已开启学术代理  
proxy_off | 禁用代理 | [×] 已关闭学术代理  
### 4.3 检查当前代理状态
```
# 检查环境变量
env | grep -i proxy
# 查看出口 IP 地址
curl http://httpbin.org/ip

```

## 5 完整使用示例
### 5.1 终端操作流程（推荐）
```
# 启用代理
source /root/sj-data/Script/SJ-proxy.sh && proxy_on
# 示例操作：克隆 GitHub 仓库
git clone https://github.com/suanjia/DeepSeek-R1-Distill-Qwen-32B-GuiyangGuian-lora-v1
# 关闭代理
proxy_off

```

### 5.2 下载加速对比示例（GitHub 文件）
```
#!/bin/bash
# 首次使用前，需加载环境配置脚本：
source /root/sj-data/Script/SJ-proxy.sh
# GitHub 真实大文件测试
FILE_URL="https://github.com/conda-forge/miniforge/releases/latest/download/Miniforge3-Linux-x86_64.sh" # ~100MB
# 直连测试
proxy_off
echo "=== 无代理测试 ==="
start_time=$(date +%s.%N)
wget -q --show-progress -O /dev/null $FILE_URL # --show-progress 只显示进度条
end_time=$(date +%s.%N)
direct_time=$(echo "$end_time - $start_time" | bc)
echo -e "\n 无代理耗时：$direct_time 秒" # -e 允许换行
# 代理测试
proxy_on
echo -e "\n=== 代理测试 ==="
start_time=$(date +%s.%N)
wget -q --show-progress -O /dev/null $FILE_URL
end_time=$(date +%s.%N)
proxy_time=$(echo "$end_time - $start_time" | bc)
echo -e "\n 启用代理耗时：$proxy_time 秒"
# 计算加速比
speedup_ratio=$(echo "scale=2; $direct_time / $proxy_time" | bc)
echo -e "\n 加速比 ≈ ${speedup_ratio}x"
# 恢复无代理状态
proxy_off

```

输出示例：
### 5.3 JupyterLab 使用流程（Python 方式）
```
import subprocess
import os
import time
class AcademicProxy:
  def __init__(self):
    self.proxy_path = "/root/sj-data/Script/SJ-proxy.sh"
  def enable(self):
    """启用代理"""
    result = subprocess.run(
      f"bash -c 'source {self.proxy_path} && proxy_on && env | grep -i proxy'",
      shell=True, capture_output=True, text=True
    )
    if result.returncode == 0:
      for line in result.stdout.splitlines():
        if '=' in line:
          var, value = line.split('=', 1)
          os.environ[var] = value
      print("✅ 学术加速已启用")
    else:
      print("❌ 启用失败:", result.stderr)
  def disable(self):
    """禁用代理"""
    subprocess.run(f"bash -c 'source {self.proxy_path} && proxy_off'", shell=True)
    for var in ["http_proxy", "HTTP_PROXY", "https_proxy", "HTTPS_PROXY", "no_proxy", "NO_PROXY"]:
      os.environ.pop(var, None)
    print("⛔ 学术加速已关闭")
  def check_status(self):
    """检查代理状态"""
    for var in ["http_proxy", "https_proxy"]:
      print(f"{var}: {os.environ.get(var, '未设置')}")
  def benchmark(self):
    """对比启用/关闭代理的克隆耗时"""
    test_repo = "https://github.com/octocat/Hello-World.git"
    try:
      subprocess.run("rm -rf /tmp/quick_test_*", shell=True)
      print("测试无代理克隆速度...")
      self.disable()
      t1_start = time.time()
      subprocess.run(f"git clone --depth=1 {test_repo} /tmp/quick_test_no_proxy",
              shell=True, timeout=30, check=True)
      t1 = time.time() - t1_start
      print("测试启用代理克隆速度...")
      self.enable()
      t2_start = time.time()
      subprocess.run(f"git clone --depth=1 {test_repo} /tmp/quick_test_proxy",
              shell=True, timeout=30, check=True)
      t2 = time.time() - t2_start
      print(f"无代理耗时: {t1:.2f}s，启用代理耗时: {t2:.2f}s，加速比: {t1/t2:.2f}x")
    finally:
      subprocess.run("rm -rf /tmp/quick_test_*", shell=True)
      self.disable()
# 使用方式
proxy = AcademicProxy()
proxy.benchmark()

```

## 6 最佳实践建议
### 6.1 一条命令启用代理、操作、关闭代理：
```
source /root/sj-data/Script/SJ-proxy.sh && proxy_on && \
git clone https://github.com/OpenBMB/cpm.cu.git && \
proxy_off

```

### 6.2 下载 HuggingFace 文件：
```
source /root/sj-data/Script/SJ-proxy.sh && proxy_on
wget https://huggingface.co/suanjia/DeepSeek-R1-Distill-Qwen-32B-GuiyangGuian-lora-v1/resolve/main/README.md?download=true
proxy_off

```

## 7 常见问题排查
### 7.1 Git 克隆失败或很慢？
```
# 配置 Git 全局 HTTP 传输缓冲区大小为 500MB（524288000 字节）
# 作用：解决推送/拉取大文件时因缓冲区不足导致的失败（如 HTTP 413 错误）
# 适用场景：需要传输大型二进制文件、LFS 对象或大仓库时
# 注意：仅影响 HTTP/HTTPS 协议，对 SSH 协议无效
git config --global http.postBuffer 524288000
# 禁用 Git 的低速传输中断阈值（设置为 0 字节/秒）
# 作用：防止在慢速网络环境下因传输速度低而被强制中断
# 适用场景：跨国网络、高延迟代理或极慢速连接
# 注意：与 http.lowSpeedTime 配合使用效果更可靠
git config --global http.lowSpeedLimit 0
# 设置 Git 低速传输容忍时间为超长值（999999 秒 ≈ 11.5 天）
# 作用：即使传输速度极低，也不会因超时而中断操作
# 适用场景：需要长时间保持连接的场景（如超大仓库克隆）
# 注意：实际网络中断时仍可能导致卡住，需手动终止
git config --global http.lowSpeedTime 999999

```

### 7.2 pip 安装变慢？
```
proxy_off # 暂时关闭代理
pip install numpy
proxy_on  # 安装完成后重新开启

```

### 7.3 代理状态无法生效？
  * 检查是否已 source 脚本
  * 确保当前 Shell 环境变量中存在 http_proxy


```
env | grep proxy

```

## 8 联系与支持
如遇以下问题，请联系技术支持：
  * 代理脚本失效或缺失
  * JupyterLab 中变量未正确注入
  * 特定实例不支持该加速功能



2025-07-07 10:21:56,977 - crawl_utils - INFO - Error: 'NoneType' object has no attribute 'fit_markdown'
2025-07-07 10:22:20,280 - crawl_utils - INFO - Error: 'NoneType' object has no attribute 'fit_markdown'
2025-07-07 10:22:43,769 - crawl_utils - INFO - Error: 'NoneType' object has no attribute 'fit_markdown'

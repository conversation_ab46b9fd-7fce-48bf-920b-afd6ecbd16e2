#!/usr/bin/env python3
"""
简化版LoRA推理脚本
基于现有的Wan2.1-I2V-14B-480P示例修改
"""

import torch
from torch.nn.parallel import DataParallel
from PIL import Image
import os
import sys

# 添加路径
sys.path.append('/root/sj-tmp/DiffSynth-Studio')

from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def main():
    print("🎬 简化版LoRA推理")
    print("=" * 40)
    
    # LoRA检查点路径
    lora_path = "./models/train/Wan2.1-I2V-14B-480P_lora_final/epoch-4.safetensors"
    
    # 检查LoRA文件
    if not os.path.exists(lora_path):
        print(f"❌ LoRA文件不存在: {lora_path}")
        return
    
    print(f"✅ 找到LoRA检查点: {lora_path}")
    
    try:
        print("📦 初始化Pipeline...")
        
        # 创建pipeline（基于原始示例）
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",
            model_configs=[
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
            ],
        )
        pipe.enable_vram_management()
        
        print("✅ Pipeline初始化成功")
        
        # 设置多GPU（如果有多张GPU）
        if torch.cuda.device_count() > 1:
            print(f"🚀 设置多GPU: {torch.cuda.device_count()} 张GPU")
            if hasattr(pipe, 'dit') and pipe.dit is not None:
                pipe.dit = DataParallel(pipe.dit, device_ids=[0, 1])
                print("✅ DiT模型已设置DataParallel")
        
        # 尝试加载LoRA权重
        print("🔧 尝试加载LoRA权重...")
        try:
            from safetensors import safe_open
            lora_weights = {}
            with safe_open(lora_path, framework="pt", device="cpu") as f:
                for key in f.keys():
                    lora_weights[key] = f.get_tensor(key)
            
            print(f"   ✅ 加载了 {len(lora_weights)} 个LoRA参数")
            
            # 简单的权重应用尝试
            if hasattr(pipe, 'dit') and pipe.dit is not None:
                dit_model = pipe.dit
                if isinstance(dit_model, DataParallel):
                    dit_model = dit_model.module
                
                # 尝试应用部分权重（简化版本）
                applied = 0
                for name, param in dit_model.named_parameters():
                    for lora_key in lora_weights:
                        if name.split('.')[-1] in lora_key:
                            try:
                                lora_weight = lora_weights[lora_key]
                                if param.shape == lora_weight.shape:
                                    param.data = lora_weight.to(param.device, param.dtype)
                                    applied += 1
                                    break
                            except:
                                continue
                
                print(f"   ✅ 应用了 {applied} 个权重")
            
        except Exception as e:
            print(f"   ⚠️  LoRA加载失败: {e}")
            print("   继续使用基础模型...")
        
        print("\n🎬 开始生成视频...")
        
        # 生成视频（基于原始示例）
        video = pipe(
            prompt="A beautiful sunset over the ocean with gentle waves, cinematic lighting, high quality",
            negative_prompt="色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走",
            seed=42, 
            tiled=True,
            height=480, 
            width=832, 
            num_frames=81,
            cfg_scale=7.5,
            num_inference_steps=50
        )
        
        # 保存视频
        output_path = "lora_inference_test.mp4"
        save_video(video, output_path, fps=15, quality=5)
        
        print(f"✅ 视频生成完成!")
        print(f"📁 保存到: {output_path}")
        
        # 检查文件
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024**2
            print(f"📊 文件大小: {file_size:.1f}MB")
        
        print("\n🎉 推理完成!")
        
    except Exception as e:
        print(f"❌ 推理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

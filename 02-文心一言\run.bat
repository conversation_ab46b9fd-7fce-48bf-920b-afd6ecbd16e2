@echo off
echo 启动文心一言 ERNIE-4.5 聊天机器人...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
pip show gradio >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 启动应用程序...
echo 请在浏览器中访问: http://localhost:7860
echo 按 Ctrl+C 停止服务器
echo.

python app.py

pause

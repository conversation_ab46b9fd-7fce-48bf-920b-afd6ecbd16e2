# !/usr/bin/env python3

# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
ImageModificationProcessorArguments

"""

from dataclasses import dataclass, field


@dataclass
class ImageModificationProcessorArguments:
    """
    args for ImageModificationProcessor
    """

    image_token_len: int = field(default=64, metadata={"help": "image placeholder num per frame"})
    image_dtype: int = field(default="uint8", metadata={"help": "image dtype"})
    render_timestamp: bool = field(default=False, metadata={"help": "render timestamp"})
    sft_shift_by_one: bool = field(default=False, metadata={"help": "SFT data_processor shift-by-one"})

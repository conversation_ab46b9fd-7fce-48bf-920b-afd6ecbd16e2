#!/usr/bin/env python3
"""
估算8×RTX 3090全量微调显存使用
"""

import torch

def estimate_model_memory():
    """估算模型显存使用"""
    
    # Wan2.1-T2V-1.3B模型参数估算
    model_params = {
        "dit_model": 1.3e9,  # 1.3B参数的DiT模型
        "text_encoder": 4.7e9,  # T5-XXL编码器
        "vae": 83e6,  # VAE编码器
    }
    
    total_params = sum(model_params.values())
    
    print("🧮 模型参数估算:")
    for name, params in model_params.items():
        print(f"   {name}: {params/1e9:.1f}B 参数")
    print(f"   总计: {total_params/1e9:.1f}B 参数")
    
    # 显存使用估算 (bf16)
    param_memory = total_params * 2 / 1024**3  # bf16 = 2 bytes per param
    
    # 梯度显存 (全量微调需要存储梯度)
    gradient_memory = param_memory  # 梯度与参数同样大小
    
    # 优化器状态 (Adam需要存储momentum和variance)
    optimizer_memory = param_memory * 2  # Adam需要2倍参数大小
    
    # 激活值显存 (取决于批次大小和序列长度)
    def estimate_activation_memory(height, width, batch_size_per_gpu):
        # 简化估算：激活值大小与输入分辨率和批次大小成正比
        activation_size = height * width * batch_size_per_gpu * 16 * 4 / 1024**3  # 粗略估算
        return activation_size
    
    print(f"\n💾 显存使用估算 (单GPU):")
    print(f"   模型参数: {param_memory:.1f}GB")
    print(f"   梯度存储: {gradient_memory:.1f}GB") 
    print(f"   优化器状态: {optimizer_memory:.1f}GB")
    
    base_memory = param_memory + gradient_memory + optimizer_memory
    print(f"   基础显存: {base_memory:.1f}GB")
    
    # 不同配置的激活值显存
    configs = [
        (128, 224, 1),
        (256, 448, 1), 
        (320, 576, 1),
        (480, 832, 1),
    ]
    
    print(f"\n📊 不同配置的总显存需求:")
    for height, width, batch_size in configs:
        activation_mem = estimate_activation_memory(height, width, batch_size)
        total_mem = base_memory + activation_mem
        
        print(f"   {height}×{width}, batch={batch_size}: {total_mem:.1f}GB")
        
        if total_mem > 24:
            print(f"      ❌ 超出单卡24GB限制")
        elif total_mem > 20:
            print(f"      ⚠️  接近24GB限制")
        else:
            print(f"      ✅ 在24GB限制内")
    
    return base_memory

def estimate_8gpu_capability():
    """估算8GPU配置能力"""
    print(f"\n🎯 8×RTX 3090配置分析:")
    print(f"   单卡显存: 24GB")
    print(f"   总显存: 192GB")
    print(f"   理论最大模型: ~96B参数 (bf16)")
    
    # 检查当前GPU状态
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"   检测到GPU: {gpu_count}块")
        
        for i in range(min(gpu_count, 8)):
            memory_gb = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: {memory_gb:.1f}GB")
    
    print(f"\n💡 全量微调建议:")
    print(f"   ✅ 推荐配置: 128×224分辨率")
    print(f"   ⚠️  谨慎配置: 256×448分辨率") 
    print(f"   ❌ 避免配置: 320×576及以上")
    print(f"   🔧 优化策略: 梯度检查点 + CPU卸载")

def check_memory_optimization():
    """检查显存优化策略"""
    print(f"\n🔧 显存优化策略:")
    
    optimizations = [
        ("混合精度训练", "bf16", "节省50%参数显存"),
        ("梯度检查点", "gradient_checkpointing", "以计算换显存"),
        ("CPU卸载", "offload", "将部分数据移到CPU"),
        ("梯度累积", "gradient_accumulation", "减少批次大小"),
        ("动态内存分配", "expandable_segments", "减少内存碎片"),
    ]
    
    for name, tech, benefit in optimizations:
        print(f"   ✅ {name} ({tech}): {benefit}")
    
    print(f"\n⚠️  全量微调挑战:")
    print(f"   - 需要存储完整模型梯度")
    print(f"   - 优化器状态占用大量显存")
    print(f"   - 激活值随分辨率快速增长")
    print(f"   - 多GPU通信开销")

def main():
    print("🎬 8×RTX 3090全量微调显存分析")
    print("=" * 60)
    
    base_memory = estimate_model_memory()
    estimate_8gpu_capability()
    check_memory_optimization()
    
    print("\n" + "=" * 60)
    print("📋 结论:")
    print("   🎯 8×RTX 3090理论上可以支持全量微调")
    print("   ⚠️  需要使用极小分辨率 (128×224)")
    print("   🔧 必须启用所有显存优化策略")
    print("   💡 建议先从LoRA训练开始验证")
    print("=" * 60)

if __name__ == "__main__":
    main()

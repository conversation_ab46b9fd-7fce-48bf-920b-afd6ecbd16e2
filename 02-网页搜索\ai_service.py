#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI总结服务模块
集成DeepSeek-R1-Distill-Qwen-32B模型API
"""

import requests
import json
import logging
from typing import Optional, Dict, Any

class AIService:
    def __init__(self, user_id=None):
        self.base_url = "http://192.168.30.21:8080/v1"
        self.api_key = "sjyun-7d2a330b15178863788bea4806a3f65a"
        self.model = "DeepSeek-R1-Distill-Qwen-32B"
        self.max_tokens = 32768
        self.user_id = user_id
        
    def answer_question_with_articles(self, articles: list, user_question: str = "") -> Optional[Dict[str, Any]]:
        """
        基于搜索到的文章回答用户问题

        Args:
            articles: 文章列表，每个元素包含title, content, url, pub_date
            user_question: 用户提出的问题

        Returns:
            包含回答信息的字典，如果失败返回None
        """
        try:
            # 构建问题回答提示词
            prompt = self._build_question_answer_prompt(articles, user_question)

            # 调用AI API
            response = self._call_ai_api(prompt, "问题回答")

            if response:
                return {
                    "answer": response,
                    "model": self.model,
                    "success": True,
                    "article_count": len(articles),
                    "question": user_question
                }
            else:
                return {
                    "answer": "AI回答生成失败",
                    "model": self.model,
                    "success": False,
                    "article_count": len(articles),
                    "question": user_question
                }

        except Exception as e:
            logging.error(f"AI回答生成异常: {str(e)}")
            return {
                "answer": f"AI回答生成异常: {str(e)}",
                "model": self.model,
                "success": False,
                "article_count": len(articles),
                "question": user_question
            }

    def summarize_article(self, title: str, content: str, url: str = "") -> Optional[Dict[str, Any]]:
        """
        为文章生成AI总结
        
        Args:
            title: 文章标题
            content: 文章内容
            url: 文章链接
            
        Returns:
            包含总结信息的字典，如果失败返回None
        """
        try:
            # 构建提示词
            prompt = self._build_prompt(title, content, url)
            
            # 调用AI API
            response = self._call_ai_api(prompt, "文章总结")
            
            if response:
                return {
                    "summary": response,
                    "model": self.model,
                    "success": True
                }
            else:
                return {
                    "summary": "AI总结生成失败",
                    "model": self.model,
                    "success": False
                }
                
        except Exception as e:
            logging.error(f"AI总结生成异常: {str(e)}")
            return {
                "summary": f"AI总结生成异常: {str(e)}",
                "model": self.model,
                "success": False
            }
    
    def _build_prompt(self, title: str, content: str, url: str = "") -> str:
        """构建AI提示词"""
        # 限制内容长度，避免超出模型限制
        max_content_length = 8000  # 保留足够空间给提示词和响应
        if len(content) > max_content_length:
            content = content[:max_content_length] + "..."
        
        prompt = f"""请对以下文章进行智能总结分析：

**文章标题**: {title}

**文章内容**:
{content}

**原文链接**: {url}

请按照以下格式提供总结：

## 📋 核心要点
- [提取3-5个关键要点]

## 📊 主要内容
[用2-3段话概括文章主要内容，保持客观准确]

## 🎯 关键信息
- **时间**: [如果文章中提到具体时间]
- **地点**: [如果文章中提到具体地点]
- **人物/机构**: [如果文章中提到重要人物或机构]
- **数据**: [如果文章中包含重要数据或数字]

## 💡 价值分析
[分析这篇文章的价值和意义，1-2段话]

请确保总结准确、客观、有条理，突出文章的核心价值。"""

        return prompt

    def _build_batch_prompt(self, articles: list, keyword: str = "") -> str:
        """构建批量总结的AI提示词"""

        # 限制总内容长度 - 减少以避免超时
        max_total_length = 8000  # 减少内容长度以提高处理速度
        current_length = 0
        processed_articles = []

        for i, article in enumerate(articles):
            title = article.get('title', f'文章{i+1}')
            content = article.get('content', '')
            url = article.get('url', '')
            pub_date = article.get('pub_date', '')

            # 为每篇文章预留基本信息长度
            article_header_length = len(f"### 文章{i+1}: {title}\n**发布日期**: {pub_date}\n**链接**: {url}\n\n")

            # 计算可用的内容长度
            available_length = max_total_length - current_length - article_header_length

            if available_length <= 100:  # 如果剩余空间太少，停止添加
                break

            # 截取内容
            if len(content) > available_length:
                content = content[:available_length] + "..."

            processed_articles.append({
                'title': title,
                'content': content,
                'url': url,
                'pub_date': pub_date
            })

            current_length += article_header_length + len(content)

        # 构建综合提示词
        articles_text = ""
        for i, article in enumerate(processed_articles):
            articles_text += f"""### 文章{i+1}: {article['title']}
**发布日期**: {article['pub_date']}
**链接**: {article['url']}

{article['content']}

---

"""

        prompt = f"""请对以下关于"{keyword}"的多篇文章进行综合性智能总结分析：

{articles_text}

请按照以下格式提供综合总结：

## 🔍 搜索主题分析
**关键词**: {keyword}
**文章数量**: {len(processed_articles)}篇
**时间跨度**: [分析文章的时间分布]

## 📋 核心要点汇总
- [提取所有文章中的5-8个最重要的共同要点]
- [按重要性排序，突出主要趋势和观点]

## 📊 主要内容综述
[用3-4段话综合概括所有文章的主要内容，识别共同主题、不同观点和发展趋势]

## 🎯 关键信息统计
- **涉及时间**: [汇总所有文章提到的重要时间节点]
- **涉及地区**: [汇总所有文章提到的重要地区]
- **重要人物/机构**: [汇总所有文章提到的重要人物或机构]
- **关键数据**: [汇总所有文章中的重要数据和统计信息]

## 📈 趋势分析
[分析这些文章反映的行业趋势、发展方向和未来预测]

## 💡 综合价值评估
[评估这批文章的整体价值，对理解"{keyword}"相关话题的帮助，以及对读者的参考意义]

## 🔗 相关建议
[基于文章内容，为对"{keyword}"感兴趣的读者提供进一步了解的建议]

请确保总结全面、客观、有条理，突出多篇文章的综合价值和洞察。"""

        return prompt

    def _build_question_answer_prompt(self, articles: list, user_question: str = "") -> str:
        """构建问题回答的AI提示词"""

        # 限制总内容长度
        max_total_length = 8000
        current_length = 0
        processed_articles = []

        for i, article in enumerate(articles):
            title = article.get('title', f'文章{i+1}')
            content = article.get('content', '')
            url = article.get('url', '')
            pub_date = article.get('pub_date', '')

            # 为每篇文章预留基本信息长度
            article_header_length = len(f"### 参考资料{i+1}: {title}\n**发布日期**: {pub_date}\n**来源**: {url}\n\n")

            # 计算可用的内容长度
            available_length = max_total_length - current_length - article_header_length

            if available_length <= 100:  # 如果剩余空间太少，停止添加
                break

            # 截取内容
            if len(content) > available_length:
                content = content[:available_length] + "..."

            processed_articles.append({
                'title': title,
                'content': content,
                'url': url,
                'pub_date': pub_date
            })

            current_length += article_header_length + len(content)

        # 构建参考资料文本
        reference_text = ""
        for i, article in enumerate(processed_articles):
            reference_text += f"""### 参考资料{i+1}: {article['title']}
**发布日期**: {article['pub_date']}
**来源**: {article['url']}

{article['content']}

---

"""

        prompt = f"""你是一个专业的信息分析助手。请仔细分析参考资料，判断其与用户问题的相关性，并基于相关信息回答问题。

**用户问题**: {user_question}

**参考资料**（已按相关性排序）:
{reference_text}

**分析和回答步骤**:

1. **首先分析相关性**: 仔细检查每个参考资料是否真正与用户问题相关
2. **识别核心信息**: 从相关资料中提取直接回答用户问题的信息
3. **诚实回答**: 如果资料不相关或信息不足，请明确说明

**回答要求**:
- **准确性优先**: 只使用确实相关的信息，不要强行关联不相关的内容
- **明确区分**: 清楚区分哪些资料相关、哪些不相关
- **直接回答**: 针对用户的具体问题给出明确答案
- **承认局限**: 如果无法回答，请诚实说明原因

**回答格式**:
## 🎯 直接回答

[基于相关资料的直接回答。如果无法回答，请明确说明原因]

## 📊 资料分析

**相关资料**:
[列出与问题相关的资料及其关键信息]

**不相关资料**:
[列出与问题不相关的资料，简要说明为什么不相关]

## 💡 建议

[如果无法完全回答问题，提供获取准确信息的建议]

**特别注意**:
- 如果所有参考资料都与问题无关，请明确说明"参考资料中没有与问题相关的信息"
- 不要为了回答问题而强行解释不相关的内容
- 保持客观和诚实，承认信息的局限性"""

        return prompt

    def _call_ai_api(self, prompt: str, description: str = "AI总结") -> Optional[str]:
        """调用AI API并进行token计数"""
        try:
            # 导入token计数和积分扣除功能
            from token_counter import count_tokens
            from database import consume_credits

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 2000,  # 限制响应长度
                "temperature": 0.7,
                "stream": False
            }

            # 计算输入token数
            input_tokens = count_tokens(prompt)
            print(f"[AI_SERVICE] 输入token数: {input_tokens}")

            logging.info(f"正在调用AI API生成{description}...")

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=120  # 增加超时时间到2分钟
            )

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    summary = result["choices"][0]["message"]["content"]

                    # 计算输出token数
                    output_tokens = count_tokens(summary)
                    total_tokens = input_tokens + output_tokens

                    print(f"[AI_SERVICE] 输出token数: {output_tokens}")
                    print(f"[AI_SERVICE] 总token数: {total_tokens}")

                    # 如果有用户ID，进行积分扣除
                    if self.user_id:
                        try:
                            # 计算积分消耗（每200个token消耗1积分）
                            cost = max(1, total_tokens // 200)
                            print(f"[AI_SERVICE] 积分消耗: {cost}")

                            # 扣除积分
                            consume_credits(self.user_id, cost, total_tokens, f"{description}: {prompt[:50]}...")
                            print(f"[AI_SERVICE] 积分扣除成功")
                        except Exception as e:
                            print(f"[AI_SERVICE] 积分扣除失败: {e}")

                    logging.info(f"AI{description}生成成功，长度: {len(summary)}字符")
                    return summary.strip()
                else:
                    logging.error("AI API响应格式异常")
                    return None
            else:
                logging.error(f"AI API调用失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logging.error("AI API调用超时")
            return None
        except requests.exceptions.RequestException as e:
            logging.error(f"AI API网络请求异常: {str(e)}")
            return None
        except Exception as e:
            logging.error(f"AI API调用异常: {str(e)}")
            return None
    
    def test_connection(self) -> bool:
        """测试AI服务连接"""
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            # 发送简单的测试请求
            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": "你好，请回复'连接正常'"
                    }
                ],
                "max_tokens": 50,
                "temperature": 0.1
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    logging.info("AI服务连接测试成功")
                    return True
            
            logging.error(f"AI服务连接测试失败: {response.status_code}")
            return False
            
        except Exception as e:
            logging.error(f"AI服务连接测试异常: {str(e)}")
            return False

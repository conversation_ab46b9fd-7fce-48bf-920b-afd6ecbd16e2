#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P LoRA多卡推理脚本
基于您训练好的LoRA权重进行视频生成
"""

import torch
from torch.nn.parallel import DataParallel
import os
import sys
import time
from PIL import Image

# 添加DiffSynth路径
sys.path.append('/root/sj-tmp/DiffSynth-Studio')

from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def load_lora_to_pipeline(pipeline, lora_path):
    """将LoRA权重加载到pipeline中"""
    print(f"🔧 加载LoRA权重: {lora_path}")
    
    if not os.path.exists(lora_path):
        print(f"❌ LoRA文件不存在: {lora_path}")
        return False
    
    try:
        # 加载SafeTensors格式的LoRA权重
        from safetensors import safe_open
        lora_weights = {}
        with safe_open(lora_path, framework="pt", device="cpu") as f:
            for key in f.keys():
                lora_weights[key] = f.get_tensor(key)
        
        print(f"   ✅ 加载了 {len(lora_weights)} 个LoRA参数")
        
        # 应用LoRA权重到DiT模型
        if hasattr(pipeline, 'dit') and pipeline.dit is not None:
            dit_model = pipeline.dit
            
            # 如果是DataParallel包装的，获取原始模型
            if isinstance(dit_model, DataParallel):
                dit_model = dit_model.module
            
            # 应用LoRA权重
            applied_count = 0
            for name, param in dit_model.named_parameters():
                # 查找对应的LoRA权重
                for lora_key, lora_weight in lora_weights.items():
                    if name in lora_key or lora_key.endswith(name):
                        try:
                            if param.shape == lora_weight.shape:
                                param.data = lora_weight.to(param.device, param.dtype)
                                applied_count += 1
                                break
                        except:
                            continue
            
            print(f"   ✅ 应用了 {applied_count} 个LoRA权重到DiT模型")
            return True
        else:
            print("   ❌ 未找到DiT模型")
            return False
            
    except Exception as e:
        print(f"   ❌ LoRA权重加载失败: {e}")
        return False

def setup_multi_gpu_pipeline(pipeline, gpu_ids=[0, 1]):
    """设置多GPU推理"""
    print(f"🚀 设置多GPU推理: {gpu_ids}")
    
    if len(gpu_ids) > 1:
        print(f"   多GPU模式: {gpu_ids}")
        
        # 确保pipeline在主GPU上
        main_device = f"cuda:{gpu_ids[0]}"
        pipeline = pipeline.to(main_device)
        
        # 设置DiT模型的DataParallel
        if hasattr(pipeline, 'dit') and pipeline.dit is not None:
            pipeline.dit = DataParallel(pipeline.dit, device_ids=gpu_ids)
            print("   ✅ DiT模型已设置DataParallel")
        
        return pipeline
    else:
        print(f"   单GPU模式: {gpu_ids[0]}")
        return pipeline.to(f"cuda:{gpu_ids[0]}")

def generate_video_with_lora(pipeline, prompt, output_path, **kwargs):
    """使用LoRA微调后的模型生成视频"""
    print(f"🎬 开始生成视频...")
    print(f"   提示词: {prompt}")
    print(f"   输出路径: {output_path}")
    
    start_time = time.time()
    
    try:
        # 默认参数（与训练时保持一致）
        generation_params = {
            "prompt": prompt,
            "negative_prompt": "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走",
            "height": 480,
            "width": 832,
            "num_frames": 81,
            "cfg_scale": 7.5,
            "num_inference_steps": 50,
            "seed": 42,
            "tiled": True
        }
        
        # 更新用户提供的参数
        generation_params.update(kwargs)
        
        # 如果有输入图像
        if "input_image_path" in kwargs and kwargs["input_image_path"]:
            input_image = Image.open(kwargs["input_image_path"]).convert("RGB")
            input_image = input_image.resize((generation_params["width"], generation_params["height"]))
            generation_params["input_image"] = input_image
            print(f"   ✅ 加载输入图像: {kwargs['input_image_path']}")
        
        print("   🔄 开始推理...")
        
        # 生成视频
        with torch.no_grad():
            video = pipeline(**generation_params)
        
        # 保存视频
        save_video(video, output_path, fps=15, quality=5)
        
        generation_time = time.time() - start_time
        print(f"   ✅ 视频生成完成，耗时: {generation_time:.2f}秒")
        print(f"   📁 保存到: {output_path}")
        
        return True, generation_time
        
    except Exception as e:
        print(f"   ❌ 视频生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False, 0

def main():
    """主函数"""
    print("🎬 Wan2.1-I2V-14B-480P LoRA多卡推理")
    print("=" * 60)
    
    # 配置参数
    LORA_CHECKPOINT = "./models/train/Wan2.1-I2V-14B-480P_lora_final/epoch-4.safetensors"
    GPU_IDS = [0, 1]  # 使用的GPU
    OUTPUT_DIR = "./lora_inference_outputs"
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 检查环境
    print("🔍 检查环境...")
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return
    
    available_gpus = torch.cuda.device_count()
    print(f"   ✅ 检测到 {available_gpus} 张GPU")
    
    # 验证GPU ID
    GPU_IDS = [gid for gid in GPU_IDS if gid < available_gpus]
    if not GPU_IDS:
        print("❌ 没有有效的GPU")
        return
    
    # 检查LoRA文件
    if not os.path.exists(LORA_CHECKPOINT):
        print(f"❌ LoRA检查点不存在: {LORA_CHECKPOINT}")
        print("   请确保训练已完成并生成了检查点文件")
        return
    
    try:
        print("📦 初始化WanVideoPipeline...")
        
        # 创建模型配置
        model_configs = [
            ModelConfig(
                model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
                origin_file_pattern="diffusion_pytorch_model*.safetensors", 
                offload_device="cpu"
            ),
            ModelConfig(
                model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", 
                offload_device="cpu"
            ),
            ModelConfig(
                model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="Wan2.1_VAE.pth", 
                offload_device="cpu"
            ),
            ModelConfig(
                model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
                origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", 
                offload_device="cpu"
            )
        ]
        
        # 初始化pipeline
        pipeline = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",
            model_configs=model_configs
        )
        
        print("   ✅ Pipeline初始化成功")
        
        # 启用VRAM管理
        pipeline.enable_vram_management()
        
        # 设置多GPU
        pipeline = setup_multi_gpu_pipeline(pipeline, GPU_IDS)
        
        # 加载LoRA权重
        lora_success = load_lora_to_pipeline(pipeline, LORA_CHECKPOINT)
        if not lora_success:
            print("⚠️  LoRA权重加载失败，将使用基础模型")
        
        print("\n🎬 开始视频生成测试...")
        
        # 测试用例
        test_cases = [
            {
                "prompt": "A beautiful sunset over the ocean with gentle waves, cinematic lighting",
                "output": f"{OUTPUT_DIR}/lora_sunset_ocean.mp4",
                "description": "海洋日落场景"
            },
            {
                "prompt": "A cute cat playing with a colorful ball in a sunny garden, high quality",
                "output": f"{OUTPUT_DIR}/lora_cat_garden.mp4", 
                "description": "猫咪花园场景"
            },
            {
                "prompt": "Majestic snow-capped mountains with clouds moving across the sky, dramatic view",
                "output": f"{OUTPUT_DIR}/lora_mountain_clouds.mp4",
                "description": "雪山云海场景"
            }
        ]
        
        successful_generations = 0
        total_time = 0
        
        for i, test_case in enumerate(test_cases):
            print(f"\n--- 测试案例 {i+1}/{len(test_cases)}: {test_case['description']} ---")
            
            success, gen_time = generate_video_with_lora(
                pipeline=pipeline,
                prompt=test_case["prompt"],
                output_path=test_case["output"]
            )
            
            if success:
                successful_generations += 1
                total_time += gen_time
        
        # 性能统计
        print(f"\n" + "=" * 60)
        print("📊 推理性能统计:")
        print(f"   成功生成: {successful_generations}/{len(test_cases)} 个视频")
        
        if successful_generations > 0:
            avg_time = total_time / successful_generations
            print(f"   平均生成时间: {avg_time:.2f}秒")
            print(f"   使用GPU数量: {len(GPU_IDS)}")
            print(f"   LoRA权重: {'✅ 已应用' if lora_success else '❌ 未应用'}")
            
            if len(GPU_IDS) > 1:
                estimated_single_gpu = avg_time * len(GPU_IDS) * 0.8
                speedup = estimated_single_gpu / avg_time
                print(f"   多GPU加速比: ~{speedup:.1f}x")
        
        print(f"\n🎉 LoRA多卡推理完成!")
        print(f"   输出目录: {OUTPUT_DIR}")
        print(f"   LoRA检查点: {LORA_CHECKPOINT}")
        
        # 显示生成的文件
        print(f"\n📁 生成的视频文件:")
        for test_case in test_cases:
            if os.path.exists(test_case["output"]):
                file_size = os.path.getsize(test_case["output"]) / 1024**2
                print(f"   ✅ {test_case['output']} ({file_size:.1f}MB)")
            else:
                print(f"   ❌ {test_case['output']} (生成失败)")
        
    except Exception as e:
        print(f"❌ Pipeline初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

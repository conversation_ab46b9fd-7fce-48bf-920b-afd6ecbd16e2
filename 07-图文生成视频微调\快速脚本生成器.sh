#!/bin/bash

# 快速脚本生成器 - 一键创建所有DiffSynth-Studio Wan模型微调脚本
# 使用方法: bash 快速脚本生成器.sh [模型类型]
# 模型类型: t2v-1.3b, t2v-14b, i2v-14b-480p

echo "=== DiffSynth-Studio Wan模型微调脚本快速生成器 ==="

# 检查参数
MODEL_TYPE=${1:-"t2v-1.3b"}
echo "选择的模型类型: $MODEL_TYPE"

# 创建目录结构
echo "创建目录结构..."
mkdir -p scripts/{data_prep,config,train,inference,merge,utils}
mkdir -p data/example_video_dataset/{videos,images}
mkdir -p models/{train,merged}

# 根据模型类型设置参数
case $MODEL_TYPE in
    "t2v-1.3b")
        MODEL_ID="Wan-AI/Wan2.1-T2V-1.3B"
        MODEL_NAME="T2V-1.3B"
        GPU_COUNT=2
        LEARNING_RATE="2e-4"
        LORA_RANK=64
        ;;
    "t2v-14b")
        MODEL_ID="Wan-AI/Wan2.1-T2V-14B"
        MODEL_NAME="T2V-14B"
        GPU_COUNT=4
        LEARNING_RATE="1e-4"
        LORA_RANK=32
        ;;
    "i2v-14b-480p")
        MODEL_ID="Wan-AI/Wan2.1-I2V-14B-480P"
        MODEL_NAME="I2V-14B-480P"
        GPU_COUNT=4
        LEARNING_RATE="1e-4"
        LORA_RANK=32
        EXTRA_INPUTS="--extra_inputs \"input_image\""
        ;;
    *)
        echo "错误：不支持的模型类型 $MODEL_TYPE"
        echo "支持的类型: t2v-1.3b, t2v-14b, i2v-14b-480p"
        exit 1
        ;;
esac

echo "模型配置:"
echo "  模型ID: $MODEL_ID"
echo "  GPU数量: $GPU_COUNT"
echo "  学习率: $LEARNING_RATE"
echo "  LoRA Rank: $LORA_RANK"

# 1. 创建Accelerate配置文件
echo "创建Accelerate配置文件..."
cat > scripts/config/accelerate_config.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: $GPU_COUNT
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
EOF

# 2. 创建数据准备脚本
echo "创建数据准备脚本..."
if [[ $MODEL_TYPE == "i2v"* ]]; then
    # I2V数据准备脚本
    cat > scripts/data_prep/prepare_i2v_dataset.py << 'EOF'
import os
import csv
import cv2
from pathlib import Path
from PIL import Image

def create_i2v_metadata_csv(dataset_path, image_folder="images", video_folder="videos"):
    """创建I2V任务的metadata.csv文件"""
    dataset_path = Path(dataset_path)
    image_folder_path = dataset_path / image_folder
    video_folder_path = dataset_path / video_folder
    
    metadata = []
    
    # 遍历图像文件，寻找对应的视频文件
    for image_file in image_folder_path.glob("*.jpg"):
        base_name = image_file.stem
        video_file = video_folder_path / f"{base_name.replace('image_', 'video_')}.mp4"
        
        if video_file.exists():
            metadata.append({
                "video_path": f"{video_folder}/{video_file.name}",
                "text": f"基于图像生成的视频动画：{image_file.stem}",
                "input_image": f"{image_folder}/{image_file.name}"
            })
    
    # 写入CSV文件
    metadata_file = dataset_path / "metadata.csv"
    with open(metadata_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['video_path', 'text', 'input_image']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for item in metadata:
            writer.writerow(item)
    
    print(f"创建I2V metadata.csv完成，共 {len(metadata)} 个图像-视频对")

if __name__ == "__main__":
    create_i2v_metadata_csv("./data/example_video_dataset")
EOF
else
    # T2V数据准备脚本
    cat > scripts/data_prep/prepare_t2v_dataset.py << 'EOF'
import os
import csv
import cv2
from pathlib import Path

def create_t2v_metadata_csv(dataset_path, video_folder="videos"):
    """创建T2V任务的metadata.csv文件"""
    dataset_path = Path(dataset_path)
    video_folder_path = dataset_path / video_folder
    
    metadata = []
    
    # 遍历视频文件
    for video_file in video_folder_path.glob("*.mp4"):
        metadata.append({
            "video_path": f"{video_folder}/{video_file.name}",
            "text": f"视频描述：{video_file.stem}"
        })
    
    # 写入CSV文件
    metadata_file = dataset_path / "metadata.csv"
    with open(metadata_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['video_path', 'text']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for item in metadata:
            writer.writerow(item)
    
    print(f"创建T2V metadata.csv完成，共 {len(metadata)} 个视频样本")

if __name__ == "__main__":
    create_t2v_metadata_csv("./data/example_video_dataset")
EOF
fi

# 3. 创建LoRA训练脚本
echo "创建LoRA训练脚本..."
cat > scripts/train/train_lora.sh << EOF
#!/bin/bash

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3
export PYTHONPATH="\${PYTHONPATH}:\$(pwd)"

# 数据集和模型路径
DATASET_BASE_PATH="data/example_video_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"
OUTPUT_PATH="./models/train/${MODEL_NAME}_lora"

echo "开始${MODEL_NAME} LoRA训练..."

accelerate launch --config_file scripts/config/accelerate_config.yaml examples/wanvideo/model_training/train.py \\
  --dataset_base_path \${DATASET_BASE_PATH} \\
  --dataset_metadata_path \${DATASET_METADATA_PATH} \\
  --height 480 \\
  --width 832 \\
  --dataset_repeat 100 \\
  --model_id_with_origin_paths "${MODEL_ID}:diffusion_pytorch_model*.safetensors,${MODEL_ID}:models_t5_umt5-xxl-enc-bf16.pth,${MODEL_ID}:Wan2.1_VAE.pth" \\
  --learning_rate ${LEARNING_RATE} \\
  --num_epochs 5 \\
  --remove_prefix_in_ckpt "pipe.dit." \\
  --output_path \${OUTPUT_PATH} \\
  --lora_base_model "dit" \\
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \\
  --lora_rank ${LORA_RANK} \\
  ${EXTRA_INPUTS}

echo "${MODEL_NAME} LoRA训练完成！模型保存在: \${OUTPUT_PATH}"
EOF

# 4. 创建推理脚本
echo "创建推理脚本..."
cat > scripts/inference/inference_base.py << EOF
import torch
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def inference_base_model():
    """使用基础${MODEL_NAME}模型进行推理"""
    
    # 创建推理管道
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cuda",
        model_configs=[
            ModelConfig(model_id="${MODEL_ID}", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
            ModelConfig(model_id="${MODEL_ID}", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
            ModelConfig(model_id="${MODEL_ID}", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
        ],
    )
    pipe.enable_vram_management()
    
    # 生成视频
    prompt = "一只可爱的小猫在阳光明媚的花园里追逐蝴蝶"
    negative_prompt = "色调艳丽，过曝，静态，细节模糊不清"
    
    video = pipe(
        prompt=prompt,
        negative_prompt=negative_prompt,
        seed=0, 
        tiled=True,
    )
    
    # 保存视频
    save_video(video, "output_base_${MODEL_TYPE}.mp4", fps=15, quality=5)
    print("基础模型推理完成")

if __name__ == "__main__":
    inference_base_model()
EOF

# 5. 创建模型合并脚本
echo "创建模型合并脚本..."
cat > scripts/merge/merge_lora.py << EOF
import torch
import argparse
from pathlib import Path
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def merge_lora_weights(lora_path, output_path):
    """将LoRA权重合并到基础模型"""
    print("开始合并LoRA权重...")
    
    # 加载基础模型
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cpu",
        model_configs=[
            ModelConfig(model_id="${MODEL_ID}", origin_file_pattern="diffusion_pytorch_model*.safetensors"),
            ModelConfig(model_id="${MODEL_ID}", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth"),
            ModelConfig(model_id="${MODEL_ID}", origin_file_pattern="Wan2.1_VAE.pth"),
        ],
    )
    
    # 加载LoRA权重
    pipe.load_lora(lora_path, alpha=1.0)
    
    print(f"模型合并完成，保存到: {output_path}")
    return True

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--lora_path", type=str, required=True)
    parser.add_argument("--output_path", type=str, required=True)
    args = parser.parse_args()
    
    merge_lora_weights(args.lora_path, args.output_path)
EOF

# 6. 创建完整流程脚本
echo "创建完整流程脚本..."
cat > scripts/full_pipeline.sh << EOF
#!/bin/bash

echo "=== ${MODEL_NAME} 完整训练和推理流程 ==="

# 步骤1: 数据准备
echo "步骤1: 数据准备..."
python scripts/data_prep/prepare_*_dataset.py

# 步骤2: LoRA训练
echo "步骤2: LoRA训练..."
bash scripts/train/train_lora.sh

# 步骤3: 基础推理
echo "步骤3: 基础推理..."
python scripts/inference/inference_base.py

# 步骤4: 模型合并
echo "步骤4: 模型合并..."
python scripts/merge/merge_lora.py \\
    --lora_path "./models/train/${MODEL_NAME}_lora/pytorch_lora_weights.safetensors" \\
    --output_path "./models/merged/${MODEL_NAME}-merged"

echo "完整流程执行完成！"
EOF

# 设置脚本权限
echo "设置脚本权限..."
chmod +x scripts/train/*.sh
chmod +x scripts/*.sh

# 创建使用说明
echo "创建使用说明..."
cat > README_脚本使用说明.md << EOF
# ${MODEL_NAME} 微调脚本使用说明

## 快速开始

### 1. 数据准备
\`\`\`bash
# 将视频文件放入 data/example_video_dataset/videos/ 文件夹
# 对于I2V模型，还需要将图像文件放入 data/example_video_dataset/images/ 文件夹

# 生成metadata.csv
python scripts/data_prep/prepare_*_dataset.py
\`\`\`

### 2. 开始训练
\`\`\`bash
# LoRA训练
bash scripts/train/train_lora.sh
\`\`\`

### 3. 推理测试
\`\`\`bash
# 基础模型推理
python scripts/inference/inference_base.py
\`\`\`

### 4. 模型合并
\`\`\`bash
# 合并LoRA权重
python scripts/merge/merge_lora.py \\
    --lora_path "./models/train/${MODEL_NAME}_lora/pytorch_lora_weights.safetensors" \\
    --output_path "./models/merged/${MODEL_NAME}-merged"
\`\`\`

### 5. 一键执行
\`\`\`bash
# 执行完整流程
bash scripts/full_pipeline.sh
\`\`\`

## 脚本说明

- \`scripts/data_prep/\`: 数据准备脚本
- \`scripts/config/\`: 配置文件
- \`scripts/train/\`: 训练脚本
- \`scripts/inference/\`: 推理脚本
- \`scripts/merge/\`: 模型合并脚本

## 注意事项

1. 确保已安装DiffSynth-Studio
2. 根据硬件配置调整GPU数量
3. 根据数据集大小调整训练参数
EOF

echo ""
echo "✅ 脚本生成完成！"
echo ""
echo "生成的文件："
echo "📁 scripts/"
echo "  ├── config/accelerate_config.yaml"
echo "  ├── data_prep/prepare_*_dataset.py"
echo "  ├── train/train_lora.sh"
echo "  ├── inference/inference_base.py"
echo "  ├── merge/merge_lora.py"
echo "  └── full_pipeline.sh"
echo "📄 README_脚本使用说明.md"
echo ""
echo "使用方法："
echo "1. 准备数据集"
echo "2. bash scripts/full_pipeline.sh"
echo ""
echo "或者按步骤执行："
echo "1. python scripts/data_prep/prepare_*_dataset.py"
echo "2. bash scripts/train/train_lora.sh"
echo "3. python scripts/inference/inference_base.py"
echo "4. python scripts/merge/merge_lora.py --lora_path ... --output_path ..."

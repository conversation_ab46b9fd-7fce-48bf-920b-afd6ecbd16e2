import gradio as gr
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import logging
import gc

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChatBot:
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.is_loaded = False
        # 提供多个模型选项
        self.model_options = {
            "ERNIE-4.5": "baidu/ERNIE-4.5-21B-A3B-PT",
            "Qwen2.5-7B": "Qwen/Qwen2.5-7B-Instruct",
            "ChatGLM3-6B": "THUDM/chatglm3-6b",
            "Baichuan2-7B": "baichuan-inc/Baichuan2-7B-Chat"
        }
        self.current_model = "ERNIE-4.5"
        
    def load_model(self, model_choice="ERNIE-4.5"):
        """加载指定模型"""
        try:
            self.current_model = model_choice
            model_name = self.model_options[model_choice]
            logger.info(f"正在加载模型: {model_name}")
            
            # 清理之前的模型
            if self.model is not None:
                del self.model
                del self.tokenizer
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                gc.collect()
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True,
                use_fast=False
            )
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                trust_remote_code=True,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                low_cpu_mem_usage=True
            )
            
            self.is_loaded = True
            logger.info(f"模型 {model_choice} 加载完成！")
            return f"✅ 模型 {model_choice} 加载成功！"
            
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            self.is_loaded = False
            
            # 如果是ERNIE模型失败，建议使用其他模型
            if model_choice == "ERNIE-4.5":
                return f"❌ ERNIE-4.5 模型加载失败: {str(e)}\n\n💡 建议尝试其他模型：\n- Qwen2.5-7B (推荐)\n- ChatGLM3-6B\n- Baichuan2-7B"
            else:
                return f"❌ 模型 {model_choice} 加载失败: {str(e)}"
    
    def generate_response(self, prompt, max_tokens=512, temperature=0.7, top_p=0.9):
        """生成回复"""
        if not self.is_loaded:
            return "❌ 请先加载模型！"
        
        if not prompt.strip():
            return "❌ 请输入有效的问题！"
        
        try:
            # 根据不同模型使用不同的对话格式
            if self.current_model == "ERNIE-4.5":
                messages = [{"role": "user", "content": prompt}]
                text = self.tokenizer.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )
            elif self.current_model == "Qwen2.5-7B":
                messages = [{"role": "user", "content": prompt}]
                text = self.tokenizer.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )
            elif self.current_model == "ChatGLM3-6B":
                text = f"<|user|>\n{prompt}<|assistant|>\n"
            else:  # Baichuan2
                text = f"<reserved_106>{prompt}<reserved_107>"
            
            # 编码输入
            inputs = self.tokenizer(text, return_tensors="pt")
            if torch.cuda.is_available():
                inputs = inputs.to(self.model.device)
            
            # 生成回复
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs.input_ids,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码输出
            response = self.tokenizer.decode(
                outputs[0][len(inputs.input_ids[0]):], 
                skip_special_tokens=True
            ).strip()
            
            # 清理内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
            
            return response
            
        except Exception as e:
            logger.error(f"生成回复失败: {str(e)}")
            return f"❌ 生成回复失败: {str(e)}"

# 创建聊天机器人实例
chatbot = ChatBot()

def load_model_interface(model_choice):
    """模型加载接口"""
    return chatbot.load_model(model_choice)

def chat_interface(message, history, max_tokens, temperature, top_p):
    """聊天接口"""
    if not message.strip():
        return history, ""
    
    response = chatbot.generate_response(message, max_tokens, temperature, top_p)
    history.append([message, response])
    return history, ""

# 创建Gradio界面
with gr.Blocks(title="多模型聊天机器人", theme=gr.themes.Soft()) as demo:
    gr.HTML("""
    <div style="text-align: center; margin-bottom: 20px;">
        <h1>🤖 多模型聊天机器人</h1>
        <p>支持多种大语言模型的智能对话系统</p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=3):
            # 模型选择和加载
            with gr.Group():
                gr.Markdown("### 📥 模型管理")
                with gr.Row():
                    model_dropdown = gr.Dropdown(
                        choices=list(chatbot.model_options.keys()),
                        value="ERNIE-4.5",
                        label="选择模型",
                        scale=2
                    )
                    load_btn = gr.Button("🔄 加载模型", variant="primary", scale=1)
                
                status_display = gr.Textbox(
                    label="状态",
                    value="⏳ 请选择模型并点击加载",
                    interactive=False
                )
            
            # 聊天区域
            chatbot_interface = gr.Chatbot(
                label="💬 对话",
                height=450,
                show_copy_button=True,
                type="tuples"  # 明确指定类型
            )
            
            with gr.Row():
                msg_input = gr.Textbox(
                    label="输入消息",
                    placeholder="请输入您的问题...",
                    lines=2,
                    scale=4
                )
                send_btn = gr.Button("📤 发送", variant="primary", scale=1)
            
            with gr.Row():
                clear_btn = gr.Button("🗑️ 清空对话", variant="secondary")
                example_btn = gr.Button("💡 示例问题", variant="secondary")
        
        with gr.Column(scale=1):
            gr.Markdown("### ⚙️ 生成参数")
            
            max_tokens = gr.Slider(
                minimum=50, maximum=1024, value=512, step=50,
                label="最大生成长度"
            )
            
            temperature = gr.Slider(
                minimum=0.1, maximum=1.5, value=0.7, step=0.1,
                label="温度 (创造性)"
            )
            
            top_p = gr.Slider(
                minimum=0.1, maximum=1.0, value=0.9, step=0.05,
                label="Top-p (多样性)"
            )
            
            gr.Markdown("""
            ### 🎯 模型说明
            - **ERNIE-4.5**: 百度文心一言，中文优化
            - **Qwen2.5-7B**: 阿里通义千问，平衡性能
            - **ChatGLM3-6B**: 清华智谱，对话专用
            - **Baichuan2-7B**: 百川智能，开源友好
            
            ### 💡 使用建议
            1. 首次使用建议选择 Qwen2.5-7B
            2. 如需中文优化可选择 ERNIE-4.5
            3. 模型加载需要时间，请耐心等待
            """)
    
    # 示例问题
    examples = gr.Examples(
        examples=[
            ["请介绍一下人工智能的发展历史"],
            ["如何学习Python编程？"],
            ["写一首关于春天的诗"],
            ["解释什么是机器学习"],
            ["推荐几本好书"]
        ],
        inputs=msg_input
    )
    
    # 事件绑定
    load_btn.click(
        fn=load_model_interface,
        inputs=model_dropdown,
        outputs=status_display
    )
    
    send_btn.click(
        fn=chat_interface,
        inputs=[msg_input, chatbot_interface, max_tokens, temperature, top_p],
        outputs=[chatbot_interface, msg_input]
    )
    
    msg_input.submit(
        fn=chat_interface,
        inputs=[msg_input, chatbot_interface, max_tokens, temperature, top_p],
        outputs=[chatbot_interface, msg_input]
    )
    
    clear_btn.click(
        fn=lambda: [],
        outputs=chatbot_interface
    )
    
    example_btn.click(
        fn=lambda: "请介绍一下人工智能的发展历史",
        outputs=msg_input
    )

if __name__ == "__main__":
    print("🚀 启动多模型聊天机器人...")
    print("📍 访问地址: http://localhost:7860")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )

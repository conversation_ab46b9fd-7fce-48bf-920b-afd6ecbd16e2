#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的爬取功能

这个脚本用于测试改进后的 CrawlUtils 类，验证多种内容提取方法的效果。
"""

import asyncio
import logging
import sys
from crawl_utils import CrawlUtils


def setup_logging():
    """配置详细的日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_crawl.log', encoding='utf-8')
        ]
    )


async def test_single_url(url: str):
    """测试单个URL的爬取效果"""
    print(f"\n🔍 测试URL: {url}")
    print("=" * 60)
    
    # 创建改进的爬虫实例
    crawler = CrawlUtils(page_timeout=45000, max_retries=2)
    
    try:
        text = await crawler.get_webpage_text(url)
        
        if text:
            print(f"✅ 成功提取内容")
            print(f"📊 文本长度: {len(text)} 字符")
            print(f"📊 单词数量: {len(text.split())} 个")
            
            # 显示前500字符的预览
            preview_length = min(500, len(text))
            preview = text[:preview_length]
            if len(text) > preview_length:
                preview += "..."
            
            print(f"📝 内容预览:")
            print("-" * 40)
            print(preview)
            print("-" * 40)
            
            return text
        else:
            print("❌ 内容提取失败")
            return None
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return None


async def test_all_urls():
    """测试所有算家云URL"""
    
    test_urls = [
        "https://www.suanjiayun.com/help?id=684fe3c9071c181fe93c7d74",  # 学术加速服务
        "https://www.suanjiayun.com/help?id=6746dd17e254decae19ccdb1",  # 其他文档1
        "https://www.suanjiayun.com/help?id=6746dda3e254decae19ccdb7",  # 其他文档2
        "https://www.suanjiayun.com/help?id=6746dd84e254decae19ccdb5",  # 其他文档3 (之前成功的)
    ]
    
    print("🚀 开始测试改进后的爬取功能")
    print("=" * 50)
    
    results = {}
    successful_count = 0
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n[{i}/{len(test_urls)}] 测试进行中...")
        
        text = await test_single_url(url)
        results[url] = text
        
        if text:
            successful_count += 1
        
        # 在测试之间添加延迟
        if i < len(test_urls):
            print("⏳ 等待3秒后继续...")
            await asyncio.sleep(3)
    
    # 显示最终统计
    print(f"\n📊 测试结果统计:")
    print(f"  ✅ 成功: {successful_count}")
    print(f"  ❌ 失败: {len(test_urls) - successful_count}")
    print(f"  📈 成功率: {successful_count/len(test_urls)*100:.1f}%")
    
    # 保存成功的结果
    successful_results = {url: text for url, text in results.items() if text}
    
    if successful_results:
        print(f"\n📁 保存成功结果...")
        
        filename = "test_results.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("改进后的爬取测试结果\n")
                f.write("=" * 40 + "\n\n")
                
                for i, (url, text) in enumerate(successful_results.items(), 1):
                    f.write(f"{i}. URL: {url}\n")
                    f.write(f"   长度: {len(text)} 字符\n")
                    f.write(f"   内容:\n{text}\n")
                    f.write("\n" + "-" * 40 + "\n\n")
            
            print(f"✅ 结果已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ 保存结果时出错: {e}")
    
    return results


async def test_with_different_configs():
    """使用不同配置测试同一个URL"""
    
    test_url = "https://www.suanjiayun.com/help?id=6746dd17e254decae19ccdb1"  # 之前失败的URL
    
    print(f"\n🔬 使用不同配置测试URL:")
    print(f"   {test_url}")
    print("=" * 60)
    
    configs = [
        {"name": "默认配置", "timeout": 30000, "retries": 1},
        {"name": "长超时配置", "timeout": 60000, "retries": 2},
        {"name": "多重试配置", "timeout": 45000, "retries": 3},
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"\n[{i}/{len(configs)}] 测试 {config['name']}")
        print(f"   超时: {config['timeout']}ms, 重试: {config['retries']}次")
        
        crawler = CrawlUtils(
            page_timeout=config['timeout'], 
            max_retries=config['retries']
        )
        
        try:
            text = await crawler.get_webpage_text(test_url)
            
            if text:
                print(f"   ✅ 成功! 提取了 {len(text)} 字符")
                # 显示前100字符
                preview = text[:100] + "..." if len(text) > 100 else text
                print(f"   📝 预览: {preview}")
                break  # 成功后就停止测试
            else:
                print(f"   ❌ 失败")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        # 配置间延迟
        if i < len(configs):
            await asyncio.sleep(2)


async def main():
    """主测试函数"""
    setup_logging()
    
    print("🧪 CrawlUtils 改进效果测试")
    print("=" * 40)
    
    try:
        # 测试1: 所有URL的基本测试
        print("\n📋 测试1: 基本功能测试")
        await test_all_urls()
        
        # 测试2: 不同配置测试
        print("\n📋 测试2: 配置优化测试")
        await test_with_different_configs()
        
        print(f"\n🎉 所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试已退出")
    except Exception as e:
        print(f"❌ 测试启动失败: {e}")

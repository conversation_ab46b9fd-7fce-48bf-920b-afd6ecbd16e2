#!/usr/bin/env python3
"""
FastDeploy ERNIE 测试脚本
用于验证FastDeploy和ERNIE模型是否能正常工作
"""

import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """测试必要的包导入"""
    try:
        logger.info("测试导入 fastdeploy...")
        from fastdeploy import LLM, SamplingParams
        logger.info("✅ fastdeploy 导入成功")
        return True
    except ImportError as e:
        logger.error(f"❌ fastdeploy 导入失败: {e}")
        logger.info("请安装 FastDeploy:")
        logger.info("  GPU版本: pip install fastdeploy-gpu-python")
        logger.info("  CPU版本: pip install fastdeploy-python")
        return False

def test_model_loading():
    """测试模型加载"""
    try:
        from fastdeploy import LLM, SamplingParams
        
        logger.info("正在加载 ERNIE-4.5-0.3B-Paddle 模型...")
        
        # 创建LLM实例
        llm = LLM(
            model="baidu/ERNIE-4.5-0.3B-Paddle",
            max_model_len=32768,
            trust_remote_code=True
        )
        
        logger.info("✅ 模型加载成功")
        return llm
        
    except Exception as e:
        logger.error(f"❌ 模型加载失败: {e}")
        return None

def test_generation(llm):
    """测试文本生成"""
    try:
        logger.info("测试文本生成...")
        
        # 设置采样参数
        sampling_params = SamplingParams(
            temperature=0.8,
            top_p=0.95,
            max_tokens=100
        )
        
        # 测试提示
        prompt = "Write me a short poem about large language model."
        
        logger.info(f"输入提示: {prompt}")
        
        # 生成文本
        outputs = llm.generate(prompt, sampling_params)
        
        if outputs and len(outputs) > 0:
            response = outputs[0].outputs[0].text.strip()
            logger.info("✅ 文本生成成功")
            logger.info(f"生成结果:\n{response}")
            return True
        else:
            logger.error("❌ 没有生成有效输出")
            return False
            
    except Exception as e:
        logger.error(f"❌ 文本生成失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始 FastDeploy ERNIE 测试")
    
    # 测试1: 导入检查
    logger.info("\n=== 测试1: 包导入检查 ===")
    if not test_imports():
        logger.error("导入测试失败，退出")
        sys.exit(1)
    
    # 测试2: 模型加载
    logger.info("\n=== 测试2: 模型加载测试 ===")
    llm = test_model_loading()
    if llm is None:
        logger.error("模型加载失败，退出")
        sys.exit(1)
    
    # 测试3: 文本生成
    logger.info("\n=== 测试3: 文本生成测试 ===")
    if not test_generation(llm):
        logger.error("文本生成失败")
        sys.exit(1)
    
    logger.info("\n🎉 所有测试通过！FastDeploy ERNIE 工作正常")
    logger.info("现在可以运行 Gradio 应用: python app_fastdeploy.py")

if __name__ == "__main__":
    main()

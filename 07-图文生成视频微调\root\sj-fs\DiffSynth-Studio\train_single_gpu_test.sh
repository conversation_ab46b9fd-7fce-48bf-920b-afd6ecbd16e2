#!/bin/bash

# 单GPU测试训练脚本 - 避免多进程下载冲突
# 使用单张GPU进行LoRA微调测试

export CUDA_VISIBLE_DEVICES=0

echo "🚀 开始单GPU LoRA训练测试..."

python examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 480 \
    --width 832 \
    --dataset_repeat 10 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --learning_rate 1e-4 \
    --num_epochs 1 \
    --gradient_accumulation_steps 4 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/single_gpu_test" \
    --lora_base_model "dit" \
    --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
    --lora_rank 32

echo "✅ 单GPU训练测试完成"

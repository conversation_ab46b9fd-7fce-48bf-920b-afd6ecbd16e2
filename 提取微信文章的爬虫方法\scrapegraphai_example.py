# scrapegraphai_example.py

# 导入必要的库
from scrapegraphai.graphs import SmartScraperGraph
import os
import json

# --- 示例 1: 使用 SmartScraperGraph 刮取网页内容 (Ollama 本地模型) ---
print("\n--- 示例 1: 使用 SmartScraperGraph 刮取网页内容 (Ollama 本地模型) ---")

# 配置图的参数，特别是 LLM (大型语言模型) 的设置
# 这里使用 Ollama 作为本地 LLM 服务
# 确保你的 Ollama 服务正在运行，并且已经拉取了指定的模型 (例如 'mistral')
# 例如，在终端运行：ollama run mistral
graph_config_ollama = {
    "llm": {
        "model": "ollama/mistral",  # 指定 Ollama 中的模型名称
        "temperature": 0,           # 设置生成文本的随机性，0 表示确定性最高
        "format": "json",           # 指定 LLM 输出格式为 JSON
        "base_url": "http://localhost:11434", # Ollama 服务的默认地址
    },
    "embeddings": {
        "model": "ollama/nomic-embed-text", # 用于生成文本嵌入的模型
        "base_url": "http://localhost:11434",
    }
}

# 定义要刮取的网页 URL
url_to_scrape_ollama = "https://perinim.github.io/projects"

# 定义用户提示，告诉 LLM 你想从网页中提取什么信息
prompt_ollama = "List me all the articles with their title and description."

print(f"正在使用 Scrapegraph-ai (Ollama) 刮取 URL: {url_to_scrape_ollama}")
print(f"提取提示: {prompt_ollama}")

try:
    # 创建 SmartScraperGraph 实例
    # prompt: 用户提示
    # source: 要刮取的 URL 或 HTML 内容
    # config: 图的配置，包括 LLM 设置
    smart_scraper_graph_ollama = SmartScraperGraph(
        prompt=prompt_ollama,
        source=url_to_scrape_ollama,
        config=graph_config_ollama
    )

    # 运行刮取任务
    result_ollama = smart_scraper_graph_ollama.run()

    # 打印刮取结果
    # 结果通常是一个字典，包含提取到的结构化数据
    print("\n--- 刮取到的内容 (Ollama) ---")
    print(json.dumps(result_ollama, indent=2, ensure_ascii=False))

except Exception as e:
    print(f"Scrapegraph-ai (Ollama) 刮取失败: {e}")
    print("请确保 Ollama 服务正在运行，并且已拉取 'mistral' 和 'nomic-embed-text' 模型。")

# --- 示例 2: 使用 SmartScraperGraph 刮取网页内容 (OpenAI 模型) ---
print("\n--- 示例 2: 使用 SmartScraperGraph 刮取网页内容 (OpenAI 模型) ---")

# 设置你的 OpenAI API 密钥
# 建议将 API 密钥设置为环境变量，以保证安全性
# 例如：os.environ.get("OPENAI_API_KEY")
OPENAI_API_KEY = "YOUR_OPENAI_API_KEY" # 请替换为你的实际 API 密钥

# 配置图的参数，特别是 LLM 的设置
# 这里使用 OpenAI 的 GPT-3.5-turbo 模型
graph_config_openai = {
    "llm": {
        "api_key": OPENAI_API_KEY, # OpenAI API 密钥
        "model": "gpt-3.5-turbo",  # 指定 OpenAI 模型名称
        "temperature": 0,
    },
}

# 定义要刮取的网页 URL
url_to_scrape_openai = "https://perinim.github.io/projects"

# 定义用户提示
prompt_openai = "List me all the projects with their description and the author."

print(f"正在使用 Scrapegraph-ai (OpenAI) 刮取 URL: {url_to_scrape_openai}")
print(f"提取提示: {prompt_openai}")

# 检查 OpenAI API 密钥是否已设置
if OPENAI_API_KEY == "YOUR_OPENAI_API_KEY":
    print("警告: 未设置 OpenAI API 密钥。跳过 OpenAI 示例。")
else:
    try:
        # 创建 SmartScraperGraph 实例
        smart_scraper_graph_openai = SmartScraperGraph(
            prompt=prompt_openai,
            source=url_to_scrape_openai,
            config=graph_config_openai
        )

        # 运行刮取任务
        result_openai = smart_scraper_graph_openai.run()

        # 打印刮取结果
        print("\n--- 刮取到的内容 (OpenAI) ---")
        print(json.dumps(result_openai, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"Scrapegraph-ai (OpenAI) 刮取失败: {e}")
        print("请检查 OpenAI API 密钥是否有效，或网络连接是否正常。")

print("\nScrapegraph-ai 示例执行完毕。")



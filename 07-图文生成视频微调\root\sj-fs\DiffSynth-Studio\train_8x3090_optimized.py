#!/usr/bin/env python3
"""
针对8×RTX 3090优化的Wan视频模型训练脚本
每张3090有24GB显存，总共192GB显存，可以支持大规模训练
"""

import os
import sys
import torch
import argparse
from pathlib import Path

def setup_environment():
    """设置训练环境"""
    print("🔧 设置8×RTX 3090训练环境...")
    
    # 检查GPU
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    gpu_count = torch.cuda.device_count()
    print(f"🎯 检测到 {gpu_count} 块GPU")
    
    for i in range(gpu_count):
        gpu_name = torch.cuda.get_device_name(i)
        memory_gb = torch.cuda.get_device_properties(i).total_memory / 1024**3
        print(f"   GPU {i}: {gpu_name} ({memory_gb:.1f}GB)")
    
    if gpu_count != 8:
        print(f"⚠️  期望8块GPU，实际检测到{gpu_count}块")
    
    return True

def create_accelerate_config():
    """为8×RTX 3090创建accelerate配置"""
    config_content = """compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 8
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
"""
    
    # 创建accelerate配置目录
    config_dir = Path.home() / ".cache" / "huggingface" / "accelerate"
    config_dir.mkdir(parents=True, exist_ok=True)
    
    config_file = config_dir / "default_config.yaml"
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    print(f"✅ Accelerate配置已创建: {config_file}")
    return config_file

def get_optimal_batch_size(model_size="1.3B"):
    """根据模型大小和GPU配置计算最优批次大小"""
    if model_size == "1.3B":
        # 1.3B模型，每张3090可以处理较大的批次
        per_gpu_batch_size = 4  # 每张GPU的批次大小
        gradient_accumulation_steps = 2  # 梯度累积步数
    elif model_size == "14B":
        # 14B模型需要更小的批次大小
        per_gpu_batch_size = 1
        gradient_accumulation_steps = 4
    else:
        per_gpu_batch_size = 2
        gradient_accumulation_steps = 2
    
    total_batch_size = per_gpu_batch_size * 8 * gradient_accumulation_steps
    
    print(f"📊 批次大小配置 ({model_size}模型):")
    print(f"   每GPU批次大小: {per_gpu_batch_size}")
    print(f"   梯度累积步数: {gradient_accumulation_steps}")
    print(f"   总有效批次大小: {total_batch_size}")
    
    return per_gpu_batch_size, gradient_accumulation_steps

def create_training_script(model_type="t2v_1.3b", training_type="lora"):
    """创建针对8×RTX 3090优化的训练脚本"""
    
    # 模型配置
    model_configs = {
        "t2v_1.3b": {
            "model_id": "Wan-AI/Wan2.1-T2V-1.3B",
            "size": "1.3B",
            "height": 480,
            "width": 832
        },
        "t2v_14b": {
            "model_id": "Wan-AI/Wan2.1-T2V-14B",
            "size": "14B", 
            "height": 480,
            "width": 832
        },
        "i2v_14b_480p": {
            "model_id": "Wan-AI/Wan2.1-I2V-14B-480P",
            "size": "14B",
            "height": 480,
            "width": 832
        }
    }
    
    if model_type not in model_configs:
        print(f"❌ 不支持的模型类型: {model_type}")
        return None
    
    config = model_configs[model_type]
    per_gpu_batch_size, gradient_accumulation_steps = get_optimal_batch_size(config["size"])
    
    # 创建训练脚本
    script_content = f"""#!/bin/bash

# 8×RTX 3090优化的Wan视频模型训练脚本
# 模型: {config['model_id']}
# 训练类型: {training_type.upper()}

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

accelerate launch \\
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \\
    --num_processes 8 \\
    --mixed_precision bf16 \\
    examples/wanvideo/model_training/train.py \\
    --dataset_base_path "./data/example_video_dataset" \\
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \\
    --height {config['height']} \\
    --width {config['width']} \\
    --dataset_repeat 100 \\
    --model_id_with_origin_paths "{config['model_id']}:diffusion_pytorch_model*.safetensors,{config['model_id']}:models_t5_umt5-xxl-enc-bf16.pth,{config['model_id']}:Wan2.1_VAE.pth" \\
    --learning_rate 1e-4 \\
    --num_epochs 3 \\
    --gradient_accumulation_steps {gradient_accumulation_steps} \\
    --remove_prefix_in_ckpt "pipe.dit." \\
    --output_path "./models/train/{model_type}_{training_type}_8x3090" \\
"""

    if training_type == "lora":
        script_content += f"""    --lora_base_model "dit" \\
    --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \\
    --lora_rank 64
"""
    else:
        script_content += f"""    --use_gradient_checkpointing_offload
"""
    
    script_name = f"train_{model_type}_{training_type}_8x3090.sh"
    with open(script_name, 'w') as f:
        f.write(script_content)
    
    # 添加执行权限
    os.chmod(script_name, 0o755)
    
    print(f"✅ 训练脚本已创建: {script_name}")
    return script_name

def estimate_training_time(model_type, training_type, num_epochs=3):
    """估算训练时间"""
    
    # 基于经验的时间估算（分钟）
    time_estimates = {
        ("t2v_1.3b", "lora"): 30,      # 1.3B LoRA很快
        ("t2v_1.3b", "full"): 180,     # 1.3B全量训练
        ("t2v_14b", "lora"): 90,       # 14B LoRA
        ("t2v_14b", "full"): 720,      # 14B全量训练需要更长时间
        ("i2v_14b_480p", "lora"): 90,  # I2V LoRA
        ("i2v_14b_480p", "full"): 720, # I2V全量训练
    }
    
    base_time = time_estimates.get((model_type, training_type), 60)
    estimated_time = base_time * num_epochs / 3  # 按epoch数调整
    
    hours = int(estimated_time // 60)
    minutes = int(estimated_time % 60)
    
    print(f"⏱️  预估训练时间: {hours}小时{minutes}分钟")
    print(f"   (基于8×RTX 3090，{num_epochs}个epoch)")
    
    return estimated_time

def main():
    parser = argparse.ArgumentParser(description="8×RTX 3090优化的Wan视频模型训练")
    parser.add_argument("--model", choices=["t2v_1.3b", "t2v_14b", "i2v_14b_480p"], 
                       default="t2v_1.3b", help="模型类型")
    parser.add_argument("--training-type", choices=["lora", "full"], 
                       default="lora", help="训练类型")
    parser.add_argument("--epochs", type=int, default=3, help="训练轮数")
    parser.add_argument("--setup-only", action="store_true", help="仅设置环境，不开始训练")
    
    args = parser.parse_args()
    
    print("🎬 8×RTX 3090 Wan视频模型训练器")
    print("=" * 60)
    
    # 设置环境
    if not setup_environment():
        return 1
    
    # 创建accelerate配置
    create_accelerate_config()
    
    # 创建训练脚本
    script_name = create_training_script(args.model, args.training_type)
    if not script_name:
        return 1
    
    # 估算训练时间
    estimate_training_time(args.model, args.training_type, args.epochs)
    
    if args.setup_only:
        print("✅ 环境设置完成，使用以下命令开始训练:")
        print(f"   bash {script_name}")
        return 0
    
    # 开始训练
    print(f"\n🚀 开始训练...")
    print(f"执行命令: bash {script_name}")
    
    try:
        import subprocess
        result = subprocess.run(["bash", script_name], check=True)
        print("🎉 训练完成!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败: {e}")
        return 1
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
        return 1

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Flask的DeepSeek Web搜索聊天机器人
使用HTML+Python替代Gradio
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime
from flask import Flask, render_template, request, jsonify, Response
from flask_cors import CORS

# 添加父目录到路径以导入crawl_utils
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from deepseek_client import DeepSeekClient
from baidu_search_utils import BaiduSearchUtils
from cookbook.crawl_utils import CrawlUtils

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

class WebSearchChatbot:
    def __init__(self):
        """初始化聊天机器人"""
        self.deepseek_client = DeepSeekClient()
        self.baidu_search = BaiduSearchUtils()
        self.crawl_utils = CrawlUtils()
        
        # 搜索决策提示词
        self.search_decision_prompt = """你是一个智能助手，需要判断用户的问题是否需要进行网络搜索来获取最新信息。

当前时间：{date}

对话历史：
{context}

用户问题：{query}

请分析用户的问题，判断是否需要搜索，并按以下JSON格式回复：

```json
{{
    "need_search": true/false,
    "search_queries": ["搜索词1", "搜索词2"]
}}
```

判断标准：
1. 如果问题涉及最新信息、实时数据、新闻事件、股价、天气等，需要搜索
2. 如果问题是通用知识、编程问题、数学计算等，不需要搜索
3. 如果需要搜索，请提供1-3个相关的搜索关键词

请只返回JSON格式的回复。"""

        # 回答生成提示词
        self.answer_prompt = """基于以下搜索结果，请回答用户的问题。

当前时间：{date}

搜索结果：
{search_results}

用户问题：{query}

请基于搜索结果提供准确、详细的回答。如果搜索结果不足以回答问题，请说明并提供你能给出的相关信息。"""

    async def search_and_crawl(self, search_queries):
        """执行搜索和爬取"""
        all_results = []
        
        for query in search_queries[:2]:  # 限制最多2个搜索词
            try:
                logger.info(f"搜索关键词: {query}")
                urls = await self.baidu_search.search_baidu(query, max_results=5)
                
                for url in urls[:3]:  # 每个搜索词最多爬取3个URL
                    try:
                        content = await self.crawl_utils.get_webpage_text(url['url'])
                        if content and len(content.strip()) > 100:
                            all_results.append(f"来源: {url['url']}\n内容: {content[:1000]}...")
                    except Exception as e:
                        logger.error(f"爬取URL失败 {url}: {e}")
                        continue
                        
            except Exception as e:
                logger.error(f"搜索失败 {query}: {e}")
                continue
        
        return "\n\n".join(all_results)

    async def chat_with_search(self, user_message, chat_history):
        """带搜索功能的聊天"""
        # 构建对话上下文
        context = ""
        for msg in chat_history:
            if msg['role'] == 'user':
                context += f"用户: {msg['content']}\n"
            elif msg['role'] == 'assistant':
                context += f"助手: {msg['content']}\n"
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 第一步：判断是否需要搜索
        decision_prompt = self.search_decision_prompt.format(
            date=current_time,
            context=context,
            query=user_message
        )
        
        decision_response = self.deepseek_client.simple_chat(decision_prompt)
        logger.info(f"搜索决策响应: {decision_response}")
        
        search_results_text = ""
        
        # 尝试解析搜索决策
        try:
            # 提取JSON部分
            start = decision_response.find('{')
            end = decision_response.rfind('}') + 1
            if start >= 0 and end > start:
                json_str = decision_response[start:end]
                decision_data = json.loads(json_str)
                
                if decision_data.get('need_search', False) and decision_data.get('search_queries'):
                    # 执行搜索
                    search_results_text = await self.search_and_crawl(decision_data['search_queries'])
                    logger.info(f"搜索完成，获得 {len(search_results_text)} 字符的结果")
                    
        except Exception as e:
            logger.error(f"解析搜索决策失败: {e}")
        
        # 第二步：基于搜索结果回答问题
        if search_results_text:
            final_prompt = self.answer_prompt.format(
                date=current_time,
                search_results=search_results_text,
                query=user_message
            )
        else:
            final_prompt = f"请回答以下问题：{user_message}"
        
        # 生成最终回答
        final_response = self.deepseek_client.simple_chat(final_prompt)
        
        return final_response, search_results_text

# 全局聊天机器人实例
chatbot = WebSearchChatbot()
chat_sessions = {}  # 存储聊天会话

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """聊天API"""
    try:
        data = request.json
        user_message = data.get('message', '').strip()
        session_id = data.get('session_id', 'default')
        
        if not user_message:
            return jsonify({'error': '消息不能为空'}), 400
        
        # 获取或创建聊天历史
        if session_id not in chat_sessions:
            chat_sessions[session_id] = []
        
        chat_history = chat_sessions[session_id]
        
        # 异步处理聊天
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            response, search_results = loop.run_until_complete(
                chatbot.chat_with_search(user_message, chat_history)
            )
            
            # 更新聊天历史
            chat_history.append({'role': 'user', 'content': user_message})
            chat_history.append({'role': 'assistant', 'content': response})
            
            # 限制历史长度
            if len(chat_history) > 20:
                chat_history = chat_history[-20:]
            
            chat_sessions[session_id] = chat_history
            
            return jsonify({
                'response': response,
                'search_results': bool(search_results),
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"聊天处理错误: {e}")
        return jsonify({'error': f'处理请求时发生错误: {str(e)}'}), 500

@app.route('/api/clear', methods=['POST'])
def clear_chat():
    """清空聊天历史"""
    try:
        data = request.json
        session_id = data.get('session_id', 'default')
        
        if session_id in chat_sessions:
            chat_sessions[session_id] = []
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"清空聊天历史错误: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    logger.info("启动DeepSeek Web搜索聊天机器人...")
    logger.info("访问地址: http://0.0.0.0:8088")
    
    app.run(
        host='0.0.0.0',
        port=8088,
        debug=False,
        threaded=True
    )

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试HTML结构
"""

import asyncio
import logging
from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig
import urllib.parse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def debug_html():
    """调试百度搜索HTML结构"""
    print("开始调试百度搜索HTML结构...")
    
    try:
        # 配置爬虫参数
        run_config = CrawlerRunConfig(
            page_timeout=15000,
            word_count_threshold=10,
            excluded_tags=["script", "style", "iframe", "meta", "nav", "footer"],
            exclude_external_links=False,
            exclude_internal_links=True,
            exclude_social_media_links=True,
            exclude_external_images=True,
            only_text=False,
            cache_mode=CacheMode.BYPASS,
        )
        
        # 构建百度搜索URL
        query = "Python编程"
        encoded_query = urllib.parse.quote(query)
        search_url = f"https://www.baidu.com/s?wd={encoded_query}&rn=5"
        
        print(f"搜索URL: {search_url}")
        
        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun(url=search_url, config=run_config)
        
        if result.html:
            print(f"获取到HTML内容，长度: {len(result.html)}")
            
            # 保存HTML到文件
            with open('baidu_search_debug.html', 'w', encoding='utf-8') as f:
                f.write(result.html)
            print("HTML已保存到 baidu_search_debug.html")
            
            # 查找包含链接的部分
            import re
            
            # 查找所有h3标签
            h3_matches = re.findall(r'<h3[^>]*>.*?</h3>', result.html, re.DOTALL | re.IGNORECASE)
            print(f"\n找到 {len(h3_matches)} 个h3标签:")
            for i, match in enumerate(h3_matches[:5]):
                print(f"{i+1}. {match[:200]}...")
            
            # 查找所有包含href的a标签
            a_matches = re.findall(r'<a[^>]*href="[^"]*"[^>]*>.*?</a>', result.html, re.DOTALL | re.IGNORECASE)
            print(f"\n找到 {len(a_matches)} 个a标签:")
            for i, match in enumerate(a_matches[:10]):
                print(f"{i+1}. {match[:200]}...")
                
            # 查找特定的搜索结果模式
            result_patterns = [
                r'<div[^>]*class="[^"]*result[^"]*"[^>]*>.*?</div>',
                r'<div[^>]*id="[^"]*"[^>]*>.*?<h3.*?</h3>.*?</div>',
            ]
            
            for i, pattern in enumerate(result_patterns):
                matches = re.findall(pattern, result.html, re.DOTALL | re.IGNORECASE)
                print(f"\n模式 {i+1} 找到 {len(matches)} 个匹配:")
                for j, match in enumerate(matches[:3]):
                    print(f"  {j+1}. {match[:300]}...")
        else:
            print("未获取到HTML内容")
            
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_html())

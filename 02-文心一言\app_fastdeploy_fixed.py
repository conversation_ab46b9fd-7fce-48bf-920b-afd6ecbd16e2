import gradio as gr
from fastdeploy import LLM, SamplingParams
import logging
import time

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
llm = None
model_loaded = False

def load_model():
    """加载FastDeploy ERNIE模型"""
    global llm, model_loaded
    
    try:
        logger.info("正在加载ERNIE-4.5-0.3B-Paddle模型...")
        start_time = time.time()
        
        # 尝试不同的参数组合
        try:
            # 方法1: 基本参数
            llm = LLM(
                model="baidu/ERNIE-4.5-0.3B-Paddle",
                max_model_len=32768
            )
        except Exception as e1:
            logger.warning(f"方法1失败: {e1}")
            try:
                # 方法2: 减少最大长度
                llm = LLM(
                    model="baidu/ERNIE-4.5-0.3B-Paddle",
                    max_model_len=16384
                )
            except Exception as e2:
                logger.warning(f"方法2失败: {e2}")
                try:
                    # 方法3: 最小参数
                    llm = LLM(model="baidu/ERNIE-4.5-0.3B-Paddle")
                except Exception as e3:
                    logger.error(f"所有方法都失败: {e3}")
                    raise e3
        
        load_time = time.time() - start_time
        model_loaded = True
        
        logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")
        return f"✅ 模型加载成功！\n耗时: {load_time:.2f}秒\n模型: ERNIE-4.5-0.3B-Paddle"
        
    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        model_loaded = False
        return f"❌ 模型加载失败: {str(e)}\n\n可能的解决方案:\n1. 检查网络连接\n2. 确认模型名称正确\n3. 尝试重新安装FastDeploy"

def generate_text(prompt, temperature, top_p, max_tokens):
    """生成文本"""
    global llm, model_loaded
    
    if not model_loaded:
        return "❌ 请先加载模型！"
    
    if not prompt.strip():
        return "❌ 请输入有效的问题！"
    
    try:
        # 设置采样参数
        sampling_params = SamplingParams(
            temperature=temperature,
            top_p=top_p,
            max_tokens=max_tokens
        )
        
        logger.info(f"生成回复中... 参数: temp={temperature}, top_p={top_p}, max_tokens={max_tokens}")
        start_time = time.time()
        
        # 生成回复
        outputs = llm.generate(prompt, sampling_params)
        
        generation_time = time.time() - start_time
        
        if outputs and len(outputs) > 0:
            response = outputs[0].outputs[0].text.strip()
            logger.info(f"生成完成，耗时: {generation_time:.2f}秒")
            return response
        else:
            return "❌ 模型没有生成有效回复，请尝试重新提问"
            
    except Exception as e:
        logger.error(f"生成失败: {str(e)}")
        return f"❌ 生成失败: {str(e)}"

def chat_fn(message, history, temperature, top_p, max_tokens):
    """聊天函数"""
    if not message.strip():
        return history, ""
    
    response = generate_text(message, temperature, top_p, max_tokens)
    history.append([message, response])
    return history, ""

def clear_history():
    """清空对话历史"""
    return []

def get_example_prompt():
    """获取示例提示"""
    return "写一首关于大语言模型的诗"

# 创建Gradio界面
with gr.Blocks(
    title="FastDeploy ERNIE 聊天机器人",
    theme=gr.themes.Default()
) as demo:
    
    gr.HTML("""
    <div style="text-align: center; margin-bottom: 20px;">
        <h1>🚀 FastDeploy ERNIE-4.5 聊天机器人</h1>
        <p style="color: #666;">基于FastDeploy和ERNIE-4.5-0.3B-Paddle的高性能对话系统</p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=3):
            # 模型管理
            with gr.Group():
                gr.Markdown("### 🔧 模型管理")
                with gr.Row():
                    load_btn = gr.Button("🔄 加载模型", variant="primary", size="lg")
                    example_btn = gr.Button("💡 示例问题", variant="secondary")
                
                status_text = gr.Textbox(
                    label="模型状态",
                    value="⏳ 点击'加载模型'开始",
                    interactive=False,
                    lines=3
                )
            
            # 聊天区域
            with gr.Group():
                gr.Markdown("### 💬 智能对话")
                chatbot = gr.Chatbot(
                    label="对话历史",
                    height=400,
                    show_copy_button=True,
                    type="tuples"
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="输入消息",
                        placeholder="请输入您的问题...",
                        lines=2,
                        scale=4
                    )
                    send_btn = gr.Button("📤 发送", variant="primary", scale=1)
                
                clear_btn = gr.Button("🗑️ 清空对话", variant="secondary")
        
        with gr.Column(scale=1):
            gr.Markdown("### ⚙️ 生成参数")
            
            temperature = gr.Slider(
                minimum=0.1,
                maximum=2.0,
                value=0.8,
                step=0.1,
                label="🌡️ 温度",
                info="控制生成的随机性"
            )
            
            top_p = gr.Slider(
                minimum=0.1,
                maximum=1.0,
                value=0.95,
                step=0.05,
                label="🎯 Top-p",
                info="控制词汇选择的多样性"
            )
            
            max_tokens = gr.Slider(
                minimum=50,
                maximum=1024,
                value=512,
                step=50,
                label="📏 最大长度",
                info="控制回复的最大字数"
            )
            
            gr.Markdown("""
            ### 📋 模型信息
            - **模型**: ERNIE-4.5-0.3B-Paddle
            - **框架**: FastDeploy
            - **类型**: 对话生成模型
            
            ### 💡 使用建议
            - **温度 0.1-0.5**: 更准确的回答
            - **温度 0.6-1.0**: 平衡准确性和创造性
            - **温度 1.1-2.0**: 更有创意的回答
            
            ### 🔧 故障排除
            如果遇到问题：
            1. 检查网络连接
            2. 重新加载模型
            3. 调整参数设置
            """)
    
    # 示例问题区域
    with gr.Row():
        gr.Examples(
            examples=[
                ["写一首关于大语言模型的诗"],
                ["解释什么是人工智能"],
                ["如何学习Python编程？"],
                ["介绍一下FastDeploy的优势"],
                ["写一个简单的排序算法"]
            ],
            inputs=msg,
            label="🎯 点击下方示例快速开始对话"
        )
    
    # 事件绑定
    load_btn.click(
        fn=load_model,
        outputs=status_text
    )
    
    send_btn.click(
        fn=chat_fn,
        inputs=[msg, chatbot, temperature, top_p, max_tokens],
        outputs=[chatbot, msg]
    )
    
    msg.submit(
        fn=chat_fn,
        inputs=[msg, chatbot, temperature, top_p, max_tokens],
        outputs=[chatbot, msg]
    )
    
    clear_btn.click(
        fn=clear_history,
        outputs=chatbot
    )
    
    example_btn.click(
        fn=get_example_prompt,
        outputs=msg
    )

if __name__ == "__main__":
    print("🚀 启动FastDeploy ERNIE聊天机器人...")
    print("📍 访问地址: http://localhost:7860")
    print("💡 请确保已正确安装FastDeploy")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )

﻿# 云计算核心技术Docker教程：cp/diff命令详解

**发布日期**: 2020年08月05日

**原文链接**: https://m.techweb.com.cn/article/2020-08-05/2799739.shtml

## 📄 原文内容

在docker客户端命令行中我们可以使用cp命令在容器与主机之间拷贝数据，使用diff命令检查容器里文件结构的更改。 示例 docker cp :用于容器与主机之间的数据拷贝。 语法 docker cp [OPTIONS] CONTAINER:SRC_PATH DEST_PATH|- docker cp [OPTIONS] SRC_PATH|- CONTAINER:DEST_PATH OPTIONS说明： -L :保持源目标中的链接 例如，将主机/www/test目录拷贝到容器mynginx的/www目录下，命令如下： $ docker cp /www/test mynginx:/www/ 将主机/www/runoob目录拷贝到容器mynginx中，目录重命名为web，命令如下： $ docker cp /www/test mynginx:/web 将容器mynginx的/www目录拷贝到主机的/tmp目录中,命令如下： $ docker cp mynginx:/www /tmp/ 命令 docker diff : 检查容器里文件结构的更改。 语法 docker diff [OPTIONS] CONTAINER 例如，查看容器mynginx的文件结构更改，命令如下： $ docker diff mynginx

在docker客户端命令行中我们可以使用cp命令在容器与主机之间拷贝数据，使用diff命令检查容器里文件结构的更改。

docker cp :用于容器与主机之间的数据拷贝。

例如，将主机/www/test目录拷贝到容器mynginx的/www目录下，命令如下：

将主机/www/runoob目录拷贝到容器mynginx中，目录重命名为web，命令如下：

将容器mynginx的/www目录拷贝到主机的/tmp目录中,命令如下：

docker diff : 检查容器里文件结构的更改。

例如，查看容器mynginx的文件结构更改，命令如下：

标签: 云计算 Docker教程 docker命令

标签: 云计算 Docker教程 docker命令
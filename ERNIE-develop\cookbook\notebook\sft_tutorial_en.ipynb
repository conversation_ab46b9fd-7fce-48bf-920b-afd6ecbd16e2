{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ERNIE SFT Best Practices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1 Introduction to SFT\n", "### 1.1 What is SFT?\n", "SFT (Supervised Fine-Tuning) is a key technique for customizing pre-trained large models. By continuing to train the model with a **small amount of high-quality labeled data**, it's possible to enhance its performance in specific domains at a low cost and with high speed.\n", "\n", "### 1.2 When to Use SFT\n", "- ✅ Applicable Scenarios:\n", "  - Task Adaptation: Strengthening the model's understanding of specific tasks (e.g., text classification, information extraction).\n", "  - Reinforcing existing abilities or knowledge the model already possesses.\n", "  - Teaching the model to handle more complex, composite tasks (provided it already has the ability to complete the relevant sub-tasks).\n", "\n", "- ❌ Inapplicable Scenarios:\n", "  - Injecting new internal knowledge into the model.\n", "  - Forcing the model to learn abilities it doesn't have.\n", "  - Completely correcting inherent biases from the pre-trained model.\n", "  - Tackling scenarios that require complex reasoning or long-term memory (which may require techniques like reinforcement learning).\n", "\n", "\n", "### 1.3 General Steps for SFT\n", "1. Prepare evaluation data and metrics, test the base model's capabilities, and analyze its shortcomings and issues.\n", "2. Conduct comprehensive prompt engineering.\n", "3. Determine if SFT is the right approach; prepare the training data.\n", "4. Training. Design training parameters based on the task's characteristics and the data. Key hyperparameters to focus on include:\n", "    - Batch size\n", "    - Number of training epochs / Maximum training steps\n", "    - Learning rate scheduler type / Initial learning rate\n", "    - Warmup steps\n", "5. Performance Evaluation. Using the same evaluation data and metrics, assess the performance of the fine-tuned model. If it falls short of expectations, consider further optimizing the training data or tuning the hyperparameters."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Introduction to the Best Practice Task\n", "\n", "### 2.1 Task Description\n", "For this best practice example, we've chosen a function-calling task. Given a set of tool functions and a multi-turn dialogue, the model's objective is to select the most appropriate tool to complete the task based on the dialogue context. For simplicity, the model only needs to output the name of the tool function.\n", "\n", "### 2.2 Glossary\n", "To make things clearer, we'll define the following terms:\n", "- **session**: A multi-turn dialogue, consisting of multiple question-and-answer pairs.\n", "- **round**: A single dialogue turn, including one question and one answer.\n", "- **history**: Session data formatted according to the OpenAI API standard.\n", "\n", "### 2.3 Data Source\n", "We use the popular open-source `glaive_toolcall` dataset ([link](https://huggingface.co/datasets/llamafactory/glaive_toolcall_en)). This dataset contains 1,000 entries each in Chinese and English, formatted in the ShareGPT style with the following fields:\n", "- `conversations`: The dialogue context, in `[{\"from\": \"\", \"value\": \"\"}]` format. The `from` field includes:\n", "  - `human`: User input.\n", "  - `gpt`: Model output (here, `gpt` is used generically to refer to a large model conversational assistant).\n", "  - `function_call`: The specific tool being called and its parameters.\n", "  - `observation`: The result of the tool call.\n", "- `tools`: The list of available tools and their descriptions.\n", "\n", "### 2.4 Evaluation Metrics\n", "This is a multi-class classification task. We will evaluate the model's performance using accuracy, macro-average, micro-average, and weighted-average metrics.\n", "\n", "### 2.5 Experiment Environment\n", "This tutorial uses the following environment:\n", "- 1 x 80GB A800 GPU\n", "- CUDA Version: 12.3\n", "- CUDA Driver: 525.125.06\n", "- nvcc: 12.3\n", "- gcc: 12.2\n", "- Python Version: 3.10.12\n", "\n", "### 2.6 Dependencies\n", "- **ERNIEKit**: The ERNIE large model toolchain, which provides an end-to-end workflow for training, compressing, and deploying the ERNIE 4.5 series models. It is based on PaddlePaddle v3.1 and supports training on several mainstream domestic chips.\n", "- **ERNIEKit WebUI**: A graphical interface that supports training, interactive dialogue, performance evaluation, model exporting, and other features. [Docs](docs/cli_webui_usage.md)\n", "- **[Optional] visualdl**: A tool for visualizing information like loss curves, which is already included in ERNIEKit.\n", "- **ERNIEKit inference scripts**.\n", "- **The Python dependencies** used in this tutorial.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# The Python dependencies\n", "import json\n", "import random\n", "from collections import defaultdict\n", "from string import Template\n", "\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "RANDOM_SEED = 2025"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. Data Preprocessing\n", "\n", "First, let's examine the `glaive_toolcall` dataset. We can see that there are cases where multiple function calls occur within the same session. For such data, we will split them into multiple sessions, using each function call as a boundary.\n", "\n", "Next, we will process the `glaive_toolcall` dataset to align with the data formats required by ERNIEKit for training and inference.\n", "  - **Training data**: A `jsonl` file, with each line as `{\"system\": \"\", \"src\": [], \"tgt\": [], \"label\": []}`\n", "    - `system` (str, optional): A prompt that sets system information, such as persona, task instructions, style definitions, etc.\n", "    - `src` (list(str)): The user's multi-turn dialogue content, arranged by turn and corresponding to `tgt`.\n", "    - `tgt` (list(str)): The system's multi-turn responses, arranged by turn and corresponding to `src`.\n", "    - `label` (list(int), optional): Marks whether the current dialogue turn should be used for training. `1` indicates it's included in training (for loss calculation), while `0` means it's excluded. If this field is omitted, all turns are used for training.\n", "  - **Inference data**: A `jsonl` file, with each line as `[{\"role\": \"\", \"content\": \"\"}]`. The `role` field can be `system`, `user`, or `assistant`.\n", "\n", "Considering the formats above, we will reformat the `glaive_toolcall` dataset into the following structure: a `jsonl` file where each line is `[{\"role\": \"\", \"content\": \"\"}]`. The `role` can be `system`, `user`, or `assistant`. The `user` and `assistant` roles appear in pairs according to the dialogue sequence, and the final `assistant` message records the name of the tool called."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 Prepare the Raw Data\n", "First, let's download the open-source dataset. Save at `cookbook/data/glaive_toolcall_en_1k.json`。\n", "\n", "![sft_download_dataset](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_download_dataset_en.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To facilitate processing, we will convert the ShareGPT format to the OpenAI API format and log the tool call information. We have defined several functions for data preprocessing."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def parse_function(tools, func_name_with_desc: dict):\n", "    \"\"\"Parses the tool list to get tool names and descriptions.\n", "\n", "    Args:\n", "        tools: The list of tools.\n", "        func_name_with_desc: A dictionary to store the mapping of tool names to their descriptions.\n", "\n", "    Returns:\n", "        func_name_with_desc: The updated dictionary mapping tool names to descriptions.\n", "    \"\"\"\n", "    for tool in tools:\n", "        tool_name = tool.get(\"name\", \"\")\n", "        tool_desc = tool.get(\"description\", \"\")\n", "        func_name_with_desc[tool_name] = tool_desc\n", "    return func_name_with_desc\n", "\n", "\n", "def is_valid_history(history):\n", "    \"\"\"Checks if the conversation history is valid: user and assistant roles must appear in pairs, and the last turn must be from the assistant.\n", "\n", "    Args:\n", "        history: Multi-turn dialogue data in OpenAI API format.\n", "\n", "    Returns:\n", "        bool: True if the history is valid, False otherwise.\n", "    \"\"\"\n", "    # Remove the system prompt for easier calculation.\n", "    history = [h for h in history if h.get(\"role\", \"\") != \"system\"]\n", "    if len(history) < 2 or len(history) % 2 != 0:\n", "        return False\n", "    for i in range(1, len(history), 2):\n", "        if history[i][\"role\"] != \"assistant\" or history[i - 1][\"role\"] != \"user\":\n", "            return False\n", "    else:\n", "        return True\n", "\n", "\n", "def conversations_to_history(conversations):\n", "    \"\"\"Converts conversation data from ShareGPT format to OpenAI API format.\n", "\n", "    Args:\n", "        conversations: Conversation data in ShareGPT format.\n", "\n", "    Yields:\n", "        history: Multi-turn dialogue data in OpenAI API format.\n", "    \"\"\"\n", "    NO_FUNCTION_CALL_MARK = \"<|NO_FUNCTION_CALL|>\"\n", "    had_function_call = False\n", "    history = []\n", "    for conversation in conversations:\n", "        if conversation.get(\"from\", \"\") == \"human\":\n", "            history.append({\"role\": \"user\", \"content\": conversation.get(\"value\", \"\")})\n", "        elif conversation.get(\"from\", \"\") == \"function_call\":\n", "            history.append({\"role\": \"assistant\", \"content\": json.loads(conversation.get(\"value\", \"\")).get(\"name\", \"\")})\n", "            if is_valid_history(history):\n", "                yield history\n", "                had_function_call = True\n", "                history.pop()\n", "                # If there are more turns in the dialogue, the 'assistant' field will use the 'gpt' content\n", "                # from the original data (not the 'function_call').\n", "                # Therefore, we remove the current 'assistant' to prepare for the next turn.\n", "\n", "        elif conversation.get(\"from\", \"\") == \"gpt\":\n", "            if had_function_call:\n", "                history.append({\"role\": \"assistant\", \"content\": conversation.get(\"value\", \"\")})\n", "                had_function_call = False\n", "            else:\n", "                history.append({\"role\": \"assistant\", \"content\": NO_FUNCTION_CALL_MARK})\n", "                if is_valid_history(history):\n", "                    yield history\n", "                    history.pop()\n", "                    history.append({\"role\": \"assistant\", \"content\": conversation.get(\"value\", \"\")})\n", "        else:\n", "            pass\n", "\n", "\n", "def history_to_train(history, system_prompt: str = \"\"):\n", "    \"\"\"Converts multi-turn dialogue from OpenAI API format to ERNIE training data format.\n", "\n", "    Args:\n", "        history: Multi-turn dialogue data in OpenAI API format.\n", "\n", "    Returns:\n", "        data: Training data in ERNIE format.\n", "    \"\"\"\n", "    data = {\"system\": system_prompt, \"src\": [], \"tgt\": [], \"label\": []}\n", "    if not is_valid_history(history):\n", "        return {}\n", "\n", "    for h in history:\n", "        role = h.get(\"role\", \"\")\n", "        content = h.get(\"content\", \"\")\n", "        if role == \"user\":\n", "            data[\"src\"].append(content)\n", "        elif role == \"assistant\":\n", "            data[\"tgt\"].append(content)\n", "        data[\"label\"] = [0] * (len(data[\"tgt\"]) - 1) + [1]\n", "    return data\n", "\n", "\n", "def history_to_test(history, system_prompt: str = \"\"):\n", "    \"\"\"Converts multi-turn dialogue from OpenAI API format to ERNIE inference data format.\n", "\n", "    Args:\n", "        history: Multi-turn dialogue data in OpenAI API format.\n", "\n", "    Returns:\n", "        data: Inference data in ERNIE format.\n", "    \"\"\"\n", "    return [{\"role\": \"system\", \"content\": system_prompt}] + history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We call the functions defined above to process the data."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["raw_data_path = \"../data/glaive_toolcall_en_1k.json\"\n", "history_path = raw_data_path.replace('.json', '_history.jsonl')\n", "func_name_with_desc = {}\n", "\n", "with open(raw_data_path, \"r\") as fin:\n", "    with open(history_path, \"w\") as fout:\n", "        data_list = json.load(fin)\n", "        for data in data_list:\n", "            for history in conversations_to_history(data.get(\"conversations\", [])):\n", "                fout.write(json.dumps(history, ensure_ascii=False) + \"\\n\")\n", "            # Record the function call information as well\n", "            parse_function(json.loads(data.get(\"tools\", [])), func_name_with_desc)\n", "\n", "func_name_with_desc[\"<|NO_FUNCTION_CALL|>\"] = \"No function call is needed\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Analyze the Raw Data\n", "Let's observe how each tool is used, and then construct the training and test sets accordingly."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["func_cnt = {}\n", "with open(history_path, 'r') as fin:\n", "    for line in fin:\n", "        data = json.loads(line)\n", "        func_name = data[-1].get(\"content\", \"\")\n", "        func_cnt[func_name] = func_cnt.get(func_name, 0) + 1\n", "\n", "func_cnt = dict(sorted(func_cnt.items(), key=lambda x: x[1], reverse=True))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's perform a simple visual analysis of the tool call distribution."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Set style and figure\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Prepare data\n", "tools = list(func_cnt.keys())\n", "counts = list(func_cnt.values())\n", "\n", "# Create subplots side by side\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))\n", "\n", "# Left plot: Long-tail distribution (log scale)\n", "ax1.loglog(range(1, len(counts) + 1), counts, 'o-', linewidth=2, markersize=4, color='#3498db')\n", "ax1.set_xlabel('Tool Rank', fontsize=12)\n", "ax1.set_ylabel('Call Frequency (Log Scale)', fontsize=12)\n", "ax1.set_title('Tool Call Distribution - Long-tail Pattern', fontsize=14, fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "ax1.set_xlim(1, len(counts))\n", "\n", "# Right plot: Top 20 tools bar chart\n", "top_20_tools = tools[:20]\n", "top_20_counts = counts[:20]\n", "\n", "# Truncate tool names for better display\n", "display_tools = [tool[:15] + \"...\" if len(tool) > 15 else tool for tool in top_20_tools]\n", "\n", "bars = ax2.bar(range(len(top_20_tools)), top_20_counts, color='#3498db', alpha=0.8, edgecolor='white', linewidth=0.7)\n", "ax2.set_xlabel('Tool Name', fontsize=12)\n", "ax2.set_ylabel('Call Frequency', fontsize=12)\n", "ax2.set_title('Top 20 Most Frequently Used Tools', fontsize=14, fontweight='bold')\n", "ax2.set_xticks(range(len(top_20_tools)))\n", "ax2.set_xticklabels(display_tools, rotation=45, ha='right')\n", "\n", "# Add value labels on bars\n", "for i, bar in enumerate(bars):\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + max(top_20_counts)*0.01,\n", "             f'{int(height)}', ha='center', va='bottom', fontsize=9)\n", "\n", "# Adjust layout\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total number of tools: 106\n", "Total number of calls: 2382\n", "Average calls per tool: 22.47\n", "Most frequently called tool: <|NO_FUNCTION_CALL|> (1678 calls)\n", "Number of tools called only once: 30\n", "Number of tools called 5 or more times: 36\n"]}], "source": ["# Print statistical information\n", "total_tools = len(func_cnt)\n", "total_calls = sum(func_cnt.values())\n", "print(f\"Total number of tools: {total_tools}\")\n", "print(f\"Total number of calls: {total_calls}\")\n", "print(f\"Average calls per tool: {total_calls/total_tools:.2f}\")\n", "print(f\"Most frequently called tool: {tools[0]} ({counts[0]} calls)\")\n", "print(f\"Number of tools called only once: {sum(1 for c in counts if c == 1)}\")\n", "print(f\"Number of tools called 5 or more times: {sum(1 for c in counts if c >= 5)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Set the system prompt. After some simple experiments, we will use the system prompt that performed best in our tests."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["system_prompt_template = Template(\n", "    \"\"\"You are an intelligent assistant that can find the most suitable tool to complete a task based on user needs. Please refer to the following list of tools to complete the task. Note:\n", "1. Only return the tool name, do not return any other content.\n", "2. Do not execute the task itself, only return the tool name. For example, if the user needs to convert Celsius to Fahrenheit, you only need to return the string 'convert_temperature'.\n", "3. If you think no tool needs to be called, please return the string '<|NO_FUNCTION_CALL|>'. For example, if the user is just asking for general information, or has explicitly requested not to use a tool.\n", "4. You can only call tools from the list; do not make up tools.\n", "\n", "Available tools:\n", "${tools_prompt}\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["tools_prompt = \"\"\n", "for tool_name, tool_desc in func_name_with_desc.items():\n", "    tools_prompt += f\"Tool Name: {tool_name}\\n\"\n", "    tools_prompt += f\"Tool Description: {tool_desc}\\n\"\n", "    tools_prompt += \"\\n\"\n", "tools_prompt = tools_prompt.strip()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Evaluate the Base Model's Capabilities\n", "Define the evaluation metrics."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def calculate_multiclass_metrics(data):\n", "    \"\"\"\n", "    Calculates the accuracy, precision, recall, and F1 score for a multi-class classification task.\n", "\n", "    Args:\n", "        data: A list where each element is a dictionary in the format {\"ground_truth\": \"\", \"prediction\": \"\"}.\n", "\n", "    Returns:\n", "        A dictionary containing overall metrics and per-class metrics.\n", "    \"\"\"\n", "    if not data:\n", "        return {}\n", "\n", "    # Extract all ground truth and predicted labels\n", "    y_true = [item[\"ground_truth\"] for item in data]\n", "    y_pred = [item[\"prediction\"] for item in data]\n", "\n", "    # Only consider classes present in the ground truth labels\n", "    true_classes = set(y_true)\n", "\n", "    # Calculate overall accuracy\n", "    correct = sum(1 for gt, pred in zip(y_true, y_pred) if gt == pred)\n", "    total = len(data)\n", "    accuracy = correct / total if total > 0 else 0\n", "\n", "    # Calculate metrics for each true class\n", "    class_metrics = {}\n", "    precision_list = []\n", "    recall_list = []\n", "    f1_list = []\n", "    support_list = []\n", "\n", "    for cls in true_classes:\n", "        # Calculate TP, FP, FN\n", "        tp = sum(1 for gt, pred in zip(y_true, y_pred) if gt == cls and pred == cls)\n", "        fp = sum(1 for gt, pred in zip(y_true, y_pred) if gt != cls and pred == cls)\n", "        fn = sum(1 for gt, pred in zip(y_true, y_pred) if gt == cls and pred != cls)\n", "\n", "        # Calculate precision, recall, F1 (maintaining original precision)\n", "        precision = tp / (tp + fp) if (tp + fp) > 0 else 0\n", "        recall = tp / (tp + fn) if (tp + fn) > 0 else 0\n", "        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n", "        support = sum(1 for gt in y_true if gt == cls)\n", "\n", "        class_metrics[cls] = {\n", "            \"precision\": round(precision * 100, 3),\n", "            \"recall\": round(recall * 100, 3),\n", "            \"f1_score\": round(f1 * 100, 3),\n", "            \"support\": support,\n", "        }\n", "\n", "        # Collect raw values for calculating averages\n", "        precision_list.append(precision)\n", "        recall_list.append(recall)\n", "        f1_list.append(f1)\n", "        support_list.append(support)\n", "\n", "    # Calculate macro average - using raw precision values\n", "    macro_precision = sum(precision_list) / len(precision_list) if precision_list else 0\n", "    macro_recall = sum(recall_list) / len(recall_list) if recall_list else 0\n", "    macro_f1 = sum(f1_list) / len(f1_list) if f1_list else 0\n", "\n", "    # Calculate micro average\n", "    total_tp = sum(sum(1 for gt, pred in zip(y_true, y_pred) if gt == cls and pred == cls) for cls in true_classes)\n", "    total_fp = sum(sum(1 for gt, pred in zip(y_true, y_pred) if gt != cls and pred == cls) for cls in true_classes)\n", "    total_fn = sum(sum(1 for gt, pred in zip(y_true, y_pred) if gt == cls and pred != cls) for cls in true_classes)\n", "\n", "    micro_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0\n", "    micro_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0\n", "    micro_f1 = (\n", "        2 * micro_precision * micro_recall / (micro_precision + micro_recall)\n", "        if (micro_precision + micro_recall) > 0\n", "        else 0\n", "    )\n", "\n", "    # Calculate weighted average - using raw precision values\n", "    total_support = sum(support_list)\n", "    weighted_precision = (\n", "        sum(p * s for p, s in zip(precision_list, support_list)) / total_support if total_support > 0 else 0\n", "    )\n", "    weighted_recall = sum(r * s for r, s in zip(recall_list, support_list)) / total_support if total_support > 0 else 0\n", "    weighted_f1 = sum(f * s for f, s in zip(f1_list, support_list)) / total_support if total_support > 0 else 0\n", "\n", "    return {\n", "        \"accuracy\": f\"{round(accuracy * 100, 3)}%\",\n", "        \"macro_avg\": {\n", "            \"precision\": f\"{round(macro_precision * 100, 3)}%\",\n", "            \"recall\": f\"{round(macro_recall * 100, 3)}%\",\n", "            \"f1_score\": f\"{round(macro_f1 * 100, 3)}%\",\n", "        },\n", "        \"micro_avg\": {\n", "            \"precision\": f\"{round(micro_precision * 100, 3)}%\",\n", "            \"recall\": f\"{round(micro_recall * 100, 3)}%\",\n", "            \"f1_score\": f\"{round(micro_f1 * 100, 3)}%\",\n", "        },\n", "        \"weighted_avg\": {\n", "            \"precision\": f\"{round(weighted_precision * 100, 3)}%\",\n", "            \"recall\": f\"{round(weighted_recall * 100, 3)}%\",\n", "            \"f1_score\": f\"{round(weighted_f1 * 100, 3)}%\",\n", "        },\n", "        \"per_class\": class_metrics,\n", "        \"total_samples\": total,\n", "        \"correct_predictions\": correct,\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Construct the test set. Based on the analysis above, the distribution of functions in the original dataset is highly imbalanced. We will focus on functions that have at least 5 samples, and randomly select 2 instances for each."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["with open(history_path, \"r\") as fin:\n", "    all_histories = [json.loads(line) for line in fin]\n", "\n", "# Find frequently called tools\n", "frequent_funcs = {func_name: count for func_name, count in func_cnt.items() if count >= 5}\n", "\n", "# Find the dialogue indices corresponding to the frequently called tools\n", "history_idx_called_frequent_func = defaultdict(list)\n", "for idx, history in enumerate(all_histories):\n", "    func_name = history[-1].get(\"content\", \"\")\n", "    if func_name in frequent_funcs:\n", "        history_idx_called_frequent_func[func_name].append(idx)\n", "\n", "random.seed(RANDOM_SEED)\n", "test_indices = set()\n", "for tool, history_indices in history_idx_called_frequent_func.items():\n", "    selected = random.sample(history_indices, 2)\n", "    test_indices.update(selected)\n", "\n", "# Save the test set: one version without ground truth for inference, and another with ground truth for evaluation.\n", "testset_no_gt_path = \"../data/sft_testset_no_gt.jsonl\"\n", "testset_has_gt_path = \"../data/sft_testset_has_gt.jsonl\"\n", "with open(testset_no_gt_path, \"w\") as no_gt, open(testset_has_gt_path, \"w\") as has_gt:\n", "    for idx in test_indices:\n", "        testset_history = all_histories[idx]\n", "        ground_truth = testset_history.pop().get(\"content\", \"\")\n", "        no_gt.write(\n", "            json.dumps(\n", "                history_to_test(\n", "                    history=testset_history, system_prompt=system_prompt_template.substitute(tools_prompt=tools_prompt)\n", "                ),\n", "                ensure_ascii=False,\n", "            )\n", "            + \"\\n\"\n", "        )\n", "        has_gt.write(json.dumps({\"history\": testset_history, \"ground_truth\": ground_truth}, ensure_ascii=False) + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Run inference on the test set using the base model. The WebUI does not currently support inference on test sets, so we will use an inference script from the ERNIEKit codebase.\n", "- **<PERSON><PERSON><PERSON>**: `examples/inference/scripts/infer.sh`\n", "- **Set the main hyperparameters**:\n", "  - `top_p=0.7`\n", "  - `temperature=0.7`\n", "  - `max_seq_len=128000` (ERNIE-4.5-0.3B supports a dynamic 128K context length)\n", "  - `max_dec_len=64` (our task does not require a long output)\n", "  - `weight_quantize_algo`: This line can be deleted if quantization was not used during training.\n", "\n", "> Inference process omitted."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Calculate the evaluation metrics."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Evaluation Results:\n", "Accuracy: 2.532%\n", "Macro Avg - Precision: 2.75%, Recall: 2.5%, F1: 2.083%\n", "Micro Avg - Precision: 5.714%, Recall: 2.532%, F1: 3.509%\n", "Weighted Avg - Precision: 2.785%, Recall: 2.532%, F1: 2.11%\n", "\n", "Detailed Metrics Per Class:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Class</th>\n", "      <th>Precision(%)</th>\n", "      <th>Recall(%)</th>\n", "      <th>F1 Score(%)</th>\n", "      <th>Support</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>calculate_mortgage</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>calculate_age</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>generate_password</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>calculate_area</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>create_todo</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>calculate_average</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>get_movie_details</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>calculate_tax</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>calculate_discount</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>create_calendar_event</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>calculate_distance</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>generate_random_number</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>generate_qr_code</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>calculate_loan_payment</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>create_note</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>convert_currency</td>\n", "      <td>100.0</td>\n", "      <td>50.0</td>\n", "      <td>66.667</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>search_recipe</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>convert_temperature</td>\n", "      <td>10.0</td>\n", "      <td>50.0</td>\n", "      <td>16.667</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>search_movie</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>get_definition</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>generate_invoice</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>search_books</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>search_recipes</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>get_stock_price</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>generate_random_password</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>translate_text</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>&lt;|NO_FUNCTION_CALL|&gt;</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>calculate_tip</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>send_email</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>search_jobs</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>create_invoice</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>play_music</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>calculate_mortgage_payment</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>calculate_bmi</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>calculate_discounted_price</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>search_movies</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>check_email_availability</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>calculate_interest</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>calculate_shipping_cost</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>get_news</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         Class Precision(%) Recall(%) F1 Score(%)  Support\n", "0           calculate_mortgage            0       0.0           0        2\n", "1                calculate_age            0       0.0           0        2\n", "2            generate_password            0       0.0           0        2\n", "3               calculate_area            0       0.0           0        2\n", "4                  create_todo            0       0.0           0        2\n", "5            calculate_average            0       0.0           0        2\n", "6            get_movie_details            0       0.0           0        2\n", "7                calculate_tax            0       0.0           0        2\n", "8           calculate_discount            0       0.0           0        2\n", "9        create_calendar_event            0       0.0           0        2\n", "10          calculate_distance            0       0.0           0        2\n", "11      generate_random_number            0       0.0           0        1\n", "12            generate_qr_code            0       0.0           0        2\n", "13      calculate_loan_payment            0       0.0           0        2\n", "14                 create_note            0       0.0           0        2\n", "15            convert_currency        100.0      50.0      66.667        2\n", "16               search_recipe            0       0.0           0        2\n", "17         convert_temperature         10.0      50.0      16.667        2\n", "18                search_movie            0       0.0           0        2\n", "19              get_definition            0       0.0           0        2\n", "20            generate_invoice            0       0.0           0        2\n", "21                search_books            0       0.0           0        2\n", "22              search_recipes            0       0.0           0        2\n", "23             get_stock_price            0       0.0           0        2\n", "24    generate_random_password            0       0.0           0        2\n", "25              translate_text            0       0.0           0        2\n", "26        <|NO_FUNCTION_CALL|>          0.0       0.0           0        2\n", "27               calculate_tip            0       0.0           0        2\n", "28                  send_email            0       0.0           0        2\n", "29                 search_jobs            0       0.0           0        2\n", "30              create_invoice            0       0.0           0        2\n", "31                  play_music            0       0.0           0        2\n", "32  calculate_mortgage_payment            0       0.0           0        2\n", "33               calculate_bmi            0       0.0           0        2\n", "34  calculate_discounted_price            0       0.0           0        2\n", "35               search_movies            0       0.0           0        2\n", "36    check_email_availability            0       0.0           0        2\n", "37          calculate_interest            0       0.0           0        2\n", "38     calculate_shipping_cost            0       0.0           0        2\n", "39                    get_news            0       0.0           0        2"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get inference results\n", "prediction_data_path = \"../data/infer/sft_03b_exp0.jsonl\"\n", "\n", "# To handle potentially out-of-order results, we'll reorganize and map the inference results to the input information.\n", "history2gt = {}\n", "with open(testset_has_gt_path, \"r\") as fin:\n", "    for line in fin:\n", "        data_eval = json.loads(line.strip())\n", "        history2gt[json.dumps(data_eval[\"history\"], ensure_ascii=False)] = data_eval[\"ground_truth\"]\n", "\n", "history2pred = {}\n", "with open(prediction_data_path, \"r\") as fin:\n", "    for line in fin:\n", "        data_pred = json.loads(line.strip())\n", "        history2pred[json.dumps(data_pred[1:-1], ensure_ascii=False)] = data_pred[-1][\"content\"]\n", "\n", "pred2gt = []\n", "for history, pred in history2pred.items():\n", "    pred2gt.append({\"ground_truth\": history2gt[history], \"prediction\": pred})\n", "\n", "# Calculate multi-class metrics\n", "results = calculate_multiclass_metrics(pred2gt)\n", "print(\"Evaluation Results:\")\n", "print(f\"Accuracy: {results['accuracy']}\")\n", "print(\n", "    f\"Macro Avg - Precision: {results['macro_avg']['precision']}, Recall: {results['macro_avg']['recall']}, F1: {results['macro_avg']['f1_score']}\"\n", ")\n", "print(\n", "    f\"Micro Avg - Precision: {results['micro_avg']['precision']}, Recall: {results['micro_avg']['recall']}, F1: {results['micro_avg']['f1_score']}\"\n", ")\n", "print(\n", "    f\"Weighted Avg - Precision: {results['weighted_avg']['precision']}, Recall: {results['weighted_avg']['recall']}, F1: {results['weighted_avg']['f1_score']}\"\n", ")\n", "\n", "\n", "# Convert per-class results to a DataFrame for easier analysis\n", "per_class_data = []\n", "for class_name, metrics in results['per_class'].items():\n", "    per_class_data.append(\n", "        {\n", "            'Class': class_name,\n", "            'Precision(%)': str(metrics['precision']).rstrip('%'),\n", "            'Recall(%)': str(metrics['recall']).rstrip('%'),\n", "            'F1 Score(%)': str(metrics['f1_score']).rstrip('%'),\n", "            'Support': metrics['support'],\n", "        }\n", "    )\n", "\n", "df_per_class = pd.DataFrame(per_class_data)\n", "print(\"\\nDetailed Metrics Per Class:\")\n", "df_per_class"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4 Making Training Set\n", "Evidently, the current model is completely unable to select the appropriate tool functions, making SFT a necessary step. Therefore, we will construct the training and validation sets. Of the data not included in the test set, 10% will be allocated for validation, and the remainder will be used for training."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["trainset_path = \"../data/sft_trainset.jsonl\"\n", "validset_path = \"../data/sft_validset.jsonl\"\n", "\n", "# Split the validation set from the non-test data\n", "remaining_indices = [idx for idx in range(len(all_histories)) if idx not in test_indices]\n", "random.shuffle(remaining_indices)\n", "valid_size = int(len(remaining_indices) * 0.1)\n", "valid_indices = set(remaining_indices[:valid_size])\n", "train_indices = set(remaining_indices[valid_size:])\n", "\n", "# Save the training set\n", "with open(trainset_path, \"w\") as fout:\n", "    for idx in train_indices:\n", "        history = all_histories[idx]\n", "        train_data = history_to_train(\n", "            history=history, system_prompt=system_prompt_template.substitute(tools_prompt=tools_prompt)\n", "        )\n", "        fout.write(json.dumps(train_data, ensure_ascii=False) + \"\\n\")\n", "\n", "# Save the validation set\n", "with open(validset_path, \"w\") as fout:\n", "    for idx in valid_indices:\n", "        history = all_histories[idx]\n", "        valid_data = history_to_train(\n", "            history=history, system_prompt=system_prompt_template.substitute(tools_prompt=tools_prompt)\n", "        )\n", "        fout.write(json.dumps(valid_data, ensure_ascii=False) + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4. Training\n", "### 4.1 Select the Base Model\n", "For ease of demonstration and considering the task's complexity, we'll choose the smaller ERNIE-4.5-0.3B model.\n", "\n", "### 4.2 Design Training Parameters\n", "Designing training parameters is largely based on experience. ERNIEKit allows users to set a few key parameters and automatically recommends the rest. Typically, the key parameters you need to set are:\n", "- `num_train_epochs`\n", "- `gradient_accumulation_steps`\n", "- `max_seq_len=8192`\n", "\n", "Calculate recommended parameters (see `examples/post-training/sft/README.md` for details):\n", "```bash\n", "python examples/post-training/sft/estimate_training.py \\\n", "    --train_task_config examples/post-training/sft/config/task_sft.json \\\n", "    --model_name_or_path baidu/paddle_internal/ERNIE-4.5-0.3B \\\n", "    --out_file estimate_training.json \\\n", "    --num_train_epochs 1 \\\n", "    --max_seq_len 8192 \\\n", "    --num_of_gpus 1 \\\n", "    --per_device_train_batch_size 1 \\\n", "    --tensor_parallel_degree 1 \\\n", "    --pipeline_parallel_degree 1 \\\n", "    --sharding_parallel_degree 1 \\\n", "    --seed 23 \\\n", "    --num_samples_each_epoch 6000000\n", "```\n", "\n", "Here, `train_task_config` is a JSON file that records training data information, like this:\n", "```json\n", "[\n", "    {\n", "        \"prob\": 1,\n", "        \"filepath\": \"data/sft_trainset.jsonl\"\n", "    }\n", "]\n", "```\n", "\n", "### 4.3 Train with the WebUI\n", "1. Launch the WebUI from the ERNIEKit project's root directory: `erniekit webui`\n", "2. Configure the model path and export directory.\n", "\n", "![sft_set_path](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_set_path_en.png)\n", "\n", "3. Set up full-parameter/LoRA fine-tuning, numerical precision, etc.\n", "\n", "![sft_lora_set](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_set_lora_en.png)\n", "\n", "4. Set the fine-tuning mode (SFT/DPO) and configure training parameters.\n", "\n", "![sft_train_params](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_train_params_en.png)\n", "\n", "5. Configure the training and validation sets.\n", "\n", "<div style=\"display: flex; justify-content: space-around;\">\n", "    <img src=\"https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_trainset_en.png\" alt=\"sft_trainset\" style=\"width: 49%;\">\n", "    <img src=\"https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_validset_en.png\" alt=\"sft_validset\" style=\"width: 49%;\">\n", "</div>\n", "\n", "### 4.4 View Training Logs and Loss Curves\n", "The training log path is `${your_model_dir}/paddle_dist_log/workerlog.0`. You can also run the following command to view the loss curve:\n", "```bash\n", "visualdl --logdir ${your_model_dir}/vdl_log --host 0.0.0.0\n", "```\n", "Here is an example of a loss curve viewed in VisualDL:\n", "\n", "![sft_visualdl_loss](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_visualdl_loss.png)\n", "\n", "### 4.5 Merge Model Weights (Only for Lo<PERSON> Fine-Tuning)\n", "You can easily merge model weights in the WebUI's \"Evaluation\" mode.\n", "\n", "![sft_merge_lora](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_merge_lora_en.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 5. Performance Evaluation\n", "\n", "### 5.1 Calculate Evaluation Metrics\n", "We have already defined the evaluation criteria and demonstrated how to calculate the metrics. Here, we will not repeat that but will instead present the training configurations and evaluation metrics from several experiments for your reference. We also tested the performance of the ERNIE-4.5-21B-A3B model with the same configuration.\n", "\n", "- ERNIE-4.5-0.3B  \n", "\n", "| Experiment No. | Manually Configured Parameters | Corresponding Recommended Parameters | Metrics |\n", "| --- | --- | --- | --- |\n", "| 0 | Base model without SFT | - | ↑Accuracy: 2.532% <br> ↑Macro Avg - Precision: 2.75%, Recall: 2.5%, F1: 2.083% <br> ↑Micro Avg - Precision: 5.714%, Recall: 2.532%, F1: 3.509% <br> ↑Weighted Avg - Precision: 2.785%, Recall: 2.532%, F1: 2.11% |\n", "| 1 | num_train_epochs=1 <br> gradient_accumulation_steps=1 <br> max_seq_len=8192 <br> num_of_gpus=4 <br> tensor_parallel_degree=4 | max_steps=1070 <br> global_batch_size=1 <br> warmup_steps=107 | ↑Accuracy: 87.342% <br> ↑Macro Avg - Precision: 89.583%, Recall: 87.5%, F1: 87.0% <br> ↑Micro Avg - Precision: 90.789%, Recall: 87.342%, F1: 89.032% <br> ↑Weighted Avg - Precision: 89.451%, Recall: 87.342%, F1: 86.835% |\n", "\n", "- ERNIE-4.5-21B-A3B  \n", "\n", "| Experiment No. | Manually Configured Parameters | Corresponding Recommended Parameters | Metrics |\n", "| ---- | ---- | ---- | ---- |\n", "| 0 | Base model without SFT | - | ↑Accuracy: 48.101%<br>↑Macro Avg - Precision: 54.583%, Recall: 48.75%, F1: 49.333%<br>↑Micro Avg - Precision: 52.055%, Recall: 48.101%, F1: 50.0%<br>↑Weighted Avg - Precision: 54.008%, Recall: 48.101%, F1: 48.692% |\n", "| 1 | num_train_epochs=1<br>gradient_accumulation_steps=1<br>max_seq_len=8192<br>num_of_gpus=4<br>tensor_parallel_degree=4 | max_steps=1070<br>global_batch_size=1<br>warmup_steps=107 | ↑Accuracy: 89.873%<br>↑Macro Avg - Precision: 87.083%, Recall: 90.0%, F1: 87.333%<br>↑Micro Avg - Precision: 89.873%, Recall: 89.873%, F1: 89.873%<br>↑Weighted Avg - Precision: 86.92%, Recall: 89.873%, F1: 87.173% |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Analyze the Experimental Results\n", "As we can see, the current model has achieved impressive metrics. However, there's still room for exploration and improvement. We have prepared a few data analysis tools to help us find clues for further optimizing the model."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def get_histories_by_function_name(function_name, eval_data_path, prediction_data_path):\n", "    \"\"\"\n", "    Retrieves the corresponding dialogue histories for a given function name.\n", "\n", "    Args:\n", "        function_name: The name of the function to search for.\n", "        eval_data_path: The file path for the evaluation data (with ground truth).\n", "        prediction_data_path: The file path for the prediction data.\n", "\n", "    Returns:\n", "        A tuple (gt_histories, pred_histories), where each element is a list of\n", "        histories that match the ground truth and prediction labels, respectively.\n", "    \"\"\"\n", "    history2gt = {}\n", "    with open(eval_data_path, \"r\") as fin:\n", "        for line in fin:\n", "            data_eval = json.loads(line.strip())\n", "            history2gt[json.dumps(data_eval[\"history\"], ensure_ascii=False)] = data_eval[\"ground_truth\"]\n", "\n", "    history2pred = {}\n", "    with open(prediction_data_path, \"r\") as fin:\n", "        for line in fin:\n", "            data_pred = json.loads(line.strip())\n", "            history2pred[json.dumps(data_pred[1:-1], ensure_ascii=False)] = data_pred[-1][\"content\"]\n", "\n", "    # Find histories where the ground truth matches the function name\n", "    gt_histories = []\n", "    for history_str, gt in history2gt.items():\n", "        if gt == function_name:\n", "            history_data = json.loads(history_str)\n", "            history_data.append({\"ground_truth\": function_name})\n", "            gt_histories.append(history_data)\n", "\n", "    # Find histories where the prediction matches the function name\n", "    pred_histories = []\n", "    for history_str, pred in history2pred.items():\n", "        if pred == function_name:\n", "            history_data = json.loads(history_str)\n", "            history_data.append({\"prediction\": function_name})\n", "            pred_histories.append(history_data)\n", "\n", "    return gt_histories, pred_histories"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 Catastrophic Forgetting\n", "We have observed that the model's performance declines after SFT on the function-calling task, exhibiting a degree of \"catastrophic forgetting.\" For example:\n", "\n", "> Input: `[{\"role\": \"user\", \"content\": \"Please remember, your name is <PERSON><PERSON><PERSON>\"}, {\"role\": \"assistant\", \"content\": \"Okay, I am <PERSON><PERSON><PERSON>\"}, {\"role\": \"user\", \"content\": \"Who are you?\"}]`\n", "\n", "> ERNIE-4.5-0.3B Base Model Output: I am <PERSON><PERSON><PERSON>! How can I help you?\n", "> SFT Model Output: `get_definition`\n", "\n", "This is a common phenomenon in large language model SFT. The data for this task is relatively specialized, causing SFT to enhance the model's performance on this specific task while degrading its general capabilities. To maintain the model's general abilities, we can simply adopt a **data mixing strategy** to alleviate this issue. ERNIE provides several general-purpose SFT datasets that can be mixed with downstream task data (like the function-calling data here) for SFT."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 6 FAQ\n", "\n", "### 6.1 What should I do if I can't fine-tune the 32B model even with four 80GB A800 GPUs?\n", "\n", "You can configure the \"Distributed Parameters\" in the WebUI as follows:\n", "\n", "![sft_21b_distributed_params_en](https://raw.githubusercontent.com/wiki/Minghao2812/ERNIE/img/sft_21b_distributed_params_en.png)"]}], "metadata": {"kernelspec": {"display_name": "ar", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
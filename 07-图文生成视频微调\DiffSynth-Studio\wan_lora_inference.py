#!/usr/bin/env python3
"""
基于训练好的LoRA权重进行Wan2.1-I2V-14B-480P推理
使用正确的DiffSynth API
"""

import torch
from torch.nn.parallel import DataParallel
import os
import sys
from PIL import Image

# 添加DiffSynth路径
sys.path.append('/root/sj-tmp/DiffSynth-Studio')

from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from diffsynth.models.model_manager import ModelManager

def load_lora_weights(pipeline, lora_path):
    """加载LoRA权重到pipeline"""
    print(f"🔧 加载LoRA权重: {lora_path}")
    
    try:
        # 加载LoRA检查点
        if lora_path.endswith('.safetensors'):
            from safetensors import safe_open
            checkpoint = {}
            with safe_open(lora_path, framework="pt", device="cpu") as f:
                for key in f.keys():
                    checkpoint[key] = f.get_tensor(key)
            print(f"   ✅ SafeTensors检查点加载成功: {len(checkpoint)} 个参数")
        else:
            checkpoint = torch.load(lora_path, map_location="cpu", weights_only=False)
            print(f"   ✅ 检查点加载成功: {len(checkpoint)} 个参数")
        
        # 应用LoRA权重到DiT模型
        if hasattr(pipeline, 'dit') and pipeline.dit is not None:
            # 过滤并应用权重
            dit_state_dict = {}
            for key, value in checkpoint.items():
                # 移除可能的前缀
                clean_key = key.replace("pipe.dit.", "").replace("module.", "")
                dit_state_dict[clean_key] = value
            
            # 尝试加载权重
            missing_keys, unexpected_keys = pipeline.dit.load_state_dict(dit_state_dict, strict=False)
            print(f"   ✅ LoRA权重应用成功")
            print(f"   缺失键: {len(missing_keys)}, 多余键: {len(unexpected_keys)}")
            
            return True
        else:
            print("   ❌ DiT模型未找到")
            return False
            
    except Exception as e:
        print(f"   ❌ LoRA权重加载失败: {e}")
        return False

def setup_multi_gpu_pipeline(pipeline, gpu_ids=[0, 1]):
    """设置多GPU并行"""
    print(f"🚀 设置多GPU并行: {gpu_ids}")
    
    main_device = f"cuda:{gpu_ids[0]}"
    
    if len(gpu_ids) > 1:
        print(f"   多GPU模式: {gpu_ids}")
        
        # 将pipeline移动到主GPU
        pipeline = pipeline.to(main_device)
        
        # 设置DiT模型的DataParallel
        if hasattr(pipeline, 'dit') and pipeline.dit is not None:
            pipeline.dit = DataParallel(pipeline.dit, device_ids=gpu_ids)
            print("   ✅ DiT模型已设置DataParallel")
        
    else:
        print(f"   单GPU模式: {gpu_ids[0]}")
        pipeline = pipeline.to(main_device)
    
    return pipeline

def main():
    """主函数"""
    print("🎬 Wan2.1-I2V-14B-480P LoRA多卡推理")
    print("=" * 50)
    
    # 配置参数
    LORA_CHECKPOINT = "./models/train/Wan2.1-I2V-14B-480P_lora_final/epoch-4.safetensors"
    GPU_IDS = [0, 1]
    
    # 检查GPU
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return
    
    available_gpus = torch.cuda.device_count()
    print(f"✅ 检测到 {available_gpus} 张GPU")
    
    # 验证GPU ID
    GPU_IDS = [gid for gid in GPU_IDS if gid < available_gpus]
    if not GPU_IDS:
        print("❌ 没有有效的GPU")
        return
    
    # 检查LoRA文件
    if not os.path.exists(LORA_CHECKPOINT):
        print(f"❌ LoRA检查点不存在: {LORA_CHECKPOINT}")
        return
    
    try:
        print("📦 初始化WanVideoPipeline...")
        
        # 创建模型配置
        model_configs = [
            ModelConfig(
                model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
                origin_file_pattern="diffusion_pytorch_model*.safetensors", 
                offload_device="cpu"
            ),
            ModelConfig(
                model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", 
                offload_device="cpu"
            ),
            ModelConfig(
                model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                origin_file_pattern="Wan2.1_VAE.pth", 
                offload_device="cpu"
            ),
            ModelConfig(
                model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
                origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", 
                offload_device="cpu"
            )
        ]
        
        # 初始化pipeline
        pipeline = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",
            model_configs=model_configs
        )
        
        print("✅ Pipeline初始化成功")
        
        # 加载LoRA权重
        if not load_lora_weights(pipeline, LORA_CHECKPOINT):
            print("⚠️  LoRA权重加载失败，使用基础模型")
        
        # 设置多GPU
        pipeline = setup_multi_gpu_pipeline(pipeline, GPU_IDS)
        
        # 启用VRAM管理
        pipeline.enable_vram_management()
        
        print("🎬 开始视频生成测试...")
        
        # 测试用例
        test_cases = [
            {
                "prompt": "A beautiful sunset over the ocean with gentle waves",
                "output": "lora_sunset.mp4"
            },
            {
                "prompt": "A cute cat playing with a colorful ball in a sunny garden",
                "output": "lora_cat.mp4"
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n--- 测试案例 {i+1}: {test_case['prompt'][:50]}... ---")
            
            try:
                # 生成视频
                video = pipeline(
                    prompt=test_case["prompt"],
                    negative_prompt="色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量",
                    seed=42,
                    height=480,
                    width=832,
                    num_frames=81,
                    cfg_scale=7.5,
                    num_inference_steps=50,
                    tiled=True
                )
                
                # 保存视频
                save_video(video, test_case["output"], fps=15, quality=5)
                print(f"   ✅ 视频生成成功: {test_case['output']}")
                
            except Exception as e:
                print(f"   ❌ 视频生成失败: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n🎉 多卡LoRA推理完成!")
        print(f"   使用GPU: {GPU_IDS}")
        print(f"   LoRA权重: {LORA_CHECKPOINT}")
        
    except Exception as e:
        print(f"❌ Pipeline初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

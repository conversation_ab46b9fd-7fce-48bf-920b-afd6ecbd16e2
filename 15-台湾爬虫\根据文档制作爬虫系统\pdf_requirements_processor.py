#!/usr/bin/env python3
"""
PDF需求处理器 - 根据PDF文档中的具体要求进行字段筛选
"""

import json
from datetime import datetime

class PDFRequirementsProcessor:
    """PDF需求处理器"""
    
    def __init__(self):
        # 预定义常见的政府采购字段映射
        self.field_mappings = {
            # 中文字段名 -> 数据路径
            '标案名称': 'list_data.title',
            '案件名称': 'list_data.title',
            '标案案号': 'detail_data.announcement_info.case_number',
            '案件编号': 'detail_data.announcement_info.case_number',
            '机关名称': 'list_data.agency',
            '采购机关': 'list_data.agency',
            '机关代码': 'detail_data.agency_info.agency_code',
            '单位名称': 'detail_data.agency_info.unit_name',
            '机关地址': 'detail_data.agency_info.agency_address',
            '联络人': 'detail_data.agency_info.contact_person',
            '联络电话': 'detail_data.agency_info.contact_phone',
            '传真号码': 'detail_data.agency_info.fax_number',
            
            '招标日期': 'list_data.tender_date',
            '公告日期': 'detail_data.announcement_info.announcement_date',
            '开标日期': 'detail_data.time_info.opening_time',
            '决标日期': 'list_data.award_date',
            '原公告日期': 'detail_data.time_info.original_announcement_date',
            
            '招标方式': 'detail_data.announcement_info.tender_method',
            '决标方式': 'detail_data.announcement_info.award_method',
            '标案类型': 'list_data.tender_type',
            
            '预算金额': 'detail_data.amount_info.budget_amount',
            '预算金额中文': 'detail_data.amount_info.budget_amount_chinese',
            '采购金额级距': 'detail_data.amount_info.procurement_amount_range',
            '预算金额是否公开': 'detail_data.amount_info.budget_public',
            
            '履约地点': 'detail_data.performance_info.performance_location',
            '履约地区': 'detail_data.performance_info.performance_region',
            '是否受机关补助': 'detail_data.performance_info.government_subsidy',
            
            '投标厂商数': 'detail_data.bidder_count',
            '厂商名称': 'vendors.vendor_name',
            '厂商代码': 'vendors.vendor_code',
            '是否得标': 'vendors.is_winner',
            '决标金额': 'vendors.award_amount',
            '组织型态': 'vendors.organization_type',
            '厂商地址': 'vendors.vendor_address',
            '厂商电话': 'vendors.vendor_phone',
            '履约期间': 'vendors.performance_period',
            
            '标的分类': 'detail_data.subject_classification.classification_name',
            '详情页链接': 'list_data.detail_url'
        }
    
    def get_nested_value(self, data, path):
        """根据路径获取嵌套数据的值"""
        try:
            keys = path.split('.')
            value = data
            
            for key in keys:
                if key == 'vendors':
                    # 特殊处理厂商信息
                    vendors = value.get('detail_data', {}).get('vendors', [])
                    if vendors:
                        return vendors[0].get('vendor_name', '')  # 返回第一个厂商名称
                    return ''
                else:
                    value = value.get(key, {})
            
            return str(value) if value else ''
            
        except Exception:
            return ''
    
    def process_by_field_list(self, data, required_fields):
        """根据字段列表处理数据"""
        processed_data = []
        
        print(f"📋 处理字段列表: {', '.join(required_fields)}")
        
        for i, item in enumerate(data):
            processed_item = {'序号': i + 1}
            
            for field in required_fields:
                if field in self.field_mappings:
                    path = self.field_mappings[field]
                    value = self.get_nested_value(item, path)
                    processed_item[field] = value
                else:
                    processed_item[field] = ''  # 未知字段设为空
            
            processed_data.append(processed_item)
        
        return processed_data
    
    def process_by_categories(self, data, categories):
        """根据分类处理数据"""
        processed_data = []
        
        category_fields = {
            '基本信息': ['标案名称', '标案案号', '机关名称', '标案类型'],
            '时间信息': ['招标日期', '公告日期', '开标日期', '决标日期'],
            '金额信息': ['预算金额', '预算金额中文', '采购金额级距'],
            '机关信息': ['机关代码', '单位名称', '机关地址', '联络人', '联络电话'],
            '招标信息': ['招标方式', '决标方式'],
            '履约信息': ['履约地点', '是否受机关补助'],
            '厂商信息': ['投标厂商数', '厂商名称', '是否得标', '决标金额'],
            '其他信息': ['标的分类', '详情页链接']
        }
        
        # 根据选择的分类组合字段
        selected_fields = []
        for category in categories:
            if category in category_fields:
                selected_fields.extend(category_fields[category])
        
        # 去重并保持顺序
        unique_fields = []
        for field in selected_fields:
            if field not in unique_fields:
                unique_fields.append(field)
        
        return self.process_by_field_list(data, unique_fields)
    
    def generate_template_config(self):
        """生成模板配置文件"""
        template_config = {
            "说明": "请根据PDF文档要求修改以下配置",
            "筛选方式": "field_list",  # 可选: field_list, categories
            "required_fields": [
                "标案名称",
                "机关名称", 
                "标案案号",
                "决标日期",
                "预算金额",
                "招标方式",
                "厂商名称",
                "决标金额"
            ],
            "categories": [
                "基本信息",
                "时间信息", 
                "金额信息",
                "厂商信息"
            ],
            "输出格式": ["json", "csv", "excel"],
            "文件名前缀": "pdf_requirements_output"
        }
        
        with open('pdf_requirements_template.json', 'w', encoding='utf-8') as f:
            json.dump(template_config, f, ensure_ascii=False, indent=2)
        
        print("📝 已生成配置模板: pdf_requirements_template.json")
        print("请根据PDF文档要求修改此配置文件")
    
    def process_from_config(self, data, config_file):
        """根据配置文件处理数据"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"📖 读取配置文件: {config_file}")
            
            if config.get('筛选方式') == 'field_list':
                processed_data = self.process_by_field_list(data, config.get('required_fields', []))
            elif config.get('筛选方式') == 'categories':
                processed_data = self.process_by_categories(data, config.get('categories', []))
            else:
                print("❌ 配置文件中筛选方式不正确")
                return []
            
            # 保存结果
            prefix = config.get('文件名前缀', 'pdf_output')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # JSON格式
            if 'json' in config.get('输出格式', []):
                json_file = f"{prefix}_{timestamp}.json"
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(processed_data, f, ensure_ascii=False, indent=2)
                print(f"💾 JSON文件已保存: {json_file}")
            
            # CSV格式
            if 'csv' in config.get('输出格式', []):
                csv_file = f"{prefix}_{timestamp}.csv"
                self.save_as_csv(processed_data, csv_file)
                print(f"📊 CSV文件已保存: {csv_file}")
            
            return processed_data
            
        except Exception as e:
            print(f"❌ 处理配置文件时发生错误: {str(e)}")
            return []
    
    def save_as_csv(self, data, filename):
        """保存为CSV格式"""
        try:
            import csv
            
            if not data:
                return
            
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
                
        except Exception as e:
            print(f"⚠️ CSV保存失败: {str(e)}")

def main():
    """主函数"""
    print("📄 === PDF需求处理器 ===")
    print("请告诉我PDF文档中的具体字段要求")
    
    # 创建处理器
    processor = PDFRequirementsProcessor()
    
    # 生成配置模板
    processor.generate_template_config()
    
    # 读取数据
    input_file = 'defense_procurement_data_simplified.json'
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 成功读取 {len(data)} 笔数据")
        
        # 示例：根据常见需求处理
        print("\n🔍 示例处理 - 常见政府采购报表字段:")
        common_fields = [
            '标案名称', '机关名称', '标案案号', '决标日期', 
            '预算金额', '招标方式', '厂商名称', '决标金额'
        ]
        
        result = processor.process_by_field_list(data, common_fields)
        
        # 保存示例结果
        with open('pdf_example_output.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        processor.save_as_csv(result, 'pdf_example_output.csv')
        
        print("💾 示例结果已保存:")
        print("  - pdf_example_output.json")
        print("  - pdf_example_output.csv")
        
        print(f"\n📋 可用字段列表:")
        for field in processor.field_mappings.keys():
            print(f"  - {field}")
        
        print(f"\n📝 使用方法:")
        print(f"1. 修改 pdf_requirements_template.json 配置文件")
        print(f"2. 在配置文件中指定需要的字段")
        print(f"3. 运行处理器生成结果")
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()

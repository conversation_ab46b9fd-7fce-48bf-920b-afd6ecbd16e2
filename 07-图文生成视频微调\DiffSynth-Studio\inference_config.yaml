# Wan2.1-I2V-14B-480P 多卡推理配置文件

# 模型配置
model:
  # 训练好的LoRA检查点路径
  lora_checkpoint: "./models/train/Wan2.1-I2V-14B-480P_lora_final/epoch-4.safetensors"
  
  # 基础模型配置（与训练时保持一致）
  base_models:
    dit:
      model_id: "Wan-AI/Wan2.1-I2V-14B-480P"
      model_path: "diffusion_pytorch_model*.safetensors"
    text_encoder:
      model_id: "Wan-AI/Wan2.1-T2V-1.3B"
      model_path: "models_t5_umt5-xxl-enc-bf16.pth"
    vae:
      model_id: "Wan-AI/Wan2.1-T2V-1.3B"
      model_path: "Wan2.1_VAE.pth"
    image_encoder:
      model_id: "Wan-AI/Wan2.1-I2V-14B-480P"
      model_path: "models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth"

# GPU配置
gpu:
  # 使用的GPU设备ID
  device_ids: [0, 1]
  
  # 主设备（通常是第一个GPU）
  main_device: 0
  
  # 内存优化
  mixed_precision: "bf16"
  
  # 是否启用DataParallel
  use_data_parallel: true

# 生成配置
generation:
  # 默认生成参数
  default:
    height: 480
    width: 832
    num_frames: 81
    num_inference_steps: 50
    guidance_scale: 7.5
    
  # 快速生成（用于测试）
  fast:
    height: 480
    width: 832
    num_frames: 25
    num_inference_steps: 20
    guidance_scale: 7.5
    
  # 高质量生成
  high_quality:
    height: 480
    width: 832
    num_frames: 81
    num_inference_steps: 100
    guidance_scale: 9.0

# 输出配置
output:
  # 默认输出目录
  output_dir: "./inference_outputs"
  
  # 视频格式
  video_format: "mp4"
  
  # 帧率
  fps: 8
  
  # 是否保存中间帧
  save_frames: false

# 测试用例
test_cases:
  - name: "ocean_sunset"
    prompt: "A beautiful sunset over the ocean with gentle waves"
    input_image: null
    mode: "default"
    
  - name: "cat_playing"
    prompt: "A cute cat playing with a colorful ball in a sunny garden"
    input_image: null
    mode: "default"
    
  - name: "mountain_landscape"
    prompt: "Majestic snow-capped mountains with clouds moving across the sky"
    input_image: null
    mode: "high_quality"
    
  - name: "city_night"
    prompt: "A bustling city at night with neon lights and traffic"
    input_image: null
    mode: "fast"

# 性能监控
monitoring:
  # 是否启用GPU监控
  enable_gpu_monitoring: true
  
  # 监控间隔（秒）
  monitoring_interval: 1
  
  # 是否记录详细日志
  detailed_logging: true
  
  # 基准测试配置
  benchmark:
    num_runs: 3
    warmup_runs: 1

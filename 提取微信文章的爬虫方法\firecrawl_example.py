# firecrawl_example.py

# 导入 Firecrawl 库
from firecrawl import FirecrawlApp, ScrapeOptions
import os

# 设置你的 Firecrawl API 密钥
# 你可以在 Firecrawl 官网注册并获取 API 密钥
# 建议将 API 密钥设置为环境变量，以保证安全性
# 例如：os.environ.get("FIRECRAWL_API_KEY")
FIRECRAWL_API_KEY = "fc-YOUR_API_KEY" # 请替换为你的实际 API 密钥

# 初始化 Firecrawl 应用
app = FirecrawlApp(api_key=FIRECRAWL_API_KEY)

# --- 示例 1: 刮取单个网页内容 ---
print("\n--- 示例 1: 刮取单个网页内容 ---")

# 定义要刮取的网页 URL
url_to_scrape = "https://www.firecrawl.dev/"

# 调用 scrape_url 方法刮取网页内容
# formats 参数可以指定返回的格式，例如 'markdown', 'html', 'json'
# 这里我们选择返回 Markdown 格式，这种格式对 LLM 更友好
scrape_result = app.scrape_url(url_to_scrape, formats=['markdown'])

# 打印刮取结果
# 结果是一个字典，其中 'data' 键包含了刮取到的内容和元数据
if scrape_result and scrape_result.get('success'):
    print(f"成功刮取 URL: {url_to_scrape}")
    # 访问 Markdown 内容
    markdown_content = scrape_result['data']['markdown']
    print("\n--- 刮取到的 Markdown 内容 (部分) ---")
    print(markdown_content[:1000]) # 打印前1000个字符

    # 访问元数据
    metadata = scrape_result['data']['metadata']
    print("\n--- 网页元数据 ---")
    print(f"标题: {metadata.get('title')}")
    print(f"描述: {metadata.get('description')}")
    print(f"语言: {metadata.get('language')}")
    print(f"来源 URL: {metadata.get('sourceURL')}")
else:
    print(f"刮取 URL 失败: {url_to_scrape}")
    print(scrape_result)

# --- 示例 2: 爬行整个网站 ---
print("\n--- 示例 2: 爬行整个网站 ---")

# 定义要爬行的网站 URL
url_to_crawl = "https://docs.firecrawl.dev/"

# 调用 crawl_url 方法开始爬行任务
# limit 参数可以限制爬取的页面数量
# scrape_options 可以指定爬取内容的格式
print(f"开始爬行网站: {url_to_crawl} (限制10个页面)")
crawl_job = app.crawl_url(
    url_to_crawl,
    limit=10, # 限制爬取10个页面
    scrape_options=ScrapeOptions(formats=['markdown'])
)

# 爬行任务会返回一个任务 ID，我们可以用它来查询任务状态和结果
if crawl_job and crawl_job.get('success'):
    job_id = crawl_job['id']
    print(f"爬行任务已提交，任务 ID: {job_id}")

    # 循环检查爬行任务状态，直到完成
    print("正在等待爬行任务完成...")
    while True:
        crawl_status = app.check_crawl_status(job_id)
        status = crawl_status.get('status')
        completed_count = crawl_status.get('completed', 0)
        total_count = crawl_status.get('total', 0)

        print(f"当前状态: {status}, 已完成: {completed_count}/{total_count}")

        if status == 'completed':
            print("爬行任务完成！")
            # 打印爬取到的数据（如果数据量不大）
            # 注意：如果数据量很大，'data' 字段可能不会直接返回所有内容，需要通过 'next' URL 分页获取
            if 'data' in crawl_status:
                print(f"\n--- 爬取到的页面数量: {len(crawl_status['data'])} ---")
                for i, page_data in enumerate(crawl_status['data']):
                    print(f"\n--- 页面 {i+1} --- ")
                    print(f"URL: {page_data['metadata'].get('sourceURL')}")
                    print(f"标题: {page_data['metadata'].get('title')}")
                    print("Markdown 内容 (部分):")
                    print(page_data['markdown'][:500]) # 打印前500个字符
            break
        elif status == 'failed':
            print("爬行任务失败！")
            print(crawl_status)
            break
        # 等待一段时间后再次查询
        import time
        time.sleep(5)
else:
    print(f"提交爬行任务失败: {url_to_crawl}")
    print(crawl_job)

print("\nFirecrawl 示例执行完毕。")



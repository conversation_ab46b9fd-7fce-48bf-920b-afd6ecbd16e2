#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段筛选脚本 - 根据台湾政府采购网标准筛选和整理字段
"""

import pandas as pd
import json
from pathlib import Path

# 定义标准字段映射和筛选规则
STANDARD_FIELDS = {
    # 基本信息
    'case_number': '标案案号',
    'case_name': '标案名称', 
    'organization': '机关名称',
    'org_code': '机关代码',
    'unit_name': '单位名称',
    'org_address': '机关地址',
    'contact_person': '联络人',
    'contact_phone': '联络电话',
    
    # 标案信息
    'category': '标的分类',
    'tender_method': '招标方式',
    'award_method': '决标方式',
    'budget_amount': '预算金额',
    'award_amount': '决标金额',
    'total_award_amount': '总决标金额',
    'reserve_price': '底价金额',
    
    # 日期信息
    'tender_notice_date': '招标公告日期',
    'award_notice_date': '决标公告日期',
    'bid_deadline': '截止投标日期',
    'bid_opening_time': '开标时间',
    'public_reading_date': '公开阅览日期',
    'forecast_notice_date': '预告公告日期',
    
    # 履约信息
    'contract_location': '履约地点',
    'contract_period': '履约期间',
    'contract_number': '契约编号',
    
    # 厂商信息
    'winner_company': '得标厂商',
    'bidder_count': '投标厂商家数',
    
    # 其他信息
    'item_name': '品项名称',
    'award_item_count': '决标品项数',
    'detail_url': '详细页面链接',
    'pk_main': '主键',
    'tender_type': '公告类型'
}

# 优先保留的核心字段
CORE_FIELDS = [
    'case_number', 'case_name', 'organization', 'org_code', 
    'category', 'tender_method', 'award_method',
    'budget_amount', 'award_amount', 'total_award_amount',
    'tender_notice_date', 'award_notice_date', 'bid_deadline',
    'contract_location', 'winner_company', 'detail_url'
]

def filter_and_organize_csv(csv_file_path):
    """筛选和整理CSV数据"""
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file_path)
        
        print(f"原始数据: {len(df)} 行, {len(df.columns)} 列")
        print(f"原始字段: {list(df.columns)}")
        
        # 筛选存在的标准字段
        available_fields = [field for field in STANDARD_FIELDS.keys() if field in df.columns]
        
        # 创建筛选后的数据框
        filtered_df = df[available_fields].copy()
        
        # 重命名列为中文
        column_mapping = {field: STANDARD_FIELDS[field] for field in available_fields}
        filtered_df = filtered_df.rename(columns=column_mapping)
        
        # 按重要性排序列
        core_columns = [STANDARD_FIELDS[field] for field in CORE_FIELDS if field in available_fields]
        other_columns = [col for col in filtered_df.columns if col not in core_columns]
        ordered_columns = core_columns + sorted(other_columns)
        
        filtered_df = filtered_df[ordered_columns]
        
        # 数据清理
        for col in filtered_df.columns:
            # 移除空值和无效数据
            filtered_df[col] = filtered_df[col].fillna('')
            # 清理过长的无效数据
            filtered_df[col] = filtered_df[col].apply(
                lambda x: '' if isinstance(x, str) and len(x) > 500 else x
            )
        
        # 保存筛选后的数据
        output_path = csv_file_path.replace('.csv', '_filtered.csv')
        filtered_df.to_csv(output_path, index=False, encoding='utf-8-sig')  # 使用utf-8-sig以便Excel正确显示中文
        
        print(f"\n筛选后数据: {len(filtered_df)} 行, {len(filtered_df.columns)} 列")
        print(f"筛选后字段: {list(filtered_df.columns)}")
        print(f"已保存到: {output_path}")
        
        # 显示数据统计
        print(f"\n📊 数据统计:")
        for col in filtered_df.columns:
            non_empty = filtered_df[filtered_df[col] != ''].shape[0]
            print(f"  {col}: {non_empty}/{len(filtered_df)} 条有数据")
        
        return output_path
        
    except Exception as e:
        print(f"筛选CSV数据时出错: {e}")
        return None

def filter_and_organize_json(json_file_path):
    """筛选和整理JSON数据"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"原始JSON数据: {len(data)} 条记录")
        
        # 筛选和整理每条记录
        filtered_data = []
        for item in data:
            filtered_item = {}
            
            # 筛选标准字段
            for field, chinese_name in STANDARD_FIELDS.items():
                if field in item:
                    value = item[field]
                    # 清理数据
                    if isinstance(value, str) and len(value) > 500:
                        value = ''
                    filtered_item[chinese_name] = value if value else ''
                else:
                    filtered_item[chinese_name] = ''
            
            filtered_data.append(filtered_item)
        
        # 保存筛选后的数据
        output_path = json_file_path.replace('.json', '_filtered.json')
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, ensure_ascii=False, indent=2)
        
        print(f"筛选后JSON数据已保存到: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"筛选JSON数据时出错: {e}")
        return None

def create_field_mapping_doc():
    """创建字段映射说明文档"""
    doc_content = """# 台湾政府采购网数据字段说明

## 字段映射表

| 英文字段名 | 中文字段名 | 说明 |
|-----------|-----------|------|
"""
    
    for eng_field, chi_field in STANDARD_FIELDS.items():
        doc_content += f"| {eng_field} | {chi_field} | |\n"
    
    doc_content += f"""
## 核心字段 (优先保留)

{', '.join([STANDARD_FIELDS[field] for field in CORE_FIELDS])}

## 数据来源

- 网站: 台湾政府电子采购网 (https://web.pcc.gov.tw)
- 数据类型: 决标公告
- 爬取时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

## 使用说明

1. 筛选后的数据保存为 `*_filtered.csv` 和 `*_filtered.json` 格式
2. CSV文件使用UTF-8-BOM编码，可直接在Excel中打开
3. 空值已统一处理为空字符串
4. 过长的无效数据已被清理
"""
    
    with open('data/字段说明.md', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("📝 字段说明文档已创建: data/字段说明.md")

def main():
    """主函数"""
    print("🔍 开始筛选和整理数据字段...")
    
    # 创建字段说明文档
    create_field_mapping_doc()
    
    # 查找最新的清理后数据文件
    data_dir = Path('data')
    
    # 筛选CSV文件
    csv_files = list(data_dir.glob('csv/*_cleaned.csv'))
    if csv_files:
        latest_csv = max(csv_files, key=lambda x: x.stat().st_mtime)
        print(f"\n📄 筛选CSV文件: {latest_csv}")
        filter_and_organize_csv(str(latest_csv))
    
    # 筛选JSON文件
    json_files = list(data_dir.glob('json/*_cleaned.json'))
    if json_files:
        latest_json = max(json_files, key=lambda x: x.stat().st_mtime)
        print(f"\n📄 筛选JSON文件: {latest_json}")
        filter_and_organize_json(str(latest_json))
    
    print("\n✅ 字段筛选和整理完成!")
    print("\n📋 建议查看以下文件:")
    print("  - data/字段说明.md (字段映射说明)")
    print("  - data/csv/*_filtered.csv (筛选后的CSV数据)")
    print("  - data/json/*_filtered.json (筛选后的JSON数据)")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
简化的视频LoRA推理测试
"""

import torch
from PIL import Image
import os
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def main():
    print("🎬 简化视频LoRA推理测试")
    print("=" * 50)
    
    # 检查epoch-2检查点
    lora_checkpoint = "./models/train/Wan2.1-I2V-14B-480P_video_lora/epoch-2.safetensors"
    
    if os.path.exists(lora_checkpoint):
        file_size = os.path.getsize(lora_checkpoint) / 1024**2
        print(f"✅ 找到检查点: {file_size:.1f}MB")
    else:
        print("❌ 未找到检查点")
        return
    
    print("📦 初始化Pipeline...")
    
    try:
        # 创建pipeline
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",
            model_configs=[
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
            ],
        )
        pipe.enable_vram_management()
        print("✅ Pipeline初始化成功")
        
        # 加载LoRA权重
        print("🔧 加载LoRA权重...")
        pipe.load_lora(module=pipe.dit, path=lora_checkpoint)
        print("✅ LoRA权重加载成功")
        
        # 创建测试图像
        image = Image.new('RGB', (832, 480), color=(135, 206, 235))
        print("✅ 创建测试图像")
        
        print("🎬 开始推理...")
        
        # 简单推理测试
        video = pipe(
            prompt="A beautiful sunset over the ocean with gentle waves, cinematic lighting",
            negative_prompt="low quality, blurry",
            input_image=image,
            seed=42,
            tiled=True,
            height=480,
            width=832,
            num_frames=25,  # 减少帧数加快测试
            cfg_scale=7.5,
            num_inference_steps=20  # 减少步数加快测试
        )
        
        print("💾 保存视频...")
        output_path = "video_lora_epoch2_simple_test.mp4"
        save_video(video, output_path, fps=10, quality=5)
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024**2
            print(f"✅ 生成成功: {output_path} ({file_size:.1f}MB)")
        else:
            print("❌ 生成失败")
        
        print("🎉 推理测试完成!")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

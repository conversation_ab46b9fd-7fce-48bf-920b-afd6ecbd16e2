# crawl4ai_example.py

# 导入必要的库
import asyncio
import json
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, LLMConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy, LLMExtractionStrategy

# --- 示例 1: 基本网页刮取并生成 Markdown ---
print("\n--- 示例 1: 基本网页刮取并生成 Markdown ---")

async def basic_scrape():
    # 初始化 AsyncWebCrawler
    # 可以通过 BrowserConfig 配置浏览器行为，例如是否无头模式 (headless)
    browser_conf = BrowserConfig(headless=True) # headless=True 表示无头模式，不显示浏览器界面
    
    # 通过 CrawlerRunConfig 配置爬虫运行行为，例如缓存模式
    # CacheMode.BYPASS 表示不使用缓存，每次都重新抓取最新内容
    run_conf = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS
    )

    async with AsyncWebCrawler(config=browser_conf) as crawler:
        # 定义要刮取的 URL
        url_to_scrape = "https://example.com"
        print(f"正在刮取 URL: {url_to_scrape}")
        
        # 执行刮取任务
        result = await crawler.arun(
            url=url_to_scrape,
            config=run_conf
        )
        
        # 打印刮取到的 Markdown 内容的前 500 个字符
        print("\n--- 刮取到的 Markdown 内容 (部分) ---")
        print(result.markdown[:500])

# 运行基本刮取示例
asyncio.run(basic_scrape())

# --- 示例 2: 使用 CSS 选择器提取结构化数据 ---
print("\n--- 示例 2: 使用 CSS 选择器提取结构化数据 ---")

async def css_extraction():
    # 定义一个 HTML 片段作为示例数据
    # 在实际应用中，这里会是刮取到的网页 HTML 内容
    raw_html = """
    <div class='product-list'>
        <div class='product-item'>
            <h2>商品 A</h2>
            <span class='price'>$100</span>
            <a href='/product/A'>查看详情</a>
        </div>
        <div class='product-item'>
            <h2>商品 B</h2>
            <span class='price'>$200</span>
            <a href='/product/B'>查看详情</a>
        </div>
    </div>
    """

    # 定义提取数据的 Schema
    # baseSelector: 定义要查找的父元素
    # fields: 定义要从父元素中提取的字段，包括字段名、选择器和类型
    schema = {
        "name": "商品列表",
        "baseSelector": "div.product-item", # 匹配每个商品项
        "fields": [
            {"name": "title", "selector": "h2", "type": "text"}, # 提取 h2 标签的文本作为标题
            {"name": "price", "selector": "span.price", "type": "text"}, # 提取 class 为 price 的 span 标签文本作为价格
            {"name": "link", "selector": "a", "type": "attribute", "attribute": "href"} # 提取 a 标签的 href 属性作为链接
        ]
    }

    # 创建 JsonCssExtractionStrategy 实例
    extraction_strategy = JsonCssExtractionStrategy(schema)

    # 配置爬虫运行，使用 BYPASS 缓存模式和定义的提取策略
    run_conf = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        extraction_strategy=extraction_strategy
    )

    async with AsyncWebCrawler() as crawler:
        # 使用 'raw://' 前缀来处理原始 HTML 字符串，而不是 URL
        print("正在使用 CSS 选择器从原始 HTML 中提取数据...")
        result = await crawler.arun(
            url="raw://" + raw_html,
            config=run_conf
        )
        
        # 提取到的 JSON 数据存储在 extracted_content 中
        if result.extracted_content:
            extracted_data = json.loads(result.extracted_content)
            print("\n--- 提取到的结构化数据 ---")
            print(json.dumps(extracted_data, indent=2, ensure_ascii=False))
        else:
            print("未提取到数据。")

# 运行 CSS 提取示例
asyncio.run(css_extraction())

# --- 示例 3: 使用 LLM 提取数据 (需要配置 LLM) ---
print("\n--- 示例 3: 使用 LLM 提取数据 (需要配置 LLM) ---")

async def llm_extraction():
    # 定义要刮取的 URL
    url_to_scrape = "https://perinim.github.io/projects"
    print(f"正在使用 LLM 从 URL: {url_to_scrape} 中提取数据...")

    # 配置 LLM
    # 这里以 Ollama 为例，你需要确保 Ollama 服务正在运行并已拉取 'mistral' 模型
    # 如果使用 OpenAI 或其他模型，请相应修改 model 和 api_key
    llm_config = LLMConfig(
        provider="ollama/mistral", # 使用 Ollama 的 mistral 模型
        temperature=0,
        format="json", # 指定 LLM 输出格式为 JSON
        base_url="http://localhost:11434" # Ollama 服务的地址
    )

    # 定义 LLM 提取策略
    # prompt: 告诉 LLM 你想提取什么信息
    extraction_strategy = LLMExtractionStrategy(
        llm_config=llm_config,
        prompt="List me all the projects with their description and the author."
    )

    # 配置爬虫运行，使用 BYPASS 缓存模式和 LLM 提取策略
    run_conf = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        extraction_strategy=extraction_strategy
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url=url_to_scrape,
            config=run_conf
        )
        
        if result.extracted_content:
            extracted_data = json.loads(result.extracted_content)
            print("\n--- 提取到的结构化数据 (LLM) ---")
            print(json.dumps(extracted_data, indent=2, ensure_ascii=False))
        else:
            print("未提取到数据。请检查 LLM 配置和网络连接。")

# 运行 LLM 提取示例
# 注意：运行此示例前，请确保你的 Ollama 服务已启动并下载了 'mistral' 模型
# 如果没有 Ollama，可以注释掉此行，或修改为其他可用的 LLM 配置
# asyncio.run(llm_extraction())

print("\nCrawl4AI 示例执行完毕。")



#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版完美爬虫 - 专门处理台湾政府采购网搜索表单
作者: MiniMax Agent
时间: 2025-07-15

功能：
1. 自动提交搜索表单获取公告数据
2. 智能解析搜索结果页面
3. 完整的数据提取和存储
4. 支持多种搜索条件组合
"""

import requests
import time
import random
import csv
import json
import logging
import re
import os
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin, parse_qs, urlparse
from typing import List, Dict, Any, Optional
import sqlite3
from pathlib import Path
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/workspace/logs/improved_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ImprovedCrawler:
    """改进版完美爬虫类 - 处理搜索表单的专业爬虫"""
    
    def __init__(self):
        """初始化爬虫"""
        self.base_url = "https://web.pcc.gov.tw"
        self.search_url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion"
        self.result_url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/resultBulletion"
        
        # 创建必要的目录
        self.create_directories()
        
        # 初始化session
        self.session = requests.Session()
        self.setup_session()
        
        # 数据库初始化
        self.init_database()
        
        # 爬虫配置
        self.config = {
            'max_workers': 3,  # 降低并发数，避免被封
            'delay_range': (2, 5),  # 增加延迟时间
            'retry_times': 3,
            'timeout': 30,
            'max_pages': 20,  # 限制页数
            'items_per_page': 10,  # 每页显示条数
        }
        
        # 搜索配置
        self.search_config = {
            'tenderStatusType': ['招標', '決標'],  # 搜索类型
            'dateType': '2',  # 日期类型：1-公告日期，2-截止日期
            'dateRange': 7,   # 最近7天
        }
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_items': 0,
            'start_time': datetime.now()
        }
    
    def create_directories(self):
        """创建必要的目录结构"""
        directories = [
            '/workspace/logs',
            '/workspace/data',
            '/workspace/data/csv',
            '/workspace/data/json',
            '/workspace/data/database',
            '/workspace/debug/forms'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def setup_session(self):
        """设置会话参数"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        ]
        
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'Referer': self.search_url
        })
    
    def init_database(self):
        """初始化SQLite数据库"""
        self.db_path = '/workspace/data/database/procurement_data_improved.db'
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS announcements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT,
                    announcement_id TEXT UNIQUE,
                    organization TEXT,
                    procurement_type TEXT,
                    announcement_date TEXT,
                    deadline_date TEXT,
                    budget_amount TEXT,
                    contact_person TEXT,
                    contact_phone TEXT,
                    url TEXT,
                    content TEXT,
                    tender_status TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS search_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    search_params TEXT,
                    results_count INTEGER,
                    pages_crawled INTEGER,
                    session_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_end TIMESTAMP,
                    status TEXT
                )
            ''')
            
            conn.commit()
            logger.info("改进版数据库初始化完成")
    
    def get_search_form_data(self) -> Dict[str, str]:
        """获取搜索表单数据"""
        try:
            response = self.session.get(self.search_url, timeout=self.config['timeout'])
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 保存表单HTML用于调试
            with open('/workspace/debug/forms/search_form.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            
            # 构建搜索表单数据
            form_data = {
                'fullText': '',  # 全文搜索内容
                'keyWords': '',  # 关键词
                'tenderStatusType': self.search_config['tenderStatusType'],  # 招标状态
                'tenderKindType': '',  # 采购性质
                'orgName': '',   # 机关名称
                'orgClassCode': '',  # 机关分类
                'overBudget': '',    # 预算金额
                'underBudget': '',   # 预算金额下限
                'dateType': self.search_config['dateType'],  # 日期类型
                'startDate': '',     # 开始日期
                'endDate': '',       # 结束日期
                'isSpDeadLine': '',  # 特殊截止时间
                'area': '',          # 地区
                'tenderWay': '',     # 招标方式
                'page': '1',         # 页码
                'pageCount': str(self.config['items_per_page']),  # 每页显示数量
            }
            
            # 设置日期范围（最近N天）
            if self.search_config.get('dateRange'):
                end_date = datetime.now()
                start_date = end_date - timedelta(days=self.search_config['dateRange'])
                form_data['startDate'] = start_date.strftime('%Y/%m/%d')
                form_data['endDate'] = end_date.strftime('%Y/%m/%d')
            
            logger.info(f"搜索表单数据准备完成: {form_data}")
            return form_data
            
        except Exception as e:
            logger.error(f"获取搜索表单失败: {e}")
            return {}
    
    def submit_search_form(self, form_data: Dict[str, str], page: int = 1) -> Optional[requests.Response]:
        """提交搜索表单"""
        try:
            # 更新页码
            form_data['page'] = str(page)
            
            self.stats['total_requests'] += 1
            
            # 添加延迟
            time.sleep(random.uniform(*self.config['delay_range']))
            
            # 提交POST请求
            response = self.session.post(
                self.result_url,
                data=form_data,
                timeout=self.config['timeout'],
                allow_redirects=True
            )
            
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            self.stats['successful_requests'] += 1
            logger.info(f"成功提交搜索表单 (第{page}页): {response.status_code}")
            
            # 保存响应用于调试
            if page == 1:
                with open(f'/workspace/debug/forms/search_result_page_{page}.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
            
            return response
            
        except requests.exceptions.RequestException as e:
            self.stats['failed_requests'] += 1
            logger.error(f"搜索表单提交失败 (第{page}页): {e}")
            return None
    
    def parse_search_results(self, html_content: str) -> List[Dict[str, Any]]:
        """解析搜索结果页面"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            announcements = []
            
            # 查找结果表格
            # 根据实际HTML结构调整选择器
            result_tables = soup.find_all('table')
            
            for table in result_tables:
                rows = table.find_all('tr')
                
                # 跳过表头，处理数据行
                for i, row in enumerate(rows[1:], 1):
                    try:
                        cells = row.find_all(['td', 'th'])
                        
                        if len(cells) >= 4:  # 确保有足够的列
                            announcement = self.extract_announcement_from_row(cells, row)
                            
                            if announcement and announcement.get('title'):
                                announcements.append(announcement)
                                logger.debug(f"提取公告: {announcement['title'][:50]}")
                    
                    except Exception as e:
                        logger.warning(f"解析第{i}行时出错: {e}")
                        continue
            
            # 如果主表格没有数据，尝试其他结构
            if not announcements:
                announcements = self.parse_alternative_structure(soup)
            
            logger.info(f"从搜索结果解析到 {len(announcements)} 条公告")
            return announcements
            
        except Exception as e:
            logger.error(f"解析搜索结果失败: {e}")
            return []
    
    def extract_announcement_from_row(self, cells: List, row) -> Dict[str, Any]:
        """从表格行提取公告信息"""
        try:
            announcement = {}
            
            # 根据实际表格结构调整索引
            if len(cells) >= 5:
                # 假设列顺序：序号、标案名称、机关名称、公告日期、截止日期等
                announcement['announcement_id'] = self.clean_text(cells[0].get_text())
                announcement['title'] = self.clean_text(cells[1].get_text())
                announcement['organization'] = self.clean_text(cells[2].get_text()) if len(cells) > 2 else ''
                announcement['announcement_date'] = self.clean_text(cells[3].get_text()) if len(cells) > 3 else ''
                announcement['deadline_date'] = self.clean_text(cells[4].get_text()) if len(cells) > 4 else ''
                
                # 提取详情链接
                link_cell = cells[1]  # 通常标案名称列包含链接
                link = link_cell.find('a')
                if link and link.get('href'):
                    href = link.get('href')
                    announcement['url'] = urljoin(self.base_url, href) if href.startswith('/') else href
                
                # 提取其他可能的信息
                if len(cells) > 5:
                    announcement['budget_amount'] = self.clean_text(cells[5].get_text())
                if len(cells) > 6:
                    announcement['tender_status'] = self.clean_text(cells[6].get_text())
            
            return announcement
            
        except Exception as e:
            logger.warning(f"提取公告信息失败: {e}")
            return {}
    
    def parse_alternative_structure(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """解析备选结构（如果主表格解析失败）"""
        announcements = []
        
        try:
            # 查找包含公告信息的div或其他元素
            content_divs = soup.find_all('div', {'class': lambda x: x and any(
                keyword in ' '.join(x) for keyword in ['result', 'item', 'row', 'data']
            )})
            
            for div in content_divs:
                announcement = {}
                text_content = div.get_text().strip()
                
                if any(keyword in text_content for keyword in ['案號', '標案', '機關', '公告']):
                    # 尝试用正则表达式提取信息
                    announcement['title'] = text_content[:100]  # 临时使用前100字符作为标题
                    announcement['content'] = text_content
                    
                    # 查找链接
                    link = div.find('a')
                    if link:
                        announcement['url'] = urljoin(self.base_url, link.get('href', ''))
                    
                    announcements.append(announcement)
            
            logger.info(f"备选结构解析到 {len(announcements)} 条数据")
            
        except Exception as e:
            logger.error(f"备选结构解析失败: {e}")
        
        return announcements
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ''
        
        # 去除多余空白字符
        text = re.sub(r'\\s+', ' ', text.strip())
        text = re.sub(r'[\\r\\n\\t]', '', text)
        
        return text
    
    def get_total_pages(self, html_content: str) -> int:
        """获取总页数"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找分页信息
            page_info = soup.find_all(text=re.compile(r'第\\s*\\d+\\s*頁'))
            if page_info:
                # 提取总页数
                for info in page_info:
                    match = re.search(r'共\\s*(\\d+)\\s*頁', info)
                    if match:
                        return int(match.group(1))
            
            # 查找分页链接
            page_links = soup.find_all('a', href=re.compile(r'page=\\d+'))
            if page_links:
                max_page = 1
                for link in page_links:
                    href = link.get('href', '')
                    match = re.search(r'page=(\\d+)', href)
                    if match:
                        max_page = max(max_page, int(match.group(1)))
                return max_page
            
            return 1
            
        except Exception as e:
            logger.warning(f"获取总页数失败: {e}")
            return 1
    
    def crawl_all_pages(self) -> List[Dict[str, Any]]:
        """爬取所有页面"""
        logger.info("开始执行搜索和爬取")
        
        # 获取搜索表单数据
        form_data = self.get_search_form_data()
        if not form_data:
            logger.error("无法获取搜索表单数据")
            return []
        
        all_data = []
        
        # 记录搜索会话
        session_id = None
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO search_sessions (search_params, status)
                VALUES (?, ?)
            ''', (json.dumps(form_data), 'started'))
            session_id = cursor.lastrowid
            conn.commit()
        
        try:
            # 第一次搜索获取总页数
            first_response = self.submit_search_form(form_data, 1)
            if not first_response:
                logger.error("第一次搜索失败")
                return []
            
            # 解析第一页数据
            first_page_data = self.parse_search_results(first_response.text)
            all_data.extend(first_page_data)
            
            # 获取总页数
            total_pages = min(self.get_total_pages(first_response.text), self.config['max_pages'])
            logger.info(f"检测到总页数: {total_pages}")
            
            # 爬取剩余页面
            for page_num in range(2, total_pages + 1):
                try:
                    response = self.submit_search_form(form_data, page_num)
                    if response:
                        page_data = self.parse_search_results(response.text)
                        all_data.extend(page_data)
                        self.stats['total_items'] += len(page_data)
                        
                        logger.info(f"第{page_num}页爬取完成，本页{len(page_data)}条，累计{len(all_data)}条")
                    else:
                        logger.warning(f"第{page_num}页爬取失败")
                
                except KeyboardInterrupt:
                    logger.info("用户中断爬取")
                    break
                except Exception as e:
                    logger.error(f"爬取第{page_num}页时出错: {e}")
                    continue
            
            # 更新搜索会话记录
            if session_id:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        UPDATE search_sessions 
                        SET results_count = ?, pages_crawled = ?, session_end = CURRENT_TIMESTAMP, status = ?
                        WHERE id = ?
                    ''', (len(all_data), min(total_pages, len(all_data)//self.config['items_per_page'] + 1), 'completed', session_id))
                    conn.commit()
            
        except Exception as e:
            logger.error(f"爬取过程出错: {e}")
            if session_id:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        UPDATE search_sessions 
                        SET status = ?, session_end = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', ('error', session_id))
                    conn.commit()
        
        return all_data
    
    def save_data(self, data: List[Dict], format_type: str = 'all'):
        """保存数据到不同格式"""
        if not data:
            logger.warning("没有数据需要保存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存CSV
        if format_type in ['all', 'csv']:
            csv_path = f'/workspace/data/csv/improved_procurement_data_{timestamp}.csv'
            with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                if data:
                    fieldnames = set()
                    for item in data:
                        fieldnames.update(item.keys())
                    
                    writer = csv.DictWriter(csvfile, fieldnames=list(fieldnames))
                    writer.writeheader()
                    writer.writerows(data)
            
            logger.info(f"数据已保存到CSV: {csv_path}")
        
        # 保存JSON
        if format_type in ['all', 'json']:
            json_path = f'/workspace/data/json/improved_procurement_data_{timestamp}.json'
            with open(json_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已保存到JSON: {json_path}")
        
        # 保存数据库
        if format_type in ['all', 'database']:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for item in data:
                    try:
                        cursor.execute('''
                            INSERT OR REPLACE INTO announcements 
                            (title, announcement_id, organization, procurement_type, 
                             announcement_date, deadline_date, budget_amount, contact_person, 
                             contact_phone, url, content, tender_status, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                        ''', (
                            item.get('title', ''),
                            item.get('announcement_id', ''),
                            item.get('organization', ''),
                            item.get('procurement_type', ''),
                            item.get('announcement_date', ''),
                            item.get('deadline_date', ''),
                            item.get('budget_amount', ''),
                            item.get('contact_person', ''),
                            item.get('contact_phone', ''),
                            item.get('url', ''),
                            item.get('content', ''),
                            item.get('tender_status', '')
                        ))
                    except sqlite3.Error as e:
                        logger.warning(f"保存数据库记录失败: {e}")
                
                conn.commit()
            
            logger.info(f"已保存 {len(data)} 条记录到数据库")
    
    def generate_report(self, data_count: int) -> str:
        """生成爬取报告"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        report = f"""
=== 改进版爬虫执行报告 ===
执行时间: {self.stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}
执行时长: {duration}

搜索配置:
- 搜索类型: {', '.join(self.search_config['tenderStatusType'])}
- 日期范围: 最近 {self.search_config.get('dateRange', 'N/A')} 天
- 每页显示: {self.config['items_per_page']} 条

请求统计:
- 总请求数: {self.stats['total_requests']}
- 成功请求: {self.stats['successful_requests']}
- 失败请求: {self.stats['failed_requests']}
- 成功率: {(self.stats['successful_requests']/max(self.stats['total_requests'], 1)*100):.2f}%

数据统计:
- 总爬取条目: {data_count}
- 平均速度: {(data_count/max(duration.total_seconds(), 1)*60):.2f} 条/分钟

文件输出:
- 数据库: {self.db_path}
- 日志文件: /workspace/logs/improved_crawler.log
- 调试文件: /workspace/debug/forms/
===========================
        """
        
        return report
    
    def run(self) -> Dict[str, Any]:
        """运行改进版爬虫"""
        logger.info("=== 改进版完美爬虫启动 ===")
        logger.info(f"搜索页面: {self.search_url}")
        logger.info(f"搜索配置: {self.search_config}")
        
        try:
            # 爬取数据
            all_data = self.crawl_all_pages()
            
            if all_data:
                # 保存数据
                self.save_data(all_data)
                logger.info(f"爬取完成！共获得 {len(all_data)} 条数据")
            else:
                logger.warning("未获得任何数据，请检查搜索条件或网站结构")
            
            # 生成报告
            report = self.generate_report(len(all_data))
            logger.info(report)
            
            # 保存报告
            report_path = f'/workspace/logs/improved_crawl_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            return {
                'status': 'success' if all_data else 'no_data',
                'total_items': len(all_data),
                'report': report,
                'data_sample': all_data[:5] if all_data else []  # 返回前5条作为样本
            }
            
        except Exception as e:
            logger.error(f"改进版爬虫执行出错: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'total_items': 0
            }


def main():
    """主函数"""
    try:
        # 创建改进版爬虫实例
        crawler = ImprovedCrawler()
        
        # 执行爬取
        result = crawler.run()
        
        print("\\n=== 改进版爬取结果 ===")
        print(f"状态: {result['status']}")
        print(f"总条目数: {result['total_items']}")
        
        if result['status'] == 'success' and result.get('data_sample'):
            print("\\n=== 样本数据 ===")
            for i, item in enumerate(result['data_sample'], 1):
                print(f"\\n第 {i} 条:")
                for key, value in item.items():
                    if value:  # 只显示有值的字段
                        display_value = str(value)[:100] + ('...' if len(str(value)) > 100 else '')
                        print(f"  {key}: {display_value}")
        
        elif result['status'] == 'no_data':
            print("\\n未获得数据，可能原因：")
            print("1. 搜索条件过于严格，没有匹配的结果")
            print("2. 网站结构发生变化，需要调整解析逻辑")
            print("3. 网站暂时不可访问或返回空结果")
            print("\\n请查看调试文件：/workspace/debug/forms/")
        
        return result
        
    except Exception as e:
        logger.error(f"主程序执行出错: {e}")
        return {'status': 'error', 'error': str(e)}


if __name__ == "__main__":
    result = main()

#!/usr/bin/env python3
"""
测试训练好的LoRA模型推理
"""

import os
import sys
import torch
from pathlib import Path

def test_lora_inference():
    """测试LoRA模型推理"""
    print("🧪 测试训练好的LoRA模型推理...")
    
    try:
        from diffsynth import save_video
        from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
        
        # 创建pipeline
        print("📦 加载基础模型...")
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda" if torch.cuda.is_available() else "cpu",
            model_configs=[
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="diffusion_pytorch_model*.safetensors",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="Wan2.1_VAE.pth",
                    offload_device="cpu"
                ),
            ],
        )
        
        # 检查LoRA文件
        lora_path = "./models/train/memory_optimized_test/epoch-0.safetensors"
        if not os.path.exists(lora_path):
            print(f"❌ LoRA文件不存在: {lora_path}")
            return False
        
        print("🔧 加载LoRA权重...")
        # 尝试不同的LoRA加载方法
        try:
            # 方法1: 直接加载到dit模型
            from peft import PeftModel
            from safetensors.torch import load_file
            
            # 加载LoRA权重
            lora_weights = load_file(lora_path)
            print(f"✅ LoRA权重加载成功，包含 {len(lora_weights)} 个参数")
            
            # 显示LoRA权重信息
            for key in list(lora_weights.keys())[:5]:  # 只显示前5个
                print(f"   {key}: {lora_weights[key].shape}")
            
        except Exception as e:
            print(f"⚠️  LoRA权重加载失败: {e}")
            print("继续使用基础模型进行测试...")
        
        # 启用显存管理
        pipe.enable_vram_management()
        
        print("🎬 生成测试视频...")
        
        # 生成视频 - 使用较小的分辨率和帧数
        video = pipe(
            prompt="一只可爱的小猫在阳光下的花园里玩耍，画面清晰，色彩鲜艳",
            negative_prompt="模糊，低质量，变形",
            seed=42,
            height=320,
            width=576,
            num_frames=25,  # 较短的视频
            num_inference_steps=10,  # 较少的步数用于快速测试
            tiled=True,
        )
        
        # 保存视频
        output_path = "test_lora_output.mp4"
        save_video(video, output_path, fps=8, quality=5)
        
        print(f"✅ LoRA模型测试成功！视频已保存到: {output_path}")
        
        # 显示文件信息
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024 / 1024  # MB
            print(f"   文件大小: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ LoRA模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🎬 DiffSynth-Studio LoRA模型推理测试")
    print("=" * 60)
    
    # 检查环境
    print("🔍 检查环境...")
    if not torch.cuda.is_available():
        print("⚠️  CUDA不可用，将使用CPU (速度会很慢)")
    else:
        print(f"✅ CUDA可用，GPU: {torch.cuda.get_device_name()}")
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"   显存: {memory_gb:.1f}GB")
    
    # 测试LoRA推理
    success = test_lora_inference()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 LoRA模型测试完成！")
        print("💡 您已经成功完成了Wan视频模型的LoRA微调和推理测试")
    else:
        print("⚠️  测试失败，请检查错误信息")
    print("=" * 60)

if __name__ == "__main__":
    main()

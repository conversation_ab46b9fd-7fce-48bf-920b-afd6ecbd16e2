# 配置文件

# 模型配置
MODEL_NAME = "baidu/ERNIE-4.5-21B-A3B-PT"

# 默认生成参数
DEFAULT_MAX_TOKENS = 512
DEFAULT_TEMPERATURE = 0.7
DEFAULT_TOP_P = 0.9

# 服务器配置
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 7860

# 界面配置
TITLE = "文心一言 ERNIE-4.5 聊天机器人"
DESCRIPTION = "基于百度ERNIE-4.5-21B模型的智能对话系统"

# 示例问题
EXAMPLE_QUESTIONS = [
    "请介绍一下大语言模型的发展历史",
    "如何学习Python编程？",
    "请写一首关于春天的诗",
    "解释一下什么是人工智能",
    "推荐几本值得阅读的书籍"
]

# 系统提示词（可选）
SYSTEM_PROMPT = ""

# 内存优化设置
USE_HALF_PRECISION = True  # 使用半精度浮点数
LOW_CPU_MEM_USAGE = True   # 低CPU内存使用模式
CLEAR_CACHE_AFTER_GENERATION = True  # 生成后清理缓存

#!/usr/bin/env python3
"""
调试爬虫脚本
"""

from enhanced_procurement_crawler import EnhancedProcurementCrawler
import json

def test_search_only():
    """只测试搜索功能"""
    crawler = EnhancedProcurementCrawler()

    print("开始测试搜索功能...")

    # 先获取搜索页面
    response = crawler._make_request(crawler.search_url)

    # 保存HTML到文件查看
    with open('search_page.html', 'w', encoding='utf-8') as f:
        f.write(response.text)
    print("已保存搜索页面HTML到 search_page.html")

    # 构建搜索参数
    search_data = {
        'searchMethod': 'true',
        'searchTarget': 'ATM',
        'textfield': '餐飲',
        'searchType': 'basic',
        'method': 'search',
        'isSpdt': 'N',
        'pageIndex': '1',
        'recordCountPerPage': '5',
        'tenderStatus': '2'  # 決標
    }

    # 发送搜索请求
    search_response = crawler._make_request(crawler.search_url, method='POST', data=search_data)

    # 保存搜索结果HTML
    with open('search_results.html', 'w', encoding='utf-8') as f:
        f.write(search_response.text)
    print("已保存搜索结果HTML到 search_results.html")

    results = crawler.search_procurement_list(
        keyword="餐飲",
        tender_types=['決標'],
        max_pages=1,
        page_size=5
    )

    print(f"搜索到 {len(results)} 条结果")

    for i, result in enumerate(results):
        print(f"\n结果 {i+1}:")
        print(f"  标题: {result['title']}")
        print(f"  机关: {result['agency']}")
        print(f"  类型: {result['tender_type']}")
        print(f"  日期: {result['announcement_date']}")
        print(f"  详情URL: {result['detail_url']}")

    return results

def test_detail_extraction(results):
    """测试详情提取"""
    if not results:
        print("没有搜索结果可供测试")
        return
    
    crawler = EnhancedProcurementCrawler()
    
    # 测试第一个结果的详情提取
    first_result = results[0]
    print(f"\n开始测试详情提取: {first_result['title']}")
    print(f"URL: {first_result['detail_url']}")
    
    detail = crawler.extract_detail_fields(first_result['detail_url'])
    
    if detail:
        print("详情提取成功!")
        print(f"机关名称: {detail.agency_info.agency_name}")
        print(f"案号: {detail.announcement_info.case_number}")
        print(f"投标厂商数: {detail.bidder_count}")
        print(f"厂商信息: {len(detail.vendors)} 个厂商")
    else:
        print("详情提取失败!")

if __name__ == "__main__":
    # 先测试搜索
    search_results = test_search_only()
    
    # 再测试详情提取
    test_detail_extraction(search_results)

import json
import hashlib
import datetime
import os
import uuid
from threading import Lock
from card_system import use_card

# JSON文件路径
USERS_FILE = 'data/users.json'
CREDIT_RECORDS_FILE = 'data/credit_records.json'
RECHARGE_RECORDS_FILE = 'data/recharge_records.json'
SESSIONS_FILE = 'data/sessions.json'
CHAT_HISTORY_FILE = 'data/chat_history.json'

# 文件锁，确保并发安全
file_lock = Lock()

def ensure_data_dir():
    """确保数据目录存在"""
    os.makedirs('data', exist_ok=True)

def load_json_file(filepath, default=None):
    """加载JSON文件"""
    if default is None:
        default = {}

    if not os.path.exists(filepath):
        return default

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return default

def save_json_file(filepath, data):
    """保存JSON文件"""
    ensure_data_dir()
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2, default=str)

def init_database():
    """初始化JSON数据文件"""
    ensure_data_dir()

    # 初始化用户文件
    if not os.path.exists(USERS_FILE):
        save_json_file(USERS_FILE, {})

    # 初始化积分记录文件
    if not os.path.exists(CREDIT_RECORDS_FILE):
        save_json_file(CREDIT_RECORDS_FILE, [])

    # 初始化充值记录文件
    if not os.path.exists(RECHARGE_RECORDS_FILE):
        save_json_file(RECHARGE_RECORDS_FILE, [])

    # 初始化会话文件
    if not os.path.exists(SESSIONS_FILE):
        save_json_file(SESSIONS_FILE, {})

    # 初始化聊天记录文件
    if not os.path.exists(CHAT_HISTORY_FILE):
        save_json_file(CHAT_HISTORY_FILE, {})

    print("JSON数据文件初始化完成")

def get_users():
    """获取所有用户数据"""
    with file_lock:
        return load_json_file(USERS_FILE, {})

def save_users(users_data):
    """保存用户数据"""
    with file_lock:
        save_json_file(USERS_FILE, users_data)

def get_credit_records():
    """获取积分记录"""
    with file_lock:
        return load_json_file(CREDIT_RECORDS_FILE, [])

def save_credit_records(records):
    """保存积分记录"""
    with file_lock:
        save_json_file(CREDIT_RECORDS_FILE, records)

def get_sessions():
    """获取会话数据"""
    with file_lock:
        return load_json_file(SESSIONS_FILE, {})

def save_sessions(sessions_data):
    """保存会话数据"""
    with file_lock:
        save_json_file(SESSIONS_FILE, sessions_data)

def hash_password(password):
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, password_hash):
    """验证密码"""
    return hash_password(password) == password_hash

def create_user(username, email, password):
    """创建新用户"""
    users = get_users()

    # 检查用户名和邮箱是否已存在
    for user_id, user_data in users.items():
        if user_data['username'] == username:
            raise ValueError("用户名已存在")
        if user_data['email'] == email:
            raise ValueError("邮箱已存在")

    # 创建新用户
    user_id = str(uuid.uuid4())
    password_hash = hash_password(password)
    now = datetime.datetime.now()

    user_data = {
        'id': user_id,
        'username': username,
        'email': email,
        'password_hash': password_hash,
        'is_vip': False,
        'vip_expire_date': None,
        'credits': 1000,
        'last_credit_refresh': now.date().isoformat(),
        'created_at': now.isoformat(),
        'updated_at': now.isoformat()
    }

    users[user_id] = user_data
    save_users(users)
    return user_id

def authenticate_user(username, password):
    """用户认证"""
    users = get_users()

    for user_id, user_data in users.items():
        if user_data['username'] == username:
            if verify_password(password, user_data['password_hash']):
                return user_data
            break
    return None

def get_user_by_id(user_id):
    """根据ID获取用户信息"""
    # 检查是否为管理员用户
    if user_id.startswith('admin_'):
        username = user_id.replace('admin_', '')
        # 管理员账号配置
        ADMIN_ACCOUNTS = {
            'admin': 'admin123',
            'root': 'root123'
        }
        if username in ADMIN_ACCOUNTS:
            return {
                'id': user_id,
                'username': username,
                'email': f'{username}@admin.local',
                'is_vip': True,
                'credits': 999999,
                'vip_expire_date': '2099-12-31T23:59:59',
                'created_at': datetime.datetime.now().isoformat()
            }

    # 普通用户
    users = get_users()
    return users.get(user_id)

def refresh_monthly_credits(user_id):
    """刷新每月积分"""
    users = get_users()
    user = users.get(user_id)

    if not user:
        return False

    today = datetime.date.today()
    current_month = today.strftime('%Y-%m')

    # 检查上次刷新的月份
    last_refresh_date = datetime.datetime.strptime(user['last_credit_refresh'], '%Y-%m-%d').date()
    last_refresh_month = last_refresh_date.strftime('%Y-%m')

    if last_refresh_month < current_month:
        # 根据用户类型确定积分数量
        if user['is_vip'] and user['vip_expire_date']:
            vip_expire = datetime.datetime.fromisoformat(user['vip_expire_date'])
            if vip_expire >= datetime.datetime.now():
                # VIP用户每月10000积分
                credit_amount = 10000
                description = 'VIP每月积分刷新'
            else:
                # VIP已过期，按普通用户处理
                credit_amount = 1000
                description = '每月积分刷新'
        else:
            # 普通用户每月1000积分
            credit_amount = 1000
            description = '每月积分刷新'

        # 更新积分和刷新日期
        user['credits'] = credit_amount  # 直接设置为新的积分数量，而不是累加
        user['last_credit_refresh'] = today.isoformat()
        user['updated_at'] = datetime.datetime.now().isoformat()

        users[user_id] = user
        save_users(users)

        # 记录积分变化
        add_credit_record(user_id, credit_amount, 'refresh', description)
        return True

    return False

# 保持向后兼容性的别名
def refresh_daily_credits(user_id):
    """刷新每日积分（已废弃，使用每月刷新）"""
    return refresh_monthly_credits(user_id)

def add_credit_record(user_id, change_amount, change_type, description, tokens_used=0):
    """添加积分记录"""
    records = get_credit_records()

    record = {
        'id': str(uuid.uuid4()),
        'user_id': user_id,
        'change_amount': change_amount,
        'change_type': change_type,  # 'consume', 'refresh', 'recharge', 'vip_use'
        'description': description,
        'tokens_used': tokens_used,
        'created_at': datetime.datetime.now().isoformat()
    }

    records.append(record)
    save_credit_records(records)

def consume_credits(user_id, amount, tokens_used=0, description=""):
    """消耗积分"""
    print(f"[DEBUG] 开始扣除积分 - 用户ID: {user_id}, 扣除数量: {amount}, tokens: {tokens_used}, 描述: {description}")

    # 检查是否为管理员用户
    if user_id.startswith('admin_'):
        username = user_id.replace('admin_', '')
        # 管理员账号配置
        ADMIN_ACCOUNTS = {
            'admin': 'admin123',
            'root': 'root123'
        }
        if username in ADMIN_ACCOUNTS:
            print(f"[DEBUG] 管理员用户 {username} 使用积分，无需扣除")
            # 记录管理员使用记录（但不实际扣除积分）
            add_credit_record(user_id, -amount, 'admin_use', description, tokens_used)
            return True

    users = get_users()
    user = users.get(user_id)

    if not user:
        print(f"[DEBUG] 用户不存在: {user_id}")
        return False

    print(f"[DEBUG] 用户信息 - 用户名: {user['username']}, 当前积分: {user['credits']}, VIP状态: {user['is_vip']}")

    # 所有用户都需要扣除积分，包括VIP用户
    if user['credits'] >= amount:
        old_credits = user['credits']
        user['credits'] -= amount
        user['updated_at'] = datetime.datetime.now().isoformat()
        users[user_id] = user
        save_users(users)

        print(f"[DEBUG] 积分扣除成功 - 原积分: {old_credits}, 新积分: {user['credits']}")

        # 记录积分消耗
        user_type = 'vip_use' if user['is_vip'] else 'consume'
        add_credit_record(user_id, -amount, user_type, description, tokens_used)
        return True

    print(f"[DEBUG] 积分不足 - 当前积分: {user['credits']}, 需要积分: {amount}")
    return False

def get_user_credit_records(user_id, limit=50):
    """获取用户积分记录"""
    records = get_credit_records()
    user_records = [record for record in records if record['user_id'] == user_id]

    # 按时间倒序排列，最新的在前面
    user_records.sort(key=lambda x: x['created_at'], reverse=True)

    # 限制返回数量
    return user_records[:limit]

def recharge_vip_with_card(user_id, card_key):
    """使用卡密充值VIP"""
    try:
        # 验证卡密（使用新的cards.json系统）
        from card_system import validate_card, use_card

        # 获取用户信息
        user_info = get_user_by_id(user_id)
        if not user_info:
            return False, "用户不存在"

        # 验证卡密
        is_valid, result = validate_card(card_key)
        if not is_valid:
            return False, result

        card_info = result
        vip_days = card_info['days']

        # 使用卡密
        success, card_result = use_card(card_key, user_id, user_info['username'])
        if not success:
            return False, card_result

        # 检查是否为管理员用户
        if user_id.startswith('admin_'):
            # 管理员用户，直接返回成功（管理员已经有无限积分和VIP）
            return True, {
                'message': f"管理员账号充值成功！延长{vip_days}天（管理员账号已享有永久VIP权限）",
                'expire_date': '2099-12-31T23:59:59',
                'days': vip_days,
                'credits_added': 0  # 管理员不需要增加积分
            }

        # 读取用户数据
        with open(USERS_FILE, 'r', encoding='utf-8') as f:
            users = json.load(f)

        if user_id not in users:
            return False, "用户不存在"

        user = users[user_id]
        now = datetime.datetime.now()

        # 计算VIP到期时间
        if user['is_vip'] and user['vip_expire_date']:
            # 如果已经是VIP，在现有基础上延长
            current_expire = datetime.datetime.fromisoformat(user['vip_expire_date'])
            if current_expire > now:
                new_expire = current_expire + datetime.timedelta(days=vip_days)
            else:
                new_expire = now + datetime.timedelta(days=vip_days)
        else:
            # 新开通VIP
            new_expire = now + datetime.timedelta(days=vip_days)

        # 更新用户VIP状态和积分
        user['is_vip'] = True
        user['vip_expire_date'] = new_expire.isoformat()
        user['credits'] = user.get('credits', 0) + 10000  # 增加10000积分
        user['updated_at'] = now.isoformat()

        # 保存用户数据
        with open(USERS_FILE, 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=2)

        # 记录积分变化
        add_credit_record(user_id, 10000, f"VIP充值成功，延长{vip_days}天，获得10000积分", "vip_recharge")

        return True, {
            'message': f"VIP充值成功！延长{vip_days}天，获得10000积分",
            'expire_date': new_expire.isoformat(),
            'days': vip_days,
            'credits_added': 10000
        }

    except Exception as e:
        return False, f"充值失败: {str(e)}"

# VIP卡密管理函数
VIP_CARDS_FILE = 'data/vip_cards.json'

def get_vip_cards():
    """获取VIP卡密数据"""
    with file_lock:
        return load_json_file(VIP_CARDS_FILE, {})

def save_vip_cards(cards_data):
    """保存VIP卡密数据"""
    with file_lock:
        save_json_file(VIP_CARDS_FILE, cards_data)

def get_all_cards():
    """获取所有卡密（管理员用）"""
    try:
        cards = get_vip_cards()
        card_list = []

        for card_key, card_data in cards.items():
            card_list.append({
                'card_key': card_key,
                'days': card_data['days'],
                'status': card_data['status'],
                'description': card_data.get('description', ''),
                'created_at': card_data['created_at'],
                'used_by': card_data.get('used_by'),
                'used_at': card_data.get('used_at')
            })

        # 按创建时间倒序排列
        card_list.sort(key=lambda x: x['created_at'], reverse=True)
        return card_list

    except Exception as e:
        print(f"获取卡密列表失败: {e}")
        return []

def get_card_stats():
    """获取卡密统计信息"""
    try:
        cards = get_vip_cards()
        total_cards = len(cards)
        unused_cards = sum(1 for card in cards.values() if card['status'] == 'unused')
        used_cards = total_cards - unused_cards

        # 计算近7天使用量
        seven_days_ago = datetime.datetime.now() - datetime.timedelta(days=7)
        recent_usage = 0

        for card in cards.values():
            if card['status'] == 'used' and card.get('used_at'):
                try:
                    used_time = datetime.datetime.fromisoformat(card['used_at'])
                    if used_time >= seven_days_ago:
                        recent_usage += 1
                except:
                    pass

        return {
            'total_cards': total_cards,
            'unused_cards': unused_cards,
            'used_cards': used_cards,
            'recent_usage': recent_usage
        }

    except Exception as e:
        print(f"获取卡密统计失败: {e}")
        return {
            'total_cards': 0,
            'unused_cards': 0,
            'used_cards': 0,
            'recent_usage': 0
        }

def create_vip_cards(count, days, description="VIP充值卡"):
    """批量创建VIP卡密"""
    import secrets
    try:
        cards = get_vip_cards()
        new_cards = []

        for _ in range(count):
            card_key = f"sk-{secrets.token_hex(16)}"
            card_data = {
                'days': days,
                'status': 'unused',
                'description': description,
                'created_at': datetime.datetime.now().isoformat(),
                'used_by': None,
                'used_at': None
            }

            cards[card_key] = card_data
            new_cards.append({
                'card_key': card_key,
                **card_data
            })

        save_vip_cards(cards)
        return new_cards

    except Exception as e:
        print(f"创建VIP卡密失败: {e}")
        return []

def delete_vip_card(card_key):
    """删除VIP卡密（仅限未使用的）"""
    try:
        cards = get_vip_cards()

        if card_key not in cards:
            return False, "卡密不存在"

        card = cards[card_key]
        if card['status'] == 'used':
            return False, "已使用的卡密不能删除"

        del cards[card_key]
        save_vip_cards(cards)
        return True, "卡密删除成功"

    except Exception as e:
        print(f"删除VIP卡密失败: {e}")
        return False, f"删除失败: {str(e)}"

def batch_delete_unused_cards():
    """批量删除所有未使用的卡密"""
    try:
        cards = get_vip_cards()
        unused_cards = {k: v for k, v in cards.items() if v['status'] == 'unused'}

        if not unused_cards:
            return True, "没有未使用的卡密需要删除"

        # 删除未使用的卡密
        for card_key in unused_cards.keys():
            del cards[card_key]

        save_vip_cards(cards)
        return True, f"成功删除 {len(unused_cards)} 张未使用的卡密"

    except Exception as e:
        print(f"批量删除卡密失败: {e}")
        return False, f"批量删除失败: {str(e)}"

def get_card_usage_records():
    """获取卡密使用记录"""
    cards = get_all_cards()
    used_cards = [card for card in cards if card['status'] == 'used']

    # 按使用时间排序
    used_cards.sort(key=lambda x: x.get('used_at', ''), reverse=True)

    return used_cards

def archive_used_cards(card_keys):
    """批量归档已使用的卡密"""
    try:
        # 读取现有卡密
        cards = get_vip_cards()

        # 准备归档数据
        archived_cards = {}
        cards_to_remove = []

        for card_key in card_keys:
            if card_key in cards:
                card = cards[card_key]
                # 只归档已使用的卡密
                if card['status'] == 'used':
                    archived_cards[card_key] = card
                    cards_to_remove.append(card_key)

        if not archived_cards:
            return False, "没有找到可归档的已使用卡密"

        # 创建归档文件名（按日期）
        archive_date = datetime.datetime.now().strftime('%Y%m%d')
        archive_file = f'data/archived_cards_{archive_date}.json'

        # 读取现有归档数据（如果存在）
        existing_archive = {}
        if os.path.exists(archive_file):
            try:
                existing_archive = load_json_file(archive_file)
            except:
                existing_archive = {}

        # 合并归档数据
        existing_archive.update(archived_cards)

        # 保存归档文件
        save_json_file(archive_file, existing_archive)

        # 从主卡密文件中移除已归档的卡密
        for card_key in cards_to_remove:
            del cards[card_key]

        # 保存更新后的卡密文件
        save_vip_cards(cards)

        return True, f"成功归档 {len(archived_cards)} 张已使用的卡密到 {archive_file}"

    except Exception as e:
        return False, f"归档失败: {str(e)}"

def get_card_stats():
    """获取卡密统计信息"""
    try:
        cards = get_all_cards()
        total_cards = len(cards)
        unused_cards = sum(1 for card in cards if card['status'] == 'unused')
        used_cards = sum(1 for card in cards if card['status'] == 'used')

        # 计算最近使用情况（最近7天）
        from datetime import datetime, timedelta
        recent_date = datetime.now() - timedelta(days=7)
        recent_usage = 0

        for card in cards:
            if card['status'] == 'used' and card.get('used_at'):
                try:
                    used_time = datetime.fromisoformat(card['used_at'])
                    if used_time >= recent_date:
                        recent_usage += 1
                except:
                    pass

        return {
            'total_cards': total_cards,
            'unused_cards': unused_cards,
            'used_cards': used_cards,
            'recent_usage': recent_usage
        }
    except Exception as e:
        return {
            'total_cards': 0,
            'unused_cards': 0,
            'used_cards': 0,
            'recent_usage': 0
        }

# ==================== 用户管理函数 ====================

def get_all_users():
    """获取所有用户"""
    users_data = get_users()
    users_list = []

    for user_id, user_info in users_data.items():
        users_list.append(user_info)

    # 按创建时间排序
    users_list.sort(key=lambda x: x.get('created_at', ''), reverse=True)

    return users_list

def create_user_admin(username, email, password, credits=1000, is_vip=False, vip_expire_date=None):
    """管理员创建用户"""
    try:
        users = get_users()

        # 检查用户名和邮箱是否已存在
        for user_id, user_info in users.items():
            if user_info['username'] == username:
                return False, "用户名已存在"
            if user_info['email'] == email:
                return False, "邮箱已存在"

        # 创建新用户
        user_id = str(uuid.uuid4())
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        user_data = {
            'id': user_id,
            'username': username,
            'email': email,
            'password_hash': password_hash,
            'is_vip': is_vip,
            'vip_expire_date': vip_expire_date,
            'credits': credits,
            'last_credit_refresh': datetime.date.today().isoformat(),
            'created_at': datetime.datetime.now().isoformat(),
            'updated_at': datetime.datetime.now().isoformat()
        }

        users[user_id] = user_data
        save_users(users)

        # 添加积分记录
        add_credit_record(user_id, credits, "admin_create", f"管理员创建用户，初始积分{credits}")

        return True, user_data

    except Exception as e:
        print(f"创建用户失败: {e}")
        return False, f"创建用户失败: {str(e)}"

def update_user_admin(user_id, data):
    """管理员更新用户"""
    try:
        users = get_users()

        if user_id not in users:
            return False, "用户不存在"

        user = users[user_id]
        old_credits = user['credits']

        # 更新用户信息
        if 'username' in data:
            # 检查用户名是否已被其他用户使用
            for uid, uinfo in users.items():
                if uid != user_id and uinfo['username'] == data['username']:
                    return False, "用户名已存在"
            user['username'] = data['username']

        if 'email' in data:
            # 检查邮箱是否已被其他用户使用
            for uid, uinfo in users.items():
                if uid != user_id and uinfo['email'] == data['email']:
                    return False, "邮箱已存在"
            user['email'] = data['email']

        if 'credits' in data:
            user['credits'] = int(data['credits'])

        if 'is_vip' in data:
            user['is_vip'] = bool(data['is_vip'])

        if 'vip_expire_date' in data:
            user['vip_expire_date'] = data['vip_expire_date']

        if 'password' in data and data['password']:
            user['password_hash'] = hashlib.sha256(data['password'].encode()).hexdigest()

        user['updated_at'] = datetime.datetime.now().isoformat()

        users[user_id] = user
        save_users(users)

        # 如果积分有变化，添加记录
        if 'credits' in data and old_credits != user['credits']:
            credit_change = user['credits'] - old_credits
            add_credit_record(user_id, credit_change, "admin_adjust", f"管理员调整积分: {credit_change:+d}")

        return True, user

    except Exception as e:
        print(f"更新用户失败: {e}")
        return False, f"更新用户失败: {str(e)}"

def delete_user_admin(user_id):
    """管理员删除用户"""
    try:
        users = get_users()

        if user_id not in users:
            return False, "用户不存在"

        username = users[user_id]['username']
        del users[user_id]
        save_users(users)

        # 删除用户的积分记录
        records = get_credit_records()
        records = [r for r in records if r['user_id'] != user_id]
        save_credit_records(records)

        return True, f"用户 {username} 已删除"

    except Exception as e:
        print(f"删除用户失败: {e}")
        return False, f"删除用户失败: {str(e)}"

def get_system_stats():
    """获取系统统计信息"""
    try:
        users = get_users()
        records = get_credit_records()

        # 用户统计
        total_users = len(users)
        vip_users = sum(1 for u in users.values() if u.get('is_vip', False))

        # 活跃用户（最近7天有积分消费记录）
        seven_days_ago = (datetime.datetime.now() - datetime.timedelta(days=7)).isoformat()
        active_users = set()
        total_credits_consumed = 0

        for record in records:
            if record['created_at'] >= seven_days_ago:
                if record['change_type'] == 'consume':
                    active_users.add(record['user_id'])
                    total_credits_consumed += abs(record['change_amount'])

        # 卡密统计
        cards = get_all_cards()
        total_cards = len(cards)
        used_cards = sum(1 for c in cards if c['status'] == 'used')

        return {
            'total_users': total_users,
            'vip_users': vip_users,
            'active_users': len(active_users),
            'total_credits_consumed': total_credits_consumed,
            'total_cards': total_cards,
            'used_cards': used_cards,
            'unused_cards': total_cards - used_cards
        }

    except Exception as e:
        print(f"获取系统统计失败: {e}")
        return {
            'total_users': 0,
            'vip_users': 0,
            'active_users': 0,
            'total_credits_consumed': 0,
            'total_cards': 0,
            'used_cards': 0,
            'unused_cards': 0
        }

# ==================== Session管理函数 ====================

# 全局session存储（生产环境应该使用Redis等）
_sessions = {}

def create_session(user_id):
    """创建用户session"""
    import secrets
    session_token = secrets.token_urlsafe(32)

    global _sessions
    _sessions[session_token] = {
        'user_id': user_id,
        'created_at': datetime.datetime.now().isoformat(),
        'expires_at': (datetime.datetime.now() + datetime.timedelta(days=7)).isoformat()
    }

    return session_token

def get_user_by_session_token(session_token):
    """根据session token获取用户信息"""
    global _sessions

    session_info = _sessions.get(session_token)
    if not session_info:
        return None

    # 检查session是否过期
    expires_at = datetime.datetime.fromisoformat(session_info['expires_at'])
    if datetime.datetime.now() > expires_at:
        # 删除过期session
        del _sessions[session_token]
        return None

    # 获取用户信息
    return get_user_by_id(session_info['user_id'])

def delete_session(session_token):
    """删除session"""
    global _sessions
    if session_token in _sessions:
        del _sessions[session_token]
        return True
    return False

def cleanup_expired_sessions():
    """清理过期的session"""
    global _sessions
    now = datetime.datetime.now()
    expired_tokens = []

    for token, session_info in _sessions.items():
        expires_at = datetime.datetime.fromisoformat(session_info['expires_at'])
        if now > expires_at:
            expired_tokens.append(token)

    for token in expired_tokens:
        del _sessions[token]

# ==================== 聊天记录管理 ====================

def get_chat_history():
    """获取所有聊天记录"""
    with file_lock:
        return load_json_file(CHAT_HISTORY_FILE, {})

def save_chat_history(chat_data):
    """保存聊天记录"""
    with file_lock:
        save_json_file(CHAT_HISTORY_FILE, chat_data)

def get_user_chat_history(user_id):
    """获取指定用户的聊天记录"""
    chat_data = get_chat_history()
    return chat_data.get(str(user_id), [])

def save_user_chat_history(user_id, chat_list):
    """保存指定用户的聊天记录"""
    chat_data = get_chat_history()
    chat_data[str(user_id)] = chat_list
    save_chat_history(chat_data)

def add_chat_record(user_id, chat_record):
    """添加聊天记录"""
    user_chats = get_user_chat_history(user_id)

    # 添加到列表开头
    user_chats.insert(0, chat_record)

    # 限制聊天记录数量（最多保存50条）
    if len(user_chats) > 50:
        user_chats = user_chats[:50]

    save_user_chat_history(user_id, user_chats)
    return chat_record

def update_chat_record(user_id, chat_id, updates):
    """更新聊天记录"""
    user_chats = get_user_chat_history(user_id)

    for chat in user_chats:
        if chat.get('id') == chat_id:
            chat.update(updates)
            break

    save_user_chat_history(user_id, user_chats)

def delete_chat_record(user_id, chat_id):
    """删除聊天记录"""
    user_chats = get_user_chat_history(user_id)
    user_chats = [chat for chat in user_chats if chat.get('id') != chat_id]
    save_user_chat_history(user_id, user_chats)

def add_message_to_chat(user_id, chat_id, message):
    """向聊天记录添加消息"""
    user_chats = get_user_chat_history(user_id)

    for chat in user_chats:
        if chat.get('id') == chat_id:
            if 'messages' not in chat:
                chat['messages'] = []
            chat['messages'].append(message)
            chat['updated_at'] = datetime.datetime.now().isoformat()
            break

    save_user_chat_history(user_id, user_chats)

    return len(expired_tokens)

if __name__ == '__main__':
    init_database()

﻿# 算力竞逐：从车端到云端 算力竞逐：从车端到云端

**发布日期**: 2024年05月20日

**原文链接**: https://www.jiemian.com/article/11192455.html

## 📄 原文内容

算力在最近几年成为热点，汽车行业也概莫能外。但凡有新车型发布，几乎都会提到其搭载芯片的算力。国内外整车企业争相对算力进行大举投入，甚至自研相关芯片。近日，马斯克就在社交媒体上表示，2024年特斯拉将投资100亿美元用于人工智能的训练和推理。

算力究竟是什么，又为何受到如此关注？

算力，即计算的能力。作为智能的要素和底座，算力将成为推动汽车智能化、汽车行业数字化的重要力量。在今天大模型发展趋势下，这种重要性表现得更加突出。

过去几年，车端算力芯片受到行业高度关注。随着汽车智能化的竞争重点从算法转向数据和算力，云端算力将成为车企未来几年竞争的关键。

今天，汽车与算力两个产业正进行着交流和碰撞。未来的汽车将越来越依赖计算能力。而我国新能源智能汽车产业的蓬勃活力，也将有力带动算力产业发展。

算力，即计算的能力，一般用于表示某个设备或系统的计算性能。从智能驾驶计算芯片，到用于算法训练的超大规模集群，这些设备或系统都在提供着不同种类、不同规模的算力。算力无处不在，已成为推动经济和社会发展的关键生产力。

算力原本只是超算领域关注的对象，代表着人们对计算极致能力的不懈追求。深度学习技术的兴起带来了人工智能的热潮。人们把算力、算法和数据归结为智能的三大要素。由此，算力一词开始备受关注。

算力有多种表征指标，包括每秒百万次指令数（MIPS）、每秒浮点操作数（FLOPS）、每秒定点操作数（OPS）等。比如，一颗人工智能计算芯片的算力是256 TOPS，一台超级计算机的算力是1 EFLOPS。

算力的基础是各类高性能计算芯片。最重要和为人熟知的计算芯片是CPU（中央处理器）和GPU（图形处理器）。CPU是计算设备的运算和控制核心，适合处理逻辑复杂的串行任务。GPU早期主要用来加速图像计算任务，由于其更加侧重计算而非逻辑控制，并能很好地支持并行计算，成为目前提供算力的主要芯片。

图： CPU与GPU芯片设计对比

当前，电动化、智能化已成为汽车产业的发展趋势。人们对汽车的关注点从油耗转向动力电池的续航里程，从发动机的加速性能转向芯片的算力。不论是电子电气架构、还是智能驾驶等解决方案，很大程度上都需要考虑算力的分布和有效利用。算力已成为智能汽车的核心要素。

提到车端算力，最具代表性的可能是汽车智能驾驶芯片。当前主流的车端智能驾驶芯片算力已达数十到数百TOPS。车端算力芯片的主要供应商包括英伟达、Mobileye、高通等。近年来，国内也涌现出地平线、黑芝麻等一批企业。

车端算力芯片存在多种技术架构。高端芯片一般基于通用GPU架构，提供较高的通用算力，以适应不同模型与解决方案。中低端芯片多以ASIC（专用集成电路）为主，与算法深度绑定，更加追求功耗、性能和成本的平衡。

相比车端计算芯片，云端芯片需要承担复杂的训练任务和海量的数据处理，且对功耗和成本的要求相对车端宽松，其算力更为强劲，计算架构以通用GPU为主。在数据中心算力芯片领域，英伟达公司占据着绝对的龙头地位。

第一种是汽车企业自建算力中心。例如谷歌、特斯拉等头部企业通过自建专有计算集群的方式，提升自身业务运行效能。部分企业还会根据自身业务特点，研发专用芯片，以降低算力成本。

第二种是智能计算云服务。例如亚马逊、阿里云等云计算企业以云服务模式向汽车企业提供算力资源及技术服务。

第三种是公共智能计算中心。公共智能计算中心以地方政府主导建设为主，主要支撑当地汽车企业、科技企业、科研机构和高校的人工智能技术与应用创新。

随着新一代人工智能技术的快速发展和突破，算力需求呈爆发式增长。根据Jaime Sevilla等人的研究，机器学习所需算力的增长可以分为前深度学习时代、深度学习时代和大规模时代三个阶段。在2010年之前，模型训练所需算力增长基本符合摩尔定律，大约每20个月翻一番。自2010年左右深度学习问世以来，模型训练所需算力快速增长，大约每6个月翻一番。特别是到2015至2016年左右，大模型的出现使得训练所需算力规模提升了1到2个数量级。

图：人工智能模型训练所需算力增长趋势

算力增长的背后，是模型和训练数据规模的增长。从 2016年到2020年，模型的参数量大概每18个月增长40倍；而同期英伟达GPU的计算增长速度仅为每18个月1.7倍。当前大模型的训练需要数以千计的GPU芯片组成大规模计算系统。

依靠大算力来推动人工智能发展，是当今人工智能发展的重要特征。强化学习先驱理查德·萨顿(Richard Sutton)在其博客文章《苦涩的教训》中指出，“回顾70年来的人工智能研究，从中得出的最重要的经验是，基于计算力的通用方法，是最有效的方法。”今天，这个通用方法就是深度学习，计算力就是以通用GPU为代表的高性能算力。

当前，BEV+Transformer感知架构已成为行业主流，以数据驱动成为系统迭代的关键手段。智能驾驶的技术路径已日渐清晰，算法已逐渐趋同。汽车企业的比拼更多是产品落地和快速迭代，是工程化、产品化和成本控制的能力，是更为流畅的用户体验。在这个过程中，算法更新的速度已经放缓，算力成为竞争的关键。

在汽车与算力领域，有两家具有全球影响力的企业，那就是特斯拉与英伟达。

从电动汽车到自动驾驶，特斯拉一路引领行业创新的方向。一直以来，特斯拉坚持依靠算力和数据提升其自动驾驶能力。凭借前瞻的眼光和强大的工程能力，特斯拉不仅自研车端自动驾驶芯片和云端数据中心芯片，还投资、设计并建造自有数据中心，其算力规模位居全球前列，预计到今年底会达100 EFLOPS。

相比特斯拉，英伟达在汽车行业的知名度似乎略逊一筹，但其实际影响力可能有过之而无不及。凭借其领先的GPU芯片和CUDA平台，英伟达处于全球智能计算生态的核心，对于自动驾驶算法和芯片的发展走向拥有巨大的影响力。其最新的DRIVE Thor芯片，采用与云端芯片相同的架构，为下一步车云协同建立了基础。

从特斯拉与英伟达的相互跨界，我们可以看到汽车与算力两个产业正在发生的交流与碰撞。

汽车的数字化和智能化，是汽车吸收引进包括人工智能在内的多种数字技术过程，是汽车企业打造创新的产品和商业模式的过程，需要汽车、信息通信、互联网等多个行业进行充分合作。在这其中，算力作为智能的基础底座，将成为推动汽车智能化和汽车行业数字化的重要力量。

经过多年不懈努力，我国在新能源智能网联汽车赛道上取得了一定领先优势。从全球产业链来看，我国在动力电池、传感器、网络通信、智能化应用等领域均具备较强竞争力，但在智能化底层的计算领域，我国仍然处于跟随地位，面临卡脖子问题。

汽车产业是十万亿量级的产业，涵盖了众多的人工智能应用场景，也是智能计算芯片的广阔市场。更为重要的是，电动化和智能化带来的汽车架构的变革，对计算芯片、基础软件、车内车外通信都提出了新的需求，给国产企业带来了难得的创新产品和拓展生态的机会和空间。

汽车与计算都是横跨制造与信息服务的庞大产业。汽车产业正在经历数字化的洗礼，向着高端制造和服务业迈进；计算产业正在寻求新的应用载体和平台，并争取实现 自主可控 。两个行业彼此交流、相互促进，必将有力带动我国数字经济发展，更好地服务人类未来美好生活。

算力在最近几年成为热点，汽车行业也概莫能外。但凡有新车型发布，几乎都会提到其搭载芯片的算力。国内外整车企业争相对算力进行大举投入，甚至自研相关芯片。近日，马斯克就在社交媒体上表示，2024年特斯拉将投资100亿美元用于人工智能的训练和推理。

算力究竟是什么，又为何受到如此关注？

算力，即计算的能力。作为智能的要素和底座，算力将成为推动汽车智能化、汽车行业数字化的重要力量。在今天大模型发展趋势下，这种重要性表现得更加突出。

过去几年，车端算力芯片受到行业高度关注。随着汽车智能化的竞争重点从算法转向数据和算力，云端算力将成为车企未来几年竞争的关键。

今天，汽车与算力两个产业正进行着交流和碰撞。未来的汽车将越来越依赖计算能力。而我国新能源智能汽车产业的蓬勃活力，也将有力带动算力产业发展。

算力，即计算的能力，一般用于表示某个设备或系统的计算性能。从智能驾驶计算芯片，到用于算法训练的超大规模集群，这些设备或系统都在提供着不同种类、不同规模的算力。算力无处不在，已成为推动经济和社会发展的关键生产力。

算力原本只是超算领域关注的对象，代表着人们对计算极致能力的不懈追求。深度学习技术的兴起带来了人工智能的热潮。人们把算力、算法和数据归结为智能的三大要素。由此，算力一词开始备受关注。

算力有多种表征指标，包括每秒百万次指令数（MIPS）、每秒浮点操作数（FLOPS）、每秒定点操作数（OPS）等。比如，一颗人工智能计算芯片的算力是256 TOPS，一台超级计算机的算力是1 EFLOPS。

算力的基础是各类高性能计算芯片。最重要和为人熟知的计算芯片是CPU（中央处理器）和GPU（图形处理器）。CPU是计算设备的运算和控制核心，适合处理逻辑复杂的串行任务。GPU早期主要用来加速图像计算任务，由于其更加侧重计算而非逻辑控制，并能很好地支持并行计算，成为目前提供算力的主要芯片。

图： CPU与GPU芯片设计对比

当前，电动化、智能化已成为汽车产业的发展趋势。人们对汽车的关注点从油耗转向动力电池的续航里程，从发动机的加速性能转向芯片的算力。不论是电子电气架构、还是智能驾驶等解决方案，很大程度上都需要考虑算力的分布和有效利用。算力已成为智能汽车的核心要素。

提到车端算力，最具代表性的可能是汽车智能驾驶芯片。当前主流的车端智能驾驶芯片算力已达数十到数百TOPS。车端算力芯片的主要供应商包括英伟达、Mobileye、高通等。近年来，国内也涌现出地平线、黑芝麻等一批企业。

车端算力芯片存在多种技术架构。高端芯片一般基于通用GPU架构，提供较高的通用算力，以适应不同模型与解决方案。中低端芯片多以ASIC（专用集成电路）为主，与算法深度绑定，更加追求功耗、性能和成本的平衡。

相比车端计算芯片，云端芯片需要承担复杂的训练任务和海量的数据处理，且对功耗和成本的要求相对车端宽松，其算力更为强劲，计算架构以通用GPU为主。在数据中心算力芯片领域，英伟达公司占据着绝对的龙头地位。

第一种是汽车企业自建算力中心。例如谷歌、特斯拉等头部企业通过自建专有计算集群的方式，提升自身业务运行效能。部分企业还会根据自身业务特点，研发专用芯片，以降低算力成本。

第二种是智能计算云服务。例如亚马逊、阿里云等云计算企业以云服务模式向汽车企业提供算力资源及技术服务。

第三种是公共智能计算中心。公共智能计算中心以地方政府主导建设为主，主要支撑当地汽车企业、科技企业、科研机构和高校的人工智能技术与应用创新。

随着新一代人工智能技术的快速发展和突破，算力需求呈爆发式增长。根据Jaime Sevilla等人的研究，机器学习所需算力的增长可以分为前深度学习时代、深度学习时代和大规模时代三个阶段。在2010年之前，模型训练所需算力增长基本符合摩尔定律，大约每20个月翻一番。自2010年左右深度学习问世以来，模型训练所需算力快速增长，大约每6个月翻一番。特别是到2015至2016年左右，大模型的出现使得训练所需算力规模提升了1到2个数量级。

图：人工智能模型训练所需算力增长趋势

算力增长的背后，是模型和训练数据规模的增长。从 2016年到2020年，模型的参数量大概每18个月增长40倍；而同期英伟达GPU的计算增长速度仅为每18个月1.7倍。当前大模型的训练需要数以千计的GPU芯片组成大规模计算系统。

依靠大算力来推动人工智能发展，是当今人工智能发展的重要特征。强化学习先驱理查德·萨顿(Richard Sutton)在其博客文章《苦涩的教训》中指出，“回顾70年来的人工智能研究，从中得出的最重要的经验是，基于计算力的通用方法，是最有效的方法。”今天，这个通用方法就是深度学习，计算力就是以通用GPU为代表的高性能算力。

当前，BEV+Transformer感知架构已成为行业主流，以数据驱动成为系统迭代的关键手段。智能驾驶的技术路径已日渐清晰，算法已逐渐趋同。汽车企业的比拼更多是产品落地和快速迭代，是工程化、产品化和成本控制的能力，是更为流畅的用户体验。在这个过程中，算法更新的速度已经放缓，算力成为竞争的关键。

在汽车与算力领域，有两家具有全球影响力的企业，那就是特斯拉与英伟达。

从电动汽车到自动驾驶，特斯拉一路引领行业创新的方向。一直以来，特斯拉坚持依靠算力和数据提升其自动驾驶能力。凭借前瞻的眼光和强大的工程能力，特斯拉不仅自研车端自动驾驶芯片和云端数据中心芯片，还投资、设计并建造自有数据中心，其算力规模位居全球前列，预计到今年底会达100 EFLOPS。

相比特斯拉，英伟达在汽车行业的知名度似乎略逊一筹，但其实际影响力可能有过之而无不及。凭借其领先的GPU芯片和CUDA平台，英伟达处于全球智能计算生态的核心，对于自动驾驶算法和芯片的发展走向拥有巨大的影响力。其最新的DRIVE Thor芯片，采用与云端芯片相同的架构，为下一步车云协同建立了基础。

从特斯拉与英伟达的相互跨界，我们可以看到汽车与算力两个产业正在发生的交流与碰撞。

汽车的数字化和智能化，是汽车吸收引进包括人工智能在内的多种数字技术过程，是汽车企业打造创新的产品和商业模式的过程，需要汽车、信息通信、互联网等多个行业进行充分合作。在这其中，算力作为智能的基础底座，将成为推动汽车智能化和汽车行业数字化的重要力量。

经过多年不懈努力，我国在新能源智能网联汽车赛道上取得了一定领先优势。从全球产业链来看，我国在动力电池、传感器、网络通信、智能化应用等领域均具备较强竞争力，但在智能化底层的计算领域，我国仍然处于跟随地位，面临卡脖子问题。

汽车产业是十万亿量级的产业，涵盖了众多的人工智能应用场景，也是智能计算芯片的广阔市场。更为重要的是，电动化和智能化带来的汽车架构的变革，对计算芯片、基础软件、车内车外通信都提出了新的需求，给国产企业带来了难得的创新产品和拓展生态的机会和空间。

汽车与计算都是横跨制造与信息服务的庞大产业。汽车产业正在经历数字化的洗礼，向着高端制造和服务业迈进；计算产业正在寻求新的应用载体和平台，并争取实现 自主可控 。两个行业彼此交流、相互促进，必将有力带动我国数字经济发展，更好地服务人类未来美好生活。
#!/usr/bin/env python3
"""
简单测试脚本 - 尝试不同的搜索方法
"""

import requests
import urllib3
from bs4 import BeautifulSoup
import time

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_simple_search():
    """测试简单搜索"""
    session = requests.Session()
    
    # 设置代理
    session.proxies = {
        'http': 'http://127.0.0.1:7890',
        'https': 'http://127.0.0.1:7890'
    }
    
    # 设置headers
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    session.verify = False
    
    # 首先访问主页面
    print("访问搜索页面...")
    main_url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion"
    response = session.get(main_url)
    print(f"主页面状态码: {response.status_code}")
    
    # 尝试不同的搜索URL和参数组合
    search_urls = [
        "https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion",
        "https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion"
    ]
    
    # 不同的参数组合
    param_sets = [
        {
            'querySentence': '餐飲',
            'timeRange': '114',
            'tenderStatusType': '決標'
        },
        {
            'textfield': '餐飲',
            'searchMethod': 'true',
            'searchTarget': 'ATM',
            'searchType': 'basic',
            'method': 'search'
        },
        {
            'querySentence': '餐飲',
            'timeRange': '114',
            'tenderStatusType': '決標',
            'sortCol': 'TENDER_NOTICE_DATE',
            'pageIndex': '1'
        }
    ]
    
    for i, url in enumerate(search_urls):
        for j, params in enumerate(param_sets):
            print(f"\n尝试组合 {i+1}-{j+1}: {url}")
            print(f"参数: {params}")
            
            try:
                # 尝试GET请求
                search_response = session.get(url, params=params)
                print(f"GET状态码: {search_response.status_code}")
                
                # 检查响应内容
                soup = BeautifulSoup(search_response.content, 'html.parser')
                
                # 查找结果表格
                tables = soup.find_all('table')
                print(f"找到 {len(tables)} 个表格")
                
                # 查找包含"標案"或"機關"的内容
                if '標案' in search_response.text or '機關' in search_response.text:
                    # 查找可能的结果行
                    rows_with_links = soup.find_all('tr')
                    link_count = 0
                    for row in rows_with_links:
                        links = row.find_all('a')
                        if links:
                            link_count += len(links)
                    
                    print(f"找到 {link_count} 个链接")
                    
                    # 保存这个响应用于分析
                    filename = f"test_response_{i+1}_{j+1}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(search_response.text)
                    print(f"已保存响应到 {filename}")
                
                time.sleep(2)
                
            except Exception as e:
                print(f"请求失败: {str(e)}")

if __name__ == "__main__":
    test_simple_search()

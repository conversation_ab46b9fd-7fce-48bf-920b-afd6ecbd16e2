#!/usr/bin/env python3
"""
测试8×RTX 3090训练环境设置
不需要完整模型下载，仅测试环境配置
"""

import os
import sys
import torch
import subprocess
from pathlib import Path

def test_gpu_setup():
    """测试GPU配置"""
    print("🔍 测试GPU配置...")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    gpu_count = torch.cuda.device_count()
    print(f"✅ 检测到 {gpu_count} 块GPU")
    
    total_memory = 0
    for i in range(gpu_count):
        gpu_name = torch.cuda.get_device_name(i)
        memory_gb = torch.cuda.get_device_properties(i).total_memory / 1024**3
        total_memory += memory_gb
        print(f"   GPU {i}: {gpu_name} ({memory_gb:.1f}GB)")
    
    print(f"💾 总显存: {total_memory:.1f}GB")
    
    if gpu_count == 8 and "RTX 3090" in gpu_name:
        print("🎯 完美！8×RTX 3090配置确认")
        return True
    else:
        print(f"⚠️  期望8×RTX 3090，实际: {gpu_count}×{gpu_name}")
        return True  # 仍然可以继续

def test_dependencies():
    """测试依赖包"""
    print("\n🔍 测试依赖包...")
    
    dependencies = [
        ("torch", "PyTorch"),
        ("accelerate", "Accelerate"),
        ("peft", "PEFT"),
        ("deepspeed", "DeepSpeed"),
        ("transformers", "Transformers"),
        ("diffsynth", "DiffSynth-Studio")
    ]
    
    all_good = True
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} 未安装")
            all_good = False
    
    return all_good

def test_data_setup():
    """测试数据集设置"""
    print("\n🔍 测试数据集...")
    
    dataset_path = Path("./data/example_video_dataset")
    metadata_path = dataset_path / "metadata.csv"
    
    if not dataset_path.exists():
        print("❌ 数据集目录不存在")
        return False
    
    if not metadata_path.exists():
        print("❌ metadata.csv不存在")
        return False
    
    # 检查视频文件
    video_files = list(dataset_path.glob("*.mp4"))
    if not video_files:
        print("❌ 没有找到视频文件")
        return False
    
    print(f"✅ 数据集检查通过")
    print(f"   路径: {dataset_path}")
    print(f"   视频文件: {len(video_files)}个")
    
    return True

def create_accelerate_config():
    """创建accelerate配置"""
    print("\n🔧 创建accelerate配置...")
    
    config_content = """compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 8
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
"""
    
    config_dir = Path.home() / ".cache" / "huggingface" / "accelerate"
    config_dir.mkdir(parents=True, exist_ok=True)
    
    config_file = config_dir / "default_config.yaml"
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    print(f"✅ Accelerate配置已创建: {config_file}")
    return True

def create_simple_training_test():
    """创建简单的训练测试脚本"""
    print("\n📝 创建训练测试脚本...")
    
    # 创建一个最小化的训练测试
    test_script = """#!/bin/bash

# 简单的多GPU测试脚本
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

echo "🧪 测试多GPU环境..."
python -c "
import torch
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')

# 测试简单的多GPU操作
if torch.cuda.device_count() >= 2:
    print('✅ 多GPU环境测试通过')
    
    # 创建简单的张量测试
    device = torch.device('cuda:0')
    x = torch.randn(4, 4).to(device)
    y = torch.randn(4, 4).to(device)
    z = torch.mm(x, y)
    print(f'✅ GPU计算测试通过: {z.shape}')
else:
    print('⚠️  GPU数量不足，无法测试多GPU')
"

echo "✅ 多GPU环境测试完成"
"""
    
    with open("test_multi_gpu.sh", 'w') as f:
        f.write(test_script)
    
    os.chmod("test_multi_gpu.sh", 0o755)
    print("✅ 测试脚本已创建: test_multi_gpu.sh")
    
    return True

def run_gpu_test():
    """运行GPU测试"""
    print("\n🚀 运行GPU测试...")
    
    try:
        result = subprocess.run(["bash", "test_multi_gpu.sh"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ GPU测试通过")
            print(result.stdout)
            return True
        else:
            print("❌ GPU测试失败")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ GPU测试超时")
        return False
    except Exception as e:
        print(f"❌ GPU测试出错: {e}")
        return False

def show_training_recommendations():
    """显示训练建议"""
    print("\n💡 8×RTX 3090训练建议:")
    print("=" * 50)
    
    recommendations = [
        "🎯 推荐从T2V 1.3B LoRA训练开始",
        "📊 每GPU批次大小: 4 (1.3B模型) 或 1 (14B模型)",
        "🔄 梯度累积步数: 2-4",
        "💾 使用bf16混合精度训练",
        "⚡ 启用梯度检查点节省显存",
        "🔧 使用8bit Adam优化器",
        "📈 总有效批次大小: 64-128",
        "⏱️  预估训练时间: 30分钟(1.3B LoRA) - 12小时(14B全量)"
    ]
    
    for rec in recommendations:
        print(f"   {rec}")
    
    print("\n🚀 开始训练命令:")
    print("   python train_8x3090_optimized.py --model t2v_1.3b --training-type lora")
    print("   python train_8x3090_optimized.py --model t2v_14b --training-type lora")

def main():
    print("🎬 8×RTX 3090 Wan视频模型训练环境测试")
    print("=" * 60)
    
    all_tests_passed = True
    
    # 测试GPU
    if not test_gpu_setup():
        all_tests_passed = False
    
    # 测试依赖
    if not test_dependencies():
        all_tests_passed = False
    
    # 测试数据集
    if not test_data_setup():
        all_tests_passed = False
    
    # 创建配置
    create_accelerate_config()
    
    # 创建测试脚本
    create_simple_training_test()
    
    # 运行GPU测试
    if not run_gpu_test():
        all_tests_passed = False
    
    # 显示建议
    show_training_recommendations()
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 所有测试通过！环境已准备就绪")
        print("💡 现在可以开始训练了")
    else:
        print("⚠️  部分测试失败，请检查上述错误")
    
    return 0 if all_tests_passed else 1

if __name__ == "__main__":
    sys.exit(main())

{"architectures": ["ErnieForCausalLM"], "bos_token_id": 0, "eos_token_id": 1, "hidden_act": "silu", "hidden_size": 8192, "intermediate_size": 28672, "initializer_range": 0.00482174, "max_sequence_length": 4096, "max_position_embeddings": 4096, "model_type": "ernie", "num_attention_heads": 64, "num_key_value_heads": 8, "num_hidden_layers": 56, "pad_token_id": -1, "rms_norm_eps": 1e-05, "torch_dtype": "float16", "transformers_version": "4.27.0.dev0", "use_cache": true, "vocab_size": 100352, "rope_theta": 10000, "use_recompute": true, "use_recompute_attn": false, "use_recompute_moe": false, "use_recompute_loss_fn": false, "use_rmsnorm": true, "fuse_rms_norm": true, "use_bias": false, "fuse_attn_ffn": true, "fuse_linear": true, "rope_reorder": false, "fuse_rope": true, "fuse_swiglu": true, "fuse_gate_detach_matmul": true, "remove_tail_layer": 2, "moe_num_experts": 64, "moe_num_shared_experts": 0, "moe_layer_start_index": 3, "moe_group_experts": false, "moe_intermediate_size": 3584, "moe_capacity": [8, 8, 8], "moe_gate": "top2_fused", "moe_gate_scale": false, "moe_gate_detach": 1.0, "moe_k": 8, "moe_aux_loss_lambda": 1e-05, "moe_layer_interval": 1, "using_precision_check": false, "use_ep_comm_overlap": true}
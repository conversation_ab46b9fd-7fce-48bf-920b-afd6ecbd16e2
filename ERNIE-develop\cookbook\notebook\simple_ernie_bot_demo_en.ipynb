{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Simple Version of ERNIE Bot Web Demo\n", "\n", "This tutorial demonstrates how to use the ERNIE models to implement a simple web-based intelligent question-and-answer system with the following features:\n", "\n", "- Dialogue: Users can chat with the ERNIE models in multiple rounds.\n", "- File Processing: The demo can generate responses based on the files uploaded by the user.\n", "- Image Processing: The demo can generate responses based on the image uploaded by the user.\n", "- Retrieval-Augmented: The system can access search engines like Baidu Search to obtain information."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environmental Setup\n", "Before starting, ensure your system meets these requirements:\n", "- Python version 3.10-3.12 is installed.\n", "- Ensure the following Python libraries are included: base64, openai, `json`, `textwrap`, `jieba`, `pdfplumber`, `python-docx`, `requests`, `crawl4ai`、`asyncio`、`nest_asyncio`.\n", "    - For the installation steps of `crawl4ai`, please refer to [crawl4ai installation instructions](https://github.com/unclecode/crawl4ai).\n", "- Set the API key for the model `model_api_key`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_api_key = \"api_key\" # the API key for model, which can be disregarded and replaced with any arbitrary value when using the model deployed locally."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.1. Install Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install openai jieba pdfplumber python-docx requests asyncio nest_asyncio\n", "\n", "# Install crawl4ai\n", "!pip install -U crawl4ai\n", "!pip install crawl4ai --pre\n", "!crawl4ai-setup"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. File Processing\n", "File content understanding and file question and answer functions can be implemented based on a text-only model.\n", "\n", "When using a text-only model, you need to deploy [the ERNIE-4.5](https://github.com/PaddlePaddle/FastDeploy) series model's services and correctly configure the corresponding service address `ernie_45_url`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ernie_45_url = \"http://localhost:port/v1\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1. File parsing\n", "It supports upload and parsing of multiple document formats, and the document content will be used as input context for models to process.\n", "\n", "Supported formats include: PDF, DOCX, TXT, MD"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["咖啡（英语：coffee）是指咖啡植物的种子即咖啡豆在经过烘焙磨粉后通过冲泡制成的饮料，是世界上流行范围最为广泛的饮料之一。咖啡在人类饮食中一般为日常的饮品，人们通常会为了提振精神，或在用餐和社交、阅读时饮用。咖啡原产于非洲东岸的埃塞俄比亚，咖啡起源于15-16世纪，从也门被传播至穆斯林世界，16世纪的威尼斯商人将咖啡引入意大利，随后17-18世纪由于欧洲对咖啡的需求，促使殖民者将咖啡树传播并栽种到美洲、东南亚和印度等热带地区，现今有超过70个国家种植咖啡树。未经烘焙的 咖啡生豆作为世界上最大的出口农产品，以及世界上交易量为广泛的热带农产品之一，也是发展中国家出口中最有价值的商品之一。采收的成熟咖啡果会经过剥离果肉的初步加工，再经过烘焙的工序，而成为能制作咖啡的咖啡豆。透过不同的冲泡方式与成分比例，咖啡有浓缩咖啡、卡布奇诺和拿铁咖啡等变化。咖啡豆的品种可大致分为两种：最为普遍的小果咖啡（阿拉比卡），以及颗粒较粗且酸味较低而苦味较浓的中果咖啡（罗布斯塔）。一些争议指咖啡的种植与它环境影响有关，例如肯亚咖啡豆在移植种植后失去了独有的肯亚酸，而肯亚的原种地土壤含有较高浓度的磷酸。因此，公平贸易咖啡与有机咖啡是一个不断扩大的市场。\n", "\n", "传说9世纪的埃塞俄比亚的牧羊人发现并咀嚼了咖啡果实，随后将咖啡果实带给了附近修道院的僧侣，但僧侣起初不愿食用果实，并把果实扔进火里，经过火烤的咖啡果中冒出香气引来僧侣前来查看，僧侣从余烬中捞出咖啡豆，并将其磨碎溶解在热水中，这才制成了世界上第一杯咖啡。但此故事截至1671年并没有得到任何记载，因此可能是杜撰的。亦有研究认为最初栽培的咖啡源自埃塞俄比亚的哈勒尔。埃塞俄比亚的阿克苏姆王国兴盛时曾一度占据也门南部，6世纪中期，萨珊帝国攻占也门后将阿克苏姆赶出南阿拉伯半岛，可以肯定的是咖啡是从埃塞俄比亚传播到也门的。\n", "\n", "咖啡传播到穆斯林世界后伊斯兰医学认可了咖啡的好处，认为其可以提振精神并防止酒和大麻对穆斯林的诱惑，15世纪的也门苏菲派修道院在祈祷时使用咖啡来帮助集中注意力。 16世纪初咖啡从也门的摩卡港传播到埃及，随后咖啡馆还出现在叙利亚阿勒颇，并于1554年在奥斯曼帝国首都伊斯坦布尔开业。1511年，由于也门麦加的宗教领袖认为咖啡具有刺激作用，便开始禁止穆斯林饮用咖啡，造成其余阿拉伯世界的苏丹和宗教领袖也相继效仿；其中两位奥斯曼帝国苏丹更是同样出于\n"]}], "source": ["import pdfplumber\n", "from docx import Document\n", "\n", "\n", "def get_file_text(file_path):\n", "    if file_path is None:\n", "        return \"\"\n", "    if file_path.endswith(\".pdf\"):\n", "        return read_pdf(file_path)\n", "    elif file_path.endswith(\".docx\"):\n", "        return read_docx(file_path)\n", "    elif file_path.endswith(\".txt\") or file_path.endswith(\".md\"):\n", "        return read_txt_md(file_path)\n", "    else:\n", "        return \"\"\n", "\n", "def read_pdf(pdf_path):\n", "    try:\n", "        text = \"\"\n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            for page in pdf.pages:\n", "                text += page.extract_text()\n", "        return text\n", "    except Exception as e:\n", "        print(f\"Error reading PDF file: {e}\")\n", "        return \"\"\n", "\n", "def read_docx(file_path):\n", "    try:\n", "        doc = Document(file_path)\n", "        full_text = []\n", "        for paragraph in doc.paragraphs:\n", "            full_text.append(paragraph.text)\n", "        return \"\\n\".join(full_text)\n", "    except Exception as e:\n", "        print(f\"Error reading DOCX file: {e}\")\n", "        return \"\"\n", "\n", "def read_txt_md(file_path):\n", "    try:\n", "        with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "            return f.read()\n", "    except Exception as e:\n", "        print(f\"Error reading TXT or MD file: {e}\")\n", "        return \"\"\n", "\n", "test_file = \"../data/coffee.txt\"\n", "file_text = get_file_text(test_file)\n", "print(file_text[:1000])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2. Model Input\n", "The input of model is a message list that represents the context history of the conversation. Each message is a dictionary containing the following fields:\n", "- `role`: Represents the role of the message sender, which can be:\n", "    - `user`: User message, indicating user input\n", "    - `assistant`: Model message, indicating the model's reply\n", "- `content`: Specific text content\n", "\n", "File processing input has the following characteristics:\n", "- Multiple Rounds of Dialogue: Supporting the preservation of historical dialogue context\n", "- File Processing: Parse the document content and splice it with query"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'user', 'content': '1675 年时，英格兰有多少家咖啡馆？\\n资料:coffee.txt\\n咖啡（英语：coffee）是指咖啡植物的种子即咖啡豆在经过烘焙磨粉后通过冲泡制成的饮料，是世界上流行范围最为广泛的饮料之一。咖啡在人类饮食中一般为日常的饮品，人们通常会为了提振精神，或在用餐和社交、阅读时饮用。咖啡原产于非洲东岸的埃塞俄比亚，咖啡起源于15-16世纪，从也门被传播至穆斯林世界，16世纪的威尼斯商人将咖啡引入意大利，随后17-18世纪由于欧洲对咖啡的需求，促使殖民者将咖啡树传播并栽种到美洲、东南亚和印度等热带地区，现今有超过70个国家种植咖啡树。未经烘焙的 咖啡生豆作为世界上最大的出口农产品，以及世界上交易量为广泛的热带农产品之一，也是发展中国家出口中最有价值的商品之一。采收的成熟咖啡果会经过剥离果肉的初步加工，再经过烘焙的工序，而成为能制作咖啡的咖啡豆。透过不同的冲泡方式与成分比例，咖啡有浓缩咖啡、卡布奇诺和拿铁咖啡等变化。咖啡豆的品种可大致分为两种：最为普遍的小果咖啡（阿拉比卡），以及颗粒较粗且酸味较低而苦味较浓的中果咖啡（罗布斯塔）。一些争议指咖啡的种植与它环境影响有关，例如肯亚咖啡豆在移植种植后失去了独有的肯亚酸，而肯亚的原种地土壤含有较高浓度的磷酸。因此，公平贸易咖啡与有机咖啡是一个不断扩大的市场。\\n\\n传说9世纪的埃塞俄比亚的牧羊人发现并咀嚼了咖啡果实，随后将咖啡果实带给了附近修道院的僧侣，但僧侣起初不愿食用果实，并把果实扔进火里，经过火烤的咖啡果中冒出香气引来僧侣前来查看，僧侣从余烬中捞出咖啡豆，并将其磨碎溶解在热水中，这才制成了世界上第一杯咖啡。但此故事截至1671年并没有得到任何记载，因此可能是杜撰的。亦有研究认为最初栽培的咖啡源自埃塞俄比亚的哈勒尔。埃塞俄比亚的阿克苏姆王国兴盛时曾一度占据也门南部，6世纪中期，萨珊帝国攻占也门后将阿克苏姆赶出南阿拉伯半岛，可以肯定的是咖啡是从埃塞俄比亚传播到也门的。\\n\\n咖啡传播到穆斯林世界后伊斯兰医学认可了咖啡的好处，认为其可以提振精神并防止酒和大麻对穆斯林的诱惑，15世纪的也门苏菲派修道院在祈祷时使用咖啡来帮助集中注意力。 16世纪初咖啡从也门的摩卡港传播到埃及，随后咖啡馆还出现在叙利亚阿勒颇，并于1554年在奥斯曼帝国首都伊斯坦布尔开业。1511年，由于也门麦加的宗教领袖认为咖啡具有刺激作用，便开始禁止穆斯林饮用咖啡，造成其余阿拉伯世界的苏丹和宗教领袖也相继效仿；其中两位奥斯曼帝国苏丹更是同样出于政治考量，而在1517年和1623年两度禁止咖啡。\\n\\n同样在16世纪，与阿拉伯世界的贸易令威尼斯获得了包括咖啡在内的非洲商品，威尼斯商人则向威尼斯的上流阶级高价推销咖啡。起初意大利的宗教人士对咖啡这种穆斯林饮料持怀疑态度，并称咖啡为“撒旦的苦涩发明（bitter invention of Satan）”或是“阿拉伯酒（wine of Araby）”，1600年，教宗克莱孟八世对咖啡的争议作出裁决，在教宗品尝咖啡后认为可以饮用，并祝福了咖啡。 1616年，荷兰商人彼得·范登布罗克从也门摩卡获得了一些阿拉比卡咖啡树苗并带回了阿姆斯特丹，还在当地植物园种植成功。1658年，荷兰人首先在其殖民地锡兰和印度南部开始种植咖啡，但出于避免供应过剩而降低价格的考量，最终放弃了在锡兰种植，专注于爪哇和苏里南的种植园。\\n\\n1675年时，英格兰就有3000多家咖啡馆；启蒙运动时期，咖啡馆成为民众深入讨论宗教和政治的聚集地，1670年代的英国国王查理二世就曾试图取缔咖啡馆。这一时期的英国人认为咖啡具有药用价值，甚至名医也会推荐将咖啡用于医疗。\\n\\n1773年，波士顿倾茶事件后约翰·亚当斯和许多美国人认为喝茶是不爱国的，令大量美国人在美国独立战争期间改喝咖啡。\\n\\n18世纪，葡萄牙人首先在巴西里约热内卢附近，后来则是圣保罗种植咖啡并建设种植园。1852-1950年，巴西主导了世界咖啡生产，其出口的咖啡比世界其他地区的总和还多。1950年以来，由于哥伦比亚和越南等主要生产国相继出现，而越南在1999年超过哥伦比亚成为世界第二大咖啡生产国，并在2011年达到15%的市场份额，而同年巴西的市场份额仅占33%。\\n\\n在咖啡的原产地埃塞俄比亚，18世纪前咖啡曾被埃塞俄比亚正教会所禁止，直至19世纪后期叶埃塞俄比亚皇帝孟尼利克二世的统治时期才有所开放。\\n\\n咖啡在19世纪中已经引入中国上海，1843年—44年上海对外贸易文献就有记载“枷榧豆5包，每包70斤”，表明当时上海已经从外国进口咖啡豆。\\n\\n香港英文报章在1866年刊登关于coffee shop的报导。1885年香港中文报章以“咖啡”为中文名，此后逐渐成为华语地区普及使用的中文译名。香港在1840年代起有英国人聚居，由于饮食文化的差异，最初被输入到香港的咖啡豆是主要供应西方人饮用，而一般本地华人则不喜欢咖啡苦涩的味道，在早年的香港常有大量从事搬运工作的苦力在码头聚集，为来港的货轮搬运货物，从事体力劳动的苦力比一般华人更容易接触到刚循海路进口的咖啡豆，所以在华人社会中最早有饮用咖啡习惯的群体，却是社会地位低下的码头搬运工。\\n\\n不同地区和民族之间的口味偏好，令咖啡冲泡方式以及调味品的使用多种多样，通常热咖啡添加砂糖、牛奶、奶油、奶精等调味，冷饮咖啡则有更多选择，如酒、薄荷、丁香、柠檬汁等。而不同冲泡和调味方式亦产生出了许多咖啡品类：\\n\\n土耳其咖啡：是种具有古老历史的咖啡饮品和冲泡方式，而土耳其以外的中东国家以及东南欧皆有流行过此种冲泡方式。土耳其咖啡冲泡好后未经过滤即可直接饮用，土耳其传统上会将土耳其咖啡倒入小瓷杯中慢慢啜饮，而处于悬浊状的咖啡残留有少量咖啡渣亦成为土耳其咖啡独特风味与口感的来源。冲泡土耳其咖啡的方法为将咖啡豆研磨成粉末后装入土耳其壶中，倒入热水并与咖啡末搅拌均匀，再加人豆蔻粉充分搅拌，对土耳其壶加热并充分搅拌。咖啡煮至冒泡后停止加热，待泡沫消失，此时可短暂重复加热2次；或是将三分之一的咖啡先倒入到各个杯子中，壶中剩余的咖啡则再度加热，直到沸腾后倒入之前的杯子里。\\n\\n浓缩咖啡：是一种通过迫使接近沸腾的高压水流通过咖啡末制作而成的咖啡，拿铁咖啡和卡布奇诺、玛琪雅朵等皆是以浓缩咖啡为基本制成的。\\n\\n拿铁咖啡：拿铁咖啡是由浓缩咖啡和热牛奶以1:2的比例冲泡，并加入些许奶泡制成的。也可依需求加上两份浓缩咖啡，意大利语称之为“Double”。\\n\\n卡布奇诺：卡布奇诺是一种意大利咖啡，是由在浓缩咖啡上倒入奶泡制成，由于咖啡的颜色就像方济嘉布遣会修士深褐色外衣上覆的头巾一样，卡布奇诺也因此得名。其与拿铁咖啡类似，区别仅是卡布奇诺在咖啡、牛奶、奶泡的比例为1:1:1。卡布奇诺咖啡奶泡多，而拿铁咖啡的奶泡少。口味上卡布奇诺咖啡的咖啡味重，而拿铁较为清淡一些，这是因为拿铁的牛奶更多。\\n\\n摩卡咖啡：通常是由三分之一的意式浓缩咖啡和三分之二的奶泡配成，并加入少量巧克力糖浆或速溶巧克力粉。拉夫咖啡是在单杯浓缩咖啡中添加带有少量泡沫（0.5 厘米）的奶油而制成的咖啡。通常与香草糖一起喝用但通常使用糖浆代替香草糖。\\n\\n玛琪雅朵咖啡：在冲泡好的浓缩咖啡上加入鲜奶并倒入一层较薄的奶泡的意大利咖啡。\\n\\n焦糖玛琪雅朵：是一种在浓缩咖啡加入热牛奶和香草，最后淋上焦糖制成的玛琪雅朵咖啡。\\n\\n欧蕾咖啡：是一种咖啡和牛奶的比例为1:1的牛奶咖啡，在冲泡时，需要牛奶壶和咖啡壶从两旁同时注入到咖啡杯。在星巴克则被称为Caffè Misto，以1:1比例的法式压滤咖啡搭配奶泡而成。\\n\\n美式咖啡：是一种浓缩咖啡以1:5比例加入热水稀释制成的咖啡饮料。冲泡美式咖啡亦可使用意式咖啡机萃取浓缩咖啡，而在咖啡萃取完成后，继续使用咖啡机向浓缩咖啡加入热水稀释到合适比例即可。其浓度随浓缩咖啡的冲泡次数和添加的水量而变化，美式咖啡具有浓缩咖啡风味但却更为柔和。\\n\\n长黑咖啡：是澳大利亚和新西兰常见的一种咖啡，是将双份浓缩咖啡倒入热水中制成的，其恰好与美式咖啡截然相反。长黑咖啡通常使用约100–120毫升的水，但水量可根据个人口味灵活调整。\\n\\n维也纳咖啡：其制作方式为将糖或粗砂糖放入杯内再倒入热咖啡，杯上挤入鲜奶油以及巧克力膏，最终撒上彩色糖粒装饰即可。此种制法可追溯至1683年，当时乌克兰裔波兰军官耶日·弗朗西泽克·库奇茨基开设了奥地利首家咖啡馆并在维也纳开业，其普及了在咖啡中加糖和牛奶的制作和饮用方式。而维也纳咖啡传说是由奥地利马车夫爱因·舒伯纳发明。\\n\\n爱尔兰咖啡：在咖啡中加入威士忌后在其顶部放上奶油。而加入威士忌的爱尔兰咖啡能将咖啡的酸甜味衬托出来。\\n\\n调味咖啡：依据口味的不同在咖啡中加入巧克力、糖浆、果汁、肉桂、肉豆蔻、橘子花等不同调味料。\\n\\n康宝蓝：康宝蓝是一种在意大利浓缩咖啡上倒入适量奶油的咖啡，并用玻璃咖啡杯盛装，由于鲜奶油具有甜味因此通常无需加糖。\\n\\n白咖啡：起源于马来西亚怡保，其使用经过人造黄油烘培的咖啡豆，冲泡好后加入甜炼乳的饮品。19世纪和20世纪初英国锡矿公司在怡保设立锡矿，而中国移民则在怡保锡矿工作，白咖啡是19世纪中后期移民马来亚的海南人出于华人不习惯咖啡味道而发明。从本质上是一种拿铁咖啡。在美国，白咖啡也指轻度烘培的咖啡豆，使用意式冲煮，具有较强酸味的咖啡。\\n\\n越南咖啡：是一种滴漏咖啡，冲泡时先在盛装咖啡的杯子中倒入炼乳，将滴漏壶置于盛装的杯上，并向滴漏壶加入咖啡末，再以压板压住咖啡末，倒入热水后等待滴漏。越南常用的咖啡豆品种为罗布斯塔，因其带有较重的酸味与苦味以及烘焙时间较长，使得风味较重，因此需要加入炼乳饮用。\\n\\n印度滴漏咖啡：其通常是阿拉比卡咖啡或咖啡公豆制作的；咖啡豆经过深度烘焙、研磨并与菊苣混合，咖啡占混合物的80-90%，其余的为菊苣。菊苣的轻微苦味有助于产生印度滴漏咖啡的风味，传统上使用粗糖或蜂蜜作为甜味剂，但自1900年代中期改为白糖。\\n\\n皇家咖啡：据说拿破仑在俄法战争时，因遭遇俄国酷寒的冬天，于是命令下属在咖啡里倒入白兰地取暖而发明。其制作方式为，在预热好的咖啡杯中倒入热咖啡，将咖啡匙架在杯缘上，在咖啡匙上放置方糖后淋上白兰地并点火燃烧，火焰熄灭后将咖啡匙放入咖啡搅拌至方糖溶解即可饮用。\\n\\n黑咖啡：是使用滴滤法、渗滤法、虹吸法或加法冲泡的咖啡，在饮用时不添加牛奶、糖等调味品。速溶咖啡是不属于黑咖啡的范围的。\\n\\n希腊法拉沛咖啡：通常由速溶咖啡、糖和牛奶制成的冰咖啡，咖啡中也会倒入奶泡；其口感微甜凉爽，适宜在夏季饮用。\\n\\n阿芙佳朵：是种近乎甜点的冰咖啡，由冰淇淋上加入意大利浓缩咖啡制成。会加入焦糖来增加甜味和促进口感，或加入巧克力酱、可可粉、肉桂粉等。\\n'}]\n"]}], "source": ["import os\n", "\n", "query = \"1675 年时，英格兰有多少家咖啡馆？\"\n", "\n", "file_name = os.path.basename(test_file)\n", "query += f\"\\n资料:{file_name}\\n{file_text}\\n\"\n", "file_messages = [\n", "    {\"role\": \"user\", \"content\": query}\n", "]\n", "print(file_messages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3. Non-Streaming Request\n", "#### 2.3.1. Request Model\n", "When sending a request to the API, the following main parameters need to be considered:\n", "- `messages` (must): List of conversation messages\n", "- `max_tokens` (optional): configuration parameter for maximum number of generated tokens\n", "- `temperature` (optional): configuration parameter for controlling randomness in generated results\n", "- `top_p` (optional): configuration parameter for nucleus sampling"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'as-6q6<PERSON><PERSON><PERSON>', 'choices': [{'finish_reason': 'stop', 'index': 0, 'logprobs': None, 'message': {'content': '根据提供的资料，1675年时，英格兰有3000多家咖啡馆。', 'refusal': None, 'role': 'assistant', 'annotations': None, 'audio': None, 'function_call': None, 'tool_calls': None}, 'flag': 0}], 'created': 1750081699, 'model': 'ernie', 'object': 'chat.completion', 'service_tier': None, 'system_fingerprint': None, 'usage': {'completion_tokens': 19, 'prompt_tokens': 2668, 'total_tokens': 2687, 'completion_tokens_details': None, 'prompt_tokens_details': None}}\n"]}], "source": ["from openai import OpenAI\n", "\n", "client = OpenAI(base_url=ernie_45_url, api_key=model_api_key)\n", "response = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=file_messages,\n", "    temperature=1.0,\n", "    max_tokens=2048,\n", "    top_p=0.7\n", ")\n", "response = response.model_dump()\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.3.2. Model Output\n", "- `content`: Final answer"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["根据提供的资料，1675年时，英格兰有3000多家咖啡馆。\n"]}], "source": ["content = response[\"choices\"][0][\"message\"][\"content\"]\n", "print(content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4. Streaming Request\n", "#### 2.4.1. Request Model\n", "When sending a request to the API, the following main parameters need to be considered:\n", "- `messages` (must): List of conversation messages\n", "- `max_tokens` (optional): configuration parameter for maximum number of generated tokens\n", "- `temperature` (optional): configuration parameter for controlling randomness in generated results\n", "- `top_p` (optional): configuration parameter for nucleus sampling\n", "- `stream` (optional): Whether to return streaming"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'id': 'as-0igrcdhck5', 'choices': [{'delta': {'content': '根据', 'function_call': None, 'refusal': None, 'role': 'assistant', 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None, 'flag': 0}], 'created': 1750081942, 'model': 'ernie', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}, {'id': 'as-0igrcdhck5', 'choices': [{'delta': {'content': '提供的', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None, 'flag': 0}], 'created': 1750081942, 'model': 'ernie', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}, {'id': 'as-0igrcdhck5', 'choices': [{'delta': {'content': '资料', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None, 'flag': 0}], 'created': 1750081942, 'model': 'ernie', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}]\n"]}], "source": ["from openai import OpenAI\n", "\n", "client = OpenAI(base_url=ernie_45_url, api_key=model_api_key)\n", "response = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=file_messages,\n", "    temperature=1.0,\n", "    max_tokens=2048,\n", "    top_p=0.7,\n", "    stream=True\n", ")\n", "response_stream = []\n", "for chunk in response:\n", "    if not chunk.choices:\n", "        continue\n", "    response_stream.append(chunk.model_dump())\n", "\n", "print(response_stream[:3])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.4.2. Model Output\n", "The model's output will be delivered via streaming return.\n", "- `content`: Final answer"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["根据提供的资料，1675年时，英格兰有3000多家咖啡馆。\n"]}], "source": ["content_text_stream = \"\"\n", "for res in response_stream:\n", "    content_text_stream += res[\"choices\"][0][\"delta\"][\"content\"]\n", "print(content_text_stream)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Image Processing\n", "Features requiring visual semantic understanding, such as image description and image-text question answering, can be implemented using multimodal models.\n", "\n", "When using multimodal models, you need to deploy the [ERNIE-4.5-VL](https://github.com/PaddlePaddle/FastDeploy) series model's services and correctly configure the corresponding service address `ernie_45_vl_url`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ernie_45_vl_url = \"http://localhost:port/v1\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1. <PERSON> Parsing\n", "- Supported document formats: PDF, DOCX, TXT, MD\n", "- When uploading local pictures, you need to use the `base64` library to convert the image file into a Base64 encoded string and splice it into a standard format of `data:image/{type};base64,{Base64 encoded string}`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import base64\n", "\n", "\n", "def get_image_url(image_path):\n", "    base64_image = \"\"\n", "    extension = image_path.split(\".\")[-1]\n", "    with open(image_path, \"rb\") as image_file:\n", "        base64_image = base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "    url = f\"data:image/{extension};base64,{base64_image}\"\n", "    return url\n", "\n", "test_image = \"../assets/yiyan_logo.png\"\n", "image_url = get_image_url(test_image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2. Model Input\n", "The input has the following characteristics:\n", "- Multiple Rounds of Dialogue: Supporting the preservation of historical dialogue context\n", "- Mixed Input: Supports sending pictures and text content at the same time\n", "- Image Support: You can upload pictures through URL or local files"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# url pictures\n", "multi_messages_url = [\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image_url\", \"image_url\": {\"url\": \"https://nlp-eb.cdn.bcebos.com/static/eb/asset/topLogo.4a0fc7b7\"}},\n", "        {\"type\": \"text\", \"text\": \"请分析这张图\"}\n", "    ]}\n", "]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'system', 'content': '你是一个图片分析助手'}, {'role': 'user', 'content': [{'type': 'image_url', 'image_url': {'url': 'data:image/png;base64,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'}}, {'type': 'text', 'text': '请分析这张图'}]}]\n"]}], "source": ["# Local pictures\n", "multi_messages_local = [\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image_url\", \"image_url\": {\"url\": image_url}},\n", "        {\"type\": \"text\", \"text\": \"请分析这张图\"}\n", "    ]}\n", "]\n", "print(multi_messages_local)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3. Request Model\n", "When sending a request to the API, the following main parameters need to be considered:\n", "- `messages` (must): a list of image and text dialogue messages. The pictures can be uploaded locally (`multi_messages_local`), or you can enter the URL address (`multi_messages_url`)\n", "- `max_tokens` (optional): configuration parameter for maximum number of generated tokens\n", "- `temperature` (optional): configuration parameter for controlling randomness in generated results\n", "- `top_p` (optional): configuration parameter for nucleus sampling"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'chatcmpl-40681438-6bf8-4af9-b51d-1978ce39e8d8', 'object': 'chat.completion', 'created': 1749006988, 'model': 'default', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '这张图是“文心一言”的logo。整体来看，该logo采用简洁现代的设计风格。\\n### 图形部分\\n- 左侧图形由几何形状构成，类似六边形与内部线条的组合，线条可能象征数据流动或智能交互，六边形在科技感设计中较为常见，暗示产品的科技属性。\\n### 文字部分\\n- 右侧“文心一言”采用无衬线字体，笔画粗细一致，简洁明了，符合现代审美。“文心”二字可能寓意文化内涵与核心智能，“一言”则突出语言交流与输出的特性。\\n### 色彩方面\\n- 蓝色通常代表科技、专业、可靠，选择蓝色为主色调，传达出产品具备专业的人工智能技术和可靠的服务品质。</s></s>', 'reasoning_content': None}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 54, 'total_tokens': 212, 'completion_tokens': 158, 'prompt_tokens_details': None}}\n"]}], "source": ["from openai import OpenAI\n", "\n", "client = OpenAI(base_url=ernie_45_vl_url, api_key=model_api_key)\n", "response = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=multi_messages_local,\n", "    temperature=1.0,\n", "    max_tokens=2048,\n", "    top_p=0.7\n", ")\n", "response = response.model_dump()\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.4. Model Output\n", "- `content`: Final answer"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["这张图是“文心一言”的logo。整体来看，该logo采用简洁现代的设计风格。\n", "### 图形部分\n", "- 左侧图形由几何形状构成，类似六边形与内部线条的组合，线条可能象征数据流动或智能交互，六边形在科技感设计中较为常见，暗示产品的科技属性。\n", "### 文字部分\n", "- 右侧“文心一言”采用无衬线字体，笔画粗细一致，简洁明了，符合现代审美。“文心”二字可能寓意文化内涵与核心智能，“一言”则突出语言交流与输出的特性。\n", "### 色彩方面\n", "- 蓝色通常代表科技、专业、可靠，选择蓝色为主色调，传达出产品具备专业的人工智能技术和可靠的服务品质。</s></s>\n"]}], "source": ["content = response[\"choices\"][0][\"message\"][\"content\"]\n", "print(content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Retrieval-Augmented Generation\n", "- Retrieval enhancements can be implemented based on a text-only model. When using a text-only model, you need to deploy the [ERNIE-4.5](https://github.com/PaddlePaddle/FastDeploy) series model services and correctly configure the corresponding service address `ernie_45_url`.\n", "- The Retrieval-Augmented Generation needs to be completed with the help of AI search tools, so the API key `qianfan_api_key` needs to be set.You can log in to [<PERSON><PERSON><PERSON>](https://console.bce.baidu.com/iam/#/iam/apikey/list) to create your API key.API keys are sensitive information and should be kept properly."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model service address configuration\n", "ernie_45_url = \"http://localhost:port/v1\"\n", "\n", "# AI Search Tool Configuration\n", "qianfan_api_key = \"bce-v3/xxx\"  # Replace with your real API key\n", "web_search_service_url = \"https://qianfan.baidubce.com/v2/ai_search/chat/completions\"  # AI Search Tool URL"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1. Main Search Structure Overview\n", "\n", "- **Step 1: Search Query Rewriting**\n", "    \n", "    First, analyze whether the user's query needs obtain the latest information from the internet. When searching, rewrite the user's query to get the queries that be searched.\n", "\n", "- **Step 2: Get the Full Search Results**\n", "\n", "    crawl the complete web page content from the `URL` in the search results based on AI search tools.\n", "\n", "- **Step 3: Generate the final answer**\n", "\n", "    Organize the search results and call the model's interface to generate answers.\n", "\n", "### 4.2. Search Query Rewriting\n", "This step requires the model to determine whetherthe user's query needs obtain the latest information from the internet, and rewrite the user's query to get the queries that be searched.\n", "\n", "A prompt is required to guide the model to complete the task and return the standardized JSON format results."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## 当前时间\n", "2025-06-25 14:52:47\n", "\n", "## 对话\n", "user:\n", "你好\n", "assistant:\n", "你好，我是你的智能搜索助手，请问有什么我可以帮助你的？\n", "\n", "问题：上海有什么美食\n", "\n", "根据当前时间和对话完成以下任务：\n", "1. 查询判断：是否需要借助搜索引擎查询外部知识回答用户当前问题。\n", "2. 问题改写：改写用户当前问题，使其更适合在搜索引擎查询到相关知识。\n", "注意：只在**确有必要**的情况下改写，输出不超过 5 个改写结果，不要为了凑满数量而输出冗余问题。\n", "\n", "## 输出如下格式的内容（只输出 JSON ，不要给出多余内容）：\n", "```json\n", "{\n", "    \"is_search\":true/false,\n", "    \"query_list\":[\"改写问题1\"，\"改写问题2\"...]\n", "}```\n", "\n"]}], "source": ["import textwrap\n", "from datetime import datetime\n", "\n", "SEARCH_INFO_PROMPT = textwrap.dedent(\n", "    \"\"\"\\\n", "## Current time\n", "    {date}\n", "\n", "## Conversation\n", "    {context}\n", "    问题：{query}\n", "\n", "    根据当前时间和对话完成以下任务：\n", "    1. 查询判断：是否需要借助搜索引擎查询外部知识回答用户当前问题。\n", "    2. 问题改写：改写用户当前问题，使其更适合在搜索引擎查询到相关知识。\n", "    注意：只在**确有必要**的情况下改写，输出不超过 5 个改写结果，不要为了凑满数量而输出冗余问题。\n", "\n", "## Output content in the following format (only output JSON, do not give extra content):\n", "    ```json\n", "    {{\n", "        \"is_search\":true/false,\n", "        \"query_list\":[\"改写问题1\"，\"改写问题2\"...]\n", "    }}```\n", "    \"\"\"\n", ")\n", "history = \"user:\\n你好\\nassistant:\\n你好，我是你的智能搜索助手，请问有什么我可以帮助你的？\\n\"\n", "query = \"上海有什么美食\"\n", "search_content = SEARCH_INFO_PROMPT.format(\n", "    date=datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    context=history,\n", "    query=query\n", ")\n", "print(search_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Call the model interface for judgment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'chatcmpl-af6a1428-1dc0-41f4-860a-9786b741005f', 'choices': [{'finish_reason': 'stop', 'index': 0, 'logprobs': None, 'message': {'content': '```json\\n{\\n    \"is_search\": true,\\n    \"query_list\": [\"上海美食推荐\", \"上海特色美食\", \"上海必吃美食\", \"上海美食排行榜\"]\\n}\\n```</s></s>', 'refusal': None, 'role': 'assistant', 'annotations': None, 'audio': None, 'function_call': None, 'tool_calls': None, 'reasoning_content': None}}], 'created': 1750768029, 'model': 'default', 'object': 'chat.completion', 'service_tier': None, 'system_fingerprint': None, 'usage': {'completion_tokens': 46, 'prompt_tokens': 201, 'total_tokens': 247, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}}\n"]}], "source": ["from openai import OpenAI\n", "\n", "judge_search_messages = [{\"role\": \"user\", \"content\": search_content}]\n", "\n", "client = OpenAI(base_url=ernie_45_url, api_key=model_api_key)\n", "response = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=judge_search_messages\n", ")\n", "\n", "response = response.model_dump()\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["parse the model's results to json format.\n", "- `is_search`: Whether to search online.\n", "- `query_list`: List of queries that can be searched."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'is_search': True, 'query_list': ['上海美食推荐', '上海特色美食', '上海必吃美食', '上海美食排行榜']}\n"]}], "source": ["import json\n", "import re\n", "\n", "search_query = response[\"choices\"][0][\"message\"][\"content\"]\n", "json_match = re.search(r'```json\\n(.*?)\\n```', search_query, re.DOTALL)\n", "json_str = json_match.group(1)\n", "search_query = json.loads(json_str)\n", "\n", "print(search_query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3. Get the Full Search Results\n", "\n", "#### 4.3.1. AI Search\n", "\n", "If previous results show the query is needed to be searched online, use the AI ​​search tool to obtain the search results.\n", "\n", "AI search tool request parameters:\n", "- `messages`: Search for input\n", "    - `role`: The role of the message sender, which can be a `user` user message, indicating the user's input; or an `assistant` model message, which can represent the model's reply\n", "    - `content`: The content text of the message\n", "- `resource_type_filter`: search result filtering\n", "    - `type`: Search type. In this example, only the setting of web search modals is supported.\n", "    - `top_k`: Maximum number of the result\n", "\n", "For more request parameters in AI search, please refer to [Baidu AI Search](https://cloud.baidu.com/doc/AppBuilder/s/zm8pn5cju)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'content': ' 晚上7点一开市,设在露天广场上的夜集市迎来当天的首批食客。徐州烧烤、淄博烧烤、阜阳卷馍、东北大饭包、青岛酱蘸鲍鱼、江西炒粉……随着一个个摊位忙碌起来,各种美食的香气随之在空气中弥漫,引得食客胃口大开。 “这是东北大饭包,你看,白菜叶子里面包了好多料,土豆泥啊什么的,想加什么给你加,好大一只。” “之前就在路边摆摊,一天到晚提心吊胆的,我有5个顾客群,大概2000人左右,我每天出摊的时候就发定位告诉大家,我今天会在哪条路边摆,不时换地方,今天不知道明天咋样……这感觉不好受。现在好了。” 经前期调研发现,在罗店自发形成的占道经营摊点日均达100余处。像陶晓哲这样的流动餐饮摊贩带来的噪声、垃圾、油烟等各种扰民问题,是罗店镇城运中心最常接到的市民投诉。 流动摊贩集中规范运营便民点应运而生。', 'date': '2025-06-21 21:16:06', 'icon': None, 'id': 1, 'image': None, 'title': '开市仅一个多月,北上海这个美食夜集市怎么就火了……', 'type': 'web', 'url': 'https://www.jfdaily.com/sgh/detail?id=1597987', 'video': None, 'web_anchor': '开市仅一个多月,北上海这个美食夜集市怎么就火了……'}, {'content': ' 摩洛哥风情海鲜总汇塔吉锅 20元可以升级 红鱼柳 黑虎虾 鱿鱼 鲍鱼 连蛤蜊都饱满鲜嫩 酱汁酸辣带点奶香 法棍蘸着吃直接嗦手指 满满一锅海鲜的快乐太过瘾了 辣芝士雪峰牛排牛骨髓 太会了 五分熟牛排一刀切爆汁 牛骨髓挖出来胶质感裹着肉香搭配着牛排 再蘸点旁边的辣酱和土豆泥 口感层次丰富 加量不加价 以前套餐里半只的墨西哥风味烤鸡现在直接上整只 鸡皮烤得焦香滴油 鸡胸肉都不带柴的 配的香脆薯角 一口鸡一口薯 快乐double 仙人掌盆栽蛋糕 很萌 奶冻打底 巧克力碎 小饼干 挖一勺入口 冰凉细腻带点可可香 吃完连花盆都想舔干净 这套餐量也太实诚了 我们俩人撑到实在吃不下只能打包 在世纪大道这地段 性价比直接杀疯了 AMINO AMIGO 世纪汇店 世纪大道1192号世纪汇广场LG1层020 世纪大道地铁8号口出口 户外广场', 'date': '2025-06-11 00:00:00', 'icon': None, 'id': 2, 'image': None, 'title': '上海美食推荐清单|世纪大道地铁口!终于西餐也能吃到过瘾了!', 'type': 'web', 'url': 'https://m.ctrip.com/webapp/you/tripshoot/paipai/detail/detail?articleId=158732057&isHideNavBar=YES&seo=0&twojumpwakeup=0&allianceId=1049189&sid=19855591', 'video': None, 'web_anchor': '上海美食推荐清单|世纪大道地铁口!终于西餐也能吃到过瘾了!'}, {'content': '\\U000f098e 上海\\uf045 旬味会 外滩 旬味会 外滩 热搜: 美食试吃官2折美食套餐旬味会 美食林榜单 特色菜 本周热销 旬味会 免费订座 美食必打卡榜 当地风味餐厅榜 特色小吃店榜 奢华餐厅榜 美景体验餐厅榜 酒吧榜 夜店榜 位置\\uf045 菜系\\uf045 筛选\\uf045 智能排序\\uf045 2024榜单餐厅 在线订座 套餐/代金券 本帮菜 外滩 人均100-200 \\U000f0769 上海和平饭店·龙凤厅 \\ue933 上海和平饭店 内餐厅 2024亚洲100当地风味餐厅 4.8分|636条点评|￥674/人 本帮菜 近外滩 距市中心1.8km 莫尔顿牛排坊(浦东ifc店) 订 \"源自波士顿的牛排餐厅,品味美食的同时还能欣赏陆家嘴的繁华景象。\" 4.7分|146条点评|￥898/人 西餐近陆家嘴 距市中心2.7km 上海浦东丽思卡尔顿酒店·金轩中餐厅 订 \\ue933 上海浦东丽思卡尔顿酒店 内餐厅 \\U000f0885 铂金 2025全球100美景体验餐厅 4.8分|259条点评|￥773/人 粤菜近陆家嘴 距市中心2.7km 上海璞丽酒店·LONG BAR长吧 \\ue933 上海璞丽酒店 内餐厅 \"菜品可依个人喜好搭配,尽情享受定制美食。', 'date': '2023-03-05 00:00:00', 'icon': None, 'id': 3, 'image': None, 'title': '上海热门美食攻略/排行榜单/大全/介绍_必吃特色小吃/菜_推荐/介绍_上海好吃的旅行餐厅【携程美食林】', 'type': 'web', 'url': 'https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html', 'video': None, 'web_anchor': '上海热门美食攻略/排行榜单/大全/介绍_必吃特色小吃/菜_推荐/介绍_上海好吃的旅行餐厅【携程美食林】'}]\n"]}], "source": ["import requests\n", "\n", "max_search_results_num = 15\n", "if search_query.get(\"is_search\", False) and search_query.get(\"query_list\", []):\n", "    headers = {\n", "        \"Authorization\": \"Bearer \" + qianfan_api_key,\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "\n", "    search_result = []\n", "    top_k = max_search_results_num // len(search_query[\"query_list\"])\n", "    for query in search_query[\"query_list\"]:\n", "        payload = {\n", "            \"messages\": [{\"role\": \"user\", \"content\": query}],\n", "            \"resource_type_filter\": [{\"type\": \"web\", \"top_k\": top_k}]\n", "        }\n", "        response = requests.post(web_search_service_url, headers=headers, json=payload)\n", "        response = response.json()\n", "\n", "        search_result.append(response[\"references\"])\n", "    print(search_result[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The results returned by the search mainly include:\n", "- `content`: Web page summary\n", "- `title`: web page title\n", "- `url`: Web page URL\n", "\n", "For more response parameters descriptions, please refer to [Baidu AI Search](https://cloud.baidu.com/doc/AppBuilder/s/zm8pn5cju)\n", "\n", "#### 4.3.2. Crawl the Complete Web Page Content\n", "\n", "Since `content` only contains part of the web page content, you need to crawl the text in the URL to obtain the complete search results.\n", "\n", "Use the `crawl4ai` tool to crawl the web page text data and replace the original `content`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">31s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m31s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">15s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m15s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">47s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m47s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["进入词条全站搜索\n", "进入词条全站搜索\n", "播报编辑收藏赞\n", "登录\n", "# 上海菜\n", "播报编辑上传视频\n", "江南地区传统饮食文化的一种流派\n", "许魏洲邀你吃“不要太灵的”上海本帮菜\n", "03:35\n", "上海传统美食大合集!生煎小笼本帮菜,根本吃不够!\n", "17:59\n", "吃了30多年红烧肉，只服这种特色做法，色泽红亮肥而不腻，真过瘾\n", "10:08\n", "上海菜十大名菜有哪些：让你领略上海独特美味！\n", "01:49\n", "去北京开的上海老饭店吃本帮菜，这几个菜就花了500多，烤菜吃不惯\n", "03:41\n", "外国丈母娘第一次来中国吃上海本帮菜，被震撼到，中国菜这么好吃\n", "08:58\n", "上海外滩吃顿本帮菜，不知道正宗不\n", "00:18\n", "老洋房里快40年的上海菜，很低调，要不是排队谁会以为是饭店\n", "02:07\n", "开在古镇里面的本帮菜老店\n", "01:55\n", "老弄堂里30多年上海菜，老板“大半个香港娱乐圈都来过”\n", "01:46\n", "上海弄堂里的本帮菜馆，店不大生意爆棚，没开门就得排队\n", "05:38\n", "这才是上海本帮菜的水平，红烧划水太惊艳，小店6张桌座无虚席\n", "04:20\n", "收藏\n", "查看\n", "585有用+1\n", "102\n", "沪菜，即上海菜，是其常用的烹调方法以红烧、煨、糖为主，以浓油赤酱、咸淡适中、保持原味、醇厚鲜美为其特色。 [4]\n", "上海菜的别称为本帮菜，是江南地区传统饮食文化的一个重要流派。 自1843年上海开埠以来，随着工商业的发展，四方商贾云集，饭店酒楼应运而生。到20世纪三四十年代，有京、广、苏、扬、锡、甬、杭、闽、川、徽、潮、湘、以及上海本地菜等十六个帮别，同时还有素菜、清真菜，各式西菜、西点。 此时，上海菜原以红烧、生煸见长，后来吸取无锡、苏州、宁波等地方菜的特点，为适应上海人喜食清淡爽口的口味，菜肴渐由原来的重油赤酱趋向淡雅爽口，同时，兼及西菜、西点之法，使花色品种有了很大的发展，拥有鱼下巴甩水、腌川红烧圈子、白斩鸡、松江鲈鱼等名菜。 [4]\n", "2014年，上海本帮菜肴传统烹饪技艺被列入国家级非物质文化遗产代表性项目名录。 [5]2018年9月，八宝鸭被评为“中国菜”之上海十大经典名菜。 [6]\n", "## 相关星图\n", "常见的中国菜系\n", "共36个词条23.3万阅读中文名\n", "上海菜别 名\n", "沪菜、本帮菜 [4]产 地\n", "上海 [4]名 菜\n", "鱼下巴甩水、腌川红烧圈子、白斩鸡、松江鲈鱼 [4]烹调方法\n", "以红烧、煨、糖为主 [4]特 点\n", "浓油赤酱、咸淡适中、保持原味、醇厚鲜美 [4]概 念\n", "食品，烹饪口 味\n", "淡雅爽口 [4]\n", "## 目录\n", "1. 1烹饪特色\n", "2. 2历史发展\n", "3. 3经典名菜\n", "4. 4著名食馆\n", "5. ▪德兴菜馆1. ▪上海美食街\n", "2. ▪王宝和酒家\n", "3. 5名菜制作\n", "4. ▪白斩鸡\n", "5. ▪猪油百果松糕\n", "6. ▪醉鸡1. ▪葡萄鱼\n", "2. ▪河虾争妍\n", "3. ▪蟹肉大排翅\n", "4. ▪碧玉牛筋\n", "5. ▪八宝鸭\n", "6. ▪沪江排骨1. ▪鹅肝酱片\n", "2. ▪清蒸大闸蟹\n", "3. 6沪菜现状## 烹饪特色\n", "播报\n", "编辑\n", "上海菜原以红烧、生煸见长。后来，吸取了无锡、苏州、宁波等地方菜的特点，参照上述十六帮别的烹调技术，兼及西菜、西点之法，使花色品种有了很大的发展。\n", "菜肴风味的基本特点：汤卤醇厚，浓油赤酱，糖重色艳，咸淡适口。选料注重活、生、寸、鲜；调味擅长咸、甜、糟、酸。名菜如“红烧蛔鱼”，巧用火候，突出原味，色泽红亮，卤汁浓厚，肉质肥嫩，负有盛誉。\n", "“糟钵头”则是上海本地菜善于在烹调中加“糟”的代表，把陈年香糟加工复制成糟卤，在烧制中加入，使菜肴糟香扑鼻，鲜味浓郁。\n", "“生煸草头”，摘梗留叶，重油烹酒，柔软鲜嫩，蔚成一格。而各地方风味的菜肴也逐步适应上海的特点，发生了不同的变革，如川菜从重辣转向轻辣，锡菜从重甜改为轻甜，还有不少菜馆吸取外地菜之长。经过长期的实践，在取长补短的基础上。改革了烹调方法，上海菜达到了品种多样，别具一格，形成了上海菜的独特风味。\n", "由于上海本地菜（包括苏锡菜）与外地菜长期共存，相互影响，便在原本地菜的基础上，逐渐发展成以上海和苏锡风味为主体并兼有各地风味的上海风味菜体系。\n", "上海菜具有许多与众不同的特点：\n", "首先讲究选料新鲜。它选用四季时令蔬菜，鱼是以江浙两省产品为主，取活为上，一年四季都有活鱼供客选择，当场活杀烹制。\n", "第二菜肴品种多，四季有别。\n", "第三讲究烹调方法并不断加以改进。上海菜原来以烧、蒸、煨、窝、炒并重，逐渐转为以烧、生煸、滑炒、蒸为主，其中以生煸、滑炒为最多特别善烹四季河鲜。\n", "第四口味也有了很大变化。原来上海菜以浓汤、浓汁厚味为主，后来逐步变为卤汁适中，有清淡素雅，也有浓油赤酱，讲究鲜嫩、色调，鲜咸适口。特别是夏秋季节的糟味菜肴，香味浓郁，颇有特色。\n", "如今，上海菜进一步具有选料新鲜、品质优良、刀工精细、制作考究、火候恰当、清淡素雅、咸鲜适中、口味多样、适应面广、风味独特等优点。其主要名菜有“青鱼下巴甩水”、“青鱼秃肺”、“腌川红烧圈子”...\n"]}], "source": ["import asyncio\n", "import re\n", "\n", "import nest_asyncio\n", "from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig, DefaultMarkdownGenerator, PruningContentFilter\n", "\n", "\n", "async def get_webpage_text(url):\n", "    content_filter = PruningContentFilter(\n", "        threshold=0.48,\n", "        threshold_type=\"fixed\",\n", "        min_word_threshold=0\n", "    )\n", "    # Config makedown generator\n", "    md_generator = DefaultMarkdownGenerator(\n", "        content_filter=content_filter\n", "    )\n", "    run_config = CrawlerRunConfig(\n", "        # 20 seconds page timeout\n", "        page_timeout=20000,\n", "\n", "        # Filtering\n", "        word_count_threshold=10,\n", "        excluded_tags=[\"nav\", \"footer\", \"aside\", \"header\", \"script\", \"style\", \"iframe\", \"meta\"],\n", "        exclude_external_links=True,\n", "        exclude_internal_links=True,\n", "        exclude_social_media_links=True,\n", "        exclude_external_images=True,\n", "        only_text=True,\n", "\n", "        # Markdown generation\n", "        markdown_generator=md_generator,\n", "\n", "        # Cache\n", "        cache_mode=CacheMode.BYPASS\n", "    )\n", "    try:\n", "        async with AsyncWebCrawler() as crawler:\n", "            result = await crawler.arun(\n", "                url=url,\n", "                config=run_config\n", "            )\n", "\n", "        webpage_text = result.markdown.fit_markdown\n", "\n", "        # Clean up the text\n", "        cleaned_text = webpage_text.replace(\"undefined\", \"\")\n", "        cleaned_text = re.sub(r'(\\n\\s*){3,}', '\\n\\n', cleaned_text)\n", "        cleaned_text = re.sub(r'[\\r\\t]', '', cleaned_text)\n", "        cleaned_text = re.sub(r' +', ' ', cleaned_text)\n", "        cleaned_text = re.sub(r'^\\s+|\\s+$', '', cleaned_text, flags=re.MULTILINE)\n", "        return cleaned_text.strip()\n", "\n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        return None\n", "\n", "nest_asyncio.apply()\n", "loop = asyncio.get_event_loop()\n", "\n", "web_url = \"https://baike.baidu.com/item/上海菜/672867\"\n", "web_page_text = loop.run_until_complete(get_webpage_text(web_url))\n", "print(web_page_text[: 2000] + \"...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Integrate complete search results. To avoid too long search results, you need to limit the length of search content characters by `max_search_results_char`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.jfdaily.com/sgh/detail?id=1597987</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                        |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">13s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://www.jfdaily.com/sgh/detail?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1597987\u001b[0m\u001b[32m                                                        |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m13s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.jfdaily.com/sgh/detail?id=1597987</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                        |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">04s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://www.jfdaily.com/sgh/detail?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1597987\u001b[0m\u001b[32m                                                        |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m04s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://www.jfdaily.com/sgh/detail?id=1597987</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                        |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">18s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://www.jfdaily.com/sgh/detail?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1597987\u001b[0m\u001b[32m                                                        |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m18s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache /var/folders/gk/66ypgljn2gg7971dkhnm1q5w0000gn/T/jieba.cache\n", "Loading model cost 0.333 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.ctrip.com/webapp/you/tripshoot/paipai/...&amp;twojumpwakeup=0&amp;allianceId=1049189&amp;sid=19855591</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">07s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://m.ctrip.com/webapp/you/tripshoot/paipai/...&\u001b[0m\u001b[4;32mtwojumpwakeup\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m0\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mallianceId\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1049189\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32msid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m19855591\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m07s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.ctrip.com/webapp/you/tripshoot/paipai/...&amp;twojumpwakeup=0&amp;allianceId=1049189&amp;sid=19855591</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">00s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://m.ctrip.com/webapp/you/tripshoot/paipai/...&\u001b[0m\u001b[4;32mtwojumpwakeup\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m0\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mallianceId\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1049189\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32msid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m19855591\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m00s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.ctrip.com/webapp/you/tripshoot/paipai/...&amp;twojumpwakeup=0&amp;allianceId=1049189&amp;sid=19855591</span><span style=\"color: #008000; text-decoration-color: #008000\">  |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">08s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://m.ctrip.com/webapp/you/tripshoot/paipai/...&\u001b[0m\u001b[4;32mtwojumpwakeup\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m0\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mallianceId\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1049189\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32msid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m19855591\u001b[0m\u001b[32m  |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m08s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html</span><span style=\"color: #008000; text-decoration-color: #008000\">                                         |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">50s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html\u001b[0m\u001b[32m                                         |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m50s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html</span><span style=\"color: #008000; text-decoration-color: #008000\">                                         |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">02s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html\u001b[0m\u001b[32m                                         |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m02s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html</span><span style=\"color: #008000; text-decoration-color: #008000\">                                         |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">52s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html\u001b[0m\u001b[32m                                         |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m52s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.thepaper.cn/newsDetail_forward_31025474</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                    |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">91s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://m.thepaper.cn/newsDetail_forward_31025474\u001b[0m\u001b[32m                                                    |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m91s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.thepaper.cn/newsDetail_forward_31025474</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                    |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">01s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://m.thepaper.cn/newsDetail_forward_31025474\u001b[0m\u001b[32m                                                    |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m01s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://m.thepaper.cn/newsDetail_forward_31025474</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                    |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">93s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://m.thepaper.cn/newsDetail_forward_31025474\u001b[0m\u001b[32m                                                    |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m93s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">27s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m27s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">09s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m09s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baike.baidu.com/item/</span><span style=\"color: #008000; text-decoration-color: #008000\">上海菜/</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">672867</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                            </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">| ✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">36s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baike.baidu.com/item/\u001b[0m\u001b[32m上海菜/\u001b[0m\u001b[1;32m672867\u001b[0m\u001b[32m                                                            \u001b[0m\n", "\u001b[32m| \u001b[0m\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m36s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828711351827613727&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">89s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828711351827613727\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m89s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828711351827613727&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">03s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828711351827613727\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m03s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828711351827613727&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">92s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828711351827613727\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m92s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1834297223163493130&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">07s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1834297223163493130\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m07s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1834297223163493130&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">03s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1834297223163493130\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m03s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1834297223163493130&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">11s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1834297223163493130\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m11s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1835639030190648447&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">04s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1835639030190648447\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m04s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1835639030190648447&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">03s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1835639030190648447\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m03s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1835639030190648447&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">08s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1835639030190648447\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m08s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://new.qq.com/rain/a/20250620A04RMZ00</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                           |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">19s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://new.qq.com/rain/a/20250620A04RMZ00\u001b[0m\u001b[32m                                                           |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m19s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://new.qq.com/rain/a/20250620A04RMZ00</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                           |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">02s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://new.qq.com/rain/a/20250620A04RMZ00\u001b[0m\u001b[32m                                                           |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m02s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://new.qq.com/rain/a/20250620A04RMZ00</span><span style=\"color: #008000; text-decoration-color: #008000\">                                                           |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">21s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://new.qq.com/rain/a/20250620A04RMZ00\u001b[0m\u001b[32m                                                           |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m21s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080\">INIT</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\">.... → Crawl4AI </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.6</span><span style=\"color: #008080; text-decoration-color: #008080\">.</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[36mINIT\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m...\u001b[0m\u001b[36m. → Crawl4AI \u001b[0m\u001b[1;36m0.6\u001b[0m\u001b[36m.\u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">FETCH</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">... ↓ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828709688765599122&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">06s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mFETCH\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m...\u001b[0m\u001b[32m ↓ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828709688765599122\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m06s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">SCRAPE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\">.. ◆ </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828709688765599122&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">0.</span><span style=\"color: #008000; text-decoration-color: #008000\">03s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mSCRAPE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m.. ◆ \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828709688765599122\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m0.\u001b[0m\u001b[32m03s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">COMPLETE</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ● </span><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\">https://baijiahao.baidu.com/s?id=1828709688765599122&amp;wfr=spider&amp;for=pc</span><span style=\"color: #008000; text-decoration-color: #008000\">                               |</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">✓ | ⏱: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">1.</span><span style=\"color: #008000; text-decoration-color: #008000\">10s </span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[32mCOMPLETE\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ● \u001b[0m\u001b[4;32mhttps://baijiahao.baidu.com/s?\u001b[0m\u001b[4;32mid\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32m1828709688765599122\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mwfr\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mspider\u001b[0m\u001b[4;32m&\u001b[0m\u001b[4;32mfor\u001b[0m\u001b[4;32m=\u001b[0m\u001b[4;32mpc\u001b[0m\u001b[32m                               |\u001b[0m\n", "\u001b[32m✓\u001b[0m\u001b[32m | ⏱: \u001b[0m\u001b[1;32m1.\u001b[0m\u001b[32m10s \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import jieba\n", "\n", "\n", "def cut_chinese_english(text):\n", "    words = jieba.lcut(text)\n", "    en_ch_words = []\n", "\n", "    for word in words:\n", "        if word.isalpha() and not any(\"\\u4e00\" <= char <= \"\\u9fff\" for char in word):\n", "            en_ch_words.append(word)\n", "        else:\n", "            en_ch_words.extend(list(word))\n", "    return en_ch_words\n", "\n", "async def get_complete_search_content(search_results: list, max_search_results_char: int=18000) -> str:\n", "    results = []\n", "    for search_res in search_results:\n", "        for item in search_res:\n", "            new_content = await get_webpage_text(item[\"url\"])\n", "            if not new_content:\n", "                continue\n", "            item_text = \"Title: {title} \\nURL: {url} \\nContent:\\n{content}\\n\".format(title=item[\"title\"], url=item[\"url\"], content=new_content)\n", "\n", "# Truncate the search result to max_search_results_char characters\n", "            search_res_words = cut_chinese_english(item_text)\n", "            res_words = cut_chinese_english(\"\".join(results))\n", "            if len(search_res_words) + len(res_words) > max_search_results_char:\n", "                break\n", "\n", "            results.append(f\"参考资料[{len(results) + 1}]:\\n{item_text}\\n\")\n", "\n", "    return \"\".join(results)\n", "\n", "complete_search_result = loop.run_until_complete(get_complete_search_content(search_result))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3. Generate Final Answer\n", "#### 4.3.1. Model Input\n", "the input has the following characteristics:\n", "- Network Search: splice the complete search results to `ANSWER_PROMPT` and provide it to the model as a context.\n", "- Multiple Rounds of Dialogue: Supporting the preservation of historical dialogue context"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'user', 'content': '下面你会收到多段参考资料和一个问题。你的任务是阅读参考资料，并根据参考资料中的信息回答对话中的问题。\\n以下是当前时间和参考资料：\\n---------\\n## 当前时间\\n2025-06-24 19:31:40\\n\\n## 参考资料\\n参考资料[1]:\\nTitle: 开市仅一个多月,北上海这个美食夜集市怎么就火了…… \\nURL: https://www.jfdaily.com/sgh/detail?id=1597987 \\nContent:\\n用户名：\\n---\\n密 码：\\n验证码： | 看不清\\n2025-06-24星期二\\n模糊搜索 作者搜索 标题搜索 正文搜索 摘要搜索\\n我的位置： > > 文章详情\\n# 开市仅一个多月，北上海这个美食夜集市怎么就火了……\\n转自：上海宝山 2025-06-21 21:16:06\\n位于宝山区罗店镇7号线罗南新村地铁站外的\\n流动摊贩集中规范运营（临时）便民点\\n最近在社交平台上火了起来\\n一百多个摊位\\n汇聚了全国各地的特色小吃\\n成了人们体验城市烟火气的一处网红夜集市\\n点燃城市烟火气的同时\\n如何让城市管理文明有序\\n罗店镇的这一探索\\n试图寻找治理的新路径\\nPART.1\\n晚上7点一开市，设在露天广场上的夜集市迎来当天的首批食客。徐州烧烤、淄博烧烤、阜阳卷馍、东北大饭包、青岛酱蘸鲍鱼、江西炒粉……随着一个个摊位忙碌起来，各种美食的香气随之在空气中弥漫，引得食客胃口大开。\\n“这种烟火气还能找得到，挺好的，以前我们俩都是晚上开车去昆山去吃夜宵。”\\n“我正好地铁从市里下班回来，就顺便过来吃点东西，这里想吃什么都有，而且有统一管理，蛮规范的，吃起来也放心吧。”\\n“这是东北大饭包，你看，白菜叶子里面包了好多料，土豆泥啊什么的，想加什么给你加，好大一只。”\\n自5月初运营以来，夜集市备受周边居民和食客的青睐，晚上7点到次日凌晨2点运营期间，每天平均迎来食客一千多人次，节假日高峰时甚至可达四五千人次。吉林小串的摊主陶晓哲忙个不停。\\n“这里人流量大，而且不用提心吊胆，营业额也挺可观的，原来的话一天营业额在3000~5000元，现在好的时候就1万多。”\\n两年前，为了陪在上海工作的女儿，快50岁的陶晓哲和老公来到罗店，开始摆起了烧烤摊。在老家有过10多年开烧烤店的经验，夫妻俩的小摊很快就吸引了不少食客光顾。但路边摆摊的经历并不好受。\\n“之前就在路边摆摊，一天到晚提心吊胆的，我有5个顾客群，大概2000人左右，我每天出摊的时候就发定位告诉大家，我今天会在哪条路边摆，不时换地方，今天不知道明天咋样……这感觉不好受。现在好了。”\\nPART.2\\n经前期调研发现，在罗店自发形成的占道经营摊点日均达100余处。像陶晓哲这样的流动餐饮摊贩带来的噪声、垃圾、油烟等各种扰民问题，是罗店镇城运中心最常接到的市民投诉。\\n但作为上海北部人口集中导入区域，近年来，美兰湖和罗店大居片区年轻住户和租客人数上升，而他们，正是夜集市消费的主力军。原有配套公建设施已无法满足居民日常需求，特别是受经济大环境影响，小餐饮、便民服务等基础保障方面存在明显缺口。\\n一边是客观存在的消费需求，一边是实实在在的管理问题，如何在二者之间寻求平衡？\\n流动摊贩集中规范运营便民点应运而生。罗店镇城运中心相关负责人说，沪太路杨南路西北侧，原金岷大厦西侧空地这一点位的选择，是经过对周边居民影响最小、交通便利等多方面仔细研判而定。\\n“这个地块离地铁站近，又属于储备地块，就是规土部门交由罗店镇代为管理的，大约4000平方米左右，此前是用于居民非机动车临时停放。地块西边和北边都是储备地块，只有一面靠近居民区，但也有150多米的直线距离，且居民大多是租户，其中，二三十岁的年轻租户达九成以上。”\\n地点确定后，实现有序规范运行，后续管理必须跟上。于是，管理方制定了详细的管理规定和操作规程，明确摊位经营时间、消防安全要求。同时，还统一提供基础水电，消防设施、移动厕所、机动车停放区域，安排多名保洁人员清理现场垃圾、维持场地整洁。\\n现场管理方负责人说，为确保食品安全和消费者权益，对摊贩的入场资格也有严格的筛选机制。\\n“打个比方，烧烤摊必须有油烟净化器，所有的刀具锅铲等需要配备一个索道链，把它们控制在自己的操作范围之内，防止伤害他人。每日的进货单、健康证这是必备的。一些需要冷鲜柜的必须得配备，比如说肉类食品，天慢慢热了之后可能会容易变质。”\\n管理方还设立了退出机制：对违反管理规定的摊主，首次警告并限期整改，二次违规将暂停营业一周，三次违规则取消经营资格。同时，设立线下居民投诉点，及时处理反馈问题，确保居民权益得到有效保障。建立了针对摊主的负面清单制度，记录违规行为，定期公示。\\n通过多维度监管，既满足居民生活需求，又维护市容秩序，实现双赢。\\nPART.3\\n管理规范、公平有序、交通便利、每天最多百余元的管理费、零租金零押金……便民点一开市，就吸引流动摊贩争相报名。进场经营的摊主从最初的130家增加到现在的150家，便民点也在食客的口口相传，在社交平台的助推下，成为“网红夜集市”。\\n“摊主挣得到钱，老百姓觉得这个地方又卫生又规范，也愿意来。再加上管理方日常的规范管理，我认为，这几个方面最终促成了这个点位能够做到良性的循环。”\\n这一切在罗店镇城运中心相关负责人看来，意料之外，情理之中。\\n经营场所和客流有了保障后，经营奶茶和烧烤的“90后”摊主杨琦有了更长远打算的底气。\\n“现在有政府托底以后，我们就更有信心去做这件事情了，把它做好，让更多的顾客过来，然后把它当一个事业去干了，我们也想看看我们能不能像一个小的连锁一样，在网上招募一些志同道合的朋友一起把这个事情给做大。”\\n日前，上海发布《关于进一步优化设摊治理 提升城市“烟火气”工作方案》，引发人们对夜集市经济的关注。热气腾腾的夜间消费景象背后，映射出人们的生活需求。但一些曾经爆红的夜集市如今已风光不再，也值得反思。\\n罗店夜集市如何从网红做到长红？\\n“罗店的夜集市与安义夜巷、泗泾夜集市有什么不一样？”在上海师范大学副教授刘德艳看来，绝不能仅仅依赖“烟火气”，挖掘地域特色与文化，打造专属IP才是助推夜集市经济长红的“软实力”。\\n“我觉得，罗店夜集市上要有特别罗店，特别美兰湖的东西，比如说罗店的非遗、罗店当地的民俗、罗店当地的土特产品，把这些东西融合在一起，慢慢形成一个集聚效应，变成除了美食之外的一个综合IP。文化、娱乐、社交等元素都能够有的话，它就变成了罗店镇的一张名片，也就具备了非去不可的理由。”\\n编辑：张思源\\n资料：话匣子\\n*转载请注明来自上海宝山官方微信\\n\\n参考资料[2]:\\nTitle: 上海美食推荐清单|世纪大道地铁口!终于西餐也能吃到过瘾了! \\nURL: https://m.ctrip.com/webapp/you/tripshoot/paipai/detail/detail?articleId=158732057&isHideNavBar=YES&seo=0&twojumpwakeup=0&allianceId=1049189&sid=19855591 \\nContent:\\n加载中\\n\\n参考资料[3]:\\nTitle: 上海热门美食攻略/排行榜单/大全/介绍_必吃特色小吃/菜_推荐/介绍_上海好吃的旅行餐厅【携程美食林】 \\nURL: https://gs.ctrip.com/html5/you/foods/2016305/list-c5000.html \\nContent:\\n忻府区\\uf045\\n\\ue69b\\n旬味会\\n海底捞订座福利\\n旬味会\\n海底捞订座福利\\n热搜：\\n海底捞福利美食试吃官2折美食套餐旬味会美食林榜单\\n海底捞\\n菜系\\n\\uf045\\n筛选\\n\\uf045\\n智能排序\\n\\uf045\\n* 套餐/代金券\\n* 人均100以内大福黑牛料理烤肉·烤鳗鱼\\n5.0分|2条点评|￥102/人\\n烧烤 忻州\\n距市中心4km\\n忻州云中河芳草地房车营地·河畔餐厅\\n\\ue933\\n忻州云中河芳草地房车营地 内餐厅\\n暂无点评，抢首评赢积分\\n山西菜 忻州\\n距市中心8.5km\\n惠538元 5-6人火锅/烧烤食材套餐（二选一）\\n忻州科澜酒店·科澜茶趣\\n\\ue933\\n忻州科澜酒店（忻府区店） 内餐厅\\n暂无点评，抢首评赢积分￥50/人\\n咖啡店 忻州\\n距市中心6.2km\\n王婆大虾(阳曲店)\\n5.0分|1条点评|￥51/人\\n海鲜 阳曲\\n距市中心38.7km\\n呷哺呷哺(大欣城店)\\n\"虾滑很好吃蔓越莓饮料也好喝哦\"\\n4.5分|136条点评|￥57/人\\n火锅 忻州\\n距市中心900m\\n必胜客(长征店)\\n4.3分|116条点评|￥68/人\\n快餐简餐 忻州\\n距市中心1.6km\\n海底捞火锅(开来欣悦店)\\n订\\n暂无点评，抢首评赢积分￥114/人\\n火锅 忻州\\n距市中心3km\\n秀容宴\\n1条点评|￥115/人\\n忻州\\n距市中心2.1km\\n口口香铁锅焖面(总店)\\n5.0分|3条点评|￥43/人\\n山西菜 忻州\\n距市中心2.2km\\n泛华大酒店中餐厅\\n\\ue933\\n忻州泛华大酒店 内餐厅\\n暂无点评，抢首评赢积分\\n山西菜 忻州\\n距市中心2.5km\\n\\n参考资料[4]:\\nTitle: 开市仅一个多月,北上海这个美食夜集市怎么就火了…… \\nURL: https://m.thepaper.cn/newsDetail_forward_31025474 \\nContent:\\n# 开市仅一个多月，北上海这个美食夜集市怎么就火了……\\n2025-06-21 20:33\\n上海\\n位于宝山区罗店镇7号线罗南新村地铁站外的\\n流动摊贩集中规范运营（临时）便民点\\n最近在社交平台上火了起来\\n一百多个摊位\\n汇聚了全国各地的特色小吃\\n成了人们体验城市烟火气的一处网红夜集市\\n点燃城市烟火气的同时\\n如何让城市管理文明有序\\n罗店镇的这一探索\\n试图寻找治理的新路径\\nPART.1\\n晚上7点一开市，设在露天广场上的夜集市迎来当天的首批食客。徐州烧烤、淄博烧烤、阜阳卷馍、东北大饭包、青岛酱蘸鲍鱼、江西炒粉……随着一个个摊位忙碌起来，各种美食的香气随之在空气中弥漫，引得食客胃口大开。\\n“这种烟火气还能找得到，挺好的，以前我们俩都是晚上开车去昆山去吃夜宵。”\\n“我正好地铁从市里下班回来，就顺便过来吃点东西，这里想吃什么都有，而且有统一管理，蛮规范的，吃起来也放心吧。”\\n“这是东北大饭包，你看，白菜叶子里面包了好多料，土豆泥啊什么的，想加什么给你加，好大一只。”\\n自5月初运营以来，夜集市备受周边居民和食客的青睐，晚上7点到次日凌晨2点运营期间，每天平均迎来食客一千多人次，节假日高峰时甚至可达四五千人次。吉林小串的摊主陶晓哲忙个不停。\\n“这里人流量大，而且不用提心吊胆，营业额也挺可观的，原来的话一天营业额在3000~5000元，现在好的时候就1万多。”\\n两年前，为了陪在上海工作的女儿，快50岁的陶晓哲和老公来到罗店，开始摆起了烧烤摊。在老家有过10多年开烧烤店的经验，夫妻俩的小摊很快就吸引了不少食客光顾。但路边摆摊的经历并不好受。\\n“之前就在路边摆摊，一天到晚提心吊胆的，我有5个顾客群，大概2000人左右，我每天出摊的时候就发定位告诉大家，我今天会在哪条路边摆，不时换地方，今天不知道明天咋样……这感觉不好受。现在好了。”\\nPART.2\\n经前期调研发现，在罗店自发形成的占道经营摊点日均达100余处。像陶晓哲这样的流动餐饮摊贩带来的噪声、垃圾、油烟等各种扰民问题，是罗店镇城运中心最常接到的市民投诉。\\n但作为上海北部人口集中导入区域，近年来，美兰湖和罗店大居片区年轻住户和租客人数上升，而他们，正是夜集市消费的主力军。原有配套公建设施已无法满足居民日常需求，特别是受经济大环境影响，小餐饮、便民服务等基础保障方面存在明显缺口。\\n一边是客观存在的消费需求，一边是实实在在的管理问题，如何在二者之间寻求平衡？\\n流动摊贩集中规范运营便民点应运而生。罗店镇城运中心相关负责人说，沪太路杨南路西北侧，原金岷大厦西侧空地这一点位的选择，是经过对周边居民影响最小、交通便利等多方面仔细研判而定。\\n“这个地块离地铁站近，又属于储备地块，就是规土部门交由罗店镇代为管理的，大约4000平方米左右，此前是用于居民非机动车临时停放。地块西边和北边都是储备地块，只有一面靠近居民区，但也有150多米的直线距离，且居民大多是租户，其中，二三十岁的年轻租户达九成以上。”\\n地点确定后，实现有序规范运行，后续管理必须跟上。于是，管理方制定了详细的管理规定和操作规程，明确摊位经营时间、消防安全要求。同时，还统一提供基础水电，消防设施、移动厕所、机动车停放区域，安排多名保洁人员清理现场垃圾、维持场地整洁。\\n现场管理方负责人说，为确保食品安全和消费者权益，对摊贩的入场资格也有严格的筛选机制。\\n“打个比方，烧烤摊必须有油烟净化器，所有的刀具锅铲等需要配备一个索道链，把它们控制在自己的操作范围之内，防止伤害他人。每日的进货单、健康证这是必备的。一些需要冷鲜柜的必须得配备，比如说肉类食品，天慢慢热了之后可能会容易变质。”\\n管理方还设立了退出机制：对违反管理规定的摊主，首次警告并限期整改，二次违规将暂停营业一周，三次违规则取消经营资格。同时，设立线下居民投诉点，及时处理反馈问题，确保居民权益得到有效保障。建立了针对摊主的负面清单制度，记录违规行为，定期公示。\\n通过多维度监管，既满足居民生活需求，又维护市容秩序，实现双赢。\\nPART.3\\n管理规范、公平有序、交通便利、每天最多百余元的管理费、零租金零押金……便民点一开市，就吸引流动摊贩争相报名。进场经营的摊主从最初的130家增加到现在的150家，便民点也在食客的口口相传，在社交平台的助推下，成为“网红夜集市”。\\n“摊主挣得到钱，老百姓觉得这个地方又卫生又规范，也愿意来。再加上管理方日常的规范管理，我认为，这几个方面最终促成了这个点位能够做到良性的循环。”\\n这一切在罗店镇城运中心相关负责人看来，意料之外，情理之中。\\n经营场所和客流有了保障后，经营奶茶和烧烤的“90后”摊主杨琦有了更长远打算的底气。\\n“现在有政府托底以后，我们就更有信心去做这件事情了，把它做好，让更多的顾客过来，然后把它当一个事业去干了，我们也想看看我们能不能像一个小的连锁一样，在网上招募一些志同道合的朋友一起把这个事情给做大。”\\n日前，上海发布《关于进一步优化设摊治理 提升城市“烟火气”工作方案》，引发人们对夜集市经济的关注。热气腾腾的夜间消费景象背后，映射出人们的生活需求。但一些曾经爆红的夜集市如今已风光不再，也值得反思。\\n罗店夜集市如何从网红做到长红？\\n“罗店的夜集市与安义夜巷、泗泾夜集市有什么不一样？”在上海师范大学副教授刘德艳看来，绝不能仅仅依赖“烟火气”，挖掘地域特色与文化，打造专属IP才是助推夜集市经济长红的“软实力”。\\n“我觉得，罗店夜集市上要有特别罗店，特别美兰湖的东西，比如说罗店的非遗、罗店当地的民俗、罗店当地的土特产品，把这些东西融合在一起，慢慢形成一个集聚效应，变成除了美食之外的一个综合IP。文化、娱乐、社交等元素都能够有的话，它就变成了罗店镇的一张名片，也就具备了非去不可的理由。”\\n往期推荐\\n原标题：《开市仅一个多月，北上海这个美食夜集市怎么就火了……》\\n特别声明\\n本文为澎湃号作者或机构在澎湃新闻上传并发布，仅代表该作者或机构观点，不代表澎湃新闻的观点或立场，澎湃新闻仅提供信息发布平台。申请澎湃号请用电脑访问https://renzheng.thepaper.cn。\\n\\n参考资料[5]:\\nTitle: 百科 \\nURL: https://baike.baidu.com/item/上海菜/672867 \\nContent:\\n进入词条全站搜索\\n进入词条全站搜索\\n播报编辑收藏赞\\n登录\\n# 上海菜\\n播报编辑上传视频\\n江南地区传统饮食文化的一种流派\\n许魏洲邀你吃“不要太灵的”上海本帮菜\\n03:35\\n上海传统美食大合集!生煎小笼本帮菜,根本吃不够!\\n17:59\\n吃了30多年红烧肉，只服这种特色做法，色泽红亮肥而不腻，真过瘾\\n10:08\\n上海菜十大名菜有哪些：让你领略上海独特美味！\\n01:49\\n去北京开的上海老饭店吃本帮菜，这几个菜就花了500多，烤菜吃不惯\\n03:41\\n外国丈母娘第一次来中国吃上海本帮菜，被震撼到，中国菜这么好吃\\n08:58\\n上海外滩吃顿本帮菜，不知道正宗不\\n00:18\\n老洋房里快40年的上海菜，很低调，要不是排队谁会以为是饭店\\n02:07\\n开在古镇里面的本帮菜老店\\n01:55\\n老弄堂里30多年上海菜，老板“大半个香港娱乐圈都来过”\\n01:46\\n上海弄堂里的本帮菜馆，店不大生意爆棚，没开门就得排队\\n05:38\\n这才是上海本帮菜的水平，红烧划水太惊艳，小店6张桌座无虚席\\n04:20\\n收藏\\n查看\\n585有用+1\\n102\\n沪菜，即上海菜，是其常用的烹调方法以红烧、煨、糖为主，以浓油赤酱、咸淡适中、保持原味、醇厚鲜美为其特色。 [4]\\n上海菜的别称为本帮菜，是江南地区传统饮食文化的一个重要流派。 自1843年上海开埠以来，随着工商业的发展，四方商贾云集，饭店酒楼应运而生。到20世纪三四十年代，有京、广、苏、扬、锡、甬、杭、闽、川、徽、潮、湘、以及上海本地菜等十六个帮别，同时还有素菜、清真菜，各式西菜、西点。 此时，上海菜原以红烧、生煸见长，后来吸取无锡、苏州、宁波等地方菜的特点，为适应上海人喜食清淡爽口的口味，菜肴渐由原来的重油赤酱趋向淡雅爽口，同时，兼及西菜、西点之法，使花色品种有了很大的发展，拥有鱼下巴甩水、腌川红烧圈子、白斩鸡、松江鲈鱼等名菜。 [4]\\n2014年，上海本帮菜肴传统烹饪技艺被列入国家级非物质文化遗产代表性项目名录。 [5]2018年9月，八宝鸭被评为“中国菜”之上海十大经典名菜。 [6]\\n## 相关星图\\n常见的中国菜系\\n共36个词条23.3万阅读中文名\\n上海菜别 名\\n沪菜、本帮菜 [4]产 地\\n上海 [4]名 菜\\n鱼下巴甩水、腌川红烧圈子、白斩鸡、松江鲈鱼 [4]烹调方法\\n以红烧、煨、糖为主 [4]特 点\\n浓油赤酱、咸淡适中、保持原味、醇厚鲜美 [4]概 念\\n食品，烹饪口 味\\n淡雅爽口 [4]\\n## 目录\\n1. 1烹饪特色\\n2. 2历史发展\\n3. 3经典名菜\\n4. 4著名食馆\\n5. ▪德兴菜馆1. ▪上海美食街\\n2. ▪王宝和酒家\\n3. 5名菜制作\\n4. ▪白斩鸡\\n5. ▪猪油百果松糕\\n6. ▪醉鸡1. ▪葡萄鱼\\n2. ▪河虾争妍\\n3. ▪蟹肉大排翅\\n4. ▪碧玉牛筋\\n5. ▪八宝鸭\\n6. ▪沪江排骨1. ▪鹅肝酱片\\n2. ▪清蒸大闸蟹\\n3. 6沪菜现状## 烹饪特色\\n播报\\n编辑\\n上海菜原以红烧、生煸见长。后来，吸取了无锡、苏州、宁波等地方菜的特点，参照上述十六帮别的烹调技术，兼及西菜、西点之法，使花色品种有了很大的发展。\\n菜肴风味的基本特点：汤卤醇厚，浓油赤酱，糖重色艳，咸淡适口。选料注重活、生、寸、鲜；调味擅长咸、甜、糟、酸。名菜如“红烧蛔鱼”，巧用火候，突出原味，色泽红亮，卤汁浓厚，肉质肥嫩，负有盛誉。\\n“糟钵头”则是上海本地菜善于在烹调中加“糟”的代表，把陈年香糟加工复制成糟卤，在烧制中加入，使菜肴糟香扑鼻，鲜味浓郁。\\n“生煸草头”，摘梗留叶，重油烹酒，柔软鲜嫩，蔚成一格。而各地方风味的菜肴也逐步适应上海的特点，发生了不同的变革，如川菜从重辣转向轻辣，锡菜从重甜改为轻甜，还有不少菜馆吸取外地菜之长。经过长期的实践，在取长补短的基础上。改革了烹调方法，上海菜达到了品种多样，别具一格，形成了上海菜的独特风味。\\n由于上海本地菜（包括苏锡菜）与外地菜长期共存，相互影响，便在原本地菜的基础上，逐渐发展成以上海和苏锡风味为主体并兼有各地风味的上海风味菜体系。\\n上海菜具有许多与众不同的特点：\\n首先讲究选料新鲜。它选用四季时令蔬菜，鱼是以江浙两省产品为主，取活为上，一年四季都有活鱼供客选择，当场活杀烹制。\\n第二菜肴品种多，四季有别。\\n第三讲究烹调方法并不断加以改进。上海菜原来以烧、蒸、煨、窝、炒并重，逐渐转为以烧、生煸、滑炒、蒸为主，其中以生煸、滑炒为最多特别善烹四季河鲜。\\n第四口味也有了很大变化。原来上海菜以浓汤、浓汁厚味为主，后来逐步变为卤汁适中，有清淡素雅，也有浓油赤酱，讲究鲜嫩、色调，鲜咸适口。特别是夏秋季节的糟味菜肴，香味浓郁，颇有特色。\\n如今，上海菜进一步具有选料新鲜、品质优良、刀工精细、制作考究、火候恰当、清淡素雅、咸鲜适中、口味多样、适应面广、风味独特等优点。其主要名菜有“青鱼下巴甩水”、“青鱼秃肺”、“腌川红烧圈子”、“生煸草头”、“白斩鸡”、“鸡骨酱”、“糟钵头”、“虾子大乌参”、“松江鲈鱼”、“枫泾丁蹄”等一二百种菜肴。\\n## 历史发展\\n播报\\n编辑\\n上海在唐代称为海滨渔村，北宋末期(一二九二年)改称上海商埠，此时始对饮食文化的发展有些认识。\\n三十年代，吸纳各地方菜肴风味，尤是苏、杭、浙的风味融合一体系而成为今日上海的主体风味。上海菜的烹饪特点：四季有别，以生煸、滑炒、蒸、煨、炖、红烧……见称，有诗句形容：\\n尖椒笃菜任君尝\\n百店千菜皆于杭\\n浓妆淡抹总相宜\\n自1843年上海开埠以来，随着工商业的发展，四方商贾云集，饭店酒楼应运而生。到本世纪三四十年代，各种地方菜馆林立，有京、广、苏、扬、锡、甬、杭、闽、川、徽、潮、湘、以及上海本地菜等十六个帮别，同时还有素菜、清真菜，各式西菜、西点。这些菜在上海各显神通，激烈竞争，又相互取长补短，融会贯通，这为博采众长，发展有独特风味的上海菜创造了有利条件。\\n## 经典名菜\\n播报\\n编辑\\n中国独特的“沪菜”代表名菜----白斩鸡 [2]、虾子大乌参、青鱼下巴甩水、松江钙鱼、鸡骨酱、桂花肉、八宝鸡、枫泾丁蹄、糟钵头、生煸草头、椒盐蹄胖、五味鸡腿、双包鸭片、卤糟猪脚、肉丝黄豆汤、四鲜白菜墩、蜜枣扒山药、口蘑锅巴汤、炒毛蟹、腌笃鲜 [1]等。\\n此外还有其他美食：上海老大房糕、五芳斋点心、鸽蛋圆子、素菜包、大壶春生煎馒头、蟹壳黄、南翔小笼馒头、四鲜烤麸、四季糕团、桂花糯米糖粥、开洋葱油面、五香豆、奶糖、梨膏糖、红烧生煸滑炒等。\\n## 著名食馆\\n播报\\n编辑\\n### 德兴菜馆\\n该菜馆经营上海菜肴，口味正宗，以汁浓色显、重油、汤鲜、味香著称，开业于1890年前后。德兴菜馆烹制的著名菜肴有虾子大乌参、鸡骨酱、鸡圈肉、八宝辣酱等。其中以虾子大乌参最有特色，是德兴菜馆的代表菜肴，极享盛誉。\\n### 上海美食街\\n该街地处黄浦区中心，位于延安东路，云南南路大世界娱乐中心旁，开设至今已有60余年的历史。上海美食街经营具有上海传统风味小吃、点心和全国各地风味小吃、著名菜肴，并提供优质服务。\\n### 王宝和酒家\\n该酒家是上海最早的酒家之一，创建于1744年。是以专营绍兴陈年黄酒而著称，供应的花雕、太雕、陈加饭、金波等优质黄酒香气浓郁，酒味醇厚。王宝和酒家的菜肴集各帮之长，尤以经营清水大河蟹闻名。该酒家经营的蟹筵，以特、新、优取胜，烹制的蟹菜风味独特，其中“芙蓉蟹粉”、“翡翠虾蟹”、“流黄蟹斗”、“阳澄蟹卷”尤为著名。\\n## 名菜制作\\n播报\\n编辑\\n### 白斩鸡\\n白斩鸡\\n白斩鸡是正牌嫡出的上海菜，上海人过年必定要吃白斩鸡，白斩鸡的烹饪方法是最简单不过的了，不过也考火候。两斤半上下光鸡一只，冷水淹过四分之三鸡身，加葱姜、料酒，大火煮沸，转小火十分钟，中间可翻一面鸡身，熄火后焖十分钟。这两个十分钟，即是要诀。这样焖出来的鸡肉极嫩，鸡腿骨中骨髓仍然鲜红，甚至带有少许血水，就像三分熟的上等牛排，是白斩鸡的最佳状态。 [2-3]\\n焖过十分钟后的鸡仍然很烫，小心捞出，在大量凉水中冲透，使鸡皮紧致，口感韧中带脆，如果任其在鸡汤中泡至冷却，则鸡皮熟烂，口感尽失。这时的鸡余温尚在，还是不能斩切，须谨记所有肉类家禽都不能在它还是热的时候就切，会散碎无法成型。必须俟它彻底冷透，表面略干，才可动刀斩件。白斩鸡蘸清酱油才是至味。 [2]\\n### 猪油百果松糕\\n猪油百果松糕\\n原 料：糯米、粳米、赤豆、莲子、蜜枣、核桃\\n做 法：将糯米、粳米掺合磨干粉，赤豆洗净煮过加白糖，制成干豆沙，另将莲心、核桃仁、蜜枣（会核）均切成小块，拌匀，加白糖和水为干粉吸收，然后在笼屉内侧刷一层芝麻油，把糕粉铺上，再铺于豆沙，最后将莲子、蜜枣、核桃块撒匀放在糕面上，笼屉放锅上，用大火沸水蒸熟，待呈紫色时，再放糖猪油丁、玫瑰花，至糕熟取出即成。 成品糯韧而香。\\n### 醉鸡\\n原 料：鸡腿2只、 绍兴酒2杯\\n调 料：盐1大匙 味精、胡椒粉各少许\\n醉鸡\\n做 法 ：1、鸡腿洗净，用调味料腌2小时，使之入味。\\n2、将鸡腿入蒸具中蒸15分钟，取出入冰水中漂凉（经过冷却，外皮较脆）。\\n3、将鸡腿沥干水分、剁块，加入绍兴酒浸泡一天，即可食用。\\n### 葡萄鱼\\n葡萄鱼\\n原 料：带皮青鱼肉350克,青菜叶4片.鸡蛋一个,咸面包屑75克,葡萄汁100克 做 法：鱼肉治净修成梯形，剞花刀至皮，放调料腌20分钟，粘一层蛋糊，在粘一层面包屑，下7成油锅中炸至金黄色，呈葡萄粒状时捞出装盘； 菜叶焯水修成葡萄叶状，贴在鱼肉旁，用白糖、白醋、盐、葡萄汁勾芡淋上即成。\\n### 河虾争妍\\n原 料：河虾100克，花雕酒\\n调 料：美极酱油、鲜柠檬、话梅、盐、味精、葱姜各适量\\n做 法：适用大小均匀的活鲜河虾剪须洗净，锅置灶上放入适量水，加花雕酒、盐、味精、话梅、葱、姜、美极酱油、鲜柠檬，放入河虾煮熟，冷却后去头壳摆放成形即可。\\n特点：色泽明快，壳脆柔嫩，有酒香味。\\n### 蟹肉大排翅\\n原 料：水发排翅80克，花蟹钳4只，银芽10克，上汤50克，湿生粉10克\\n蟹肉大排翅\\n调 料：大红浙醋50克，盐、味精、鸡粉、葱油、香菜叶少许\\n做 法：1、花蟹钳上笼蒸后，去壳备用；\\n2、水发大排翅上笼蒸10分钟后拿出，加入上汤50克和调味后勾芡，淋上少许葱油；\\n3、盆中摆上蟹钳肉，把芡汁浇在鱼翅上，大红浙醋、香菜、银芽一并跟上即可。\\n特 点：软糯腴润，汤汁鲜美。\\n### 碧玉牛筋\\n原 料：牛筋250克，胡萝卜150克，长白萝卜250克\\n调 料：荷兰芥末、葱、姜、花生酱、味精、生抽、XO酱各适量\\n做 法：新鲜牛筋洗净，加葱、姜、料酒、胡萝卜、长白萝卜煮酥；然后再另起锅放入煮酥的牛筋加调味料、花生酱、生抽、XO酱、荷兰芥末同煮，冷却后切片装盆即可。\\n特点：配色鲜明、协调，味香，有韧性。\\n### 八宝鸭\\n原 料：肥鸭，笋丁，肉丁，火腿丁，栗子丁，鸡肫丁，冬菇丁，莲子，虾米，糯米饭 调 料：绍酒，酱油，白糖，味精，虾仁，湿淀粉，熟青豆，猪油。\\n八宝鸭\\n做 法： 将肥壮嫩鸭宰杀治净，劈开背脊，剪去鸭脚，入沸水锅焯水后捞出洗净，沥干，在鸭身上抹上酱油；将笋丁，肉丁，火腿丁，栗子丁，鸡肫丁，冬菇丁，莲子，虾米，糯米饭放入碗内，加绍酒，酱油，白糖，味精，拌和成馅放入鸭肚内，背朝上放入盛器，上笼蒸三四小时，至鸭肉酥烂时取出翻扣在盘中；炒锅烧热，下猪油，将虾仁滑熟取出，锅内留油少许，放笋片，冬菇片，加酱油少许，蒸鸭原汁适量，烧沸后放虾仁和熟青豆，下湿淀粉少许勾芡，淋上猪油，出锅浇在鸭身上即成。\\n特 点：成菜色泽红润，形状完整，鸭肉酥烂，腴香浓溢，汁浓味鲜。\\n### 沪江排骨\\n原 料：肋排300克\\n调料：生粉，奶粉，吉士粉，食粉，鸡蛋\\n调 料：盐，味精，糖，精制油，黄酒，蒜泥，葱花，干辣椒末。 制 作：将肋排剁成拇指大小方块，漂净血水。把鸡蛋、食粉、水放入排骨里打上劲，然后再放奶粉、士粉、生粉打匀。将油锅烧至五成热左右，倒入排骨，炸至金黄色，倒入笊篱沥干油分，最后放入糖、盐、黄酒、蒜泥、葱花、干辣椒末，翻炒出锅，装盆。 特 点：外脆里嫩，香辣微甜，美味可口。\\n### 鹅肝酱片\\n原 料：鹅肝500克\\n鹅肝酱片\\n调 料：食油，黄油，葱，姜，生抽，味精，胡椒粉，糖，黄酒。 做 法：将鹅肝洗净，再焯水：取一炒锅，锅上火下油，用葱、姜煸炒，再加入上上述调料，放入鹅肝翻炒至熟盛起，等冷却待用。炒好的鹅肝用粉碎机搅成茸，再取模型，两面擦黄油，放入鹅肝茸置冰箱冷藏3～4小时至凝固，改刀装盘即可。 特 点：香糯可口，回味悠长，中西结合，营养丰富。\\n### 清蒸大闸蟹\\n原 料：螃蟹1000克\\n调 料：黄酒15克、姜末30克酱油20克、白糖、味精各少许、麻油 15克、香醋50克\\n清蒸大闸蟹\\n做 法：1.将螃蟹用清水流净，放在盛器里；\\n2.将姜末放在小酒碗内，加熬熟的酱油、白糖、味精、黄酒、麻油搅和；\\n3.另取一小碗，放醋待用；\\n4.将螃蟹上笼用火蒸15-20分钟，至蟹壳呈鲜红色，蟹肉成熟时取出。上桌时随带油调味和醋。\\n特 点：肉嫩鲜美。\\n## 沪菜现状\\n播报\\n编辑\\n所谓的上海菜，内容洋洋大观，举凡大菜、点心、小吃和零食等无所不包，花样又多，常常把外地人搞得一头雾水，不知道自己到底在吃啥。这是因为上海菜其实是杭州、宁波、徽州、扬州、苏州和无锡菜的综合体，四方交集，五味杂陈，早已失去了它的纯粹性的缘故。此外，上海那华洋杂处的环境，使它的西餐水准在中国独占鳌头，已成为上海菜的一部分。几乎每个到上海的人都得到“红房子”瞻仰一番，尝尝它的海派法式西菜。他们的奶油烤蛤蜊、虾二杯、牛尾汤脍炙人口，仍保持着原有的水准。还有“德大西菜社”的德国菜和“天鹅阁面包房”的法式小圆面包，在上海都算是头一份儿。\\n分享你的世界查看更多\\n上海人吃“辣”，鲜过云贵川！\\n这个拥有强大包容性的菜系不断紧跟城市融合的步伐，一如老一辈上海人的款款深情，永远向着那些“雷声大雨点小”，却对家常鲜味大有作为的“上海辣椒”。\\n地道风物地道风物官方账号\\n上海，到底有多“油腻”？\\n都说江南清丽，上海精致，而这些令人垂涎的吃食，竟大多油宽色重、缱绻甜腻，总结起来就是两个字——油、腻。 上海本帮菜呈现出与这里的语言一样鲜明的特点：浓油赤酱，不甜不欢。\\n那一座城\\n分量小？吃不饱？你可能吃了一顿假的上海菜\\n吃惯了生长地的味道，要打开另一个地方的菜系不容易，除了有冒险意愿的味蕾，还和餐馆、点菜、吃饭的人密切相关。\\n地道风物地道风物官方账号\\n参考资料\\n* 1．本地宝．2016-08-31 [引用日期2017-07-7]\\n* 2．经济参考报 [引用日期2025-02-03]\\n* 3．解放日报 [引用日期2025-02-03]\\n* 4．华夏经纬网 [引用日期2025-04-01]\\n* 5．看看新闻 [引用日期2025-04-01]\\n* 6．手机广西网 [引用日期2025-04-01]上海菜的概述图（4张）\\n分享你的世界查看更多\\n上海，到底有多“油腻”？\\n都说江南清丽，上海精致，而这些令人垂涎的吃食，竟大多油宽色重、缱绻甜腻，总结起来就是两个字——油、腻。 上海本帮菜呈现出与这里的语言一样鲜明的特点：浓油赤酱，不甜不欢。\\n修童彤0J7在这里，发现城市的骄傲。\\n分量小？吃不饱？你可能吃了一顿假的上海菜\\n吃惯了生长地的味道，要打开另一个地方的菜系不容易，除了有冒险意愿的味蕾，还和餐馆、点菜、吃饭的人密切相关。\\n地道风物地道风物官方账号\\n上海人吃“辣”，鲜过云贵川！\\n这个拥有强大包容性的菜系不断紧跟城市融合的步伐，一如老一辈上海人的款款深情，永远向着那些“雷声大雨点小”，却对家常鲜味大有作为的“上海辣椒”。\\n地道风物地道风物官方账号\\n上海，到底有多“油腻”？\\n都说江南清丽，上海精致，而这些令人垂涎的吃食，竟大多油宽色重、缱绻甜腻，总结起来就是两个字——油、腻。 上海本帮菜呈现出与这里的语言一样鲜明的特点：浓油赤酱，不甜不欢。\\n修童彤0J7在这里，发现城市的骄傲。\\n分量小？吃不饱？你可能吃了一顿假的上海菜\\n吃惯了生长地的味道，要打开另一个地方的菜系不容易，除了有冒险意愿的味蕾，还和餐馆、点菜、吃饭的人密切相关。\\n地道风物地道风物官方账号\\n词条统计\\n浏览次数：1584553次\\n编辑次数：99次\\n最近更新：（2天前）\\n突出贡献榜\\n上海菜\\n选择朗读音色\\n成熟女声\\n成熟男声\\n磁性男声\\n年轻女声\\n情感男声\\n2x\\n1.5x\\n1.25x\\n1x\\n0.75x\\n0.5x\\n分享到微信朋友圈\\n打开微信“扫一扫”即可将网页分享至朋友圈\\n新手上路\\n我有疑问\\n投诉建议\\n©2025 Baidu | | | |\\n\\n参考资料[6]:\\nTitle: 上海十大必吃美食 \\nURL: https://baijiahao.baidu.com/s?id=1828711351827613727&wfr=spider&for=pc \\nContent:\\nicon_voice_on icon_voice\\n上海十大必吃美食\\n2025-04-07 11:04广东\\n导读\\n•AI导读带你速览精华\\n从非遗南翔小笼的0.1毫米面皮到老正兴油爆虾15秒的舌尖杂技，百年老字号在铜招牌里镌刻城市密码。清晨粢饭团裹着四白三黑的乡愁，深夜油爆虾映着霓虹，每一口都吞吐着中西交融的烟火气。张爱玲笔下的葱油香仍萦绕弄堂，创新芝士拉丝年糕已排起长队，这座城总能在酥脆与软糯间找到味觉的黄金分割。\\n内容由AI智能生成\\n有用\\n上海，这座中西交融的国际都市，每一道美食都是一部鲜活的城市史。从清晨的粢饭团到深夜的油爆虾，十大必吃美味串联起百年风情，让舌尖在时光中穿梭。\\n一、南翔小笼包：舌尖上的非遗传奇\\n作为国家级非遗技艺的承载者，南翔小笼包的历史可追溯至 1871 年。第三代传人李建钢将面皮擀至 0.1 毫米薄度，包入 30 克夹心腿肉与肉皮冻，经 20 分钟蒸制，形成 \"皮薄如纸、汁多如泉\" 的奇观。咬开半透明的外皮，25℃的蟹粉汤汁瞬间在口腔迸发，搭配姜丝香醋，鲜得人眉梢颤动。古猗园分店每日卖出 2 万只，游客为尝现蒸美味甘愿排队 2 小时，连《舌尖上的中国》也用 4 分钟镜头记录这道 \"会跳舞的小笼\"。\\n二、生煎馒头：老城厢的黄金脆响\\n1920 年，实业家黄楚九在萝春阁茶馆创制生煎，从此 \"混水生煎\" 与 \"清水生煎\" 两大流派争奇斗艳。大壶春的半发酵面皮蓬松如棉，25 克剂子包出 12 道褶子，煎至金黄的 \"金脊背\" 酥脆作响，咬开后汤汁如涌泉。小杨生煎则革新为全发酵皮，搭配藤椒大虾等创新馅料，年销量突破 1 亿只。老食客深谙 \"先开窗、后喝汤\" 的门道，就着咖喱牛肉汤，感受 \"底脆、汤鲜、肉嫩\" 的三重奏。\\n三、蟹粉豆腐：金秋限定的味觉盛宴\\n每年 9-11 月，阳澄湖大闸蟹与苏北豆腐碰撞出极致鲜美。老正兴的蟹粉豆腐坚持 \"三现\" 原则：现拆蟹粉、现磨豆浆、现点卤水。将 3 两蟹粉与 2 斤嫩豆腐同煮，撒上白胡椒粉提鲜，蟹黄的橘红与豆腐的雪白交织成 \"金沙埋玉\"。张爱玲在《谈吃》中写道：\"蟹粉豆腐要趁热吃，连汤汁拌饭能吃三碗\"，如今这道菜仍是米其林餐厅的招牌，2023 年入选 \"亚洲 50 大美食\"。\\n四、红烧肉：浓油赤酱的上海灵魂\\n上海红烧肉的奥秘藏在 \"三肥两瘦\" 的肋条肉里。德兴馆采用 \"四步烹调法\"：焯水去腥、冰糖炒色、绍酒焖烧、收汁勾芡，经 4 小时慢炖，肉皮呈琥珀色，入口即化却不失弹性。李伯荣大师改良的 \"毛氏红烧肉\" 加入湘式辣椒，成为国宴佳肴。老克勒们偏爱 \"红烧肉配草头\"，甜糯的肉香与草头的清香在舌尖交响，这道菜年销量超百万份，是上海人年夜饭的 \"镇桌之宝\"。\\n五、排骨年糕：市井烟火的黄金组合\\n1947 年，\"鲜得来\" 首创排骨年糕，将猪大排拍松腌制，与年糕同炸至金黄，浇上甜辣酱汁。排骨外脆里嫩，年糕软糯弹牙，1997 年获 \"中华名小吃\" 称号。如今 \"小常州\" 排骨年糕推出 \"芝士拉丝\" 版本，年轻人排队 2 小时只为尝新。老食客则钟情 \"排骨年糕汤\"，用炸制余油熬出浓白高汤，撒上葱花，堪称 \"上海版味噌汤\"。\\n六、葱油拌面：老弄堂的极简主义\\n看似简单的葱油拌面，实则考验 \"熬油三时辰\" 的功夫。王家沙选用崇明金瓜丝与嘉定小葱，以 2:1 的猪油与菜籽油熬制，葱香渗入每根面条。张爱玲在《公寓生活记趣》中写道：\"葱油的香气是上海的味道\"，如今这道平民美食登上米其林餐盘，改良版加入松露、蟹粉，价格从 5 元飙升至 88 元，却依然供不应求。\\n七、糟钵斗：老上海的味觉密码\\n邵万生的糟钵斗堪称 \"冷菜之王\"，用五年陈糟卤浸泡鸡、虾、毛豆等食材。其糟黄泥螺采用宁波黄泥螺，经 \"三腌三晒\" 工艺，螺肉如水晶般透亮，咸鲜中带着米糟的回甘。夏季冰镇后食用，酒香沁脾，杜月笙曾用糟钵斗宴请黄金荣，赞其 \"比绍兴醉蟹更有腔调\"。2023 年推出的 \"糟香冰淇淋\"，将传统风味与现代甜品结合，成为网红爆款。\\n八、鲜肉月饼：中秋限定的酥皮暴击\\n杏花楼的鲜肉月饼采用 \"水油皮包酥\" 工艺，20 层酥皮包裹 3:7 肥瘦相间的猪肉馅，经 230℃高温烘烤，形成 \"金圈银底\" 的完美品相。每日卖出 15 万只，排队者从福州路延伸至外滩。沈大成的 \"蟹粉鲜肉月饼\" 创新加入 10% 蟹黄，售价 38 元 / 只仍被抢购一空。老食客发明 \"冰火两重天\" 吃法：刚出炉的月饼蘸镇江香醋，酥脆与酸爽在口腔爆炸。\\n九、油爆虾：本帮菜的火与速度\\n老正兴的油爆虾选用 20 只 / 斤的河虾，经 \"一炸二炒三收汁\"，虾壳红亮如玛瑙，虾肉弹牙带甜。李伯荣大师改良的 \"双味油爆虾\"，一半保持原味，一半加入芥末酱，成为 APEC 峰会指定菜品。这道菜对火候要求苛刻，280℃油温下，虾身须在 15 秒内完成变色，堪称 \"舌尖上的杂技\"。\\n十、粢饭团：清晨的能量炸弹\\n老上海的粢饭团讲究 \"四白三黑\"：白糯米、白芝麻、白糖、白油条，搭配黑芝麻、黑洋酥、黑米。阿婆粢饭团坚持用木桶蒸饭，油条现炸现包，裹上肉松、咸蛋黄等馅料，重达 300 克。清晨 6 点，长乐路的摊前已排起长队，上班族捧着 \"巨无霸\" 粢饭团边走边吃，这道传承百年的早餐，年销量突破 500 万只，成为上海人的 \"续命神器\"。\\n寻味地图\\n老字号集聚地：南京东路（沈大成、泰康食品）、福州路（老正兴、杏花楼）、城隍庙（南翔馒头店）\\n夜市必去：黄河路美食街（佳家汤包）、云南南路（鲜得来排骨年糕）\\n创新体验：大董餐厅（分子料理版油爆虾）、南翔小笼文化展（非遗技艺体验）\\n隐藏菜单：老正兴的 \"三丁鲜肉月饼\"、德兴馆的 \"糟香扣肉\"\\n上海的美食密码，藏在百年老字号的铜招牌里，融在浓油赤酱的镬气中，更流淌在弄堂口阿婆的吴侬软语间。从米其林餐厅到街边小摊，每一口都在讲述这座城市的包容与创新，值得用整个旅程去细细品味。\\n作者声明：作品含AI生成内容举报/反馈\\n4.1万获赞 2673粉丝\\n读书、游学、运动，期待和大家每天进步一点\\n## 作者最新文章\\n2小时前4阅读\\n3小时前3阅读\\n7小时前12阅读\\n## 相关推荐\\n换一换\\n* 1热\\n* 2新\\n* 3\\n* 4\\n* 5热\\n* 6热\\n* 7\\n* 8\\n* 9新\\n* 10热分享\\n微信好友\\n新浪微博\\n复制链接\\n扫码分享至微信\\n© Baidu 京ICP证030173号\\n\\n参考资料[7]:\\nTitle: 上海美食地图:十大必吃经典,从弄堂小吃到本帮盛宴! \\nURL: https://baijiahao.baidu.com/s?id=1834297223163493130&wfr=spider&for=pc \\nContent:\\nicon_voice_on icon_voice\\n上海美食地图：十大必吃经典，从弄堂小吃到本帮盛宴！\\n2025-06-08 07:30挪威\\n导读\\n•AI导读带你速览精华\\n上海美食地图：从皮薄馅大的南翔小笼到焦脆底部的生煎包，从浓油赤酱的红烧肉到夏日清爽的糟货，十大必吃经典带你尝遍魔都的浓醇本帮味与市井烟火气。\\n内容由AI智能生成\\n有用\\n上海美食地图：十大必吃经典，从弄堂小吃到本帮盛宴！\\n上海作为中国最具国际化的都市之一，美食融合了本帮菜的浓油赤酱、江南风味的精致以及各地乃至全球的风味。以下是到上海必尝的十大经典美食，既有传统特色，也有市井烟火气。#图文打卡计划#\\n1. 小笼包（南翔小笼）\\n- 特色：皮薄馅大，汤汁饱满，以城隍庙的南翔馒头店最负盛名。\\n- 吃法：先咬破皮吸汤汁，再蘸姜丝醋食用。\\n2. 生煎馒头（生煎包）\\n- 推荐：大壶春（全发酵面皮，酥脆底）和小杨生煎（汤汁多）。\\n- 灵魂：焦脆的底部搭配鲜肉或虾仁馅，趁热吃最佳。\\n3. 葱油拌面\\n- 精髓：熬香的葱油+酱油糖汁，简单却滋味浓郁，老字号\"老地方面馆\"或街头小店都可尝试。\\n4. 红烧肉（本帮特色）\\n- 经典做法：浓油赤酱，肥而不腻，常配卤蛋或百叶结，推荐\"老吉士酒家\"。\\n5. 粢饭团\\n- 市井早餐：糯米包裹油条、肉松、榨菜，咸甜皆宜，南阳路粢饭团是网红款。\\n6. 白斩鸡（小绍兴鸡粥）\\n- 特色：皮黄肉嫩，蘸特制酱油，配鸡粥更地道，\"小绍兴\"是老字号代表。\\n7. 鲜肉月饼\\n- 季节限定：中秋前后最火，酥皮包裹鲜肉馅，光明邨大酒家常年排队。\\n8. 糟货（糟钵头）\\n- 夏日风味：用酒糟卤制的毛豆、门腔（猪舌）、虾等，清凉爽口，老字号\"老人和\"值得一试。\\n9. 油爆虾\\n- 本帮甜口：虾壳酥脆，酱汁甜中带咸，推荐\"上海老饭店\"。\\n10. 咸豆浆+大饼油条\\n- 老上海早餐：咸豆浆加紫菜、虾皮、油条，搭配酥脆大饼，体验本地烟火气。\\n其他推荐：\\n- 蟹粉类：秋季必吃蟹粉捞饭、蟹粉豆腐（如\"成隆行蟹王府\"）。\\n- 草头圈子：酒香草头配肥糯猪大肠，本帮菜经典组合。\\n- 国际范：外滩高端餐厅或法租界Brunch，感受中西融合。\\nTips：\\n- 地道味道往往藏在老字号或弄堂小店，比如\"兰心餐厅\"\"海金滋\"。\\n- 甜口预警：本帮菜偏甜，北方游客可搭配茶水解腻。\\n总结：\\n上海是一座美食天堂，既有浓油赤酱的本帮经典，也有市井烟火气的街头小吃。从汤汁丰盈的小笼包、焦香酥脆的生煎，到甜咸交织的红烧肉、清爽糟货，再到早餐必吃的粢饭团和咸豆浆，每一道都承载着上海独特的风味记忆。本文精选十大必吃美食，带你尝遍老字号与网红店，体验舌尖上的魔都魅力！上海的美食版图既有百年传承，也有创新融合，从路边摊到米其林，总有一种味道能打动你！\\n举报/反馈\\n44.1万获赞 5.3万粉丝\\n用心做美食 用心分享交流\\n美食领域创作者\\n## 作者最新文章\\n17天前52阅读\\n18天前73阅读\\n19天前56阅读\\n## 相关推荐\\n换一换\\n* 1热\\n* 2新\\n* 3\\n* 4\\n* 5热\\n* 6热\\n* 7\\n* 8\\n* 9新\\n* 10热分享\\n微信好友\\n新浪微博\\n复制链接\\n扫码分享至微信\\n© Baidu 京ICP证030173号\\n\\n参考资料[8]:\\nTitle: 上海美食 | 醉庐:环球港里的顶流本帮餐厅 \\nURL: https://new.qq.com/rain/a/20250620A04RMZ00 \\nContent:\\n正在浏览：上海美食 | 醉庐：环球港里的顶流本帮餐厅\\n魏无心\\n搜索\\n客户端\\n无障碍\\n快捷访问\\n+关注\\n评论\\n6\\n复制链接\\n微信好友\\n用微信扫描二维码\\nQQ好友\\n用QQ扫描二维码\\n手机看\\n微信扫一扫，随时随地看\\n# 上海美食 | 醉庐：环球港里的顶流本帮餐厅\\n2025-06-20 12:47发布于上海时尚领域创作者\\n+关注\\n醉庐应该是环球港顶流本帮餐厅了，每次有外地朋友过来上海玩，找我约饭，我都选这里，每次大家都吃得很满意。\\n环境是摩登现代感的老上海风情，服务也很不错。\\n收藏打卡有送冰淇淋或布丁，我们这次总共全家5个人，老人小孩没有APP，3个成人操作了，但还是有送5份哦。\\n而且还加送了花束一样美的杨枝甘露，大家都超开心。\\n点了个双人套餐，实际上足够三个成人吃。另外再加点几样家人爱吃的，人均才70左右，性价比很高。\\n套餐里的青柠海蜇金瓜丝，清新爽脆，很开胃。\\n剁椒鲽鱼可以让店家做成不辣的，小朋友很爱吃，鱼肉嫩嫩的。\\n香酥蹄髈是我超爱的一道，本来我是不爱吃猪蹄这种软软腻腻口感的食物，但是烤蹄髈的外皮超酥脆，一点不腻啦，非常香。配的薯条，又是小朋友最爱。\\n石锅豆腐和蒜香秋葵不用多说了，水准之上。\\n荠菜黄鱼春卷，里面的黄鱼肉很细嫩，也不腥，老爸很喜欢。\\n甜品是红豆沙小圆子，红豆沙很浓稠，小圆子也不少，撒上桂花香香甜甜的。\\n另外加点了小朋友爱吃的炖蛋，还有一道白汤肠肺。肠肺都处理得很干净，加上白胡椒粉，汤味鲜浓。\\n【总结一下】\\n这里是改良的本帮菜，做得比较精致，又不会过于形式化。\\n不论是朋友约饭，还是家人小聚都挺合适的，请客不掉价。\\n套餐的性价比非常高，味道也都没有踩雷。总之，很值得推荐。\\n--------------------------------\\n醉庐\\n地址：上海市中山北路环球港B1层\\n免责声明：本内容来自腾讯平台创作者，不代表腾讯新闻或腾讯网的观点和立场。\\n举报\\n评论 0文明上网理性发言，请遵守《新闻评论服务协议》\\n请先登录后发表评论~\\n加载中...\\n| | | | | | | | | |\\nCopyright © 1998 - 2025 Tencent. All Rights Reserved\\n刷新\\n反馈\\n* 提示当前您处于未登录状态，未登录状态下腾讯广告无法为您在PC网站上提供个性化广告推荐服务，请登录后进行广告设置。广告设置更多\\n顶部\\n\\n\\n\\n请严格遵守以下规则：\\n1. 回答必须结合问题需求和当前时间，对参考资料的可用性进行判断，避免在回答中使用错误或过时的信息。\\n2. 当参考资料中的信息无法准确地回答问题时，你需要在回答中提供获取相应信息的建议，或承认无法提供相应信息。\\n3. 你需要优先根据百度高权威信息、百科、官网、权威机构、专业网站等高权威性来源的信息来回答问题，\\n   但务必不要用“（来源：xx）”这类格式给出来源，\\n   不要暴露来源网站中的“_百度高权威信息”，\\n   也不要出现\\'根据参考资料\\'，\\'根据当前时间\\'等表述。\\n4. 更多地使用参考文章中的相关数字、案例、法律条文、公式等信息，让你的答案更专业。\\n5. 只要使用了参考资料中的任何内容，必须在句末或段末加上资料编号，如 \"[1]\" 或 \"[2][4]\"。不要遗漏编号，也不要随意编造编号。编号必须来源于参考资料中已有的标注。\\n---------\\n下面请结合以上信息，回答问题，补全对话:\\n## 对话\\nuser:\\n你好\\nassistant:\\n你好，我是你的智能搜索助手，请问有什么我可以帮助你的？\\n\\n问题：上海美食攻略\\n\\n直接输出回复内容即可。\\n'}]\n"]}], "source": ["ANSWER_PROMPT = textwrap.dedent(\n", "    \"\"\"\\\n", "    下面你会收到多段参考资料和一个问题。你的任务是阅读参考资料，并根据参考资料中的信息回答对话中的问题。\n", "    以下是当前时间和参考资料：\n", "    ---------\n", "## Current time\n", "    {date}\n", "\n", "## References\n", "    {search_result}\n", "\n", "    请严格遵守以下规则：\n", "    1. 回答必须结合问题需求和当前时间，对参考资料的可用性进行判断，避免在回答中使用错误或过时的信息。\n", "    2. 当参考资料中的信息无法准确地回答问题时，你需要在回答中提供获取相应信息的建议，或承认无法提供相应信息。\n", "    3. 你需要优先根据百度高权威信息、百科、官网、权威机构、专业网站等高权威性来源的信息来回答问题，\n", "       但务必不要用“（来源：xx）”这类格式给出来源，\n", "       不要暴露来源网站中的“_百度高权威信息”，\n", "       也不要出现'根据参考资料'，'根据当前时间'等表述。\n", "    4. 更多地使用参考文章中的相关数字、案例、法律条文、公式等信息，让你的答案更专业。\n", "    5. 只要使用了参考资料中的任何内容，必须在句末或段末加上资料编号，如 \"[1]\" 或 \"[2][4]\"。不要遗漏编号，也不要随意编造编号。编号必须来源于参考资料中已有的标注。\n", "    ---------\n", "    下面请结合以上信息，回答问题，补全对话:\n", "## Conversation\n", "    {context}\n", "    问题：{query}\n", "\n", "    直接输出回复内容即可。\n", "    \"\"\"\n", ")\n", "\n", "query = ANSWER_PROMPT.format(\n", "    date=datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    search_result=complete_search_result,\n", "    context=history,\n", "    query=query\n", ")\n", "web_search_messages = [{\"role\": \"user\", \"content\": query}]\n", "print(web_search_messages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4.3.2. Request Model\n", "When sending a request to the API, the following main parameters need to be considered:\n", "- `messages` (must): List of conversation messages\n", "- `max_tokens` (optional): configuration parameter for maximum number of generated tokens\n", "- `temperature` (optional): configuration parameter for controlling randomness in generated results\n", "- `top_p` (optional): configuration parameter for nucleus sampling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'chatcmpl-ebe32a15-d82a-4153-8a36-888b36f440eb', 'choices': [{'finish_reason': 'stop', 'index': 0, 'logprobs': None, 'message': {'content': '上海作为国际大都市，美食种类繁多，融合了本帮菜的浓油赤酱、江南风味的精致以及各地乃至全球的风味。以下是一份详尽的上海美食攻略：\\n\\n### 一、经典本帮菜\\n\\n1. **小笼包（南翔小笼）**\\n\\n   * 特色：皮薄馅大，汤汁饱满，以城隍庙的南翔馒头店最负盛名。\\n   * 吃法：先咬破皮吸汤汁，再蘸姜丝醋食用。[6][7]\\n\\n2. **生煎馒头（生煎包）**\\n\\n   * 推荐：大壶春（全发酵面皮，酥脆底）和小杨生煎（汤汁多）。\\n   * 灵魂：焦脆的底部搭配鲜肉或虾仁馅，趁热吃最佳。[6][7]\\n\\n3. **葱油拌面**\\n\\n   * 精髓：熬香的葱油+酱油糖汁，简单却滋味浓郁。\\n   * 推荐：老字号“老地方面馆”或街头小店。[6][7]\\n\\n4. **红烧肉（本帮特色）**\\n\\n   * 经典做法：浓油赤酱，肥而不腻，常配卤蛋或百叶结。\\n   * 推荐：“老吉士酒家”。[6][7]\\n\\n5. **粢饭团**\\n\\n   * 市井早餐：糯米包裹油条、肉松、榨菜，咸甜皆宜。\\n   * 推荐：南阳路粢饭团。 [6][7]\\n\\n6. **白斩鸡（小绍兴鸡粥）**\\n\\n   * 特色：皮黄肉嫩，蘸特制酱油，配鸡粥更地道。\\n   * 推荐：“小绍兴”。[6][7]\\n\\n7. **油爆虾**\\n\\n   * 特色：本帮甜口，虾壳酥脆，酱汁甜中带咸。\\n   * 推荐：“上海老饭店”。[6][7]\\n\\n### 二、特色小吃与夜市\\n\\n1. **鲜肉月饼**\\n\\n   * 季节限定：中秋前后最火，酥皮包裹鲜肉馅。\\n   * 推荐：光明邨大酒家。[6][7]\\n\\n2. **糟货（糟钵头）**\\n\\n   * 夏日风味：用酒糟卤制的毛豆、门腔（猪舌）、虾等，清凉爽口。\\n   * 推荐：老字号“老人和”。[6][7]\\n\\n3. **咸豆浆+大饼油条**\\n\\n   * 老上海早餐：咸豆浆加紫菜、虾皮、油条，搭配酥脆大饼，体验本地烟火气。[6][7]\\n\\n4. **夜市推荐**：宝山区罗店镇7号线罗南新村地铁站外的流动摊贩集中规范运营（临时）便民点，汇聚了全国各地的特色小吃，成了人们体验城市烟火气的一处网红夜集市。[1][4]\\n\\n### 三、其他美食推荐\\n\\n1. **醉庐**：环球港顶流本帮餐厅，环境是摩登现代感的老上海风情，服务也很不错，套餐性价比很高。[8]\\n2. **蟹粉类**：秋季必吃蟹粉捞饭、蟹粉豆腐，推荐“成隆行蟹王府”。[7]\\n3. **草头圈子**：酒香草头配肥糯猪大肠，本帮菜经典组合。[7]\\n\\n上海美食既有浓油赤酱的本帮经典，也有市井烟火气的街头小吃，从路边摊到米其林餐厅，总有一种味道能打动你。</s></s>', 'refusal': None, 'role': 'assistant', 'annotations': None, 'audio': None, 'function_call': None, 'tool_calls': None, 'reasoning_content': None}}], 'created': 1750764713, 'model': 'default', 'object': 'chat.completion', 'service_tier': None, 'system_fingerprint': None, 'usage': {'completion_tokens': 821, 'prompt_tokens': 13932, 'total_tokens': 14753, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}}\n"]}], "source": ["client = OpenAI(base_url=ernie_45_url, api_key=model_api_key)\n", "response = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=web_search_messages,\n", "    temperature=1.0,\n", "    max_tokens=2048,\n", "    top_p=0.7\n", ")\n", "response = response.model_dump()\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4.3.3. Model Output\n", "- `content`: Final answer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["上海作为国际大都市，美食种类繁多，融合了本帮菜的浓油赤酱、江南风味的精致以及各地乃至全球的风味。以下是一份详尽的上海美食攻略：\n", "\n", "### 一、经典本帮菜\n", "\n", "1. **小笼包（南翔小笼）**\n", "\n", "   * 特色：皮薄馅大，汤汁饱满，以城隍庙的南翔馒头店最负盛名。\n", "   * 吃法：先咬破皮吸汤汁，再蘸姜丝醋食用。[6][7]\n", "\n", "2. **生煎馒头（生煎包）**\n", "\n", "   * 推荐：大壶春（全发酵面皮，酥脆底）和小杨生煎（汤汁多）。\n", "   * 灵魂：焦脆的底部搭配鲜肉或虾仁馅，趁热吃最佳。[6][7]\n", "\n", "3. **葱油拌面**\n", "\n", "   * 精髓：熬香的葱油+酱油糖汁，简单却滋味浓郁。\n", "   * 推荐：老字号“老地方面馆”或街头小店。[6][7]\n", "\n", "4. **红烧肉（本帮特色）**\n", "\n", "   * 经典做法：浓油赤酱，肥而不腻，常配卤蛋或百叶结。\n", "   * 推荐：“老吉士酒家”。[6][7]\n", "\n", "5. **粢饭团**\n", "\n", "   * 市井早餐：糯米包裹油条、肉松、榨菜，咸甜皆宜。\n", "   * 推荐：南阳路粢饭团。 [6][7]\n", "\n", "6. **白斩鸡（小绍兴鸡粥）**\n", "\n", "   * 特色：皮黄肉嫩，蘸特制酱油，配鸡粥更地道。\n", "   * 推荐：“小绍兴”。[6][7]\n", "\n", "7. **油爆虾**\n", "\n", "   * 特色：本帮甜口，虾壳酥脆，酱汁甜中带咸。\n", "   * 推荐：“上海老饭店”。[6][7]\n", "\n", "### 二、特色小吃与夜市\n", "\n", "1. **鲜肉月饼**\n", "\n", "   * 季节限定：中秋前后最火，酥皮包裹鲜肉馅。\n", "   * 推荐：光明邨大酒家。[6][7]\n", "\n", "2. **糟货（糟钵头）**\n", "\n", "   * 夏日风味：用酒糟卤制的毛豆、门腔（猪舌）、虾等，清凉爽口。\n", "   * 推荐：老字号“老人和”。[6][7]\n", "\n", "3. **咸豆浆+大饼油条**\n", "\n", "   * 老上海早餐：咸豆浆加紫菜、虾皮、油条，搭配酥脆大饼，体验本地烟火气。[6][7]\n", "\n", "4. **夜市推荐**：宝山区罗店镇7号线罗南新村地铁站外的流动摊贩集中规范运营（临时）便民点，汇聚了全国各地的特色小吃，成了人们体验城市烟火气的一处网红夜集市。[1][4]\n", "\n", "### 三、其他美食推荐\n", "\n", "1. **醉庐**：环球港顶流本帮餐厅，环境是摩登现代感的老上海风情，服务也很不错，套餐性价比很高。[8]\n", "2. **蟹粉类**：秋季必吃蟹粉捞饭、蟹粉豆腐，推荐“成隆行蟹王府”。[7]\n", "3. **草头圈子**：酒香草头配肥糯猪大肠，本帮菜经典组合。[7]\n", "\n", "上海美食既有浓油赤酱的本帮经典，也有市井烟火气的街头小吃，从路边摊到米其林餐厅，总有一种味道能打动你。</s></s>\n"]}], "source": ["content = response[\"choices\"][0][\"message\"][\"content\"]\n", "print(content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}
#!/usr/bin/env python3
"""
简化的多卡并行训练脚本 for Wan2.1-I2V-14B-480P 模型
基于原始train.py修改，支持多GPU训练
"""

import torch, os, json
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from diffsynth.trainers.utils import DiffusionTrainingModule, VideoDataset, ModelLogger, launch_training_task, wan_parser
from accelerate import Accelerator
import logging
import time

os.environ["TOKENIZERS_PARALLELISM"] = "false"

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WanTrainingModuleMultiGPU(DiffusionTrainingModule):
    def __init__(
        self,
        model_paths=None, model_id_with_origin_paths=None,
        trainable_models=None,
        lora_base_model=None, lora_target_modules="q,k,v,o,ffn.0,ffn.2", lora_rank=32,
        use_gradient_checkpointing=True,
        use_gradient_checkpointing_offload=False,
        extra_inputs=None,
        accelerator=None,
    ):
        super().__init__()
        self.accelerator = accelerator
        
        # 只在主进程中打印日志，避免重复输出
        if self.accelerator is None or self.accelerator.is_main_process:
            logger.info("Initializing WanTrainingModuleMultiGPU...")
            
        # Load models
        model_configs = []
        if model_paths is not None:
            model_paths = json.loads(model_paths)
            model_configs += [ModelConfig(path=path) for path in model_paths]
        if model_id_with_origin_paths is not None:
            model_id_with_origin_paths = model_id_with_origin_paths.split(",")
            model_configs += [ModelConfig(model_id=i.split(":")[0], origin_file_pattern=i.split(":")[1]) for i in model_id_with_origin_paths]
        
        # 使用CPU初始化模型，避免GPU内存冲突
        if self.accelerator is None or self.accelerator.is_main_process:
            logger.info("Loading models on CPU first to avoid GPU memory conflicts...")
        self.pipe = WanVideoPipeline.from_pretrained(torch_dtype=torch.bfloat16, device="cpu", model_configs=model_configs)
        
        # Reset training scheduler
        self.pipe.scheduler.set_timesteps(1000, training=True)
        
        # Freeze untrainable models
        self.pipe.freeze_except([] if trainable_models is None else trainable_models.split(","))
        
        # Add LoRA to the base models
        if lora_base_model is not None:
            if self.accelerator is None or self.accelerator.is_main_process:
                logger.info(f"Adding LoRA to {lora_base_model}...")
            model = self.add_lora_to_model(
                getattr(self.pipe, lora_base_model),
                target_modules=lora_target_modules.split(","),
                lora_rank=lora_rank
            )
            setattr(self.pipe, lora_base_model, model)
            
        # Store other configs
        self.use_gradient_checkpointing = use_gradient_checkpointing
        self.use_gradient_checkpointing_offload = use_gradient_checkpointing_offload
        self.extra_inputs = extra_inputs.split(",") if extra_inputs is not None else []
        
        if self.accelerator is None or self.accelerator.is_main_process:
            logger.info("WanTrainingModuleMultiGPU initialization completed")
        
    def forward_preprocess(self, data):
        # CFG-sensitive parameters
        inputs_posi = {"prompt": data["prompt"]}
        inputs_nega = {}
        
        # CFG-unsensitive parameters
        inputs_shared = {
            # Assume you are using this pipeline for inference,
            # please fill in the input parameters.
            "input_video": data["video"],
            "height": data["video"][0].size[1],
            "width": data["video"][0].size[0],
            "num_frames": len(data["video"]),
            # Please do not modify the following parameters
            # unless you clearly know what this will cause.
            "cfg_scale": 1,
            "tiled": False,
            "rand_device": self.pipe.device,
            "use_gradient_checkpointing": self.use_gradient_checkpointing,
            "use_gradient_checkpointing_offload": self.use_gradient_checkpointing_offload,
            "cfg_merge": False,
            "vace_scale": 1,
        }
        
        # Extra inputs
        for extra_input in self.extra_inputs:
            if extra_input == "input_image":
                inputs_shared["input_image"] = data["video"][0]
            elif extra_input == "end_image":
                inputs_shared["end_image"] = data["video"][-1]
            else:
                inputs_shared[extra_input] = data[extra_input]
        
        # Pipeline units will automatically process the input parameters.
        for unit in self.pipe.units:
            inputs_shared, inputs_posi, inputs_nega = self.pipe.unit_runner(unit, self.pipe, inputs_shared, inputs_posi, inputs_nega)
        return {**inputs_shared, **inputs_posi}
    
    def forward(self, data, inputs=None):
        if inputs is None: inputs = self.forward_preprocess(data)
        models = {name: getattr(self.pipe, name) for name in self.pipe.in_iteration_models}
        loss = self.pipe.training_loss(**models, **inputs)
        return loss


def main():
    parser = wan_parser()
    # 添加多卡训练相关参数
    parser.add_argument("--mixed_precision", type=str, default="bf16", choices=["no", "fp16", "bf16"], help="Mixed precision training")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility")
    args = parser.parse_args()
    
    # 初始化 Accelerator
    accelerator = Accelerator(
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        mixed_precision=args.mixed_precision,
        log_with="tensorboard",
        project_dir=os.path.join(args.output_path, "logs"),
    )
    
    # 设置随机种子以确保可重现性
    if args.seed is not None:
        torch.manual_seed(args.seed)
        if accelerator.is_main_process:
            logger.info(f"Set random seed to {args.seed}")
    
    # 只在主进程中打印参数信息
    if accelerator.is_main_process:
        logger.info("="*50)
        logger.info("Wan2.1-I2V-14B-480P Multi-GPU Training")
        logger.info("="*50)
        logger.info(f"Training arguments: {args}")
        logger.info(f"Output directory: {args.output_path}")
        logger.info(f"Number of processes: {accelerator.num_processes}")
        logger.info(f"Distributed type: {accelerator.distributed_type}")
        logger.info(f"Mixed precision: {accelerator.mixed_precision}")
        logger.info(f"Device: {accelerator.device}")
        
        # 创建输出目录
        os.makedirs(args.output_path, exist_ok=True)
        
        # 保存训练参数
        with open(os.path.join(args.output_path, "training_args.json"), "w") as f:
            json.dump(vars(args), f, indent=4)
    
    # 确保所有进程同步
    accelerator.wait_for_everyone()
    
    # 加载数据集
    if accelerator.is_main_process:
        logger.info("Loading dataset...")
    dataset = VideoDataset(args=args)
    
    # 初始化模型
    if accelerator.is_main_process:
        logger.info("Initializing model...")
    model = WanTrainingModuleMultiGPU(
        model_paths=args.model_paths,
        model_id_with_origin_paths=args.model_id_with_origin_paths,
        trainable_models=args.trainable_models,
        lora_base_model=args.lora_base_model,
        lora_target_modules=args.lora_target_modules,
        lora_rank=args.lora_rank,
        use_gradient_checkpointing_offload=args.use_gradient_checkpointing_offload,
        extra_inputs=args.extra_inputs,
        accelerator=accelerator,
    )
    
    # 初始化模型日志记录器
    model_logger = ModelLogger(
        args.output_path,
        remove_prefix_in_ckpt=args.remove_prefix_in_ckpt
    )
    
    # 初始化优化器和学习率调度器
    if accelerator.is_main_process:
        logger.info(f"Setting up optimizer with learning rate: {args.learning_rate}")
    optimizer = torch.optim.AdamW(model.trainable_modules(), lr=args.learning_rate)
    scheduler = torch.optim.lr_scheduler.ConstantLR(optimizer)
    
    # 启动训练任务
    if accelerator.is_main_process:
        logger.info(f"Starting training for {args.num_epochs} epochs...")
        start_time = time.time()
    
    # 使用原始的launch_training_task函数，它已经支持accelerator
    launch_training_task(
        dataset, model, model_logger, optimizer, scheduler,
        num_epochs=args.num_epochs,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
    )
    
    # 训练完成
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        total_time = time.time() - start_time
        logger.info("="*50)
        logger.info(f"Training completed in {total_time:.2f} seconds ({total_time/60:.2f} minutes)")
        logger.info(f"Model saved to {args.output_path}")
        logger.info("="*50)


if __name__ == "__main__":
    main()

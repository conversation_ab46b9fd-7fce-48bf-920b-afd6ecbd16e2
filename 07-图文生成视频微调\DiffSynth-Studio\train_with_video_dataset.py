#!/usr/bin/env python3
"""
使用自定义视频数据集进行Wan2.1-I2V-14B-480P微调
基于您成功的训练经验，使用真正的视频数据集
"""

import os
import subprocess
import sys
from pathlib import Path
from datetime import datetime

def setup_environment():
    """设置训练环境"""
    print("🔧 设置视频数据集训练环境...")
    
    # 设置环境变量
    env_vars = {
        "NCCL_TIMEOUT": "1800",
        "TOKENIZERS_PARALLELISM": "false"
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"   设置环境变量: {key}={value}")
    
    # 检查视频数据集
    dataset_dir = Path("data/custom_video_dataset")
    metadata_file = dataset_dir / "metadata.csv"
    videos_dir = dataset_dir / "videos"
    images_dir = dataset_dir / "images"
    
    if not dataset_dir.exists():
        print(f"❌ 自定义视频数据集目录不存在: {dataset_dir}")
        print("   请先运行: python create_video_dataset.py")
        return False
    
    if not metadata_file.exists():
        print(f"❌ 元数据文件不存在: {metadata_file}")
        return False
    
    if not videos_dir.exists() or not images_dir.exists():
        print(f"❌ 视频或图像目录不存在")
        return False
    
    print(f"✅ 自定义视频数据集检查通过: {dataset_dir}")
    
    # 检查视频和图像文件
    video_files = list(videos_dir.glob("*.mp4"))
    image_files = list(images_dir.glob("*.jpg"))
    print(f"✅ 找到 {len(video_files)} 个视频文件")
    print(f"✅ 找到 {len(image_files)} 个图像文件")
    
    # 显示数据集统计
    try:
        import pandas as pd
        df = pd.read_csv(metadata_file)
        print(f"✅ CSV记录: {len(df)} 个样本")
        print(f"   列名: {list(df.columns)}")
    except Exception as e:
        print(f"⚠️  CSV读取警告: {e}")
    
    return True

def run_video_dataset_training():
    """运行视频数据集训练"""
    print("🎬 开始自定义视频数据集训练...")
    print("=" * 60)
    
    # 训练参数
    training_config = {
        "dataset_base_path": "data/custom_video_dataset",
        "dataset_metadata_path": "data/custom_video_dataset/metadata.csv",
        "height": 480,
        "width": 832,
        "dataset_repeat": 30,  # 增加重复次数以充分利用小视频数据集
        "learning_rate": 1e-4,
        "num_epochs": 5,
        "gradient_accumulation_steps": 1,
        "output_path": "./models/train/Wan2.1-I2V-14B-480P_video_lora",
        "lora_rank": 8,
        "mixed_precision": "bf16"
    }
    
    print("📋 视频数据集训练配置:")
    for key, value in training_config.items():
        print(f"   {key}: {value}")
    
    # 构建训练命令（基于您成功的配置）
    train_cmd = f"""
cd /root/sj-tmp/DiffSynth-Studio && \\
source /root/miniconda3/etc/profile.d/conda.sh && \\
conda activate wan_video_env && \\
accelerate launch --config_file accelerate_config.yaml \\
  examples/wanvideo/model_training/train.py \\
  --dataset_base_path {training_config['dataset_base_path']} \\
  --dataset_metadata_path {training_config['dataset_metadata_path']} \\
  --height {training_config['height']} --width {training_config['width']} \\
  --dataset_repeat {training_config['dataset_repeat']} \\
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \\
  --learning_rate {training_config['learning_rate']} \\
  --num_epochs {training_config['num_epochs']} \\
  --gradient_accumulation_steps {training_config['gradient_accumulation_steps']} \\
  --remove_prefix_in_ckpt "pipe.dit." \\
  --output_path "{training_config['output_path']}" \\
  --lora_base_model "dit" \\
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \\
  --lora_rank {training_config['lora_rank']} \\
  --extra_inputs "input_image" \\
  --mixed_precision "{training_config['mixed_precision']}"
"""
    
    print(f"\n🎯 执行视频数据集训练命令...")
    print("   这可能需要较长时间，请耐心等待...")
    
    # 记录开始时间
    start_time = datetime.now()
    print(f"   开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 执行训练
        result = subprocess.run(train_cmd, shell=True, check=True, 
                              capture_output=False, text=True)
        
        # 记录结束时间
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n✅ 自定义视频数据集训练完成!")
        print(f"   结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   训练时长: {duration}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败: {e}")
        return False

def check_video_training_results():
    """检查视频训练结果"""
    print("\n🔍 检查自定义视频训练结果...")
    
    output_dir = Path("./models/train/Wan2.1-I2V-14B-480P_video_lora")
    
    if not output_dir.exists():
        print(f"❌ 训练输出目录不存在: {output_dir}")
        return False
    
    # 检查epoch文件
    epoch_files = list(output_dir.glob("epoch-*.safetensors"))
    
    if epoch_files:
        print(f"✅ 找到 {len(epoch_files)} 个训练检查点:")
        for epoch_file in sorted(epoch_files):
            file_size = epoch_file.stat().st_size / 1024**2
            print(f"   {epoch_file.name} ({file_size:.1f}MB)")
        
        # 找到最新的检查点
        latest_epoch = max(epoch_files, key=lambda x: x.stat().st_mtime)
        print(f"\n🎯 最新检查点: {latest_epoch.name}")
        
        return True
    else:
        print(f"❌ 未找到训练检查点文件")
        return False

def create_video_inference_script():
    """创建视频数据集训练后的推理脚本"""
    print("\n📝 创建视频LoRA推理脚本...")
    
    inference_script = '''#!/usr/bin/env python3
"""
视频数据集训练后的推理脚本
"""

import torch
from PIL import Image
import os
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def main():
    print("🎬 视频LoRA推理")
    print("=" * 50)
    
    # 检查视频LoRA检查点
    lora_dir = "./models/train/Wan2.1-I2V-14B-480P_video_lora"
    available_epochs = []
    
    for i in range(10):
        epoch_file = f"{lora_dir}/epoch-{i}.safetensors"
        if os.path.exists(epoch_file):
            available_epochs.append(i)
    
    if available_epochs:
        latest_epoch = max(available_epochs)
        lora_checkpoint = f"{lora_dir}/epoch-{latest_epoch}.safetensors"
        print(f"✅ 找到视频LoRA检查点: epoch-{latest_epoch}")
        file_size = os.path.getsize(lora_checkpoint) / 1024**2
        print(f"   文件大小: {file_size:.1f}MB")
    else:
        print("❌ 未找到视频LoRA检查点")
        return False
    
    print("\\n📦 初始化Pipeline...")
    
    # 创建pipeline
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cuda",
        model_configs=[
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
        ],
    )
    pipe.enable_vram_management()
    
    print("✅ Pipeline初始化成功")
    
    # 使用视频数据集中的一个图像作为输入
    video_image_path = "data/custom_video_dataset/images/ocean_sunset_000.jpg"
    if os.path.exists(video_image_path):
        image = Image.open(video_image_path)
        print(f"✅ 使用视频数据集图像: {video_image_path}")
    else:
        # 创建默认图像
        image = Image.new('RGB', (832, 480), color=(135, 206, 235))
        print("✅ 使用默认图像")
    
    print("\\n🎬 开始视频LoRA推理...")
    
    # 测试视频数据集场景
    test_prompts = [
        "A beautiful sunset over the ocean with gentle waves, warm golden light reflecting on water, cinematic lighting",
        "A peaceful forest in the morning with sunlight filtering through green leaves, natural lighting, serene atmosphere",
        "Majestic mountains with snow-capped peaks under a clear blue sky, dramatic landscape, high quality"
    ]
    
    for i, prompt in enumerate(test_prompts):
        print(f"\\n--- 测试 {i+1}: {prompt[:50]}... ---")
        
        try:
            video = pipe(
                prompt=prompt,
                negative_prompt="low quality, blurry, static",
                input_image=image,
                seed=42 + i,
                tiled=True,
                height=480,
                width=832,
                num_frames=45,  # 匹配训练数据的帧数
                cfg_scale=7.5,
                num_inference_steps=30
            )
            
            output_path = f"video_lora_test_{i+1}_epoch{latest_epoch}.mp4"
            save_video(video, output_path, fps=15, quality=5)  # 匹配训练数据的fps
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024**2
                print(f"✅ 生成成功: {output_path} ({file_size:.1f}MB)")
            else:
                print(f"❌ 生成失败: {output_path}")
                
        except Exception as e:
            print(f"❌ 推理失败: {e}")
    
    print(f"\\n🎉 视频LoRA推理完成!")

if __name__ == "__main__":
    main()
'''
    
    script_path = "video_lora_inference.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(inference_script)
    
    print(f"✅ 视频LoRA推理脚本已创建: {script_path}")
    return script_path

def main():
    """主函数"""
    print("🎬 Wan2.1-I2V-14B-480P 自定义视频数据集训练")
    print("=" * 60)
    
    # 1. 设置环境
    if not setup_environment():
        print("❌ 环境设置失败")
        sys.exit(1)
    
    # 2. 运行训练
    if not run_video_dataset_training():
        print("❌ 训练失败")
        sys.exit(1)
    
    # 3. 检查结果
    if not check_video_training_results():
        print("❌ 训练结果检查失败")
        sys.exit(1)
    
    # 4. 创建推理脚本
    inference_script = create_video_inference_script()
    
    print(f"\n🎉 自定义视频数据集训练流程完成!")
    print(f"=" * 60)
    print(f"✅ 环境配置完成")
    print(f"✅ 自定义视频数据集训练完成")
    print(f"✅ LoRA权重生成完成")
    print(f"✅ 推理脚本创建完成")
    
    print(f"\n🚀 下一步操作:")
    print(f"   1. 运行视频LoRA推理:")
    print(f"      python {inference_script}")
    print(f"   ")
    print(f"   2. 对比效果:")
    print(f"      - 基础模型推理")
    print(f"      - 原始LoRA推理")
    print(f"      - 视频LoRA推理")
    print(f"   ")
    print(f"   3. 进一步优化:")
    print(f"      - 调整训练参数")
    print(f"      - 增加视频数据")
    print(f"      - 尝试不同的LoRA配置")

if __name__ == "__main__":
    main()

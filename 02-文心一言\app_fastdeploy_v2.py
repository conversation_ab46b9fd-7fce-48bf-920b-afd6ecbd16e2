import gradio as gr
from fastdeploy import LLM, SamplingParams
import logging
import time

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
llm = None
model_loaded = False

def extract_response_text(outputs):
    """从FastDeploy输出中提取文本"""
    if not outputs or len(outputs) == 0:
        return None

    output = outputs[0]

    try:
        # FastDeploy的正确格式: outputs[0].outputs.text (不是列表)
        if hasattr(output, 'outputs') and hasattr(output.outputs, 'text'):
            return output.outputs.text.strip()
    except Exception as e:
        logger.warning(f"方法1失败: {e}")

    try:
        # 备用方法: 直接的 outputs[0].text
        if hasattr(output, 'text'):
            return output.text.strip()
    except Exception as e:
        logger.warning(f"方法2失败: {e}")

    # 如果所有方法都失败，返回调试信息
    logger.error(f"无法提取文本，输出类型: {type(output)}, 属性: {dir(output)}")
    return f"生成完成，但无法解析输出格式。类型: {type(output)}"

def load_model():
    """加载FastDeploy ERNIE模型"""
    global llm, model_loaded
    
    try:
        logger.info("正在加载ERNIE-4.5-0.3B-Paddle模型...")
        start_time = time.time()
        
        llm = LLM(
            model="baidu/ERNIE-4.5-0.3B-Paddle",
            max_model_len=32768
        )
        
        load_time = time.time() - start_time
        model_loaded = True
        
        logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")
        return f"✅ 模型加载成功！\n耗时: {load_time:.2f}秒\n模型: ERNIE-4.5-0.3B-Paddle"
        
    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        model_loaded = False
        return f"❌ 模型加载失败: {str(e)}"

def generate_text(prompt, temperature, top_p, max_tokens):
    """生成文本"""
    global llm, model_loaded
    
    if not model_loaded:
        return "❌ 请先加载模型！"
    
    if not prompt.strip():
        return "❌ 请输入有效的问题！"
    
    try:
        # 设置采样参数
        sampling_params = SamplingParams(
            temperature=temperature,
            top_p=top_p,
            max_tokens=max_tokens
        )
        
        logger.info(f"生成回复中... 参数: temp={temperature}, top_p={top_p}, max_tokens={max_tokens}")
        start_time = time.time()
        
        # 生成回复
        outputs = llm.generate(prompt, sampling_params)
        
        generation_time = time.time() - start_time
        
        # 提取响应文本
        response = extract_response_text(outputs)
        
        if response:
            logger.info(f"生成完成，耗时: {generation_time:.2f}秒")
            return response
        else:
            return "❌ 模型没有生成有效回复"
            
    except Exception as e:
        logger.error(f"生成失败: {str(e)}")
        return f"❌ 生成失败: {str(e)}"

def chat_fn(message, history, temperature, top_p, max_tokens):
    """聊天函数"""
    if not message.strip():
        return history, ""
    
    response = generate_text(message, temperature, top_p, max_tokens)
    history.append([message, response])
    return history, ""

def test_generation():
    """测试生成功能"""
    if not model_loaded:
        return "❌ 请先加载模型！"
    
    test_prompt = "你好"
    result = generate_text(test_prompt, 0.8, 0.95, 50)
    return f"测试结果:\n输入: {test_prompt}\n输出: {result}"

# 创建Gradio界面
with gr.Blocks(
    title="FastDeploy ERNIE 聊天机器人 v2",
    theme=gr.themes.Default()
) as demo:
    
    gr.HTML("""
    <div style="text-align: center; margin-bottom: 20px;">
        <h1>🚀 FastDeploy ERNIE-4.5 聊天机器人 v2</h1>
        <p style="color: #666;">修复输出格式问题的增强版本</p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=3):
            # 模型管理
            with gr.Group():
                gr.Markdown("### 🔧 模型管理")
                with gr.Row():
                    load_btn = gr.Button("🔄 加载模型", variant="primary")
                    test_btn = gr.Button("🧪 测试生成", variant="secondary")
                
                status_text = gr.Textbox(
                    label="状态",
                    value="⏳ 点击'加载模型'开始",
                    interactive=False,
                    lines=4
                )
            
            # 聊天区域
            with gr.Group():
                gr.Markdown("### 💬 智能对话")
                chatbot = gr.Chatbot(
                    label="对话历史",
                    height=400,
                    show_copy_button=True,
                    type="tuples"
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="输入消息",
                        placeholder="请输入您的问题...",
                        lines=2,
                        scale=4
                    )
                    send_btn = gr.Button("📤 发送", variant="primary", scale=1)
                
                clear_btn = gr.Button("🗑️ 清空对话")
        
        with gr.Column(scale=1):
            gr.Markdown("### ⚙️ 生成参数")
            
            temperature = gr.Slider(
                minimum=0.1,
                maximum=2.0,
                value=0.8,
                step=0.1,
                label="🌡️ 温度",
                info="控制生成的随机性"
            )
            
            top_p = gr.Slider(
                minimum=0.1,
                maximum=1.0,
                value=0.95,
                step=0.05,
                label="🎯 Top-p",
                info="控制词汇选择的多样性"
            )
            
            max_tokens = gr.Slider(
                minimum=10,
                maximum=1024,
                value=512,
                step=10,
                label="📏 最大长度",
                info="控制回复的最大字数"
            )
            
            gr.Markdown("""
            ### 📋 版本信息
            - **版本**: v2 (修复输出格式)
            - **模型**: ERNIE-4.5-0.3B-Paddle
            - **框架**: FastDeploy
            
            ### 🔧 新功能
            - 智能输出格式检测
            - 多种文本提取方法
            - 增强的错误处理
            - 生成测试功能
            
            ### 💡 使用建议
            1. 先点击"加载模型"
            2. 可以点击"测试生成"验证
            3. 然后开始正常对话
            """)
    
    # 示例问题
    with gr.Row():
        gr.Examples(
            examples=[
                ["你好，请介绍一下自己"],
                ["写一首关于春天的诗"],
                ["解释什么是人工智能"],
                ["如何学习编程？"],
                ["推荐一本好书"]
            ],
            inputs=msg,
            label="🎯 点击示例快速开始"
        )
    
    # 事件绑定
    load_btn.click(
        fn=load_model,
        outputs=status_text
    )
    
    test_btn.click(
        fn=test_generation,
        outputs=status_text
    )
    
    send_btn.click(
        fn=chat_fn,
        inputs=[msg, chatbot, temperature, top_p, max_tokens],
        outputs=[chatbot, msg]
    )
    
    msg.submit(
        fn=chat_fn,
        inputs=[msg, chatbot, temperature, top_p, max_tokens],
        outputs=[chatbot, msg]
    )
    
    clear_btn.click(
        fn=lambda: [],
        outputs=chatbot
    )

if __name__ == "__main__":
    print("🚀 启动FastDeploy ERNIE聊天机器人 v2...")
    print("📍 访问地址: http://localhost:7860")
    print("🔧 修复了输出格式问题")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )

# Wan2.1-I2V-14B-480P 多卡并行微调详细指南

## 概述

本指南详细介绍如何使用多GPU并行训练来微调 Wan-AI/Wan2.1-I2V-14B-480P 模型。该方案使用 Accelerate 库实现分布式训练，支持混合精度训练，并通过 LoRA (Low-Rank Adaptation) 技术进行高效微调。

## 环境要求

### 硬件要求
- **GPU**: 至少2张GPU，推荐4张或更多GPU（每张GPU至少24GB显存）
- **内存**: 至少64GB系统内存
- **存储**: 至少100GB可用空间用于模型和数据

### 软件要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **Python**: 3.8+
- **CUDA**: 11.8+
- **Conda**: 用于环境管理

## 环境配置

### 1. 创建并激活Conda环境

```bash
# 创建虚拟环境
conda create -n wan_video_env python=3.9 -y

# 激活环境
conda activate wan_video_env
```

### 2. 安装依赖包

```bash
# 安装PyTorch (根据你的CUDA版本调整)
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# 安装其他依赖
pip install accelerate transformers diffusers
pip install peft safetensors
pip install imageio pandas tqdm
pip install tensorboard  # 用于日志记录

# 安装DiffSynth-Studio
pip install -e .
```

### 3. 配置Accelerate

```bash
# 生成accelerate配置文件
accelerate config

# 或者使用预配置的文件
cp accelerate_config.yaml ~/.cache/huggingface/accelerate/default_config.yaml
```

## 数据准备

### 1. 数据集结构

确保你的数据集按以下结构组织：

```
data/example_video_dataset/
├── metadata.csv
├── video1.mp4
├── video2.mp4
└── ...
```

### 2. 元数据格式

`metadata.csv` 文件应包含以下列：
- `video_path`: 视频文件路径
- `prompt`: 对应的文本描述

示例：
```csv
video_path,prompt
video1.mp4,"A beautiful sunset over the ocean"
video2.mp4,"A cat playing with a ball"
```

## 模型配置

### 1. 模型文件组织

为避免模型文件冲突，建议按以下方式组织：

```
models/
├── Wan-AI/
│   └── Wan2.1-I2V-14B-480P/
│       ├── diffusion_pytorch_model*.safetensors
│       ├── models_t5_umt5-xxl-enc-bf16.pth
│       ├── Wan2.1_VAE.pth
│       └── models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth
└── train/
    └── Wan2.1-I2V-14B-480P_lora_multi_gpu/  # 训练输出目录
```

### 2. LoRA配置

本方案使用LoRA进行高效微调：
- **基础模型**: dit (Diffusion Transformer)
- **目标模块**: q,k,v,o,ffn.0,ffn.2
- **LoRA秩**: 32
- **学习率**: 1e-4

## 训练配置

### 1. 多GPU配置 (accelerate_config.yaml)

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 4  # 根据GPU数量调整
rdzv_backend: static
same_network: true
use_cpu: false
```

### 2. 训练参数

| 参数 | 值 | 说明 |
|------|----|----|
| height | 480 | 视频高度 |
| width | 832 | 视频宽度 |
| learning_rate | 1e-4 | 学习率 |
| num_epochs | 2 | 训练轮数 |
| gradient_accumulation_steps | 4 | 梯度累积步数 |
| lora_rank | 32 | LoRA秩 |
| mixed_precision | bf16 | 混合精度训练 |
| seed | 42 | 随机种子 |

## 启动训练

### 1. 使用启动脚本

```bash
# 给脚本执行权限
chmod +x examples/wanvideo/model_training/run_multi_gpu_training.sh

# 启动训练
./examples/wanvideo/model_training/run_multi_gpu_training.sh
```

### 2. 手动启动

```bash
# 激活环境
conda activate wan_video_env

# 启动多卡训练
accelerate launch \
  --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train_multi_gpu.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 480 \
  --width 832 \
  --dataset_repeat 20 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-4 \
  --num_epochs 2 \
  --gradient_accumulation_steps 4 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "./models/train/Wan2.1-I2V-14B-480P_lora_multi_gpu" \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 32 \
  --extra_inputs "input_image" \
  --mixed_precision "bf16" \
  --seed 42 \
  --redirect_common_files False
```

## 监控训练

### 1. 日志监控

训练过程中会生成以下日志：
- **控制台输出**: 实时训练进度和损失
- **TensorBoard日志**: 保存在 `{output_path}/logs/` 目录

查看TensorBoard：
```bash
tensorboard --logdir ./models/train/Wan2.1-I2V-14B-480P_lora_multi_gpu/logs
```

### 2. GPU监控

```bash
# 监控GPU使用情况
watch -n 1 nvidia-smi

# 或使用gpustat
pip install gpustat
gpustat -i 1
```

## 输出文件

训练完成后，输出目录包含：

```
models/train/Wan2.1-I2V-14B-480P_lora_multi_gpu/
├── epoch-0.safetensors      # 第1轮训练的LoRA权重
├── epoch-1.safetensors      # 第2轮训练的LoRA权重
├── training_args.json       # 训练参数记录
└── logs/                    # TensorBoard日志
    └── events.out.tfevents.*
```

## 故障排除

### 1. 常见问题

**问题**: CUDA内存不足
**解决**: 
- 减少 `gradient_accumulation_steps`
- 启用 `use_gradient_checkpointing_offload`
- 减少 `lora_rank`

**问题**: 多GPU同步问题
**解决**:
- 检查网络连接
- 确保所有GPU可见
- 重新配置accelerate

**问题**: 模型加载失败
**解决**:
- 检查模型路径
- 确保模型文件完整
- 检查权限设置

### 2. 性能优化

- **数据加载**: 增加 `num_workers`
- **内存优化**: 启用梯度检查点
- **通信优化**: 使用NCCL后端
- **存储优化**: 使用SSD存储数据

## 模型使用

训练完成后，可以这样加载和使用微调后的模型：

```python
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

# 加载基础模型
pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cuda",
    model_configs=[
        # 基础模型配置...
    ]
)

# 加载LoRA权重
pipe.load_lora("./models/train/Wan2.1-I2V-14B-480P_lora_multi_gpu/epoch-1.safetensors")

# 生成视频
video = pipe(
    prompt="Your prompt here",
    input_image=input_image,
    height=480,
    width=832,
    num_frames=16
)
```

## 注意事项

1. **内存管理**: 确保有足够的GPU内存和系统内存
2. **文件权限**: 确保输出目录有写权限
3. **网络稳定**: 多GPU训练需要稳定的网络连接
4. **备份重要**: 定期备份训练检查点
5. **版本兼容**: 确保所有依赖包版本兼容

## 技术支持

如遇到问题，请检查：
1. 环境配置是否正确
2. 数据格式是否符合要求
3. 硬件资源是否充足
4. 日志文件中的错误信息

更多技术细节请参考 DiffSynth-Studio 官方文档。

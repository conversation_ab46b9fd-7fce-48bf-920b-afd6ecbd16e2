@echo off
echo ========================================
echo FastDeploy ERNIE 环境安装脚本
echo ========================================
echo.

REM 检查Python版本
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 当前Python版本:
python --version
echo.

REM 检查是否有GPU
echo 检测GPU支持...
python -c "import torch; print('CUDA可用:', torch.cuda.is_available())" 2>nul
if errorlevel 1 (
    echo 警告: 无法检测CUDA支持，将安装CPU版本
    set GPU_SUPPORT=false
) else (
    for /f "tokens=*" %%i in ('python -c "import torch; print(torch.cuda.is_available())"') do set GPU_RESULT=%%i
    if "!GPU_RESULT!"=="True" (
        set GPU_SUPPORT=true
        echo ✅ 检测到GPU支持
    ) else (
        set GPU_SUPPORT=false
        echo ℹ️ 未检测到GPU，将安装CPU版本
    )
)
echo.

REM 升级pip
echo 升级pip...
python -m pip install --upgrade pip
echo.

REM 安装基础依赖
echo 安装基础依赖...
pip install gradio torch transformers accelerate
echo.

REM 根据GPU支持情况安装FastDeploy
if "%GPU_SUPPORT%"=="true" (
    echo 安装FastDeploy GPU版本...
    pip install fastdeploy-gpu-python
) else (
    echo 安装FastDeploy CPU版本...
    pip install fastdeploy-python
)

if errorlevel 1 (
    echo.
    echo ❌ FastDeploy安装失败，尝试备选方案...
    echo.
    echo 备选方案1: 手动安装
    echo   pip install fastdeploy-gpu-python  # GPU版本
    echo   pip install fastdeploy-python      # CPU版本
    echo.
    echo 备选方案2: 使用conda安装
    echo   conda install -c paddlepaddle fastdeploy
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.

REM 运行测试
echo 运行环境测试...
python test_fastdeploy.py

if errorlevel 1 (
    echo.
    echo ⚠️ 测试失败，请检查安装
    echo.
    echo 常见问题解决方案:
    echo 1. 网络问题: 使用镜像源
    echo    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple fastdeploy-gpu-python
    echo.
    echo 2. 版本冲突: 创建虚拟环境
    echo    python -m venv venv
    echo    venv\Scripts\activate
    echo    然后重新运行此脚本
    echo.
    echo 3. 权限问题: 以管理员身份运行
    echo.
) else (
    echo.
    echo 🎉 环境配置成功！
    echo.
    echo 现在可以运行应用:
    echo   python app_fastdeploy.py
    echo.
    echo 或者运行测试:
    echo   python test_fastdeploy.py
    echo.
)

pause

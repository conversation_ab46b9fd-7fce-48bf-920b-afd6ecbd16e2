#!/usr/bin/env python3
"""
政府采购网增强爬虫 - 完整字段提取版本
基于实际网页结构优化的爬虫代码
"""

import requests
from bs4 import BeautifulSoup
import time
import logging
import re
from urllib.parse import urljoin, parse_qs, urlparse
from dataclasses import dataclass, asdict
from typing import List, Dict, Optional
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ProcurementData:
    """完整的采购数据结构"""
    # 基本信息
    case_number: str = ""
    case_title: str = ""
    tender_type: str = ""
    announcement_date: str = ""
    
    # 机关信息
    agency_code: str = ""
    agency_name: str = ""
    unit_name: str = ""
    agency_address: str = ""
    contact_person: str = ""
    contact_phone: str = ""
    fax_number: str = ""
    email: str = ""
    
    # 采购资料
    subject_classification: str = ""
    procurement_nature: str = ""
    procurement_amount_range: str = ""
    processing_method: str = ""
    legal_basis: str = ""
    
    # 金额信息
    budget_amount: str = ""
    budget_public: str = ""
    estimated_amount: str = ""
    estimated_public: str = ""
    
    # 招标资料
    tender_method: str = ""
    award_method: str = ""
    announcement_count: str = ""
    tender_status: str = ""
    public_date: str = ""
    is_multiple_award: str = ""
    has_reserve_price: str = ""
    
    # 时间信息
    bid_deadline: str = ""
    opening_time: str = ""
    opening_location: str = ""
    
    # 履约信息
    performance_location: str = ""
    performance_period: str = ""
    
    # 其他信息
    is_sensitive_security: str = ""
    is_national_security: str = ""
    government_subsidy: str = ""
    subsidy_details: str = ""
    
    # 厂商资格
    vendor_qualifications: str = ""
    
    # 原始链接
    detail_url: str = ""

class EnhancedProcurementCrawler:
    """增强版政府采购爬虫"""
    
    def __init__(self):
        self.base_url = "https://web.pcc.gov.tw"
        self.search_url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    def search_procurement_list(self, keyword="", tender_types=None, year_range="114年1至12月", page_size=10, max_pages=3):
        """
        搜索采购信息列表
        
        Args:
            keyword: 搜索关键词
            tender_types: 标案类型列表 ['招標', '決標', '公開閱覽及公開徵求', '政府採購預告']
            year_range: 年份范围
            page_size: 每页数量
            max_pages: 最大页数
        """
        if tender_types is None:
            tender_types = ['招標', '決標']
        
        results = []
        
        try:
            # 构建搜索参数
            search_data = {
                'querySentence': keyword,
                'searchType': 'basic',
                'method': 'search',
                'isSpdt': 'N',
                'pageIndex': '1',
                'pageSize': str(page_size),
                'sortCol': 'TENDER_NOTICE_DATE',
                'timeRange': '114'  # 114年
            }
            
            # 添加标案类型
            if '招標' in tender_types:
                search_data['tenderStatusType'] = '招標'
            if '決標' in tender_types:
                search_data['tenderStatusType'] = '決標'
            
            # 执行搜索
            for page in range(1, max_pages + 1):
                search_data['pageIndex'] = str(page)
                
                logger.info(f"正在抓取第 {page} 页数据...")
                
                # 发送GET请求到搜索结果页面
                search_url_with_params = f"{self.search_url}/readBulletion"
                search_response = self.session.get(self.search_url, params=search_data)
                
                # 解析搜索结果
                page_results = self._parse_search_results(search_response.content)
                
                if not page_results:
                    logger.info(f"第 {page} 页没有更多数据，停止抓取")
                    break
                
                results.extend(page_results)
                
                # 添加延迟避免被封
                time.sleep(2)
                
        except Exception as e:
            logger.error(f"搜索过程中发生错误: {str(e)}")
            
        return results
    
    def _parse_search_results(self, html_content):
        """解析搜索结果页面，获取详情页链接"""
        results = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找所有表格，并根据表头内容判断是否为搜索结果表格
            result_table = None
            tables = soup.find_all("table")
            for table in tables:
                # 检查表格中是否包含“项次”和“机管名称”等关键词，以识别搜索结果表格
                if "項次" in table.get_text() and "機關名稱" in table.get_text():
                    result_table = table
                    break
            
            if not result_table:
                logger.warning("未找到搜索结果表格")
                return results
            
            # 解析每一行数据，跳过表头
            rows = result_table.find_all("tr")[1:]
            for row in rows:
                cells = row.find_all("td")
                if len(cells) >= 6:  # 确保有足够的列
                    try:
                        # 提取基本信息
                        tender_type = cells[1].get_text(strip=True)
                        agency = cells[2].get_text(strip=True)
                        
                        # 提取标案信息和链接
                        case_cell = cells[3]
                        case_link = case_cell.find('a')
                        
                        if case_link:
                            case_title = case_link.get_text(strip=True)
                            original_href = case_link.get("href", "")
                            if "urlSelector" in original_href:
                                parsed_url = urlparse(original_href)
                                pk_value = parse_qs(parsed_url.query).get("pk", [None])[0]
                                if pk_value:
                                    detail_url = f"https://web.pcc.gov.tw/tps/QueryTender/query/searchTenderDetail?pkPmsMain={pk_value}"
                                else:
                                    detail_url = ""
                            else:
                                detail_url = urljoin(self.base_url, original_href)
                        else:
                            case_title = case_cell.get_text(strip=True)
                            detail_url = ""
                        
                        announcement_date = cells[4].get_text(strip=True)
                        
                        result = {
                            'tender_type': tender_type,
                            'agency': agency,
                            'case_title': case_title,
                            'announcement_date': announcement_date,
                            'detail_url': detail_url
                        }
                        
                        results.append(result)
                        
                    except Exception as e:
                        logger.warning(f"解析行数据时出错: {str(e)}")
                        continue
                        
        except Exception as e:
            logger.error(f"解析搜索结果时出错: {str(e)}")
            
        return results
    
    def extract_detail_fields(self, detail_url: str) -> Optional[ProcurementData]:
        """
        提取详情页面的完整字段信息
        
        Args:
            detail_url: 详情页面URL
            
        Returns:
            ProcurementData: 完整的采购详情对象
        """
        try:
            if not detail_url:
                return None
            
            logger.info(f"正在提取详情: {detail_url}")
            
            response = self.session.get(detail_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 初始化数据对象
            data = ProcurementData()
            data.detail_url = detail_url
            
            # 提取所有表格数据
            self._extract_all_fields(soup, data)
            
            return data
            
        except Exception as e:
            logger.error(f"提取详情失败 {detail_url}: {str(e)}")
            return None
    
    def _extract_all_fields(self, soup: BeautifulSoup, data: ProcurementData):
        """提取所有字段信息"""
        try:
            # 查找所有表格
            tables = soup.find_all("table")
            
            for table in tables:
                rows = table.find_all("tr")
                for row in rows:
                    cells = row.find_all(["td", "th"])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        self._map_field_value(label, value, data)
            
            # 提取页面标题中的信息
            title_element = soup.find("title")
            if title_element:
                title_text = title_element.get_text()
                if "招標" in title_text:
                    data.tender_type = "招標"
                elif "決標" in title_text:
                    data.tender_type = "決標"
            
            # 提取公告日期（从页面中查找）
            announcement_date_pattern = r"公告日[：:]\s*(\d{3}/\d{2}/\d{2})"
            page_text = soup.get_text()
            date_match = re.search(announcement_date_pattern, page_text)
            if date_match:
                data.announcement_date = date_match.group(1)
                
        except Exception as e:
            logger.warning(f"提取字段时出错: {str(e)}")
    
    def _map_field_value(self, label: str, value: str, data: ProcurementData):
        """将标签和值映射到数据对象的字段"""
        try:
            # 机关信息
            if '機關代碼' in label:
                data.agency_code = value
            elif '機關名稱' in label:
                data.agency_name = value
            elif '單位名稱' in label:
                data.unit_name = value
            elif '機關地址' in label:
                data.agency_address = value
            elif '聯絡人' in label:
                data.contact_person = value
            elif '聯絡電話' in label:
                data.contact_phone = value
            elif '傳真號碼' in label:
                data.fax_number = value
            elif '電子郵件' in label:
                data.email = value
            
            # 采购资料
            elif '標案案號' in label:
                data.case_number = value
            elif '標案名稱' in label:
                data.case_title = value
            elif '標的分類' in label:
                data.subject_classification = value
            elif '採購性質' in label:
                data.procurement_nature = value
            elif '採購金額級距' in label:
                data.procurement_amount_range = value
            elif '辦理方式' in label:
                data.processing_method = value
            elif '依據法條' in label:
                data.legal_basis = value
            
            # 金额信息
            elif '預算金額' in label and '是否公開' not in label:
                data.budget_amount = value
            elif '預算金額是否公開' in label:
                data.budget_public = value
            elif '預計金額' in label and '是否公開' not in label:
                data.estimated_amount = value
            elif '預計金額是否公開' in label:
                data.estimated_public = value
            
            # 招标资料
            elif '招標方式' in label:
                data.tender_method = value
            elif '決標方式' in label:
                data.award_method = value
            elif '新增公告傳輸次數' in label:
                data.announcement_count = value
            elif '招標狀態' in label:
                data.tender_status = value
            elif '公告日' in label:
                data.public_date = value
            elif '是否複數決標' in label:
                data.is_multiple_award = value
            elif '是否訂有底價' in label:
                data.has_reserve_price = value
            
            # 时间信息
            elif '截止投標' in label:
                data.bid_deadline = value
            elif '開標時間' in label:
                data.opening_time = value
            elif '開標地點' in label:
                data.opening_location = value
            
            # 履约信息
            elif '履約地點' in label:
                data.performance_location = value
            elif '履約期限' in label:
                data.performance_period = value
            
            # 安全相关
            elif '敏感性或國安' in label:
                data.is_sensitive_security = value
            elif '涉及國家安全' in label:
                data.is_national_security = value
            elif '是否受機關補助' in label:
                data.government_subsidy = value
                # 如果有补助详情，也一并提取
                if '補助' in value:
                    data.subsidy_details = value
            
            # 厂商资格
            elif '廠商資格摘要' in label:
                data.vendor_qualifications = value
                
        except Exception as e:
            logger.warning(f"映射字段时出错 {label}: {str(e)}")
    
    def crawl_with_full_details(self, keyword="", tender_types=None, year_range="114年1至12月", page_size=10, max_pages=3):
        """
        爬取数据并获取完整的详细字段信息
        
        Args:
            keyword: 搜索关键词
            tender_types: 标案类型列表
            year_range: 年份范围
            page_size: 每页数量
            max_pages: 最大页数
            
        Returns:
            List[Dict]: 包含完整字段信息的采购数据列表
        """
        # 首先获取搜索结果列表
        search_results = self.search_procurement_list(keyword, tender_types, year_range, page_size, max_pages)
        
        detailed_results = []
        
        # 为每个结果获取详细信息
        for i, result in enumerate(search_results):
            logger.info(f"正在获取第 {i+1}/{len(search_results)} 条详细信息...")
            
            detail = self.extract_detail_fields(result['detail_url'])
            
            if detail:
                # 合并基本信息和详细信息
                detail.case_title = detail.case_title or result['case_title']
                detail.agency_name = detail.agency_name or result['agency']
                detail.tender_type = detail.tender_type or result['tender_type']
                detail.announcement_date = detail.announcement_date or result['announcement_date']
                
                # 转换为字典格式
                detailed_result = asdict(detail)
                detailed_results.append(detailed_result)
            
            # 添加延迟避免被封
            time.sleep(3)
        
        return detailed_results
    
    def save_to_json(self, data: List[Dict], filename: str):
        """保存数据到JSON文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到 {filename}")
        except Exception as e:
            logger.error(f"保存数据失败: {str(e)}")
    
    def save_to_csv(self, data: List[Dict], filename: str):
        """保存数据到CSV文件"""
        try:
            import pandas as pd
            df = pd.DataFrame(data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            logger.info(f"数据已保存到 {filename}")
        except ImportError:
            logger.error("需要安装pandas库才能保存CSV文件: pip install pandas")
        except Exception as e:
            logger.error(f"保存CSV失败: {str(e)}")

def main():
    """主函数 - 演示使用"""
    crawler = EnhancedProcurementCrawler()
    
    # 爬取数据
    print("开始爬取政府采购数据...")
    results = crawler.crawl_with_full_details(
        keyword="餐飲",
        tender_types=['招標'],
        max_pages=2,
        page_size=5
    )
    
    print(f"爬取完成，共获取 {len(results)} 条详细数据")
    
    # 保存数据
    crawler.save_to_json(results, 'procurement_complete_data.json')
    
    # 显示第一条数据的结构
    if results:
        print("\n第一条数据结构:")
        for key, value in results[0].items():
            if value:  # 只显示有值的字段
                print(f"  {key}: {value}")

if __name__ == "__main__":
    main()






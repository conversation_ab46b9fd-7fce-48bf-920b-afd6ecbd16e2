# Wan2.1-I2V-14B-480P 多卡并行微调

## 快速开始

### 1. 环境检查

首先运行环境检查脚本，确保所有依赖都正确安装：

```bash
# 激活conda环境
conda activate wan_video_env

# 运行环境检查
python examples/wanvideo/model_training/check_environment.py
```

### 2. 准备数据

确保你的数据集按以下结构组织：

```
data/example_video_dataset/
├── metadata.csv
├── video1.mp4
├── video2.mp4
└── ...
```

### 3. 启动训练

使用提供的启动脚本：

```bash
# 给脚本执行权限（如果还没有）
chmod +x examples/wanvideo/model_training/run_multi_gpu_training.sh

# 启动多卡训练
./examples/wanvideo/model_training/run_multi_gpu_training.sh
```

## 文件说明

- `train_multi_gpu.py` - 多GPU训练脚本
- `run_multi_gpu_training.sh` - 训练启动脚本
- `check_environment.py` - 环境检查脚本
- `accelerate_config.yaml` - Accelerate配置文件

## 主要改进

### 1. 多卡并行支持
- 使用 Accelerate 库实现分布式训练
- 支持混合精度训练 (bf16)
- 自动GPU负载均衡

### 2. 避免模型文件冲突
- 在CPU上初始化模型
- 使用进程同步机制
- 独立的输出目录

### 3. 完善的日志系统
- 只在主进程输出日志
- TensorBoard集成
- 详细的训练进度监控

### 4. 错误处理和恢复
- 环境检查脚本
- 详细的错误信息
- 训练参数保存

## 配置参数

主要训练参数可以在 `run_multi_gpu_training.sh` 中修改：

```bash
# 数据集配置
DATASET_BASE_PATH="data/example_video_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"

# 模型配置
HEIGHT=480
WIDTH=832
LEARNING_RATE=1e-4
NUM_EPOCHS=2
GRADIENT_ACCUMULATION_STEPS=4

# LoRA配置
LORA_RANK=32
LORA_TARGET_MODULES="q,k,v,o,ffn.0,ffn.2"

# 训练配置
MIXED_PRECISION="bf16"
SEED=42
```

## 监控训练

### 查看实时日志
```bash
# 训练过程中会显示实时日志
tail -f nohup.out  # 如果使用nohup运行
```

### TensorBoard监控
```bash
# 启动TensorBoard
tensorboard --logdir ./models/train/Wan2.1-I2V-14B-480P_lora_multi_gpu/logs

# 在浏览器中访问 http://localhost:6006
```

### GPU监控
```bash
# 实时监控GPU使用情况
watch -n 1 nvidia-smi

# 或使用gpustat
pip install gpustat
gpustat -i 1
```

## 输出文件

训练完成后，在输出目录中会生成：

```
models/train/Wan2.1-I2V-14B-480P_lora_multi_gpu/
├── epoch-0.safetensors      # 第1轮训练的LoRA权重
├── epoch-1.safetensors      # 第2轮训练的LoRA权重
├── training_args.json       # 训练参数记录
└── logs/                    # TensorBoard日志
```

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少 `GRADIENT_ACCUMULATION_STEPS`
   - 降低 `LORA_RANK`
   - 启用梯度检查点卸载

2. **多GPU同步失败**
   - 检查网络连接
   - 重新配置accelerate
   - 确保所有GPU可见

3. **模型下载失败**
   - 检查网络连接
   - 设置HuggingFace镜像
   - 手动下载模型文件

### 性能优化

1. **提高训练速度**
   - 增加GPU数量
   - 使用更快的存储设备
   - 优化数据加载

2. **减少内存使用**
   - 启用梯度检查点
   - 使用混合精度训练
   - 减少批次大小

## 技术支持

如遇到问题，请：

1. 运行环境检查脚本
2. 查看详细的错误日志
3. 检查硬件资源使用情况
4. 参考完整的微调指南文档

更多详细信息请参考：`docs/Wan2.1-I2V-14B-480P_多卡微调指南.md`

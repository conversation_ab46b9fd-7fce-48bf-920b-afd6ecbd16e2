#!/bin/bash
# 多卡并行训练启动脚本

# 设置环境变量
export PYTHONPATH="$PYTHONPATH:$(pwd)"
export TOKENIZERS_PARALLELISM=false

# 激活conda环境
echo "激活 wan_video_env 环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate wan_video_env

# 检查环境是否成功激活
if [ $? -ne 0 ]; then
    echo "错误: 无法激活 wan_video_env 环境，请确保该环境已创建"
    exit 1
fi

# 检查CUDA是否可用
python -c "import torch; print('CUDA可用:',torch.cuda.is_available()); print('GPU数量:',torch.cuda.device_count()); print('GPU型号:',torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'N/A')"

if [ $? -ne 0 ]; then
    echo "错误: 无法检测CUDA环境，请确保CUDA和PyTorch正确安装"
    exit 1
fi

# 创建输出目录
OUTPUT_DIR="./models/train/Wan2.1-I2V-14B-480P_lora_multi_gpu"
mkdir -p $OUTPUT_DIR

# 设置训练参数
DATASET_BASE_PATH="data/example_video_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"
HEIGHT=480
WIDTH=832
DATASET_REPEAT=20
MODEL_ID_WITH_ORIGIN_PATHS="Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth"
LEARNING_RATE=1e-4
NUM_EPOCHS=2
GRADIENT_ACCUMULATION_STEPS=4
REMOVE_PREFIX="pipe.dit."
LORA_BASE_MODEL="dit"
LORA_TARGET_MODULES="q,k,v,o,ffn.0,ffn.2"
LORA_RANK=32
EXTRA_INPUTS="input_image"
MIXED_PRECISION="bf16"
SEED=42

echo "启动多卡并行训练..."
echo "输出目录: $OUTPUT_DIR"

# 使用accelerate启动多卡训练
accelerate launch \
  --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train_multi_gpu.py \
  --dataset_base_path $DATASET_BASE_PATH \
  --dataset_metadata_path $DATASET_METADATA_PATH \
  --height $HEIGHT \
  --width $WIDTH \
  --dataset_repeat $DATASET_REPEAT \
  --model_id_with_origin_paths "$MODEL_ID_WITH_ORIGIN_PATHS" \
  --learning_rate $LEARNING_RATE \
  --num_epochs $NUM_EPOCHS \
  --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
  --remove_prefix_in_ckpt "$REMOVE_PREFIX" \
  --output_path "$OUTPUT_DIR" \
  --lora_base_model "$LORA_BASE_MODEL" \
  --lora_target_modules "$LORA_TARGET_MODULES" \
  --lora_rank $LORA_RANK \
  --extra_inputs "$EXTRA_INPUTS" \
  --mixed_precision "$MIXED_PRECISION" \
  --seed $SEED \
  --redirect_common_files False

# 检查训练是否成功完成
if [ $? -eq 0 ]; then
    echo "训练成功完成!"
    echo "模型保存在: $OUTPUT_DIR"
else
    echo "训练过程中出现错误，请检查日志"
    exit 1
fi

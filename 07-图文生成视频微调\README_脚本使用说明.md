# T2V-1.3B 微调脚本使用说明

## 快速开始

### 1. 数据准备
```bash
# 将视频文件放入 data/example_video_dataset/videos/ 文件夹
# 对于I2V模型，还需要将图像文件放入 data/example_video_dataset/images/ 文件夹

# 生成metadata.csv
python scripts/data_prep/prepare_*_dataset.py
```

### 2. 开始训练
```bash
# LoRA训练
bash scripts/train/train_lora.sh
```

### 3. 推理测试
```bash
# 基础模型推理
python scripts/inference/inference_base.py
```

### 4. 模型合并
```bash
# 合并LoRA权重
python scripts/merge/merge_lora.py \
    --lora_path "./models/train/T2V-1.3B_lora/pytorch_lora_weights.safetensors" \
    --output_path "./models/merged/T2V-1.3B-merged"
```

### 5. 一键执行
```bash
# 执行完整流程
bash scripts/full_pipeline.sh
```

## 脚本说明

- `scripts/data_prep/`: 数据准备脚本
- `scripts/config/`: 配置文件
- `scripts/train/`: 训练脚本
- `scripts/inference/`: 推理脚本
- `scripts/merge/`: 模型合并脚本

## 注意事项

1. 确保已安装DiffSynth-Studio
2. 根据硬件配置调整GPU数量
3. 根据数据集大小调整训练参数

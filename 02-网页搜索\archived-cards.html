<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>归档卡密管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            color: #718096;
            font-size: 1.1rem;
        }

        .back-btn {
            position: absolute;
            top: 30px;
            left: 30px;
            background: #4299e1;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f7fafc;
        }

        .section-header h2 {
            color: #2d3748;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .stat-card h3 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .stat-card p {
            font-size: 1rem;
            opacity: 0.9;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            table-layout: fixed;
        }

        .table th:first-child,
        .table td:first-child {
            width: 35%;
            min-width: 300px;
        }

        .table th {
            background: #f9fafb;
            padding: 16px 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table td {
            padding: 16px 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tbody tr:hover {
            background: #f9fafb;
        }

        .card-key-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-key {
            font-family: 'Monaco', 'Menlo', monospace;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
            flex: 1;
            max-width: 250px;
            overflow-wrap: break-word;
            white-space: normal;
        }

        .btn-copy {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 8px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .btn-copy:hover {
            background: #2563eb;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-used {
            background: #fee2e2;
            color: #dc2626;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #cbd5e0;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .date-filter {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
        }

        .date-filter input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <a href="/admin.html" class="back-btn">
        <i class="fas fa-arrow-left"></i>
        返回管理面板
    </a>

    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-archive"></i>
                归档卡密管理
            </h1>
            <p>查看和管理已归档的卡密记录</p>
        </div>

        <div id="alertContainer"></div>

        <!-- 统计信息 -->
        <div class="section">
            <div class="section-header">
                <h2><i class="fas fa-chart-bar"></i> 归档统计</h2>
            </div>
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <h3 id="totalArchived">0</h3>
                    <p>总归档数</p>
                </div>
                <div class="stat-card">
                    <h3 id="archivedFiles">0</h3>
                    <p>归档文件数</p>
                </div>
            </div>
        </div>

        <!-- 归档文件列表 -->
        <div class="section">
            <div class="section-header">
                <h2><i class="fas fa-folder-open"></i> 归档文件</h2>
                <button class="btn btn-primary" onclick="loadArchivedData()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
            
            <div class="date-filter">
                <label>筛选日期:</label>
                <input type="date" id="startDate" onchange="filterByDate()">
                <span>至</span>
                <input type="date" id="endDate" onchange="filterByDate()">
                <button class="btn btn-secondary" onclick="clearDateFilter()">清除筛选</button>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>卡密</th>
                            <th>天数</th>
                            <th>状态</th>
                            <th>描述</th>
                            <th>创建时间</th>
                            <th>使用者</th>
                            <th>使用时间</th>
                            <th>归档日期</th>
                        </tr>
                    </thead>
                    <tbody id="archivedTableBody">
                        <tr>
                            <td colspan="8" class="loading">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        let archivedData = [];
        let allArchivedData = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadArchivedData();
        });

        // 加载归档数据
        async function loadArchivedData() {
            try {
                const response = await fetch('/api/admin/archived-cards', {
                    credentials: 'include'
                });

                const data = await response.json();
                if (data.success) {
                    allArchivedData = data.archived_cards || [];
                    archivedData = [...allArchivedData];
                    updateStats(data.stats);
                    updateArchivedTable();
                } else {
                    throw new Error(data.error || '获取归档数据失败');
                }
            } catch (error) {
                console.error('加载归档数据失败:', error);
                showError('加载归档数据失败，请稍后重试');
            }
        }

        // 更新统计信息
        function updateStats(stats) {
            document.getElementById('totalArchived').textContent = stats.total_archived || 0;
            document.getElementById('archivedFiles').textContent = stats.archived_files || 0;
        }

        // 更新归档表格
        function updateArchivedTable() {
            const tbody = document.getElementById('archivedTableBody');
            
            if (archivedData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <div>暂无归档数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            const rows = archivedData.map(card => {
                const createdAt = card.created_at ? new Date(card.created_at).toLocaleString('zh-CN') : '-';
                const usedAt = card.used_at ? new Date(card.used_at).toLocaleString('zh-CN') : '-';
                const usedBy = card.used_by || '-';
                const description = card.description || '-';
                const archiveDate = card.archive_date || '-';

                return `
                    <tr>
                        <td>
                            <div class="card-key-container">
                                <span class="card-key">${card.card_key}</span>
                                <button class="btn-copy" onclick="copyCardKey('${card.card_key}')" title="复制卡密">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </td>
                        <td>${card.days}天</td>
                        <td><span class="status-badge status-used">已使用</span></td>
                        <td>${description}</td>
                        <td>${createdAt}</td>
                        <td>${usedBy}</td>
                        <td>${usedAt}</td>
                        <td>${archiveDate}</td>
                    </tr>
                `;
            }).join('');

            tbody.innerHTML = rows;
        }

        // 复制卡密
        function copyCardKey(cardKey) {
            navigator.clipboard.writeText(cardKey).then(() => {
                showSuccess('卡密已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = cardKey;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showSuccess('卡密已复制到剪贴板');
            });
        }

        // 按日期筛选
        function filterByDate() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate && !endDate) {
                archivedData = [...allArchivedData];
            } else {
                archivedData = allArchivedData.filter(card => {
                    if (!card.used_at) return false;

                    const cardDate = new Date(card.used_at).toISOString().split('T')[0];

                    if (startDate && endDate) {
                        return cardDate >= startDate && cardDate <= endDate;
                    } else if (startDate) {
                        return cardDate >= startDate;
                    } else if (endDate) {
                        return cardDate <= endDate;
                    }
                    return true;
                });
            }

            updateArchivedTable();
        }

        // 清除日期筛选
        function clearDateFilter() {
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            archivedData = [...allArchivedData];
            updateArchivedTable();
        }

        // 显示成功消息
        function showSuccess(message) {
            showAlert(message, 'success');
        }

        // 显示错误消息
        function showError(message) {
            showAlert(message, 'error');
        }

        // 显示提示消息
        function showAlert(message, type) {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${message}
            `;

            container.innerHTML = '';
            container.appendChild(alert);
            alert.style.display = 'block';

            setTimeout(() => {
                alert.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>

# DiffSynth-Studio Wan模型微调完整使用指南

## 📚 文档概览

本项目提供了完整的DiffSynth-Studio Wan模型微调文档和脚本，包含以下优化版文档：

### ✅ 已完成的优化版文档

1. **Wan2.1-T2V-1.3B多卡微调文档-完整版-优化版.md**
   - 1.3B轻量级模型，适合资源有限的环境
   - 8GB显存即可训练，速度快8-10倍
   - 包含完整的合并模型推理功能

2. **Wan2.1-T2V-14B多卡微调文档-最新版-优化版.md**
   - 14B大模型，生成质量更高
   - 需要24GB+显存，适合高端硬件
   - 支持DeepSpeed优化

3. **Wan2.1-I2V-14B-480P多卡微调文档-最新版-优化版.md**
   - 图像到视频生成模型
   - 包含CLIP图像编码器配置
   - 支持图像-视频对训练

### 🛠️ 工具脚本

4. **快速脚本生成器.sh**
   - 一键生成所有训练脚本
   - 支持三种模型类型
   - 自动配置参数

5. **文档优化总结.md**
   - 详细的优化说明
   - 使用建议和最佳实践

## 🚀 快速开始

### 方法1：使用快速脚本生成器（推荐）

```bash
# 为T2V-1.3B模型生成脚本（推荐新手）
bash 快速脚本生成器.sh t2v-1.3b

# 为T2V-14B模型生成脚本（高端硬件）
bash 快速脚本生成器.sh t2v-14b

# 为I2V-14B-480P模型生成脚本（图像到视频）
bash 快速脚本生成器.sh i2v-14b-480p
```

生成后的目录结构：
```
scripts/
├── config/accelerate_config.yaml
├── data_prep/prepare_*_dataset.py
├── train/train_lora.sh
├── inference/inference_base.py
├── merge/merge_lora.py
└── full_pipeline.sh
```

### 方法2：手动按文档创建脚本

选择对应的优化版文档，按照其中的vim/nano命令逐个创建脚本：

```bash
# 示例：创建数据准备脚本
vim prepare_t2v_dataset.py
# 复制文档中的脚本内容，保存退出

# 创建训练脚本
vim train_t2v_lora.sh
# 复制文档中的脚本内容，保存退出

# 设置执行权限
chmod +x train_t2v_lora.sh
```

## 📋 完整工作流程

### 1. 环境准备

```bash
# 创建conda环境
conda create -n wan_video python=3.10 -y
conda activate wan_video

# 克隆DiffSynth-Studio
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 安装依赖
pip install -e .
pip install accelerate deepspeed transformers diffusers xformers modelscope opencv-python pillow numpy peft lightning pandas
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 2. 数据准备

```bash
# 创建数据集目录
mkdir -p data/example_video_dataset/{videos,images}

# 将视频文件放入videos文件夹
# 对于I2V模型，还需要将对应的图像文件放入images文件夹

# 生成metadata.csv
python scripts/data_prep/prepare_*_dataset.py
```

### 3. 训练执行

```bash
# 方法1：一键执行完整流程
bash scripts/full_pipeline.sh

# 方法2：分步执行
bash scripts/train/train_lora.sh                    # LoRA训练
python scripts/inference/inference_base.py          # 基础推理
python scripts/merge/merge_lora.py --lora_path ... --output_path ...  # 模型合并
```

### 4. 结果验证

训练完成后会生成：
- `models/train/[MODEL_NAME]_lora/`: LoRA权重文件
- `models/merged/[MODEL_NAME]-merged/`: 合并后的模型
- `output_*.mp4`: 生成的视频文件

## 🎯 模型选择指南

### T2V-1.3B（推荐新手）
- **优势**：训练快、显存需求低、部署简单
- **适用**：资源有限、快速实验、学习研究
- **硬件**：8GB显存，2卡即可
- **速度**：比14B快8-10倍

### T2V-14B（追求质量）
- **优势**：生成质量高、细节丰富
- **适用**：商业应用、高质量生产
- **硬件**：24GB+显存，4卡推荐
- **速度**：较慢但质量更好

### I2V-14B-480P（图像生成）
- **优势**：基于图像生成视频、控制性强
- **适用**：图像动画、条件生成
- **硬件**：24GB+显存，4卡推荐
- **特色**：支持图像条件输入

## 🔧 参数调优建议

### LoRA训练参数

| 模型 | 学习率 | LoRA Rank | GPU数量 | 训练轮数 |
|------|--------|-----------|---------|----------|
| T2V-1.3B | 2e-4 | 64 | 2 | 8 |
| T2V-14B | 1e-4 | 32 | 4 | 5 |
| I2V-14B-480P | 1e-4 | 32 | 4 | 5 |

### 显存优化

```bash
# 降低分辨率
--height 360 --width 640

# 减少LoRA rank
--lora_rank 16

# 启用梯度检查点
--use_gradient_checkpointing

# 使用DeepSpeed（全量训练）
--config_file accelerate_config_deepspeed.yaml
```

## 🐛 常见问题解决

### 1. 显存不足
- 使用T2V-1.3B模型
- 降低训练分辨率
- 减少GPU数量
- 启用梯度检查点

### 2. 训练不稳定
- 降低学习率
- 检查数据质量
- 增加warmup步数
- 使用混合精度训练

### 3. 推理质量差
- 调整推理参数
- 优化提示词
- 检查模型合并
- 验证数据集质量

## 📞 技术支持

### 文档结构
```
├── Wan2.1-T2V-1.3B多卡微调文档-完整版-优化版.md    # 1.3B模型完整文档
├── Wan2.1-T2V-14B多卡微调文档-最新版-优化版.md     # 14B模型文档
├── Wan2.1-I2V-14B-480P多卡微调文档-最新版-优化版.md # I2V模型文档
├── 快速脚本生成器.sh                              # 脚本生成工具
├── 文档优化总结.md                                # 优化说明
└── 完整使用指南.md                                # 本文档
```

### 获取帮助
1. 查看对应的优化版文档
2. 使用快速脚本生成器
3. 检查生成的README_脚本使用说明.md
4. 参考文档优化总结.md

## 🎉 总结

本项目提供了完整的DiffSynth-Studio Wan模型微调解决方案：

1. **✅ 完整的文档**：三个模型的详细微调文档
2. **✅ 自动化脚本**：一键生成所有需要的脚本
3. **✅ vim/nano命令**：每个脚本都提供创建命令
4. **✅ 权限设置**：自动设置脚本执行权限
5. **✅ 错误处理**：包含完整的错误检查机制
6. **✅ 合并推理**：支持模型合并后的推理功能

无论您是初学者还是专业开发者，都可以根据需求选择合适的方案，快速开始Wan模型的微调工作。

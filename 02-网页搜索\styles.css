/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* 登录和注册样式 */
.login-section, .register-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 20px;
}

.login-box, .register-box {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-box h2, .register-box h2 {
    margin-bottom: 30px;
    color: #333;
    font-size: 24px;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.login-btn, .register-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.login-btn:hover, .register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.register-link, .login-link {
    margin-top: 20px;
    color: #666;
}

.register-link a, .login-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.register-link a:hover, .login-link a:hover {
    text-decoration: underline;
}

/* 用户信息样式 */
.user-info {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.user-info span {
    color: #333;
    font-weight: 600;
}

.logout-btn {
    padding: 8px 15px;
    background: #ff6b6b;
    color: white;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: #ff5252;
    transform: translateY(-1px);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.search-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.search-box {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

#questionInput {
    padding: 15px 20px;
    font-size: 1.1rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    outline: none;
    transition: border-color 0.3s;
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
    line-height: 1.5;
}

#questionInput:focus {
    border-color: #667eea;
}

.search-options {
    display: flex;
    gap: 20px;
    align-items: center;
}

.search-options label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.search-options input[type="number"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 80px;
}

.search-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    align-self: flex-start;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.search-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.progress-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-weight: 500;
    color: #555;
    margin-bottom: 10px;
}

.stats {
    font-size: 0.9rem;
    color: #777;
}

.results-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.results-header h2 {
    color: #333;
    margin-bottom: 20px;
}

.results-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 25px;
    font-weight: 500;
}

.article-card {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.article-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.article-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.4;
}

.article-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.article-content {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
}

.article-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.article-link:hover {
    text-decoration: underline;
}

.ai-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #667eea;
    padding: 20px;
    margin: 15px 0;
    border-radius: 8px;
    position: relative;
}

.ai-summary-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    font-weight: 600;
    color: #495057;
}

.ai-summary-header i {
    color: #667eea;
    font-size: 1.1rem;
}

.ai-summary-content {
    color: #495057;
    line-height: 1.6;
    white-space: pre-wrap;
}

.ai-summary-loading {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #6c757d;
    font-style: italic;
}

.ai-summary-loading .spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.ai-summary-error {
    color: #dc3545;
    font-style: italic;
}

.ai-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 10px;
}

.global-summary-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border: 2px solid #667eea;
}

.global-summary-header {
    margin-bottom: 25px;
}

.global-summary-header h2 {
    color: #495057;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.global-summary-header h2 i {
    color: #667eea;
    font-size: 1.3rem;
}

.global-summary-meta {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px;
    border-radius: 8px;
    font-size: 0.95rem;
    color: #495057;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.global-summary-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.global-summary-content {
    background: white;
    padding: 25px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    line-height: 1.7;
    color: #495057;
    white-space: pre-wrap;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.global-summary-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    padding: 40px;
    color: #6c757d;
    font-size: 1.1rem;
}

.global-summary-loading .spinner-large {
    width: 32px;
    height: 32px;
    border: 3px solid #e9ecef;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading {
    text-align: center;
    padding: 50px;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .search-section,
    .results-section {
        padding: 20px;
    }
    
    .search-options {
        flex-direction: column;
        align-items: flex-start;
    }
}

#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P 微调演示脚本
展示多卡并行训练的完整流程
"""

import torch
import os
import json
import logging
import time
from accelerate import Accelerator

# 设置环境变量
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_multi_gpu_training():
    """演示多GPU训练流程"""
    
    print("🚀 Wan2.1-I2V-14B-480P 多卡并行微调演示")
    print("="*60)
    
    # 1. 检查GPU环境
    print("1. 检查GPU环境...")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    print(f"   GPU数量: {torch.cuda.device_count()}")
    
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
    
    # 2. 初始化Accelerator
    print("\n2. 初始化Accelerator...")
    try:
        accelerator = Accelerator(
            gradient_accumulation_steps=2,
            mixed_precision="bf16",
            log_with="tensorboard",
            project_dir="./models/train/demo_logs",
        )
        print(f"   ✓ 分布式类型: {accelerator.distributed_type}")
        print(f"   ✓ 进程数量: {accelerator.num_processes}")
        print(f"   ✓ 混合精度: {accelerator.mixed_precision}")
        print(f"   ✓ 设备: {accelerator.device}")
    except Exception as e:
        print(f"   ✗ Accelerator初始化失败: {e}")
        return False
    
    # 3. 模拟模型加载
    print("\n3. 模拟模型加载...")
    try:
        # 创建一个简单的模型来演示
        class DemoModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = torch.nn.Linear(1024, 512)
                self.output = torch.nn.Linear(512, 1)
                
            def forward(self, x):
                x = self.linear(x)
                x = torch.relu(x)
                return self.output(x)
        
        model = DemoModel()
        print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 创建优化器
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
        
        # 创建虚拟数据
        dataset_size = 10
        batch_size = 2
        
        print(f"   ✓ 数据集大小: {dataset_size}")
        print(f"   ✓ 批次大小: {batch_size}")
        
    except Exception as e:
        print(f"   ✗ 模型初始化失败: {e}")
        return False
    
    # 4. 准备训练组件
    print("\n4. 准备训练组件...")
    try:
        # 创建虚拟数据加载器
        class DemoDataset(torch.utils.data.Dataset):
            def __init__(self, size):
                self.size = size
                
            def __len__(self):
                return self.size
                
            def __getitem__(self, idx):
                return {
                    'input': torch.randn(1024),
                    'target': torch.randn(1)
                }
        
        dataset = DemoDataset(dataset_size)
        dataloader = torch.utils.data.DataLoader(
            dataset, 
            batch_size=batch_size, 
            shuffle=True
        )
        
        # 使用accelerator准备组件
        model, optimizer, dataloader = accelerator.prepare(
            model, optimizer, dataloader
        )
        
        print(f"   ✓ 数据加载器准备完成")
        print(f"   ✓ 模型和优化器准备完成")
        
    except Exception as e:
        print(f"   ✗ 训练组件准备失败: {e}")
        return False
    
    # 5. 执行训练演示
    print("\n5. 执行训练演示...")
    try:
        num_epochs = 2
        total_steps = 0
        
        if accelerator.is_main_process:
            print(f"   开始训练 {num_epochs} 个epoch...")
        
        start_time = time.time()
        
        for epoch in range(num_epochs):
            if accelerator.is_main_process:
                print(f"   Epoch {epoch + 1}/{num_epochs}")
            
            epoch_loss = 0.0
            num_batches = 0
            
            for batch_idx, batch in enumerate(dataloader):
                with accelerator.accumulate(model):
                    # 前向传播
                    outputs = model(batch['input'])
                    loss = torch.nn.functional.mse_loss(outputs, batch['target'])
                    
                    # 反向传播
                    optimizer.zero_grad()
                    accelerator.backward(loss)
                    optimizer.step()
                    
                    epoch_loss += loss.item()
                    num_batches += 1
                    total_steps += 1
                    
                    if accelerator.is_main_process and batch_idx % 2 == 0:
                        print(f"     Step {total_steps}, Loss: {loss.item():.6f}")
            
            avg_loss = epoch_loss / num_batches
            if accelerator.is_main_process:
                print(f"   Epoch {epoch + 1} 平均损失: {avg_loss:.6f}")
        
        training_time = time.time() - start_time
        
        if accelerator.is_main_process:
            print(f"   ✓ 训练完成，耗时: {training_time:.2f}秒")
            print(f"   ✓ 总步数: {total_steps}")
        
    except Exception as e:
        print(f"   ✗ 训练执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 6. 保存模型演示
    print("\n6. 保存模型演示...")
    try:
        output_dir = "./models/train/demo_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 等待所有进程完成
        accelerator.wait_for_everyone()
        
        if accelerator.is_main_process:
            # 保存模型状态
            unwrapped_model = accelerator.unwrap_model(model)
            torch.save(unwrapped_model.state_dict(), f"{output_dir}/demo_model.pth")
            
            # 保存训练信息
            training_info = {
                "num_epochs": num_epochs,
                "total_steps": total_steps,
                "training_time": training_time,
                "final_loss": avg_loss,
                "num_processes": accelerator.num_processes,
                "mixed_precision": str(accelerator.mixed_precision),
            }
            
            with open(f"{output_dir}/training_info.json", "w") as f:
                json.dump(training_info, f, indent=2)
            
            print(f"   ✓ 模型保存到: {output_dir}/demo_model.pth")
            print(f"   ✓ 训练信息保存到: {output_dir}/training_info.json")
        
    except Exception as e:
        print(f"   ✗ 模型保存失败: {e}")
        return False
    
    # 7. 总结
    print("\n" + "="*60)
    print("🎉 多卡并行微调演示完成!")
    print("="*60)
    
    if accelerator.is_main_process:
        print("训练总结:")
        print(f"  • 使用GPU数量: {accelerator.num_processes}")
        print(f"  • 混合精度: {accelerator.mixed_precision}")
        print(f"  • 训练轮数: {num_epochs}")
        print(f"  • 总训练步数: {total_steps}")
        print(f"  • 训练时间: {training_time:.2f}秒")
        print(f"  • 最终损失: {avg_loss:.6f}")
        print(f"  • 输出目录: {output_dir}")
        
        print("\n多卡训练优势:")
        print(f"  • 并行加速: {accelerator.num_processes}x GPU并行")
        print(f"  • 内存优化: bf16混合精度训练")
        print(f"  • 自动同步: 分布式训练自动同步")
        print(f"  • 易于扩展: 支持更多GPU和节点")
    
    return True

def main():
    """主函数"""
    success = demo_multi_gpu_training()
    
    if success:
        print("\n✅ 演示成功完成!")
        print("现在您可以使用相同的方法训练真实的Wan2.1-I2V-14B-480P模型")
    else:
        print("\n❌ 演示失败!")
        print("请检查环境配置和错误信息")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)

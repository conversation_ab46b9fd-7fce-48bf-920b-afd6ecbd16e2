# 台湾政府采购网爬虫开发对话记录

## 📅 时间：2025年7月15日
## 👥 参与者：用户 & Augment Agent

---

## 🎯 项目概述

本次对话记录了完整的台湾政府采购网爬虫系统开发过程，从初始需求到最终实现，包括数据爬取、清理、字段筛选和繁简转换等功能。

---

## 📋 对话流程总结

### 1. 初始需求 (21:58)
**用户**: 执行爬虫文件，系统代理是127.0.0.1:7890，还有就是使用中文回答问题

**AI回应**: 
- 查看了现有的爬虫文件 `code/ultimate_perfect_crawler.py`
- 设置系统代理 127.0.0.1:7890
- 成功执行爬虫，爬取了50条数据
- 生成了CSV、JSON和数据库文件

**结果**: 初始爬虫成功运行，但爬取的是网站导航信息，不是真正的决标数据

### 2. 需求明确 (22:18)
**用户**: 爬取的网页"https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E6%B1%BA%E6%A8%99&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10"

先爬取单行的数据，再点击"检视"进去爬取详细数据

**AI回应**:
- 分析了用户的具体需求：爬取决标公告列表 → 点击检视 → 获取详细页面数据
- 修改了爬虫配置，针对决标公告页面
- 重写了解析器来处理决标公告的数据结构
- 添加了详细页面爬取功能

**关键改进**:
```python
# 修改了目标URL和参数
search_params={
    'querySentence': '国防部',
    'tenderStatusType': '决标', 
    'sortCol': 'AWARD_NOTICE_DATE',
    'timeRange': '111',
    'pron': 'true',
    'fuzzy': 'true',
    'pageSize': '10'
}
```

### 3. 详细页面解析 (22:25)
**用户**: 提供了详细页面的完整数据结构示例

**AI回应**:
- 根据用户提供的详细页面结构，重写了 `parse_detail_page` 方法
- 实现了机关资料、已公告资料、投标厂商、决标品项、决标资料的提取
- 使用正则表达式精确提取各个字段
- 更新了数据库结构以支持新的字段

**核心功能**:
```python
def _extract_agency_info(self, text: str, data: Dict):
    """提取机关资料"""
def _extract_announcement_info(self, text: str, data: Dict):
    """提取已公告资料"""  
def _extract_bidder_info(self, text: str, data: Dict):
    """提取投标厂商信息"""
```

### 4. 数据清理和字段筛选 (22:39)
**用户**: 根据PDF文档进行字段筛选

**AI回应**:
- 发现category字段包含了大量导航菜单信息
- 创建了数据清理脚本 `code/clean_data.py`
- 创建了字段筛选脚本 `code/filter_fields.py`
- 定义了23个标准字段，按重要性排序
- 生成了中文字段名的标准化数据

**标准字段**:
- 基本信息：标案案号、标案名称、机关名称等
- 日期信息：招标公告日期、决标公告日期、截止投标日期等
- 金额信息：预算金额、决标金额、总决标金额等
- 履约信息：履约地点、履约期间、契约编号等

### 5. 繁简转换需求 (22:57)
**用户**: 使用新字段爬取数据进行保存为json，字段和内容都要使用简体中文，还是两份数据，一份原始，一份简体中文

**AI回应**:
- 安装了 `opencc-python-reimplemented` 库进行繁简转换
- 创建了增强版数据处理器 `code/enhanced_crawler_with_chinese.py`
- 实现了45个标准字段的映射
- 生成两个版本的数据：
  - 繁体中文原始数据 (traditional)
  - 简体中文数据 (simplified)

**技术实现**:
```python
import opencc
converter = opencc.OpenCC('t2s')

def convert_to_simplified(text):
    """将繁体中文转换为简体中文"""
    return converter.convert(str(text))
```

---

## 🎯 最终成果

### 生成的文件结构
```
data/
├── processed/
│   ├── taiwan_procurement_traditional_20250715_225823.json  # 繁体原始数据
│   ├── taiwan_procurement_traditional_20250715_225823.csv
│   ├── taiwan_procurement_simplified_20250715_225823.json   # 简体中文数据
│   ├── taiwan_procurement_simplified_20250715_225823.csv
│   └── 字段说明.md                                        # 字段说明文档
├── csv/
│   └── ultimate_crawler_data_*_cleaned_filtered.csv        # 清理筛选后数据
├── json/
│   └── ultimate_crawler_data_*_cleaned_filtered.json
└── database/
    └── ultimate_crawler.db                                 # SQLite数据库
```

### 核心脚本文件
```
code/
├── ultimate_perfect_crawler.py          # 主爬虫程序
├── clean_data.py                        # 数据清理脚本
├── filter_fields.py                     # 字段筛选脚本
└── enhanced_crawler_with_chinese.py     # 繁简转换处理器
```

### 数据对比示例
**繁体中文原始数据**:
```json
{
  "标案名称": "太平營區新建工程",
  "机关名称": "國防部",
  "招标方式": "公開招標",
  "履约地点": "臺東縣卑南鄉(原住民地區)"
}
```

**简体中文数据**:
```json
{
  "标案名称": "太平营区新建工程", 
  "机关名称": "国防部",
  "招标方式": "公开招标",
  "履约地点": "台东县卑南乡(原住民地区)"
}
```

---

## 🔧 技术特点

1. **完整的爬取流程**: 列表页 → 详细页 → 数据提取
2. **智能数据清理**: 自动识别和清除导航菜单信息
3. **标准化字段**: 45个标准字段，涵盖政府采购全流程
4. **繁简转换**: 使用OpenCC库进行准确转换
5. **多格式输出**: JSON、CSV、SQLite数据库
6. **代理支持**: 支持系统代理127.0.0.1:7890
7. **容错机制**: Selenium失败时自动切换requests
8. **中文友好**: 全程中文交互和文档

---

## 📊 最终数据统计

- **爬取记录**: 10条完整的政府采购决标公告
- **字段数量**: 45个标准化字段
- **数据版本**: 繁体中文原始版 + 简体中文版
- **文件格式**: JSON + CSV (UTF-8-BOM编码)
- **数据来源**: 台湾政府电子采购网 (https://web.pcc.gov.tw)
- **爬取时间**: 2025-07-15 22:58:23

---

## 💡 开发亮点

1. **需求理解准确**: 从模糊需求到精确实现
2. **迭代开发**: 根据用户反馈不断优化
3. **数据质量**: 从原始HTML到结构化数据
4. **用户体验**: 中文交互，易于理解
5. **技术深度**: 涵盖爬虫、数据处理、NLP等多个领域
6. **实用性强**: 生成的数据可直接用于分析和应用

---

## 🎉 项目总结

本次对话成功完成了一个完整的台湾政府采购网爬虫系统开发，从需求分析到最终实现，展现了AI辅助开发的完整流程。系统不仅实现了数据爬取功能，还包含了数据清理、字段标准化、繁简转换等高级功能，为用户提供了高质量的结构化数据。

**项目价值**:
- 为政府采购数据分析提供了可靠的数据源
- 支持繁体和简体中文两种版本，适应不同用户需求  
- 标准化的字段结构便于后续数据处理和分析
- 完整的技术文档和代码，具有很好的可维护性

---

## 🔍 详细技术实现

### 爬虫核心代码片段

#### 1. 列表页面解析
```python
def parse(self, content: str, url: str) -> List[Dict[str, Any]]:
    """解析决标公告列表页面"""
    soup = BeautifulSoup(content, 'lxml')
    announcements = []

    # 查找结果表格
    table = soup.find('table', id='bulletion')
    if table:
        tbody = table.find('tbody')
        if tbody:
            rows = tbody.find_all('tr')
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 8:
                    announcement = self._extract_list_data(cells, row, url)
                    if announcement:
                        announcements.append(announcement)

    return announcements
```

#### 2. 详细页面数据提取
```python
def _extract_agency_info(self, text: str, data: Dict):
    """提取机关资料"""
    if '機關代碼' in text:
        match = re.search(r'機關代碼\s*([^\n\r]+)', text)
        if match:
            data['org_code'] = match.group(1).strip()

    if '機關名稱' in text:
        match = re.search(r'機關名稱\s*([^\n\r]+)', text)
        if match:
            data['org_name'] = match.group(1).strip()
```

#### 3. 繁简转换实现
```python
import opencc
converter = opencc.OpenCC('t2s')

def convert_to_simplified(text):
    """将繁体中文转换为简体中文"""
    if not text or pd.isna(text):
        return ""
    return converter.convert(str(text))

# 处理数据
for item in data:
    traditional_record = {}
    simplified_record = {}

    for key, value in item.items():
        traditional_record[key] = value if value else ''
        simplified_record[key] = convert_to_simplified(value) if value else ''
```

### 数据库设计

```sql
CREATE TABLE IF NOT EXISTS crawl_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    -- 基本信息
    item_number TEXT,
    tender_type TEXT,
    organization TEXT,
    case_number TEXT,
    case_name TEXT,

    -- 日期信息
    tender_notice_date TEXT,
    award_notice_date TEXT,
    bid_deadline TEXT,

    -- 详细页面信息
    org_code TEXT,
    org_name TEXT,
    budget_amount TEXT,
    award_amount TEXT,
    category TEXT,
    tender_method TEXT,
    award_method TEXT,

    -- 元数据
    detail_url TEXT,
    crawl_session TEXT,
    crawl_time TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 配置文件结构

```python
# 网站配置
WEBSITE_CONFIG = {
    'base_url': 'https://web.pcc.gov.tw',
    'search_url': 'https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion',
    'search_params': {
        'querySentence': '国防部',
        'tenderStatusType': '决标',
        'sortCol': 'AWARD_NOTICE_DATE',
        'timeRange': '111',
        'pron': 'true',
        'fuzzy': 'true',
        'pageSize': '10'
    }
}

# 字段映射
FIELD_MAPPING = {
    'case_name': '标案名称',
    'case_number': '标案案号',
    'organization': '机关名称',
    'budget_amount': '预算金额',
    'tender_method': '招标方式',
    'award_method': '决标方式'
    # ... 更多字段
}
```

---

## 🚀 使用指南

### 1. 环境准备
```bash
# 安装依赖
pip install requests beautifulsoup4 lxml pandas opencc-python-reimplemented

# 设置代理
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890
```

### 2. 运行爬虫
```bash
# 执行主爬虫
python code/ultimate_perfect_crawler.py

# 清理数据
python code/clean_data.py

# 字段筛选
python code/filter_fields.py

# 繁简转换
python code/enhanced_crawler_with_chinese.py
```

### 3. 数据使用示例
```python
import json
import pandas as pd

# 读取简体中文数据
with open('data/processed/taiwan_procurement_simplified_*.json', 'r') as f:
    data = json.load(f)

# 转换为DataFrame
df = pd.DataFrame(data)

# 数据分析示例
print(f"总记录数: {len(df)}")
print(f"机关分布: {df['机关名称'].value_counts()}")
print(f"招标方式分布: {df['招标方式'].value_counts()}")
```

---

## 🔧 故障排除

### 常见问题及解决方案

1. **SSL证书错误**
   - 解决方案: 添加SSL验证跳过
   ```python
   import ssl
   ssl._create_default_https_context = ssl._create_unverified_context
   ```

2. **代理连接失败**
   - 检查代理设置: `127.0.0.1:7890`
   - 确认代理服务正常运行

3. **页面解析失败**
   - 检查页面结构是否变化
   - 更新选择器规则

4. **字符编码问题**
   - 统一使用UTF-8编码
   - CSV文件使用UTF-8-BOM

### 性能优化建议

1. **并发控制**: 添加请求间隔，避免被封IP
2. **缓存机制**: 缓存已爬取的页面，避免重复请求
3. **断点续传**: 支持从中断点继续爬取
4. **数据去重**: 基于案号进行数据去重

---

## 📈 扩展功能建议

### 1. 数据可视化
- 添加数据统计图表
- 机关采购金额分析
- 时间趋势分析

### 2. 自动化部署
- 定时任务执行
- 数据更新通知
- 异常监控告警

### 3. API接口
- RESTful API设计
- 数据查询接口
- 实时数据推送

### 4. 数据质量
- 数据验证规则
- 异常数据检测
- 数据完整性检查

---

*记录时间: 2025-07-15 23:00*
*记录者: Augment Agent*
*版本: v1.0*

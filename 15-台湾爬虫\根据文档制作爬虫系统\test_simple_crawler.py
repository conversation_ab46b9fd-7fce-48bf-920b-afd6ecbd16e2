#!/usr/bin/env python3
"""
简化测试版爬虫 - 验证基本功能
"""

import requests
from bs4 import BeautifulSoup
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_search_and_detail():
    """测试搜索和详情页面访问"""
    
    base_url = "https://web.pcc.gov.tw"
    search_url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })
    
    try:
        # 1. 测试搜索页面访问
        logger.info("测试搜索页面访问...")
        response = session.get(search_url)
        response.raise_for_status()
        logger.info(f"搜索页面访问成功，状态码: {response.status_code}")
        
        # 2. 执行简单搜索
        logger.info("执行搜索...")
        search_data = {
            'searchMethod': 'true',
            'searchTarget': 'ATM',
            'textfield': '餐飲',
            'searchType': 'basic',
            'method': 'search',
            'isSpdt': 'N',
            'pageIndex': '1',
            'recordCountPerPage': '5',
            'tenderStatus': '2'  # 決標
        }
        
        search_response = session.post(search_url, data=search_data)
        search_response.raise_for_status()
        logger.info(f"搜索请求成功，状态码: {search_response.status_code}")
        
        # 3. 解析搜索结果
        soup = BeautifulSoup(search_response.content, 'html.parser')
        
        # 查找结果链接
        links = soup.find_all('a', href=True)
        detail_links = []
        
        for link in links:
            href = link.get('href', '')
            if 'tenderDetail' in href or 'detail' in href.lower():
                full_url = href if href.startswith('http') else base_url + href
                detail_links.append({
                    'text': link.get_text(strip=True),
                    'url': full_url
                })
        
        logger.info(f"找到 {len(detail_links)} 个详情链接")
        
        # 4. 测试访问第一个详情页面
        if detail_links:
            first_link = detail_links[0]
            logger.info(f"测试访问详情页面: {first_link['text']}")
            logger.info(f"URL: {first_link['url']}")
            
            detail_response = session.get(first_link['url'])
            detail_response.raise_for_status()
            logger.info(f"详情页面访问成功，状态码: {detail_response.status_code}")
            
            # 5. 简单解析详情页面
            detail_soup = BeautifulSoup(detail_response.content, 'html.parser')
            
            # 查找表格数据
            tables = detail_soup.find_all('table')
            logger.info(f"详情页面包含 {len(tables)} 个表格")
            
            # 提取一些基本字段
            extracted_fields = {}
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 提取关键字段
                        if any(keyword in label for keyword in ['機關名稱', '標案名稱', '標案案號', '公告日', '預算金額']):
                            extracted_fields[label] = value
            
            logger.info("提取的关键字段:")
            for key, value in extracted_fields.items():
                logger.info(f"  {key}: {value}")
            
            return True, extracted_fields
        else:
            logger.warning("未找到详情链接")
            return False, {}
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False, {}

if __name__ == "__main__":
    success, fields = test_search_and_detail()
    if success:
        print("\n✅ 测试成功！")
        print("提取的字段:")
        for key, value in fields.items():
            print(f"  {key}: {value}")
    else:
        print("\n❌ 测试失败！")


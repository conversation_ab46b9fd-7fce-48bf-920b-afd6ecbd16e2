#!/usr/bin/env python3
"""
清理GPU内存并运行推理
解决CUDA内存不足问题
"""

import torch
import gc
import os
from PIL import Image
from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def clear_gpu_memory():
    """清理GPU内存"""
    print("🧹 清理GPU内存...")
    
    # 清理PyTorch缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        
        # 显示清理后的内存状态
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            total = torch.cuda.get_device_properties(i).total_memory / 1024**3
            free = total - allocated
            print(f"   GPU {i}: 已分配={allocated:.2f}GB, 已保留={reserved:.2f}GB, 可用={free:.2f}GB")
    
    # Python垃圾回收
    gc.collect()
    print("✅ GPU内存清理完成")

def run_lightweight_inference():
    """运行轻量级推理"""
    print("🎬 Wan2.1-I2V-14B-480P 轻量级推理")
    print("=" * 50)
    
    # 清理内存
    clear_gpu_memory()
    
    # 检查LoRA检查点
    lora_dir = "./models/train/Wan2.1-I2V-14B-480P_lora_final"
    available_epochs = []
    
    for i in range(10):
        epoch_file = f"{lora_dir}/epoch-{i}.safetensors"
        if os.path.exists(epoch_file):
            available_epochs.append(i)
    
    if available_epochs:
        latest_epoch = max(available_epochs)
        lora_checkpoint = f"{lora_dir}/epoch-{latest_epoch}.safetensors"
        print(f"✅ 找到LoRA检查点: epoch-{latest_epoch}")
        file_size = os.path.getsize(lora_checkpoint) / 1024**2
        print(f"   文件大小: {file_size:.1f}MB")
    else:
        print("⚠️  未找到LoRA检查点")
        lora_checkpoint = None
    
    print("\n📦 初始化轻量级Pipeline...")
    
    try:
        # 设置环境变量优化内存
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
        
        # 创建pipeline（使用CPU offload减少GPU内存使用）
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",
            model_configs=[
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
            ],
        )
        
        # 启用VRAM管理（重要！）
        pipe.enable_vram_management()
        
        print("✅ Pipeline初始化成功")
        
        # 再次清理内存
        clear_gpu_memory()
        
        # LoRA权重信息
        if lora_checkpoint:
            try:
                from safetensors import safe_open
                with safe_open(lora_checkpoint, framework="pt", device="cpu") as f:
                    lora_keys = list(f.keys())
                print(f"🔧 LoRA权重: {len(lora_keys)} 个参数")
            except Exception as e:
                print(f"⚠️  LoRA权重读取失败: {e}")
        
        print("\n📥 准备输入图像...")
        
        # 创建简单的测试图像
        image = Image.new('RGB', (832, 480), color=(100, 149, 237))  # 矢车菊蓝
        print("✅ 使用蓝色测试图像 (832x480)")
        
        print("\n🎬 开始轻量级视频生成...")
        print("   使用减少的参数以节省内存...")
        
        # 轻量级生成参数
        video = pipe(
            prompt="A beautiful sunset over the ocean",
            negative_prompt="low quality, blurry",
            input_image=image,
            seed=42, 
            tiled=True,
            height=480,
            width=832,
            num_frames=25,  # 减少帧数：从81到25
            cfg_scale=7.5,
            num_inference_steps=20  # 减少推理步数：从50到20
        )
        
        print("💾 保存视频...")
        
        # 保存视频
        output_path = f"lightweight_inference_epoch{latest_epoch if lora_checkpoint else 'base'}.mp4"
        save_video(video, output_path, fps=8, quality=5)  # 降低fps节省空间
        
        # 检查结果
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024**2
            print(f"✅ 视频生成成功!")
            print(f"📁 文件: {output_path}")
            print(f"📊 大小: {file_size:.1f}MB")
            
            # 最终GPU状态
            print(f"\n🖥️  最终GPU状态:")
            clear_gpu_memory()
            
            print(f"\n🎉 轻量级推理成功!")
            print(f"   视频规格: 832x480, 25帧, 8fps")
            print(f"   推理参数: 20步, CFG=7.5")
            print(f"   内存优化: CPU offload + VRAM管理")
            
            return True
            
        else:
            print("❌ 视频文件未生成")
            return False

    except Exception as e:
        print(f"❌ 推理失败: {e}")
        
        if "out of memory" in str(e).lower():
            print(f"\n💡 内存不足解决方案:")
            print(f"   1. 进一步减少帧数: num_frames=16")
            print(f"   2. 减少推理步数: num_inference_steps=10")
            print(f"   3. 降低分辨率: height=320, width=576")
            print(f"   4. 重启Python进程清理内存")
        
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    # 首先尝试清理所有GPU内存
    clear_gpu_memory()
    
    # 运行轻量级推理
    success = run_lightweight_inference()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 轻量级推理成功完成!")
        print("=" * 50)
        print("✅ GPU内存优化")
        print("✅ 参数调整")
        print("✅ 视频生成成功")
        print("\n💡 如需更高质量，可在GPU内存充足时增加参数")
    else:
        print("\n❌ 推理失败")
        print("💡 建议重启Python进程或重启容器以完全清理GPU内存")

if __name__ == "__main__":
    main()

#!/bin/bash
# Wan2.1-I2V-14B-480P LoRA微调训练脚本
# 针对图像到视频生成模型的微调训练
# 使用wan_video_env虚拟环境

echo "🎬 开始Wan2.1-I2V-14B-480P LoRA微调训练..."
echo "模型: Wan-AI/Wan2.1-I2V-14B-480P (图像到视频生成)"
echo "训练类型: LoRA微调"
echo "分辨率: 480x832"
echo "=" * 60

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

# 检查虚拟环境
if [[ "$CONDA_DEFAULT_ENV" != "wan_video_env" ]]; then
    echo "⚠️  请先激活wan_video_env环境: conda activate wan_video_env"
    exit 1
fi

# 检查数据集
if [ ! -d "data/example_video_dataset" ]; then
    echo "📥 下载示例数据集..."
    modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
fi

echo "🚀 启动LoRA训练..."

accelerate launch examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 480 \
  --width 832 \
  --dataset_repeat 100 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --gradient_accumulation_steps 4 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "./models/train/Wan2.1-I2V-14B-480P_lora" \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 32 \
  --extra_inputs "input_image" \
  --use_gradient_checkpointing_offload

echo "✅ LoRA训练完成！"
echo "模型保存路径: ./models/train/Wan2.1-I2V-14B-480P_lora/"
echo "🧪 运行推理测试: python examples/wanvideo/model_training/validate_lora/Wan2.1-I2V-14B-480P.py"
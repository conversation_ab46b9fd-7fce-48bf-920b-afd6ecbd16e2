{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🔍 Advanced Search Demo - Integrating with AI Search MCP Server\n", "This tutorial demonstrates how to implement advanced AI search capabilities by integrating with AI Search MCP Server. The example presents an implementation of AI-driven search using the AI Search MCP Server. It demonstrates how to orchestrate a recursive advanced search loop powered by a language model and the MCP search infrastructure."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.1 System Requirments\n", "\n", "Before starting, ensure your system meets these requirements:\n", "- Python version over 3.10 is installed.\n", "- Ensure the following Python libraries are included: ```appbuilder```, ```argparse```, ```openai```."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Install Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pip install appbuilder-sdk argparse openai"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 2. 🔁 Main Search Structure Overview\n", "Before initiating the search process, the LLM capability is invoked to generate a task plan based on the input query, which serves to guide the subsequent search process and supplement the reference information for input query.\n", "\n", "The main Search process contains 3 steps in a loop:\n", "- LLM Decision – Whether need more information to answer the user query (further search or not)\n", "- Generates MCP Server Search Parameters\n", "- Call MCP Server and Update Context\n", "\n", "When the loop ends, LLM answers the User query refering the Context by searching.\n", "\n", "## 3. Core Funcionality Dependencies\n", "### 3.1 Set up AI Search MCP Endpoint\n", "#### 3.1.1 Get an API Key\n", "You’ll need an API key from [QIANFAN](https://cloud.baidu.com/doc/AppBuilder/s/lm68r8e6i) to call the [AI Search MCP server](https://sai.baidu.com/ai/server/%25E7%2599%25BE%25E5%25BA%25A6AI%25E6%2590%259C%25E7%25B4%25A2/%E7%99%BE%E5%BA%A6?id=e014c6ffd555697deabf00d058baf388).\n", "\n", "The API key is sensitive and should be kept secret. You can set it as an environment variable or store it in a .env file in your local folder. We will use the python-dotenv package to load the environment variables from the .env file. \n", "\n", "An example of a .env file is : \n", "    API_KEY=your_api_key_here"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["API_KEY = \"<AppBuilder API Key>\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.1.2 Initialize the MCP server\n", "Then let's define the service url and initilize an MCP client:"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["service_url = f\"http://appbuilder.baidu.com/v2/ai_search/mcp/sse?api_key=Bearer+{API_KEY}\""]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2025-06-26 21:27:41,312.312] client.py [line:81] INFO [main-12862631056286909524] \n", "Connected to server with tools:['AIsearch']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:appbuilder:\n", "Connected to server with tools:['AIsearch']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[Tool(name='AIsearch', description='\\n    执行搜索。\\n\\n    Args:\\n        query (str): 搜索查询关键词或短语，用于指定需要搜索的内容。支持自然语言查询，可以包含多个关键词。Agent应该根据用户意图构建合适的查询语句，确保搜索结果的准确性和相关性。最佳实践：1) 使用简洁明确的关键词组合；2) 对于复杂查询，可以拆分为多个简单查询；3) 避免使用过于宽泛或模糊的词语。\\n        model (str, optional): 指定是否使用大语言模型对搜索结果进行总结，以及使用哪个模型进行总结。默认值为空字符串，表示直接返回原始搜索结果。当指定模型名称时（如\"ERNIE-3.5-8K\"），系统将使用对应的大模型对搜索结果进行智能总结。此参数会显著影响其他参数的行为：1) 当未指定时，temperature、top_p等LLM相关参数将失效；2) 当指定时，搜索结果会经过LLM处理，可能改变原始结果的呈现方式。最佳实践：1) 对于需要深入分析或总结的查询，建议使用ERNIE-3.5-8K或更高级的模型；2) 对于只需要原始搜索结果的查询，保持默认值即可；3) 根据实际需求选择合适的模型版本。\\n        instruction (str, optional): 用于控制搜索结果输出风格和格式的指令参数。Agent可以通过此参数指定特定的输出要求，如：结果排序方式、内容过滤条件、输出格式等。当未指定时，将使用默认的搜索结果展示方式。最佳实践：1) 明确指定需要的输出格式（如列表、摘要等）；2) 指定结果排序方式（如按时间、相关性等）；3) 设置内容过滤条件（如排除特定类型的内容）。\\n        temperature (float, optional): 控制模型输出随机性的采样参数，取值范围为(0, 1]。默认值为1e-10。该参数仅在model参数指定了LLM模型时生效。参数影响模型输出的多样性和稳定性：值越大，输出结果越多样化但可能不够稳定；值越小，输出结果越确定和集中。最佳实践：1) 对于事实性查询，建议使用较低的值（如0.1）；2) 对于创意性内容，可以使用较高的值（如0.7）；3) 避免使用极端值（接近0或1）。\\n        top_p (float, optional): 控制模型输出多样性的核采样参数，默认值为1e-10。该参数仅在model参数指定了LLM模型时生效。参数通过设置累积概率阈值来筛选候选词：值越小，模型会从更少的候选词中选择，输出更加确定和保守；值越大，模型会考虑更多的候选词，输出更加多样和创造性。最佳实践：1) 通常建议设置为0.7-0.9之间；2) 与temperature参数配合使用，共同控制输出质量；3) 对于需要高准确性的场景，建议使用较低的值。\\n        search_domain_filter (list[str], optional) 用于限制搜索结果来源的域名过滤列表。Agent可以指定一个或多个域名（如[\"baidu.com\"]），系统将只返回这些指定域名的网页内容。最佳实践：1) 对于需要权威信息的查询，建议限制在官方或知名网站；2) 可以组合多个相关领域的权威网站；3) 避免过度限制导致结果不足；4) 根据查询主题选择合适的域名范围。\\n        resource_type_filter (list[dict]): 指定搜索资源的类型和每种类型返回的结果数量。默认配置为[{\"type\": \"web\",\"top_k\": 10}]，表示只返回网页类型的搜索结果，且最多返回10条。每种资源类型（web、image、video等）的top_k取值范围为1~10。最佳实践：1) 根据查询需求选择适当的资源类型组合；2) 合理分配各类型资源的返回数量；3) 对于多类型查询，建议每种类型设置3-5条结果；4) 避免请求过多不必要的结果类型。\\n    ', inputSchema={'properties': {'query': {'title': 'Query', 'type': 'string'}, 'instruction': {'default': None, 'title': 'Instruction', 'type': 'string'}, 'model': {'default': None, 'title': 'Model', 'type': 'string'}, 'temperature': {'default': 1e-10, 'title': 'Temperature', 'type': 'number'}, 'top_p': {'default': 1e-10, 'title': 'Top P', 'type': 'number'}, 'search_domain_filter': {'default': None, 'items': {'type': 'string'}, 'title': 'Search Domain Filter', 'type': 'array'}, 'resource_type_filter': {'default': None, 'items': {'type': 'object'}, 'title': 'Resource Type Filter', 'type': 'array'}}, 'required': ['query'], 'title': 'AIsearchArguments', 'type': 'object'})]\n"]}], "source": ["import asyncio\n", "import logging\n", "\n", "import nest_asyncio\n", "from appbuilder.mcp_server.client import MCPClient\n", "\n", "logging.getLogger(\"httpx\").setLevel(logging.WARNING)\n", "logging.getLogger(\"mcp.client.sse\").setLevel(logging.WARNING)\n", "\n", "async def call_mcp():\n", "    client = MCPClient()\n", "    await client.connect_to_server(service_url=service_url)\n", "    return client\n", "\n", "nest_asyncio.apply()\n", "\n", "loop = asyncio.get_event_loop()\n", "client = loop.run_until_complete(call_mcp())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Request Model Example\n", "In this section, the ERNIE 4.5 series models can be used directly for the following key parts:\n", "\n", "- Deciding whether the main loop ends (if search further or not)\n", "- Generating MCP search server calling parameters\n", "\n", "You should firstly deploy a model as a local service in the following way and configure the model_url properly. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3.2.1 Deploying Model and Setting Key Parameters\n", "\n", "The deployment can be accomplished using the FastDeploy tool, which is an inference deployment tool for large models open-sourced by PaddlePaddle. Currently, FastDeploy supports 4.5 series models, and provides commonly used inference deployment functionalities. For deployment methods, please refer to the [FastDeploy official documentation](https://github.com/PaddlePaddle/FastDeploy).\n", "\n", "After deploying FastDeploy as a backend service, you need to set up the model_url in the configuration below."]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["model_url = \"http://localhost:port/v1/chat/completions\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Prepare the following environment variables and parameters (as the default configuration for requesting model, which can be adjusted according to the actual situation of the used model):"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["api_key=\"bce-v3/ALTAK-xxx\" # this could be the same as the API KEY you've got from QIANFAN in 3.1.1\n", "model=\"ERNIE-4.5-xx\" # Select the model to invoke\n", "max_tokens=8000 # refer to the model documentation for the maximum number of tokens\n", "temperature=0.7 # default setting or as per your requirement\n", "top_p=1.0 # default setting or as per your requirement"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The following is an example function for model request, please use it to test your model service."]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "\n", "def chat(query: str, model: str, max_tokens: int = 2048, temperature: float = 0.7, top_p: float = 1.0) -> str:\n", "    \"\"\"Process chat request and generate response.\n", "\n", "    Args:\n", "        query: User input query.\n", "        model: Model name to use.\n", "        max_tokens: Maximum tokens for response generation.\n", "        temperature: Controls randomness of generated responses.\n", "        top_p: Controls diversity of generated responses.\n", "\n", "    Returns:\n", "        str: response content.\n", "    \"\"\"\n", "    conversation = []\n", "    conversation.append({\"role\": \"user\", \"content\": query})\n", "    try:\n", "        client = OpenAI(base_url=model_url, api_key=api_key)\n", "        response_text = client.chat.completions.create(\n", "            model=model,\n", "            messages=conversation,\n", "            temperature=temperature,\n", "            max_tokens=max_tokens,\n", "            top_p=top_p\n", "        )\n", "        response_text = response_text.model_dump()\n", "        content_text = response_text[\"choices\"][0][\"message\"][\"content\"]\n", "        return content_text\n", "    except Exception as e:\n", "        raise print(\"Exception: \" + repr(e))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Implement the Advanced Search Demo\n", "This example implements a recursive AI-powered search loop demo designed to emulate expert reasoning and efficient information gathering."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1 Structure Overview\n", "1. Input user query\n", "2. Generate the task plan info\n", "3. Iteratice Search (3-step loop)\n", "\n", "        Step1: LLM Decision - if context information is enough\n", "        \n", "        Step2: Generates MCP Server Search Parameters\n", "        \n", "        Step3: Call MCP Server and Update Context\n", "\n", "   \n", "4. Answer user query with full context"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Firstly, input your user query:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Query:  请帮我分析一下近年全球渐冻症领域的主要研究进展，包括发病机制、诊断手段、治疗方法等。请对比分析中美欧在诊疗技术上的差异，并梳理当前中国在渐冻症临床实践中面临的核心挑战。\n"]}], "source": ["query = input(\"\\nQuery: \").strip()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Generate the Search Task Plan\n", "Using LLM to generate a task plan based on the input query, to guide the subsequent search process."]}, {"cell_type": "code", "execution_count": 45, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. 基础资料收集与整理\n", "    (1) 发病机制研究进展：收集近年来全球关于渐冻症（ALS）发病机制的最新研究成果，涵盖基因突变（如SOD1、C9ORF72等）、蛋白质异常聚集（如TDP-43）、神经炎症、氧化应激及线粒体功能障碍等相关研究。\n", "    (2) 诊断手段进展：整理目前全球范围内ALS的诊断标准及最新进展，包括神经电生理检测、影像学检查（如MRI）、生物标志物研究（如神经丝轻链蛋白）以及人工智能在诊断中的应用。\n", "    (3) 治疗方法进展：调研近年来ALS治疗领域的重大突破，包括药物研发（如Relyvrio、Tofersen等）、基因治疗、干细胞治疗、呼吸支持及营养支持等非药物治疗手段的最新研究。\n", "2. 中美欧诊疗技术差异对比分析\n", "    (1) 诊断技术差异：对比中美欧在ALS诊断技术上的差异，包括诊断标准的执行情况、新型诊断技术的普及程度及诊断效率。\n", "    (2) 治疗方法差异：分析中美欧在ALS治疗方法上的选择偏好及实施效果，特别是新药研发、临床试验的参与程度及非药物治疗手段的应用情况。\n", "    (3) 医疗资源分配差异：探讨中美欧在ALS医疗资源分配上的差异，包括专科医生数量、治疗中心分布及患者获取治疗的便捷性。\n", "3. 中国渐冻症临床实践核心挑战梳理\n", "    (1) 诊断挑战：分析中国在ALS诊断过程中面临的主要挑战，包括诊断延误、误诊率、新型诊断技术的应用障碍等。\n", "    (2) 治疗挑战：梳理中国在ALS治疗方面的核心挑战，包括新药可及性、治疗费用、非药物治疗手段的普及程度及患者治疗依从性。\n", "    (3) 医疗资源与支持体系挑战：探讨中国在ALS医疗资源分配、患者支持体系（如护理、心理支持）及社会认知度方面存在的不足。\n", "4. 深度分析与综合推理\n", "    (1) 影响机制分析：基于收集的数据，深入分析ALS发病机制、诊断及治疗方法之间的相互影响，探讨不同诊疗技术对患者预后的影响。\n", "    (2) 差异原因探究：针对中美欧在诊疗技术上的差异，探究其背后的原因，包括科研投入、医疗政策、社会文化等因素。\n", "    (3) 挑战应对策略：结合中国渐冻症临床实践的核心挑战，探讨可能的应对策略，包括政策建议、技术创新、患者教育等。\n", "5. 研究成果产出与报告撰写\n", "    (1) 成果整合：将上述分析结果整合成一份全面的研究报告，涵盖ALS发病机制、诊断手段、治疗方法的研究进展，中美欧诊疗技术的差异对比，以及中国渐冻症临床实践的核心挑战。\n", "    (2) 可视化呈现：运用图表、图形等可视化工具，直观展示研究数据和分析结果，增强报告的可读性和说服力。\n", "    (3) 报告撰写与修改：根据整合的成果和可视化呈现，撰写详细的研究报告，并经过多次修改和完善，确保报告内容的准确性和逻辑性。\n"]}], "source": ["import textwrap\n", "\n", "# Generate task plan\n", "task_plan = \"\"\n", "\n", "TASK_PLAN_PROMPT = textwrap.dedent(\n", "    \"\"\"\\\n", "    # 任务\n", "    我是一个专业的研究员。我会根据给定的query需求，逐层深入分析，并以清晰易懂的语言组织成一份完整的研究方案规划，便于执行和实施。\n", "    ## 深度研究参考思路\n", "    1. 目标确认与基础信息收集\n", "        （1）确定研究目的：收集相关领域的信息，明确研究背景、细化通过研究想要达成的具体成果\n", "        （2）数据收集与整理：我会系统收集目标领域的历史数据和案例，将这些信息整理成标准化的内容，可选择用数据表格形态整理。关键是确保数据的完整性、准确性和时序性，为后续所有分析提供可靠的事实基础，数据收集必须覆盖足够的时间范围\\\n", "    ，包含所有相关的关键信息字段。\n", "    2. 深度分析与信息深度挖掘\n", "        （1）深度模式分析：基于收集到的数据，我会深入分析其中的新研究对象、关键模式、规律和趋势等。这包括频率统计、周期性变化、发展趋势等量化分析，目标是揭示隐藏在数据背后的内在逻辑和规律性特征。对于上一步中出现的新的重要概念或\\\n", "    实体，需对该类需要探究的内容进行二次信息搜集。分析结果尽可能用统计数据和可视化图表来呈现。\n", "        （2）核心驱动因素提取：通过对模式的深度分析，我需要识别出真正影响结果的核心驱动因素。这些因素需要按照影响力大小进行排序，并评估各自的权重。重点是找到那些具有决定性作用的关键变量，而不是表面的相关性因素。\n", "        （3）现实背景信息补强：针对已识别的核心驱动因素，我会收集当前相关的现实背景信息。这包括最新的政策变化、市场环境、技术发展、社会趋势等可能影响分析结果的现实因素。目标是将历史规律与当前实际情况相结合，确保分析的时效性和准\\\n", "    确性。\n", "    3. 研究成果产出\n", "        （1）综合推理与结论：最后将整合上述获取到的所有信息，运用严密的逻辑推理得出最终结论。这不仅包括具体的预测结果，还要包含完整的推理过程、逻辑链条、以及对结论可信度的评估。同时需要考虑可能存在的风险因素和不确定性。\n", "        （2）产出符合需求的交付形态：根据输入query需求或场景通用的形态，组织最终研究成果或调研报告，需包含上述研究过程的各类细节信息，\n", "    *以上思路步骤仅供参考，可根据实际需求或命题情况进行调整\n", "\n", "\n", "    ## query示例及样例输出：\n", "    **输入query1：**\n", "    \"请你深入研究基于事件的光流估计，梳理经典代表算法与最新的研究趋势，分析当前这个领域的瓶颈及可行的解决方案\"\n", "\n", "    **样例输出1：**\n", "    1. 资料收集\n", "        (1) 调研基于事件的视觉传感器（事件相机）的工作原理、特点及其与传统帧式相机的区别。\n", "        (2) 梳理基于事件的光流估计领域的经典代表算法，包括但不限于基于事件匹配、基于事件累积和基于学习的方法。\n", "    2. 深入研究并梳理趋势并识别挑战\n", "        (1) 深入分析基于事件的光流估计的最新研究趋势，例如与深度学习的结合、多模态融合以及在特定应用场景（如高速运动、低光照）下的进展。\n", "        (2) 识别并分析当前基于事件的光流估计领域面临的主要瓶颈和挑战，例如数据稀疏性、噪声敏感性、计算效率、泛化能力以及缺乏大规模标准数据集。\n", "    3. 瓶颈和方案探索\n", "        (1) 针对上述瓶颈和挑战，探讨可行的解决方案和未来研究方向，例如新型事件表示方法、更鲁棒的算法设计、高效的硬件加速以及跨领域知识迁移。\n", "        (2) 查找并分析基于事件的光流估计在实际应用中的案例，例如机器人导航、自动驾驶或工业检测，以了解其应用前景和实际需求。\n", "    4. 报告撰写与交付\n", "        （1）将研究成果整理成深入的研究报告，包括研究过程、结论和建议\n", "\n", "    **输入query2：**\n", "    \"请帮我梳理一下面向AIGC/扩散模型/大模型训练的数据集所有权保护方法，并分析当前的挑战，给出一些创新性的思路方案\"\n", "    **样例输出2：**\n", "    1. 信息收集和整理\n", "        (1) 梳理当前适用于人工智能训练数据所有权和知识产权保护的法律和技术框架。\n", "        (2) 深入研究专门针对AIGC、扩散模型和大型模型训练数据所有权保护的现有方法，例如数据水印、溯源技术、加密技术和合同协议。\n", "    2. 分析和方案制定\n", "        (1) 分析在大型AI模型训练中实施和执行这些数据所有权保护方法所面临的挑战，包括技术限制、法律模糊性及实际操作困难。\n", "        (2) 查找并评估与AI模型训练数据所有权保护相关的实际案例或争议，以了解其成功和失败的经验。\n", "        (3) 探索新兴技术和研究方向，如区块链、先进密码学或新型数字版权管理，它们如何为AI数据所有权保护提供创新解决方案。\n", "        (4) 思考在AI数据所有权领域可能出现的政策或监管创新，例如新的许可模式、集体权利管理或国际合作框架。\n", "    3. 报告撰写\n", "        (1) 综合上述研究，提出结合法律、技术和伦理考量的创新性概念框架或混合方案，以实现AIGC/大型模型训练数据的稳健所有权保护。\n", "\n", "    ## 更多应用场景：\n", "    这个框架同样适用于商业战略分析、市场趋势预测、投资价值评估、政策效果研究、产品发展规划等各种需要深度分析和预测的场景。无论是分析企业发展策略、预测行业发展趋势，还是评估投资机会，都会按照同样的五层逻辑进行系统化的深度研究。\n", "\n", "    ## 要求\n", "    我不会对输入query作出任何评价，也不会解释相关的动作，我只会生成方案规划，一定不要直接回复输入query。\n", "    我输出的规划是按步骤顺序组织的整体思路，每个步骤的规划用一两句话或一两个列表元素表示具体步骤，每个步骤的标题要严格和输入query相关，不要简单重复上述参考思路的小标题。\n", "    但对于一些简单的问题，不用输出详细的规划步骤（也不需要给出规划示例）；如果遇到一些困难的问题，我可能会更进一步地扩展深度研究的要求。\n", "\n", "    ## 当前输入query：{query}\n", "    \"\"\"\n", ")\n", "\n", "plan_prompt = TASK_PLAN_PROMPT.format(query=\"{query}\")\n", "task_plan = chat(plan_prompt.format(query=query), model, max_tokens, temperature, top_p)\n", "\n", "print(task_plan)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 LLM-Guided Iterative Search (3-Step Loop)\n", "The overall process is then broken into three steps:\n", "\n", "#### 4.3.1 Step 1: LLM Decision – Do We Have Enough Information?\n", "First asks the LLM model to decicide:\n", "\n", ">“Given the user's question and current context, is additional information needed before answering?”\n", "\n", "This step ensures the demo can reason about knowledge gaps before querying any tool or database."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model decision:{'reasoning': '为了完成深度研究任务，我将首先从基础资料收集与整理入手，具体包括发病机制、诊断手段和治疗方法的最新研究进展。这能帮助我建立对渐冻症（ALS）领域当前研究状况的全面理解。基于这些信息，我将对比分析中美欧在诊疗技术上的差异，包括诊断技术、治疗方法和医疗资源分配。接下来，我会根据这些对比分析，梳理中国在渐冻症临床实践中面临的核心挑战。最后，我会进行深度分析与综合推理，包括影响机制分析、差异原因探究和挑战应对策略的探讨，并将所有分析结果整合成一份全面的研究报告。因此，首先我需要调用工具，搜索全球在渐冻症发病机制、诊断手段、治疗方法方面的最新研究进展。', 'need_more_info': True}\n"]}], "source": ["import json\n", "\n", "# Step 1: Check if more information is needed\n", "need_more_info = None\n", "search_results=[]\n", "SEARCH_INFO_PROMPT = textwrap.dedent(\n", "    \"\"\"\\\n", "    现在需要你完成以下 **[深度研究]任务**：\n", "    **{query}**\n", "    为了完成该 [深度研究] 任务，你需要通过**动态、多次地调用检索工具**，以获取足够且相关的 [参考信息](工具返回的搜索结果)。\n", "    [参考信息] 必须满足以下两个要求：\n", "    1. 全面覆盖任务问题中的各个细节需求；\n", "    2. 对前序 [参考信息] 中涉及的延伸概念进行进一步的具体说明。\n", "\n", "    在信息量不足或理解尚不充分的情况下，请持续调用工具补充信息，直到所收集的信息足以支撑对任务问题的完整理解和回答为止。\n", "\n", "    你应当根据**历史检索结果**，判断下一步搜索的方向和重点，并决定是否需要继续调用工具。这一过程应具有**自适应性和递进性**。\n", "\n", "    请严格参照以下任务规划指导，辅助你进行任务执行：\n", "    ```\n", "    {task_plan}\n", "    ```\n", "    历史检索结果：\n", "    ```\n", "    {search_results_string}\n", "    ```\n", "\n", "    请以JSON格式输出，格式要求如下：\n", "    {{\n", "        \"reasoning\": \"决策理由\",\n", "        \"need_more_tool_info\": true/false\n", "    }}\n", "    注意：need_more_tool_info为true表示需要调用工具获取更多信息，若任务过于简单或需要向用户澄清获取信息时则为false。\n", "    \"\"\"\n", ")\n", "search_results_string = \"\\n\\n\".join(search_results)\n", "content = chat(\n", "    SEARCH_INFO_PROMPT.format(\n", "        query=query, task_plan=task_plan, search_results_string=search_results_string\n", "    ),\n", "    model,\n", "    max_tokens,\n", "    temperature,\n", "    top_p\n", ")\n", "\n", "try:\n", "    start = content.find(\"{\")\n", "    end = content.rfind(\"}\") + 1\n", "    if start >= 0 and end > start:\n", "        content = content[start:end]\n", "    result = json.loads(content)\n", "    print(f\"Model decision:{result}\")\n", "except json.JSONDecodeError:\n", "    print(\"错误：模型输出不是有效的JSON格式\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4.3.2 Step2: Generates MCP Server Search Parameters\n", "If additional information is needed, the model is asked to generate precise search parameters for the MCP Server."]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model decision:[{'tool_name': 'AIsearch', 'tool_args': {'query': '近年 全球渐冻症 发病机制 研究进展 基因突变 蛋白质异常聚集 神经炎症 氧化应激 线粒体功能障碍', 'instruction': '整理近年全球渐冻症（ALS）发病机制的研究进展，涵盖基因突变（如SOD1、C9ORF72等）、蛋白质异常聚集（如TDP-43）、神经炎症、氧化应激及线粒体功能障碍等相关研究，以列表形式展示'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '近年 全球渐冻症 诊断手段 研究进展 神经电生理检测 影像学检查 MRI 生物标志物研究 神经丝轻链蛋白 人工智能 诊断', 'instruction': '整理近年全球渐冻症（ALS）诊断手段的研究进展，包括神经电生理检测、影像学检查（如MRI）、生物标志物研究（如神经丝轻链蛋白）以及人工智能在诊断中的应用，以列表形式展示'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '近年 全球渐冻症 治疗方法 研究进展 药物研发 Relyvrio Tofersen 基因治疗 干细胞治疗 呼吸支持 营养支持', 'instruction': '整理近年全球渐冻症（ALS）治疗方法的研究进展，包括药物研发（如Relyvrio、Tofersen等）、基因治疗、干细胞治疗、呼吸支持及营养支持等非药物治疗手段的最新研究，以列表形式展示'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '中美欧 渐冻症 诊断技术 差异 诊断标准 新型诊断技术 普及程度 诊断效率', 'instruction': '对比中美欧在渐冻症（ALS）诊断技术上的差异，包括诊断标准的执行情况、新型诊断技术的普及程度及诊断效率，以列表形式展示'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '中美欧 渐冻症 治疗方法 差异 新药研发 临床试验 参与程度 非药物治疗手段 应用情况', 'instruction': '分析中美欧在渐冻症（ALS）治疗方法上的选择偏好及实施效果，特别是新药研发、临床试验的参与程度及非药物治疗手段的应用情况，以列表形式展示'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '中美欧 渐冻症 医疗资源 分配 差异 专科医生数量 治疗中心 分布 患者 获取治疗 便捷性', 'instruction': '探讨中美欧在渐冻症（ALS）医疗资源分配上的差异，包括专科医生数量、治疗中心分布及患者获取治疗的便捷性，以列表形式展示'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '中国 渐冻症 临床实践 诊断挑战 诊断延误 误诊率 新型诊断技术 应用障碍', 'instruction': '分析中国在渐冻症（ALS）诊断过程中面临的主要挑战，包括诊断延误、误诊率、新型诊断技术的应用障碍等，以列表形式展示'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '中国 渐冻症 临床实践 治疗挑战 新药可及性 治疗费用 非药物治疗手段 普及程度 患者 治疗依从性', 'instruction': '梳理中国在渐冻症（ALS）治疗方面的核心挑战，包括新药可及性、治疗费用、非药物治疗手段的普及程度及患者治疗依从性，以列表形式展示'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '中国 渐冻症 临床实践 医疗资源与支持体系 挑战 医疗资源分配 患者支持体系 社会认知度', 'instruction': '探讨中国在渐冻症（ALS）医疗资源分配、患者支持体系（如护理、心理支持）及社会认知度方面存在的不足，以列表形式展示'}}]\n"]}], "source": ["# Step 2: Generate search parameters\n", "available_tools = json.dumps([{\n", "    \"name\": tool.name,\n", "    \"description\": tool.description,\n", "    \"input_schema\": tool.inputSchema\n", "} for tool in client.tools], ensure_ascii=False, indent=2)\n", "\n", "search_results_string= \"\\n\\n\".join(search_results)\n", "\n", "GEN_QUERY_PROMPT = textwrap.dedent(\n", "    \"\"\"\\\n", "    可用工具信息：\n", "    {available_tools}\n", "\n", "    历史检索结果：\n", "    ```\n", "    {search_results_string}\n", "    ```\n", "    用户问题：{query}\n", "\n", "    请严格参照以下任务规划指导，执行下一步工具调用：\n", "    ```\n", "    {task_plan}\n", "    ```\n", "\n", "    请根据以上历史检索结果判断回复用户问题所缺少的信息内容，或根据任务规划指导判断下一步需要完成的检索任务，调用合适的工具，生成新的检索query来补充所缺少的信息或需要执行的其他检索任务.\n", "    回复用户问题所需的完整参考信息需包含对用户问题中各细节需求及信息，以及参考信息中与需求相关的延伸概念的具体说明，同时需要完成任务规划中指导的各检索任务。\n", "    生成检索query的要求如下：\n", "    - 生成的检索query要与历史检索结果中的检索词不同，生成更深入的信息补充类query。\n", "    - 单个检索的query需独立为一个简单直接的搜索主题，不能过长或过于复杂，若有多个需要补充的信息，则拆分成多个query，通过json列表形式分成多次工具调用。\n", "\n", "    以下为生成本轮query的示例1：\n", "    ```\n", "    用户问题：帮我做竞品调研，深入了解当前Agent产品在旅行场景下如何识别、响应和解决用户的具体需求，包括需求识别的准确性、解决方案的有效性，以及在满足用户需求过程中面临的挑战。请结合当前市面上的产品和AI能力帮我输出详细的调研报告。\n", "    上一轮检索query为：\"旅行场景 AI Agent 产品 竞品\"\n", "    上一轮得的检索结果包括以下内容：\"Manus\"、\"飞猪问一问\"、\"字节豆包AI\"等Agent产品介绍\n", "    本轮生成query则应根据上一轮检索结果进行下一步有关产品细节的深度挖掘，生成如下检索query：\n", "    - Manus 需求应用案例\n", "    - 飞猪问一问 需求应用案例\n", "    - 字节豆包AI 需求应用案例\n", "    ```\n", "\n", "    以下为生成本轮query的示例2：\n", "    ```\n", "    用户问题：帮我做竞品调研，深入了解当前Agent产品在旅行场景下如何识别、响应和解决用户的具体需求，包括需求识别的准确性、解决方案的有效性，以及在满足用户需求过程中面临的挑战。请结合当前市面上的产品和AI能力帮我输出详细的调研报告。\n", "    上一轮检索query为：\"Manus 需求方案 应用案例\"、 \"飞猪问一问 需求方案 应用案例\"、\"字节豆包AI 需求方案 应用案例\"\n", "    上一轮得的检索结果包括以下内容：\"Manus相关产品方案和案例\"、\"飞猪问一问应用案例\"、\"字节豆包AI产品方案\"等产品案例内容\n", "    本轮生成query则应根据上一轮检索结果判断，缺少有关产品满足用户需求中面临的挑战相关内容，因此进行下一步检索，生成如下检索query：\n", "    - Manus 满足需求面临的挑战\n", "    - 飞猪问一问 满足需求面临的挑战\n", "    - 字节豆包AI 满足需求面临的挑战\n", "    ```\n", "\n", "    请根据用户问题和历史检索结果中的结果，参考任务规划指导进行工具调用，并参照上述示例生成恰当的参数，直接以JSON格式输出需要调用的工具名称（tool_name）和工具调用参数（tool_args）。\n", "    \"\"\"\n", ")\n", "content_text = chat(\n", "    GEN_QUERY_PROMPT.format(\n", "        available_tools=available_tools,\n", "        query=query,\n", "        task_plan=task_plan,\n", "        search_results_string=search_results_string,\n", "    ),\n", "    model,\n", "    max_tokens,\n", "    temperature,\n", "    top_p\n", ")\n", "\n", "try:\n", "    start = content_text.find(\"```json\") + 7\n", "    end = content_text.rfind(\"```\")\n", "    if start >= 0 and end > start:\n", "        content_text = content_text[start:end]\n", "    content = json.loads(content_text)\n", "    print(f\"Model decision:{content}\")\n", "except json.JSONDecodeError:\n", "    print(\"错误：模型输出不是有效的JSON格式\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4.3.3 Step3: Call MCP Server and Update Context\n", "Using the parameters above, the system sends a request to the MCP Server and stores the results for reasoning in the next loop."]}, {"cell_type": "code", "execution_count": 22, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["工具调用结果(truncated):Title: 首款渐冻症对因药物开出首方!全球多款小核酸/基因/细胞疗法并进,ALS对因治疗迎加速突破\n", "Content:  一、已上市药物: 从症状控制到病理干预的突破 目前,全球获批的 ALS 治疗药物仍以传统机制为主,但新型疗法已展现出对病理机制的针对性干预。 过去很长一段时间,ALS 的治疗目标以减缓病情进展和改善症状为主。但随着技术的不断发展,针对渐冻症的药物开发逐渐转为针对病因。 托夫生即是全球首个获批的渐冻症对因治疗药物。其通过靶向突变 SOD1 基因产生的 mRNA,可有效减少毒性 SOD1 蛋白的合成(这些毒性蛋白积累会引起细胞功能障碍,从而导致患者出现神经退化和肌肉无力)。 值得一提的是,6 月 10 日,托夫生已在北京大学第三医院开出首方,标志着这款药物正式应用于我国临床。 二、核心研发管线: 多技术平台并行,靶向精准治疗 当前ALS 研发管线呈现「多机制、多技术、全球化」特点,小核酸药物、基因疗法、细胞疗法等前沿技术与小分子、单抗等传统手段齐头并进。以下从作用机制角度梳理关键在研项目。 (一)小核酸药物 目前针对 ALS 开发的小核酸药物主要有ASO(反义寡核酸)\n", "工具调用结果(truncated):Title: 渐冻症靶向治疗应用临床 患者在北医三院完成全国首针注射\n", "Content:  2025年6月10日6:00,43岁的李女士在北京大学第三医院神经内科接受了托夫生注射液腰穿鞘内注射治疗。据了解,这是肌萎缩侧索硬化症(ALS,俗称“渐冻症”)靶向治疗药物托夫生注射液国内正式商业上市后的全国首方。 这标志着我国首个针对超氧化物歧化酶1(SOD1)基因突变所致成人渐冻症(SOD1-ALS)的疾病修正治疗药物正式应用于临床,为治疗这一罕见且致命的神经系统疾病带来源头干预的新希望。 据了解,李女士因不明原因的进行性肌肉无力来到北医三院就诊,诊断患有SOD1-ALS。在神经内科与药学部的紧密协作下,北医三院高效完成了药物引进和采购流程,确保了此次首方治疗的顺利实施。 托夫生注射液是全球首个且目前唯一获批用于SOD1-ALS成人患者的疾病修正治疗(DMT)药物。它突破了传统治疗仅能缓解症状的局限,首次实现了针对病因的精准干预。该药物分别于2023年4月获美国FDA批准、2024年9月获中国国家药品监督管理局(NMPA)批准上市。 其核心作用机制在于作为一种反义寡核苷酸(ASO)药物,能够特\n", "工具调用结果(truncated):Title: 临床新进展,生命园企业加速研发渐冻症药物\n", "Content: 近日,园内企业神济昌华自主研发的基因疗法SNUG01多中心研究者发起的临床研究(IIT-02)正式启动。不仅神济昌华,今年,园内另一创新药企维泰瑞隆申报的1类新药也获得临床试验默示许可,拟开发治疗渐冻症药物。 目前,国内一批创新药企正奔跑在研发治疗渐冻症创新药的赛道上。此前完成的首次研究者发起的临床研究已初步验证该疗法安全耐受性良好,在疗效指标及生物标志物改善方面呈现积极信号。凭借最新的科学进展,公司立志为全球退行性疾病患者带来变革性疗法,通过发现和开发药物,并通过直接解决退行性疾病的致病原因和风险因素,来改善患者及其家人的生活质量。\n", "URL: https://baijiahao.baidu.com/s?id=1833172633035935153&wfr=spider&for=pc\n", "\n", "Title: 渐冻症创新药在青完成山东首针\n", "Content:  托夫生注射液在青岛大学附属医院开出山东首方 渐冻症进入精准治疗时代 6月12日,首位在青岛大学附属医院确诊携带超氧化物歧化酶1(SOD1)基因突变的肌萎缩侧索硬化(A\n", "工具调用结果(truncated):Title: 中医视角如何解释渐冻症,为何西医认为是神经的问题?\n", "Content:  西医聚焦“分子战争” 神经元退化的本质是基因突变(如SOD1基因)与环境毒素(重金属、农药)共同引发的细胞级战争。利鲁唑通过抑制谷氨酸释放延缓退化,依达拉奉则清除自由基,但无法逆转已受损的神经元。 中医重构“生命网络” 中医视人体为“气血网络”,渐冻症\n", "URL: http://www.zhihu.com/question/3132298725/answer/111590266128\n", "\n", "Title: 2年vs5年!渐冻症恶化速度天壤之别?这4点救命差异大多数人不知道!\n", "Content:  一、基因:藏在DNA里的“定时炸弹” 1.恶性突变 vs 良性突变 约5%-10%的ALS患者携带遗传性基因突变(如SOD1、C9orf72),其中某些突变类型(如C9orf72重复扩增)会导致病情急速恶化,生存期仅2-3年;而良性突变患者(如某些罕见SOD1变异)可能存活10年以上,霍金即属此类。 2.基因之外的“表观调控” 即使无遗传突变,环境因素(如毒素暴露)可能通过表观遗传修饰“激活”促衰基因,加速神经元死亡。\n", "工具调用结果(truncated):Title: 一年143万!渐冻症迎来第一款对因治疗方案\n", "Content:  一、从对症到对因:渐冻症治疗的革命性突破 肌萎缩侧索硬化症(ALS)被定义为一种进行性、致命的神经退行性疾病。它无情地攻击大脑和脊髓的运动神经元,导致肌肉无力,瘫痪,并最终夺走患者的生命。在托夫生出现前,渐冻症治疗领域经历了漫长而艰难的跋涉。 近200年来,全球有1000多万人因为这种运动神经元病死亡,而治愈率始终为0。绝大多数患者在确诊后2至5年内死亡,医学界几乎束手无策。过去近30年间,全球仅有屈指可数的几种渐冻症药物获批: 1995年:利鲁唑(力如太)上市,平均延长患者生命2-3个月。 2015年:依达拉奉获批,仅对约5%的患者有效。 2022年:Albrioza获得批准但随后因III期试验失败退出市场。 这些药物都只能缓解症状、延缓疾病进展,却无法触及疾病的根本原因。医生和患者都像是在与时间进行一场必败的赛跑,手中却没有真正有效的武器。托夫生的问世,标志着渐冻症治疗从\"对症\"转向\"对因\"的革命性突破。作为全球首个且目前唯一获批用于SOD1-ALS成人患者的疾病修正治疗药物(DMT),托夫生的核心机制直\n"]}], "source": ["# Step 3: Call MCP server tool\n", "import asyncio\n", "\n", "import nest_asyncio\n", "\n", "\n", "async def call_mcp(tool_name, tool_args):\n", "    result = await client.call_tool(tool_name, tool_args)\n", "    return result\n", "\n", "nest_asyncio.apply() # 允许嵌套事件循环（只需运行一次）\n", "\n", "loop = asyncio.get_event_loop()\n", "\n", "search_result = []\n", "\n", "for content_item in content:\n", "    tool_name = content_item.get(\"tool_name\")\n", "    tool_args = content_item.get(\"tool_args\")\n", "    if \"model\" in tool_args:\n", "        del tool_args[\"model\"]\n", "\n", "    tool_result = loop.run_until_complete(call_mcp(tool_name, tool_args))\n", "    if any(text.type == \"text\" and \"Error executing tool AIsearch\" in text.text for text in tool_result.content):\n", "        print(tool_result.content[0].text)\n", "        break\n", "    tool_result = \"\\n\\n\".join([text.text[:1000] for text in tool_result.content if text.type == \"text\"])\n", "    print(f\"工具调用结果(truncated):{tool_result}\")\n", "    search_result.append(f\"检索词：{tool_args.get('query')}\\n检索结果：\\n{tool_result}\\n\")\n", "search_results.append(f\"***************第{len(search_results) + 1}次检索结果*******************：\\n{json.dumps(search_result, ensure_ascii=False, indent=2)}\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Repeat the above three steps until the model's decision no longer requires additional information to be supplemented."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.4 Generate Final Responses\n", "At last, let LLM answer the user query with full context provived."]}, {"cell_type": "code", "execution_count": 48, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["final answer:### 渐冻症领域近年（2020 年 - 至今）主要研究进展\n", "\n", "#### 发病机制\n", "- **基因研究深入**：近年来发现了多个与渐冻症（肌萎缩侧索硬化症，ALS）发病相关的新基因。例如，C9orf72 基因的六核苷酸重复扩增被认为是家族性 ALS 最常见的致病原因之一，这一发现有助于深入理解 ALS 的遗传基础和发病机制。此外，还有其他一些基因如 TARDBP、FUS 等也被证实与 ALS 发病相关，这些基因涉及 RNA 代谢、蛋白质稳态等重要生物学过程，其异常可能导致运动神经元的退化和死亡。\n", "- **蛋白质聚集与毒性**：研究表明，异常蛋白质的聚集在 ALS 发病中起着关键作用。例如，TAR DNA 结合蛋白 43（TDP - 43）在大部分 ALS 患者的大脑和脊髓运动神经元中存在异常聚集，形成不溶性包涵体，干扰细胞的正常功能，最终导致神经元死亡。此外，超氧化物歧化酶 1（SOD1）突变引起的蛋白质错误折叠和聚集也被认为是 ALS 发病的重要机制之一。\n", "- **神经炎症**：神经炎症在 ALS 发病过程中的作用受到越来越多的关注。小胶质细胞和星形胶质细胞的激活在 ALS 模型中被广泛观察到，这些激活的胶质细胞可以释放炎症因子，如肿瘤坏死因子 - α（TNF - α）、白细胞介素 - 6（IL - 6）等，进一步加重神经元的损伤和死亡。\n", "\n", "#### 诊断手段\n", "- **生物标志物研究**：寻找敏感和特异的生物标志物是 ALS 诊断研究的重要方向。近年来，一些潜在的生物标志物如神经丝轻链（NfL）在血液和脑脊液中的水平被证实与 ALS 的病情进展和预后相关。NfL 的检测有助于早期诊断和监测疾病的发展，为临床决策提供重要依据。\n", "- **影像学技术**：先进的磁共振成像（MRI）技术，如扩散张量成像（DTI）和磁共振波谱成像（MRS），在 ALS 诊断中的应用越来越广泛。DTI 可以检测脑白质纤维束的完整性，发现 ALS 患者早期大脑运动区的结构改变；MRS 能够检测脑内代谢产物的变化，为 ALS 的诊断和鉴别诊断提供更多信息。\n", "\n", "#### 治疗方法\n", "- **药物治疗新进展**：虽然目前 ALS 仍然缺乏根治性的治疗方法，但近年来有一些新的药物进入临床试验阶段。例如，Relyvrio（苯丁酸钠和牛磺酸二醇口服固定剂量复方制剂）于 2022 年在美国获批用于治疗 ALS，它可能通过调节细胞内蛋白质稳态等机制发挥治疗作用。此外，还有一些针对特定发病机制的药物，如针对神经炎症的药物、调节 RNA 代谢的药物等正在进行临床试验。\n", "- **基因治疗**：随着对 ALS 基因研究的深入，基因治疗成为一种有潜力的治疗策略。一些针对特定基因突变的基因疗法，如利用反义寡核苷酸（ASO）技术来调节致病基因的表达，在动物模型中显示出了一定的疗效，并正在逐步推进到临床试验阶段。\n", "- **非药物治疗**：呼吸支持和营养支持在 ALS 治疗中仍然非常重要。无创正压通气（NIPPV）可以改善患者的呼吸功能，提高生活质量；经皮胃造瘘术（PEG）则为患者提供了可靠的营养补充途径。此外，康复治疗、心理支持等也有助于提高患者的整体生活质量和功能状态。\n", "\n", "### 中美欧在诊疗技术上的差异\n", "\n", "#### 美国\n", "- **诊断技术**：美国在生物标志物检测方面具有领先地位，许多研究机构和医院能够开展包括 NfL 在内的多种生物标志物的检测，为早期诊断提供有力支持。在影像学诊断方面，拥有先进的 MRI 设备和技术，能够进行多模态的影像学评估，更准确地发现疾病早期的结构改变和功能异常。\n", "- **治疗方法**：美国是渐冻症新药研发和临床试验的重要中心，许多新型药物首先在美国开展临床试验并获得批准上市。例如，上述提到的 Relyvrio 就是首先在美国获批。此外，美国在基因治疗等前沿治疗领域的研究也处于领先地位，有多个针对 ALS 的基因治疗项目正在进行。\n", "\n", "#### 欧洲\n", "- **诊断技术**：欧洲在一些传统的诊断技术方面具有丰富的经验，如神经电生理检查技术非常成熟。同时，欧洲也在积极推动多中心合作研究，开展大规模的临床研究项目，以寻找更有效的诊断方法和生物标志物。例如，欧洲的一些大型研究项目在神经影像学和生物标志物联合应用方面取得了一定的成果。\n", "- **治疗方法**：欧洲在渐冻症的康复治疗方面有其特色，注重多学科团队的合作，为患者提供全面的康复方案。此外，欧洲也有一些独特的治疗方法和药物正在研究和应用中，例如一些基于传统医学理念的治疗方法在部分患者中取得了一定的效果。\n", "\n", "#### 中国\n", "- **诊断技术**：近年来，中国在渐冻症诊断技术方面取得了显著进步。大型医院逐渐普及了先进的影像学设备和神经电生理检查技术，能够开展多模态的诊断评估。同时，国内也在积极开展生物标志物的研究和检测工作，但与美国相比，在检测技术和应用范围上仍存在一定差距。\n", "- **治疗方法**：中国在渐冻症的治疗上以传统的药物治疗为主，同时也在积极引进和开展新型治疗方法的研究。在康复治疗方面，结合中医康复理念和现代康复技术，为患者提供个性化的康复方案。此外，中国在渐冻症的临床试验方面也在不断增加，但与美国和欧洲相比，新药研发和临床试验的规模和水平仍有待提高。\n", "\n", "### 当前中国在渐冻症临床实践中面临的核心挑战\n", "\n", "#### 早期诊断困难\n", "由于渐冻症早期症状不典型，容易与其他神经系统疾病混淆，导致早期诊断困难。而且目前国内部分基层医院对渐冻症的认识和诊断能力有限，缺乏先进的诊断技术和设备，使得很多患者在疾病晚期才得到确诊，错过了最佳治疗时机。\n", "\n", "#### 治疗手段有限\n", "虽然有一些新型治疗方法在研究和应用中，但总体而言，中国渐冻症的治疗手段仍然相对有限。传统的药物治疗效果有限，且部分药物价格昂贵，患者经济负担较重。基因治疗等前沿治疗方法在国内的应用还处于初步阶段，尚未广泛普及。\n", "\n", "#### 多学科协作不足\n", "渐冻症的治疗需要神经内科、呼吸科、营养科、康复科等多学科的协作，但目前国内多学科协作机制还不够完善，各学科之间的沟通和合作不够紧密，导致患者不能得到全面、系统的治疗。\n", "\n", "#### 患者支持体系不完善\n", "渐冻症是一种慢性、进行性的致残性疾病，患者和家属面临着巨大的心理和经济压力。目前国内针对渐冻症患者的支持体系还不够完善，缺乏专业的康复机构、护理服务和心理支持，患者的生活质量受到较大影响。\n", "\n", "#### 临床研究资源有限\n", "开展大规模、高质量的临床研究是推动渐冻症诊疗技术进步的关键。但目前中国在渐冻症临床研究方面的资源有限，包括研究经费、研究人才和研究样本等方面，这制约了国内新药研发和治疗方法的创新。\n"]}], "source": ["search_results_string= \"\\n\\n\".join(search_results)\n", "\n", "FINAL_ANSWER_PROMPT = textwrap.dedent(\n", "    \"\"\"\\\n", "    参考信息：{reference_results_string}\n", "    用户问题：{query}\n", "    请参考以上信息回复用户问题，需遵循以下要求：\n", "    1. 结合问题需求，对参考信息中的检索内容进行可用性判断，避免在回复中使用错误或不恰当的信息；\n", "    2. 优先根据官网、百科、权威机构、专业网站等高权威性来源的信息来回答问题，更多地参考其中的相关数字和案例。\n", "    3. 回复内容需覆盖用户需求的各个细节，尽可能的全面的解答问题，输出内容尽可能详细且结构化，按照用户需求的格式（如有）组织输出形式。\n", "    \"\"\"\n", ")\n", "final_response = chat(\n", "    FINAL_ANSWER_PROMPT.format(\n", "        reference_results_string=search_results_string,\n", "        query=query\n", "    ),\n", "    model,\n", "    max_tokens,\n", "    temperature,\n", "    top_p\n", ")\n", "print(f\"final answer:{final_response}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 5. Iterative Search Implementation\n", "\n", "Below is the example of the iterative search logic."]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "async def AI_Deep_Search(query):\n", "\n", "    # Generate task plan\n", "    task_plan = \"\"\n", "    TASK_PLAN_PROMPT = textwrap.dedent(\n", "        \"\"\"\\\n", "        # 任务\n", "        我是一个专业的研究员。我会根据给定的query需求，逐层深入分析，并以清晰易懂的语言组织成一份完整的研究方案规划，便于执行和实施。\n", "        ## 深度研究参考思路\n", "        1. 目标确认与基础信息收集\n", "            （1）确定研究目的：收集相关领域的信息，明确研究背景、细化通过研究想要达成的具体成果\n", "            （2）数据收集与整理：我会系统收集目标领域的历史数据和案例，将这些信息整理成标准化的内容，可选择用数据表格形态整理。关键是确保数据的完整性、准确性和时序性，为后续所有分析提供可靠的事实基础，数据收集必须覆盖足够的时间范围\\\n", "        ，包含所有相关的关键信息字段。\n", "        2. 深度分析与信息深度挖掘\n", "            （1）深度模式分析：基于收集到的数据，我会深入分析其中的新研究对象、关键模式、规律和趋势等。这包括频率统计、周期性变化、发展趋势等量化分析，目标是揭示隐藏在数据背后的内在逻辑和规律性特征。对于上一步中出现的新的重要概念或\\\n", "        实体，需对该类需要探究的内容进行二次信息搜集。分析结果尽可能用统计数据和可视化图表来呈现。\n", "            （2）核心驱动因素提取：通过对模式的深度分析，我需要识别出真正影响结果的核心驱动因素。这些因素需要按照影响力大小进行排序，并评估各自的权重。重点是找到那些具有决定性作用的关键变量，而不是表面的相关性因素。\n", "            （3）现实背景信息补强：针对已识别的核心驱动因素，我会收集当前相关的现实背景信息。这包括最新的政策变化、市场环境、技术发展、社会趋势等可能影响分析结果的现实因素。目标是将历史规律与当前实际情况相结合，确保分析的时效性和准\\\n", "        确性。\n", "        3. 研究成果产出\n", "            （1）综合推理与结论：最后将整合上述获取到的所有信息，运用严密的逻辑推理得出最终结论。这不仅包括具体的预测结果，还要包含完整的推理过程、逻辑链条、以及对结论可信度的评估。同时需要考虑可能存在的风险因素和不确定性。\n", "            （2）产出符合需求的交付形态：根据输入query需求或场景通用的形态，组织最终研究成果或调研报告，需包含上述研究过程的各类细节信息，\n", "        *以上思路步骤仅供参考，可根据实际需求或命题情况进行调整\n", "\n", "\n", "        ## query示例及样例输出：\n", "        **输入query1：**\n", "        \"请你深入研究基于事件的光流估计，梳理经典代表算法与最新的研究趋势，分析当前这个领域的瓶颈及可行的解决方案\"\n", "\n", "        **样例输出1：**\n", "        1. 资料收集\n", "            (1) 调研基于事件的视觉传感器（事件相机）的工作原理、特点及其与传统帧式相机的区别。\n", "            (2) 梳理基于事件的光流估计领域的经典代表算法，包括但不限于基于事件匹配、基于事件累积和基于学习的方法。\n", "        2. 深入研究并梳理趋势并识别挑战\n", "            (1) 深入分析基于事件的光流估计的最新研究趋势，例如与深度学习的结合、多模态融合以及在特定应用场景（如高速运动、低光照）下的进展。\n", "            (2) 识别并分析当前基于事件的光流估计领域面临的主要瓶颈和挑战，例如数据稀疏性、噪声敏感性、计算效率、泛化能力以及缺乏大规模标准数据集。\n", "        3. 瓶颈和方案探索\n", "            (1) 针对上述瓶颈和挑战，探讨可行的解决方案和未来研究方向，例如新型事件表示方法、更鲁棒的算法设计、高效的硬件加速以及跨领域知识迁移。\n", "            (2) 查找并分析基于事件的光流估计在实际应用中的案例，例如机器人导航、自动驾驶或工业检测，以了解其应用前景和实际需求。\n", "        4. 报告撰写与交付\n", "            （1）将研究成果整理成深入的研究报告，包括研究过程、结论和建议\n", "\n", "        **输入query2：**\n", "        \"请帮我梳理一下面向AIGC/扩散模型/大模型训练的数据集所有权保护方法，并分析当前的挑战，给出一些创新性的思路方案\"\n", "        **样例输出2：**\n", "        1. 信息收集和整理\n", "            (1) 梳理当前适用于人工智能训练数据所有权和知识产权保护的法律和技术框架。\n", "            (2) 深入研究专门针对AIGC、扩散模型和大型模型训练数据所有权保护的现有方法，例如数据水印、溯源技术、加密技术和合同协议。\n", "        2. 分析和方案制定\n", "            (1) 分析在大型AI模型训练中实施和执行这些数据所有权保护方法所面临的挑战，包括技术限制、法律模糊性及实际操作困难。\n", "            (2) 查找并评估与AI模型训练数据所有权保护相关的实际案例或争议，以了解其成功和失败的经验。\n", "            (3) 探索新兴技术和研究方向，如区块链、先进密码学或新型数字版权管理，它们如何为AI数据所有权保护提供创新解决方案。\n", "            (4) 思考在AI数据所有权领域可能出现的政策或监管创新，例如新的许可模式、集体权利管理或国际合作框架。\n", "        3. 报告撰写\n", "            (1) 综合上述研究，提出结合法律、技术和伦理考量的创新性概念框架或混合方案，以实现AIGC/大型模型训练数据的稳健所有权保护。\n", "\n", "        ## 更多应用场景：\n", "        这个框架同样适用于商业战略分析、市场趋势预测、投资价值评估、政策效果研究、产品发展规划等各种需要深度分析和预测的场景。无论是分析企业发展策略、预测行业发展趋势，还是评估投资机会，都会按照同样的五层逻辑进行系统化的深度研究。\n", "\n", "        ## 要求\n", "        我不会对输入query作出任何评价，也不会解释相关的动作，我只会生成方案规划，一定不要直接回复输入query。\n", "        我输出的规划是按步骤顺序组织的整体思路，每个步骤的规划用一两句话或一两个列表元素表示具体步骤，每个步骤的标题要严格和输入query相关，不要简单重复上述参考思路的小标题。\n", "        但对于一些简单的问题，不用输出详细的规划步骤（也不需要给出规划示例）；如果遇到一些困难的问题，我可能会更进一步地扩展深度研究的要求。\n", "\n", "        ## 当前输入query：{query}\n", "        \"\"\"\n", "    )\n", "\n", "    plan_prompt = TASK_PLAN_PROMPT.format(query=\"{query}\")\n", "    task_plan = chat(plan_prompt, model, max_tokens, temperature, top_p)\n", "    print(f\"task plan:{task_plan}\")\n", "\n", "    # Start iterative Search\n", "\n", "    current_iteration = 0\n", "    max_iterations = 5\n", "    search_results=[]\n", "\n", "    while current_iteration < max_iterations:\n", "        current_iteration += 1\n", "        # Step 1: Check if more information is needed\n", "        need_more_info = None\n", "\n", "        SEARCH_INFO_PROMPT = textwrap.dedent(\n", "            \"\"\"\\\n", "            现在需要你完成以下 **[深度研究]任务**：\n", "            **{query}**\n", "            为了完成该 [深度研究] 任务，你需要通过**动态、多次地调用检索工具**，以获取足够且相关的 [参考信息](工具返回的搜索结果)。\n", "            [参考信息] 必须满足以下两个要求：\n", "            1. 全面覆盖任务问题中的各个细节需求；\n", "            2. 对前序 [参考信息] 中涉及的延伸概念进行进一步的具体说明。\n", "\n", "            在信息量不足或理解尚不充分的情况下，请持续调用工具补充信息，直到所收集的信息足以支撑对任务问题的完整理解和回答为止。\n", "\n", "            你应当根据**历史检索结果**，判断下一步搜索的方向和重点，并决定是否需要继续调用工具。这一过程应具有**自适应性和递进性**。\n", "\n", "            请严格参照以下任务规划指导，辅助你进行任务执行：\n", "            ```\n", "            {task_plan}\n", "            ```\n", "            历史检索结果：\n", "            ```\n", "            {search_results_string}\n", "            ```\n", "\n", "            请以JSON格式输出，格式要求如下：\n", "            {{\n", "                \"reasoning\": \"决策理由\",\n", "                \"need_more_tool_info\": true/false\n", "            }}\n", "            注意：need_more_tool_info为true表示需要调用工具获取更多信息，若任务过于简单或需要向用户澄清获取信息时则为false。\n", "            \"\"\"\n", "        )\n", "        search_results_string= \"\\n\\n\".join(search_results)\n", "        content = chat(\n", "            SEARCH_INFO_PROMPT.format(\n", "                query=query, task_plan=task_plan, search_results_string=search_results_string\n", "            ),\n", "            model,\n", "            max_tokens,\n", "            temperature,\n", "            top_p\n", "        )\n", "\n", "        try:\n", "            start = content.find(\"{\")\n", "            end = content.rfind(\"}\") + 1\n", "            if start >= 0 and end > start:\n", "                content = content[start:end]\n", "            result = json.loads(content)\n", "            print(f\"Model decision:{result}\")\n", "        except json.JSONDecodeError:\n", "            print(\"错误：模型输出不是有效的JSON格式\")\n", "            continue\n", "\n", "        if not result:\n", "            continue\n", "\n", "        if not result.get(\"need_more_tool_info\", False):\n", "            print(\"信息搜集完毕，终止检索\")\n", "            break\n", "\n", "        print(f\"************开始第{current_iteration}次检索************\")\n", "        # Step 2: Generate search parameters\n", "        available_tools = json.dumps([{\n", "            \"name\": tool.name,\n", "            \"description\": tool.description,\n", "            \"input_schema\": tool.inputSchema\n", "        } for tool in client.tools], ensure_ascii=False, indent=2)\n", "\n", "        GEN_QUERY_PROMPT = textwrap.dedent(\n", "            \"\"\"\\\n", "            可用工具信息：\n", "            {available_tools}\n", "\n", "            历史检索结果：\n", "            ```\n", "            {search_results_string}\n", "            ```\n", "            用户问题：{query}\n", "\n", "            请严格参照以下任务规划指导，执行下一步工具调用：\n", "            ```\n", "            {task_plan}\n", "            ```\n", "\n", "            请根据以上历史检索结果判断回复用户问题所缺少的信息内容，或根据任务规划指导判断下一步需要完成的检索任务，调用合适的工具，生成新的检索query来补充所缺少的信息或需要执行的其他检索任务.\n", "            回复用户问题所需的完整参考信息需包含对用户问题中各细节需求及信息，以及参考信息中与需求相关的延伸概念的具体说明，同时需要完成任务规划中指导的各检索任务。\n", "            生成检索query的要求如下：\n", "            - 生成的检索query要与历史检索结果中的检索词不同，生成更深入的信息补充类query。\n", "            - 单个检索的query需独立为一个简单直接的搜索主题，不能过长或过于复杂，若有多个需要补充的信息，则拆分成多个query，通过json列表形式分成多次工具调用。\n", "\n", "            以下为生成本轮query的示例1：\n", "            ```\n", "            用户问题：帮我做竞品调研，深入了解当前Agent产品在旅行场景下如何识别、响应和解决用户的具体需求，包括需求识别的准确性、解决方案的有效性，以及在满足用户需求过程中面临的挑战。请结合当前市面上的产品和AI能力帮我输出详细的调研报告。\n", "            上一轮检索query为：\"旅行场景 AI Agent 产品 竞品\"\n", "            上一轮得的检索结果包括以下内容：\"Manus\"、\"飞猪问一问\"、\"字节豆包AI\"等Agent产品介绍\n", "            本轮生成query则应根据上一轮检索结果进行下一步有关产品细节的深度挖掘，生成如下检索query：\n", "            - Manus 需求应用案例\n", "            - 飞猪问一问 需求应用案例\n", "            - 字节豆包AI 需求应用案例\n", "            ```\n", "\n", "            以下为生成本轮query的示例2：\n", "            ```\n", "            用户问题：帮我做竞品调研，深入了解当前Agent产品在旅行场景下如何识别、响应和解决用户的具体需求，包括需求识别的准确性、解决方案的有效性，以及在满足用户需求过程中面临的挑战。请结合当前市面上的产品和AI能力帮我输出详细的调研报告。\n", "            上一轮检索query为：\"Manus 需求方案 应用案例\"、 \"飞猪问一问 需求方案 应用案例\"、\"字节豆包AI 需求方案 应用案例\"\n", "            上一轮得的检索结果包括以下内容：\"Manus相关产品方案和案例\"、\"飞猪问一问应用案例\"、\"字节豆包AI产品方案\"等产品案例内容\n", "            本轮生成query则应根据上一轮检索结果判断，缺少有关产品满足用户需求中面临的挑战相关内容，因此进行下一步检索，生成如下检索query：\n", "            - Manus 满足需求面临的挑战\n", "            - 飞猪问一问 满足需求面临的挑战\n", "            - 字节豆包AI 满足需求面临的挑战\n", "            ```\n", "\n", "            请根据用户问题和历史检索结果中的结果，参考任务规划指导进行工具调用，并参照上述示例生成恰当的参数，直接以JSON格式输出需要调用的工具名称（tool_name）和工具调用参数（tool_args）。\n", "            \"\"\"\n", "        )\n", "        content_text = chat(\n", "            GEN_QUERY_PROMPT.format(\n", "                available_tools=available_tools,\n", "                query=query,\n", "                task_plan=task_plan,\n", "                search_results_string=search_results_string,\n", "            ),\n", "            model,\n", "            max_tokens,\n", "            temperature,\n", "            top_p\n", "        )\n", "\n", "        try:\n", "            start = content_text.find(\"```json\") + 7\n", "            end = content_text.rfind(\"```\")\n", "            if start >= 0 and end > start:\n", "                content_text = content_text[start:end]\n", "            content = json.loads(content_text)\n", "            print(f\"Model decision:{content}\")\n", "        except json.JSONDecodeError:\n", "            print(\"错误：模型输出不是有效的JSON格式\")\n", "            continue\n", "\n", "        if not content:\n", "            continue\n", "\n", "        # Step 3: Call MCP server tool\n", "        search_result = []\n", "\n", "        for content_item in content:\n", "            tool_name = content_item.get(\"tool_name\")\n", "            tool_args = content_item.get(\"tool_args\")\n", "            if \"model\" in tool_args:\n", "                del tool_args[\"model\"]\n", "            for key in list(tool_args.keys()):  # 复制键，生成列表\n", "                if tool_args[key] == \"\" or tool_args[key] is None:\n", "                    del tool_args[key]\n", "            print(tool_args)\n", "            tool_result = await client.call_tool(tool_name, tool_args)\n", "            tool_result = \"\\n\\n\".join([text.text[:2000] for text in tool_result.content if text.type == \"text\"])\n", "            print(f\"工具调用结果(truncated):{tool_result}\")\n", "            search_result.append(f\"检索词：{tool_args.get('query')}\\n检索结果：\\n{tool_result}\\n\")\n", "            search_result_string = \"\\n\\n\".join(search_result)\n", "        search_results.append(f\"***************第{len(search_results) + 1}次检索结果*******************：\\n{search_result_string}\\n\")\n", "\n", "        print(f\"************第{current_iteration}次检索完成************\")\n", "\n", "    print(\"************输出最终结果***********\")\n", "\n", "    search_results_string= \"\\n\\n\".join(search_results)\n", "\n", "    FINAL_ANSWER_PROMPT = textwrap.dedent(\n", "        \"\"\"\\\n", "        参考信息：{reference_results_string}\n", "        用户问题：{query}\n", "        请参考以上信息回复用户问题，需遵循以下要求：\n", "        1. 结合问题需求，对参考信息中的检索内容进行可用性判断，避免在回复中使用错误或不恰当的信息；\n", "        2. 优先根据官网、百科、权威机构、专业网站等高权威性来源的信息来回答问题，更多地参考其中的相关数字和案例。\n", "        3. 回复内容需覆盖用户需求的各个细节，尽可能的全面的解答问题，输出内容尽可能详细且结构化，按照用户需求的格式（如有）组织输出形式。\n", "        \"\"\"\n", "    )\n", "    final_response = chat(\n", "        FINAL_ANSWER_PROMPT.format(\n", "            reference_results_string=search_results_string,\n", "            query=query\n", "        ),\n", "        model,\n", "        max_tokens,\n", "        temperature,\n", "        top_p\n", "    )\n", "    print(f\"final answer:{final_response}\")\n", "    return final_response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Example case"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["task plan:1. 文献与现状调研\n", "    (1) 系统收集近年来基于大语言模型（LLM）的文本摘要事实一致性评估方法的相关文献，涵盖学术会议论文、期刊论文、技术报告等。\n", "    (2) 整理并分析这些文献中的最新研究进展，提炼出具有代表性的研究工作，明确不同方法的核心思想、技术特点及优缺点。\n", "2. 无监督事实一致性评估技术路径、能力边界与挑战分析\n", "    (1) 对无监督事实一致性评估中的技术路径进行详细梳理，包括但不限于基于语义相似度、基于知识图谱、基于逻辑推理等方法。\n", "    (2) 评估不同技术路径在处理文本摘要事实一致性评估时的能力边界，明确各种方法在不同场景下的适用性。\n", "    (3) 全面分析当前无监督事实一致性评估面临的主要挑战，如语义理解的深度不足、知识覆盖的局限性、评估指标的合理性等。\n", "3. 创新思路提出\n", "    (1) 结合现有技术瓶颈，提出两至三个具有较高可行性的无监督事实一致性评估创新思路，如融合多模态信息的评估方法、基于对抗训练的评估模型等。\n", "    (2) 针对每个创新思路，详细阐述其设计理念、技术实现路径及预期效果。\n", "4. 创新思路框架图设计\n", "    (1) 为每个创新思路设计框架图，清晰展示整体流程与关键模块设计，包括输入、处理过程、输出等环节。\n", "    (2) 在框架图中明确标注各模块的功能及相互之间的关联，确保框架图的可读性和可理解性。\n", "5. 案例验证与效果评估（可选，根据实际需求与资源决定是否实施）\n", "    (1) 选取具有代表性的文本摘要数据集，对提出的创新思路进行实证研究，验证其有效性和可行性。\n", "    (2) 设计合理的评估指标，对创新思路在无监督事实一致性评估中的效果进行量化评估，对比不同方法之间的性能差异。\n", "6. 报告撰写与成果总结\n", "    (1) 将上述调研、分析、创新思路及框架图等内容整理成系统调研报告，确保报告内容完整、逻辑清晰、数据准确。\n", "    (2) 总结研究成果，明确未来研究方向，提出对基于大语言模型的文本摘要事实一致性评估方法的改进建议。\n", "Model decision:{'need_more_info': True, 'reasoning': '为了完成基于大语言模型（LLM）的文本摘要事实一致性评估方法的系统调研，首先需要系统收集近年来相关的学术会议论文、期刊论文、技术报告等文献，以全面覆盖任务问题中的各个细节需求。由于目前尚未进行任何文献检索，因此缺乏对最新研究进展、代表性工作以及无监督事实一致性评估中技术路径、能力边界与主要挑战的深入理解。为了支撑对任务问题的完整理解和回答，需要继续调用工具进行文献检索，以获取足够且相关的参考信息。'}\n", "************开始第1次检索************\n", "Model decision:[{'tool_name': 'AIsearch', 'tool_args': {'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 最新研究进展', 'instruction': '按照时间顺序列出近年来（如近五年）的相关研究进展'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 代表性工作', 'instruction': '列出该领域内具有代表性的工作，并简要描述其贡献'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 技术路径 能力边界 主要挑战', 'instruction': '详细分析当前无监督事实一致性评估中的技术路径、能力边界与主要挑战'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 创新思路', 'instruction': '列出两至三个具有较高可行性的无监督事实一致性评估创新思路，并简要描述每个思路的核心想法'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 创新思路 框架图设计', 'instruction': '针对之前提出的创新思路，输出每个思路的整体流程与关键模块设计的框架图相关参考信息'}}]\n", "{'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 最新研究进展', 'instruction': '按照时间顺序列出近年来（如近五年）的相关研究进展'}\n", "工具调用结果(truncated):Title: 基于大语言模型的文本摘要质量评估①\n", "Content: 计算机系统应用 ISSN 1003-3254, CODEN CSAOBN 2025,34(2):28−36 [doi: 10.15888/j.cnki.csa.009779] [CSTR: 32024.14.csa.009779] ©中国科学院软件研究所版权所有.   E-mail: <EMAIL> http://www.c-s-a.org.cn Tel: +86-10-62661041   基于大语言模型的文本摘要质量评估① 谭琛瀚1,2,  贾克斌1,2,  王浩宇1,2 1(北京工业大学 信息学部, 北京 100124) 2(先进信息网络北京实验室, 北京 100876) 通信作者: 贾克斌, E-mail: <EMAIL> 摘要: 自动文本摘要是自然语言处理 (NLP) 领域中的一个重要分支, 其主要难点之一是在于如何快速,客观且 准确地评估生成摘要的质量. 针对现有文本摘要质量评估方法中评估准确度不高,需要参考文本以及计算资源消 耗大的问题, 本文提出一种基于大语言模型的文本摘要质量评估方法, 设计基于思维链原理的提示词构建方法以提 高大语言模型在文本摘要质量评估任务上的性能, 同时生成思维链数据集并以模型微调的方式对小型大语言模型 进行训练, 显著降低了计算需求. 本文方法首先根据文本摘要的特点确定评估维度, 并基于思维链原理 (chain of thought, CoT) 构建提示词; 使用提示词对大型大语言模型进行引导, 使其根据摘要样本生成思维链过程与评估结 果, 同时以此为基础生成思维链数据集; 使用生成的思维链数据集对小型大语言模型进行微调训练; 最后使用微调 后的小型大语言模型完成文本摘要的质量评估任务. 本文在 Summeval 数据集上进行了对比实验与分析, 实验结果 表明, 本评估方法显著提高了小型大语言模型在文本摘要质量评估任务上的评估准确度, 实现了一种无需参考文 本,评估准确度高,计算需求低,便于部署的文本摘要质量评估方法. \n", "URL: https://c-s-a.org.cn/csaen/ch/reader/create_pdf.aspx?file_no=9779&year_id=2025&quarter_id=2\n", "\n", "Tit\n", "{'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 代表性工作', 'instruction': '列出该领域内具有代表性的工作，并简要描述其贡献'}\n", "工具调用结果(truncated):Title: 「LLM-综述」利用大语言模型进行自然语言生成评估的综述\n", "Content:  一、结论写在前面 论文调研了大语言模型在自然语言生成评估中的作用。论文按照三个维度对当前的研究进行分类:评估函数、评估参考和评估任务。论文深入研究了各种基于LLM的方法,仔细分析了它们的优势并比较了它们的区别。此外,论文总结了自然语言生成评估的常用评估基准。虽然LLM在评估自然语言生成输出方面提供了突破性的潜力,但仍然存在一些未解决的问题需要关注,包括偏见、鲁棒性、集成混合评估方法的需要,以及LLM评估器中需要特定领域和统一评估。预期解决这些挑战将为更普遍、更有效和更可靠的自然语言生成评估技术铺平道路。 图1:LLM用于NLG评估的示意图。虚线表示参考和来源基于场景是可选的 二、论文的简单介绍 2.1 论文的背景 随着NLG(Natural Language Generation,自然语言生成)技术的快速发展,建立可靠的评估方法以准确衡量生成内容的质量变得越来越重要。传统的NLG评估指标,如BLEU、ROUGE和TER,主要关注文本表层(surface-level)的差异,在评估语义方面往往不足。这一局限性被认为阻碍了研究进展,并可能导致误导性的研究结论。此外,其他采用神经嵌入(neural embeddings)来计算分数的方法,尽管评估了语义等价(semantic equivalence)和流畅性等方面,但灵活性有限,范围有限。另外,这些传统方法往往与人类判断存在较大差异,并且对分数缺乏解释性。这些缺点强调了NLG领域需要更细致和全面的评估方法的必要性。\n", "URL: https://baijiahao.baidu.com/s?id=1788679661289456142&wfr=spider&for=pc\n", "\n", "Title: 【论文解读】大模型事实性调查(上) \n", "Content:  本调查探讨了大型语言模型(llm)中的事实性的关键问题。随着llm在不同领域的应用,其输出的可靠性和准确性变得至关重要。论文将“事实性问题”定义为llm产生与既定事实不一致的内容的概率。然后,论文的讨论过渡到评估LLM事实性的方法,强调关键指标、基准和研究。论文的调查为研究人员提供了一个结构化的指导,旨在加强llm的事实可靠性。 在本节中,论文将描述大型语言模型中的事实性问题及其影响。 在文献中没有\n", "{'query': '无监督事实一致性评估 技术路径 能力边界 主要挑战', 'instruction': '详细分析当前无监督事实一致性评估中的技术路径、能力边界与主要挑战'}\n", "工具调用结果(truncated):Title: 无监督学习的未来趋势与挑战\n", "Content:  1.背景介绍 无监督学习是人工智能领域的一个重要分支,它涉及到从未经过训练的数据中提取知识的过程。无监督学习算法通常用于处理大量不完全标注的数据,以识别数据中的模式和结构。随着数据规模的增加和计算能力的提高,无监督学习技术在各个领域得到了广泛应用,如图像处理、自然语言处理、生物信息学等。 在本文中,我们将讨论无监督学习的核心概念、算法原理、具体实现以及未来的发展趋势和挑战。 2.核心概念与联系 无监督学习与监督学习是人工智能中两大主流的学习方法。监督学习需要预先标注的数据集来训练模型,而无监督学习则只依赖于未标注的数据。无监督学习可以帮助我们发现数据中的隐藏结构和关系,从而提高模型的性能。 无监督学习可以分为以下几类: 1.聚类分析:将数据分为多个群集,使得同一群集内的数据点相似度高,同时群集间的相似度低。 2.降维处理:将高维数据降至低维,使得数据的特征更加清晰和简洁。 3.异常检测:识别数据中的异常点,以便进一步分析和处理。 4.自组织映射:将高维数据映射到低维空间,以便更好地可视化和分析。 3.核心算法原理和具体操作步骤以及数学模型公式详细讲解 3.1聚类分析 聚类分析是无监督学习中最常用的方法之一,它旨在根据数据点之间的相似度将其划分为多个群集。常见的聚类算法有K均值算法、DBSCAN算法和层次聚类算法等。 3.1.1K均值算法 K均值算法是一种迭代的聚类算法,它的核心思想是将数据点分为K个群集,使得每个群集的内部相似度最大,同时群集间的相似度最小。 具体步骤如下: 1.随机选择K个数据点作为初始的聚类中心。 2.将每个数据点分配到与其距离最近的聚类中心所在的群集中。 3.计算每个聚类中心的新位置,使其为该群集中所有数据点的平均值。 4.重复步骤2和3,直到聚类中心的位置不再变化或达到最大迭代次数。 K均值算法的数学模型公式为: $$ \\\\min{C} \\\\sum{k=1}^{K} \\\\sum{x \\\\in Ck} \\\\|x - c_k\\\\|^2 $$ 其中,$C$ 表示聚类中心,$Ck$ 表示第k个聚类中心,$ck$ 表示第k个聚类中心的位置,$x$ 表示数据点。 3.1.2DBSCAN算法 DBSCAN(Density-Based Spatial Clustering of Applications wi\n", "{'query': '无监督事实一致性评估 创新思路', 'instruction': '列出两至三个具有较高可行性的无监督事实一致性评估创新思路，并简要描述每个思路的核心想法'}\n", "工具调用结果(truncated):Title: 无监督模型评估\n", "Content:  1.无监督学习的定义:无监督学习是一种机器学习范式,旨在从无标签数据中发现潜在的结构、模式和关系。它不依赖于预先定义的目标或标签,而是通过数据自身的特征和关系来进行分析和推断。 2.无监督模型的目标:主要包括聚类、降维、异常检测等。聚类旨在将数据分成若干个不相交的簇,使得同一簇内的数据具有较高的相似性,而不同簇之间的数据差异较大;降维则是通过减少数据的维度,保留数据的主要信息,以便更好地进行分析和可视化;异常检测则是检测数据中的异常点或离群值,这些点可能表示数据中的异常情况或错误。 3.无监督模型的应用领域:广泛应用于数据分析、数据挖掘、模式识别、图像处理、自然语言处理等领域。在数据分析中,可以用于发现数据中的潜在分组和趋势;在数据挖掘中,可以用于挖掘大规模数据中的隐藏模式和关联;在模式识别中,可以用于识别数据中的模式和特征;在图像处理中,可以用于图像分割、特征提取等;在自然语言处理中,可以用于文本聚类、主题模型构建等。\n", "URL: https://www.renrendoc.com/paper/357941713.html\n", "\n", "Title: 推荐项目:评估摘要事实一致性模型\n", "Content:  推荐项目:评估摘要事实一致性模型 项目地址:https://gitcode.com/gh_mirrors/fa/factCC 该项目是由Wojciech Kryściński等人提出的,专注于评估抽象文本总结的合理性,特别是检查生成的摘要是否与源文档在事实上保持一致。传统评估方法忽视了这一点,而该项目提供了一种弱监督、基于模型的方法,用于事实一致性验证和识别文档与摘要之间的冲突。通过一系列规则转换生成训练数据,然后训练模型执行三个任务:判断句子变换后是否仍保持一致,提取支持一致性的源文档片段,以及(如果存在)提取不一致的摘要片段。 该模型采用了创新的数据生成策略,利用了如回译、代词替换等变换来模拟可能的事实偏差。同时,模型被设计为同时处理这三个相关任务,这提高了其对事实一致性的识别能力。此外,模型还显示出了在多个最先进的摘要生成模型上显著优于前代模型的能力,即使是在仅使用少量标注数据的情况下。 这个项目适用于自然语言处理领域,特别是文本摘要和信息检索的开发者和研究人员。它可以帮助改进摘要算法,确保生成的内容既精炼又准确。对于新闻\n", "{'query': '无监督事实一致性评估 创新思路 框架图设计', 'instruction': '针对之前提出的创新思路，输出每个思路的整体流程与关键模块设计的框架图相关参考信息'}\n", "工具调用结果(truncated):Title: 机器学习实战之数据预处理、监督算法、无监督算法、模型评估与改进-思维导图拆分篇\n", "Content:  数据预处理包括:数据处理、转换、特征工程、降为可视化、管道; 监督算法包括:线性回归、梯度下降回归、岭回归、lasoo、逻辑回归、决策树、k近邻、朴素贝叶斯、支持向量机(线性、核函数)、多分类模型OVO\\\\OVR、梯度提升树、XGBoost、随机森林、极端随机数、stacking; 无监督算法包括:k均值聚类、凝聚聚类、DBSCAN以及聚类评估方法; 模型评估与改进:不平衡数据的处理、二分类、多分类评估指标、交叉验证、网格搜索。\n", "URL: https://blog.csdn.net/2302_80425184/article/details/146462966\n", "\n", "Title: 自监督训练超越传统微调!论文作者授权解读自监督革命性研究!\n", "Content:  (1)纯无监督自我训练框架——Genius 这篇论文的核心创新在于提出了一个无任何外部监督信号的自我训练框架——Genius。它只需要\\\"通用场景下的自然语言查询\\\"(即并不带标准答案或评价指标的普通文本问题),就能在推理能力上\\\"自学\\\"并提升模型性能。 (2)引入\\\"前瞻式(foresight)重采样\\\"策略 为了克服自回归生成过程容易\\\"鼠目寸光\\\"的问题,论文提出了一个关键策略:在每个推理步骤,不仅生成若干候选步骤,还会\\\"向前模拟\\\"可能出现的后续步骤,从而给当前的候选步骤一个更具全局意识的\\\"前瞻打分\\\"(foresight score)。随后利用这些打分进行重采样,一方面进行探索(确保多样性),另一方面则做出\\\"最优\\\"选择用于训练(确保质量)。 (3)优势值校准的自训练优化(Advantage-Calibrated Optimization, ACO) 在无监督自我训练时,难免会因为打分不准、未来步骤模拟不完备等因素产生噪声或估计偏差。论文提出了一个\\\"优势值校准\\\"的优化损失函数(ACO),通过\\\"对比正负样本的前瞻得分差值\\\"来调节训练时的梯度惩罚,减少不准确打分带来的影响,并在无监督场景下依旧能保持模型训练的稳定和性能的提升。 (4)在多任务、大范围评测中的有效性与可扩展性 在没有任何外部标注回答或额外监督的情况下,Genius仅使用通用数据(25K或32K条无标注查询)就能在多种推理任务(例如数学、逻辑、常识等)上获\n", "Model decision:{'need_more_info': True, 'reasoning': '根据目前的检索结果，关于基于大语言模型（LLM）的文本摘要事实一致性评估方法，已获得了一些有价值的信息，包括相关文献的摘要和部分代表性工作的介绍。然而，这些信息主要聚焦于LLM在自然语言处理及文本摘要质量评估中的一般应用和挑战，而对于无监督事实一致性评估的具体技术路径、能力边界及主要挑战的深入分析还不够详尽。特别是，缺乏对近年来无监督事实一致性评估中技术路径的详细梳理、不同方法核心思想的提炼、技术特点的比较，以及这些方法在不同场景下的适用性评估。\\\\n\\\\n此外，关于无监督事实一致性评估的创新思路，当前检索结果仅提供了一些无监督学习和模型评估的一般性介绍，以及一个具体的评估摘要事实一致性模型的项目地址，但并未给出具体的创新思路及其框架图设计。\\\\n\\\\n因此，为了完成本次深度研究任务，需要进一步检索相关信息，特别是关于无监督事实一致性评估的具体技术路径、代表性工作的深入分析、当前面临的主要挑战，以及可能的创新思路和框架图设计。这些信息将有助于更全面地理解无监督事实一致性评估的现状，并提出更具可行性的创新思路。'}\n", "************开始第2次检索************\n", "Model decision:[{'tool_name': 'AIsearch', 'tool_args': {'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 最新研究进展 2023年至今', 'instruction': '只返回2023年至今的基于大语言模型（LLM）的文本摘要事实一致性评估方法的最新研究进展相关内容'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 代表性工作', 'instruction': '详细梳理基于大语言模型（LLM）的文本摘要事实一致性评估方法的代表性工作，返回详细方法描述'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 技术路径', 'instruction': '详细分析无监督事实一致性评估的技术路径'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 能力边界', 'instruction': '详细总结无监督事实一致性评估的能力边界'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 主要挑战', 'instruction': '详细总结无监督事实一致性评估面临的主要挑战'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 创新思路', 'instruction': '返回两至三个具有较高可行性的无监督事实一致性评估创新思路，并包含每个思路的整体流程与关键模块设计的概述'}}]\n", "{'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 最新研究进展 2023年至今', 'instruction': '只返回2023年至今的基于大语言模型（LLM）的文本摘要事实一致性评估方法的最新研究进展相关内容'}\n", "工具调用结果(truncated):Title: 弗吉尼亚大学团队打造可靠生物医学假设生成评估工具\n", "Content:  首先,验证生成假设的准确性通常需要大量时间和资源。这就像要验证一个谣言是否属实,可能需要进行耗时的事实核查。 其次,大语言模型的\\\"幻觉\\\"问题可能导致生成看似合理但实际上错误的假设,这会严重影响其可靠性。这就像一个善于讲故事的人,能让虚构的事件听起来栩栩如生,让人难以分辨真假。\n", "URL: https://www.163.com/dy/article/K17LIF7105118UGF.html\n", "\n", "Title: 基于大语言模型的文本摘要质量评估①\n", "Content: 计算机系统应用 ISSN 1003-3254, CODEN CSAOBN 2025,34(2):28−36 [doi: 10.15888/j.cnki.csa.009779] [CSTR: 32024.14.csa.009779] ©中国科学院软件研究所版权所有.   E-mail: <EMAIL> http://www.c-s-a.org.cn Tel: +86-10-62661041   基于大语言模型的文本摘要质量评估① 谭琛瀚1,2,  贾克斌1,2,  王浩宇1,2 1(北京工业大学 信息学部, 北京 100124) 2(先进信息网络北京实验室, 北京 100876) 通信作者: 贾克斌, E-mail: <EMAIL> 摘要: 自动文本摘要是自然语言处理 (NLP) 领域中的一个重要分支, 其主要难点之一是在于如何快速,客观且 准确地评估生成摘要的质量. 针对现有文本摘要质量评估方法中评估准确度不高,需要参考文本以及计算资源消 耗大的问题, 本文提出一种基于大语言模型的文本摘要质量评估方法, 设计基于思维链原理的提示词构建方法以提 高大语言模型在文本摘要质量评估任务上的性能, 同时生成思维链数据集并以模型微调的方式对小型大语言模型 进行训练, 显著降低了计算需求. 本文方法首先根据文本摘要的特点确定评估维度, 并基于思维链原理 (chain of thought, CoT) 构建提示词; 使用提示词对大型大语言模型进行引导, 使其根据摘要样本生成思维链过程与评估结 果, 同时以此为基础生成思维链数据集; 使用生成的思维链数据集对小型大语言模型进行微调训练; 最后使用微调 后的小型\n", "{'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 代表性工作', 'instruction': '详细梳理基于大语言模型（LLM）的文本摘要事实一致性评估方法的代表性工作，返回详细方法描述'}\n", "工具调用结果(truncated):Title: 「LLM-综述」利用大语言模型进行自然语言生成评估的综述\n", "Content:  一、结论写在前面 论文调研了大语言模型在自然语言生成评估中的作用。论文按照三个维度对当前的研究进行分类:评估函数、评估参考和评估任务。论文深入研究了各种基于LLM的方法,仔细分析了它们的优势并比较了它们的区别。此外,论文总结了自然语言生成评估的常用评估基准。虽然LLM在评估自然语言生成输出方面提供了突破性的潜力,但仍然存在一些未解决的问题需要关注,包括偏见、鲁棒性、集成混合评估方法的需要,以及LLM评估器中需要特定领域和统一评估。预期解决这些挑战将为更普遍、更有效和更可靠的自然语言生成评估技术铺平道路。 图1:LLM用于NLG评估的示意图。虚线表示参考和来源基于场景是可选的 二、论文的简单介绍 2.1 论文的背景 随着NLG(Natural Language Generation,自然语言生成)技术的快速发展,建立可靠的评估方法以准确衡量生成内容的质量变得越来越重要。传统的NLG评估指标,如BLEU、ROUGE和TER,主要关注文本表层(surface-level)的差异,在评估语义方面往往不足。这一局限性被认为阻碍了研究进展,并可能导致误导性的研究结论。此外,其他采用神经嵌入(neural embeddings)来计算分数的方法,尽管评估了语义等价(semantic equivalence)和流畅性等方面,但灵活性有限,范围有限。另外,这些传统方法往往与人类判断存在较大差异,并且对分数缺乏解释性。这些缺点强调了NLG领域需要更细致和全面的评估方法的必要性。\n", "URL: https://baijiahao.baidu.com/s?id=1788679661289456142&wfr=spider&for=pc\n", "\n", "Title: 【论文解读】大模型事实性调查(上) \n", "Content:  本调查探讨了大型语言模型(llm)中的事实性的关键问题。随着llm在不同领域的应用,其输出的可靠性和准确性变得至关重要。论文将“事实性问题”定义为llm产生与既定事实不一致的内容的概率。然后,论文的讨论过渡到评估LLM事实性的方法,强调关键指标、基准和研究。论文的调查为研究人员提供了一个结构化的指导,旨在加强llm的事实可靠性。 在本节中,论文将描述大型语言模型中的事实性问题及其影响。 在文献中没有\n", "{'query': '无监督事实一致性评估 技术路径', 'instruction': '详细分析无监督事实一致性评估的技术路径'}\n", "工具调用结果(truncated):Title: 推荐项目:评估摘要事实一致性模型\n", "Content:  推荐项目:评估摘要事实一致性模型 项目地址:https://gitcode.com/gh_mirrors/fa/factCC 该项目是由Wojciech Kryściński等人提出的,专注于评估抽象文本总结的合理性,特别是检查生成的摘要是否与源文档在事实上保持一致。传统评估方法忽视了这一点,而该项目提供了一种弱监督、基于模型的方法,用于事实一致性验证和识别文档与摘要之间的冲突。通过一系列规则转换生成训练数据,然后训练模型执行三个任务:判断句子变换后是否仍保持一致,提取支持一致性的源文档片段,以及(如果存在)提取不一致的摘要片段。 该模型采用了创新的数据生成策略,利用了如回译、代词替换等变换来模拟可能的事实偏差。同时,模型被设计为同时处理这三个相关任务,这提高了其对事实一致性的识别能力。此外,模型还显示出了在多个最先进的摘要生成模型上显著优于前代模型的能力,即使是在仅使用少量标注数据的情况下。 这个项目适用于自然语言处理领域,特别是文本摘要和信息检索的开发者和研究人员。它可以帮助改进摘要算法,确保生成的内容既精炼又准确。对于新闻媒体、数据分析公司或任何需要自动摘要的组织来说,这是一个有价值的工具,可以提高信息准确性和可信度。 弱监督学习:不需要大量人工标注数据,通过规则变换生成训练集。 多任务联合学习:模型同时处理语义一致性判断、来源证据抽取和摘要中矛盾点定位,提高了检测效率。 高度可扩展性:能有效应用于现有先进模型的评估,揭示潜在的事实不一致性问题。 人性化评估:辅助的任务有助于人类验证事实一致性,增强了系统的实用性。 这个项目是研究文本摘要质量提升的重要一步,对于理解并改进自然语言处理系统的行为具有重要意义。如果你正致力于自然语言处理技术或者对此领域感兴趣,那么这个项目绝对值得你一试。\n", "URL: https://blog.csdn.net/gitblog_00042/article/details/139191879\n", "\n", "Title: 无监督角度感知突破数据瓶颈,端到端自动驾驶具备大模型scaling law \n", "Content:  ●前面的子任务需要大量高质量的3D标注作为监督,这对扩展训练数据构成了重大障碍; ●每个子模块在训练和推理中都需要大量的计算开销。\n", "URL: https://mp.we\n", "{'query': '无监督事实一致性评估 能力边界', 'instruction': '详细总结无监督事实一致性评估的能力边界'}\n", "工具调用结果(truncated):Title: 无监督BEV SOTA:挖掘BEV潜力边界 \n", "Content: 首先,它提出了一种基于查询的领Domain Adaptation策略,这种策略利用了图像视图特征和BEV特征的互补性,适用于无监督的BEV感知适应。其次,它设计了DA-BEV,这是一种引入基于查询的对抗学习和基于查询的自训练的框架,有效地联合解决了领域自适应BEV感知的问题。最后,通过广泛的实验,DA-BEV在不同数据集和任务(如3D物体检测和3D场景分割)上展示了其在BEV感知适应方面的优越性能。\n", "URL: https://mp.weixin.qq.com/s?__biz=Mzk0NzE4MTQwMg==&mid=2247527999&idx=2&sn=f3afe43791401e15024bd8a23e70e269&chksm=c2e775e068a8e234b822b4be3c384b986a10055197e3067e84a8636b2134bd8c97db7b4c0cb1&scene=27\n", "\n", "Title: 无监督学习评估标准 无监督评价指标\n", "Content:  一、聚类的形式限制 从直观的角度来看,聚类问题有一个非常明确的目标;也就是说,正确地聚类一组未标记的数据。尽管“聚类”的概念具有直观的吸引力,但它无法精确定义,因此已经提出了各种各样的聚类算法。 A. 簇的理想特性 Jon <PERSON>berg提出了三个公理,强调了一个分组问题应该表现出来的特征,并且可以被认为是“好的”,而不依赖于用于寻找解决方案的算法。这些公理分别是尺度不变性(scale invariance)、一致性(consistency)和丰富性(richness)[3],下面将详细说明。 定义分组函数为 ( 个点)和点对之间的距离的集合。点的集合为 ,点之间的距离由距离函数 给出,其中 。距离函数 测量点对之间的不相似性。例如,欧几里得距离(Euclidean)、曼哈顿距离(Manhattan)、切比雪夫距离(Chebyshev)和马氏距离(Mahalanobis)等等都可以使用。或者,也可以使用相似函数。 1)尺度不变性(Scale Invariance) Kleinberg 的第一个公理表明,对于任何距离函数 和任何比例因子 , 。 这个公理表明,当所有点之间的距离由常数 2)丰富性(Richness) 当 \n", "{'query': '无监督事实一致性评估 主要挑战', 'instruction': '详细总结无监督事实一致性评估面临的主要挑战'}\n", "工具调用结果(truncated):Title: 人工智能:无监督学习的挑战\n", "Content:  1.缺乏标签 挑战: 无监督学习没有标签数据,缺乏衡量模型性能的标准。 影响: 结果验证困难,导致生成模型的可信度降低。 策略: 半监督学习:将少量标记数据结合大量无标记数据进行训练。 自监督学习:设计辅助任务(如图像旋转预测),自动生成标签。 交叉验证:通过数据多样性或领域知识进行间接验证 2.模型选择 挑战:无监督学习算法种类繁多。 影响: 不同任务需要不同算法,选择最优模型的困难 策略: 基准测试:使用公开数据集和基准测试评估不同模型。 理论分析:基于算法理论和数据特性选择模型。 试验与反馈:通过小规模实验获得初步结果,调整模型选择。 3.结果解释 挑战: 聚类或降维结果难以解释。 影响: 需要理解所得到的模式和结构对实际应用的意义。 策略: 可视化技术:使用降维和聚类结果可视化,增强解释性。 领域知识集成:结合领域知识解释模型结果。 用户调研:通过专家或用户反馈优化和解释结果。 4.维度诅咒 挑战: 高维数据中,数据点之间的距离难以度量。 影响: 导致聚类和降维效果的降低。 策略: 特征降维:使用PCA、t-SNE等降维技术减少维度。 特征选择:选择最具信息量的特征。 正则化:在模型训练中使用正则化项防止过拟合。 5.噪声与异常值 挑战: 无监督学习对噪声和异常值敏感。 影响: 不准确的模型生成结果,影响数据关联的可靠性。 策略: 数据清洗:在预处理阶段剔除或修正异常数据。 鲁棒性算法:选择对噪声不敏感的算法。 噪声识别模型:在模型中集成用于识别和处理噪声的模块。 6.过拟合与泛化能力 挑战: 模型易于找到噪声而非数据结构。 影响: 降低在新数据上的泛化能力。 策略: 交叉验证:使用交叉验证评估模型泛化能力。 正则化:通过正则化技术限制模型的复杂度。 集成学习:使用多个模型组合提高泛化能力 7.超参数调优 挑战: 多数算法依赖超参数设置,选择合适的超参数困难。 影响: 不同设置可能导致不同结果。 策略: 自动化调参:使用Grid Search、Random Search或贝叶斯优化进行调参。 动态调整:在训练过程中动态调整超参数。 实践经验:\n", "URL: https://blog.csdn.net/Wei_sx/article/details/142680873\n", "\n", "Title: 什么是无监督学习\n", "C\n", "{'query': '无监督事实一致性评估 创新思路', 'instruction': '返回两至三个具有较高可行性的无监督事实一致性评估创新思路，并包含每个思路的整体流程与关键模块设计的概述'}\n", "工具调用结果(truncated):Title: 无监督模型评估\n", "Content:  1.无监督学习的定义:无监督学习是一种机器学习范式,旨在从无标签数据中发现潜在的结构、模式和关系。它不依赖于预先定义的目标或标签,而是通过数据自身的特征和关系来进行分析和推断。 2.无监督模型的目标:主要包括聚类、降维、异常检测等。聚类旨在将数据分成若干个不相交的簇,使得同一簇内的数据具有较高的相似性,而不同簇之间的数据差异较大;降维则是通过减少数据的维度,保留数据的主要信息,以便更好地进行分析和可视化;异常检测则是检测数据中的异常点或离群值,这些点可能表示数据中的异常情况或错误。 3.无监督模型的应用领域:广泛应用于数据分析、数据挖掘、模式识别、图像处理、自然语言处理等领域。在数据分析中,可以用于发现数据中的潜在分组和趋势;在数据挖掘中,可以用于挖掘大规模数据中的隐藏模式和关联;在模式识别中,可以用于识别数据中的模式和特征;在图像处理中,可以用于图像分割、特征提取等;在自然语言处理中,可以用于文本聚类、主题模型构建等。\n", "URL: https://www.renrendoc.com/paper/357941713.html\n", "\n", "Title: 推荐项目:评估摘要事实一致性模型\n", "Content:  推荐项目:评估摘要事实一致性模型 项目地址:https://gitcode.com/gh_mirrors/fa/factCC 该项目是由Wojciech Kryściński等人提出的,专注于评估抽象文本总结的合理性,特别是检查生成的摘要是否与源文档在事实上保持一致。传统评估方法忽视了这一点,而该项目提供了一种弱监督、基于模型的方法,用于事实一致性验证和识别文档与摘要之间的冲突。通过一系列规则转换生成训练数据,然后训练模型执行三个任务:判断句子变换后是否仍保持一致,提取支持一致性的源文档片段,以及(如果存在)提取不一致的摘要片段。 该模型采用了创新的数据生成策略,利用了如回译、代词替换等变换来模拟可能的事实偏差。同时,模型被设计为同时处理这三个相关任务,这提高了其对事实一致性的识别能力。此外,模型还显示出了在多个最先进的摘要生成模型上显著优于前代模型的能力,即使是在仅使用少量标注数据的情况下。 这个项目适用于自然语言处理领域,特别是文本摘要和信息检索的开发者和研究人员。它可以帮助改进摘要算法,确保生成的内容既精炼又准确。对于新闻\n", "Model decision:{'need_more_info': False, 'reasoning': '根据提供的参考信息，已经获取了基于大语言模型（LLM）的文本摘要事实一致性评估方法的最新研究进展，包括代表性工作、无监督事实一致性评估的技术路径、能力边界与主要挑战。具体涵盖了利用大语言模型进行自然语言生成评估的综述、无监督学习在事实一致性评估中的应用、以及一些具体的项目和模型，如评估摘要事实一致性模型factCC。同时，也了解到无监督学习在面临缺乏标签、模型选择、结果解释、维度诅咒、噪声与异常值、过拟合与泛化能力、超参数调优等方面的挑战。这些信息足以支撑对无监督事实一致性评估的系统调研，包括技术路径梳理、能力边界与挑战分析，以及提出创新思路和框架图设计。因此，不需要进一步调用检索工具获取更多信息。'}\n", "************输出最终结果***********\n", "final answer:### 基于大语言模型的文本摘要事实一致性评估调研报告\n", "\n", "#### 一、最新研究进展与代表性工作\n", "\n", "**最新研究进展**\n", "\n", "近年来，基于大语言模型（LLM）的文本摘要质量评估及事实一致性评估成为自然语言处理（NLP）领域的研究热点。2025年《计算机系统应用》期刊上的一篇论文《基于大语言模型的文本摘要质量评估》提出了一种新的评估方法，该方法通过思维链原理（Chain of Thought, CoT）构建提示词，引导大型LLM生成思维链过程与评估结果，进而生成思维链数据集用于微调小型LLM，显著降低了计算需求并提高了评估准确度。该研究在Summeval数据集上的对比实验证明了方法的有效性。\n", "\n", "**代表性工作**\n", "\n", "1. **大语言模型在自然语言生成评估中的综述**：该综述论文从评估函数、评估参考和评估任务三个维度对现有基于LLM的NLG评估方法进行了分类和比较，指出了LLM在评估自然语言生成输出方面的突破性潜力，同时也提出了偏见、鲁棒性、集成混合评估方法的需要等挑战。\n", "\n", "2. **大模型事实性调查**：该调查聚焦于LLM中的事实性问题，定义了事实性问题为LLM产生与既定事实不一致内容的概率，并讨论了评估LLM事实性的方法、关键指标和基准，旨在加强LLM的事实可靠性。\n", "\n", "#### 二、无监督事实一致性评估的技术路径、能力边界与主要挑战\n", "\n", "**技术路径**\n", "\n", "在无监督事实一致性评估方面，一个具有代表性的项目是factCC，该项目由Wojciech Kryściński等人提出，专注于评估抽象文本总结的合理性，特别是检查生成的摘要是否与源文档在事实上保持一致。项目采用弱监督、基于模型的方法，通过规则转换生成训练数据，训练模型执行判断句子一致性、提取支持一致性的源文档片段和提取不一致的摘要片段三个任务，显著提高了事实一致性的识别能力。\n", "\n", "**能力边界**\n", "\n", "无监督事实一致性评估的能力边界主要体现在对事实偏差的捕捉能力和对复杂文本结构的理解能力上。尽管factCC等模型在现有实验中表现出色，但在处理高度复杂或领域特定的文本时，其性能可能会受到限制。\n", "\n", "**主要挑战**\n", "\n", "1. **缺乏标签数据**：无监督学习没有标签数据，结果验证困难，导致生成模型的可信度降低。\n", "2. **模型选择**：无监督学习算法种类繁多，不同任务需要不同算法，选择最优模型的难度大。\n", "3. **结果解释**：聚类或降维结果难以解释，影响实际应用的意义理解。\n", "4. **维度诅咒**：高维数据中，数据点之间的距离难以度量，导致聚类和降维效果的降低。\n", "5. **噪声与异常值**：无监督学习对噪声和异常值敏感，影响数据关联的可靠性。\n", "\n", "#### 三、无监督事实一致性评估创新思路\n", "\n", "**创新思路一：基于自监督训练的事实一致性评估**\n", "\n", "**整体流程**\n", "\n", "1. **数据预处理**：收集大量无标签的文本数据，进行清洗和预处理。\n", "2. **自监督任务设计**：设计一系列自监督任务，如句子重组、代词替换等，模拟可能的事实偏差。\n", "3. **模型训练**：使用自监督任务生成的数据训练模型，使其学会识别和纠正事实偏差。\n", "4. **事实一致性评估**：将训练好的模型应用于文本摘要的事实一致性评估任务。\n", "\n", "**关键模块设计**\n", "\n", "- **自监督任务生成模块**：负责生成自监督任务的数据。\n", "- **模型训练模块**：负责训练模型以识别和纠正事实偏差。\n", "- **事实一致性评估模块**：负责评估文本摘要的事实一致性。\n", "\n", "**创新思路二：基于多模态融合的事实一致性评估**\n", "\n", "**整体流程**\n", "\n", "1. **多模态数据收集**：收集文本、图像、音频等多模态数据，构建多模态数据集。\n", "2. **多模态特征提取**：使用预训练模型提取多模态数据的特征。\n", "3. **多模态融合**：将多模态特征进行融合，形成综合特征表示。\n", "4. **事实一致性评估**：基于综合特征表示进行文本摘要的事实一致性评估。\n", "\n", "**关键模块设计**\n", "\n", "- **多模态数据收集模块**：负责收集多模态数据。\n", "- **多模态特征提取模块**：负责提取多模态数据的特征。\n", "- **多模态融合模块**：负责将多模态特征进行融合。\n", "- **事实一致性评估模块**：负责评估文本摘要的事实一致性。\n", "\n", "**创新思路三：基于知识图谱的事实一致性评估**\n", "\n", "**整体流程**\n", "\n", "1. **知识图谱构建**：构建领域特定的知识图谱，包含实体、关系和属性等信息。\n", "2. **文本摘要解析**：对文本摘要进行解析，提取实体和关系信息。\n", "3. **知识图谱匹配**：将提取的实体和关系信息与知识图谱进行匹配，检查一致性。\n", "4. **事实一致性评估**：基于匹配结果进行文本摘要的事实一致性评估。\n", "\n", "**关键模块设计**\n", "\n", "- **知识图谱构建模块**：负责构建领域特定的知识图谱。\n", "- **文本摘要解析模块**：负责解析文本摘要，提取实体和关系信息。\n", "- **知识图谱匹配模块**：负责将提取的实体和关系信息与知识图谱进行匹配。\n", "- **事实一致性评估模块**：负责评估文本摘要的事实一致性。\n", "\n", "#### 四、框架图设计\n", "\n", "**基于自监督训练的事实一致性评估框架图**\n", "\n", "```plaintext\n", "+-------------------+\n", "|   数据预处理      |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 自监督任务设计    |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "|   模型训练        |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 事实一致性评估    |\n", "+-------------------+\n", "```\n", "\n", "**基于多模态融合的事实一致性评估框架图**\n", "\n", "```plaintext\n", "+-------------------+\n", "| 多模态数据收集    |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 多模态特征提取    |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "|   多模态融合      |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 事实一致性评估    |\n", "+-------------------+\n", "```\n", "\n", "**基于知识图谱的事实一致性评估框架图**\n", "\n", "```plaintext\n", "+-------------------+\n", "|   知识图谱构建    |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 文本摘要解析      |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 知识图谱匹配      |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 事实一致性评估    |\n", "+-------------------+\n", "```\n", "\n", "以上是对于基于大语言模型（LLM）的文本摘要事实一致性评估的系统调研，重点梳理了近年来的最新研究进展与代表性工作，并全面分析了当前在无监督事实一致性评估中的技术路径、能力边界与主要挑战。在此基础上，提出了三个具有较高可行性的无监督事实一致性评估创新思路，并通过框架图形式清晰展示了每个思路的整体流程与关键模块设计。\n", "task plan:1. 文献与现状调研\n", "    (1) 系统收集近年来基于大语言模型（LLM）的文本摘要事实一致性评估方法的相关文献，涵盖学术会议论文、期刊论文、技术报告等。\n", "    (2) 整理并分析这些文献中的最新研究进展，提炼出具有代表性的研究工作，明确不同方法的核心思想、技术特点及优缺点。\n", "2. 无监督事实一致性评估技术路径、能力边界与挑战分析\n", "    (1) 对无监督事实一致性评估中的技术路径进行详细梳理，包括但不限于基于语义相似度、基于知识图谱、基于逻辑推理等方法。\n", "    (2) 评估不同技术路径在处理文本摘要事实一致性评估时的能力边界，明确各种方法在不同场景下的适用性。\n", "    (3) 全面分析当前无监督事实一致性评估面临的主要挑战，如语义理解的深度不足、知识覆盖的局限性、评估指标的合理性等。\n", "3. 创新思路提出\n", "    (1) 结合现有技术瓶颈，提出两至三个具有较高可行性的无监督事实一致性评估创新思路，如融合多模态信息的评估方法、基于对抗训练的评估模型等。\n", "    (2) 针对每个创新思路，详细阐述其设计理念、技术实现路径及预期效果。\n", "4. 创新思路框架图设计\n", "    (1) 为每个创新思路设计框架图，清晰展示整体流程与关键模块设计，包括输入、处理过程、输出等环节。\n", "    (2) 在框架图中明确标注各模块的功能及相互之间的关联，确保框架图的可读性和可理解性。\n", "5. 案例验证与效果评估（可选，根据实际需求与资源决定是否实施）\n", "    (1) 选取具有代表性的文本摘要数据集，对提出的创新思路进行实证研究，验证其有效性和可行性。\n", "    (2) 设计合理的评估指标，对创新思路在无监督事实一致性评估中的效果进行量化评估，对比不同方法之间的性能差异。\n", "6. 报告撰写与成果总结\n", "    (1) 将上述调研、分析、创新思路及框架图等内容整理成系统调研报告，确保报告内容完整、逻辑清晰、数据准确。\n", "    (2) 总结研究成果，明确未来研究方向，提出对基于大语言模型的文本摘要事实一致性评估方法的改进建议。\n", "Model decision:{'need_more_info': True, 'reasoning': '为了完成基于大语言模型（LLM）的文本摘要事实一致性评估方法的系统调研，首先需要系统收集近年来相关的学术会议论文、期刊论文、技术报告等文献，以全面覆盖任务问题中的各个细节需求。由于目前尚未进行任何文献检索，因此缺乏对最新研究进展、代表性工作以及无监督事实一致性评估中技术路径、能力边界与主要挑战的深入理解。为了支撑对任务问题的完整理解和回答，需要继续调用工具进行文献检索，以获取足够且相关的参考信息。'}\n", "************开始第1次检索************\n", "Model decision:[{'tool_name': 'AIsearch', 'tool_args': {'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 最新研究进展', 'instruction': '按照时间顺序列出近年来（如近五年）的相关研究进展'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 代表性工作', 'instruction': '列出该领域内具有代表性的工作，并简要描述其贡献'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 技术路径 能力边界 主要挑战', 'instruction': '详细分析当前无监督事实一致性评估中的技术路径、能力边界与主要挑战'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 创新思路', 'instruction': '列出两至三个具有较高可行性的无监督事实一致性评估创新思路，并简要描述每个思路的核心想法'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 创新思路 框架图设计', 'instruction': '针对之前提出的创新思路，输出每个思路的整体流程与关键模块设计的框架图相关参考信息'}}]\n", "{'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 最新研究进展', 'instruction': '按照时间顺序列出近年来（如近五年）的相关研究进展'}\n", "工具调用结果(truncated):Title: 基于大语言模型的文本摘要质量评估①\n", "Content: 计算机系统应用 ISSN 1003-3254, CODEN CSAOBN 2025,34(2):28−36 [doi: 10.15888/j.cnki.csa.009779] [CSTR: 32024.14.csa.009779] ©中国科学院软件研究所版权所有.   E-mail: <EMAIL> http://www.c-s-a.org.cn Tel: +86-10-62661041   基于大语言模型的文本摘要质量评估① 谭琛瀚1,2,  贾克斌1,2,  王浩宇1,2 1(北京工业大学 信息学部, 北京 100124) 2(先进信息网络北京实验室, 北京 100876) 通信作者: 贾克斌, E-mail: <EMAIL> 摘要: 自动文本摘要是自然语言处理 (NLP) 领域中的一个重要分支, 其主要难点之一是在于如何快速,客观且 准确地评估生成摘要的质量. 针对现有文本摘要质量评估方法中评估准确度不高,需要参考文本以及计算资源消 耗大的问题, 本文提出一种基于大语言模型的文本摘要质量评估方法, 设计基于思维链原理的提示词构建方法以提 高大语言模型在文本摘要质量评估任务上的性能, 同时生成思维链数据集并以模型微调的方式对小型大语言模型 进行训练, 显著降低了计算需求. 本文方法首先根据文本摘要的特点确定评估维度, 并基于思维链原理 (chain of thought, CoT) 构建提示词; 使用提示词对大型大语言模型进行引导, 使其根据摘要样本生成思维链过程与评估结 果, 同时以此为基础生成思维链数据集; 使用生成的思维链数据集对小型大语言模型进行微调训练; 最后使用微调 后的小型大语言模型完成文本摘要的质量评估任务. 本文在 Summeval 数据集上进行了对比实验与分析, 实验结果 表明, 本评估方法显著提高了小型大语言模型在文本摘要质量评估任务上的评估准确度, 实现了一种无需参考文 本,评估准确度高,计算需求低,便于部署的文本摘要质量评估方法. \n", "URL: https://c-s-a.org.cn/csaen/ch/reader/create_pdf.aspx?file_no=9779&year_id=2025&quarter_id=2\n", "\n", "Tit\n", "{'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 代表性工作', 'instruction': '列出该领域内具有代表性的工作，并简要描述其贡献'}\n", "工具调用结果(truncated):Title: 「LLM-综述」利用大语言模型进行自然语言生成评估的综述\n", "Content:  一、结论写在前面 论文调研了大语言模型在自然语言生成评估中的作用。论文按照三个维度对当前的研究进行分类:评估函数、评估参考和评估任务。论文深入研究了各种基于LLM的方法,仔细分析了它们的优势并比较了它们的区别。此外,论文总结了自然语言生成评估的常用评估基准。虽然LLM在评估自然语言生成输出方面提供了突破性的潜力,但仍然存在一些未解决的问题需要关注,包括偏见、鲁棒性、集成混合评估方法的需要,以及LLM评估器中需要特定领域和统一评估。预期解决这些挑战将为更普遍、更有效和更可靠的自然语言生成评估技术铺平道路。 图1:LLM用于NLG评估的示意图。虚线表示参考和来源基于场景是可选的 二、论文的简单介绍 2.1 论文的背景 随着NLG(Natural Language Generation,自然语言生成)技术的快速发展,建立可靠的评估方法以准确衡量生成内容的质量变得越来越重要。传统的NLG评估指标,如BLEU、ROUGE和TER,主要关注文本表层(surface-level)的差异,在评估语义方面往往不足。这一局限性被认为阻碍了研究进展,并可能导致误导性的研究结论。此外,其他采用神经嵌入(neural embeddings)来计算分数的方法,尽管评估了语义等价(semantic equivalence)和流畅性等方面,但灵活性有限,范围有限。另外,这些传统方法往往与人类判断存在较大差异,并且对分数缺乏解释性。这些缺点强调了NLG领域需要更细致和全面的评估方法的必要性。\n", "URL: https://baijiahao.baidu.com/s?id=1788679661289456142&wfr=spider&for=pc\n", "\n", "Title: 【论文解读】大模型事实性调查(上) \n", "Content:  本调查探讨了大型语言模型(llm)中的事实性的关键问题。随着llm在不同领域的应用,其输出的可靠性和准确性变得至关重要。论文将“事实性问题”定义为llm产生与既定事实不一致的内容的概率。然后,论文的讨论过渡到评估LLM事实性的方法,强调关键指标、基准和研究。论文的调查为研究人员提供了一个结构化的指导,旨在加强llm的事实可靠性。 在本节中,论文将描述大型语言模型中的事实性问题及其影响。 在文献中没有\n", "{'query': '无监督事实一致性评估 技术路径 能力边界 主要挑战', 'instruction': '详细分析当前无监督事实一致性评估中的技术路径、能力边界与主要挑战'}\n", "工具调用结果(truncated):Title: 无监督学习的未来趋势与挑战\n", "Content:  1.背景介绍 无监督学习是人工智能领域的一个重要分支,它涉及到从未经过训练的数据中提取知识的过程。无监督学习算法通常用于处理大量不完全标注的数据,以识别数据中的模式和结构。随着数据规模的增加和计算能力的提高,无监督学习技术在各个领域得到了广泛应用,如图像处理、自然语言处理、生物信息学等。 在本文中,我们将讨论无监督学习的核心概念、算法原理、具体实现以及未来的发展趋势和挑战。 2.核心概念与联系 无监督学习与监督学习是人工智能中两大主流的学习方法。监督学习需要预先标注的数据集来训练模型,而无监督学习则只依赖于未标注的数据。无监督学习可以帮助我们发现数据中的隐藏结构和关系,从而提高模型的性能。 无监督学习可以分为以下几类: 1.聚类分析:将数据分为多个群集,使得同一群集内的数据点相似度高,同时群集间的相似度低。 2.降维处理:将高维数据降至低维,使得数据的特征更加清晰和简洁。 3.异常检测:识别数据中的异常点,以便进一步分析和处理。 4.自组织映射:将高维数据映射到低维空间,以便更好地可视化和分析。 3.核心算法原理和具体操作步骤以及数学模型公式详细讲解 3.1聚类分析 聚类分析是无监督学习中最常用的方法之一,它旨在根据数据点之间的相似度将其划分为多个群集。常见的聚类算法有K均值算法、DBSCAN算法和层次聚类算法等。 3.1.1K均值算法 K均值算法是一种迭代的聚类算法,它的核心思想是将数据点分为K个群集,使得每个群集的内部相似度最大,同时群集间的相似度最小。 具体步骤如下: 1.随机选择K个数据点作为初始的聚类中心。 2.将每个数据点分配到与其距离最近的聚类中心所在的群集中。 3.计算每个聚类中心的新位置,使其为该群集中所有数据点的平均值。 4.重复步骤2和3,直到聚类中心的位置不再变化或达到最大迭代次数。 K均值算法的数学模型公式为: $$ \\\\min{C} \\\\sum{k=1}^{K} \\\\sum{x \\\\in Ck} \\\\|x - c_k\\\\|^2 $$ 其中,$C$ 表示聚类中心,$Ck$ 表示第k个聚类中心,$ck$ 表示第k个聚类中心的位置,$x$ 表示数据点。 3.1.2DBSCAN算法 DBSCAN(Density-Based Spatial Clustering of Applications wi\n", "{'query': '无监督事实一致性评估 创新思路', 'instruction': '列出两至三个具有较高可行性的无监督事实一致性评估创新思路，并简要描述每个思路的核心想法'}\n", "工具调用结果(truncated):Title: 无监督模型评估\n", "Content:  1.无监督学习的定义:无监督学习是一种机器学习范式,旨在从无标签数据中发现潜在的结构、模式和关系。它不依赖于预先定义的目标或标签,而是通过数据自身的特征和关系来进行分析和推断。 2.无监督模型的目标:主要包括聚类、降维、异常检测等。聚类旨在将数据分成若干个不相交的簇,使得同一簇内的数据具有较高的相似性,而不同簇之间的数据差异较大;降维则是通过减少数据的维度,保留数据的主要信息,以便更好地进行分析和可视化;异常检测则是检测数据中的异常点或离群值,这些点可能表示数据中的异常情况或错误。 3.无监督模型的应用领域:广泛应用于数据分析、数据挖掘、模式识别、图像处理、自然语言处理等领域。在数据分析中,可以用于发现数据中的潜在分组和趋势;在数据挖掘中,可以用于挖掘大规模数据中的隐藏模式和关联;在模式识别中,可以用于识别数据中的模式和特征;在图像处理中,可以用于图像分割、特征提取等;在自然语言处理中,可以用于文本聚类、主题模型构建等。\n", "URL: https://www.renrendoc.com/paper/357941713.html\n", "\n", "Title: 推荐项目:评估摘要事实一致性模型\n", "Content:  推荐项目:评估摘要事实一致性模型 项目地址:https://gitcode.com/gh_mirrors/fa/factCC 该项目是由Wojciech Kryściński等人提出的,专注于评估抽象文本总结的合理性,特别是检查生成的摘要是否与源文档在事实上保持一致。传统评估方法忽视了这一点,而该项目提供了一种弱监督、基于模型的方法,用于事实一致性验证和识别文档与摘要之间的冲突。通过一系列规则转换生成训练数据,然后训练模型执行三个任务:判断句子变换后是否仍保持一致,提取支持一致性的源文档片段,以及(如果存在)提取不一致的摘要片段。 该模型采用了创新的数据生成策略,利用了如回译、代词替换等变换来模拟可能的事实偏差。同时,模型被设计为同时处理这三个相关任务,这提高了其对事实一致性的识别能力。此外,模型还显示出了在多个最先进的摘要生成模型上显著优于前代模型的能力,即使是在仅使用少量标注数据的情况下。 这个项目适用于自然语言处理领域,特别是文本摘要和信息检索的开发者和研究人员。它可以帮助改进摘要算法,确保生成的内容既精炼又准确。对于新闻\n", "{'query': '无监督事实一致性评估 创新思路 框架图设计', 'instruction': '针对之前提出的创新思路，输出每个思路的整体流程与关键模块设计的框架图相关参考信息'}\n", "工具调用结果(truncated):Title: 机器学习实战之数据预处理、监督算法、无监督算法、模型评估与改进-思维导图拆分篇\n", "Content:  数据预处理包括:数据处理、转换、特征工程、降为可视化、管道; 监督算法包括:线性回归、梯度下降回归、岭回归、lasoo、逻辑回归、决策树、k近邻、朴素贝叶斯、支持向量机(线性、核函数)、多分类模型OVO\\\\OVR、梯度提升树、XGBoost、随机森林、极端随机数、stacking; 无监督算法包括:k均值聚类、凝聚聚类、DBSCAN以及聚类评估方法; 模型评估与改进:不平衡数据的处理、二分类、多分类评估指标、交叉验证、网格搜索。\n", "URL: https://blog.csdn.net/2302_80425184/article/details/146462966\n", "\n", "Title: 自监督训练超越传统微调!论文作者授权解读自监督革命性研究!\n", "Content:  (1)纯无监督自我训练框架——Genius 这篇论文的核心创新在于提出了一个无任何外部监督信号的自我训练框架——Genius。它只需要\\\"通用场景下的自然语言查询\\\"(即并不带标准答案或评价指标的普通文本问题),就能在推理能力上\\\"自学\\\"并提升模型性能。 (2)引入\\\"前瞻式(foresight)重采样\\\"策略 为了克服自回归生成过程容易\\\"鼠目寸光\\\"的问题,论文提出了一个关键策略:在每个推理步骤,不仅生成若干候选步骤,还会\\\"向前模拟\\\"可能出现的后续步骤,从而给当前的候选步骤一个更具全局意识的\\\"前瞻打分\\\"(foresight score)。随后利用这些打分进行重采样,一方面进行探索(确保多样性),另一方面则做出\\\"最优\\\"选择用于训练(确保质量)。 (3)优势值校准的自训练优化(Advantage-Calibrated Optimization, ACO) 在无监督自我训练时,难免会因为打分不准、未来步骤模拟不完备等因素产生噪声或估计偏差。论文提出了一个\\\"优势值校准\\\"的优化损失函数(ACO),通过\\\"对比正负样本的前瞻得分差值\\\"来调节训练时的梯度惩罚,减少不准确打分带来的影响,并在无监督场景下依旧能保持模型训练的稳定和性能的提升。 (4)在多任务、大范围评测中的有效性与可扩展性 在没有任何外部标注回答或额外监督的情况下,Genius仅使用通用数据(25K或32K条无标注查询)就能在多种推理任务(例如数学、逻辑、常识等)上获\n", "Model decision:{'need_more_info': True, 'reasoning': '根据目前的检索结果，关于基于大语言模型（LLM）的文本摘要事实一致性评估方法，已获得了一些有价值的信息，包括相关文献的摘要和部分代表性工作的介绍。然而，这些信息主要聚焦于LLM在自然语言处理及文本摘要质量评估中的一般应用和挑战，而对于无监督事实一致性评估的具体技术路径、能力边界及主要挑战的深入分析还不够详尽。特别是，缺乏对近年来无监督事实一致性评估中技术路径的详细梳理、不同方法核心思想的提炼、技术特点的比较，以及这些方法在不同场景下的适用性评估。\\\\n\\\\n此外，关于无监督事实一致性评估的创新思路，当前检索结果仅提供了一些无监督学习和模型评估的一般性介绍，以及一个具体的评估摘要事实一致性模型的项目地址，但并未给出具体的创新思路及其框架图设计。\\\\n\\\\n因此，为了完成本次深度研究任务，需要进一步检索相关信息，特别是关于无监督事实一致性评估的具体技术路径、代表性工作的深入分析、当前面临的主要挑战，以及可能的创新思路和框架图设计。这些信息将有助于更全面地理解无监督事实一致性评估的现状，并提出更具可行性的创新思路。'}\n", "************开始第2次检索************\n", "Model decision:[{'tool_name': 'AIsearch', 'tool_args': {'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 最新研究进展 2023年至今', 'instruction': '只返回2023年至今的基于大语言模型（LLM）的文本摘要事实一致性评估方法的最新研究进展相关内容'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 代表性工作', 'instruction': '详细梳理基于大语言模型（LLM）的文本摘要事实一致性评估方法的代表性工作，返回详细方法描述'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 技术路径', 'instruction': '详细分析无监督事实一致性评估的技术路径'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 能力边界', 'instruction': '详细总结无监督事实一致性评估的能力边界'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 主要挑战', 'instruction': '详细总结无监督事实一致性评估面临的主要挑战'}}, {'tool_name': 'AIsearch', 'tool_args': {'query': '无监督事实一致性评估 创新思路', 'instruction': '返回两至三个具有较高可行性的无监督事实一致性评估创新思路，并包含每个思路的整体流程与关键模块设计的概述'}}]\n", "{'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 最新研究进展 2023年至今', 'instruction': '只返回2023年至今的基于大语言模型（LLM）的文本摘要事实一致性评估方法的最新研究进展相关内容'}\n", "工具调用结果(truncated):Title: 弗吉尼亚大学团队打造可靠生物医学假设生成评估工具\n", "Content:  首先,验证生成假设的准确性通常需要大量时间和资源。这就像要验证一个谣言是否属实,可能需要进行耗时的事实核查。 其次,大语言模型的\\\"幻觉\\\"问题可能导致生成看似合理但实际上错误的假设,这会严重影响其可靠性。这就像一个善于讲故事的人,能让虚构的事件听起来栩栩如生,让人难以分辨真假。\n", "URL: https://www.163.com/dy/article/K17LIF7105118UGF.html\n", "\n", "Title: 基于大语言模型的文本摘要质量评估①\n", "Content: 计算机系统应用 ISSN 1003-3254, CODEN CSAOBN 2025,34(2):28−36 [doi: 10.15888/j.cnki.csa.009779] [CSTR: 32024.14.csa.009779] ©中国科学院软件研究所版权所有.   E-mail: <EMAIL> http://www.c-s-a.org.cn Tel: +86-10-62661041   基于大语言模型的文本摘要质量评估① 谭琛瀚1,2,  贾克斌1,2,  王浩宇1,2 1(北京工业大学 信息学部, 北京 100124) 2(先进信息网络北京实验室, 北京 100876) 通信作者: 贾克斌, E-mail: <EMAIL> 摘要: 自动文本摘要是自然语言处理 (NLP) 领域中的一个重要分支, 其主要难点之一是在于如何快速,客观且 准确地评估生成摘要的质量. 针对现有文本摘要质量评估方法中评估准确度不高,需要参考文本以及计算资源消 耗大的问题, 本文提出一种基于大语言模型的文本摘要质量评估方法, 设计基于思维链原理的提示词构建方法以提 高大语言模型在文本摘要质量评估任务上的性能, 同时生成思维链数据集并以模型微调的方式对小型大语言模型 进行训练, 显著降低了计算需求. 本文方法首先根据文本摘要的特点确定评估维度, 并基于思维链原理 (chain of thought, CoT) 构建提示词; 使用提示词对大型大语言模型进行引导, 使其根据摘要样本生成思维链过程与评估结 果, 同时以此为基础生成思维链数据集; 使用生成的思维链数据集对小型大语言模型进行微调训练; 最后使用微调 后的小型\n", "{'query': '基于大语言模型（LLM）的文本摘要事实一致性评估方法 代表性工作', 'instruction': '详细梳理基于大语言模型（LLM）的文本摘要事实一致性评估方法的代表性工作，返回详细方法描述'}\n", "工具调用结果(truncated):Title: 「LLM-综述」利用大语言模型进行自然语言生成评估的综述\n", "Content:  一、结论写在前面 论文调研了大语言模型在自然语言生成评估中的作用。论文按照三个维度对当前的研究进行分类:评估函数、评估参考和评估任务。论文深入研究了各种基于LLM的方法,仔细分析了它们的优势并比较了它们的区别。此外,论文总结了自然语言生成评估的常用评估基准。虽然LLM在评估自然语言生成输出方面提供了突破性的潜力,但仍然存在一些未解决的问题需要关注,包括偏见、鲁棒性、集成混合评估方法的需要,以及LLM评估器中需要特定领域和统一评估。预期解决这些挑战将为更普遍、更有效和更可靠的自然语言生成评估技术铺平道路。 图1:LLM用于NLG评估的示意图。虚线表示参考和来源基于场景是可选的 二、论文的简单介绍 2.1 论文的背景 随着NLG(Natural Language Generation,自然语言生成)技术的快速发展,建立可靠的评估方法以准确衡量生成内容的质量变得越来越重要。传统的NLG评估指标,如BLEU、ROUGE和TER,主要关注文本表层(surface-level)的差异,在评估语义方面往往不足。这一局限性被认为阻碍了研究进展,并可能导致误导性的研究结论。此外,其他采用神经嵌入(neural embeddings)来计算分数的方法,尽管评估了语义等价(semantic equivalence)和流畅性等方面,但灵活性有限,范围有限。另外,这些传统方法往往与人类判断存在较大差异,并且对分数缺乏解释性。这些缺点强调了NLG领域需要更细致和全面的评估方法的必要性。\n", "URL: https://baijiahao.baidu.com/s?id=1788679661289456142&wfr=spider&for=pc\n", "\n", "Title: 【论文解读】大模型事实性调查(上) \n", "Content:  本调查探讨了大型语言模型(llm)中的事实性的关键问题。随着llm在不同领域的应用,其输出的可靠性和准确性变得至关重要。论文将“事实性问题”定义为llm产生与既定事实不一致的内容的概率。然后,论文的讨论过渡到评估LLM事实性的方法,强调关键指标、基准和研究。论文的调查为研究人员提供了一个结构化的指导,旨在加强llm的事实可靠性。 在本节中,论文将描述大型语言模型中的事实性问题及其影响。 在文献中没有\n", "{'query': '无监督事实一致性评估 技术路径', 'instruction': '详细分析无监督事实一致性评估的技术路径'}\n", "工具调用结果(truncated):Title: 推荐项目:评估摘要事实一致性模型\n", "Content:  推荐项目:评估摘要事实一致性模型 项目地址:https://gitcode.com/gh_mirrors/fa/factCC 该项目是由Wojciech Kryściński等人提出的,专注于评估抽象文本总结的合理性,特别是检查生成的摘要是否与源文档在事实上保持一致。传统评估方法忽视了这一点,而该项目提供了一种弱监督、基于模型的方法,用于事实一致性验证和识别文档与摘要之间的冲突。通过一系列规则转换生成训练数据,然后训练模型执行三个任务:判断句子变换后是否仍保持一致,提取支持一致性的源文档片段,以及(如果存在)提取不一致的摘要片段。 该模型采用了创新的数据生成策略,利用了如回译、代词替换等变换来模拟可能的事实偏差。同时,模型被设计为同时处理这三个相关任务,这提高了其对事实一致性的识别能力。此外,模型还显示出了在多个最先进的摘要生成模型上显著优于前代模型的能力,即使是在仅使用少量标注数据的情况下。 这个项目适用于自然语言处理领域,特别是文本摘要和信息检索的开发者和研究人员。它可以帮助改进摘要算法,确保生成的内容既精炼又准确。对于新闻媒体、数据分析公司或任何需要自动摘要的组织来说,这是一个有价值的工具,可以提高信息准确性和可信度。 弱监督学习:不需要大量人工标注数据,通过规则变换生成训练集。 多任务联合学习:模型同时处理语义一致性判断、来源证据抽取和摘要中矛盾点定位,提高了检测效率。 高度可扩展性:能有效应用于现有先进模型的评估,揭示潜在的事实不一致性问题。 人性化评估:辅助的任务有助于人类验证事实一致性,增强了系统的实用性。 这个项目是研究文本摘要质量提升的重要一步,对于理解并改进自然语言处理系统的行为具有重要意义。如果你正致力于自然语言处理技术或者对此领域感兴趣,那么这个项目绝对值得你一试。\n", "URL: https://blog.csdn.net/gitblog_00042/article/details/139191879\n", "\n", "Title: 无监督角度感知突破数据瓶颈,端到端自动驾驶具备大模型scaling law \n", "Content:  ●前面的子任务需要大量高质量的3D标注作为监督,这对扩展训练数据构成了重大障碍; ●每个子模块在训练和推理中都需要大量的计算开销。\n", "URL: https://mp.we\n", "{'query': '无监督事实一致性评估 能力边界', 'instruction': '详细总结无监督事实一致性评估的能力边界'}\n", "工具调用结果(truncated):Title: 无监督BEV SOTA:挖掘BEV潜力边界 \n", "Content: 首先,它提出了一种基于查询的领Domain Adaptation策略,这种策略利用了图像视图特征和BEV特征的互补性,适用于无监督的BEV感知适应。其次,它设计了DA-BEV,这是一种引入基于查询的对抗学习和基于查询的自训练的框架,有效地联合解决了领域自适应BEV感知的问题。最后,通过广泛的实验,DA-BEV在不同数据集和任务(如3D物体检测和3D场景分割)上展示了其在BEV感知适应方面的优越性能。\n", "URL: https://mp.weixin.qq.com/s?__biz=Mzk0NzE4MTQwMg==&mid=2247527999&idx=2&sn=f3afe43791401e15024bd8a23e70e269&chksm=c2e775e068a8e234b822b4be3c384b986a10055197e3067e84a8636b2134bd8c97db7b4c0cb1&scene=27\n", "\n", "Title: 无监督学习评估标准 无监督评价指标\n", "Content:  一、聚类的形式限制 从直观的角度来看,聚类问题有一个非常明确的目标;也就是说,正确地聚类一组未标记的数据。尽管“聚类”的概念具有直观的吸引力,但它无法精确定义,因此已经提出了各种各样的聚类算法。 A. 簇的理想特性 Jon <PERSON>berg提出了三个公理,强调了一个分组问题应该表现出来的特征,并且可以被认为是“好的”,而不依赖于用于寻找解决方案的算法。这些公理分别是尺度不变性(scale invariance)、一致性(consistency)和丰富性(richness)[3],下面将详细说明。 定义分组函数为 ( 个点)和点对之间的距离的集合。点的集合为 ,点之间的距离由距离函数 给出,其中 。距离函数 测量点对之间的不相似性。例如,欧几里得距离(Euclidean)、曼哈顿距离(Manhattan)、切比雪夫距离(Chebyshev)和马氏距离(Mahalanobis)等等都可以使用。或者,也可以使用相似函数。 1)尺度不变性(Scale Invariance) Kleinberg 的第一个公理表明,对于任何距离函数 和任何比例因子 , 。 这个公理表明,当所有点之间的距离由常数 2)丰富性(Richness) 当 \n", "{'query': '无监督事实一致性评估 主要挑战', 'instruction': '详细总结无监督事实一致性评估面临的主要挑战'}\n", "工具调用结果(truncated):Title: 人工智能:无监督学习的挑战\n", "Content:  1.缺乏标签 挑战: 无监督学习没有标签数据,缺乏衡量模型性能的标准。 影响: 结果验证困难,导致生成模型的可信度降低。 策略: 半监督学习:将少量标记数据结合大量无标记数据进行训练。 自监督学习:设计辅助任务(如图像旋转预测),自动生成标签。 交叉验证:通过数据多样性或领域知识进行间接验证 2.模型选择 挑战:无监督学习算法种类繁多。 影响: 不同任务需要不同算法,选择最优模型的困难 策略: 基准测试:使用公开数据集和基准测试评估不同模型。 理论分析:基于算法理论和数据特性选择模型。 试验与反馈:通过小规模实验获得初步结果,调整模型选择。 3.结果解释 挑战: 聚类或降维结果难以解释。 影响: 需要理解所得到的模式和结构对实际应用的意义。 策略: 可视化技术:使用降维和聚类结果可视化,增强解释性。 领域知识集成:结合领域知识解释模型结果。 用户调研:通过专家或用户反馈优化和解释结果。 4.维度诅咒 挑战: 高维数据中,数据点之间的距离难以度量。 影响: 导致聚类和降维效果的降低。 策略: 特征降维:使用PCA、t-SNE等降维技术减少维度。 特征选择:选择最具信息量的特征。 正则化:在模型训练中使用正则化项防止过拟合。 5.噪声与异常值 挑战: 无监督学习对噪声和异常值敏感。 影响: 不准确的模型生成结果,影响数据关联的可靠性。 策略: 数据清洗:在预处理阶段剔除或修正异常数据。 鲁棒性算法:选择对噪声不敏感的算法。 噪声识别模型:在模型中集成用于识别和处理噪声的模块。 6.过拟合与泛化能力 挑战: 模型易于找到噪声而非数据结构。 影响: 降低在新数据上的泛化能力。 策略: 交叉验证:使用交叉验证评估模型泛化能力。 正则化:通过正则化技术限制模型的复杂度。 集成学习:使用多个模型组合提高泛化能力 7.超参数调优 挑战: 多数算法依赖超参数设置,选择合适的超参数困难。 影响: 不同设置可能导致不同结果。 策略: 自动化调参:使用Grid Search、Random Search或贝叶斯优化进行调参。 动态调整:在训练过程中动态调整超参数。 实践经验:\n", "URL: https://blog.csdn.net/Wei_sx/article/details/142680873\n", "\n", "Title: 什么是无监督学习\n", "C\n", "{'query': '无监督事实一致性评估 创新思路', 'instruction': '返回两至三个具有较高可行性的无监督事实一致性评估创新思路，并包含每个思路的整体流程与关键模块设计的概述'}\n", "工具调用结果(truncated):Title: 无监督模型评估\n", "Content:  1.无监督学习的定义:无监督学习是一种机器学习范式,旨在从无标签数据中发现潜在的结构、模式和关系。它不依赖于预先定义的目标或标签,而是通过数据自身的特征和关系来进行分析和推断。 2.无监督模型的目标:主要包括聚类、降维、异常检测等。聚类旨在将数据分成若干个不相交的簇,使得同一簇内的数据具有较高的相似性,而不同簇之间的数据差异较大;降维则是通过减少数据的维度,保留数据的主要信息,以便更好地进行分析和可视化;异常检测则是检测数据中的异常点或离群值,这些点可能表示数据中的异常情况或错误。 3.无监督模型的应用领域:广泛应用于数据分析、数据挖掘、模式识别、图像处理、自然语言处理等领域。在数据分析中,可以用于发现数据中的潜在分组和趋势;在数据挖掘中,可以用于挖掘大规模数据中的隐藏模式和关联;在模式识别中,可以用于识别数据中的模式和特征;在图像处理中,可以用于图像分割、特征提取等;在自然语言处理中,可以用于文本聚类、主题模型构建等。\n", "URL: https://www.renrendoc.com/paper/357941713.html\n", "\n", "Title: 推荐项目:评估摘要事实一致性模型\n", "Content:  推荐项目:评估摘要事实一致性模型 项目地址:https://gitcode.com/gh_mirrors/fa/factCC 该项目是由Wojciech Kryściński等人提出的,专注于评估抽象文本总结的合理性,特别是检查生成的摘要是否与源文档在事实上保持一致。传统评估方法忽视了这一点,而该项目提供了一种弱监督、基于模型的方法,用于事实一致性验证和识别文档与摘要之间的冲突。通过一系列规则转换生成训练数据,然后训练模型执行三个任务:判断句子变换后是否仍保持一致,提取支持一致性的源文档片段,以及(如果存在)提取不一致的摘要片段。 该模型采用了创新的数据生成策略,利用了如回译、代词替换等变换来模拟可能的事实偏差。同时,模型被设计为同时处理这三个相关任务,这提高了其对事实一致性的识别能力。此外,模型还显示出了在多个最先进的摘要生成模型上显著优于前代模型的能力,即使是在仅使用少量标注数据的情况下。 这个项目适用于自然语言处理领域,特别是文本摘要和信息检索的开发者和研究人员。它可以帮助改进摘要算法,确保生成的内容既精炼又准确。对于新闻\n", "Model decision:{'need_more_info': False, 'reasoning': '根据提供的参考信息，已经获取了基于大语言模型（LLM）的文本摘要事实一致性评估方法的最新研究进展，包括代表性工作、无监督事实一致性评估的技术路径、能力边界与主要挑战。具体涵盖了利用大语言模型进行自然语言生成评估的综述、无监督学习在事实一致性评估中的应用、以及一些具体的项目和模型，如评估摘要事实一致性模型factCC。同时，也了解到无监督学习在面临缺乏标签、模型选择、结果解释、维度诅咒、噪声与异常值、过拟合与泛化能力、超参数调优等方面的挑战。这些信息足以支撑对无监督事实一致性评估的系统调研，包括技术路径梳理、能力边界与挑战分析，以及提出创新思路和框架图设计。因此，不需要进一步调用检索工具获取更多信息。'}\n", "************输出最终结果***********\n", "final answer:### 基于大语言模型的文本摘要事实一致性评估调研报告\n", "\n", "#### 一、最新研究进展与代表性工作\n", "\n", "**最新研究进展**\n", "\n", "近年来，基于大语言模型（LLM）的文本摘要质量评估及事实一致性评估成为自然语言处理（NLP）领域的研究热点。2025年《计算机系统应用》期刊上的一篇论文《基于大语言模型的文本摘要质量评估》提出了一种新的评估方法，该方法通过思维链原理（Chain of Thought, CoT）构建提示词，引导大型LLM生成思维链过程与评估结果，进而生成思维链数据集用于微调小型LLM，显著降低了计算需求并提高了评估准确度。该研究在Summeval数据集上的对比实验证明了方法的有效性。\n", "\n", "**代表性工作**\n", "\n", "1. **大语言模型在自然语言生成评估中的综述**：该综述论文从评估函数、评估参考和评估任务三个维度对现有基于LLM的NLG评估方法进行了分类和比较，指出了LLM在评估自然语言生成输出方面的突破性潜力，同时也提出了偏见、鲁棒性、集成混合评估方法的需要等挑战。\n", "\n", "2. **大模型事实性调查**：该调查聚焦于LLM中的事实性问题，定义了事实性问题为LLM产生与既定事实不一致内容的概率，并讨论了评估LLM事实性的方法、关键指标和基准，旨在加强LLM的事实可靠性。\n", "\n", "#### 二、无监督事实一致性评估的技术路径、能力边界与主要挑战\n", "\n", "**技术路径**\n", "\n", "在无监督事实一致性评估方面，一个具有代表性的项目是factCC，该项目由Wojciech Kryściński等人提出，专注于评估抽象文本总结的合理性，特别是检查生成的摘要是否与源文档在事实上保持一致。项目采用弱监督、基于模型的方法，通过规则转换生成训练数据，训练模型执行判断句子一致性、提取支持一致性的源文档片段和提取不一致的摘要片段三个任务，显著提高了事实一致性的识别能力。\n", "\n", "**能力边界**\n", "\n", "无监督事实一致性评估的能力边界主要体现在对事实偏差的捕捉能力和对复杂文本结构的理解能力上。尽管factCC等模型在现有实验中表现出色，但在处理高度复杂或领域特定的文本时，其性能可能会受到限制。\n", "\n", "**主要挑战**\n", "\n", "1. **缺乏标签数据**：无监督学习没有标签数据，结果验证困难，导致生成模型的可信度降低。\n", "2. **模型选择**：无监督学习算法种类繁多，不同任务需要不同算法，选择最优模型的难度大。\n", "3. **结果解释**：聚类或降维结果难以解释，影响实际应用的意义理解。\n", "4. **维度诅咒**：高维数据中，数据点之间的距离难以度量，导致聚类和降维效果的降低。\n", "5. **噪声与异常值**：无监督学习对噪声和异常值敏感，影响数据关联的可靠性。\n", "\n", "#### 三、无监督事实一致性评估创新思路\n", "\n", "**创新思路一：基于自监督训练的事实一致性评估**\n", "\n", "**整体流程**\n", "\n", "1. **数据预处理**：收集大量无标签的文本数据，进行清洗和预处理。\n", "2. **自监督任务设计**：设计一系列自监督任务，如句子重组、代词替换等，模拟可能的事实偏差。\n", "3. **模型训练**：使用自监督任务生成的数据训练模型，使其学会识别和纠正事实偏差。\n", "4. **事实一致性评估**：将训练好的模型应用于文本摘要的事实一致性评估任务。\n", "\n", "**关键模块设计**\n", "\n", "- **自监督任务生成模块**：负责生成自监督任务的数据。\n", "- **模型训练模块**：负责训练模型以识别和纠正事实偏差。\n", "- **事实一致性评估模块**：负责评估文本摘要的事实一致性。\n", "\n", "**创新思路二：基于多模态融合的事实一致性评估**\n", "\n", "**整体流程**\n", "\n", "1. **多模态数据收集**：收集文本、图像、音频等多模态数据，构建多模态数据集。\n", "2. **多模态特征提取**：使用预训练模型提取多模态数据的特征。\n", "3. **多模态融合**：将多模态特征进行融合，形成综合特征表示。\n", "4. **事实一致性评估**：基于综合特征表示进行文本摘要的事实一致性评估。\n", "\n", "**关键模块设计**\n", "\n", "- **多模态数据收集模块**：负责收集多模态数据。\n", "- **多模态特征提取模块**：负责提取多模态数据的特征。\n", "- **多模态融合模块**：负责将多模态特征进行融合。\n", "- **事实一致性评估模块**：负责评估文本摘要的事实一致性。\n", "\n", "**创新思路三：基于知识图谱的事实一致性评估**\n", "\n", "**整体流程**\n", "\n", "1. **知识图谱构建**：构建领域特定的知识图谱，包含实体、关系和属性等信息。\n", "2. **文本摘要解析**：对文本摘要进行解析，提取实体和关系信息。\n", "3. **知识图谱匹配**：将提取的实体和关系信息与知识图谱进行匹配，检查一致性。\n", "4. **事实一致性评估**：基于匹配结果进行文本摘要的事实一致性评估。\n", "\n", "**关键模块设计**\n", "\n", "- **知识图谱构建模块**：负责构建领域特定的知识图谱。\n", "- **文本摘要解析模块**：负责解析文本摘要，提取实体和关系信息。\n", "- **知识图谱匹配模块**：负责将提取的实体和关系信息与知识图谱进行匹配。\n", "- **事实一致性评估模块**：负责评估文本摘要的事实一致性。\n", "\n", "#### 四、框架图设计\n", "\n", "**基于自监督训练的事实一致性评估框架图**\n", "\n", "```plaintext\n", "+-------------------+\n", "|   数据预处理      |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 自监督任务设计    |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "|   模型训练        |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 事实一致性评估    |\n", "+-------------------+\n", "```\n", "\n", "**基于多模态融合的事实一致性评估框架图**\n", "\n", "```plaintext\n", "+-------------------+\n", "| 多模态数据收集    |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 多模态特征提取    |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "|   多模态融合      |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 事实一致性评估    |\n", "+-------------------+\n", "```\n", "\n", "**基于知识图谱的事实一致性评估框架图**\n", "\n", "```plaintext\n", "+-------------------+\n", "|   知识图谱构建    |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 文本摘要解析      |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 知识图谱匹配      |\n", "+-------------------+\n", "          |\n", "          v\n", "+-------------------+\n", "| 事实一致性评估    |\n", "+-------------------+\n", "```\n", "\n", "以上是对于基于大语言模型（LLM）的文本摘要事实一致性评估的系统调研，重点梳理了近年来的最新研究进展与代表性工作，并全面分析了当前在无监督事实一致性评估中的技术路径、能力边界与主要挑战。在此基础上，提出了三个具有较高可行性的无监督事实一致性评估创新思路，并通过框架图形式清晰展示了每个思路的整体流程与关键模块设计。\n", "\n"]}], "source": ["nest_asyncio.apply() # 允许嵌套事件循环（只需运行一次）\n", "loop = asyncio.get_event_loop()\n", "result = loop.run_until_complete(AI_Deep_Search(\"请对基于大语言模型（LLM）的文本摘要事实一致性评估方法展开系统调研，重点梳理近年来的最新研究进展与代表性工作，全面分析当前在无监督事实一致性评估中的技术路径、能力边界与主要挑战。在此基础上，结合现有技术瓶颈，提出两至三个具有较高可行性的无监督事实一致性评估创新思路，并通过框架图形式清晰展示每个思路的整体流程与关键模块设计\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON>\n", "This tutorial demonstrates how to build a model-guided, recursive AI search demo by:\n", "\n", "- Using an LLM to reason and guide the search\n", "- Dynamically generating search parameters\n", "- Calling the MCP Server to retrieve relevant knowledge\n", "- Closing the loop when sufficient information is gathered\n", "\n", "This pattern is extensible and production-ready for intelligent enterprise search workflows."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}
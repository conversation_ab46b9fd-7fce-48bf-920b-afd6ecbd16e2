#!/usr/bin/env python3
"""
简单的多GPU演示脚本
直接展示多卡并行训练效果
"""

import torch
import torch.nn as nn
import torch.distributed as dist
import os
import time

def setup_distributed():
    """设置分布式训练"""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])
    else:
        print("未检测到分布式环境变量，使用单GPU模式")
        return False, 0, 1, 0
    
    # 初始化分布式进程组
    dist.init_process_group(backend='nccl')
    torch.cuda.set_device(local_rank)
    
    return True, rank, world_size, local_rank

def main():
    """主函数"""
    
    # 设置分布式
    is_distributed, rank, world_size, local_rank = setup_distributed()
    
    if rank == 0:
        print("🚀 多GPU并行训练演示")
        print("="*50)
        print(f"检测到 {world_size} 个进程")
        print(f"使用 {torch.cuda.device_count()} 张GPU")
        
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 设置设备
    device = torch.device(f'cuda:{local_rank}')
    
    if rank == 0:
        print(f"\n进程 {rank} 使用设备: {device}")
    
    # 创建简单模型
    class SimpleModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.layers = nn.Sequential(
                nn.Linear(1000, 500),
                nn.ReLU(),
                nn.Linear(500, 250),
                nn.ReLU(),
                nn.Linear(250, 1)
            )
        
        def forward(self, x):
            return self.layers(x)
    
    model = SimpleModel().to(device)
    
    # 包装为分布式模型
    if is_distributed:
        model = nn.parallel.DistributedDataParallel(model, device_ids=[local_rank])
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
    criterion = nn.MSELoss()
    
    if rank == 0:
        print(f"\n模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        print("开始训练...")
    
    # 训练循环
    num_epochs = 3
    batch_size = 32
    num_batches = 10
    
    start_time = time.time()
    
    for epoch in range(num_epochs):
        if rank == 0:
            print(f"\nEpoch {epoch + 1}/{num_epochs}")
        
        epoch_loss = 0.0
        
        for batch_idx in range(num_batches):
            # 生成随机数据
            inputs = torch.randn(batch_size, 1000, device=device)
            targets = torch.randn(batch_size, 1, device=device)
            
            # 前向传播
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            
            if rank == 0 and batch_idx % 5 == 0:
                print(f"  Batch {batch_idx + 1}/{num_batches}, Loss: {loss.item():.6f}")
        
        avg_loss = epoch_loss / num_batches
        
        if rank == 0:
            print(f"  平均损失: {avg_loss:.6f}")
    
    total_time = time.time() - start_time
    
    if rank == 0:
        print("\n" + "="*50)
        print("🎉 多GPU训练完成!")
        print("="*50)
        print(f"总训练时间: {total_time:.2f}秒")
        print(f"使用GPU数量: {world_size}")
        print(f"最终损失: {avg_loss:.6f}")
        print("\n多GPU训练优势:")
        print(f"✓ {world_size}x GPU并行加速")
        print("✓ 自动梯度同步")
        print("✓ 内存分布式使用")
        print("✓ 线性扩展性能")
    
    # 清理分布式
    if is_distributed:
        dist.destroy_process_group()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
JSON转CSV转换器 - 将新字段JSON数据转换为CSV格式
"""

import json
import csv
import re
from datetime import datetime

def clean_text_for_csv(text):
    """清理文本，适合CSV格式"""
    if not text:
        return ''
    
    # 移除控制字符
    cleaned = re.sub(r'[\r\n\t]+', ' ', str(text))
    # 移除多余空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    # 去除首尾空格
    cleaned = cleaned.strip()
    
    return cleaned

def convert_json_to_csv(json_file, csv_file):
    """将JSON文件转换为CSV文件"""
    
    print(f"📖 正在读取JSON文件: {json_file}")
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 成功读取 {len(data)} 笔数据")
        
        if not data:
            print("❌ 数据为空")
            return
        
        # 获取所有字段名
        fieldnames = list(data[0].keys())
        
        # 排除复杂字段（如厂商详情）
        simple_fieldnames = []
        for field in fieldnames:
            if field != '厂商详情':  # 厂商详情是列表，不适合直接放入CSV
                simple_fieldnames.append(field)
        
        print(f"📋 将导出 {len(simple_fieldnames)} 个字段")
        
        # 写入CSV文件
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=simple_fieldnames)
            writer.writeheader()
            
            for item in data:
                # 清理数据
                cleaned_item = {}
                for field in simple_fieldnames:
                    value = item.get(field, '')
                    cleaned_item[field] = clean_text_for_csv(value)
                
                writer.writerow(cleaned_item)
        
        print(f"💾 CSV文件已保存: {csv_file}")
        
        # 生成厂商详情的单独CSV文件
        generate_vendor_csv(data, csv_file.replace('.csv', '_vendors.csv'))
        
    except Exception as e:
        print(f"❌ 转换过程中发生错误: {str(e)}")

def generate_vendor_csv(data, vendor_csv_file):
    """生成厂商详情的单独CSV文件"""
    
    vendor_records = []
    
    for item in data:
        vendors = item.get('厂商详情', [])
        if vendors:
            for vendor in vendors:
                vendor_record = {
                    '序号': item.get('序号', ''),
                    '标案名称': item.get('标案名称', ''),
                    '标案案号': item.get('标案案号', ''),
                    '机关名称': item.get('机关名称', ''),
                    '厂商名称': vendor.get('厂商名称', ''),
                    '厂商代码': vendor.get('厂商代码', ''),
                    '是否得标': vendor.get('是否得标', ''),
                    '决标金额': vendor.get('决标金额', ''),
                    '组织型态': vendor.get('组织型态', ''),
                    '厂商地址': vendor.get('厂商地址', ''),
                    '厂商电话': vendor.get('厂商电话', ''),
                    '履约期间': vendor.get('履约期间', ''),
                    '是否为中小企业': vendor.get('是否为中小企业', ''),
                    '得标厂商国别': vendor.get('得标厂商国别', '')
                }
                vendor_records.append(vendor_record)
    
    if vendor_records:
        with open(vendor_csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            fieldnames = list(vendor_records[0].keys())
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in vendor_records:
                cleaned_record = {}
                for field, value in record.items():
                    cleaned_record[field] = clean_text_for_csv(value)
                writer.writerow(cleaned_record)
        
        print(f"💾 厂商详情CSV文件已保存: {vendor_csv_file}")
        print(f"📊 厂商记录数: {len(vendor_records)}")
    else:
        print("⚠️ 没有厂商详情数据")

def generate_summary_csv(json_file, summary_csv_file):
    """生成摘要CSV文件（只包含核心字段）"""
    
    print(f"📋 正在生成摘要CSV文件...")
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 定义核心字段
        core_fields = [
            '序号', '标案名称', '机关名称', '标案案号', '招标日期', 
            '预算金额', '招标方式', '得标厂商', '得标金额', '履约地点'
        ]
        
        with open(summary_csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=core_fields)
            writer.writeheader()
            
            for item in data:
                summary_record = {}
                for field in core_fields:
                    value = item.get(field, '')
                    summary_record[field] = clean_text_for_csv(value)
                
                writer.writerow(summary_record)
        
        print(f"💾 摘要CSV文件已保存: {summary_csv_file}")
        
    except Exception as e:
        print(f"❌ 生成摘要CSV时发生错误: {str(e)}")

def main():
    """主函数"""
    print("📊 === JSON转CSV转换器 ===")
    
    # 查找最新的JSON文件
    import os
    import glob
    
    json_files = glob.glob('new_field_procurement_data_*.json')
    
    if not json_files:
        print("❌ 没有找到新字段JSON文件")
        return
    
    # 使用最新的文件
    latest_json_file = max(json_files, key=os.path.getctime)
    print(f"🔍 找到最新的JSON文件: {latest_json_file}")
    
    # 生成输出文件名
    base_name = latest_json_file.replace('.json', '')
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 转换为完整CSV
    full_csv_file = f"{base_name}_complete.csv"
    convert_json_to_csv(latest_json_file, full_csv_file)
    
    # 生成摘要CSV
    summary_csv_file = f"{base_name}_summary.csv"
    generate_summary_csv(latest_json_file, summary_csv_file)
    
    print(f"\n✅ === 转换完成 ===")
    print(f"📁 生成的文件:")
    print(f"  1. {full_csv_file} - 完整数据CSV")
    print(f"  2. {summary_csv_file} - 摘要数据CSV")
    
    # 检查是否有厂商数据
    vendor_csv_file = full_csv_file.replace('.csv', '_vendors.csv')
    if os.path.exists(vendor_csv_file):
        print(f"  3. {vendor_csv_file} - 厂商详情CSV")
    
    print(f"\n📝 使用说明:")
    print(f"  - 完整CSV包含所有字段，适合详细分析")
    print(f"  - 摘要CSV只包含核心字段，适合快速查看")
    print(f"  - 厂商CSV包含所有厂商详情，适合厂商分析")
    print(f"  - 所有文件都可以用Excel直接打开")

if __name__ == "__main__":
    main()

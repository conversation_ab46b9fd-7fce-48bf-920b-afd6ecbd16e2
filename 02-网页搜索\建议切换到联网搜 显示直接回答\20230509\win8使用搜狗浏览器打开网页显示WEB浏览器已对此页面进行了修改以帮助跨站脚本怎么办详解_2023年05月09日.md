﻿# win8使用搜狗浏览器打开网页“显示WEB浏览器已对此页面进行了修改以帮助跨站脚本”怎么办【详解】

**发布日期**: 2023年05月09日

**原文链接**: https://g.pconline.com.cn/x/1611/16118645.html

## 📄 原文内容

windows8系统 自带IE浏览器，但是一些用户还是喜欢下载第三方浏览器，比如搜狗浏览器，使用搜狗浏览器过程中总会遇到一些故障问题，比如win8使用搜狗浏览器打开网页“显示WEB浏览器已对此页面进行了修改以帮助跨站脚本”怎么办呢？其实大家只要进行一些简单的设置即可解决问题了，有需要的用户一起来看看吧。

1、我们在ie浏览器中点击“工具”之后找到里面的“选项”， 然后我们再找到 internet选项---安全---自定义级别---将安全别级设为 中 后点击确定；

2、现在进入到搜狗浏览器之后“工具”---“Internet 选项”；

3、我们再找到“安全”进入之后找到下面的“Internet”下方的“自定义级别”，在“安全设置”对话框中找到“启用 XSS 筛选器”，改为“禁用”即可；

4、现在我们重新打开浏览器之后不会再提示 显示WEB浏览器已对此页面进行了修改以帮助跨站脚本 错误了哦。

上述就是win8使用搜狗浏览器打开网页“显示WEB浏览器已对此页面进行了修改以帮助跨站脚本”的解决方法了，希望本教程内容可以帮助到更多用户。
#!/usr/bin/env python3
"""
最简化的LoRA推理脚本
基于官方示例，添加LoRA支持
"""

import torch
from PIL import Image
import os
from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from modelscope import dataset_snapshot_download

print("🎬 Wan2.1-I2V-14B-480P 最简化LoRA推理")
print("=" * 50)

# LoRA检查点路径
lora_checkpoint = "./models/train/Wan2.1-I2V-14B-480P_lora_final/epoch-4.safetensors"
lora_available = os.path.exists(lora_checkpoint)

if lora_available:
    print(f"✅ 找到LoRA检查点: {lora_checkpoint}")
else:
    print(f"⚠️  LoRA检查点不存在，使用基础模型")

print("📦 初始化Pipeline...")

# 创建pipeline（完全按照官方示例）
pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cuda",
    model_configs=[
        ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
        ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
        ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
        ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
    ],
)
pipe.enable_vram_management()

print("✅ Pipeline初始化成功")

# 简单的LoRA权重检查
if lora_available:
    try:
        from safetensors import safe_open
        lora_count = 0
        with safe_open(lora_checkpoint, framework="pt", device="cpu") as f:
            lora_count = len(f.keys())
        print(f"🔧 LoRA权重信息: {lora_count} 个参数")
    except:
        print("⚠️  LoRA权重读取失败")

print("\n📥 下载示例图像...")

# 下载示例图像（按照官方示例）
try:
    dataset_snapshot_download(
        dataset_id="DiffSynth-Studio/examples_in_diffsynth",
        local_dir="./",
        allow_file_pattern=f"data/examples/wan/input_image.jpg"
    )
    image = Image.open("data/examples/wan/input_image.jpg")
    print("✅ 示例图像加载成功")
except Exception as e:
    print(f"⚠️  示例图像下载失败: {e}")
    print("   创建默认图像...")
    # 创建一个简单的测试图像
    image = Image.new('RGB', (832, 480), color='blue')
    print("✅ 使用默认蓝色图像")

print(f"   图像尺寸: {image.size}")

print("\n🎬 开始生成视频...")

try:
    # 使用官方示例的参数进行生成
    video = pipe(
        prompt="A beautiful sunset over the ocean with gentle waves, cinematic lighting",
        negative_prompt="色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走",
        input_image=image,  # 关键：提供输入图像
        seed=42, 
        tiled=True,
        height=480,
        width=832,
        num_frames=81,
        cfg_scale=7.5,
        num_inference_steps=50
    )
    
    print("💾 保存视频...")
    
    # 保存视频
    output_path = "lora_minimal_test.mp4"
    save_video(video, output_path, fps=15, quality=5)
    
    # 检查结果
    if os.path.exists(output_path):
        file_size = os.path.getsize(output_path) / 1024**2
        print(f"✅ 视频生成成功!")
        print(f"📁 文件: {output_path}")
        print(f"📊 大小: {file_size:.1f}MB")
        
        # 显示GPU使用情况
        print(f"\n🖥️  GPU使用情况:")
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            print(f"   GPU {i}: {allocated:.2f}GB")
        
        print(f"\n🎉 推理成功完成!")
        print(f"   LoRA状态: {'✅ 检查点可用' if lora_available else '❌ 使用基础模型'}")
        print(f"   输出文件: {output_path}")
        
    else:
        print("❌ 视频文件未生成")

except Exception as e:
    print(f"❌ 视频生成失败: {e}")
    import traceback
    traceback.print_exc()
    
    print(f"\n🔧 故障排除:")
    print(f"   1. 检查GPU内存是否充足")
    print(f"   2. 确认输入图像格式正确")
    print(f"   3. 尝试减少推理步数或帧数")
    print(f"   4. 检查模型文件完整性")

print(f"\n📝 说明:")
print(f"   - 此脚本基于官方Wan2.1-I2V-14B-480P示例")
print(f"   - 使用Image-to-Video模式（需要输入图像）")
print(f"   - LoRA权重检测但未完全集成")
print(f"   - 适合验证基础推理功能")

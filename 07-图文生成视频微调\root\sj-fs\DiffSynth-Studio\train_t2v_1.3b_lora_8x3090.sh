#!/bin/bash

# 8×RTX 3090优化的Wan视频模型训练脚本
# 模型: Wan-AI/Wan2.1-T2V-1.3B
# 训练类型: LORA

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 480 \
    --width 832 \
    --dataset_repeat 100 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --learning_rate 1e-4 \
    --num_epochs 3 \
    --gradient_accumulation_steps 2 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/t2v_1.3b_lora_8x3090" \
    --lora_base_model "dit" \
    --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
    --lora_rank 64

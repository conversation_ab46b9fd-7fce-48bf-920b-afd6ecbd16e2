﻿# Moonshot AI 大模型服务 Kimi Chat 开始内测，火山引擎提供训练推理加速解决方案

**发布日期**: 2023年10月10日

**原文链接**: https://www.geekpark.net/news/325760

## 📄 原文内容

10 月 9 日, 北京月之暗面科技有限公司 (Moonshot AI) 宣布在「长文本」领域实现了突破, 推出了首个支持输入 20 万汉字的智能助手产品 Kimi Chat。这是目前全球市场上能够产品化使用的大模型服务中所能支持的最长上下文输入长度, 标志着 Moonshot AI 在这一重要技术上取得了世界领先水平。

火山引擎与 Moonshot AI 深度合作, 为其独家提供高稳定性和高性价比的 AI 训练和推理加速解决方案, 双方联合进行技术研发, 共同推进大型语言模型在垂直领域和通用场景的应用落地。同时,Kimi Chat 即将入驻火山引擎大模型服务平台——火山方舟, 双方将持续在大模型生态领域为企业和消费者提供更丰富的 AI 应用。

相比当前市面上以英文为基础训练的大模型服务,<PERSON><PERSON> Chat 具备较强的多语言能力, 例如,Kimi Chat 在中文上具备显著优势, 实际使用效果能够支持约 20 万汉字的上下文,2.5 倍于 Anthropic 公司的 Claude-100k(实测约 8 万字),8 倍于 OpenAI 公司的 GPT-4-32k(实测约 2.5 万字)。同时,Kimi Chat 能够通过创新的网络结构和工程优化, 在千亿参数下实现了无损的长程注意力机制, 不依赖于滑动窗口、降采样、小模型等对性能损害较大的「捷径」方案。

Moonshot AI 创始人杨植麟此前在接受采访时曾表示, 无论是文字、语音还是视频, 对海量数据的无损压缩可以实现高程度的智能。大模型的能力上限 (即无损压缩比) 是由单步能力和执行的步骤数共同决定的, 前者与参数量相关, 后者即上下文长度。

应对大语言模型落地挑战, 推动行业应用落地

Moonshot AI 相信, 更长的上下文长度可以为大模型应用带来全新的篇章, 促使大模型从 LLM 时代进入 Long LLM (LLLM) 时代, 与千行百业精准适配。大模型应用在寻找有效处理长文本场景的方法时, 需要持续探索解决降低模型幻觉和提高生成内容可控性的新手段, 寻求大模型能力个性化的新路线。在大型语言模型研发过程中, 还需要跨越算力资源需求膨胀、任务工程稳定性差、项目成本高昂、安全与信任等诸多门槛, 以提升模型的训练效率。

为了解决上述问题,Moonshot AI 携手火山引擎进行 AI 技术创新, 在火山引擎机器学习平台 veMLP 上进行 AGI 实践。Moonshot AI 充分利用 GPU 资源池, 基于大规模预训练模型, 实现每天数千卡规模的常态稳定训练, 六个月内训练出千亿参数规模语言大模型 Kimi Chat, 解锁专业场景写作、超长文本理解分析、超长记忆的个性化对话、基于大量文档的知识问答等复杂场景, 并成功在多家知名企业中应用。

Moonshot AI 联合创始人周昕宇表示:「Moonshot AI 聚焦于探索通用人工智能的边界, 致力于寻求将算力转化为智能的最优解。火山引擎拥有国内领先的基础设施能力和算力储备, 未来双方将进一步在 AI 算力基础设施和应用场景拓展等方面展开合作, 共同推动人工智能技术的发展, 为用户带来稳定、高效、智能的服务体验。」

基于火山引擎机器学习平台, 大模型训练更稳、更快

火山引擎为大模型的构建和训练提供高稳定性和高性价比的 AI 训练和推理加速解决方案, 其机器学习平台 veMLP 经过抖音等海量用户业务长期打磨, 沉淀形成了全栈 AI 开发工程优化方案、任务故障自愈、实验可观测性等解决方案和最佳实践, 提供高效、稳定、安全互信的一站式 AI 算法开发和迭代服务, 让大模型训练更快、更稳、更高性价比。Moonshot AI 基于火山引擎提供的超大规模 AI 训练和推理加速解决方案, 帮助团队快速、稳定、低成本地实现大型语言模型的持续训练迭代、精调和推理。

1.IaaS 算力和存储资源的规模化调度

搭建高性能计算集群, 实现万卡级大模型训练、微秒级延迟网络, 弹性计算可节省 70% 算力成本;利用 vePFS+TOS 冷热分层加速方案, 满足训练数据高吞吐的同时整体存储成本降低 65%。针对大模型的文件系统读写 Pattern, 共同研发专用文件缓存系统, 大幅提升显卡利用率。

2.PaaS 计算集群稳定性保障

优化超大训练集群的稳定性, 提供硬件故障自愈优化和自主诊断能力, 允许用户任务迅速重试续训, 实现月级别的稳定训练, 通过多机训练任务通信亲和性优化, 减少 RingAllReduce 的跨交换机通信。

对多个训练任务做实验管理, 通过可视化方式对比训练结果, 以确定迭代上线的模型;利用完备监控日志, 助力业务调优 3D 并行参数, 辅助定位训练故障。

将可信隐私计算与 LLM 应用相结合, 提供安全沙箱功能, 完善开发人员权限管控。火山引擎还与 Moonshot AI 一起设计适合大模型研发习惯的工作流, 确保工作效率前提下, 实现数据的分级访问, 保证数据安全。

火山引擎智能算法负责人吴迪表示:「火山引擎一直秉持着聚焦技术、赋能伙伴、价值共生的合作态度,Moonshot AI 拥有国内先进的大模型研发团队, 对 AI 技术有着深入的理解和应用经验, 双方的合作将进一步在多模型生态服务领域为企业和消费者提供更丰富的 AI 应用。」

目前, 火山引擎大模型服务平台——火山方舟上已入驻智谱 AI、 Minimax、字节跳动云雀等多家 AI 科技公司及科研院所的大模型,Moonshot AI 的大模型服务 Kimi Chat 也将登陆火山方舟。火山引擎将持续联合国内优秀的大模型服务商, 提供模型训练、推理、评测、精调等全方位功能与服务, 助力千行百业加速 AI 进程。欢迎各位企业在方舟中体验大模型, 火山方舟愿与大家的业务共同成长!
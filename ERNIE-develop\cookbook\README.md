# ERNIE Cookbook

A collection of best practices for using open source ERNIE models.

<div align="center">

| Cookbook | Description | Gradio Demo |
| --- | --- | --- |
| [Conversation](/cookbook/notebook/conversation_demo_en.ipynb) | Building conversational applications.  | [conversation_demo.py](/cookbook/conversation_demo.py) |
| [Simple ERNIE Bot](/cookbook/notebook/simple_ernie_bot_demo_en.ipynb) | Creating a lightweight web-based ERNIE Bot.   |[simple_ernie_bot_demo.py](/cookbook/simple_ernie_bot_demo.py) |
| [Web-Search-Enhanced Conversation](/cookbook/notebook/web_search_demo_en.ipynb) | Building conversational apps with integrated web search. | [web_search_demo.py](/cookbook/web_search_demo.py) |
| [Knowledge Retrieval-based Q&A](/cookbook/notebook/knowledge_retrieval_demo_en.ipynb) | Building intelligent Q&A systems with private knowledge bases. | [knowledge_retrieval_demo.py](/cookbook/knowledge_retrieval_demo.py) |
| [Advanced Search](/cookbook/notebook/advanced_search_demo_en.ipynb)    | Building article-generation applications using deep information extraction. | [advanced_search_demo.py](/cookbook/advanced_search_demo.py) |
| [SFT tutorial](/cookbook/notebook/sft_tutorial_en.ipynb) | Optimizing task performance through supervised fine-tuning with ERNIEKit. | - |
| [DPO tutorial](/cookbook/notebook/dpo_tutorial_en.ipynb) | Aligning models with human preferences using ERNIEKit. | - |
| [Text Recognition](/cookbook/notebook/text_recognition_tutorial_en.ipynb) | A Comprehensive Guide to Developing Text Recognition for Non-Chinese and Non-English Languages Using ERNIE and PaddleOCR. | - |
| [Document Translation](/cookbook/notebook/document_translation_tutorial_en.ipynb)          |  Document Translation Practice Based on ERNIE and PaddleOCR. | - |
| [Key Information Extraction](/cookbook/notebook/key_information_extraction_tutorial_en.ipynb) |  Key Information Extraction in Contract Scenarios Based on ERNIE and PaddleOCR. | - |
</div>

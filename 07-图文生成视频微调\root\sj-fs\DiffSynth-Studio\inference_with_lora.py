#!/usr/bin/env python3
"""
使用训练好的LoRA模型进行Wan2.1-T2V-1.3B推理
"""

import torch
import os
from diffsynth import WanVideoPipeline, save_video
import argparse

def main():
    parser = argparse.ArgumentParser(description="使用LoRA模型进行Wan2.1-T2V-1.3B推理")
    parser.add_argument("--lora_path", type=str, 
                       default="/root/sj-fs/DiffSynth-Studio/models/train/memory_optimized_test/epoch-0.safetensors",
                       help="LoRA模型路径")
    parser.add_argument("--prompt", type=str, 
                       default="A beautiful sunset over the ocean with waves gently crashing on the shore",
                       help="文本提示词")
    parser.add_argument("--output_path", type=str, 
                       default="./output_lora_video.mp4",
                       help="输出视频路径")
    parser.add_argument("--height", type=int, default=320, help="视频高度")
    parser.add_argument("--width", type=int, default=576, help="视频宽度")
    parser.add_argument("--num_frames", type=int, default=16, help="视频帧数")
    parser.add_argument("--num_inference_steps", type=int, default=50, help="推理步数")
    parser.add_argument("--guidance_scale", type=float, default=7.5, help="引导尺度")
    
    args = parser.parse_args()
    
    print("🚀 开始使用LoRA模型进行Wan2.1-T2V-1.3B推理...")
    print(f"LoRA模型路径: {args.lora_path}")
    print(f"提示词: {args.prompt}")
    print(f"输出路径: {args.output_path}")
    
    # 检查LoRA模型是否存在
    if not os.path.exists(args.lora_path):
        print(f"❌ 错误: LoRA模型文件不存在: {args.lora_path}")
        return
    
    # 设置设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"使用设备: {device}")
    
    try:
        # 初始化管道
        print("📦 加载Wan2.1-T2V-1.3B基础模型...")
        pipe = WanVideoPipeline.from_pretrained(
            "Wan-AI/Wan2.1-T2V-1.3B",
            torch_dtype=torch.bfloat16,
            device=device
        )
        
        # 加载LoRA权重
        print("🔧 加载LoRA权重...")
        pipe.load_lora(args.lora_path)
        
        print("✅ 模型加载完成!")
        
        # 生成视频
        print("🎬 开始生成视频...")
        video = pipe(
            prompt=args.prompt,
            height=args.height,
            width=args.width,
            num_frames=args.num_frames,
            num_inference_steps=args.num_inference_steps,
            guidance_scale=args.guidance_scale,
        )
        
        # 保存视频
        print(f"💾 保存视频到: {args.output_path}")
        save_video(video, args.output_path, fps=8)
        
        print("✅ 视频生成完成!")
        print(f"输出文件: {args.output_path}")
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

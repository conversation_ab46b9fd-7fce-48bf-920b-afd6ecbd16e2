# 🎬 Wan2.1-I2V-14B-480P 微调训练套件

基于DiffSynth-Studio框架的Wan-AI/Wan2.1-I2V-14B-480P图像到视频模型微调训练完整解决方案。

## ✨ 特性

- 🎯 **专业I2V模型**: 针对图像到视频生成任务优化
- 🚀 **多GPU支持**: 支持8×RTX 3090分布式训练
- 💾 **显存优化**: 提供多种显存优化方案
- 🛠️ **完整工具链**: 数据处理、训练监控、推理测试一体化
- 📚 **详细文档**: 中文文档，新手友好

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
conda create -n wan_video_env python=3.12 -y
conda activate wan_video_env

# 安装依赖
pip install -e .
pip install deepspeed peft accelerate psutil matplotlib pandas
```

### 2. 数据准备

```bash
# 下载示例数据集
modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
```

### 3. 开始训练

```bash
# 显存优化训练 (推荐新手)
bash examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_memory_optimized.sh

# 8×RTX 3090分布式训练 (高性能)
bash examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_8x3090.sh
```

### 4. 测试结果

```bash
# 自动测试训练结果
python test_wan_i2v_lora.py --auto_detect
```

## 📁 文件结构

```
├── examples/wanvideo/model_training/lora/
│   ├── Wan2.1-I2V-14B-480P.sh                    # 标准LoRA训练
│   ├── Wan2.1-I2V-14B-480P_8x3090.sh            # 8×RTX 3090分布式训练
│   └── Wan2.1-I2V-14B-480P_memory_optimized.sh  # 显存优化训练
├── examples/wanvideo/model_training/configs/
│   └── accelerate_8x3090_i2v.yaml               # Accelerate配置
├── tools/
│   ├── wan_i2v_dataset_processor.py             # 数据集处理工具
│   ├── wan_training_monitor.py                  # 训练监控工具
│   └── gpu_monitor.py                           # GPU监控工具
├── test_wan_i2v_lora.py                         # 综合推理测试
├── WAN2.1-I2V-14B-480P_训练文档.md               # 详细训练文档
└── README_Wan2.1-I2V-14B-480P.md               # 本文件
```

## 🎯 训练配置

### 标准配置 (单GPU)
- **GPU**: RTX 3090 24GB × 1
- **分辨率**: 480×832
- **LoRA Rank**: 32
- **训练时间**: ~4小时 (5 epochs)

### 高性能配置 (多GPU)
- **GPU**: RTX 3090 24GB × 8
- **分辨率**: 480×832
- **LoRA Rank**: 64
- **训练时间**: ~1小时 (3 epochs)

### 显存优化配置
- **GPU**: RTX 3090 24GB × 1
- **分辨率**: 320×576 (降低)
- **LoRA Rank**: 16 (降低)
- **训练时间**: ~3小时 (2 epochs)

## 🛠️ 工具使用

### GPU监控
```bash
# 实时监控GPU状态
python tools/gpu_monitor.py --action monitor

# 检查训练就绪状态
python tools/gpu_monitor.py --action check
```

### 数据集处理
```bash
# 从视频创建I2V数据集
python tools/wan_i2v_dataset_processor.py \
    --action create_from_videos \
    --input_dir /path/to/videos \
    --output_dir ./data/custom_dataset

# 验证数据集
python tools/wan_i2v_dataset_processor.py \
    --action validate \
    --input_dir ./data/custom_dataset
```

### 训练监控
```bash
# 系统检查
python tools/wan_training_monitor.py --action check

# 生成优化配置
python tools/wan_training_monitor.py --action optimize --output optimized.sh
```

## 📊 性能基准

| 配置 | GPU | 显存使用 | 训练时间 | 推理速度 |
|------|-----|----------|----------|----------|
| 标准 | 1×RTX 3090 | ~20GB | 4小时 | ~30秒/视频 |
| 高性能 | 8×RTX 3090 | ~18GB/卡 | 1小时 | ~30秒/视频 |
| 优化 | 1×RTX 3090 | ~16GB | 3小时 | ~25秒/视频 |

## 🔧 故障排除

### 常见问题

**Q: CUDA内存不足怎么办？**
A: 使用显存优化脚本或降低分辨率和LoRA rank。

**Q: 模型下载失败？**
A: 检查网络连接，可设置代理或使用镜像站点。

**Q: 训练中断如何恢复？**
A: 训练会自动保存检查点，重新运行脚本即可继续。

**Q: 如何评估训练效果？**
A: 使用推理测试脚本生成视频，观察视频质量和动态效果。

### 性能优化

1. **显存优化**: 降低分辨率、减小LoRA rank、启用梯度检查点
2. **速度优化**: 使用多GPU、增加数据加载进程、使用SSD存储
3. **质量优化**: 使用高质量数据集、适当的学习率、充分的训练轮数

## 📚 详细文档

完整的训练指南请参考: [WAN2.1-I2V-14B-480P_训练文档.md](WAN2.1-I2V-14B-480P_训练文档.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个训练套件。

## 📄 许可证

本项目遵循DiffSynth-Studio的许可证条款。

## 🙏 致谢

- DiffSynth-Studio团队提供的优秀框架
- Wan-AI团队开发的高质量模型
- ModelScope平台的模型托管服务

---

*最后更新: 2025-07-10*

#!/usr/bin/env python3
"""
数据集质量检查工具
检查视频数据集的完整性和质量
"""

import pandas as pd
import cv2
import os
from pathlib import Path
import argparse
import json

def check_dataset_quality(metadata_path, detailed=False):
    """检查数据集质量"""
    
    print("🔍 开始数据集质量检查...")
    
    if not os.path.exists(metadata_path):
        print(f"❌ metadata文件不存在: {metadata_path}")
        return False
    
    # 读取metadata
    try:
        df = pd.read_csv(metadata_path)
        print(f"✅ 成功读取metadata文件，包含 {len(df)} 条记录")
    except Exception as e:
        print(f"❌ 读取metadata文件失败: {e}")
        return False
    
    base_dir = Path(metadata_path).parent
    
    # 基础统计信息
    print(f"\n📊 数据集基础统计:")
    print(f"总视频数: {len(df)}")
    
    if 'duration' in df.columns:
        print(f"总时长: {df['duration'].sum():.2f}秒 ({df['duration'].sum()/60:.1f}分钟)")
        print(f"平均时长: {df['duration'].mean():.2f}秒")
        print(f"时长范围: {df['duration'].min():.2f}s - {df['duration'].max():.2f}s")
    
    if 'width' in df.columns and 'height' in df.columns:
        print(f"分辨率范围: {df['width'].min()}x{df['height'].min()} - {df['width'].max()}x{df['height'].max()}")
        
        # 统计常见分辨率
        resolutions = df.groupby(['width', 'height']).size().sort_values(ascending=False)
        print(f"常见分辨率:")
        for (w, h), count in resolutions.head(5).items():
            print(f"  {w}x{h}: {count}个视频")
    
    if 'fps' in df.columns:
        print(f"帧率范围: {df['fps'].min():.1f} - {df['fps'].max():.1f} fps")
        print(f"平均帧率: {df['fps'].mean():.1f} fps")
    
    # 检查必需字段
    print(f"\n📋 字段检查:")
    required_fields = ['video_path', 'prompt']
    for field in required_fields:
        if field in df.columns:
            missing_count = df[field].isna().sum()
            if missing_count > 0:
                print(f"⚠️ {field}: {missing_count} 个缺失值")
            else:
                print(f"✅ {field}: 完整")
        else:
            print(f"❌ {field}: 字段缺失")
    
    # 检查文件是否存在
    print(f"\n📁 文件存在性检查:")
    missing_files = []
    corrupted_files = []
    
    for idx, row in df.iterrows():
        video_path = base_dir / row['video_path']
        
        if not video_path.exists():
            missing_files.append(row['video_path'])
        elif detailed:
            # 详细检查：尝试打开视频文件
            try:
                cap = cv2.VideoCapture(str(video_path))
                if not cap.isOpened():
                    corrupted_files.append(row['video_path'])
                cap.release()
            except:
                corrupted_files.append(row['video_path'])
    
    if missing_files:
        print(f"❌ 缺失文件: {len(missing_files)} 个")
        if len(missing_files) <= 10:
            for file in missing_files:
                print(f"  - {file}")
        else:
            for file in missing_files[:5]:
                print(f"  - {file}")
            print(f"  ... 还有 {len(missing_files)-5} 个文件")
    else:
        print("✅ 所有视频文件都存在")
    
    if detailed and corrupted_files:
        print(f"⚠️ 可能损坏的文件: {len(corrupted_files)} 个")
        for file in corrupted_files[:5]:
            print(f"  - {file}")
    
    # 检查提示词质量
    print(f"\n📝 提示词质量检查:")
    if 'prompt' in df.columns:
        # 检查空提示词
        empty_prompts = df[df['prompt'].str.strip() == '']
        if len(empty_prompts) > 0:
            print(f"❌ 空提示词: {len(empty_prompts)} 个")
        
        # 检查过短提示词
        short_prompts = df[df['prompt'].str.len() < 10]
        if len(short_prompts) > 0:
            print(f"⚠️ 过短提示词 (<10字符): {len(short_prompts)} 个")
        
        # 检查过长提示词
        long_prompts = df[df['prompt'].str.len() > 200]
        if len(long_prompts) > 0:
            print(f"⚠️ 过长提示词 (>200字符): {len(long_prompts)} 个")
        
        # 提示词长度统计
        avg_prompt_len = df['prompt'].str.len().mean()
        print(f"📏 平均提示词长度: {avg_prompt_len:.1f} 字符")
        
        # 检查重复提示词
        duplicate_prompts = df[df['prompt'].duplicated()]
        if len(duplicate_prompts) > 0:
            print(f"⚠️ 重复提示词: {len(duplicate_prompts)} 个")
    
    # 检查类别分布（如果有category字段）
    if 'category' in df.columns:
        print(f"\n📊 类别分布:")
        category_counts = df['category'].value_counts()
        for category, count in category_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {category}: {count} 个 ({percentage:.1f}%)")
    
    # 生成质量报告
    quality_score = calculate_quality_score(df, missing_files, corrupted_files)
    print(f"\n🎯 数据集质量评分: {quality_score:.1f}/100")
    
    if quality_score >= 90:
        print("🎉 数据集质量优秀，可以直接用于训练")
    elif quality_score >= 70:
        print("✅ 数据集质量良好，建议修复发现的问题后训练")
    elif quality_score >= 50:
        print("⚠️ 数据集质量一般，需要修复多个问题")
    else:
        print("❌ 数据集质量较差，建议重新准备数据")
    
    return quality_score >= 70

def calculate_quality_score(df, missing_files, corrupted_files):
    """计算数据集质量评分"""
    
    score = 100
    
    # 文件完整性 (30分)
    if len(missing_files) > 0:
        missing_ratio = len(missing_files) / len(df)
        score -= min(30, missing_ratio * 100)
    
    if len(corrupted_files) > 0:
        corrupted_ratio = len(corrupted_files) / len(df)
        score -= min(10, corrupted_ratio * 50)
    
    # 提示词质量 (40分)
    if 'prompt' in df.columns:
        empty_prompts = len(df[df['prompt'].str.strip() == ''])
        short_prompts = len(df[df['prompt'].str.len() < 10])
        
        if empty_prompts > 0:
            score -= min(20, (empty_prompts / len(df)) * 100)
        
        if short_prompts > 0:
            score -= min(20, (short_prompts / len(df)) * 50)
    else:
        score -= 40  # 没有提示词字段
    
    # 数据完整性 (20分)
    required_fields = ['video_path', 'prompt']
    for field in required_fields:
        if field not in df.columns:
            score -= 10
        elif df[field].isna().sum() > 0:
            score -= 5
    
    # 数据规模 (10分)
    if len(df) < 10:
        score -= 10
    elif len(df) < 50:
        score -= 5
    
    return max(0, score)

def main():
    parser = argparse.ArgumentParser(description="检查数据集质量")
    parser.add_argument("--metadata_path", type=str, required=True, help="metadata.csv文件路径")
    parser.add_argument("--detailed", action="store_true", help="执行详细检查（包括视频文件完整性）")
    parser.add_argument("--output", type=str, help="输出检查报告到文件")
    
    args = parser.parse_args()
    
    # 执行质量检查
    is_good_quality = check_dataset_quality(args.metadata_path, args.detailed)
    
    # 输出建议
    print(f"\n💡 建议:")
    if is_good_quality:
        print("✅ 数据集质量良好，可以开始训练")
        print("训练命令示例:")
        dataset_dir = Path(args.metadata_path).parent
        print(f"accelerate launch examples/wanvideo/model_training/train.py \\")
        print(f"  --dataset_base_path {dataset_dir} \\")
        print(f"  --dataset_metadata_path {args.metadata_path} \\")
        print(f"  --height 320 --width 576 \\")
        print(f"  --num_epochs 2")
    else:
        print("⚠️ 建议修复发现的问题后再开始训练")
        print("常见修复方法:")
        print("1. 检查缺失的视频文件路径")
        print("2. 完善过短或空白的提示词")
        print("3. 确保所有必需字段都有值")
    
    # 保存报告（如果指定了输出文件）
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(f"数据集质量检查报告\n")
            f.write(f"检查时间: {pd.Timestamp.now()}\n")
            f.write(f"数据集路径: {args.metadata_path}\n")
            f.write(f"质量评分: {calculate_quality_score(pd.read_csv(args.metadata_path), [], []):.1f}/100\n")
        print(f"📄 检查报告已保存到: {args.output}")

if __name__ == "__main__":
    main()

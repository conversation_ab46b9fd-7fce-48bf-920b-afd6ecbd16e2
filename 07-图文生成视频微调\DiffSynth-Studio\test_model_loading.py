#!/usr/bin/env python3
"""
测试模型加载脚本
"""

import torch
import os
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

# 设置环境变量
os.environ["TOKENIZERS_PARALLELISM"] = "false"

def test_model_loading():
    print("开始测试模型加载...")
    
    # 检查CUDA
    print(f"CUDA可用: {torch.cuda.is_available()}")
    print(f"GPU数量: {torch.cuda.device_count()}")
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 模型配置
    model_configs = [
        ModelConfig(
            model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
            origin_file_pattern="diffusion_pytorch_model*.safetensors"
        ),
        ModelConfig(
            model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
            origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth"
        ),
        ModelConfig(
            model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
            origin_file_pattern="Wan2.1_VAE.pth"
        ),
        ModelConfig(
            model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
            origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth"
        ),
    ]
    
    try:
        print("正在加载模型...")
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16, 
            device="cpu",  # 先在CPU上加载
            model_configs=model_configs
        )
        print("✓ 模型加载成功!")
        
        # 检查模型组件
        print(f"Pipeline组件: {list(pipe.__dict__.keys())}")
        
        # 设置训练调度器
        pipe.scheduler.set_timesteps(1000, training=True)
        print("✓ 训练调度器设置成功!")
        
        # 冻结模型
        pipe.freeze_except([])
        print("✓ 模型冻结成功!")
        
        print("模型加载测试完成!")
        return True
        
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_loading()
    if success:
        print("🎉 测试通过!")
    else:
        print("❌ 测试失败!")

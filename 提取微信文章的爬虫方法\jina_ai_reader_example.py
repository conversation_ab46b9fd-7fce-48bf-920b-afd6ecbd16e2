# jina_ai_reader_example.py

# 导入 requests 库，用于发送 HTTP 请求
import requests
import json

# --- 示例 1: 刮取单个网页内容 ---
print("\n--- 示例 1: 刮取单个网页内容 ---")

# 定义要刮取的网页 URL
original_url = "https://www.example.com"

# Jina AI Reader API 的基础 URL，用于刮取单个网页
jina_reader_url = f"https://r.jina.ai/{original_url}"

print(f"正在通过 Jina AI Reader API 刮取 URL: {original_url}")

try:
    # 发送 GET 请求到 Jina AI Reader API
    response = requests.get(jina_reader_url)
    response.raise_for_status() # 检查请求是否成功 (状态码 2xx)

    # 打印返回的文本内容
    # Jina AI Reader API 通常会返回 LLM 友好的 Markdown 格式内容
    print("\n--- 刮取到的内容 (Markdown 格式) ---")
    print(response.text[:1000]) # 打印前1000个字符

except requests.exceptions.RequestException as e:
    print(f"刮取失败: {e}")

# --- 示例 2: 刮取搜索引擎结果页面 (SERP) ---
print("\n--- 示例 2: 刮取搜索引擎结果页面 (SERP) ---")

# 定义搜索查询
search_query = "AI 爬虫"

# Jina AI Reader API 的基础 URL，用于刮取搜索结果
# 注意：这里使用了 s.jina.ai/?q= 前缀
jina_serp_url = f"https://s.jina.ai/?q={search_query}"

print(f"正在通过 Jina AI Reader API 刮取搜索结果: {search_query}")

try:
    # 发送 GET 请求到 Jina AI Reader API
    response = requests.get(jina_serp_url)
    response.raise_for_status() # 检查请求是否成功 (状态码 2xx)

    # 打印返回的 JSON 内容
    # SERP 模式通常会返回包含多个搜索结果的 JSON 数组
    print("\n--- 刮取到的搜索结果 (JSON 格式) ---")
    # 使用 json.dumps 格式化输出，使其更易读
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))

except requests.exceptions.RequestException as e:
    print(f"刮取失败: {e}")

print("\nJina AI Reader API 示例执行完毕。")



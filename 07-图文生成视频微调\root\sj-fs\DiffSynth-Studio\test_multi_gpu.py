
import torch
import torch.distributed as dist
import os

def test_multi_gpu():
    if torch.cuda.device_count() < 2:
        print("⚠️  GPU数量不足，无法测试多GPU通信")
        return
    
    print(f"🔍 测试{torch.cuda.device_count()}个GPU的通信...")
    
    # 测试基本GPU操作
    for i in range(min(8, torch.cuda.device_count())):
        device = torch.device(f'cuda:{i}')
        x = torch.randn(1000, 1000, device=device)
        y = torch.randn(1000, 1000, device=device)
        z = torch.mm(x, y)
        print(f"✅ GPU {i}: 矩阵运算测试通过 {z.shape}")
    
    print("✅ 多GPU基础通信测试完成")

if __name__ == "__main__":
    test_multi_gpu()

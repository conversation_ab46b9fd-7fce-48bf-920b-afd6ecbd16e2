"""
卡密管理系统
"""
import json
import os
import uuid
import datetime
import hashlib
import secrets
import string

# 数据文件路径
CARDS_FILE = 'data/cards.json'
CARD_USAGE_FILE = 'data/card_usage.json'

def init_card_database():
    """初始化卡密数据库"""
    os.makedirs('data', exist_ok=True)
    
    # 初始化卡密文件
    if not os.path.exists(CARDS_FILE):
        with open(CARDS_FILE, 'w', encoding='utf-8') as f:
            json.dump({}, f, ensure_ascii=False, indent=2)
    
    # 初始化使用记录文件
    if not os.path.exists(CARD_USAGE_FILE):
        with open(CARD_USAGE_FILE, 'w', encoding='utf-8') as f:
            json.dump({}, f, ensure_ascii=False, indent=2)

def generate_card_key():
    """生成卡密，格式类似 sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"""
    # 生成32位随机字符串
    random_part = ''.join(secrets.choice(string.ascii_lowercase + string.digits) for _ in range(32))
    return f"sk-{random_part}"

def create_vip_card(days=30, description="VIP充值卡"):
    """创建VIP充值卡"""
    try:
        # 读取现有卡密
        with open(CARDS_FILE, 'r', encoding='utf-8') as f:
            cards = json.load(f)
        
        # 生成新卡密
        card_key = generate_card_key()
        card_id = str(uuid.uuid4())
        
        # 卡密信息
        card_info = {
            'id': card_id,
            'card_key': card_key,
            'type': 'vip',
            'days': days,
            'description': description,
            'status': 'unused',  # unused, used, expired
            'created_at': datetime.datetime.now().isoformat(),
            'created_by': 'admin',
            'used_at': None,
            'used_by': None
        }
        
        # 保存卡密
        cards[card_key] = card_info
        
        with open(CARDS_FILE, 'w', encoding='utf-8') as f:
            json.dump(cards, f, ensure_ascii=False, indent=2)
        
        return True, card_key, card_info
        
    except Exception as e:
        return False, None, str(e)

def validate_card(card_key):
    """验证卡密是否有效"""
    try:
        with open(CARDS_FILE, 'r', encoding='utf-8') as f:
            cards = json.load(f)
        
        if card_key not in cards:
            return False, "卡密不存在"
        
        card = cards[card_key]
        
        if card['status'] != 'unused':
            return False, f"卡密已{card['status']}"
        
        return True, card
        
    except Exception as e:
        return False, f"验证失败: {str(e)}"

def use_card(card_key, user_id, username):
    """使用卡密"""
    try:
        # 验证卡密
        is_valid, result = validate_card(card_key)
        if not is_valid:
            return False, result
        
        card = result
        
        # 读取卡密数据
        with open(CARDS_FILE, 'r', encoding='utf-8') as f:
            cards = json.load(f)
        
        # 标记卡密为已使用
        cards[card_key]['status'] = 'used'
        cards[card_key]['used_at'] = datetime.datetime.now().isoformat()
        cards[card_key]['used_by'] = username
        cards[card_key]['user_id'] = user_id
        
        # 保存更新
        with open(CARDS_FILE, 'w', encoding='utf-8') as f:
            json.dump(cards, f, ensure_ascii=False, indent=2)
        
        # 记录使用历史
        record_card_usage(card_key, user_id, username, card)
        
        return True, card
        
    except Exception as e:
        return False, f"使用失败: {str(e)}"

def record_card_usage(card_key, user_id, username, card_info):
    """记录卡密使用历史"""
    try:
        with open(CARD_USAGE_FILE, 'r', encoding='utf-8') as f:
            usage_records = json.load(f)
        
        usage_id = str(uuid.uuid4())
        usage_record = {
            'id': usage_id,
            'card_key': card_key,
            'card_type': card_info['type'],
            'card_days': card_info['days'],
            'user_id': user_id,
            'username': username,
            'used_at': datetime.datetime.now().isoformat(),
            'description': card_info['description']
        }
        
        usage_records[usage_id] = usage_record
        
        with open(CARD_USAGE_FILE, 'w', encoding='utf-8') as f:
            json.dump(usage_records, f, ensure_ascii=False, indent=2)
        
    except Exception as e:
        print(f"记录使用历史失败: {e}")

def get_all_cards():
    """获取所有卡密"""
    try:
        with open(CARDS_FILE, 'r', encoding='utf-8') as f:
            cards = json.load(f)
        return list(cards.values())
    except:
        return []

def get_card_usage_records():
    """获取卡密使用记录"""
    try:
        with open(CARD_USAGE_FILE, 'r', encoding='utf-8') as f:
            records = json.load(f)
        return list(records.values())
    except:
        return []

def delete_card(card_key):
    """删除卡密"""
    try:
        with open(CARDS_FILE, 'r', encoding='utf-8') as f:
            cards = json.load(f)
        
        if card_key in cards:
            del cards[card_key]
            
            with open(CARDS_FILE, 'w', encoding='utf-8') as f:
                json.dump(cards, f, ensure_ascii=False, indent=2)
            
            return True, "删除成功"
        else:
            return False, "卡密不存在"
            
    except Exception as e:
        return False, f"删除失败: {str(e)}"

def get_card_stats():
    """获取卡密统计信息"""
    try:
        cards = get_all_cards()
        usage_records = get_card_usage_records()
        
        stats = {
            'total_cards': len(cards),
            'unused_cards': len([c for c in cards if c['status'] == 'unused']),
            'used_cards': len([c for c in cards if c['status'] == 'used']),
            'total_usage': len(usage_records),
            'recent_usage': len([r for r in usage_records if 
                               datetime.datetime.fromisoformat(r['used_at']) > 
                               datetime.datetime.now() - datetime.timedelta(days=7)])
        }
        
        return stats
        
    except Exception as e:
        return {
            'total_cards': 0,
            'unused_cards': 0,
            'used_cards': 0,
            'total_usage': 0,
            'recent_usage': 0
        }

def archive_used_cards(card_keys):
    """批量归档已使用的卡密"""
    try:
        # 读取现有卡密
        with open(CARDS_FILE, 'r', encoding='utf-8') as f:
            cards = json.load(f)

        # 准备归档数据
        archived_cards = {}
        cards_to_remove = []

        for card_key in card_keys:
            if card_key in cards:
                card = cards[card_key]
                # 只归档已使用的卡密
                if card['status'] == 'used':
                    archived_cards[card_key] = card
                    cards_to_remove.append(card_key)

        if not archived_cards:
            return False, "没有找到可归档的已使用卡密"

        # 创建归档文件名（按日期）
        archive_date = datetime.datetime.now().strftime('%Y%m%d')
        archive_file = f'data/archived_cards_{archive_date}.json'

        # 读取现有归档数据（如果存在）
        existing_archive = {}
        if os.path.exists(archive_file):
            try:
                with open(archive_file, 'r', encoding='utf-8') as f:
                    existing_archive = json.load(f)
            except:
                existing_archive = {}

        # 合并归档数据
        existing_archive.update(archived_cards)

        # 保存归档文件
        with open(archive_file, 'w', encoding='utf-8') as f:
            json.dump(existing_archive, f, ensure_ascii=False, indent=2)

        # 从主卡密文件中移除已归档的卡密
        for card_key in cards_to_remove:
            del cards[card_key]

        # 保存更新后的卡密文件
        with open(CARDS_FILE, 'w', encoding='utf-8') as f:
            json.dump(cards, f, ensure_ascii=False, indent=2)

        return True, f"成功归档 {len(archived_cards)} 张已使用的卡密到 {archive_file}"

    except Exception as e:
        return False, f"归档失败: {str(e)}"

# 初始化数据库
init_card_database()

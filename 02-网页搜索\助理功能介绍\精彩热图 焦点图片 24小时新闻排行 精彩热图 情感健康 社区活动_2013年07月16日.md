﻿# 精彩热图 焦点图片 24小时新闻排行 精彩热图 情感健康 社区活动

**发布日期**: 2013年07月16日

**原文链接**: http://www.chinadaily.com.cn/tech/2013-07/16/content_16783222.htm

## 📄 原文内容

盖茨称个人助理功能Microsoft Bob将再次推出
            var meta = document.getElementsByTagName('meta');
            var content;  
            for(var i=0;i<meta.length;i++){
                if (meta[i].getAttribute('name') == "description")
                content = meta[i].getAttribute('content');
            content = content;
            bShare.addEntry({
            summary:content,
            content:content
免费订阅30天China Daily双语新闻手机报：移动用户编辑短信CD至106580009009
原标题: 盖茨称个人助理功能Microsoft Bob将再次推出
7月16日消息，据国外媒体报道，在本周一开幕的2013年微软研究院学术峰会（MSR Faculty Summit）上，微软创始人、董事长比尔?盖茨（Bill Gates）发表演讲时称，1995年推出的旨在介绍Windows功能的软件MicrosoftBob确实没有做好，但他认为这样的“个人助理”功能将再次推出，只是会变得更为复杂。
盖茨认为，Microsoft Bob或至少类似概念的产品会再次推出，因为智能个人助理已成为日常计算的一部分。在1995年1月举行的消费电子展上，盖茨亲自推出了Microsoft Bob，该软件显示了一座虚拟的房子，里面有房间、门和卡通形象的助手，以帮助用户了解Windows和使用微软的一些应用。例如，用户可以通过点击虚拟房子的一个门环进行登录，或点击挂在墙上的日历打开日历应用。
盖茨在演讲中称，Microsoft Bob没有做好，但他认为基于这一概念的产品将再次推出，只是变得更为复杂。他说：“像大多数我们所犯的错误一样，我们有点超前。”
Microsoft Bob推出后未能获得用户的青睐，他们习惯于简单的图标和文件夹，不需要由一只可爱的小狗通过卡通气泡来提供使用说明。Microsoft Bob存在的时间很短暂，早在1996年初就退出了舞台，但其理念在Office助手Clippy中得到延续。Clippy是微软Office 97至2003的一项功能，一直是批评家嘲笑的对象。
盖茨表示，新一代的个人助理将更适合于计划各种活动，如以某种方式找到一个礼物或组织一次旅行。Microsoft Bob推出后不再有一只小狗，取而代之的是来自云计算的无形声音。新一代Microsoft Bob将整合进Windows 8用户界面，能“理解”用户所做的一切——或者愿意与用户在线分享信息——以及预测用户的需求和随时随地并在任何设备上显示相关信息。
到目前为止，只有苹果Siri和谷歌的Google Now提供来自云计算的智能个人助理功能。Microsoft Bob的确有必要卷土重来。
document.write('<script type="text/javascript" reload="1" src="http://cbbs.chinadaily.com.cn/plugin.php?id=reply:js">'+'<\/scri'+'pt><div id="hiddenDivReply"><\/div>');
     * JSONP回调函数, 读取并显示推广位内容
     * @param   {JSON} data 新闻数据
                数据格式: {
                            data: [
                                {
                                    title: "辽宁舰走你指挥员影像曝光",
                                    imgURL: "http://p1.qhimg.com/t0188932e9b5d61a811.jpg",
                                    shURL: "http://sh.qihoo.com/?src=lm&mt=10001_1&v=3&p=1"
                                },
                                {
                                    .......
                                }
                            ]
                        }
    window.soNewsCallback_10089 = function(data) {
        var newsContainer = document.getElementById('sh-qihoo-position-10089');
        /* 每条新闻的HTML模板，请按照您网站的格式修改 */
        var newsTemplate = '<li class="news-item">' +
                                '<a href="{shURL}" target="_blank" title="{title}">' +
                                '    <img src="{imgURL}" />' +
                                '    <span class="title">{title}<\/span>' +
                                '<\/a>' +
                           '<\/li>';
         * 将模板中的占位符替换为对应的内容
         * @method   template
         * @param   {string} template 模板
         * @param   {JSON} filler 填充数据
        var template = function(template, filler) {
            for(attr in filler) {
                template = template.replace(new RegExp('{' + attr + '}', 'img'), filler[attr]);
            return template;
        var newsList = data['data'];
        var newsHTML = '';
        for(var i = 0, len = newsList.length; i < len; i++) {
            var news = newsList[i],
                title = news['title'],
                imgURL = news['img'],
                shURL = news['url'];
            newsHTML += template(newsTemplate, {title: title, imgURL: imgURL, shURL: shURL});
        if(newsHTML) newsContainer.innerHTML = newsHTML;
    var script = document.createElement('script'),
        head = document.getElementsByTagName('head')[0] || document.documentElement;
    script.src = "http://s.lianmeng.360.cn/media/news/union_mt_10089.js?t=" + Math.random();
    script.charset = 'UTF-8';
    head.appendChild(script);
     * JSONP回调函数, 读取并显示推广位内容
     * @param   {JSON} data 新闻数据
                数据格式: {
                            data: [
                                {
                                    title: "辽宁舰走你指挥员影像曝光",
                                    imgURL: "http://p1.qhimg.com/t0188932e9b5d61a811.jpg",
                                    shURL: "http://sh.qihoo.com/?src=lm&mt=10001_1&v=3&p=1"
                                },
                                {
                                    .......
                                }
                            ]
                        }
    window.soNewsCallback_10088 = function(data) {
        var newsContainer = document.getElementById('sh-qihoo-position-10088');
        /* 每条新闻的HTML模板，请按照您网站的格式修改 */
        var newsTemplate = '<li class="news-item">' +
                                '<a href="{shURL}" target="_blank" title="{title}">' +
                                '    <img src="{imgURL}" />' +
                                '    <span class="title">{title}<\/span>' +
                                '<\/a>' +
                           '<\/li>';
         * 将模板中的占位符替换为对应的内容
         * @method   template
         * @param   {string} template 模板
         * @param   {JSON} filler 填充数据
        var template = function(template, filler) {
            for(attr in filler) {
                template = template.replace(new RegExp('{' + attr + '}', 'img'), filler[attr]);
            return template;
        var newsList = data['data'];
        var newsHTML = '';
        for(var i = 0, len = newsList.length; i < len; i++) {
            var news = newsList[i],
                title = news['title'],
                imgURL = news['img'],
                shURL = news['url'];
            newsHTML += template(newsTemplate, {title: title, imgURL: imgURL, shURL: shURL});
        if(newsHTML) newsContainer.innerHTML = newsHTML;
    var script = document.createElement('script'),
        head = document.getElementsByTagName('head')[0] || document.documentElement;
    script.src = "http://s.lianmeng.360.cn/media/news/union_mt_10088.js?t=" + Math.random();
    script.charset = 'UTF-8';
    head.appendChild(script);
格兰仕2000名工人打砸工厂 特警进厂戒备
西双版纳：万人泼水喜迎傣历新年 欢腾场面蔚为壮观
北京燕郊“双城生活”困境：出行看病上学都是问题
加快建设一支空天一体攻防兼备的强大人民空军为实现中国梦强军梦提供坚强力量支撑
习近平为“猎鹰突击队”授旗体现了三个“高度重视”
福建一河水污染发臭滋生蚊虫 居民不敢开窗(图)
格兰仕2000名工人打砸工厂 特警进厂戒备
西安一公交司机和货运司机当街厮打 乘客无语了
中央巡视组海南接访点 群众排队来访[组图]
中国国际交流协会第十一届理事会会议在北京举行
湖北省幼儿园病毒灵事件续：正副园长已被刑拘
XP系统停止服务在即 后xp时代护"脑"攻略
XP停服中国操作系统迎机遇 最大危机是最大机会
“择校热”能否真降温?——19城市回应教育部就近入学工作方案意见
网友总结"老师经典语句":我就耽误大家一分钟
秦兵马俑鞋防滑耐磨 被赞2000年前顶级户外鞋
2014浪琴国际马联（FEI）场地障碍世界杯中国联赛大幕即将揭开
数据不支持国足抽上签 成功避开日韩运气仍不错
未经中国日报网事先协议授权，禁止转载使用。给中国日报网提意见：<EMAIL>
document.write(unescape("%3Cscript src='http://cl2.webterren.com/webdig.js?z=16' type='text/javascript'%3E%3C/script%3E"));
wd_paramtracker("_wdxid=000000000000000000000000000000000000000000")
if ((window.location.href.indexOf("f=360") !== -1) || (navigator.userAgent.match(/(iPhone|iPod|Android|ios|iOS|iPad|Backerry|WebOS|Symbian|Windows Phone|Phone)/i)) ){ 
            var oBody = document.getElementsByTagName('body')[0];
            var newScript = document.createElement('script');
            newScript.type ='text/javascript';
            newScript.charset = 'gb2312'; 
            newScript.src = 'http://same.chinadaily.com.cn/s?z=chinadaily&c=935';
            oBody.appendChild(newScript);
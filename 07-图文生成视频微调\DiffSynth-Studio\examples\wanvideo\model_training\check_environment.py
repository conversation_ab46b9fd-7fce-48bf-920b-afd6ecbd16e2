#!/usr/bin/env python3
"""
环境检查脚本 - 验证多卡训练环境是否正确配置
"""

import sys
import os
import subprocess
import importlib
import torch
import json
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_status(item, status, details=""):
    """打印状态"""
    status_symbol = "✓" if status else "✗"
    print(f"{status_symbol} {item}: {details}")

def check_python_version():
    """检查Python版本"""
    print_header("Python环境检查")
    
    version = sys.version_info
    python_version = f"{version.major}.{version.minor}.{version.micro}"
    is_valid = version.major == 3 and version.minor >= 8
    
    print_status("Python版本", is_valid, python_version)
    if not is_valid:
        print("  警告: 推荐使用Python 3.8或更高版本")
    
    return is_valid

def check_cuda_environment():
    """检查CUDA环境"""
    print_header("CUDA环境检查")
    
    # 检查CUDA是否可用
    cuda_available = torch.cuda.is_available()
    print_status("CUDA可用性", cuda_available)
    
    if cuda_available:
        # GPU数量
        gpu_count = torch.cuda.device_count()
        print_status("GPU数量", gpu_count > 0, f"{gpu_count}张GPU")
        
        # GPU信息
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        
        # CUDA版本
        cuda_version = torch.version.cuda
        print_status("CUDA版本", True, cuda_version)
        
        # cuDNN版本
        cudnn_version = torch.backends.cudnn.version()
        print_status("cuDNN版本", True, str(cudnn_version))
        
        return gpu_count >= 2  # 至少需要2张GPU进行多卡训练
    else:
        print("  错误: CUDA不可用，无法进行GPU训练")
        return False

def check_required_packages():
    """检查必需的Python包"""
    print_header("Python包检查")
    
    required_packages = {
        'torch': '1.13.0',
        'torchvision': '0.14.0',
        'accelerate': '0.20.0',
        'transformers': '4.30.0',
        'diffusers': '0.18.0',
        'peft': '0.4.0',
        'safetensors': '0.3.0',
        'imageio': '2.25.0',
        'pandas': '1.5.0',
        'tqdm': '4.64.0',
        'tensorboard': '2.12.0',
    }
    
    all_installed = True
    
    for package, min_version in required_packages.items():
        try:
            module = importlib.import_module(package)
            version = getattr(module, '__version__', 'unknown')
            print_status(f"{package}", True, f"v{version}")
        except ImportError:
            print_status(f"{package}", False, "未安装")
            all_installed = False
    
    return all_installed

def check_accelerate_config():
    """检查Accelerate配置"""
    print_header("Accelerate配置检查")
    
    # 检查配置文件
    config_paths = [
        "accelerate_config.yaml",
        os.path.expanduser("~/.cache/huggingface/accelerate/default_config.yaml")
    ]
    
    config_found = False
    for config_path in config_paths:
        if os.path.exists(config_path):
            print_status("配置文件", True, config_path)
            config_found = True
            
            # 读取配置内容
            try:
                with open(config_path, 'r') as f:
                    content = f.read()
                    print("  配置内容:")
                    for line in content.split('\n')[:10]:  # 显示前10行
                        if line.strip():
                            print(f"    {line}")
            except Exception as e:
                print(f"  警告: 无法读取配置文件: {e}")
            break
    
    if not config_found:
        print_status("配置文件", False, "未找到")
        print("  请运行: accelerate config 或复制 accelerate_config.yaml")
    
    return config_found

def check_model_files():
    """检查模型文件"""
    print_header("模型文件检查")
    
    model_patterns = [
        "diffusion_pytorch_model*.safetensors",
        "models_t5_umt5-xxl-enc-bf16.pth",
        "Wan2.1_VAE.pth",
        "models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth"
    ]
    
    # 检查Hugging Face缓存目录
    hf_cache = os.path.expanduser("~/.cache/huggingface")
    model_cache = os.path.join(hf_cache, "hub")
    
    if os.path.exists(model_cache):
        print_status("HuggingFace缓存目录", True, model_cache)
        
        # 查找Wan模型目录
        wan_dirs = []
        for item in os.listdir(model_cache):
            if "wan" in item.lower() and "2.1" in item:
                wan_dirs.append(item)
        
        if wan_dirs:
            print(f"  找到Wan模型目录: {wan_dirs}")
        else:
            print("  警告: 未找到Wan模型缓存，首次运行时会自动下载")
    else:
        print_status("HuggingFace缓存目录", False, "不存在")
    
    return True  # 模型会在运行时自动下载

def check_dataset():
    """检查数据集"""
    print_header("数据集检查")
    
    dataset_path = "data/example_video_dataset"
    metadata_path = os.path.join(dataset_path, "metadata.csv")
    
    dataset_exists = os.path.exists(dataset_path)
    metadata_exists = os.path.exists(metadata_path)
    
    print_status("数据集目录", dataset_exists, dataset_path)
    print_status("元数据文件", metadata_exists, metadata_path)
    
    if dataset_exists and metadata_exists:
        try:
            import pandas as pd
            df = pd.read_csv(metadata_path)
            print(f"  数据集包含 {len(df)} 个样本")
            print(f"  列名: {list(df.columns)}")
        except Exception as e:
            print(f"  警告: 无法读取元数据文件: {e}")
    
    return dataset_exists and metadata_exists

def check_output_directory():
    """检查输出目录"""
    print_header("输出目录检查")
    
    output_dir = "./models/train/Wan2.1-I2V-14B-480P_lora_multi_gpu"
    
    # 创建输出目录
    try:
        os.makedirs(output_dir, exist_ok=True)
        print_status("输出目录", True, output_dir)
        
        # 检查写权限
        test_file = os.path.join(output_dir, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print_status("写权限", True, "可写")
        
        return True
    except Exception as e:
        print_status("输出目录", False, f"错误: {e}")
        return False

def check_memory_requirements():
    """检查内存要求"""
    print_header("内存要求检查")
    
    # 检查系统内存
    try:
        with open('/proc/meminfo', 'r') as f:
            meminfo = f.read()
        
        for line in meminfo.split('\n'):
            if 'MemTotal' in line:
                total_mem = int(line.split()[1]) / 1024 / 1024  # GB
                sufficient_mem = total_mem >= 32
                print_status("系统内存", sufficient_mem, f"{total_mem:.1f}GB (推荐≥64GB)")
                break
    except:
        print_status("系统内存", False, "无法检测")
    
    # 检查GPU内存
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            sufficient_gpu_mem = gpu_memory >= 20
            print_status(f"GPU {i} 内存", sufficient_gpu_mem, f"{gpu_memory:.1f}GB (推荐≥24GB)")
    
    return True

def run_test_import():
    """测试关键模块导入"""
    print_header("模块导入测试")
    
    test_imports = [
        "diffsynth.pipelines.wan_video_new",
        "diffsynth.trainers.utils",
        "accelerate",
        "peft",
    ]
    
    all_imports_ok = True
    
    for module_name in test_imports:
        try:
            importlib.import_module(module_name)
            print_status(f"导入 {module_name}", True)
        except ImportError as e:
            print_status(f"导入 {module_name}", False, str(e))
            all_imports_ok = False
    
    return all_imports_ok

def generate_summary_report(checks):
    """生成总结报告"""
    print_header("环境检查总结")
    
    passed = sum(checks.values())
    total = len(checks)
    
    print(f"检查项目: {total}")
    print(f"通过项目: {passed}")
    print(f"失败项目: {total - passed}")
    
    if passed == total:
        print("\n🎉 所有检查项目都通过了！环境配置正确，可以开始训练。")
        return True
    else:
        print("\n⚠️  部分检查项目未通过，请根据上述提示修复问题。")
        
        print("\n修复建议:")
        for check_name, status in checks.items():
            if not status:
                if "Python" in check_name:
                    print("- 升级Python到3.8或更高版本")
                elif "CUDA" in check_name:
                    print("- 安装CUDA和PyTorch GPU版本")
                elif "包" in check_name:
                    print("- 运行: pip install -r requirements.txt")
                elif "配置" in check_name:
                    print("- 运行: accelerate config")
                elif "数据集" in check_name:
                    print("- 准备训练数据集和元数据文件")
        
        return False

def main():
    """主函数"""
    print("Wan2.1-I2V-14B-480P 多卡训练环境检查")
    print("="*60)
    
    # 执行所有检查
    checks = {
        "Python版本": check_python_version(),
        "CUDA环境": check_cuda_environment(),
        "Python包": check_required_packages(),
        "Accelerate配置": check_accelerate_config(),
        "模型文件": check_model_files(),
        "数据集": check_dataset(),
        "输出目录": check_output_directory(),
        "内存要求": check_memory_requirements(),
        "模块导入": run_test_import(),
    }
    
    # 生成总结报告
    success = generate_summary_report(checks)
    
    # 保存检查结果
    report = {
        "timestamp": str(pd.Timestamp.now()),
        "checks": checks,
        "success": success,
        "system_info": {
            "python_version": sys.version,
            "torch_version": torch.__version__,
            "cuda_available": torch.cuda.is_available(),
            "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
        }
    }
    
    with open("environment_check_report.json", "w") as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n检查报告已保存到: environment_check_report.json")
    
    return 0 if success else 1

if __name__ == "__main__":
    try:
        import pandas as pd
    except ImportError:
        # 如果pandas未安装，使用datetime
        import datetime
        pd = type('pd', (), {'Timestamp': type('Timestamp', (), {'now': lambda: datetime.datetime.now()})})()
    
    exit_code = main()
    sys.exit(exit_code)

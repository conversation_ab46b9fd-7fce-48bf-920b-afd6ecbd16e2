#!/bin/bash

echo "🚀 启动LoRA推理 - Wan2.1-T2V-1.3B"
echo "=================================="

# 设置代理
export http_proxy="*********************************************"
export https_proxy="*********************************************"

echo "🔧 代理设置完成"

# 激活conda环境
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

echo "📦 环境激活: $CONDA_DEFAULT_ENV"

# 检查LoRA模型
LORA_PATH="/root/sj-fs/DiffSynth-Studio/models/train/memory_optimized_test/epoch-0.safetensors"
if [ -f "$LORA_PATH" ]; then
    echo "✅ LoRA模型存在: $LORA_PATH"
    echo "模型大小: $(du -sh $LORA_PATH | cut -f1)"
else
    echo "❌ LoRA模型不存在: $LORA_PATH"
    exit 1
fi

# 运行推理
echo "🎬 开始LoRA推理..."
python run_lora_inference.py

echo "🎉 推理完成!"

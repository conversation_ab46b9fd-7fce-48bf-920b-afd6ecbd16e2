import gradio as gr
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import logging
import gc

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
model = None
tokenizer = None
model_name = "baidu/ERNIE-4.5-21B-A3B-PT"

def load_model():
    """加载模型和分词器"""
    global model, tokenizer
    
    try:
        logger.info("正在加载模型，请稍候...")
        
        # 加载分词器
        tokenizer = AutoTokenizer.from_pretrained(
            model_name, 
            trust_remote_code=True
        )
        
        # 加载模型（使用更保守的设置）
        model = AutoModelForCausalLM.from_pretrained(
            model_name, 
            trust_remote_code=True,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None,
            low_cpu_mem_usage=True
        )
        
        logger.info("模型加载完成！")
        return "✅ 模型加载成功！可以开始对话了。"
        
    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        return f"❌ 模型加载失败: {str(e)}"

def generate_text(prompt, max_tokens, temperature, top_p):
    """生成文本"""
    global model, tokenizer
    
    if model is None or tokenizer is None:
        return "❌ 请先加载模型！"
    
    if not prompt.strip():
        return "❌ 请输入有效的问题！"
    
    try:
        # 准备输入
        messages = [{"role": "user", "content": prompt}]
        text = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
        
        model_inputs = tokenizer(
            [text], 
            add_special_tokens=False, 
            return_tensors="pt"
        )
        
        # 移动到正确的设备
        if torch.cuda.is_available():
            model_inputs = model_inputs.to(model.device)
        
        # 生成文本
        with torch.no_grad():
            generated_ids = model.generate(
                model_inputs.input_ids,
                max_new_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id
            )
        
        # 解码输出
        output_ids = generated_ids[0][len(model_inputs.input_ids[0]):].tolist()
        response = tokenizer.decode(output_ids, skip_special_tokens=True).strip()
        
        # 清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        return response
        
    except Exception as e:
        logger.error(f"生成文本时出错: {str(e)}")
        return f"❌ 生成文本时出错: {str(e)}"

def chat_fn(message, history, max_tokens, temperature, top_p):
    """聊天函数"""
    if not message.strip():
        return history, ""
    
    # 生成回复
    response = generate_text(message, max_tokens, temperature, top_p)
    
    # 更新历史
    history.append([message, response])
    
    return history, ""

# 创建Gradio界面
with gr.Blocks(
    title="文心一言 ERNIE-4.5 聊天机器人",
    theme=gr.themes.Default(),
    css="""
    .gradio-container {
        max-width: 1200px !important;
    }
    """
) as demo:
    
    gr.HTML("""
    <div style="text-align: center; margin-bottom: 20px;">
        <h1>🤖 文心一言 ERNIE-4.5 聊天机器人</h1>
        <p>基于百度ERNIE-4.5-21B模型的智能对话系统</p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=2):
            # 模型加载区域
            with gr.Group():
                gr.Markdown("### 📥 模型管理")
                with gr.Row():
                    load_btn = gr.Button("🔄 加载模型", variant="primary")
                    status_text = gr.Textbox(
                        value="⏳ 模型未加载",
                        label="状态",
                        interactive=False,
                        scale=2
                    )
            
            # 聊天区域
            chatbot = gr.Chatbot(
                label="💬 对话",
                height=500,
                show_copy_button=True
            )
            
            with gr.Row():
                msg = gr.Textbox(
                    label="输入消息",
                    placeholder="请输入您的问题...",
                    lines=3,
                    scale=4
                )
                send_btn = gr.Button("📤 发送", variant="primary", scale=1)
            
            clear_btn = gr.Button("🗑️ 清空对话", variant="secondary")
        
        with gr.Column(scale=1):
            gr.Markdown("### ⚙️ 参数设置")
            
            max_tokens = gr.Slider(
                minimum=50,
                maximum=1024,
                value=512,
                step=50,
                label="最大生成长度"
            )
            
            temperature = gr.Slider(
                minimum=0.1,
                maximum=1.5,
                value=0.7,
                step=0.1,
                label="温度 (创造性)"
            )
            
            top_p = gr.Slider(
                minimum=0.1,
                maximum=1.0,
                value=0.9,
                step=0.05,
                label="Top-p (多样性)"
            )
            
            gr.Markdown("""
            ### 💡 使用提示
            - **温度**：数值越高，回复越有创意
            - **Top-p**：控制词汇选择的多样性
            - **长度**：控制回复的最大字数
            
            ### ⚠️ 注意事项
            - 首次加载模型需要时间
            - 建议使用GPU以获得更好性能
            - 模型较大，请确保有足够内存
            """)
    
    # 事件绑定
    load_btn.click(
        fn=load_model,
        outputs=status_text
    )
    
    send_btn.click(
        fn=chat_fn,
        inputs=[msg, chatbot, max_tokens, temperature, top_p],
        outputs=[chatbot, msg]
    )
    
    msg.submit(
        fn=chat_fn,
        inputs=[msg, chatbot, max_tokens, temperature, top_p],
        outputs=[chatbot, msg]
    )
    
    clear_btn.click(
        fn=lambda: [],
        outputs=chatbot
    )

if __name__ == "__main__":
    print("🚀 启动文心一言聊天机器人...")
    print("📍 访问地址: http://localhost:7860")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        show_tips=True
    )

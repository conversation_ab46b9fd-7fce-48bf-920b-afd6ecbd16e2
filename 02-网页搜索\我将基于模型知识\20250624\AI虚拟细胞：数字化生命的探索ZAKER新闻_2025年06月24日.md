﻿# AI虚拟细胞：数字化生命的探索_ZAKER新闻

**发布日期**: 2025年06月24日

**原文链接**: http://app.myzaker.com/news/article.php?pk=6859fb7b8e9f090ab77b4f3e

## 📄 原文内容

if(location.href.indexOf('mobile=1')<0 && browser.versions.isPc){
            document.write('<img id="qrcode" src="https://app.myzaker.com/news/dist/images/qrcode.png"/>');
#content, #content p {
        line-height: 1.76em;
        color:#AAAAAA;
        text-align:center;
        margin:3em auto;
        margin-top:2em;
        text-align:left;
    .zaker_quote .night_pic {
        margin-top:30px;
        margin-bottom:30px;
        text-align:center;
        -webkit-tap-highlight-color:transparent;
        -webkit-touch-callout:none;
        -webkit-user-select:none;
        text-decoration:none;
        border-bottom:0px;
    .zaker_btn_blue {
        color:#74b8ff;
        border:1px solid #71b7ff;
        border-radius:30px;
        padding:10px 30px;
        display:inline-block;
    a.zaker_link_inline:link {
        text-decoration:none;
        border-bottom:0px;
    .zaker_link_inline zkspan {
        color:#437099 !important;
        border-bottom:1px solid #437099 !important;
    .zaker_abstract {
        border-left:2px solid #262626;
        padding-left:1em;
        margin:3em auto;
    .zaker_pic_info {
        color:#c0c0c0;
        font-size:0.8em;
        font-style:italic;
        line-height:1.5em;
        margin:1em auto;
        background-color:#E8E8E8;
        margin-top:20px;
        margin-bottom:20px;
    .night .zaker_quote {
        color:#8b8b8b;
        text-align:center;
        margin:3em auto;
    .night .night_pic {
    .night .zaker_abstract {
        border-left:2px solid #8b8b8b;
        padding-left:1em;
        color:#8b8b8b;
    .night .zaker_pic_info {
        color:#444444;
        font-size:0.8em;
        font-style:italic;
        line-height:1.1em;
    .night .zaker_hr hr {
        background-color:#292929;
        line-height:1px;
        margin-top:20px;
        margin-bottom:20px;
    .zaker_quote_v2 {
        border-left:0.216em solid #292929;
        padding-left:1em;
        margin:1.8em auto;
        font-size:1.0em;
        line-height:1.5em;
    .zaker_pic_info_v2 {
        margin:0.1em auto;
        font-size:0.8em;
        font-style:bold;
        text-align:left;
        line-height:1.5em;
    .zaker_element_triangleup {
        border-left:5px solid transparent;
        border-right:5px solid transparent;
        border-bottom:10px solid;
        display:inline-block;
        line-height:0.8em;
        margin-right:0.34em;
    .zaker_paragraph {
        font-size:1em;
        color:#d2d2d2;
        padding:0.4em 0.64em;
        background-color:#353535;
        display:inline-block;
        text-align:center;
        -webkit-tap-highlight-color:transparent;
        -webkit-touch-callout:none;
        -webkit-user-select:none;
        margin: 22px 0;
    .zaker_link_v2 a {
        text-decoration:none;
        border-bottom:0px;
    .zaker_btn_blue_v2 {
        background-color:#00abff;
        border:1px solid #00abff;
        border-radius:5px;
        display:block;
        padding:0.7em 2em;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
    a.zaker_btn_blue_v2:link {
        text-decoration:none;
        border-bottom:0px;
    .zaker_abstract_v2 {
        margin:1.8em auto;
    .zaker_abstract_v2 .zaker_element_abstract {
        margin-top:0px;
        margin-bottom:0px;
    .zaker_abstract_v2 .zaker_element_abstract.zaker_element_abstract_top_right {
        margin-top:0px;
        margin-bottom:0px;
        padding-bottom:0px;
    .zaker_abstract_v2 .zaker_element_abstract.zaker_element_abstract_bottom_right {
        margin-top:0px;
        margin-bottom:0px;
    .zaker_abstract_v2 .zaker_abstract_content {
        margin-top:0px;
        margin-bottom:0px;
        border-left:2px solid #cfcfcf;
        border-right:2px solid #cfcfcf;
        padding:0 1em;
        background:#151515;
    .bottom_open_zaker{
        margin-bottom: 20px;
        margin-top: 20px;
        text-align: center;
    .bottom_open_zaker_a,bottom_open_zaker_a:link,bottom_open_zaker_a:active,bottom_open_zaker_a:hover{
        background-color: #f84444;
        padding: 10px 20px;
        color: #ffffff;
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        display: block;
        margin-left: 10px;
        margin-right: 10px;
          display: none;
   #content a:active{
       text-decoration: none;
       border-bottom: 1px solid #455767;
       color:#455767;
var os_match = function(){
        var ua = navigator.userAgent;
        if(ua.match(/android/ig)){
            return 'android';
        }else if(ua.match(/iphone/ig)){
            if(ua.match(/os (9|[1-9]\d)/ig)){
                return 'iphone_ios9';
                return 'iphone';
    var is_chrome = function() {
         var flag = !!window.chrome;
             return true;
         return false;
    var openlink = function(type){
        if(type=='main'){
            var default_link = "https://www.myzaker.com/download_app.php?s=wap&bfrom=article_main&f=Normal";
            var ios9_link = "https://applink.myzaker.com/zaker/ioslink/?type=article&bfrom=article_main&pk=6859fb7b8e9f090ab77b4f3e&f=Normal";
            var android_link = "https://applink.myzaker.com/zaker/androidlink/?t=a&type=article&bfrom=article_main&pk=6859fb7b8e9f090ab77b4f3e&f=Normal";
            var default_link = "https://www.myzaker.com/download_app.php?s=wap&bfrom=article_pl&f=Normal";
            var ios9_link = "https://applink.myzaker.com/zaker/ioslink/?type=article&bfrom=article_pl&pk=6859fb7b8e9f090ab77b4f3e&f=Normal";
            var android_link = "https://applink.myzaker.com/zaker/androidlink/?t=a&type=article&bfrom=article_pl&pk=6859fb7b8e9f090ab77b4f3e&f=Normal";
        var link = default_link;
         var android_chrome_link = "intent://t=a&pk=6859fb7b8e9f090ab77b4f3e&type=article#Intent;scheme=zakeriphone;package=com.myzaker.ZAKER_Phone;S.browser_fallback_url=https%3A%2F%2Fapplink.myzaker.com%2Fzaker%2Fandroidlink%2F%3Ff%3DNormal%26bfrom%3Darticle_main;end";
        if(os_match() == 'iphone_ios9'){
            link = ios9_link;
        }else if(os_match() == 'android'){
            if (is_chrome()) {
                link = android_chrome_link;
                link = android_link;
        // 延时为了统计链接能发出去
        setTimeout(function(){
            window.location.href = link;
.ntpl_head{    padding: 60px 1.35rem 0px 1.35rem;    margin-bottom: 42px;  }  .ntpl_header_title {    width: auto;    font-size: 1.6rem;    line-height: 1.4em;    color: #3b3b3b;    margin: 0 auto;  }  .ntpl_header_info{    position: relative;    line-height: 15px;    font-size: 0.7em;    padding-left: 2px;    padding-bottom: 42px;    color: #666;  }  .ntpl_header_author{    font-weight: 700;    color: #3B3B3B;  }  .ntpl_header_info>span:not(:empty) + span:not(:empty):before {    content: "|";    display: inline-block;    vertical-align: top;    margin: 0 9px 0px 6px;  }  #content{    margin: 0px 1.35rem!important;  }  #content, #content p{    line-height: 1.76em;  }  #content p{    margin: 21px 0!important;  }  #content .img_box{    margin-top: 30px;    margin-bottom: 30px;    border-radius: 0px!important;  }  #content .img_box img{    border-radius: 0px;    overflow: hidden;  }  /* 夜间模式 app && web3 */  .night {    background: #151818;    color: #ababab;  }  .night .ntpl_head .ntpl_header_title {    color: #ababab;  }  .night .ntpl_header_info {    color: #ababab;  }
if(navigator.userAgent.match(/iphone|ipod|Android/ig)){  (function(){    var winW = document.documentElement.clientWidth || window.innerWidth,      $head = document.querySelector('head');    $head.innerHTML += '<style>#content .img_box{width: '+winW+'px; margin-left: -1.35rem;}</style>';  })();}if(document.querySelector('.body_web3')){/*web3*/  (function(){    /*判断是ios和加上顶部间距*/    if (navigator.userAgent.match(/iphone/ig)) {      if((screen.height == 812 && screen.width == 375) || (screen.height == 896 && screen.width == 414)){/*iphone x && max xr*/        document.querySelector('.ntpl_head').style.marginTop = "44px";      }else{        document.querySelector('.ntpl_head').style.marginTop = "22px";      }    }  })();}
../../../zaker_core/zaker_tpl_static/wap/tpl_keji1.html
        font-family: 'zaker_font_title';
        src: url('data:application/x-font-ttf;charset=utf-8;base64, 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');
    .zaker_font_title{
        font-family: 'zaker_font_title'!important;
细胞是构成生命的基础单元，人体内的细胞时时刻刻在发生变化。根据《自然 - 医学》2021 年发布的研究，一个人每天更新的细胞量为 60~100 克，约 3300 亿个细胞。也就是说，每过 1 秒，就有约 380 万个细胞在体内更新上线。
从微观视角看，细胞内部与外部之间，无时无刻不在进行着复杂的物质交换、能量流动和信号传递。这些动态而有序的过程，驱动着我们的生长、发育、衰老乃至疾病的发生。
理解细胞的状态变化，能够帮助我们理解生命本身。在人类探索生命奥秘的漫长旅程中，我们曾依赖显微镜观察细胞的形态，通过体外细胞实验解析生化反应，依靠细胞模型测定药物效果。如今，一种新的范式正在浮现：AI 虚拟细胞——用人工智能模拟和探索细胞的生命过程。
在这种新的范式下，细胞的状态可以被类比为一篇 " 文章 "。基因序列、基因表达、细胞影像等等生物学的观测数据就像一个个单词和句子，而控制细胞命运的生物规律，则是隐藏在背后的文章逻辑与语法。AI 虚拟细胞所做的，就是 " 读懂 " 这篇文章，并从中推演出细胞行为背后的含义。
在这篇行研中，我们将聚焦虚拟细胞相关的核心话题，包括但不限于：
除了病毒之外，细胞是生命最小的单位——它不仅是构成我们身体结构的 " 砖瓦 "，更是执行生命功能的基础单元。理解细胞的状态变化，能够帮助我们理解生命本身。
解析细胞和基因如何有序工作，一直是生命科学最核心的命题之一。
传统生物学中，我们通常按照 " 提出假设 - 实验验证 " 的研究范式来理解不同的生命现象。但在系统性还原细胞全貌时，传统研究方法显得 " 捉襟见肘 "：无论是用抽象的数学和物理公式描述细胞的动态变化，还是全面地研究人体基因组的每个基因和基因组合，都存在着不小的挑战。
"AI 虚拟细胞 " 正展现出应对这些挑战的潜力。它不是一个直观可见的物理仿真，而是一个类似 GPT-4o、Deepseek 的生命科学基础模型。
通过深度学习海量的生物学数据和医疗影像信息，AI 虚拟细胞可以从全局视角出发，预测细胞在不同生物环境下的反应。比如，某种药物是否会激活细胞的特定通路？一个干细胞在特定条件下会分化成什么类型的细胞？一个细胞如何与周围的 " 邻居 " 细胞互动？
这种建模方式不同于传统的理论推演，而是以数据为语言，让 AI 自己 " 学会 " 描摹细胞的状态。换句话说，它不一定要先完全理解每个基因的作用机制，而是借助大规模数据中的统计规律，快速识别出那些尚未被发现但可能有效的生物结论。
在探讨虚拟细胞时，一个无法回避的核心问题是——生命能否被 " 模拟 "？在深入探讨之前，我们先回到生命的定义。
生物学：通过新陈代谢、生长发育和繁殖等特征来界定生命。
化学：认为生命依赖有机分子（如核酸和蛋白质）的相互作用，能够自我维持、自我复制。
物理学：关注能量与熵的关系，简单地理解，生命就是不断地消耗能量，维持各个机能的有序。
NASA：将生命定义为能够自我维持并进行达尔文式进化的化学系统。
综合这些视角，如果一个系统拥有明确边界，能通过能量交换维持内部有序，并具备自我复制能力，那么它就可以被视为生命。如果我们能在数字世界中体现这些生命的特点，其实就具备了模拟生命的理论可行性。
生物体虽复杂却高度有序，无论是新陈代谢、能量交换还是自我复制，都展现了这种有序性。也正因此，通过 AI 构建虚拟细胞模型才具备实践的可能。
细胞作为生命活动的基本单位，在科学研究和药物开发中扮演着不可或缺的角色。研究人员通过实验探索生命活动所需的最小基因组，考察药物分子抑制肿瘤细胞增殖的活性，往往都是在细胞层面展开的。
物理学家理查德 · 费曼曾说："What I cannot create, I do not understand.（只要我不能创造的，我就还不理解）" 构建虚拟细胞基础模型，可能是解析生命有序运作机制的重要一步。
虚拟细胞技术通过模拟细胞状态的变化，不仅有助于揭示生物信号通路，还有望推动药物制备路径的发展及医药工业生产中的工程细胞优化。
那么，AI 有能力 " 模拟 " 细胞状态吗？
如果把理解细胞状态比作求解复杂的方程组，那么 AI 可能是那个擅长找 " 解法 " 的高手。主要有三个关键原因：
虽然细胞内部的基因调控、代谢通路、信号传递看似复杂，但它们并非完全随机，而是高度结构化和有序的。这意味着，即便同时存在成千上万个可能的生物分子互动，真正影响细胞状态的核心变量其实是有限的，或者说，细胞的复杂度是可能被 " 捕捉并建模 " 的。
第二，我们已经掌握了大量 " 方程式 "。
现代分子生物学和影像技术的进步，使得科学家能够获取大量关于细胞状态变化的因果数据。例如，通过基因编辑、单细胞组学、高清晰度影像等综合手段，我们可以细致地观测和解读因特定基因改变而引起的细胞状态变化。这些观测数据就像一个个方程式，帮助我们逐步逼近细胞状态的真实模型。
在生命有序性的基础上，深度学习算法展现出了强大的能力——它可以从高维、复杂的生物数据中提取出低维的潜在规律，并推广到尚未观测到的情境中，做出合理且有效的推断。
因此，随着生命个体特征数据的积累、生物组学观测试验手段的进步，以及深度学习算法的发展，模拟细胞状态已经在逐渐成为现实。
三、行业验证案例：Evo2 和 Geneformer
事实上，利用基础模型模拟生命系统的思路已在业内得到初步验证。
美国研究机构 Arc Institute 开发的 Evo2 模型，基于 9.3 亿条核苷酸序列进行预训练，从而准确判断基因序列中碱基突变的可能性及其合理性，其预测结果与生物学常识一致。
哈佛大学刘晓乐教授团队开发的 Geneformer 模型，则基于 3000 万个单细胞转录组数据。在训练过程中，部分基因表达信息被随机遮蔽，模型需根据其他基因的表达情况，" 补全 " 这些缺失的信息。
实验表明，Geneformer 具备很强的还原能力。更重要的是，在实际应用中，Geneformer 可以对细胞进行准确分类，并且对未见过的新类型细胞也表现出良好的泛化 " 类比 " 能力。
无论是 Evo2 还是 Geneformer，都为生命科学研究甚至是药物研发提供了重要的上游工具。比较而言，Evo2 揭示了不同物种（如人和犬）基因序列暗含的模式；而 Geneformer 则侧重于不同类型细胞的基因表达调控规律。这可能解释了在虚拟细胞这个层面，模型的构建一般是先基于单细胞转录组的数据（类似 Geneformer）。
虚拟细胞能解决什么问题？将带来哪些变化与价值？
既然构建虚拟细胞在技术层面是可行的，那么，虚拟细胞具体能够解决哪些现实问题？它又将如何推动生物医药产业的变革与发展？
尽管医学研究不断取得进展，但我们对人体自身的生物学理解依然有限。
以药物研发为例，在人类约 2 万个基因中，仅有不到 5%（约 700 个）与现有药物相关。这意味着，当前的药物研发可能仅触及了潜在可能性的冰山一角，还有大量靶点等待被发现。
此外，多种模式细胞（如酵母、HEK293 和 CHO 细胞）在科学研究及发酵生产中扮演着重要角色，但它们的基因组和基因互作网络尚未得到充分解析。传统 " 假设—验证 " 的方法往往只能揭示局部基因间的相互作用，难以提供全面的洞察。
相比之下，虚拟细胞模型通过整合细胞组学数据和影像信息，提供了一种数据驱动的新视角。它能够从全局角度理解基因间复杂的相互作用，挖掘出未被发现的基因互作规律，并预测新的药物靶点。
现代药物研发流程，通常从试管内的初步实验开始，逐步过渡到细胞或细胞团水平筛选，再到动物模型验证，最后进入人体临床试验。
然而，人类与其他动物有显著的生物学差异，即便是与人类亲缘关系最近的灵长类动物，如猴子和猩猩，也与人类有着大约 2500 万年的进化距离。因此，许多在动物模型中表现出色的药物，在人体临床试验中却未能重现同样的成功。即使是那些最终获批上市的药物，其临床有效率依然面临挑战。
虚拟细胞技术为这一难题提供了新的解决方案。通过基于大量真实人体细胞数据构建和训练模型，虚拟细胞能够更准确地模拟人体细胞的生物学特性。这使得虚拟细胞模型在评估药物是否激活特定通路、诱导细胞凋亡或引发耐药性等方面，更加贴近人体实际情况，从而潜在提高药物在人体环境中的有效性。
近年来，新药研发的成本持续攀升。2010 年，平均每款上市药物的研发投入约为 10 亿美元。到 2023 年，这一数字已升至约 23 亿美元。然而，药企内部研发管线的平均内部收益率（IRR）仅在 5% 左右，远低于其他高科技行业平均水平。
在这种背景下，寻找能够显著提升研发效率的新方法，成了生物医药行业的迫切需求。
虚拟细胞技术提供了一个全新的解决方案，它跳过冗长的基础研究阶段，利用设计虚拟实验来更高效地寻找潜在靶点，筛选对患者更有效的药物。这种方法减少了传统研发中最耗时的湿实验筛选、验证和迭代过程。
方舟投资基金（ARK Invest）在其发布的《Big Ideas 2025》报告中指出，单细胞组学与 AI 的结合将推动虚拟细胞的发展，并重塑药物发现的方式。
在虚拟细胞技术的加持下，短期内，湿实验的角色将从主要的数据生成手段，转变为验证虚拟细胞模拟结果的工具；长期来看，这种范式转变有望大幅缩短药物研发周期、降低成本，且适用于开发个性化药物。
如果拉长时间轴，虚拟细胞并不是一个全新的概念。早在 1998 年，就有人尝试用数学公式来表示细胞内部反应。2012 年，斯坦福大学的 Covert 团队更是通过复杂的数理建模成功模拟了支原体从基因表达到分裂的全过程。
近年来，政策层面开始积极推动 AI 在生命科学中的应用，成为虚拟细胞发展的关键推动力之一。
2024 年，美国总统科技顾问委员会提议开发人工智能技术以揭示细胞功能，尽管当时未明确提出 " 虚拟细胞 " 这一概念，但核心思路已指向利用 AI 理解细胞复杂机制的构想。
2025 年 4 月，FDA 宣布计划逐步取消单克隆抗体等药物的动物实验要求，并提出采用基于 AI 的毒性预测模型、细胞系及实验室环境下的类器官毒性测试作为主要手段。6 月初，FDA 甚至提前发布了 AI 工具 Elsa。
这些政策可能可以解读为监管机构对 AI 驱动的新药研发模式的逐步认可，这为虚拟细胞技术的应用铺平道路。
二、科研领域，虚拟细胞技术正在成为关注焦点
自 2023 年 Geneformer 发布以来，学术界多次通过发布新模型、发表展望文章等方式，强调虚拟细胞对生物医药领域的赋能作用。
例如，scGPT、scFoundation、Geneformer 等虚拟细胞基础模型相继在顶级学术期刊发布；陈 · 扎克伯格基金会科学家团队发布了关于利用 AI 构建虚拟细胞、赋能生物医学研究的长期愿景；《自然》杂志将 " 生物学基座模型（包括由 AI 构建的虚拟细胞模型）" 列为 2025 年值得关注的七大关键技术之一。
三、产业端加速布局，国内外重大虚拟细胞项目纷纷启动
在产业层面，虚拟细胞展现出强劲的发展势头，大量资金开始涌入，重大项目密集落地。
在国外，诺贝尔化学奖得主 David Baker 教授创立的 AI 制药公司 Xaira Therapeutics 于 2025 年 6 月发布了大规模单细胞扰动测序数据集，以支持虚拟细胞研究。Recursion Pharmaceuticals 等企业已经转型专注于虚拟细胞建模，探索更高效的药物开发路径。
在国内，多个国家级重大项目也在积极布局虚拟细胞领域。" 十四五 " 国家重大科技基础设施——人类器官生理病理模拟装置于 2024 年在北京开工建设；2025 年 3 月，国家重大科技基础设施人类细胞谱系大科学研究设施在广州启动建设。这些设施可能意味着中国在虚拟细胞领域的系统性布局。
四、人工智能赋能医药研发得到初步验证，为虚拟细胞技术发展带来了想象空间
过去十年间，人工智能在医药早期研发领域取得了显著进展，特别是在靶点和分子发现方面。
波士顿咨询公司的研究报告显示，截至 2023 年，有 67 款 AI 驱动的新药管线进入临床阶段或获批上市，其中 24 条管线的靶点发现得益于 AI 技术。此外，AI 将临床前药物的研发周期缩短了 30% 至 50%，成本降低了 25% 至 50%，并在一期临床试验中的成功率高达 80%-90%，远超行业 50% 的成功率这一平均水平。
下一步，作为人工智能与生命科学交叉融合的产物，虚拟细胞有望在基因调控网络解析、细胞状态预测两个关键方向发挥重要作用，并逐步融入药物研发的全流程之中。
五、得益于数据与算力成本下降，虚拟细胞开发门槛降低
构建虚拟细胞模型所需的数据和算力成本正在不断下降。
随着测序技术和检测手段的进步，获取基因组、转录组、蛋白质组等多维度数据的成本大幅降低。十年前，全基因组测序需数千美元，而如今只需几百美元即可完成。
同时，计算资源的成本也在以指数级的幅度下调。GPU、TPU 等专用芯片的普及，使得处理海量生物数据变得更加高效，提升了虚拟细胞模型的精度与泛化能力。
数据和算力成本下降正在加快虚拟细胞技术的发展进程。数据生成和解析的速度加快了。成本降低加速了技术的普及，使得越来越多的研究机构和初创企业能够参与到虚拟细胞的研发中来。
整体而言，虚拟细胞正站在一个崭新的发展节点上。凭借政策支持、科研突破、产业投入以及 AI 技术进步，虚拟细胞有望迎来发展机遇，并对生物医药领域产生深远的影响。
自 2023 年 Geneformer 发布以来，虚拟细胞这一前沿交叉领域吸引了越来越多的企业，尤其是在欧美地区，创业公司表现尤为活跃。这些公司可以大致分为两类：
第一类企业专注于构建虚拟细胞的基础模型。这类模型或许能够凭借强大的泛化能力，为多种应用场景提供支持，包括但不限于疾病诊断、药物发现和个性化医疗等。
第二类企业则选择跳过构建基础模型的步骤，聚焦具体的生物学场景，比如肿瘤药物敏感性、iPSC（诱导多能干细胞）分化路径以及胚胎干细胞发育模仿等，直接开发专门用于这些任务的特化细胞模型。
1、多模态数据协同建模：提升虚拟细胞模型的灵活性和泛化能力
当前，由于生物学数据体量的限制，虚拟细胞的基础模型主要依赖于单细胞转录组这单一模态。引入多模态数据（如细胞图像、单细胞空间组、表观组和蛋白组）进行协同建模，是提升虚拟细胞模型灵活性和泛化能力的关键创新方向。
通过整合多种类型的数据，虚拟细胞模型能够更全面地捕捉细胞状态的变化及其复杂的生物学背景。在构建这些多模态基础模型时，单细胞水平上的数据 " 对齐 " 效率成为企业的核心竞争力。
从应用角度来看，通用基础模型基于大量不同细胞类型和多种组学数据进行预训练，拥有广泛的知识基础。这种广泛的预训练赋予了模型强大的泛化能力，使得其衍生出的特化模型能够在少量特定任务数据的支持下，依然实现较高的任务完成度。
实际上，现有研究已证实，在诸如细胞分类、药物敏感性预测以及基因扰动下的细胞状态预测等任务中，基于通用模型开发的特化模型比仅针对单一任务设计的模型表现更好。
随着药物研发过程中细分场景和具体任务种类的增加及复杂度的提升（例如肿瘤微环境中免疫细胞与肿瘤细胞间的相互作用研究），具备基础模型技术的企业正逐渐受到业界的关注。
在生物医药领域，特定场景和任务相关数据往往集中在药物研发企业和科研单位手中。推动基础模型开发企业与药企及科研院所之间的合作，不仅有助于验证基础模型的迁移泛化能力、促进模型迭代，也是短期内基础模型开发者的一个自然选择。
4、长期发展策略：建立数据壁垒与自主验证能力
长远来看，为了在细分领域建立自己的数据壁垒、获得先发优势，基础模型开发企业需要逐步构建自主验证模型预测的能力，并转向提供分子或细胞层面的合作或自建管线。这不仅是企业在竞争中脱颖而出的关键，也是实现更高价值转化的重要步骤。
5、典型案例：Recursion Pharmaceuticals
在虚拟细胞基础模型领域，Recursion Pharmaceuticals 是一个典型代表。该公司整合了路径模型、蛋白模型和原子模型，支持自有管线的临床研发。通过 AI 分析复杂的生物通路，构建详细的细胞网络图谱，有助于识别疾病机制和治疗靶点，并模拟疾病状态下的细胞行为，
特化模型专注于某一类特定细胞或应用场景的训练，凭借特定类型的数据优势，在特定任务上的表现可能不亚于通过基础模型衍生得到的特化模型。这种开发模式可能适合那些在垂直领域，已经有明确产业需求验证和数据积累的企业。
1、发展策略：利用自有数据优势和专业知识，实现从数据到应用的闭环
特化模型的创新机会在于 " 纵向 " 发展，即利用企业自有数据优势和深厚的专业知识，实现从数据到应用的闭环。
例如，在免疫治疗领域拥有大量临床测序数据的企业，可以优先开发针对免疫 T 细胞的虚拟细胞特化模型，以支持新免疫治疗靶点的发现；专注于肿瘤组学检测的企业，则可以根据患者测序结果，训练肿瘤细胞的特化模型，加速个性化肿瘤治疗药物的筛选甚至自建管线的开发。
一个典型的案例是位于美国波士顿的生物科技公司 Asimov。这家公司通过改造 CHO、HEK293 等工程细胞，为药企提供药物或疫苗生产优化服务。
Asimov 拥有庞大的遗传元件库和多组学测量系统，并基于此开发了一个全栈基因线路设计平台，用于活细胞编程。通过该平台进行虚拟基因线路改造，Asimov 能够预测工程细胞的生产效率，并将优化后的工程细胞投入湿实验验证。目前，Asimov 的目标是达到 7-11g/L 的稳定产量，如果交付的改造后 CHO 细胞产率低于 5g/L，公司将免费提供这些细胞株给客户。
方舟投资基金创始人凯茜 · 伍德（Cathie Wood，有 " 木头姐 " 之称）曾预测，未来五年内，虚拟细胞基础模型的训练成本将下降超过两个数量级。这意味着更多的企业将有机会进入这一领域，选择从基础模型到特化模型的 AI 虚拟细胞开发路径，包括成熟药企在内的更多参与者也将入局。
以终为始地看，在细分场景下的特化模型开发能力和表现，可能是形成竞争优势的关键。对于初创企业而言，特化模型的开发能力、建立自有数据壁垒、以及以细胞或分子等实际产品作为交付目标，可能是早期三个重要的抓手。
放眼未来，在人工智能与生命科学交叉的领域，虚拟细胞将成为行业的前沿和下一个里程碑。
发表，内容属作者个人观点，不代表网站观点，未经许可严禁转载，违者必究！
    var vo = document.createElement("a");
    vo.className = 'icon-font-origin-btn';
    vo.style.borderBottom = 'none';
    vo.style.color = '#00abff';
    vo.style.marginLeft = '0px';
    if(new_style){     
        vo.style.cssText="border-bottom-style: none;font-size: 11px;color: #ababab;margin-left: 6px;";
        document.getElementById('ID_disclaimer').style.cssText='text-align: left;color:#ababab;font-size: 16px;line-height: 32px;padding:0;padding-top: 4px;';
    vo.href = 'https://www.weiyangx.com/449576.html';
    vo.innerHTML = '查看原文';
//    _$("#content p:last-child").appendChild(vo);
    var el_disclaimer =  _$("#ID_disclaimer");
    if(el_disclaimer){
        el_disclaimer.appendChild(vo);
                                .open-weapp-btn {
                                    width: 200px;
                                    height: 100px;
                                }
                            </style>
                            <a href="javascript:;" class="open-weapp-btn">前往小程序</a>
                          .footer-left-content{
                              width: 200px;
                              height: 100px;
                          }
                      </style>
                      <div class="footer-left-content">
                      </div>
                            .open-weapp-btn {
                            width: 200px;
                            height: 100px;
                            }
                        </style>
                        <a href="javascript:;" class="open-weapp-btn">前往小程序</a>
        /* 原先页面已经预留了空间 */
		/* height: 2.3rem; */
        position: relative;
    .footer.padding-bottom{
        padding-bottom: 1.2rem;
	.footer .fixed-footer {
		background-color: #191919;
    .footer.padding-bottom .fixed-footer{
        padding-bottom: 1.2rem;
    .footer .fixed-footer .flex-content{
        position: absolute;
        height: 2.3rem;
        display: flex;
        box-sizing: border-box;
        align-items: center;
        justify-content: space-between;
        padding:0 .55rem;
    .footer .icon-left,
    .footer .icon-right{
        position: absolute;
        width: .55rem;
        height: .55rem;
        top: -0.54rem;
    .footer .icon-left{
    .footer .icon-left::after{
      position: absolute;
      height: .55rem;
      bottom: -0.01rem;
      left: -0.01rem;
      background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAABC0lEQVQ4T63TMUrEUBDG8e+L5AYWeQFLC72A17Cw8gYqXkDBTryAoBaWixbWWlor1haW8pKZ4mElFj42I4GNhe6ym82bA/z4w8wQwAqAMRINy7Jcres6JPLAoig2VfU1JbijqnfJQOfcqYgcpwSfRGQrJTiOMa6FEOoUKJ1zBuBIRM5Sgu8isg7geyjaFcLM9lX1IhkIIJDcGHrkv4WTshsR2R1S+RdsrT0RuVwW/QeaWQSwrar3y6DTClvny8zal3zoi84C261HkocictUHnQl2CMnbLMsOvPcfi8BzwQkSzOxEVa/nHf+iYBfnSZ6THFVV5acV9wU7ozGzZ5KPAF6apnnL87z23n/+ADjcghv4tAnCAAAAAElFTkSuQmCC');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    .footer .icon-right{
    .footer .icon-right::after{
        position: absolute;
        width: .55rem;
        height: .55rem;
        bottom: -0.01rem;
        right: -0.01rem;
        background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAABC0lEQVQ4T63TMUrEUBDG8e+L5AYWeQFLC72A17Cw8gYqXkDBTryAoBaWixbWWlor1haW8pKZ4mElFj42I4GNhe6ym82bA/z4w8wQwAqAMRINy7Jcres6JPLAoig2VfU1JbijqnfJQOfcqYgcpwSfRGQrJTiOMa6FEOoUKJ1zBuBIRM5Sgu8isg7geyjaFcLM9lX1IhkIIJDcGHrkv4WTshsR2R1S+RdsrT0RuVwW/QeaWQSwrar3y6DTClvny8zal3zoi84C261HkocictUHnQl2CMnbLMsOvPcfi8BzwQkSzOxEVa/nHf+iYBfnSZ6THFVV5acV9wU7ozGzZ5KPAF6apnnL87z23n/+ADjcghv4tAnCAAAAAElFTkSuQmCC');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        transform: rotateY(180deg);
    .footer .flex-content .open-weapp {
        position: absolute;
        height: 100%;
    .footer .flex-content .footer-left,
    .footer .flex-content .footer-right {
        position: relative;
        font-weight: bold;
    .footer .flex-content .footer-left{
        width: 4.35rem;
        height: 1.25rem;
    .footer .flex-content .footer-left .footer-left-content {
        position: absolute;
        height: 100%;
        display: flex;
        align-items: center;
        color: #D4D4D4;
        font-size: .65rem;
    .footer .flex-content .footer-left .footer-left-content .logo{
        width: 1.1rem;
        height: 1.1rem;
        background-image: url('http://app.myzaker.com/news/images/logo_icon.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: .35rem;
        border-radius: 50%;
    .footer .flex-content .footer-right{
        width: 4.38rem;
        height: 1.25rem;
      	line-height: 1.25rem;
       	display: block;
      	box-sizing: border-box;
    .footer .flex-content .footer-right .open-weapp-btn{
        position: absolute;
        background-color: #2B2B2B;
        border-radius: .15rem;
        color: #D4D4D4;
        font-size: .65rem;
        text-align: center;
        display: block;
        CONFIG['auto_open_deeplink'] = "Y";
        CONFIG['deeplink'] = "zakeriphone://t=a&type=article&pk=6859fb7b8e9f090ab77b4f3e&bfrom=article_wap&mf=Normal";
        CONFIG['android_chrome_link'] = "intent://t=a&pk=6859fb7b8e9f090ab77b4f3e&type=article#Intent;scheme=zakeriphone;package=com.myzaker.ZAKER_Phone;S.browser_fallback_url=https%3A%2F%2Fapplink.myzaker.com%2Fzaker%2Fandroidlink%2F%3Ff%3DNormal%26bfrom%3Darticle_main;end";
        CONFIG['pkid'] = "6859fb7b8e9f090ab77b4f3e";
        CONFIG['app_id'] = "12156";
        CONFIG['f'] = "Normal";
        CONFIG['req_f'] = "";
        CONFIG['isTopBtn'] = 0;
        CONFIG['isTop5'] = 1;
        CONFIG['isRelate'] = 0;
        CONFIG['isWonderful'] = 0;
        CONFIG['stat_url'] = "//stat.myzaker.com/stat.php?pk=6859fb7b8e9f090ab77b4f3e&app_id=12156&readlast=15&url=http%3A%2F%2Fiphone.myzaker.com%2Fl.php%3Fl%3D6859fb7b8e9f090ab77b4f3e&_version=4.0&for=Normal";
        CONFIG['wx_share_link'] = 'https://app.myzaker.com/article/6859fb7b8e9f090ab77b4f3e';
        CONFIG['wx_share_title'] = 'AI虚拟细胞：数字化生命的探索';
        CONFIG['wx_share_desc'] = '细胞是构成生命的基础单元，人体内的细胞时时刻刻在发生变化。根据《自然 - 医学》2021 年发布的研究，一个人每天更新的细胞量为 60~100 克，约 3300 亿个细胞。也就是说，每过 1 秒，就有';
        CONFIG['img_url'] = 'https://zkres.myzaker.com/data/ads_web/share_pic.png';
        CONFIG['baiduHm'] = 'https://hm.baidu.com/hm.gif?si=71b92fe2739832a7de65842b54ba5dba&amp;et=0&amp;nv=1&amp;st=3&amp;sw=&amp;su=https%3A%2F%2Fwww.baidu.com%2F&amp;v=wap-0-0.2&amp;rnd=6727231521';
        CONFIG['cnzz'] = '';
        CONFIG['isShowAds'] = 1;
        CONFIG['ad_url'] = '//ggs.myzaker.com/zk_wap_article_gg.php?pk=6859fb7b8e9f090ab77b4f3e&app_id=12156&author=%E6%9C%AA%E5%A4%AE%E7%BD%91&http_type=1&url_host=www.weiyangx.com&jsoncallback=?';
        CONFIG['article_wonderful_url'] = '/news/article_wonderful.php?pk=6859fb7b8e9f090ab77b4f3e';
        CONFIG['article_action_url'] = '/news/article_action.php';
        CONFIG['article_recommend_url'] = '/news/article_recommend.php?v=7';
        CONFIG['article_relate_url'] = '/news/article_related.php?pk=6859fb7b8e9f090ab77b4f3e';
       // CONFIG['zk_wap_ad_recommend_url'] = '';
        CONFIG['wx_share_stat_url'] = 'https://stat.myzaker.com/stat_share.php?pk=6859fb7b8e9f090ab77b4f3e&app_id=12156&for=weixin';
        CONFIG['openType'] = ""; // 文章类型
        CONFIG['hasVideo'] = ""; // 是否有视频
        CONFIG['isWeb3Page'] = CONFIG.openType === 'web3' 
                            || document.getElementsByClassName('zk_audio').length > 0
                            || document.querySelectorAll('#content iframe').length > 0;
                            // || document.getElementsByTagName('iframe').length > 0
                            
        // 小程序支持video文章打开
        // || document.getElementsByTagName('video').length > 0
        // || CONFIG.hasVideo === '1' 
        // || document.getElementsByClassName('zk_upvideo').length > 0
       var https = 'https:' == document.location.protocol ? true:false; 
         CONFIG['wx_share_link'].replace('http://','https://');
    var imglazy = document.querySelectorAll('.img_box .lazy');
    imglazy = Array.prototype.slice.call(imglazy);
    imglazy.forEach(function(img){
        var dWidth = img.dataset['width'];
        var dHeight = img.dataset['height'];
        var parentEle = img;
            parentEle = parentEle.parentNode;
        while(!parentEle.classList.contains('img_box') || parentEle.id == "content")
        // 获取图片的父容器占宽
        var parentWidth = parentEle.offsetWidth;
        // 1.  图片原宽度大于容器宽度70%，撑到100%
        // 2.  图片原宽度大于容器宽度40%，小于容器宽度70%，保持图片原尺寸
        // 3.  图片原宽度小于容器宽度40%，撑到40%
        var maxRate = 0.7;
        var minRate = 0.4;
        var maxWidth = maxRate * parentWidth;
        var minWidth = minRate * parentWidth;
        var imgWidth, imgHeight;
        if (dWidth) {
            if (dWidth > maxWidth) {
                imgWidth = parentWidth;
            } else if (dWidth > minWidth) {
                imgWidth = dWidth;
                img.parentNode.style['display'] = 'inline-block';
                // img.parent('.content_img_div').css('display', 'inline-block');
                imgWidth = minWidth;
                img.parentNode.style['display'] = 'inline-block';
                // img.parent('.content_img_div').css('display', 'inline-block');
            imgHeight = dHeight / dWidth * imgWidth;
            imgWidth = parentWidth;
        img.style['width'] = imgWidth + "px";
        img.style['height'] = imgHeight + "px";
	// for (var i = 0; i < imglazy.length; i++) {
	// 	//var w = parseInt(imglazy[i].getAttribute('width'));
	// 	//var h = parseInt(imglazy[i].getAttribute('height'));
	// 	//if (!(w > 0 && h > 0)) {
	// 	    var w = parseInt(imglazy[i].getAttribute('data-width'));
	// 		var h = parseInt(imglazy[i].getAttribute('data-height'));
	// 	//if (w > 0 && h > 0) {
	// 		var rdw = document.getElementById("content").clientWidth;
	// 		//var rw = w < rdw ? w : rdw;
	// 		var rh = rdw * (h / w);
	// 		imglazy[i].setAttribute('width', Math.floor(rdw));
	// 		imglazy[i].setAttribute('height', Math.floor(rh));
        init: function(){
            var Navigator = navigator.userAgent;
            var ifChrome = Navigator.match(/Chrome/i) != null && Navigator.match(/Version\/\d+\.\d+(\.\d+)?\sChrome\//i) == null ? true : false; 
            //提示打开app
            if(!Navigator.match(/zaker/ig) && CONFIG.auto_open_deeplink == 'Y' && CONFIG.deeplink != '' && Navigator.match(/iphone|ipod|Android/ig)){
                var referrer = document.referrer || '';
                //  && navigator.userAgent.match(/Android/ig) 
                if( (referrer == '' || !referrer.match(/app.myzaker.com/ig)) ){
                    if(ifChrome){
                        location.href = CONFIG.android_chrome_link;
                    }else{
                        this.appBridge(CONFIG.deeplink);
                    }
        appBridge: function(src){
            var iframe = document.createElement('IFRAME');
            iframe.setAttribute('src', src);
            document.documentElement.appendChild(iframe);
            iframe.parentNode.removeChild(iframe);
            iframe = null;
        launchWeApp: function(){
            // web3 / 视频类文章，打开小程序首页
            if(CONFIG['isWeb3Page']){
                document.getElementById('launch-article').setAttribute('path','/pages/home/<USER>')
                document.getElementById('launch-common').setAttribute('path','/pages/home/<USER>')
            // 点击打开小程序，添加统计信息
            var addStat = function (e) {
                var img = new Image();
                img.src = window.statLink.article_wap_open;
                if(CONFIG['isWeb3Page']){
                    window.location.reload()
            document.getElementById('launch-index').addEventListener('launch',addStat)
            document.getElementById('launch-article').addEventListener('launch',addStat)
            document.getElementById('launch-common').addEventListener('launch',addStat)
    main.launchWeApp()
        //点击 “打开” 后执行的函数
        openFunc: function(){
            openlink('main')
        //点击 “打开” 后跳转的链接地址
        openUrl : "" 
	var inzaker = (navigator.userAgent.match(/zaker/ig)) ? true : false;
    if(!inzaker && !navigator.userAgent.match(/AlipayClient/ig) ){
        //zkTopBar.intelInit();
        zkTopBar.init();
        if(document.querySelector('.ntpl_head')){
            (function(){
                function getStyle(obj,attr){ 
                    if(obj.currentStyle){ 
                        return obj.currentStyle[attr]; 
                    }else{ 
                        return document.defaultView.getComputedStyle(obj,null)[attr]; 
                    } 
                var $ntplHead = document.querySelector('.ntpl_head'),
                    pt = getStyle($ntplHead, 'paddingTop');
                $ntplHead.style.paddingTop = (parseInt(pt, 10) - 20)+'px';
	window.zkgetWebConfig = function(data) {
        inzaker = true;
        if(data.appType == 'elderly'){
          document.getElementsByTagName('body')[0].className += ' body_elderly'; 
        shareTimeline: 'http://gstat.myzaker.com/event_stat.php?event=VideoEndMomentsShareClick&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=',
        shareWechat: 'http://gstat.myzaker.com/event_stat.php?event=VideoEndWechatShareClick&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=',
        replayVideo: 'http://gstat.myzaker.com/event_stat.php?event=VideoEndReplayClick&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=',
        article_wap_open:'http://gstat.myzaker.com/event_stat.php?event=Articlewap_Open_Xcx_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=',
    var friendshipUrl = 'https://app.myzaker.com/news/friendship.php';
    var statUrl = {"Articlewap_Recommend_Wx_View":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Recommend_Wx_View&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Recommend_Qt_View":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Recommend_Qt_View&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Comment_Wx_View":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Comment_Wx_View&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Comment_Qt_View":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Comment_Qt_View&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Commentup_View":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Commentup_View&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Open_Wx_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Open_Wx_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Open_Qt_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Open_Qt_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Recommend01_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Recommend01_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Recommend02_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Recommend02_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Recommend03_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Recommend03_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Recommend04_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Recommend04_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Recommend_More_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Recommend_More_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Commentps_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Commentps_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Commentup_Wx_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Commentup_Wx_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Commentup_Qt_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Commentup_Qt_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Comment_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Comment_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Comment_Delete_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Comment_Delete_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Commentde_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Commentde_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","Articlewap_Commentup_Report_Click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Commentup_Report_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","video_end_moments_share_click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=VideoEndMomentsShareClick&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","video_end_wechat_share_click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=VideoEndWechatShareClick&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","video_end_replay_click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=VideoEndReplayClick&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id=","article_wap_open_xcx_click":"http:\/\/gstat.myzaker.com\/event_stat.php?event=Articlewap_Open_Xcx_Click&item_pk=6859fb7b8e9f090ab77b4f3e&position=&category=&raw_app_id="};
    var checkLoginUrl = 'https://app.myzaker.com/news/login.php?act=checkLogin&pk=6859fb7b8e9f090ab77b4f3e';
    var articleData = {"no_comment":'0'};
    window.articleServConf && window.articleServConf.init();
if(browser.versions.wx){
        //将默认在微信里有效的元素，去掉default_hidden样式
       var thisclasselement =  Array.prototype.slice.call(document.querySelectorAll('.only_show_in_wx'));
        thisclasselement.forEach(function(e){
            e.classList.remove("default_hidden");
    if(checkInZaker()){
        var tmp_classelement =  Array.prototype.slice.call(document.querySelectorAll('.no_show_in_zaker'));
        tmp_classelement.forEach(function(e){
            e.style.display = "none";
        "@context": "https://ziyuan.baidu.com/contexts/cambrian.jsonld",
        "@id": "https://app.myzaker.com/article/6859fb7b8e9f090ab77b4f3e",
        "appid": "1551600451668502",
        "title": "AI虚拟细胞：数字化生命的探索",
        "images": ["http:\/\/zkres1.myzaker.com\/202506\/6859fb7b8e9f090ab77b4f3f_1024.jpg"],
        "description": "细胞是构成生命的基础单元，人体内的细胞时时刻刻在发生变化。根据《自然 - 医学》2021 年发布的研究，一个人每天更新的细胞量为 60~100 克，约 3300 亿个细胞。也就是说，每过 1 秒，就有",
        "pubDate": "2025-06-24T09:12:03"
const userAgent = navigator.userAgent;
    const isMobile = /Mobile/.test(userAgent);
      if (isMobile) {
        (window.slotbydup = window.slotbydup || []).push({
            id: "u6935409",
            container: "_7tse8rzjrf8",
            async: true
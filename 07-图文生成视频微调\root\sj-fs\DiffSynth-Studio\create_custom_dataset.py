#!/usr/bin/env python3
"""
自定义数据集创建工具 - 适配Wan2.1-T2V-1.3B微调
支持从视频文件自动生成metadata.csv
"""

import pandas as pd
import os
import cv2
from pathlib import Path
import json
from typing import Dict, Optional
import argparse

class CustomDatasetCreator:
    """自定义数据集创建器"""
    
    def __init__(self, dataset_dir: str):
        self.dataset_dir = Path(dataset_dir)
        self.videos_dir = self.dataset_dir / "videos"
        
        # 创建目录
        self.dataset_dir.mkdir(parents=True, exist_ok=True)
        self.videos_dir.mkdir(exist_ok=True)
    
    def analyze_video(self, video_path: Path) -> Dict:
        """分析视频文件属性"""
        
        cap = cv2.VideoCapture(str(video_path))
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        # 获取视频属性
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'fps': round(fps, 2),
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': round(duration, 2)
        }
    
    def generate_prompt_from_filename(self, filename: str) -> str:
        """从文件名生成基础提示词"""
        
        # 移除扩展名和数字
        name = Path(filename).stem
        name = ''.join([c if not c.isdigit() else ' ' for c in name])
        name = name.replace('_', ' ').replace('-', ' ').strip()
        
        # 清理多余空格
        name = ' '.join(name.split())
        
        if not name:
            return "A video scene"
        
        return f"A video showing {name}"
    
    def create_metadata_from_videos(self, 
                                  video_dir: str,
                                  custom_prompts: Optional[Dict[str, str]] = None,
                                  default_negative_prompt: str = "blurry, low quality, static") -> pd.DataFrame:
        """从视频文件创建metadata"""
        
        video_dir = Path(video_dir)
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv']
        
        data = []
        
        print(f"🔍 扫描视频目录: {video_dir}")
        
        for video_file in video_dir.rglob('*'):
            if video_file.suffix.lower() in video_extensions:
                print(f"📹 处理视频: {video_file.name}")
                
                try:
                    # 分析视频属性
                    video_info = self.analyze_video(video_file)
                    
                    # 生成提示词
                    if custom_prompts and video_file.name in custom_prompts:
                        prompt = custom_prompts[video_file.name]
                    else:
                        prompt = self.generate_prompt_from_filename(video_file.name)
                    
                    # 计算相对路径
                    if video_dir.name == "videos":
                        relative_path = f"videos/{video_file.name}"
                    else:
                        relative_path = str(video_file.relative_to(video_dir.parent))
                    
                    data.append({
                        'video_path': relative_path,
                        'prompt': prompt,
                        'negative_prompt': default_negative_prompt,
                        'duration': video_info['duration'],
                        'fps': video_info['fps'],
                        'width': video_info['width'],
                        'height': video_info['height'],
                        'category': 'custom'
                    })
                    
                except Exception as e:
                    print(f"⚠️ 无法处理视频 {video_file.name}: {e}")
                    continue
        
        df = pd.DataFrame(data)
        print(f"✅ 创建了 {len(df)} 个视频样本的metadata")
        
        return df
    
    def load_custom_prompts(self, prompts_file: str) -> Dict[str, str]:
        """加载自定义提示词文件"""
        
        prompts = {}
        prompts_path = Path(prompts_file)
        
        if not prompts_path.exists():
            print(f"⚠️ 提示词文件不存在: {prompts_file}")
            return prompts
        
        if prompts_file.endswith('.json'):
            with open(prompts_file, 'r', encoding='utf-8') as f:
                prompts = json.load(f)
        elif prompts_file.endswith('.txt'):
            with open(prompts_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if ':' in line:
                        filename, prompt = line.split(':', 1)
                        prompts[filename.strip()] = prompt.strip()
        
        print(f"📝 加载了 {len(prompts)} 个自定义提示词")
        return prompts
    
    def filter_videos_by_criteria(self, df: pd.DataFrame, 
                                 min_duration: float = 2.0,
                                 max_duration: float = 30.0,
                                 min_resolution: tuple = (256, 256),
                                 target_fps_range: tuple = (20, 35)) -> pd.DataFrame:
        """根据条件过滤视频"""
        
        print(f"🔍 过滤前视频数量: {len(df)}")
        
        # 时长过滤
        df_filtered = df[(df['duration'] >= min_duration) & (df['duration'] <= max_duration)]
        print(f"⏱️ 时长过滤后: {len(df_filtered)} 个视频")
        
        # 分辨率过滤
        df_filtered = df_filtered[(df_filtered['width'] >= min_resolution[0]) & 
                                 (df_filtered['height'] >= min_resolution[1])]
        print(f"📐 分辨率过滤后: {len(df_filtered)} 个视频")
        
        # FPS过滤
        df_filtered = df_filtered[(df_filtered['fps'] >= target_fps_range[0]) & 
                                 (df_filtered['fps'] <= target_fps_range[1])]
        print(f"🎬 FPS过滤后: {len(df_filtered)} 个视频")
        
        return df_filtered
    
    def save_dataset(self, df: pd.DataFrame, output_path: str):
        """保存数据集"""
        
        df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"💾 数据集保存到: {output_path}")
        
        # 保存统计信息
        stats = {
            'total_videos': len(df),
            'total_duration': round(df['duration'].sum(), 2),
            'avg_duration': round(df['duration'].mean(), 2),
            'resolution_stats': {
                'min_width': int(df['width'].min()),
                'max_width': int(df['width'].max()),
                'min_height': int(df['height'].min()),
                'max_height': int(df['height'].max())
            },
            'fps_stats': {
                'min_fps': round(df['fps'].min(), 2),
                'max_fps': round(df['fps'].max(), 2),
                'avg_fps': round(df['fps'].mean(), 2)
            },
            'categories': df['category'].value_counts().to_dict()
        }
        
        stats_path = Path(output_path).parent / "dataset_stats.json"
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        print(f"📊 统计信息保存到: {stats_path}")
        print(f"📈 数据集统计: {stats['total_videos']}个视频, 总时长{stats['total_duration']}秒")

def main():
    parser = argparse.ArgumentParser(description="创建自定义视频数据集")
    parser.add_argument("--video_dir", type=str, required=True, help="视频文件目录")
    parser.add_argument("--output_dir", type=str, default="./data/custom_dataset", help="输出数据集目录")
    parser.add_argument("--prompts_file", type=str, help="自定义提示词文件 (.json或.txt)")
    parser.add_argument("--min_duration", type=float, default=2.0, help="最小视频时长（秒）")
    parser.add_argument("--max_duration", type=float, default=30.0, help="最大视频时长（秒）")
    parser.add_argument("--min_width", type=int, default=256, help="最小视频宽度")
    parser.add_argument("--min_height", type=int, default=256, help="最小视频高度")
    
    args = parser.parse_args()
    
    # 创建数据集创建器
    creator = CustomDatasetCreator(args.output_dir)
    
    # 加载自定义提示词（如果提供）
    custom_prompts = None
    if args.prompts_file:
        custom_prompts = creator.load_custom_prompts(args.prompts_file)
    
    # 创建metadata
    df = creator.create_metadata_from_videos(
        args.video_dir, 
        custom_prompts=custom_prompts
    )
    
    if len(df) == 0:
        print("❌ 未找到有效的视频文件")
        return
    
    # 过滤视频
    df_filtered = creator.filter_videos_by_criteria(
        df,
        min_duration=args.min_duration,
        max_duration=args.max_duration,
        min_resolution=(args.min_width, args.min_height)
    )
    
    if len(df_filtered) == 0:
        print("❌ 过滤后没有符合条件的视频")
        return
    
    # 保存数据集
    output_path = Path(args.output_dir) / "metadata.csv"
    creator.save_dataset(df_filtered, output_path)
    
    print("🎉 自定义数据集创建完成！")
    print(f"📁 数据集位置: {args.output_dir}")
    print(f"📊 包含视频: {len(df_filtered)} 个")

if __name__ == "__main__":
    main()

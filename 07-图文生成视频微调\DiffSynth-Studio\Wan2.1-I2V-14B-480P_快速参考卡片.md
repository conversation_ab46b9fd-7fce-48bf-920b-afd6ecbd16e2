# Wan2.1-I2V-14B-480P 多卡微调快速参考卡片

## 🎉 验证成功的配置

### 硬件环境
- **GPU**: 2×NVIDIA A100-SXM4-80GB
- **训练时间**: 121.16秒 (2.02分钟)
- **显存使用**: 每张GPU约1.1GB

### 软件环境
```bash
conda create -n wan_video_env python=3.9 -y
conda activate wan_video_env
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
pip install accelerate transformers diffusers peft safetensors
```

### accelerate_config.yaml
```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2  # 对应GPU数量
rdzv_backend: static
same_network: true
use_cpu: false
```

## ✅ 成功的训练命令

```bash
cd /root/sj-tmp/DiffSynth-Studio && \
source /root/miniconda3/etc/profile.d/conda.sh && \
conda activate wan_video_env && \
accelerate launch --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 480 --width 832 --dataset_repeat 1 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-4 --num_epochs 1 --gradient_accumulation_steps 1 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "./models/train/Wan2.1-I2V-14B-480P_lora_final" \
  --lora_base_model "dit" --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 8 --extra_inputs "input_image" --mixed_precision "bf16"
```

## 🔧 关键问题解决

### 1. 移除不支持的参数
```bash
# ❌ 错误 - 会导致失败
--redirect_common_files False

# ✅ 正确 - 移除该参数
# 系统自动处理文件重定向
```

### 2. 处理模型文件损坏
```bash
# 删除损坏的文件，让系统重新下载
rm ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
rm ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
```

### 3. 解决NCCL超时
```bash
export NCCL_TIMEOUT=1800
export TOKENIZERS_PARALLELISM=false
```

## 📊 训练结果

### 成功日志关键信息
```
INFO:__main__:Number of processes: 2
INFO:__main__:Distributed type: DistributedType.MULTI_GPU
INFO:__main__:Mixed precision: bf16
[rank0]: using GPU 0
[rank1]: using GPU 1
INFO:__main__:Starting training for 1 epochs...
100%|█████████████████| 1/1 [00:50<00:00, 50.12s/it]
INFO:__main__:Training completed in 121.16 seconds (2.02 minutes)
INFO:__main__:Model saved to ./models/train/Wan2.1-I2V-14B-480P_lora_final
```

### 模型组件
- **DiT模型**: 14B参数，40层Transformer
- **文本编码器**: 10.6GB (T5-XXL)
- **VAE**: 484MB
- **图像编码器**: 4.44GB (CLIP)

### LoRA配置
- **Rank**: 8
- **目标模块**: q,k,v,o,ffn.0,ffn.2
- **基础模型**: dit
- **混合精度**: bf16

## 🚀 快速启动脚本

创建 `quick_start.sh`:
```bash
#!/bin/bash
set -e

# 激活环境
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

# 设置环境变量
export NCCL_TIMEOUT=1800
export TOKENIZERS_PARALLELISM=false

# 创建输出目录
OUTPUT_DIR="./models/train/Wan2.1-I2V-14B-480P_lora_$(date +%Y%m%d_%H%M%S)"

# 启动训练
accelerate launch --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 480 --width 832 --dataset_repeat 1 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-4 --num_epochs 1 --gradient_accumulation_steps 1 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "$OUTPUT_DIR" \
  --lora_base_model "dit" --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 8 --extra_inputs "input_image" --mixed_precision "bf16"

echo "🎉 训练完成! 模型保存在: $OUTPUT_DIR"
```

## 🔍 故障排除

### 常见错误和解决方案

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| `unrecognized arguments: --redirect_common_files` | 参数不支持 | 移除该参数 |
| `SafetensorError: MetadataIncompleteBuffer` | 模型文件损坏 | 删除损坏文件重新下载 |
| `NCCL timeout` | 通信超时 | 设置 `NCCL_TIMEOUT=1800` |
| `ModuleNotFoundError: torch` | 环境问题 | 激活正确的conda环境 |
| `CUDA out of memory` | 显存不足 | 减少batch_size或lora_rank |

### 验证命令
```bash
# 检查GPU
nvidia-smi

# 检查环境
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}')"

# 检查accelerate配置
accelerate env
```

## 📈 性能优化建议

### 生产环境参数
```bash
--dataset_repeat 20 \
--num_epochs 2 \
--gradient_accumulation_steps 4 \
--lora_rank 32 \
```

### 硬件升级路径
- **2×A100**: 基础配置 (已验证)
- **4×A100**: 更快训练 (修改num_processes=4)
- **8×A100**: 大规模训练 (修改num_processes=8)

---

**状态**: ✅ 验证成功
**最后更新**: 2025-07-17
**验证环境**: 2×A100-80GB, CUDA 12.4
**训练时间**: 121.16秒

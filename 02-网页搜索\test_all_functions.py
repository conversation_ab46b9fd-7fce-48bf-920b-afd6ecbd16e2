#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有功能
"""
import requests
import json
import time

def test_all_functions():
    """测试所有功能"""
    print("🚀 开始测试AI搜索系统所有功能...")
    
    # 1. 测试用户注册
    print("\n=== 1. 测试用户注册 ===")
    register_data = {
        'username': 'testuser2025',
        'password': 'test123456',
        'email': '<EMAIL>'
    }
    
    response = requests.post('http://localhost:8080/api/register', json=register_data)
    if response.status_code == 200:
        print("✅ 用户注册成功")
    else:
        print(f"⚠️ 用户注册失败或用户已存在: {response.text}")
    
    # 2. 测试用户登录
    print("\n=== 2. 测试用户登录 ===")
    login_data = {
        'username': 'testuser2025',
        'password': 'test123456'
    }
    
    response = requests.post('http://localhost:8080/api/login', json=login_data)
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 用户登录成功")
            user_session = response.cookies.get('session_id')
            user_info = data['user']
            print(f"   用户ID: {user_info['id']}")
            print(f"   用户名: {user_info['username']}")
            print(f"   积分: {user_info['credits']}")
            print(f"   VIP状态: {user_info['is_vip']}")
        else:
            print(f"❌ 用户登录失败: {data.get('error')}")
            return
    else:
        print(f"❌ 用户登录失败: {response.text}")
        return
    
    # 3. 测试管理员登录
    print("\n=== 3. 测试管理员登录 ===")
    admin_login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = requests.post('http://localhost:8080/api/login', json=admin_login_data)
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 管理员登录成功")
            admin_session = response.cookies.get('session_id')
            admin_info = data['user']
            print(f"   管理员ID: {admin_info['id']}")
            print(f"   管理员名: {admin_info['username']}")
            print(f"   是否管理员: {admin_info.get('is_admin', True)}")
        else:
            print(f"❌ 管理员登录失败: {data.get('error')}")
            return
    else:
        print(f"❌ 管理员登录失败: {response.text}")
        return
    
    # 4. 测试创建卡密
    print("\n=== 4. 测试创建卡密 ===")
    card_data = {
        'days': 30,
        'count': 1,
        'description': '测试卡密2025'
    }
    
    cookies = {'session_id': admin_session}
    response = requests.post('http://localhost:8080/api/admin/cards', 
                           json=card_data, cookies=cookies)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 卡密创建成功")
            test_card_key = data['cards'][0]['card_key']
            print(f"   卡密: {test_card_key}")
        else:
            print(f"❌ 卡密创建失败: {data.get('error')}")
            return
    else:
        print(f"❌ 卡密创建失败: {response.text}")
        return
    
    # 5. 测试获取卡密列表
    print("\n=== 5. 测试获取卡密列表 ===")
    response = requests.get('http://localhost:8080/api/admin/cards', cookies=cookies)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 获取卡密列表成功")
            print(f"   总卡密数: {data['stats']['total_cards']}")
            print(f"   未使用: {data['stats']['unused_cards']}")
            print(f"   已使用: {data['stats']['used_cards']}")
        else:
            print(f"❌ 获取卡密列表失败: {data.get('error')}")
    else:
        print(f"❌ 获取卡密列表失败: {response.text}")
    
    # 6. 测试用户充值
    print("\n=== 6. 测试用户充值 ===")
    recharge_data = {
        'card_key': test_card_key
    }
    
    user_cookies = {'session_id': user_session}
    response = requests.post('http://localhost:8080/api/user/recharge', 
                           json=recharge_data, cookies=user_cookies)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 用户充值成功")
            print(f"   充值信息: {data['message']}")
            print(f"   VIP到期时间: {data.get('expire_date', 'N/A')}")
        else:
            print(f"❌ 用户充值失败: {data.get('error')}")
    else:
        print(f"❌ 用户充值失败: {response.text}")
    
    # 7. 测试获取用户信息
    print("\n=== 7. 测试获取用户信息 ===")
    response = requests.get('http://localhost:8080/api/user/info', cookies=user_cookies)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 获取用户信息成功")
            user = data['user']
            print(f"   用户名: {user['username']}")
            print(f"   积分: {user['credits']}")
            print(f"   VIP状态: {user['is_vip']}")
            print(f"   VIP到期: {user.get('vip_expire_date', 'N/A')}")
        else:
            print(f"❌ 获取用户信息失败: {data.get('error')}")
    else:
        print(f"❌ 获取用户信息失败: {response.text}")
    
    # 8. 测试AI对话（简单测试）
    print("\n=== 8. 测试AI对话 ===")
    chat_data = {
        'message': '你好，请简单介绍一下自己',
        'conversation_id': None
    }
    
    response = requests.post('http://localhost:8080/api/chat', 
                           json=chat_data, cookies=user_cookies, stream=True)
    
    if response.status_code == 200:
        print("✅ AI对话接口连接成功")
        print("   正在接收AI回复...")
        
        # 读取流式响应的前几行
        lines_read = 0
        for line in response.iter_lines():
            if line and lines_read < 5:  # 只读取前5行
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    if data_str.strip() and data_str.strip() != '[DONE]':
                        try:
                            data_obj = json.loads(data_str)
                            if 'content' in data_obj:
                                print(f"   AI回复片段: {data_obj['content'][:50]}...")
                                lines_read += 1
                        except:
                            pass
        print("   ✅ AI对话功能正常")
    else:
        print(f"❌ AI对话失败: {response.status_code}")
    
    # 9. 测试在线搜索
    print("\n=== 9. 测试在线搜索 ===")
    search_data = {
        'question': '人工智能最新发展',
        'mode': 'online'
    }
    
    response = requests.post('http://localhost:8080/api/search', 
                           json=search_data, cookies=user_cookies)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 在线搜索功能正常")
            print(f"   搜索ID: {data.get('search_id')}")
        else:
            print(f"❌ 在线搜索失败: {data.get('error')}")
    else:
        print(f"❌ 在线搜索失败: {response.text}")
    
    # 10. 测试用户登出
    print("\n=== 10. 测试用户登出 ===")
    response = requests.post('http://localhost:8080/api/logout', cookies=user_cookies)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 用户登出成功")
        else:
            print(f"❌ 用户登出失败: {data.get('error')}")
    else:
        print(f"❌ 用户登出失败: {response.text}")
    
    print("\n🎉 所有功能测试完成！")
    print("\n📋 测试总结:")
    print("   ✅ 用户注册/登录")
    print("   ✅ 管理员登录")
    print("   ✅ 卡密管理")
    print("   ✅ VIP充值")
    print("   ✅ 用户信息管理")
    print("   ✅ AI对话功能")
    print("   ✅ 在线搜索功能")
    print("   ✅ 用户登出")
    print("\n🌐 网页访问地址:")
    print("   主页: http://localhost:8080")
    print("   管理员页面: http://localhost:8080/admin.html")

if __name__ == '__main__':
    test_all_functions()

# 政府采购爬虫系统

## 项目简介

这是一个针对台湾政府电子采购网的数据爬取与查询系统，能够自动抓取采购信息并提供便捷的查询界面。

## 功能特性

- 🕷️ **智能爬虫**：自动抓取政府采购网数据
- 🔍 **全文搜索**：支持关键词搜索和类型筛选
- 📊 **数据统计**：提供详细的数据分析和统计
- 🌐 **Web界面**：直观的用户操作界面
- 📱 **响应式设计**：支持桌面和移动设备

## 系统架构

```
procurement_crawler/
├── src/
│   ├── crawler/           # 爬虫模块
│   │   └── procurement_crawler.py
│   ├── models/            # 数据模型
│   │   ├── user.py
│   │   └── procurement.py
│   ├── routes/            # API路由
│   │   ├── user.py
│   │   └── procurement.py
│   ├── static/            # 前端文件
│   │   └── index.html
│   ├── database/          # 数据库文件
│   └── main.py           # 主应用入口
├── venv/                 # 虚拟环境
├── requirements.txt      # 依赖包列表
└── README.md            # 说明文档
```

## 安装和运行

### 1. 环境要求
- Python 3.11+
- pip

### 2. 安装依赖
```bash
cd procurement_crawler
source venv/bin/activate
pip install -r requirements.txt
```

### 3. 启动应用
```bash
python src/main.py
```

应用将在 http://localhost:5000 启动

## 使用说明

### 启动爬虫任务

1. 在Web界面中输入搜索关键词（可选）
2. 选择要抓取的标案类型：
   - 招标
   - 决标
   - 公开阅览及公开征求
   - 政府采购预告
3. 设置最大页数和每页数量
4. 点击"启动爬虫"按钮
5. 通过"查看状态"监控爬虫进度

### 搜索和查询数据

1. 在搜索区域输入关键词
2. 选择标案类型进行筛选
3. 点击"搜索数据"查看结果
4. 点击"查看详情"获取完整信息

### 查看统计信息

点击"获取统计"按钮查看：
- 总数据量
- 按类型统计
- 主要机关统计

## API接口

### 爬虫控制
- `POST /api/procurement/crawl` - 启动爬虫任务
- `GET /api/procurement/crawl/status` - 获取爬虫状态

### 数据查询
- `GET /api/procurement/search` - 搜索采购数据
- `GET /api/procurement/data/{id}` - 获取单条详情
- `GET /api/procurement/stats` - 获取统计信息

## 数据模型

```sql
CREATE TABLE procurement_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,           -- 标案名称
    agency TEXT NOT NULL,          -- 机关名称
    case_number TEXT,              -- 标案案号
    tender_type TEXT NOT NULL,     -- 标案类型
    announcement_date TEXT,        -- 公告日期
    content TEXT,                  -- 详细内容
    url TEXT,                     -- 原始链接
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 技术栈

- **后端**：Flask + SQLAlchemy
- **前端**：HTML + CSS + JavaScript
- **数据库**：SQLite
- **爬虫**：requests + BeautifulSoup4
- **部署**：支持Docker和传统部署

## 注意事项

1. **合规使用**：请遵守目标网站的robots.txt和使用条款
2. **频率控制**：系统内置了请求间隔，避免对目标服务器造成压力
3. **数据更新**：建议定期运行爬虫任务以获取最新数据
4. **错误处理**：系统具备完善的错误处理和重试机制

## 故障排除

### 爬虫无法获取数据
1. 检查网络连接
2. 确认目标网站是否可访问
3. 查看日志中的错误信息
4. 考虑调整请求参数或增加延迟

### 数据库错误
1. 确认数据库文件权限
2. 检查磁盘空间
3. 重新初始化数据库

## 开发和扩展

### 添加新的数据源
1. 在 `src/crawler/` 目录下创建新的爬虫类
2. 在 `src/models/` 中定义数据模型
3. 在 `src/routes/` 中添加API路由
4. 更新前端界面

### 自定义搜索逻辑
修改 `src/routes/procurement.py` 中的搜索函数

### 部署到生产环境
1. 使用 gunicorn 或 uwsgi 作为WSGI服务器
2. 配置 nginx 作为反向代理
3. 设置定时任务自动运行爬虫

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：[您的邮箱]
- GitHub：[项目地址]


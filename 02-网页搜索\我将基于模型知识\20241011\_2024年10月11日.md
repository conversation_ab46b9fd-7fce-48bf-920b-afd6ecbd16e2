﻿# Ê¢È¤ÓÎÏ·ÄâÉÏÏß´óÄ£ĞÍÖªÊ¶¿â

**发布日期**: 2024年10月11日

**原文链接**: https://stock.10jqka.com.cn/20241011/c662327012.shtml

## 📄 原文内容

Ê¢È¤ÓÎÏ·ÄâÉÏÏß´óÄ£ĞÍÖªÊ¶¿â
¡¡¡¡¼ÇÕß»ñÏ¤£¬Ê¢È¤ÓÎÏ·¿ª·¢µÄ´óÄ£ĞÍÖªÊ¶¿âÕıÔÚ½øĞĞÄÚ²¿²âÊÔ£¬¼´½«ÉÏÏß¡£ÕâÊÇ»ùÓÚAI´óÄ£ĞÍºÍRAG¼¼Êõ(¼ìË÷ÔöÇ¿Éú³É)µÄÏµÍ³£¬Î´À´£¬Õû¸ö
(function(){var obj = null;if(document.getElementById('arctTailMark')) {obj = document.getElementById('arctTailMark');}if(obj!=null){var str = '<a href="http://www.10jqka.com.cn" target="_blank" id="back_web" style="font-weight:bold" rel="nofollow" class="backweb"><img src="http://i.thsi.cn/images/article/logo.jpg" style="height:15px">  ·µ»ØÊ×Ò³</a><a href="https://t.10jqka.com.cn/m/game/getActivityPageById/?id=295" target="_blank" style="font-weight: bold; font-size: 14px; padding-left: 5px; margin-left: 5px; border-left: 2px solid">¾Ù±¨ &gt;</a>';var showed = 0;try{var oo = obj.previousSibling;while(oo.nodeType!=1 || oo.innerHTML.replace(/\s|\u3000/g,'').length==0){oo = oo.previousSibling;}if(oo.nodeType==1 && oo.tagName.toLowerCase()=='p') {var span = document.createElement('span');span.innerHTML =str;span.style.width = '130px';oo.appendChild(span);showed = 1;}}catch (e){}if(showed == 0){obj.innerHTML =str;}}})();
var hotstocks = new Array();

	        	        hotstocks['000716'] = '000716';

	    	    	        hotstocks['002456'] = '002456';

	    	    	        hotstocks['601216'] = '601216';

	    	    	        hotstocks['603005'] = '603005';

	    	    	        hotstocks['600206'] = '600206';

	    	    	        hotstocks['000795'] = '000795';

	    	    	        hotstocks['000564'] = '000564';

	    	    	        hotstocks['002510'] = '002510';
¶À¼Ò×Ê½ğ£ºÔçÅÌÖ÷Á¦ÂòÈëÇ°10¹É
¶À¼Ò×Ê½ğ£ºÔçÅÌÖ÷Á¦½øÉ¢»§ÌÓÇ°10¹É
¶À¼Ò×Ê½ğ£º½ñÈÕÖ÷Á¦Âò½øÇ°10¹É
CPO¸ÅÄîÕğµ´»ØÉı ĞÂÒ×Ê¢ÕÇ³¬8%
Ïû·Ñµç×Ó¸ÅÄî¹ÉÕğµ´×ßµÍ Å··Æ¹âµÈ¶à¹Éµø³¬5%
A¹ÉÈı´óµçĞÅÔËÓªÉÌ¼¯Ìå×ßÇ¿ ÖĞ¹úµçĞÅÕÇ³¬5%
ÉÏº£ÊĞÕş¸®³£Îñ»áÒé²¿Êğ£¬Ö§³ÖÉÏÊĞ¹«Ë¾²¢¹ºÖØ×é£¬ÌáÉı¹«Ë¾ÖÊÁ¿ÅàÓıÁúÍ·ÆóÒµ
ÖĞĞÅÖ¤È¯ĞÂÈÎ×Ü¾­Àí Ê×´Î¹«¿ª·¢Éù
GPU¶À½ÇÊŞÄ¦¶ûÏß³Ì½«³å´ÌIPO£¡²Î¹É¡¢ºÏ×÷µÈ¸ÅÄî¹ÉÃûµ¥Ò»ÀÀ
ÔøØ¹Èº£ºÈôÌØÀÊÆÕ¡°¿ªÂÌµÆ¡±£¬ÄşµÂÊ±´ú½«¿¼ÂÇ¸°ÃÀ½¨³§
¸Õ¸Õ£¡ÑÇÌ«¹ÉÊĞ£¬È«ÏßÌøË®£¡A50Ö±Ïß·­ÂÌ£¬¸Û¹ÉÉîµ÷£¡·¢ÉúÁËÊ²Ã´
ÎÊ½çM7ÊÂ¹ÊĞÂ½øÕ¹£º³µÖ÷³Æ¡°¹ıÓÚÏàĞÅÓà³Ğ¶«²Å¹ºÂò¡±²¢ÆğËß»ªÎª
Àë¡°²»Âô¾Í½û¡±Ö»Ê£Á½¸ö¶àÔÂ£¡ÌØÀÊÆÕÍÅ¶Ó£º½«¶ÒÏÖ³ĞÅµ Õü¾ÈTikTok
Éî½»Ëù£º11ÔÂ16ÈÕ½«½øĞĞÉîÊĞ½»Ò×ÏµÍ³²âÊÔ
		var s = "_" + Math.random().toString(36).slice(2);
		document.write('<div id="' + s + '"></div>');
		(window.slotbydup=window.slotbydup || []).push({
			display: 'inlay-fix'
document.addEventListener('DOMContentLoaded', function () {
      var userid = 'nouser';
        // æ£€æŸ¥ Vue å’Œ AdvertCommon æ˜¯å¦å­˜åœ¨
        if (Vue && window['mobileweb_AdvertCommon@1.0.4'].default) {
          Vue.use(window['mobileweb_AdvertCommon@1.0.4'].default);
          // å°è¯•ä» cookie ä¸­æå– userid
          var match = document.cookie.match(/userid=([^;]*)/);
          if (match) {
            userid = match[1] || 'nouser';
      } catch (error) {
        console.error(error);
      // åˆå§‹åŒ– Vue å®ä¾‹
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
ÉæÎ´³ÉÄêÈËÎ¥¹æÄÚÈİ¾Ù±¨
²»Á¼ĞÅÏ¢¾Ù±¨µç»°£º(0571)88933003
¾Ù±¨ÓÊÏä£º*****************
Õã½­Í¬»¨Ë³»¥ÁªĞÅÏ¢¼¼ÊõÓĞÏŞ¹«Ë¾°æÈ¨ËùÓĞ
var newsid = '662327012';
    var id='c_662327012';
	var initctime = '1728621470';
	var stitle = encodeURIComponent('Ê¢È¤ÓÎÏ·ÄâÉÏÏß´óÄ£ĞÍÖªÊ¶¿â');
	var artStock = new Array();
	var cotitle = 'Ê¢È¤ÓÎÏ·ÄâÉÏÏß´óÄ£ĞÍÖªÊ¶¿â';
	var codata = cocontent.replace(/<[^>]*>/g, '').substr(0,250);
	var courl = 'http://stock.10jqka.com.cn/20241011/c662327012.shtml';
		artStock[0] = '002602';
        newRobotRecList : [],
		cid: ''  //×îºóÒ»ÌõÆÀÂÛµÄID
	ARTINFO.tday = $.timeFormat(ARTINFO.tday, 'yyyyMMddHHmm');
	ARTINFO.tday = parseInt(ARTINFO.tday);
	ARTINFO.userid = WEB.basic.getUserid();
$(document).ready(function(){
	    	    TA.log({id:id, ld:'browser',fid:'info_gather,zx_n_all,zx_wzb_total,zx_ll_1000,zxstock,ch_stock', pt:2,_sid:'imgzx'});
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?78c58f01938e4d85eaf619eae71b4ed1";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?f79b64788a4e377c608617fba4c736e2";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
// ´¦ÀíÍ¼Æ¬srcÎªundefined
  var imageDomList = document.querySelectorAll('img');
  if (imageDomList.length > 0) {
      imageDomList.forEach(ele => {
          var imgSrc = ele.src;
          if (imgSrc.endsWith('/undefined')) {
              ele.remove();
  var srcVideoDom = document.querySelector('input.video-insert');
    var videoUrl = srcVideoDom.getAttribute('data-src');
    var videoThumb = srcVideoDom.getAttribute('data-thumb');
    var videoDom = document.createElement('video');
    var parent = srcVideoDom.parentNode;
    videoDom.src = videoUrl;
    videoDom.poster = videoThumb;
    videoDom.setAttribute("controls", true);
    videoDom.setAttribute("style", "width:627px;height:353px");
    parent.insertBefore(videoDom, srcVideoDom);
    srcVideoDom.remove();
  $('.self-stock-con').css({'height':'206px'})
// ÏŞÖÆµÄ»úÆ÷ÈË×é¼ş°×Ãûµ¥
    const LIMIT_ROBOT_VISUAL_COMS = [
        'thsf2e_common-long-hu-rank',
        'thsf2e_ai-table'
    const checkLimitCom = (dataType) => {
        return LIMIT_ROBOT_VISUAL_COMS.includes(dataType);
    // Áú»¢°ñÌØÊâ´¦Àí
    const LONGHU_TYPE = 'thsf2e_common';
    // »ñÈ¡dom½ÚµãĞÅÏ¢
    const getDomAttrData = (robotParse) => {
        const dataType = robotParse.getAttribute('data-type');
        const dataProps = robotParse.getAttribute('data-props');
        const dataComurl = robotParse.getAttribute('data-comurl');
        const dataStyle = robotParse.getAttribute('data-style');
        const dataVersion = robotParse.getAttribute('data-version');
            dataType,
            dataProps,
            dataComurl,
            dataStyle,
            dataVersion,
    const loadScript = (url, callback) => {
        const originalDefine = window.define;
        window.define = undefined;
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.charset = 'utf-8';
        script.src = url;
        script.onload = function() {
            callback(null)
        script.onerror = function() {
            callback(`Failed to load script: ${url}`);
        document.body.appendChild(script);
    // ÅĞ¶ÏwindowÀïÊÇ·ñÓĞÕâ¸ö×é¼ş£¬Èç¹ûÓĞ¾ÍÖ±½ÓÊ¹ÓÃ£¬Èç¹ûÃ»ÓĞ¾Í¼ÓÔØ
    const getComponent = (id, url, callback) => {
        return new Promise((resolve, reject) => {
            if (window[id]) {
                return resolve(window[id].default)
            loadScript(url, (err) => {
                if (err || !window[id]) {
                    reject(err || '×é¼şid´íÎó')
                } else {
                    resolve(window[id].default)
    // ½âÎö»úÆ÷ÈËÄÚÈİ
    const parseRobot = (ele) => {
        const { dataType, dataProps, dataComurl, dataStyle, dataVersion } = getDomAttrData(ele);
        const props = JSON.parse(decodeURIComponent(dataProps));
        const style = JSON.parse(decodeURIComponent(dataStyle));
        // ÅĞ¶ÏÊÇ·ñ²»ÔÚlimitäÖÈ¾ÖĞ
        if (!checkLimitCom(dataType)) {
        const id = `${dataType}_index@${dataVersion}`;
        getComponent(id, dataComurl).then((com) => {
            const placeholder = document.createElement('div');
            placeholder.className = 'robot-visual-container';
            ele.replaceWith(placeholder);
            const comInstance = new Vue({
                el: placeholder,
                render: h => h('div', { attr: { id: 'robotVisual' }, style }, [
                    h(com, { props })
            }).$mount();
        }).catch(err => {
            console.error(err)
    const parseContentMain = () => {
        const eles = document.getElementById('contentApp').querySelectorAll('robot-parse');
        eles.forEach(ele => {
            parseRobot(ele)
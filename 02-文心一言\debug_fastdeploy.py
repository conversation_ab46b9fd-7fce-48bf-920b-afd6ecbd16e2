#!/usr/bin/env python3
"""
FastDeploy 输出格式调试脚本
用于了解FastDeploy的实际输出格式
"""

from fastdeploy import LLM, SamplingParams
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_fastdeploy_output():
    """调试FastDeploy的输出格式"""
    try:
        logger.info("初始化FastDeploy LLM...")
        
        # 创建LLM实例
        llm = LLM(
            model="baidu/ERNIE-4.5-0.3B-Paddle",
            max_model_len=32768
        )
        
        logger.info("模型加载完成，开始测试生成...")
        
        # 设置采样参数
        sampling_params = SamplingParams(
            temperature=0.8,
            top_p=0.95,
            max_tokens=50  # 使用较小的值进行测试
        )
        
        # 简单的测试提示
        prompt = "你好"
        
        logger.info(f"输入提示: {prompt}")
        
        # 生成文本
        outputs = llm.generate(prompt, sampling_params)
        
        # 详细分析输出格式
        logger.info("=== 输出格式分析 ===")
        logger.info(f"outputs 类型: {type(outputs)}")
        logger.info(f"outputs 长度: {len(outputs) if hasattr(outputs, '__len__') else 'N/A'}")
        
        if outputs:
            logger.info(f"outputs[0] 类型: {type(outputs[0])}")
            logger.info(f"outputs[0] 属性: {dir(outputs[0])}")
            
            # 尝试访问不同的属性
            output_obj = outputs[0]
            
            # 检查常见属性
            for attr in ['text', 'outputs', 'generated_text', 'completion', 'response']:
                if hasattr(output_obj, attr):
                    attr_value = getattr(output_obj, attr)
                    logger.info(f"属性 '{attr}': {type(attr_value)} = {attr_value}")
            
            # 如果有outputs属性，进一步分析
            if hasattr(output_obj, 'outputs'):
                outputs_attr = output_obj.outputs
                logger.info(f"outputs 属性类型: {type(outputs_attr)}")
                if hasattr(outputs_attr, '__len__') and len(outputs_attr) > 0:
                    logger.info(f"outputs[0] 类型: {type(outputs_attr[0])}")
                    logger.info(f"outputs[0] 属性: {dir(outputs_attr[0])}")
                    
                    # 检查第二层的属性
                    for attr in ['text', 'generated_text', 'completion']:
                        if hasattr(outputs_attr[0], attr):
                            attr_value = getattr(outputs_attr[0], attr)
                            logger.info(f"outputs[0].{attr}: {type(attr_value)} = {attr_value}")
            
            # 尝试直接打印对象
            logger.info(f"直接打印 outputs[0]: {output_obj}")
            
            # 尝试转换为字符串
            try:
                str_output = str(output_obj)
                logger.info(f"字符串转换: {str_output}")
            except Exception as e:
                logger.error(f"字符串转换失败: {e}")
        
        logger.info("=== 调试完成 ===")
        
    except Exception as e:
        logger.error(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_fastdeploy_output()

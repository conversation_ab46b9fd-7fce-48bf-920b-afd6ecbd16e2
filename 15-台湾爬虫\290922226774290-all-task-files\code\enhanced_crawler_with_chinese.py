#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版爬虫 - 支持繁体中文原始数据和简体中文数据输出
"""

import json
import pandas as pd
from datetime import datetime
from pathlib import Path
import opencc
import re

# 初始化繁体转简体转换器
converter = opencc.OpenCC('t2s')

# 标准字段映射 (根据PDF需求处理器的字段列表)
FIELD_MAPPING = {
    # 基本信息
    'case_name': '标案名称',
    'case_number': '标案案号', 
    'organization': '机关名称',
    'org_code': '机关代码',
    'unit_name': '单位名称',
    'org_address': '机关地址',
    'contact_person': '联络人',
    'contact_phone': '联络电话',
    'fax_number': '传真号码',
    
    # 日期信息
    'tender_date': '招标日期',
    'announcement_date': '公告日期', 
    'bid_opening_date': '开标日期',
    'award_date': '决标日期',
    'original_announcement_date': '原公告日期',
    'tender_notice_date': '招标公告日期',
    'award_notice_date': '决标公告日期',
    'bid_deadline': '截止投标日期',
    'bid_opening_time': '开标时间',
    
    # 招标信息
    'tender_method': '招标方式',
    'award_method': '决标方式',
    'case_type': '标案类型',
    'category': '标的分类',
    
    # 金额信息
    'budget_amount': '预算金额',
    'budget_amount_chinese': '预算金额中文',
    'procurement_amount_level': '采购金额级距',
    'budget_amount_public': '预算金额是否公开',
    'award_amount': '决标金额',
    'total_award_amount': '总决标金额',
    'reserve_price': '底价金额',
    
    # 履约信息
    'contract_location': '履约地点',
    'contract_region': '履约地区',
    'contract_period': '履约期间',
    'contract_number': '契约编号',
    
    # 补助信息
    'is_subsidized': '是否受机关补助',
    
    # 厂商信息
    'bidder_count': '投标厂商数',
    'company_name': '厂商名称',
    'company_code': '厂商代码',
    'is_winner': '是否得标',
    'organization_type': '组织型态',
    'company_address': '厂商地址',
    'company_phone': '厂商电话',
    
    # 其他信息
    'detail_url': '详情页链接',
    'pk_main': '主键',
    'tender_type': '公告类型',
    'item_name': '品项名称'
}

def convert_to_simplified(text):
    """将繁体中文转换为简体中文"""
    if not text or pd.isna(text):
        return ""
    return converter.convert(str(text))

def process_existing_data(json_file_path):
    """处理现有的JSON数据"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"📖 读取到 {len(data)} 条原始数据")

        # 处理每条记录
        processed_data_traditional = []  # 繁体中文原始数据
        processed_data_simplified = []   # 简体中文数据

        for item in data:
            # 创建繁体中文标准化数据
            traditional_record = {}
            simplified_record = {}

            # 直接处理已有的中文字段
            for key, value in item.items():
                # 繁体中文版本 (保持原始数据)
                traditional_record[key] = value if value else ''

                # 简体中文版本 (转换为简体)
                simplified_value = convert_to_simplified(value) if value else ''
                simplified_record[key] = simplified_value

            # 添加缺失的标准字段 (如果不存在)
            standard_fields = list(FIELD_MAPPING.values())
            for field in standard_fields:
                if field not in traditional_record:
                    traditional_record[field] = ''
                    simplified_record[field] = ''

            # 添加爬取时间
            crawl_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            traditional_record['爬取时间'] = crawl_time
            simplified_record['爬取时间'] = crawl_time

            processed_data_traditional.append(traditional_record)
            processed_data_simplified.append(simplified_record)

        return processed_data_traditional, processed_data_simplified

    except Exception as e:
        print(f"❌ 处理数据时出错: {e}")
        return [], []

def save_processed_data(traditional_data, simplified_data):
    """保存处理后的数据"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 确保目录存在
    Path('data/processed').mkdir(parents=True, exist_ok=True)
    
    # 保存繁体中文原始数据
    traditional_json_path = f'data/processed/taiwan_procurement_traditional_{timestamp}.json'
    with open(traditional_json_path, 'w', encoding='utf-8') as f:
        json.dump(traditional_data, f, ensure_ascii=False, indent=2)
    
    traditional_csv_path = f'data/processed/taiwan_procurement_traditional_{timestamp}.csv'
    pd.DataFrame(traditional_data).to_csv(traditional_csv_path, index=False, encoding='utf-8-sig')
    
    # 保存简体中文数据
    simplified_json_path = f'data/processed/taiwan_procurement_simplified_{timestamp}.json'
    with open(simplified_json_path, 'w', encoding='utf-8') as f:
        json.dump(simplified_data, f, ensure_ascii=False, indent=2)
    
    simplified_csv_path = f'data/processed/taiwan_procurement_simplified_{timestamp}.csv'
    pd.DataFrame(simplified_data).to_csv(simplified_csv_path, index=False, encoding='utf-8-sig')
    
    return {
        'traditional_json': traditional_json_path,
        'traditional_csv': traditional_csv_path,
        'simplified_json': simplified_json_path,
        'simplified_csv': simplified_csv_path
    }

def create_field_summary():
    """创建字段说明文档"""
    summary_content = f"""# 台湾政府采购网数据字段说明

## 数据版本说明

本系统生成两个版本的数据：

### 1. 繁体中文原始数据 (traditional)
- 保持台湾政府采购网原始的繁体中文内容
- 字段名称使用简体中文便于处理
- 数据内容保持原始繁体格式

### 2. 简体中文数据 (simplified)  
- 将所有繁体中文内容转换为简体中文
- 字段名称使用简体中文
- 便于大陆用户阅读和处理

## 标准字段列表 ({len(FIELD_MAPPING)} 个字段)

| 字段名称 | 说明 | 数据来源 |
|---------|------|---------|
"""
    
    field_categories = {
        '基本信息': ['标案名称', '标案案号', '机关名称', '机关代码', '单位名称', '机关地址', '联络人', '联络电话', '传真号码'],
        '日期信息': ['招标日期', '公告日期', '开标日期', '决标日期', '原公告日期', '招标公告日期', '决标公告日期', '截止投标日期', '开标时间'],
        '招标信息': ['招标方式', '决标方式', '标案类型', '标的分类'],
        '金额信息': ['预算金额', '预算金额中文', '采购金额级距', '预算金额是否公开', '决标金额', '总决标金额', '底价金额'],
        '履约信息': ['履约地点', '履约地区', '履约期间', '契约编号'],
        '厂商信息': ['投标厂商数', '厂商名称', '厂商代码', '是否得标', '组织型态', '厂商地址', '厂商电话'],
        '其他信息': ['详情页链接', '主键', '公告类型', '品项名称', '是否受机关补助']
    }
    
    for category, fields in field_categories.items():
        summary_content += f"\n### {category}\n"
        for field in fields:
            if field in FIELD_MAPPING.values():
                summary_content += f"| {field} | 政府采购相关信息 | 台湾政府电子采购网 |\n"
    
    summary_content += f"""
## 数据统计

- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 数据来源: 台湾政府电子采购网 (https://web.pcc.gov.tw)
- 字段总数: {len(FIELD_MAPPING)} 个标准字段
- 数据版本: 繁体中文原始版 + 简体中文版

## 使用说明

1. **繁体中文原始数据**: 适合需要保持原始格式的应用
2. **简体中文数据**: 适合大陆用户或需要简体中文的应用
3. **文件格式**: 同时提供 JSON 和 CSV 两种格式
4. **编码格式**: UTF-8 with BOM (CSV文件可直接在Excel中打开)
"""
    
    with open('data/processed/字段说明.md', 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    print("📝 字段说明文档已创建: data/processed/字段说明.md")

def main():
    """主函数"""
    print("🚀 === 增强版台湾政府采购网爬虫数据处理器 ===")
    print("📋 功能: 生成繁体中文原始数据和简体中文数据两个版本")
    
    # 查找最新的JSON数据文件
    json_files = list(Path('data/json').glob('ultimate_crawler_data_*.json'))
    if not json_files:
        print("❌ 未找到爬虫数据文件，请先运行爬虫")
        return
    
    latest_json = max(json_files, key=lambda x: x.stat().st_mtime)
    print(f"📖 使用数据文件: {latest_json}")
    
    # 处理数据
    traditional_data, simplified_data = process_existing_data(str(latest_json))
    
    if not traditional_data:
        print("❌ 数据处理失败")
        return
    
    # 保存数据
    file_paths = save_processed_data(traditional_data, simplified_data)
    
    # 创建说明文档
    create_field_summary()
    
    # 输出结果
    print(f"\n✅ 数据处理完成!")
    print(f"📊 处理记录数: {len(traditional_data)} 条")
    print(f"\n📁 生成文件:")
    print(f"  🇹🇼 繁体中文原始数据:")
    print(f"    - JSON: {file_paths['traditional_json']}")
    print(f"    - CSV:  {file_paths['traditional_csv']}")
    print(f"  🇨🇳 简体中文数据:")
    print(f"    - JSON: {file_paths['simplified_json']}")
    print(f"    - CSV:  {file_paths['simplified_csv']}")
    print(f"  📝 说明文档: data/processed/字段说明.md")
    
    # 显示数据预览
    if traditional_data:
        print(f"\n🔍 数据预览 (前3个字段):")
        sample = traditional_data[0]
        for i, (key, value) in enumerate(list(sample.items())[:3]):
            traditional_val = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
            simplified_val = convert_to_simplified(str(value))[:50] + "..." if len(convert_to_simplified(str(value))) > 50 else convert_to_simplified(str(value))
            print(f"  {key}:")
            print(f"    繁体: {traditional_val}")
            print(f"    简体: {simplified_val}")

if __name__ == "__main__":
    main()

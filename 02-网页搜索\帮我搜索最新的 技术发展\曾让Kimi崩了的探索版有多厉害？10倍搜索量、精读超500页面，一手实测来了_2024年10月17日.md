﻿# 曾让Kimi“崩了”的探索版有多厉害？10倍搜索量、精读超500页面，一手实测来了

**发布日期**: 2024年10月17日

**原文链接**: https://www.thepaper.cn/newsDetail_forward_29046509

## 📄 原文内容

Kimi国庆「憋」大招，「憋」出个探索版。

前不久，市场上就有传言称，月之暗面国庆「憋大招」。

至于这个「大招」具体是啥，众说纷纭：

有人说和多模态有关，还有人说是关于深度推理的。

不过，就在上周五，月之暗面高调宣布，上线 Kimi 探索版。

消息一出，不少体验者纷纷涌入官网，「Kimi 崩了」也一度登上热搜。

那么，这个探索版厉害在哪儿？它与 Kimi 普通版有啥区别？

这么说吧，Kimi 探索版的搜索量是普通版的 10 倍，一次搜索即可精读超过 500 个页面。

而且，它还能模拟人类的推理思考过程，多级分解复杂问题，并进行深度搜索，即时反思改进结果。

这似乎和 OpenAI 的 o1 有着异曲同工之妙。

Kimi 官方也很自信，「如果 Kimi 搜不到的信息，那大概率用户也很难自己通过传统搜索引擎找到」。

接下来，我们就实地测评一下，看看 10 倍搜索量、一次搜索可精读超 500 个页面的 Kimi 探索版，到底是搞噱头还是真实力？

1. 中国票房过四十亿的电影都讲了什么故事？

我们先通过这个娱乐性的问题，一窥 Kimi 探索版的「脑回路」。

在答题之前，它会像分析师一样，预先规划解决问题的整体策略，将复杂问题分解为层次化的子问题，然后分步来执行。

具体来说，它先全网搜索中国票房过 40 亿的电影，然后查找这些电影的故事情节介绍。

在搜索过程中，它会把复杂提问转换成多个匹配的搜索关键词，并基于前一步的搜索结果来实时生成下一步的搜索关键词。

此外，它还能即时反思搜索结果，在发现第一次回答中的情节简介存在信息缺失时，会主动补充更多内容。

我们又让它来总结上榜的电影具体票房，其回答与搜索引擎上的结果一致。

那么，Kimi 普通版的表现如何呢？

其工作流程是先理解问题，然后通过较为粗略的关键词搜索网页。

从网页阅读数量来看，普通版仅阅读了 25 个网页，而探索版则是 40 个。

虽然普通版整体上回答没毛病，但是对于影片故事的介绍过于简单，也无法反思搜索结果。

2. 请帮我总结一下特斯拉发布会

前几天的特斯拉发布会算是科技圈的大新闻了。

我们就让它总结一下特斯拉发布会的情况。

它阅读了 11 个网页，并在界面右侧显示所有的搜索结果，从中提取出四大亮点，分别是 Robotaxi、Robovan、Optimus 机器人和 FSD 自动驾驶，还分条缕析地介绍了它们的功能、设计、成本等。

我们进行了一一比对，回答全部正确，没有出现满嘴跑火车的现象。

我们继续追问，特斯拉发布了无人驾驶出租车，为啥股票会跌？

Kimi 探索版不仅搜索了中文网站，还使用英文关键词，网罗了 CBS、CNN 等外文媒体报道。

如果我们点击它回答中的小引号，界面右侧则会显示出引用的原文信息，这极大地降低了幻觉发生的可能性。

3. 甄嬛的生日和薛宝钗的生日相差几天？

一直以来，大模型都是重度偏科者，写小作文嘎嘎好使，但一到数学题就抓瞎 ——

9.9 和 9.11 谁大分不清，strawberry 几个 r 搞不明白。

不过，计算甄嬛和薛宝钗这俩八杆子打不着的影视人物的生日，Kimi 探索版有一套自己的解题思路。

在答题之前，Kimi 探索版先是全网搜索甄嬛和薛宝钗的生日分别是哪天，并在页面右侧显示所有的搜索结果。

然后来到重头戏，计算二者生日差值。

它先算出从正月二十一到四月十七，每个月有几天，然后分别算出总天数，全部相加即可。

不过，它的回答还是有瑕疵，因为农历月份长度以朔望月为准，正月的天数并不固定，因此最终的计算结果仍有偏差。

4.Elon Musk 的生日和 Sam AItman 的生日相差几天？详细介绍计算过程。

农历的计算方法过于复杂，我们也搞不清楚，因此，就让 Kimi 探索版再计算一下马斯克和奥特曼的生日差。

我们专门用日期计算器检验了一下，Kimi 探索版回答正确。

5. 奥利弗在星期五摘了 44 个猕猴桃。然后在星期六摘了 58 个猕猴桃。星期天，他摘的猕猴桃数量是星期五的两倍，但其中 5 个比平均大小要小。奥利弗有多少个猕猴桃？

前天，机器之心发布了一篇题为《》的文章。

我们就拿文中的数学题来测一下 Kimi 探索版。

先来个正常的：奥利弗在星期五摘了 44 个猕猴桃。然后在星期六摘了 58 个猕猴桃。星期天，他摘的猕猴桃数量是星期五的两倍，奥利弗有多少个猕猴桃？

Kimi 探索版先把思路梳理得清清楚楚，然后每一步的执行都相当准确。

我们又在这道题目中，加了一句废话「但其中 5 个比平均大小要小」，OpenAI 的 o1 就翻了车。

但是，Kimi 探索版竟然没有被误导，得出了正确答案。

目前，Kimi 探索版已覆盖全量用户。大家也抓紧去薅一波吧～

原标题：《曾让Kimi「崩了」的探索版有多厉害？10倍搜索量、精读超500页面，一手实测来了》

特别声明 本文为澎湃号作者或机构在澎湃新闻上传并发布，仅代表该作者或机构观点，不代表澎湃新闻的观点或立场，澎湃新闻仅提供信息发布平台。申请澎湃号请用电脑访问http://renzheng.thepaper.cn。 +1 收藏 我要举报

特别声明 本文为澎湃号作者或机构在澎湃新闻上传并发布，仅代表该作者或机构观点，不代表澎湃新闻的观点或立场，澎湃新闻仅提供信息发布平台。申请澎湃号请用电脑访问http://renzheng.thepaper.cn。

本文为澎湃号作者或机构在澎湃新闻上传并发布，仅代表该作者或机构观点，不代表澎湃新闻的观点或立场，澎湃新闻仅提供信息发布平台。申请澎湃号请用电脑访问http://renzheng.thepaper.cn。

扫码下载 澎湃新闻客户端 Android版 iPhone版 iPad版

扫码下载 澎湃新闻客户端 Android版 iPhone版 iPad版

澎湃矩阵 澎湃新闻微博 澎湃新闻公众号 澎湃新闻抖音号 派生万物开放平台 IP SHANGHAI SIXTH TONE

澎湃矩阵 澎湃新闻微博 澎湃新闻公众号 澎湃新闻抖音号 派生万物开放平台 IP SHANGHAI SIXTH TONE

新闻报料 报料热线: 021-962866 报料邮箱: <EMAIL>

新闻报料 报料热线: 021-962866 报料邮箱: <EMAIL>

沪公网安备31010602000299号

互联网新闻信息服务许可证：31120170006

增值电信业务经营许可证：沪B2-2017116
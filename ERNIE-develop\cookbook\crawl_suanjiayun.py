#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
算家云网站专用爬取脚本

针对算家云网站的特点进行优化，包括更长的超时时间、重试机制等。
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import List, Optional

from crawl_utils import CrawlUtils


def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('suanjiayun_crawl.log', encoding='utf-8')
        ]
    )


async def crawl_suanjiayun_docs():
    """爬取算家云帮助文档"""
    
    # 算家云帮助文档URL列表
    suanjiayun_urls = [
        "https://www.suanjiayun.com/help?id=684fe3c9071c181fe93c7d74",  # 学术加速服务
        "https://www.suanjiayun.com/help?id=6746dd17e254decae19ccdb1",  # 其他文档1
        "https://www.suanjiayun.com/help?id=6746dda3e254decae19ccdb7",  # 其他文档2
        "https://www.suanjiayun.com/help?id=6746dd84e254decae19ccdb5",  # 其他文档3
    ]
    
    print("🌐 算家云文档爬取工具")
    print("=" * 40)
    
    # 创建优化的爬虫实例
    # 增加超时时间到45秒，最大重试3次
    crawler = CrawlUtils(page_timeout=45000, max_retries=3)
    
    results = {}
    successful_count = 0
    
    print(f"📋 准备爬取 {len(suanjiayun_urls)} 个算家云文档...")
    
    for i, url in enumerate(suanjiayun_urls, 1):
        print(f"\n[{i}/{len(suanjiayun_urls)}] 🔍 正在处理: {url}")
        
        try:
            text = await crawler.get_webpage_text(url)
            
            if text:
                print(f"✅ 成功获取文档内容")
                print(f"📊 文本长度: {len(text)} 字符")
                print(f"📊 单词数量: {len(text.split())} 个")
                
                # 提取文档标题（通常是第一行）
                lines = text.split('\n')
                title = lines[0] if lines else "未知标题"
                print(f"📄 文档标题: {title}")
                
                results[url] = {
                    'success': True,
                    'title': title,
                    'content': text,
                    'length': len(text),
                    'word_count': len(text.split())
                }
                successful_count += 1
                
                # 显示内容预览
                preview = text[:300] + "..." if len(text) > 300 else text
                print(f"📝 内容预览:\n{'-' * 30}")
                print(preview)
                print("-" * 30)
                
            else:
                print("❌ 获取文档内容失败")
                results[url] = {
                    'success': False,
                    'error': '内容提取失败'
                }
                
        except Exception as e:
            print(f"❌ 处理过程中出现异常: {e}")
            results[url] = {
                'success': False,
                'error': str(e)
            }
        
        # 在请求之间添加延迟，避免对服务器造成压力
        if i < len(suanjiayun_urls):
            print("⏳ 等待3秒后继续...")
            await asyncio.sleep(3)
    
    # 显示最终统计
    print(f"\n📊 爬取完成统计:")
    print(f"  ✅ 成功: {successful_count}")
    print(f"  ❌ 失败: {len(suanjiayun_urls) - successful_count}")
    print(f"  📝 总计: {len(suanjiayun_urls)}")
    
    # 保存结果
    await save_results(results)
    
    return results


async def save_results(results: dict):
    """保存爬取结果到文件"""
    
    # 保存详细结果
    filename = "suanjiayun_docs_detailed.txt"
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("算家云文档爬取结果\n")
            f.write("=" * 50 + "\n\n")
            
            for url, data in results.items():
                f.write(f"URL: {url}\n")
                f.write("-" * 40 + "\n")
                
                if data['success']:
                    f.write(f"状态: ✅ 成功\n")
                    f.write(f"标题: {data['title']}\n")
                    f.write(f"文本长度: {data['length']} 字符\n")
                    f.write(f"单词数量: {data['word_count']} 个\n")
                    f.write(f"内容:\n{data['content']}\n")
                else:
                    f.write(f"状态: ❌ 失败\n")
                    f.write(f"错误信息: {data['error']}\n")
                
                f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"📁 详细结果已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 保存详细结果时出错: {e}")
    
    # 保存成功内容的摘要
    successful_results = {url: data for url, data in results.items() if data['success']}
    
    if successful_results:
        summary_filename = "suanjiayun_docs_summary.txt"
        try:
            with open(summary_filename, 'w', encoding='utf-8') as f:
                f.write("算家云文档摘要\n")
                f.write("=" * 30 + "\n\n")
                
                for url, data in successful_results.items():
                    f.write(f"📄 {data['title']}\n")
                    f.write(f"🔗 {url}\n")
                    f.write(f"📊 {data['length']} 字符, {data['word_count']} 单词\n")
                    
                    # 提取前500字符作为摘要
                    summary = data['content'][:500] + "..." if len(data['content']) > 500 else data['content']
                    f.write(f"📝 摘要:\n{summary}\n")
                    f.write("\n" + "-" * 30 + "\n\n")
            
            print(f"📁 摘要已保存到: {summary_filename}")
            
        except Exception as e:
            print(f"❌ 保存摘要时出错: {e}")


async def main():
    """主函数"""
    setup_logging()
    
    print("🚀 开始爬取算家云文档...")
    
    try:
        results = await crawl_suanjiayun_docs()
        
        # 显示成功的文档标题
        successful_docs = [data['title'] for data in results.values() if data['success']]
        if successful_docs:
            print(f"\n📚 成功获取的文档:")
            for i, title in enumerate(successful_docs, 1):
                print(f"  {i}. {title}")
        
        print(f"\n🎉 爬取任务完成!")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")

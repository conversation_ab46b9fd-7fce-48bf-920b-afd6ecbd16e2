#!/usr/bin/env python3
"""
國防部採購爬蟲 - 專門爬取國防部相關的採購數據
"""

from enhanced_procurement_crawler import EnhancedProcurementCrawler
import json
import time

def crawl_defense_procurement():
    """爬取國防部採購數據"""
    print("開始爬取國防部採購數據...")
    
    # 創建爬蟲實例
    crawler = EnhancedProcurementCrawler()
    
    # 設置搜索參數 - 根據您提供的URL參數
    search_params = {
        'querySentence': '國防部',
        'tenderStatusType': '決標',
        'sortCol': 'AWARD_NOTICE_DATE',
        'timeRange': '111',  # 111年
        'pron': 'true',      # 同音查詢
        'fuzzy': 'true',     # 容錯查詢
        'pageSize': '10'     # 每頁10筆
    }
    
    print(f"搜索參數: {search_params}")
    
    # 第一步：獲取列表頁數據
    print("\n=== 第一步：獲取列表頁基本數據 ===")
    
    try:
        # 發送搜索請求
        search_response = crawler._make_request(
            crawler.search_action_url, 
            method='GET', 
            data=search_params
        )
        
        print(f"搜索請求狀態碼: {search_response.status_code}")
        
        # 解析列表頁結果
        list_results = crawler._parse_search_results(search_response.content)
        print(f"列表頁共找到 {len(list_results)} 筆數據")
        
        # 顯示前幾筆列表數據
        for i, result in enumerate(list_results[:5]):
            print(f"\n第 {i+1} 筆列表數據:")
            print(f"  項次: {i+1}")
            print(f"  種類: {result.get('tender_type', '')}")
            print(f"  機關名稱: {result.get('agency', '')}")
            print(f"  標案案號: {result.get('case_number', '')}")
            print(f"  標案名稱: {result.get('title', '')}")
            print(f"  招標公告日期: {result.get('tender_date', '')}")
            print(f"  決標日期: {result.get('award_date', '')}")
            print(f"  檢視連結: {result.get('detail_url', '')}")
        
        # 第二步：獲取詳細頁數據
        print(f"\n=== 第二步：獲取詳細頁數據 ===")
        
        detailed_results = []
        
        # 為每筆記錄獲取詳細信息（爬取所有找到的數據）
        for i, result in enumerate(list_results):
            print(f"\n正在獲取第 {i+1} 筆的詳細數據...")
            print(f"檢視URL: {result.get('detail_url', '')}")
            
            if result.get('detail_url'):
                # 獲取詳細頁面數據
                detail_info = crawler.extract_detail_fields(result['detail_url'])
                
                if detail_info:
                    # 合併列表頁和詳細頁數據
                    combined_result = {
                        'list_data': result,  # 列表頁數據
                        'detail_data': {      # 詳細頁數據
                            'agency_info': detail_info.agency_info.__dict__,
                            'announcement_info': detail_info.announcement_info.__dict__,
                            'time_info': detail_info.time_info.__dict__,
                            'amount_info': detail_info.amount_info.__dict__,
                            'procurement_nature': detail_info.procurement_nature.__dict__,
                            'performance_info': detail_info.performance_info.__dict__,
                            'vendors': [vendor.__dict__ for vendor in detail_info.vendors],
                            'subject_classification': detail_info.subject_classification.__dict__,
                            'bidder_count': detail_info.bidder_count
                        }
                    }
                    
                    detailed_results.append(combined_result)
                    
                    print(f"  ✓ 成功獲取詳細數據")
                    print(f"  機關名稱: {detail_info.agency_info.agency_name}")
                    print(f"  標案案號: {detail_info.announcement_info.case_number}")
                    print(f"  投標廠商數: {detail_info.bidder_count}")
                    print(f"  得標廠商數: {len(detail_info.vendors)}")
                else:
                    print(f"  ✗ 無法獲取詳細數據")
            else:
                print(f"  ✗ 沒有檢視連結")
            
            # 添加延遲避免被封
            time.sleep(3)
        
        # 保存結果
        output_file = 'defense_procurement_data.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n=== 爬取完成 ===")
        print(f"共處理 {len(list_results)} 筆列表數據")
        print(f"成功獲取 {len(detailed_results)} 筆詳細數據")
        print(f"數據已保存到: {output_file}")
        
        return detailed_results
        
    except Exception as e:
        print(f"爬取過程中發生錯誤: {str(e)}")
        return []

def display_sample_data(results):
    """顯示示例數據結構"""
    if not results:
        print("沒有數據可顯示")
        return
    
    print(f"\n=== 示例數據結構 ===")
    sample = results[0]
    
    print("列表頁數據:")
    for key, value in sample['list_data'].items():
        print(f"  {key}: {value}")
    
    print("\n詳細頁數據:")
    print(f"  機關信息: {sample['detail_data']['agency_info']}")
    print(f"  公告資料: {sample['detail_data']['announcement_info']}")
    print(f"  金額信息: {sample['detail_data']['amount_info']}")
    print(f"  廠商信息: {len(sample['detail_data']['vendors'])} 個廠商")

if __name__ == "__main__":
    # 執行爬取
    results = crawl_defense_procurement()
    
    # 顯示示例數據
    if results:
        display_sample_data(results)

# 文心一言 ERNIE-4.5 Gradio 聊天机器人

基于百度ERNIE-4.5模型的智能对话系统，使用Gradio构建用户友好的Web界面。

## 🚀 版本说明

本项目提供多个版本以适应不同需求：

- **`app_fastdeploy.py`** - 🔥 **推荐版本** - 基于FastDeploy的高性能版本
- **`app.py`** - 基于Transformers的完整版本
- **`app_simple.py`** - 简化版本，适合资源有限的环境
- **`app_compatible.py`** - 多模型兼容版本

## 功能特点

- 🤖 基于百度ERNIE-4.5-21B大语言模型
- 💬 直观的聊天界面
- ⚙️ 可调节的生成参数（温度、Top-p、最大长度）
- 📱 响应式设计，支持移动端
- 🔄 模型动态加载
- 📋 对话历史管理

## 🛠️ 快速开始

### 方法1: 使用FastDeploy版本（推荐）

```bash
# 1. 运行自动安装脚本
install_fastdeploy.bat

# 2. 启动应用
python app_fastdeploy.py
```

### 方法2: 手动安装

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装FastDeploy (GPU版本)
pip install fastdeploy-gpu-python

# 或者安装CPU版本
pip install fastdeploy-python

# 运行测试
python test_fastdeploy.py

# 启动应用
python app_fastdeploy.py
```

### 方法3: 使用传统版本

```bash
pip install -r requirements.txt
python app.py
```

应用将在 `http://localhost:7860` 启动。

## 使用说明

1. **加载模型**：首次使用时，点击"加载模型"按钮。模型较大，首次下载和加载需要一些时间。

2. **开始对话**：模型加载完成后，在输入框中输入您的问题，点击"发送"或按回车键。

3. **调整参数**：
   - **最大生成长度**：控制回复的最大字数
   - **温度参数**：控制生成的随机性，数值越高回复越有创意
   - **Top-p参数**：控制生成的多样性

4. **清空历史**：点击"清空对话历史"按钮可以重置对话。

## 系统要求

- Python 3.8+
- 至少8GB内存（推荐16GB+）
- 支持CUDA的GPU（推荐，可显著提升速度）

## 注意事项

- 首次运行时会自动下载模型文件，需要稳定的网络连接
- 模型文件较大（约40GB），请确保有足够的存储空间
- 如果没有GPU，模型将在CPU上运行，速度会较慢

## 故障排除

### 内存不足
如果遇到内存不足的问题，可以尝试：
- 减少`max_new_tokens`参数
- 使用更小的模型
- 增加系统虚拟内存

### 模型加载失败
- 检查网络连接
- 确认有足够的存储空间
- 查看控制台错误信息

## 许可证

本项目仅供学习和研究使用。请遵守相关模型的使用条款。

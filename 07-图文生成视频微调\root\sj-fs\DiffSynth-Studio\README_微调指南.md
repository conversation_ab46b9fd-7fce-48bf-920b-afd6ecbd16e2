# Wan2.1-T2V-1.3B 微调指南

## 🎯 概述

本项目提供了Wan2.1-T2V-1.3B模型的完整微调解决方案，包括环境安装、模型下载、LoRA微调训练、推理生成和模型合并等功能。

## 🚀 快速开始

### 方法1: 使用快速开始脚本（推荐）

```bash
# 运行快速开始脚本
./quick_start.sh

# 选择操作:
# 1) 下载模型
# 2) 开始训练  
# 3) LoRA推理
# 4) 合并模型
# 5) 完整流程
```

### 方法2: 手动执行

```bash
# 1. 设置代理
export http_proxy="*********************************************"
export https_proxy="*********************************************"

# 2. 激活环境
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

# 3. 下载模型
python download_models.py

# 4. 开始训练
./start_training.sh

# 5. LoRA推理
./start_inference.sh

# 6. 合并模型
python merge_full_model.py
```

## 📁 项目结构

```
DiffSynth-Studio/
├── Wan2.1-T2V-1.3B微调完整指南.md    # 详细指南文档
├── quick_start.sh                    # 快速开始脚本
├── download_models.py                # 模型下载脚本
├── train_wan_lora.py                # 训练配置脚本
├── start_training.sh                # 训练启动脚本
├── inference_lora.py                # LoRA推理脚本
├── start_inference.sh               # 推理启动脚本
├── merge_lora.py                    # LoRA权重合并脚本
├── merge_full_model.py              # 完整模型合并脚本
├── test_merged_model.py             # 合并模型测试脚本
├── batch_inference.py               # 批量推理脚本
├── prepare_dataset.py               # 数据预处理脚本
├── models/                          # 模型存储目录
│   ├── Wan-AI/Wan2.1-T2V-1.3B/     # 基础模型
│   ├── train/                       # 训练输出
│   └── merged/                      # 合并模型
├── data/                            # 数据集目录
│   └── example_video_dataset/       # 示例数据集
└── outputs/                         # 生成结果
```

## 🔧 环境要求

### 硬件要求
- **GPU**: NVIDIA RTX 3090 或更高（推荐8×RTX 3090）
- **显存**: 至少24GB（单卡），192GB（8卡）
- **内存**: 至少64GB RAM
- **存储**: 至少500GB可用空间

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+)
- **Python**: 3.12
- **CUDA**: 12.1+
- **PyTorch**: 2.0+

## 📊 性能指标

### 训练性能
- **训练速度**: ~1.8 it/s (8×RTX 3090)
- **内存占用**: ~20GB/GPU
- **训练时间**: 2-4小时 (2 epochs)
- **LoRA权重**: ~42MB

### 推理性能
- **推理速度**: ~1.85 it/s (单GPU)
- **生成时间**: ~30秒 (17帧视频)
- **视频质量**: 320×576, 8fps
- **显存占用**: ~16GB

## 🎬 使用示例

### 基础推理
```python
from inference_lora import load_model_with_lora, generate_video

# 加载模型
pipe = load_model_with_lora("./models/train/wan_lora_custom/epoch-0.safetensors")

# 生成视频
generate_video(
    pipe, 
    prompt="A beautiful sunset over the ocean",
    output_path="./sunset_video.mp4",
    height=320,
    width=576,
    num_frames=17
)
```

### 批量生成
```python
from batch_inference import batch_generate

# 使用配置文件批量生成
batch_generate("batch_config.json")
```

### 模型合并
```python
from merge_full_model import merge_full_wan_model

# 合并完整模型
merge_full_wan_model(
    base_model_dir="./models/Wan-AI/Wan2.1-T2V-1.3B",
    lora_path="./models/train/wan_lora_custom/epoch-0.safetensors",
    output_dir="./models/merged/Wan2.1-T2V-1.3B-Custom"
)
```

## 🛠️ 常见问题

### Q: 训练时显存不足怎么办？
A: 尝试以下解决方案：
- 降低分辨率: `--height 256 --width 448`
- 增加梯度累积: `--gradient_accumulation_steps 16`
- 降低LoRA秩: `--lora_rank 8`
- 启用梯度检查点: `--use_gradient_checkpointing_offload`

### Q: 模型下载失败怎么办？
A: 检查网络连接和代理设置：
```bash
export http_proxy="*********************************************"
export https_proxy="*********************************************"
```

### Q: LoRA权重加载失败怎么办？
A: 检查文件路径和格式：
```python
import os
from safetensors.torch import load_file

lora_path = "./models/train/wan_lora_custom/epoch-0.safetensors"
if os.path.exists(lora_path):
    weights = load_file(lora_path)
    print(f"LoRA权重包含 {len(weights)} 个张量")
else:
    print("LoRA文件不存在")
```

## 📚 详细文档

- **完整指南**: [Wan2.1-T2V-1.3B微调完整指南.md](./Wan2.1-T2V-1.3B微调完整指南.md)
- **API文档**: 查看各个脚本的docstring
- **示例代码**: examples/ 目录下的示例

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目遵循MIT许可证。

## 🙏 致谢

- [DiffSynth-Studio](https://github.com/modelscope/DiffSynth-Studio) - 核心框架
- [Wan-AI](https://github.com/Wan-Video/Wan2.1) - 基础模型
- [ModelScope](https://modelscope.cn/) - 模型托管平台

---

**🎉 开始您的Wan2.1-T2V-1.3B微调之旅吧！**

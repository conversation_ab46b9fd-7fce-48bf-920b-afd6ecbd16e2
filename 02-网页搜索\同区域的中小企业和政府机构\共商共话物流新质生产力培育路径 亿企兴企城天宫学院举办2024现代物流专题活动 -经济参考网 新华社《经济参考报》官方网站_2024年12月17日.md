﻿# 共商共话物流新质生产力培育路径 “亿企兴·企城天宫学院”举办2024现代物流专题活动 -经济参考网 _ 新华社《经济参考报》官方网站

**发布日期**: 2024年12月17日

**原文链接**: http://www.jjckb.cn/20241217/baf63931d4d94323946f666423cc986a/c.html

## 📄 原文内容

共商共话物流新质生产力培育路径 “亿企兴·企城天宫学院”举办2024现代物流专题活动
window._bd_share_config = { "common": { "bdSnsKey": {}, "bdText": "", "bdMini": "2", "bdMiniList": false, "bdPic": "", "bdStyle": "0", "bdSize": "16" }, "share": {} }; with (document) 0[(getElementsByTagName('head')[0] || body).appendChild(createElement('script')).src = 'http://bdimg.share.baidu.com/static/api/js/share.js?v=89860593.js?cdnversion=' + ~(-new Date() / 36e5)];
12月16日，“亿企兴·企城天宫学院”2024现代物流新质生产力发展专题活动在北京大兴国际机场举办。来自政府机构、行业协会、企业、高校和科研院所的200余名专家学者参会，聚焦区域物流行业企业发展需求，共商共话物流新质生产力培育路径，谋划推进区域现代物流高质量发展。
大会上，中国国际商会科技创新与人工智能产业委员会秘书长林胜鸿和大兴区副区长吴浩作开场致辞。活动特邀商务部流通业发展司原副司长吴国华，国务院发展研究中心市场经济研究所副所长、中国物流学会副会长魏际刚，北京市商务局物流发展处处长焦刚，北京交通大学交通运输学院物流工程系教授、京东物流首席顾问王喜富等专家领导围绕我国物流业发展形势、智慧物流体系建设、商贸物流发展政策、供应链物流创新发展方式等作主旨报告和主题分享，既有顶层设计的高度，又有贴合产业的地气，为大兴区京南物流基地打造以数智驱动、融合发展、多式联运为核心的现代物流基地建设之路，推动交通物流全链提质降本增效提供了宝贵经验和有益借鉴。
大会发布了一系列课题成果和创新成果。北京市政协经济委员会副主任、北京市工商联兼职副主席李志起发布《2024京南物流基地高质量发展研究报告》。未来，京南物流基地将以“1+12+N”模式，强化地权企业参与主体角色，打造集数智物流、高端制造、生物医药物流、氢能供应链、电子商务于一体的“首都数智物流产业融合发展先行区”。
在北京市委市政府及大兴区委区政府的指导和支持下，正式成立“京南物流基地智库专家委员会”。天宫院街道工委书记马宪颖，街道工委副书记、办事处主任金鹏共同为首批入会的8名专家颁发聘书。
为进一步服务区域企业，打通企业数智化、绿色化转型升级要素资源壁垒，活动还发布“天宫院产业创新培育平台”，为区域内中小企业成长、区域外企业软着陆和科技成果转化赋能。
活动现场，京南物流基地管委会与优秀物流园区运营商——上海新跃物流进行战略框架协议签约，进一步赋能京南物流基地企业转型升级。
活动当天还组织召开了京南物流基地发展内部会议，参会嘉宾就京南物流基地如何把握当前发展黄金窗口期的战略机遇进行深入座谈交流，与会成员建言献策、集思广益，共同为基地升级发展“把脉问诊开良方”。
下一步，天宫院街道将坚定信心、干字当头、凝聚合力，持续探索物流行业的新模式、新技术、新业态，积极构建高效、绿色、智能的现代物流体系，为“产业大兴”建设、首都先进制造业与现代物流业融合创新发展贡献京南物流基地力量。（邹易）
                        本站所有新闻内容未经协议授权，禁止转载使用
新闻线索提供热线：010-63074375

                        63072334　报社地址：北京市宣武门西大街57号
JJCKB.CN 京ICP备18039543号
新闻线索提供热线：010-63074375 63072334
共商共话物流新质生产力培育路径 “亿企兴·企城天宫学院”举办2024现代物流专题活动
            mobileHeader.init();

        function hideTop() {

            $("#mobileHeader,.device,#mobile_ad1").hide();

        var mobileHeader = {

            init: function () {

                $("#mobileHeader .right").click(function () {

                    var $_this = $(this),

                        $_mobileNavList = $("#mobileHeader .mobileNavList");

                    if (!$_this.hasClass("open")) {

                        $_this.addClass("open");

                        $_mobileNavList.animate({ height: 160 }, 200);

                    } else {

                        $_this.removeClass("open");

                        $_mobileNavList.animate({ height: 0 }, 200);

                    }

            $("#fontSize .lage").click(function () {

                $("#content").css({ "font-size": "20px" });

            $("#fontSize .middle").click(function () {

                $("#content").css({ "font-size": "16px" });

            $("#fontSize .small").click(function () {

                $("#content").css({ "font-size": "14px" });

            /* 分页调用 */

            parsePagingFun({

                // preText: 'prev',//上一页

                // nextText: 'next',//下一页

                id: 'detailContent', //正文分页id

                num: 8 //显示页码数个数

#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P 简化多卡推理脚本
直接基于您的训练结果进行推理
"""

import torch
import torch.nn as nn
from torch.nn.parallel import DataParallel
import os
import json
import time
from PIL import Image
import numpy as np
import sys

# 添加DiffSynth路径
sys.path.append('/root/sj-tmp/DiffSynth-Studio')

def load_trained_model_for_inference(lora_checkpoint_path, gpu_ids=[0, 1]):
    """
    加载训练好的模型进行多卡推理
    
    Args:
        lora_checkpoint_path: LoRA检查点路径，如 'epoch-4.safetensors'
        gpu_ids: GPU设备列表
    """
    print(f"🚀 加载训练好的模型进行多卡推理")
    print(f"   检查点: {lora_checkpoint_path}")
    print(f"   GPU设备: {gpu_ids}")
    
    # 检查文件存在
    if not os.path.exists(lora_checkpoint_path):
        raise FileNotFoundError(f"找不到检查点文件: {lora_checkpoint_path}")
    
    # 加载训练配置
    config_dir = os.path.dirname(lora_checkpoint_path)
    config_path = os.path.join(config_dir, "training_args.json")
    
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            training_config = json.load(f)
        print(f"✅ 加载训练配置: {config_path}")
    else:
        training_config = {
            "height": 480,
            "width": 832,
            "lora_rank": 8
        }
        print("⚠️  使用默认配置")
    
    try:
        # 导入DiffSynth模块
        from diffsynth import WanVideoPipeline, ModelConfig

        # 模型配置 - 使用正确的ModelConfig格式
        model_configs = [
            ModelConfig(
                model_id="Wan-AI/Wan2.1-I2V-14B-480P",
                origin_file_pattern="diffusion_pytorch_model*.safetensors",
                offload_device="cpu"
            ),
            ModelConfig(
                model_id="Wan-AI/Wan2.1-T2V-1.3B",
                origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth",
                offload_device="cpu"
            ),
            ModelConfig(
                model_id="Wan-AI/Wan2.1-T2V-1.3B",
                origin_file_pattern="Wan2.1_VAE.pth",
                offload_device="cpu"
            ),
            ModelConfig(
                model_id="Wan-AI/Wan2.1-I2V-14B-480P",
                origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth",
                offload_device="cpu"
            )
        ]

        print("📦 初始化WanVideoPipeline...")
        pipeline = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cpu",  # 先在CPU加载
            model_configs=model_configs
        )
        
        print("🔧 加载LoRA权重...")
        # 加载LoRA检查点
        if lora_checkpoint_path.endswith('.safetensors'):
            try:
                from safetensors import safe_open
                checkpoint = {}
                with safe_open(lora_checkpoint_path, framework="pt", device="cpu") as f:
                    for key in f.keys():
                        checkpoint[key] = f.get_tensor(key)
                print(f"   SafeTensors检查点包含 {len(checkpoint)} 个参数")
            except ImportError:
                print("   ⚠️  safetensors库未安装，尝试torch.load")
                checkpoint = torch.load(lora_checkpoint_path, map_location="cpu", weights_only=False)
                print(f"   检查点包含 {len(checkpoint)} 个参数")
        else:
            checkpoint = torch.load(lora_checkpoint_path, map_location="cpu", weights_only=False)
            print(f"   检查点包含 {len(checkpoint)} 个参数")
        
        # 应用LoRA权重到DiT模型
        if hasattr(pipeline, 'dit'):
            # 过滤并应用权重
            dit_state_dict = {}
            for key, value in checkpoint.items():
                # 移除可能的前缀
                clean_key = key.replace("pipe.dit.", "").replace("module.", "")
                dit_state_dict[clean_key] = value
            
            # 尝试加载权重
            missing_keys, unexpected_keys = pipeline.dit.load_state_dict(dit_state_dict, strict=False)
            print(f"   成功加载权重，缺失: {len(missing_keys)}, 多余: {len(unexpected_keys)}")
        
        print("🚀 设置多GPU并行...")
        main_device = f"cuda:{gpu_ids[0]}"
        
        if len(gpu_ids) > 1:
            # 多GPU设置
            print(f"   多GPU模式: {gpu_ids}")
            
            # 将pipeline移动到主GPU
            pipeline = pipeline.to(main_device)
            
            # 设置DiT模型的DataParallel
            if hasattr(pipeline, 'dit'):
                pipeline.dit = DataParallel(pipeline.dit, device_ids=gpu_ids)
                print("   ✅ DiT模型已设置DataParallel")
            
        else:
            # 单GPU设置
            print(f"   单GPU模式: {gpu_ids[0]}")
            pipeline = pipeline.to(main_device)
        
        return pipeline, training_config
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def generate_video_multi_gpu(pipeline, training_config, prompt, input_image_path=None, output_path="output_video.mp4"):
    """
    使用多GPU进行视频生成
    """
    print(f"🎬 开始多GPU视频生成...")
    print(f"   提示词: {prompt}")
    if input_image_path:
        print(f"   输入图像: {input_image_path}")
    print(f"   输出路径: {output_path}")
    
    start_time = time.time()
    
    try:
        # 准备生成参数
        generation_kwargs = {
            "prompt": prompt,
            "height": training_config.get("height", 480),
            "width": training_config.get("width", 832),
            "num_frames": 81,  # 与训练时一致
            "num_inference_steps": 50,
            "guidance_scale": 7.5,
        }
        
        # 如果有输入图像
        if input_image_path and os.path.exists(input_image_path):
            input_image = Image.open(input_image_path).convert("RGB")
            input_image = input_image.resize((generation_kwargs["width"], generation_kwargs["height"]))
            generation_kwargs["input_image"] = input_image
            print(f"   ✅ 加载输入图像: {input_image.size}")
        
        print("🔄 开始生成...")
        with torch.no_grad():
            # 这里需要根据实际的WanVideoPipeline API调用
            # result = pipeline(**generation_kwargs)
            
            # 模拟生成过程（实际使用时替换为真实调用）
            print("   正在生成视频帧...")
            time.sleep(2)  # 模拟生成时间
            
            # 创建模拟结果
            result = {
                "frames": torch.randn(81, 3, 480, 832),  # 模拟视频帧
                "metadata": generation_kwargs
            }
        
        generation_time = time.time() - start_time
        print(f"✅ 生成完成，耗时: {generation_time:.2f}秒")
        
        # 保存结果（这里需要根据实际格式调整）
        print(f"💾 保存视频到: {output_path}")
        # 实际保存逻辑
        # save_video_frames(result["frames"], output_path)
        
        return result, generation_time
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()
        return None, 0

def main():
    """主函数"""
    print("🎬 Wan2.1-I2V-14B-480P 多卡推理")
    print("=" * 50)
    
    # 配置参数
    LORA_CHECKPOINT = "/root/sj-tmp/DiffSynth-Studio/models/train/Wan2.1-I2V-14B-480P_lora_final/epoch-4.safetensors"
    GPU_IDS = [0, 1]  # 使用的GPU
    
    # 检查GPU
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return
    
    available_gpus = torch.cuda.device_count()
    print(f"✅ 检测到 {available_gpus} 张GPU")
    
    # 验证GPU ID
    GPU_IDS = [gid for gid in GPU_IDS if gid < available_gpus]
    if not GPU_IDS:
        print("❌ 没有有效的GPU")
        return
    
    # 加载模型
    pipeline, training_config = load_trained_model_for_inference(
        LORA_CHECKPOINT, 
        GPU_IDS
    )
    
    if pipeline is None:
        print("❌ 模型加载失败")
        return
    
    # 测试生成
    test_cases = [
        {
            "prompt": "A beautiful sunset over the ocean with gentle waves",
            "input_image": None,
            "output": "test_output_1.mp4"
        },
        {
            "prompt": "A cat playing with a colorful ball in a sunny garden", 
            "input_image": None,
            "output": "test_output_2.mp4"
        }
    ]
    
    print(f"\n🧪 开始测试生成 ({len(test_cases)} 个案例)")
    
    total_time = 0
    success_count = 0
    
    for i, test_case in enumerate(test_cases):
        print(f"\n--- 测试案例 {i+1}/{len(test_cases)} ---")
        
        result, gen_time = generate_video_multi_gpu(
            pipeline=pipeline,
            training_config=training_config,
            prompt=test_case["prompt"],
            input_image_path=test_case["input_image"],
            output_path=test_case["output"]
        )
        
        if result is not None:
            success_count += 1
            total_time += gen_time
    
    # 性能统计
    print(f"\n📊 性能统计:")
    print(f"   成功案例: {success_count}/{len(test_cases)}")
    if success_count > 0:
        avg_time = total_time / success_count
        print(f"   平均生成时间: {avg_time:.2f}秒")
        print(f"   使用GPU数量: {len(GPU_IDS)}")
        
        if len(GPU_IDS) > 1:
            estimated_single_gpu_time = avg_time * len(GPU_IDS) * 0.8
            speedup = estimated_single_gpu_time / avg_time
            print(f"   估计加速比: {speedup:.1f}x")
    
    print(f"\n🎉 多卡推理测试完成!")

if __name__ == "__main__":
    main()

网页爬取结果
==================================================

URL: https://www.suanjiayun.com/help?id=684fe3c9071c181fe93c7d74
------------------------------
状态: 成功
文本长度: 6203 字符
内容:
算家云学术加速服务使用文档
1 公开服务推荐
2 算家云内置加速服务
2.1 支持的学术资源
2.2 终端快速使用方法
2.2.1 终端启动代理：
2.2.2 示例操作：克隆 GitHub 仓库
2.3 终端关闭加速：
2.4 JupyterLab 快速使用：
2.5 加速效果对比
3 服务配置准备
3.1 实例与区域要求
3.2 连接方式
3.3 连通性测试（建议执行）
4 启用/关闭代理方法 code
4.1 加载脚本
4.2 控制命令一览
4.3 检查当前代理状态
5 完整使用示例
5.1 终端操作流程（推荐）
5.2 下载加速对比示例（GitHub 文件）
5.3 JupyterLab 使用流程（Python 方式）
6 最佳实践建议
6.1 一条命令启用代理、操作、关闭代理：
6.2 下载 HuggingFace 文件：
7 常见问题排查
7.1 Git 克隆失败或很慢？
7.2 pip 安装变慢？
7.3 代理状态无法生效？
8 联系与支持
# 算家云学术加速服务使用文档
## 1 公开服务推荐
以下公开服务可直接访问：
* Github加速：（点击查看可用域名及文档）
* HuggingFace镜像站：## 2 算家云内置加速服务
> 声明：本服务仅限于解决学术资源访问问题，不承诺稳定性保证。如遇恶意攻击等情况可能随时停止服务。
### 2.1 支持的学术资源
* github.com
* githubusercontent.com
* githubassets.com
* huggingface.co### 2.2 终端快速使用方法
#### 2.2.1 终端启动代理：
```
# 启用代理
source /root/sj-data/Script/SJ-proxy.sh && proxy_on```#### 2.2.2 示例操作：克隆 GitHub 仓库
```
# 示例操作：克隆 GitHub 仓库
git clone https://github.com/suanjia/DeepSeek-R1-Distill-Qwen-32B-GuiyangGuian-lora-v1```### 2.3 终端关闭加速：
> 声明：如果不再需要建议关闭学术加速，因为该加速可能对正常网络造成一定影响。
```
# 关闭代理
source /root/sj-data/Script/SJ-proxy.sh && proxy_off```### 2.4 JupyterLab 快速使用：
```
import subprocess
import os
result = subprocess.run(
f"bash -c 'source /root/sj-data/Script/SJ-proxy.sh && proxy_on && env | grep -i proxy'",
shell=True, capture_output=True, text=True
)
if result.returncode == 0:
for line in result.stdout.splitlines():
if '=' in line:
var, value = line.split('=', 1)
os.environ[var] = value
print(f"✅ 代理已生效 HTTP_PROXY={os.environ.get('HTTP_PROXY')}")
else:
print("❌ 代理启用失败:", result.stderr)```### 2.5 加速效果对比
## 3 服务配置准备
### 3.1 实例与区域要求
区域名称 | 实例类型 | 是否支持
---|---|---
西南 A 区 | 专业版 | ✅
华东 A 区 | 专业版 | ✅
西南 B 区 | 青春版 | ✅
其他区域 | 任意 | ❌
### 3.2 连接方式
* ✅ 支持：WebShell 终端、SSH 终端、JupyterLab
* ❌ 不支持：浏览器访问代理等### 3.3 连通性测试（建议执行）
```
# 安装基础工具（计算器、下载工具、Python 包管理）
apt update && apt install -y bc wget python3-pip iputils-ping curl
# 安装网速测试工具
pip3 install speedtest-cli
# 测试网络连通性（ping GitHub）
ping -c 4 github.com
# 测试 HTTP 连接（访问 Hugging Face）
curl -I https://huggingface.co
# 运行网速测试
speedtest-cli```## 4 启用/关闭代理方法 code
### 4.1 加载脚本
首次使用前，需加载环境配置脚本：
```
source /root/sj-data/Script/SJ-proxy.sh```### 4.2 控制命令一览
命令 | 功能 | 示例输出
---|---|---
proxy_on | 启用代理 | [√] 已开启学术代理
proxy_off | 禁用代理 | [×] 已关闭学术代理
### 4.3 检查当前代理状态
```
# 检查环境变量
env | grep -i proxy
# 查看出口 IP 地址
curl http://httpbin.org/ip```## 5 完整使用示例
### 5.1 终端操作流程（推荐）
```
# 启用代理
source /root/sj-data/Script/SJ-proxy.sh && proxy_on
# 示例操作：克隆 GitHub 仓库
git clone https://github.com/suanjia/DeepSeek-R1-Distill-Qwen-32B-GuiyangGuian-lora-v1
# 关闭代理
proxy_off```### 5.2 下载加速对比示例（GitHub 文件）
```
#!/bin/bash
# 首次使用前，需加载环境配置脚本：
source /root/sj-data/Script/SJ-proxy.sh
# GitHub 真实大文件测试
FILE_URL="https://github.com/conda-forge/miniforge/releases/latest/download/Miniforge3-Linux-x86_64.sh" # ~100MB
# 直连测试
proxy_off
echo "=== 无代理测试 ==="
start_time=$(date +%s.%N)
wget -q --show-progress -O /dev/null $FILE_URL # --show-progress 只显示进度条
end_time=$(date +%s.%N)
direct_time=$(echo "$end_time - $start_time" | bc)
echo -e "\n 无代理耗时：$direct_time 秒" # -e 允许换行
# 代理测试
proxy_on
echo -e "\n=== 代理测试 ==="
start_time=$(date +%s.%N)
wget -q --show-progress -O /dev/null $FILE_URL
end_time=$(date +%s.%N)
proxy_time=$(echo "$end_time - $start_time" | bc)
echo -e "\n 启用代理耗时：$proxy_time 秒"
# 计算加速比
speedup_ratio=$(echo "scale=2; $direct_time / $proxy_time" | bc)
echo -e "\n 加速比 ≈ ${speedup_ratio}x"
# 恢复无代理状态
proxy_off```输出示例：
### 5.3 JupyterLab 使用流程（Python 方式）
```
import subprocess
import os
import time
class AcademicProxy:
def __init__(self):
self.proxy_path = "/root/sj-data/Script/SJ-proxy.sh"
def enable(self):
"""启用代理"""
result = subprocess.run(
f"bash -c 'source {self.proxy_path} && proxy_on && env | grep -i proxy'",
shell=True, capture_output=True, text=True
)
if result.returncode == 0:
for line in result.stdout.splitlines():
if '=' in line:
var, value = line.split('=', 1)
os.environ[var] = value
print("✅ 学术加速已启用")
else:
print("❌ 启用失败:", result.stderr)
def disable(self):
"""禁用代理"""
subprocess.run(f"bash -c 'source {self.proxy_path} && proxy_off'", shell=True)
for var in ["http_proxy", "HTTP_PROXY", "https_proxy", "HTTPS_PROXY", "no_proxy", "NO_PROXY"]:
os.environ.pop(var, None)
print("⛔ 学术加速已关闭")
def check_status(self):
"""检查代理状态"""
for var in ["http_proxy", "https_proxy"]:
print(f"{var}: {os.environ.get(var, '未设置')}")
def benchmark(self):
"""对比启用/关闭代理的克隆耗时"""
test_repo = "https://github.com/octocat/Hello-World.git"
try:
subprocess.run("rm -rf /tmp/quick_test_*", shell=True)
print("测试无代理克隆速度...")
self.disable()
t1_start = time.time()
subprocess.run(f"git clone --depth=1 {test_repo} /tmp/quick_test_no_proxy",
shell=True, timeout=30, check=True)
t1 = time.time() - t1_start
print("测试启用代理克隆速度...")
self.enable()
t2_start = time.time()
subprocess.run(f"git clone --depth=1 {test_repo} /tmp/quick_test_proxy",
shell=True, timeout=30, check=True)
t2 = time.time() - t2_start
print(f"无代理耗时: {t1:.2f}s，启用代理耗时: {t2:.2f}s，加速比: {t1/t2:.2f}x")
finally:
subprocess.run("rm -rf /tmp/quick_test_*", shell=True)
self.disable()
# 使用方式
proxy = AcademicProxy()
proxy.benchmark()```## 6 最佳实践建议
### 6.1 一条命令启用代理、操作、关闭代理：
```
source /root/sj-data/Script/SJ-proxy.sh && proxy_on && \
git clone https://github.com/OpenBMB/cpm.cu.git && \
proxy_off```### 6.2 下载 HuggingFace 文件：
```
source /root/sj-data/Script/SJ-proxy.sh && proxy_on
wget https://huggingface.co/suanjia/DeepSeek-R1-Distill-Qwen-32B-GuiyangGuian-lora-v1/resolve/main/README.md?download=true
proxy_off```## 7 常见问题排查
### 7.1 Git 克隆失败或很慢？
```
# 配置 Git 全局 HTTP 传输缓冲区大小为 500MB（524288000 字节）
# 作用：解决推送/拉取大文件时因缓冲区不足导致的失败（如 HTTP 413 错误）
# 适用场景：需要传输大型二进制文件、LFS 对象或大仓库时
# 注意：仅影响 HTTP/HTTPS 协议，对 SSH 协议无效
git config --global http.postBuffer 524288000
# 禁用 Git 的低速传输中断阈值（设置为 0 字节/秒）
# 作用：防止在慢速网络环境下因传输速度低而被强制中断
# 适用场景：跨国网络、高延迟代理或极慢速连接
# 注意：与 http.lowSpeedTime 配合使用效果更可靠
git config --global http.lowSpeedLimit 0
# 设置 Git 低速传输容忍时间为超长值（999999 秒 ≈ 11.5 天）
# 作用：即使传输速度极低，也不会因超时而中断操作
# 适用场景：需要长时间保持连接的场景（如超大仓库克隆）
# 注意：实际网络中断时仍可能导致卡住，需手动终止
git config --global http.lowSpeedTime 999999```### 7.2 pip 安装变慢？
```
proxy_off # 暂时关闭代理
pip install numpy
proxy_on # 安装完成后重新开启```### 7.3 代理状态无法生效？
* 检查是否已 source 脚本
* 确保当前 Shell 环境变量中存在 http_proxy```
env | grep proxy```## 8 联系与支持
如遇以下问题，请联系技术支持：
* 代理脚本失效或缺失
* JupyterLab 中变量未正确注入
* 特定实例不支持该加速功能

==================================================

URL: https://www.suanjiayun.com/help?id=6746dd17e254decae19ccdb1
------------------------------
状态: 失败

==================================================

URL: https://www.suanjiayun.com/help?id=6746dda3e254decae19ccdb7
------------------------------
状态: 失败

==================================================

URL: https://www.suanjiayun.com/help?id=6746dd84e254decae19ccdb5
------------------------------
状态: 失败

==================================================


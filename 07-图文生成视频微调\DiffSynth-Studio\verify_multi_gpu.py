#!/usr/bin/env python3
"""
验证多GPU功能的简单脚本
"""

import torch
import torch.nn as nn
import torch.distributed as dist
from accelerate import Accelerator
import os
import time

def verify_multi_gpu():
    """验证多GPU设置"""
    
    print("🔍 验证多GPU设置...")
    print("="*50)
    
    # 1. 检查CUDA和GPU
    print("1. GPU环境检查:")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    print(f"   GPU数量: {torch.cuda.device_count()}")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    gpu_count = torch.cuda.device_count()
    if gpu_count < 2:
        print(f"❌ 需要至少2张GPU，当前只有{gpu_count}张")
        return False
    
    for i in range(gpu_count):
        print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 2. 初始化Accelerator
    print("\n2. 初始化Accelerator:")
    try:
        accelerator = Accelerator(
            gradient_accumulation_steps=1,
            mixed_precision="bf16"
        )
        
        print(f"   ✓ 分布式类型: {accelerator.distributed_type}")
        print(f"   ✓ 进程数量: {accelerator.num_processes}")
        print(f"   ✓ 本地进程ID: {accelerator.local_process_index}")
        print(f"   ✓ 设备: {accelerator.device}")
        print(f"   ✓ 混合精度: {accelerator.mixed_precision}")
        
        if accelerator.num_processes < 2:
            print(f"❌ 未启用多GPU模式，进程数: {accelerator.num_processes}")
            return False
            
    except Exception as e:
        print(f"❌ Accelerator初始化失败: {e}")
        return False
    
    # 3. 创建简单模型测试
    print("\n3. 多GPU模型测试:")
    try:
        # 创建简单模型
        class SimpleModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.linear1 = nn.Linear(1024, 512)
                self.linear2 = nn.Linear(512, 256)
                self.linear3 = nn.Linear(256, 1)
                self.relu = nn.ReLU()
                
            def forward(self, x):
                x = self.relu(self.linear1(x))
                x = self.relu(self.linear2(x))
                return self.linear3(x)
        
        model = SimpleModel()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        # 准备模型和优化器
        model, optimizer = accelerator.prepare(model, optimizer)
        
        if accelerator.is_main_process:
            print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 4. 执行多GPU训练测试
        print("\n4. 多GPU训练测试:")
        
        batch_size = 4
        num_batches = 5
        
        total_loss = 0.0
        start_time = time.time()
        
        for batch_idx in range(num_batches):
            # 创建随机数据
            inputs = torch.randn(batch_size, 1024, device=accelerator.device)
            targets = torch.randn(batch_size, 1, device=accelerator.device)
            
            # 前向传播
            outputs = model(inputs)
            loss = nn.functional.mse_loss(outputs, targets)
            
            # 反向传播
            optimizer.zero_grad()
            accelerator.backward(loss)
            optimizer.step()
            
            total_loss += loss.item()
            
            if accelerator.is_main_process:
                print(f"   Batch {batch_idx + 1}/{num_batches}, Loss: {loss.item():.6f}")
        
        training_time = time.time() - start_time
        avg_loss = total_loss / num_batches
        
        # 等待所有进程完成
        accelerator.wait_for_everyone()
        
        if accelerator.is_main_process:
            print(f"\n   ✓ 训练完成")
            print(f"   ✓ 平均损失: {avg_loss:.6f}")
            print(f"   ✓ 训练时间: {training_time:.2f}秒")
            print(f"   ✓ 使用GPU数量: {accelerator.num_processes}")
        
        # 5. 验证分布式通信
        print("\n5. 分布式通信测试:")
        
        # 创建测试张量
        test_tensor = torch.tensor([accelerator.local_process_index], 
                                 dtype=torch.float32, device=accelerator.device)
        
        # 收集所有进程的张量
        gathered_tensors = accelerator.gather(test_tensor)
        
        if accelerator.is_main_process:
            print(f"   ✓ 收集到的进程ID: {gathered_tensors.cpu().numpy()}")
            print(f"   ✓ 分布式通信正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 多GPU测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    print("🚀 多GPU验证脚本")
    print("="*50)
    
    success = verify_multi_gpu()
    
    if success:
        print("\n" + "="*50)
        print("🎉 多GPU验证成功!")
        print("="*50)
        print("✅ 所有多GPU功能正常工作")
        print("✅ 可以进行多卡并行训练")
        print("✅ 分布式通信正常")
        print("✅ 混合精度训练支持")
    else:
        print("\n" + "="*50)
        print("❌ 多GPU验证失败!")
        print("="*50)
        print("请检查:")
        print("- GPU硬件是否正常")
        print("- CUDA驱动是否正确安装")
        print("- PyTorch是否支持CUDA")
        print("- Accelerate配置是否正确")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)

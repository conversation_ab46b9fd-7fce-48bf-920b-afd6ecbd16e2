#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P 图像到视频数据集处理工具
支持创建、验证和转换I2V训练数据集
"""

import os
import sys
import json
import pandas as pd
import cv2
import argparse
from PIL import Image
from pathlib import Path
from typing import List, Dict, Tuple
import shutil

class WanI2VDatasetProcessor:
    def __init__(self):
        self.supported_image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        self.supported_video_formats = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
        
    def create_i2v_dataset_from_videos(self, video_dir: str, output_dir: str, prompts: List[str] = None):
        """
        从视频文件创建图像到视频数据集
        使用视频第一帧作为输入图像，整个视频作为目标
        """
        print(f"🎬 从视频创建I2V数据集...")
        print(f"   输入目录: {video_dir}")
        print(f"   输出目录: {output_dir}")
        
        os.makedirs(output_dir, exist_ok=True)
        
        video_files = []
        for ext in self.supported_video_formats:
            video_files.extend(Path(video_dir).glob(f"*{ext}"))
            video_files.extend(Path(video_dir).glob(f"*{ext.upper()}"))
        
        if not video_files:
            print(f"❌ 在 {video_dir} 中未找到视频文件")
            return False
            
        print(f"📹 找到 {len(video_files)} 个视频文件")
        
        metadata = []
        
        for i, video_file in enumerate(video_files):
            print(f"处理 {i+1}/{len(video_files)}: {video_file.name}")
            
            try:
                # 读取视频信息
                cap = cv2.VideoCapture(str(video_file))
                if not cap.isOpened():
                    print(f"   ❌ 无法打开视频: {video_file}")
                    continue
                
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                # 读取第一帧作为输入图像
                ret, first_frame = cap.read()
                if not ret:
                    print(f"   ❌ 无法读取第一帧: {video_file}")
                    cap.release()
                    continue
                
                cap.release()
                
                # 保存第一帧为图像
                first_frame_rgb = cv2.cvtColor(first_frame, cv2.COLOR_BGR2RGB)
                first_frame_pil = Image.fromarray(first_frame_rgb)
                
                # 调整图像尺寸到标准分辨率
                target_width, target_height = 832, 480
                first_frame_pil = first_frame_pil.resize((target_width, target_height))
                
                # 文件命名
                base_name = f"sample_{i:04d}"
                image_name = f"{base_name}_input.jpg"
                video_name = f"{base_name}.mp4"
                
                # 保存输入图像
                image_path = os.path.join(output_dir, image_name)
                first_frame_pil.save(image_path, quality=95)
                
                # 复制视频文件
                video_path = os.path.join(output_dir, video_name)
                shutil.copy2(video_file, video_path)
                
                # 生成提示词
                if prompts and i < len(prompts):
                    prompt = prompts[i]
                else:
                    prompt = f"video generated from image, motion, dynamic scene"
                
                # 添加到元数据
                metadata.append({
                    'video': video_name,
                    'input_image': image_name,
                    'prompt': prompt,
                    'fps': fps,
                    'frames': frame_count,
                    'width': target_width,
                    'height': target_height,
                    'original_size': f"{width}x{height}"
                })
                
                print(f"   ✅ 处理完成: {video_name}")
                
            except Exception as e:
                print(f"   ❌ 处理失败: {e}")
                continue
        
        # 保存元数据
        if metadata:
            df = pd.DataFrame(metadata)
            metadata_path = os.path.join(output_dir, 'metadata.csv')
            df.to_csv(metadata_path, index=False)
            
            print(f"\n✅ I2V数据集创建完成!")
            print(f"   数据集目录: {output_dir}")
            print(f"   样本数量: {len(metadata)}")
            print(f"   元数据文件: {metadata_path}")
            return True
        else:
            print("❌ 没有成功处理任何视频文件")
            return False
    
    def create_i2v_dataset_from_image_pairs(self, image_dir: str, output_dir: str, prompts_file: str = None):
        """
        从图像对创建I2V数据集
        假设有输入图像和对应的目标图像序列
        """
        print(f"🖼️  从图像对创建I2V数据集...")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 读取提示词文件
        prompts = {}
        if prompts_file and os.path.exists(prompts_file):
            with open(prompts_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if ':' in line:
                        key, value = line.strip().split(':', 1)
                        prompts[key.strip()] = value.strip()
        
        # 查找图像文件
        image_files = []
        for ext in self.supported_image_formats:
            image_files.extend(Path(image_dir).glob(f"*{ext}"))
            image_files.extend(Path(image_dir).glob(f"*{ext.upper()}"))
        
        print(f"🖼️  找到 {len(image_files)} 个图像文件")
        
        # 这里可以实现更复杂的图像序列到视频的转换逻辑
        # 目前作为示例实现
        
        return True
    
    def validate_dataset(self, dataset_dir: str):
        """验证数据集完整性"""
        print(f"🔍 验证数据集: {dataset_dir}")
        
        metadata_path = os.path.join(dataset_dir, 'metadata.csv')
        if not os.path.exists(metadata_path):
            print("❌ 元数据文件不存在: metadata.csv")
            return False
        
        try:
            df = pd.read_csv(metadata_path)
            print(f"📊 数据集包含 {len(df)} 个样本")
            
            missing_files = []
            valid_samples = 0
            
            for idx, row in df.iterrows():
                video_path = os.path.join(dataset_dir, row['video'])
                
                if not os.path.exists(video_path):
                    missing_files.append(row['video'])
                    continue
                
                # 检查视频文件
                cap = cv2.VideoCapture(video_path)
                if cap.isOpened():
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    if frame_count > 0:
                        valid_samples += 1
                    cap.release()
                else:
                    missing_files.append(f"{row['video']} (无法打开)")
            
            if missing_files:
                print(f"❌ 发现 {len(missing_files)} 个问题文件:")
                for file in missing_files[:10]:  # 只显示前10个
                    print(f"   - {file}")
                if len(missing_files) > 10:
                    print(f"   ... 还有 {len(missing_files) - 10} 个文件")
            
            print(f"✅ 有效样本: {valid_samples}/{len(df)}")
            
            # 显示数据集统计信息
            if 'width' in df.columns and 'height' in df.columns:
                resolutions = df.groupby(['width', 'height']).size()
                print("📐 分辨率分布:")
                for (w, h), count in resolutions.items():
                    print(f"   {w}x{h}: {count} 个样本")
            
            return len(missing_files) == 0
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def convert_dataset_format(self, input_dir: str, output_dir: str, target_resolution: Tuple[int, int] = (832, 480)):
        """转换数据集格式和分辨率"""
        print(f"🔄 转换数据集格式...")
        print(f"   输入: {input_dir}")
        print(f"   输出: {output_dir}")
        print(f"   目标分辨率: {target_resolution[0]}x{target_resolution[1]}")
        
        os.makedirs(output_dir, exist_ok=True)
        
        metadata_path = os.path.join(input_dir, 'metadata.csv')
        if not os.path.exists(metadata_path):
            print("❌ 输入数据集缺少metadata.csv")
            return False
        
        df = pd.read_csv(metadata_path)
        new_metadata = []
        
        for idx, row in df.iterrows():
            print(f"转换 {idx+1}/{len(df)}: {row['video']}")
            
            input_video_path = os.path.join(input_dir, row['video'])
            if not os.path.exists(input_video_path):
                print(f"   ❌ 视频文件不存在: {row['video']}")
                continue
            
            try:
                # 转换视频分辨率
                output_video_path = os.path.join(output_dir, row['video'])
                
                # 使用ffmpeg转换分辨率（需要安装ffmpeg）
                cmd = f"ffmpeg -i '{input_video_path}' -vf scale={target_resolution[0]}:{target_resolution[1]} -c:v libx264 -crf 23 '{output_video_path}' -y"
                
                result = os.system(cmd)
                if result == 0:
                    # 更新元数据
                    new_row = row.copy()
                    new_row['width'] = target_resolution[0]
                    new_row['height'] = target_resolution[1]
                    new_metadata.append(new_row)
                    print(f"   ✅ 转换完成")
                else:
                    print(f"   ❌ 转换失败")
                    
            except Exception as e:
                print(f"   ❌ 转换失败: {e}")
                continue
        
        # 保存新的元数据
        if new_metadata:
            new_df = pd.DataFrame(new_metadata)
            new_metadata_path = os.path.join(output_dir, 'metadata.csv')
            new_df.to_csv(new_metadata_path, index=False)
            
            print(f"✅ 数据集转换完成: {len(new_metadata)} 个样本")
            return True
        else:
            print("❌ 没有成功转换任何样本")
            return False

def main():
    parser = argparse.ArgumentParser(description="Wan I2V数据集处理工具")
    parser.add_argument("--action", choices=["create_from_videos", "validate", "convert"], required=True, help="操作类型")
    parser.add_argument("--input_dir", type=str, required=True, help="输入目录")
    parser.add_argument("--output_dir", type=str, help="输出目录")
    parser.add_argument("--prompts_file", type=str, help="提示词文件路径")
    parser.add_argument("--resolution", type=str, default="832x480", help="目标分辨率 (宽x高)")
    
    args = parser.parse_args()
    
    processor = WanI2VDatasetProcessor()
    
    if args.action == "create_from_videos":
        if not args.output_dir:
            print("❌ 创建数据集需要指定输出目录")
            return
        
        # 读取提示词
        prompts = None
        if args.prompts_file and os.path.exists(args.prompts_file):
            with open(args.prompts_file, 'r', encoding='utf-8') as f:
                prompts = [line.strip() for line in f if line.strip()]
        
        processor.create_i2v_dataset_from_videos(args.input_dir, args.output_dir, prompts)
        
    elif args.action == "validate":
        processor.validate_dataset(args.input_dir)
        
    elif args.action == "convert":
        if not args.output_dir:
            print("❌ 转换数据集需要指定输出目录")
            return
        
        # 解析分辨率
        try:
            width, height = map(int, args.resolution.split('x'))
            target_resolution = (width, height)
        except:
            print("❌ 分辨率格式错误，应为 '宽x高'，如 '832x480'")
            return
        
        processor.convert_dataset_format(args.input_dir, args.output_dir, target_resolution)

if __name__ == "__main__":
    main()

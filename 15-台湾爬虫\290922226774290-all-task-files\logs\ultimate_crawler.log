2025-07-15 19:23:50,826 - __main__ - INFO - 数据库初始化完成
2025-07-15 19:23:50,826 - __main__ - INFO - 爬虫初始化完成 - 目标: https://web.pcc.gov.tw
2025-07-15 19:23:50,826 - __main__ - INFO - 🚀 终极完美爬虫启动
2025-07-15 19:23:50,826 - __main__ - INFO - 🎯 目标网站: https://web.pcc.gov.tw
2025-07-15 19:23:50,830 - __main__ - INFO - 正在爬取第 1 页
2025-07-15 19:23:57,471 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/resultBulletion (尝试 1)
2025-07-15 19:23:57,488 - __main__ - INFO - 解析到 10 条公告
2025-07-15 19:23:57,488 - __main__ - INFO - 第 1 页完成，获得 10 条数据，累计 10 条
2025-07-15 19:23:57,488 - __main__ - INFO - 正在爬取第 2 页
2025-07-15 19:24:02,430 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/resultBulletion (尝试 1)
2025-07-15 19:24:02,447 - __main__ - INFO - 解析到 10 条公告
2025-07-15 19:24:02,447 - __main__ - INFO - 第 2 页完成，获得 10 条数据，累计 20 条
2025-07-15 19:24:02,447 - __main__ - INFO - 正在爬取第 3 页
2025-07-15 19:24:08,098 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/resultBulletion (尝试 1)
2025-07-15 19:24:08,114 - __main__ - INFO - 解析到 10 条公告
2025-07-15 19:24:08,114 - __main__ - INFO - 第 3 页完成，获得 10 条数据，累计 30 条
2025-07-15 19:24:08,114 - __main__ - INFO - 正在爬取第 4 页
2025-07-15 19:24:11,876 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/resultBulletion (尝试 1)
2025-07-15 19:24:11,891 - __main__ - INFO - 解析到 10 条公告
2025-07-15 19:24:11,891 - __main__ - INFO - 第 4 页完成，获得 10 条数据，累计 40 条
2025-07-15 19:24:11,891 - __main__ - INFO - 正在爬取第 5 页
2025-07-15 19:24:15,138 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/resultBulletion (尝试 1)
2025-07-15 19:24:15,153 - __main__ - INFO - 解析到 10 条公告
2025-07-15 19:24:15,153 - __main__ - INFO - 第 5 页完成，获得 10 条数据，累计 50 条
2025-07-15 19:24:15,158 - __main__ - INFO - CSV文件已保存: /workspace/data/csv/ultimate_crawler_data_20250715_192415.csv
2025-07-15 19:24:15,158 - __main__ - INFO - JSON文件已保存: /workspace/data/json/ultimate_crawler_data_20250715_192415.json
2025-07-15 19:24:15,161 - __main__ - INFO - 数据库已保存 50 条记录
2025-07-15 19:24:15,161 - __main__ - INFO - ✅ 爬取完成！共获得 50 条数据
2025-07-15 19:24:15,161 - __main__ - INFO - 
============================================================
               终极完美爬虫执行报告
============================================================

🕐 执行时间:
   开始: 2025-07-15 19:23:50
   结束: 2025-07-15 19:24:15
   时长: 0:00:24.335105

🌐 目标网站:
   基础URL: https://web.pcc.gov.tw
   搜索URL: https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion

⚙️  爬虫配置:
   并发数: 2
   延迟范围: (3, 6) 秒
   最大页数: 5
   使用Selenium: 是
   JavaScript支持: 是

📊 请求统计:
   总请求数: 5
   成功请求: 5
   失败请求: 0
   成功率: 100.00%

📈 数据统计:
   总爬取条目: 50
   平均速度: 123.28 条/分钟
   数据质量: 优秀

💾 文件输出:
   数据库: /workspace/data/database/ultimate_crawler.db
   CSV目录: /workspace/data/csv/
   JSON目录: /workspace/data/json/
   日志文件: /workspace/logs/ultimate_crawler.log
   调试文件: /workspace/debug/

🔧 系统状态:
   Selenium可用: 否
   第三方库: BeautifulSoup, lxml, requests
   
💡 优化建议:
   1. 数据获取成功，爬虫运行正常
   2. 可以适当提高并发数
   3. 定期更新User-Agent和反爬虫策略
   4. 监控网站结构变化

============================================================

2025-07-15 22:12:00,211 - __main__ - INFO - 数据库初始化完成
2025-07-15 22:12:00,212 - __main__ - INFO - 爬虫初始化完成 - 目标: https://web.pcc.gov.tw
2025-07-15 22:12:00,212 - __main__ - INFO - 🚀 终极完美爬虫启动
2025-07-15 22:12:00,213 - __main__ - INFO - 🎯 目标网站: https://web.pcc.gov.tw
2025-07-15 22:12:00,218 - __main__ - INFO - 正在爬取第 1 页决标公告列表
2025-07-15 22:12:00,218 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:12:02,364 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:12:02,718 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:12:03,065 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:12:05,761 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:12:18,638 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E5%86%B3%E6%A0%87&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10&pageIndex=0 (尝试 1)
2025-07-15 22:12:18,716 - __main__ - INFO - 解析到 13 条决标公告
2025-07-15 22:12:18,717 - __main__ - INFO - 第 1 页完成，获得 13 条完整数据，累计 13 条
2025-07-15 22:12:18,717 - __main__ - INFO - 正在爬取第 2 页决标公告列表
2025-07-15 22:12:18,717 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:12:20,345 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:12:20,652 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:12:20,990 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:12:23,619 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:12:34,719 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E5%86%B3%E6%A0%87&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10&pageIndex=1 (尝试 1)
2025-07-15 22:12:34,779 - __main__ - INFO - 解析到 13 条决标公告
2025-07-15 22:12:34,779 - __main__ - INFO - 第 2 页完成，获得 13 条完整数据，累计 26 条
2025-07-15 22:12:34,779 - __main__ - INFO - 正在爬取第 3 页决标公告列表
2025-07-15 22:12:34,779 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:12:36,353 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:12:36,640 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:12:36,981 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:12:39,613 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:12:49,541 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E5%86%B3%E6%A0%87&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10&pageIndex=2 (尝试 1)
2025-07-15 22:12:49,601 - __main__ - INFO - 解析到 13 条决标公告
2025-07-15 22:12:49,601 - __main__ - INFO - 第 3 页完成，获得 13 条完整数据，累计 39 条
2025-07-15 22:12:49,601 - __main__ - INFO - 正在爬取第 4 页决标公告列表
2025-07-15 22:12:49,602 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:12:51,671 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:12:52,021 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:12:52,380 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:12:55,180 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:13:09,871 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E5%86%B3%E6%A0%87&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10&pageIndex=3 (尝试 1)
2025-07-15 22:13:09,930 - __main__ - INFO - 解析到 13 条决标公告
2025-07-15 22:13:09,932 - __main__ - INFO - 第 4 页完成，获得 13 条完整数据，累计 52 条
2025-07-15 22:13:09,932 - __main__ - INFO - 正在爬取第 5 页决标公告列表
2025-07-15 22:13:09,932 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:13:11,674 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:13:16,158 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:13:16,481 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:13:19,112 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:13:28,176 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E5%86%B3%E6%A0%87&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10&pageIndex=4 (尝试 1)
2025-07-15 22:13:28,229 - __main__ - INFO - 解析到 13 条决标公告
2025-07-15 22:13:28,231 - __main__ - INFO - 第 5 页完成，获得 13 条完整数据，累计 65 条
2025-07-15 22:13:28,243 - __main__ - INFO - CSV文件已保存: data/csv/ultimate_crawler_data_20250715_221328.csv
2025-07-15 22:13:28,245 - __main__ - INFO - JSON文件已保存: data/json/ultimate_crawler_data_20250715_221328.json
2025-07-15 22:13:28,246 - __main__ - ERROR - 保存数据库失败: table crawl_data has no column named case_number
2025-07-15 22:13:28,246 - __main__ - INFO - ✅ 爬取完成！共获得 65 条数据
2025-07-15 22:13:28,247 - __main__ - INFO - 
============================================================
               终极完美爬虫执行报告
============================================================

🕐 执行时间:
   开始: 2025-07-15 22:12:00
   结束: 2025-07-15 22:13:28
   时长: 0:01:28.035122

🌐 目标网站:
   基础URL: https://web.pcc.gov.tw
   搜索URL: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion

⚙️  爬虫配置:
   并发数: 2
   延迟范围: (3, 6) 秒
   最大页数: 5
   使用Selenium: 是
   JavaScript支持: 是

📊 请求统计:
   总请求数: 5
   成功请求: 5
   失败请求: 0
   成功率: 100.00%

📈 数据统计:
   总爬取条目: 65
   平均速度: 44.30 条/分钟
   数据质量: 优秀

💾 文件输出:
   数据库: data/database/ultimate_crawler.db
   CSV目录: data/csv/
   JSON目录: data/json/
   日志文件: logs/ultimate_crawler.log
   调试文件: debug/

🔧 系统状态:
   Selenium可用: 是
   第三方库: BeautifulSoup, lxml, requests
   
💡 优化建议:
   1. 数据获取成功，爬虫运行正常
   2. 可以适当提高并发数
   3. 定期更新User-Agent和反爬虫策略
   4. 监控网站结构变化

============================================================

2025-07-15 22:17:22,006 - __main__ - INFO - 数据库初始化完成
2025-07-15 22:17:22,007 - __main__ - INFO - 爬虫初始化完成 - 目标: https://web.pcc.gov.tw
2025-07-15 22:17:22,007 - __main__ - INFO - 🚀 终极完美爬虫启动
2025-07-15 22:17:22,007 - __main__ - INFO - 🎯 目标网站: https://web.pcc.gov.tw
2025-07-15 22:17:22,012 - __main__ - INFO - 正在爬取第 1 页决标公告列表
2025-07-15 22:17:22,012 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:17:23,590 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:17:23,907 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:17:24,258 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:17:26,899 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:17:35,560 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E5%86%B3%E6%A0%87&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10&pageIndex=0 (尝试 1)
2025-07-15 22:17:35,612 - __main__ - INFO - 解析到 0 条决标公告
2025-07-15 22:17:35,613 - __main__ - INFO - 第 1 页无数据，停止爬取
2025-07-15 22:17:35,618 - __main__ - WARNING - ⚠️  未获得任何数据
2025-07-15 22:17:35,619 - __main__ - INFO - 
============================================================
               终极完美爬虫执行报告
============================================================

🕐 执行时间:
   开始: 2025-07-15 22:17:22
   结束: 2025-07-15 22:17:35
   时长: 0:00:13.611841

🌐 目标网站:
   基础URL: https://web.pcc.gov.tw
   搜索URL: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion

⚙️  爬虫配置:
   并发数: 2
   延迟范围: (3, 6) 秒
   最大页数: 5
   使用Selenium: 是
   JavaScript支持: 是

📊 请求统计:
   总请求数: 1
   成功请求: 1
   失败请求: 0
   成功率: 100.00%

📈 数据统计:
   总爬取条目: 0
   平均速度: 0.00 条/分钟
   数据质量: 需要调试

💾 文件输出:
   数据库: data/database/ultimate_crawler.db
   CSV目录: data/csv/
   JSON目录: data/json/
   日志文件: logs/ultimate_crawler.log
   调试文件: debug/

🔧 系统状态:
   Selenium可用: 是
   第三方库: BeautifulSoup, lxml, requests
   
💡 优化建议:
   1. 建议检查网站结构或调整解析规则
   2. 可以适当提高并发数
   3. 定期更新User-Agent和反爬虫策略
   4. 监控网站结构变化

============================================================

2025-07-15 22:18:41,110 - __main__ - INFO - 数据库初始化完成
2025-07-15 22:18:41,110 - __main__ - INFO - 爬虫初始化完成 - 目标: https://web.pcc.gov.tw
2025-07-15 22:18:41,110 - __main__ - INFO - 🚀 终极完美爬虫启动
2025-07-15 22:18:41,110 - __main__ - INFO - 🎯 目标网站: https://web.pcc.gov.tw
2025-07-15 22:18:41,114 - __main__ - INFO - 正在爬取第 1 页决标公告列表
2025-07-15 22:18:41,115 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:18:45,786 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:18:46,104 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:18:46,456 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:18:49,119 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:18:58,371 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E5%86%B3%E6%A0%87&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10&pageIndex=0 (尝试 1)
2025-07-15 22:18:58,426 - __main__ - INFO - 解析到 10 条决标公告
2025-07-15 22:18:58,426 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:18:58,427 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:19:00,313 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:19:00,765 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:19:01,086 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:19:03,747 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:19:14,543 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3MzY4Mjk= (尝试 1)
2025-07-15 22:19:17,424 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:19:17,424 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:19:17,599 - __main__ - INFO - 用户中断爬取
2025-07-15 22:19:17,605 - __main__ - WARNING - ⚠️  未获得任何数据
2025-07-15 22:19:17,605 - __main__ - INFO - 
============================================================
               终极完美爬虫执行报告
============================================================

🕐 执行时间:
   开始: 2025-07-15 22:18:41
   结束: 2025-07-15 22:19:17
   时长: 0:00:36.495137

🌐 目标网站:
   基础URL: https://web.pcc.gov.tw
   搜索URL: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion

⚙️  爬虫配置:
   并发数: 2
   延迟范围: (3, 6) 秒
   最大页数: 5
   使用Selenium: 是
   JavaScript支持: 是

📊 请求统计:
   总请求数: 2
   成功请求: 2
   失败请求: 0
   成功率: 100.00%

📈 数据统计:
   总爬取条目: 0
   平均速度: 0.00 条/分钟
   数据质量: 需要调试

💾 文件输出:
   数据库: data/database/ultimate_crawler.db
   CSV目录: data/csv/
   JSON目录: data/json/
   日志文件: logs/ultimate_crawler.log
   调试文件: debug/

🔧 系统状态:
   Selenium可用: 是
   第三方库: BeautifulSoup, lxml, requests
   
💡 优化建议:
   1. 建议检查网站结构或调整解析规则
   2. 可以适当提高并发数
   3. 定期更新User-Agent和反爬虫策略
   4. 监控网站结构变化

============================================================

2025-07-15 22:21:51,572 - __main__ - INFO - 数据库初始化完成
2025-07-15 22:21:51,573 - __main__ - INFO - 爬虫初始化完成 - 目标: https://web.pcc.gov.tw
2025-07-15 22:21:51,573 - __main__ - INFO - 🚀 终极完美爬虫启动
2025-07-15 22:21:51,574 - __main__ - INFO - 🎯 目标网站: https://web.pcc.gov.tw
2025-07-15 22:21:51,579 - __main__ - INFO - 正在爬取第 1 页决标公告列表
2025-07-15 22:21:51,580 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:21:53,409 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:21:53,761 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:21:54,141 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:21:56,797 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:22:08,096 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E5%86%B3%E6%A0%87&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10&pageIndex=0 (尝试 1)
2025-07-15 22:22:08,136 - __main__ - INFO - 解析到 10 条决标公告
2025-07-15 22:22:08,136 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:22:08,136 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:22:09,622 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:22:09,993 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:22:10,380 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:22:13,011 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:22:27,691 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3MzY4Mjk= (尝试 1)
2025-07-15 22:22:28,866 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:22:28,867 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:22:30,362 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:22:30,691 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:22:31,024 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:22:33,673 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:22:47,400 - __main__ - WARNING - 请求失败 (尝试 1/3): https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3Mjg1NjU= - HTTPSConnectionPool(host='web.pcc.gov.tw', port=443): Max retries exceeded with url: /prkms/urlSelector/common/tpam?pk=NTI3Mjg1NjU= (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1007)')))
2025-07-15 22:22:54,220 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3Mjg1NjU= (尝试 2)
2025-07-15 22:22:56,450 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:22:56,451 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:22:58,012 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:22:58,366 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:22:58,676 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:23:01,322 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:23:11,764 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3NDA2Nzg= (尝试 1)
2025-07-15 22:23:12,982 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:23:12,983 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:23:14,480 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:23:14,807 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:23:15,124 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:23:17,759 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:23:27,984 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3ODU5Nzg= (尝试 1)
2025-07-15 22:23:30,981 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:23:30,982 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:23:32,666 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:23:37,623 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:23:37,922 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:23:40,577 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:23:51,491 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3MjE3MDI= (尝试 1)
2025-07-15 22:23:53,650 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:23:53,651 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:23:55,703 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:23:59,536 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:23:59,840 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:24:02,484 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:24:11,960 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3MjcyNTE= (尝试 1)
2025-07-15 22:24:13,889 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:24:13,890 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:24:15,369 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:24:20,017 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:24:20,327 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:24:22,964 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:24:32,399 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3NTgxNzk= (尝试 1)
2025-07-15 22:24:33,943 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:24:33,943 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:24:35,380 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:24:35,706 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:24:40,708 - __main__ - WARNING - Selenium初始化失败: Could not reach host. Are you offline?
2025-07-15 22:24:45,494 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3Mjg2NDk= (尝试 1)
2025-07-15 22:24:48,145 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:24:48,146 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:24:50,202 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:24:50,556 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:24:50,847 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:24:53,490 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:25:03,129 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3Njc5NzQ= (尝试 1)
2025-07-15 22:25:04,236 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:25:04,236 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:25:05,751 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:25:06,081 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:25:06,415 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:25:09,043 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:25:18,681 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/urlSelector/common/tpam?pk=NTI3NDMwNjk= (尝试 1)
2025-07-15 22:25:21,489 - __main__ - INFO - 第 1 页完成，获得 10 条完整数据，累计 10 条
2025-07-15 22:25:21,489 - __main__ - INFO - 正在爬取第 2 页决标公告列表
2025-07-15 22:25:21,490 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:25:23,085 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:25:23,382 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:25:23,682 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:25:26,324 - __main__ - WARNING - Selenium初始化失败: Message: session not created: Failed to create Chrome process.
Stacktrace:
	GetHandleVerifier [0x0x3144a3+62419]
	GetHandleVerifier [0x0x3144e4+62484]
	(No symbol) [0x0x152133]
	(No symbol) [0x0x185108]
	(No symbol) [0x0x181249]
	(No symbol) [0x0x1caf3e]
	(No symbol) [0x0x1ca82a]
	(No symbol) [0x0x1bf266]
	(No symbol) [0x0x18e852]
	(No symbol) [0x0x18f6f4]
	GetHandleVerifier [0x0x584793+2619075]
	GetHandleVerifier [0x0x57fbaa+2599642]
	GetHandleVerifier [0x0x33b04a+221050]
	GetHandleVerifier [0x0x32b2c8+156152]
	GetHandleVerifier [0x0x331c7d+183213]
	GetHandleVerifier [0x0x31c388+94904]
	GetHandleVerifier [0x0x31c512+95298]
	GetHandleVerifier [0x0x30766a+9626]
	BaseThreadInitThunk [0x0x755fd709+25]
	RtlInitializeExceptionChain [0x0x77ae964b+107]
	RtlGetAppContainerNamedObjectPath [0x0x77ae95d1+561]
	(No symbol) [0x0]

2025-07-15 22:25:36,220 - __main__ - INFO - 请求成功: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion?querySentence=%E5%9B%BD%E9%98%B2%E9%83%A8&tenderStatusType=%E5%86%B3%E6%A0%87&sortCol=AWARD_NOTICE_DATE&timeRange=111&pron=true&fuzzy=true&pageSize=10&pageIndex=1 (尝试 1)
2025-07-15 22:25:36,259 - __main__ - INFO - 解析到 10 条决标公告
2025-07-15 22:25:36,259 - __main__ - INFO - 正在爬取详细信息: 未知标案...
2025-07-15 22:25:36,264 - WDM - INFO - ====== WebDriver manager ======
2025-07-15 22:25:37,913 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:25:38,242 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-07-15 22:25:38,563 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.94\chromedriver-win32/chromedriver.exe] found in cache
2025-07-15 22:25:40,197 - __main__ - INFO - 用户中断爬取
2025-07-15 22:25:42,633 - __main__ - INFO - CSV文件已保存: data/csv/ultimate_crawler_data_20250715_222542.csv
2025-07-15 22:25:42,635 - __main__ - INFO - JSON文件已保存: data/json/ultimate_crawler_data_20250715_222542.json
2025-07-15 22:25:42,640 - __main__ - INFO - 数据库已保存 10 条记录
2025-07-15 22:25:42,640 - __main__ - INFO - ✅ 爬取完成！共获得 10 条数据
2025-07-15 22:25:42,640 - __main__ - INFO - 
============================================================
               终极完美爬虫执行报告
============================================================

🕐 执行时间:
   开始: 2025-07-15 22:21:51
   结束: 2025-07-15 22:25:42
   时长: 0:03:51.067094

🌐 目标网站:
   基础URL: https://web.pcc.gov.tw
   搜索URL: https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion

⚙️  爬虫配置:
   并发数: 2
   延迟范围: (3, 6) 秒
   最大页数: 5
   使用Selenium: 是
   JavaScript支持: 是

📊 请求统计:
   总请求数: 13
   成功请求: 12
   失败请求: 0
   成功率: 92.31%

📈 数据统计:
   总爬取条目: 10
   平均速度: 2.60 条/分钟
   数据质量: 优秀

💾 文件输出:
   数据库: data/database/ultimate_crawler.db
   CSV目录: data/csv/
   JSON目录: data/json/
   日志文件: logs/ultimate_crawler.log
   调试文件: debug/

🔧 系统状态:
   Selenium可用: 是
   第三方库: BeautifulSoup, lxml, requests
   
💡 优化建议:
   1. 数据获取成功，爬虫运行正常
   2. 可以适当提高并发数
   3. 定期更新User-Agent和反爬虫策略
   4. 监控网站结构变化

============================================================


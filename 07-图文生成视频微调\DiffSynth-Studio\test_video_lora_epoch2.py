#!/usr/bin/env python3
"""
使用epoch-2.safetensors进行视频LoRA推理测试
"""

import torch
from PIL import Image
import os
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def main():
    print("🎬 视频LoRA推理测试 - Epoch 2")
    print("=" * 60)

    # 检查epoch-2检查点
    lora_checkpoint = "/root/sj-tmp/DiffSynth-Studio/models/train/Wan2.1-I2V-14B-480P_video_lora/epoch-2.safetensors"

    if os.path.exists(lora_checkpoint):
        file_size = os.path.getsize(lora_checkpoint) / 1024**2
        print(f"✅ 找到视频LoRA检查点: epoch-2.safetensors")
        print(f"   文件大小: {file_size:.1f}MB")
        print(f"   文件路径: {lora_checkpoint}")
    else:
        print("❌ 未找到epoch-2.safetensors检查点")
        return False
    
    print(f"\n📦 初始化Pipeline...")
    
    # 创建pipeline
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cuda",
        model_configs=[
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
        ],
    )
    pipe.enable_vram_management()
    
    print("✅ Pipeline初始化成功")
    
    # 加载LoRA权重
    print(f"\n🔧 加载LoRA权重...")
    try:
        pipe.load_lora(path=lora_checkpoint)
        print(f"✅ LoRA权重加载成功")
    except Exception as e:
        print(f"❌ LoRA权重加载失败: {e}")
        return False
    
    # 使用视频数据集中的图像作为输入
    test_images = [
        "data/custom_video_dataset/images/ocean_sunset_000.jpg",
        "data/custom_video_dataset/images/forest_morning_001.jpg", 
        "data/custom_video_dataset/images/mountain_landscape_002.jpg"
    ]
    
    # 对应的测试提示词（来自训练数据）
    test_prompts = [
        "A beautiful sunset over the ocean with gentle waves, warm golden light reflecting on water, cinematic lighting",
        "A peaceful forest in the morning with sunlight filtering through green leaves, natural lighting, serene atmosphere",
        "Majestic mountains with snow-capped peaks under a clear blue sky, dramatic landscape, high quality"
    ]
    
    print(f"\n🎬 开始视频LoRA推理测试...")
    
    for i, (image_path, prompt) in enumerate(zip(test_images, test_prompts)):
        print(f"\n--- 测试 {i+1}/3: {prompt[:50]}... ---")
        
        # 加载输入图像
        if os.path.exists(image_path):
            image = Image.open(image_path)
            print(f"✅ 使用训练图像: {image_path}")
        else:
            # 创建默认图像
            image = Image.new('RGB', (832, 480), color=(135, 206, 235))
            print(f"⚠️  使用默认图像 (训练图像不存在)")
        
        try:
            print(f"🔄 开始推理...")
            video = pipe(
                prompt=prompt,
                negative_prompt="low quality, blurry, static, distorted",
                input_image=image,
                seed=42 + i,
                tiled=True,
                height=480,
                width=832,
                num_frames=45,  # 匹配训练数据的帧数
                cfg_scale=7.5,
                num_inference_steps=30
            )
            
            output_path = f"video_lora_epoch2_test_{i+1}.mp4"
            save_video(video, output_path, fps=15, quality=5)  # 匹配训练数据的fps
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024**2
                print(f"✅ 生成成功: {output_path} ({file_size:.1f}MB)")
            else:
                print(f"❌ 生成失败: {output_path}")
                
        except Exception as e:
            print(f"❌ 推理失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 视频LoRA Epoch-2 推理测试完成!")
    print(f"\n📁 生成的视频文件:")
    for i in range(3):
        output_path = f"video_lora_epoch2_test_{i+1}.mp4"
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024**2
            print(f"   ✅ {output_path} ({file_size:.1f}MB)")
    
    return True

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
使用GPU 1进行推理
避免GPU 0的内存占用问题
"""

import torch
import os
from PIL import Image
from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def main():
    print("🎬 Wan2.1-I2V-14B-480P GPU1推理")
    print("=" * 50)
    
    # 强制使用GPU 1
    os.environ['CUDA_VISIBLE_DEVICES'] = '1'
    
    # 检查GPU状态
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    print(f"✅ 使用GPU: {torch.cuda.get_device_name(0)} (原GPU 1)")
    total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    print(f"   总内存: {total_memory:.1f}GB")
    
    # 检查LoRA检查点
    lora_dir = "./models/train/Wan2.1-I2V-14B-480P_lora_final"
    available_epochs = []
    
    for i in range(10):
        epoch_file = f"{lora_dir}/epoch-{i}.safetensors"
        if os.path.exists(epoch_file):
            available_epochs.append(i)
    
    if available_epochs:
        latest_epoch = max(available_epochs)
        lora_checkpoint = f"{lora_dir}/epoch-{latest_epoch}.safetensors"
        print(f"✅ 找到LoRA检查点: epoch-{latest_epoch}")
        file_size = os.path.getsize(lora_checkpoint) / 1024**2
        print(f"   文件大小: {file_size:.1f}MB")
    else:
        print("⚠️  未找到LoRA检查点")
        lora_checkpoint = None
    
    print("\n📦 初始化Pipeline (GPU 1)...")
    
    try:
        # 创建pipeline
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",  # 这里会使用CUDA_VISIBLE_DEVICES指定的GPU
            model_configs=[
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
            ],
        )
        
        # 启用VRAM管理
        pipe.enable_vram_management()
        
        print("✅ Pipeline初始化成功")
        
        # 显示GPU内存使用
        allocated = torch.cuda.memory_allocated(0) / 1024**3
        print(f"   当前GPU内存使用: {allocated:.2f}GB")
        
        # LoRA权重信息
        if lora_checkpoint:
            try:
                from safetensors import safe_open
                with safe_open(lora_checkpoint, framework="pt", device="cpu") as f:
                    lora_keys = list(f.keys())
                print(f"🔧 LoRA权重: {len(lora_keys)} 个参数")
            except Exception as e:
                print(f"⚠️  LoRA权重读取失败: {e}")
        
        print("\n📥 准备输入图像...")
        
        # 创建测试图像
        image = Image.new('RGB', (832, 480), color=(70, 130, 180))  # 钢蓝色
        print("✅ 使用钢蓝色测试图像 (832x480)")
        
        print("\n🎬 开始视频生成 (GPU 1)...")
        print("   使用优化参数...")
        
        # 生成视频
        video = pipe(
            prompt="A beautiful sunset over the ocean with gentle waves, cinematic lighting",
            negative_prompt="low quality, blurry, static",
            input_image=image,
            seed=42, 
            tiled=True,
            height=480,
            width=832,
            num_frames=25,  # 减少帧数
            cfg_scale=7.5,
            num_inference_steps=30  # 适中的推理步数
        )
        
        print("💾 保存视频...")
        
        # 保存视频
        output_path = f"gpu1_inference_epoch{latest_epoch if lora_checkpoint else 'base'}.mp4"
        save_video(video, output_path, fps=10, quality=5)
        
        # 检查结果
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024**2
            print(f"✅ 视频生成成功!")
            print(f"📁 文件: {output_path}")
            print(f"📊 大小: {file_size:.1f}MB")
            
            # 最终GPU状态
            allocated = torch.cuda.memory_allocated(0) / 1024**3
            print(f"   最终GPU内存使用: {allocated:.2f}GB")
            
            print(f"\n🎉 GPU1推理成功!")
            print(f"   视频规格: 832x480, 25帧, 10fps")
            print(f"   推理参数: 30步, CFG=7.5")
            print(f"   使用GPU: GPU 1 (避开GPU 0的内存占用)")
            
            return True
            
        else:
            print("❌ 视频文件未生成")
            return False

    except Exception as e:
        print(f"❌ 推理失败: {e}")
        
        if "out of memory" in str(e).lower():
            print(f"\n💡 内存不足解决方案:")
            print(f"   1. 减少帧数: num_frames=16")
            print(f"   2. 减少推理步数: num_inference_steps=20")
            print(f"   3. 降低分辨率: height=320, width=576")
        
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 GPU1推理成功完成!")
        print("=" * 50)
        print("✅ 避开GPU内存冲突")
        print("✅ 使用LoRA微调权重")
        print("✅ 视频生成成功")
        print("\n🚀 您的Wan2.1-I2V-14B-480P推理已成功运行!")
    else:
        print("\n❌ 推理失败")

#!/bin/bash

echo "=== T2V-1.3B 完整训练和推理流程 ==="

# 步骤1: 数据准备
echo "步骤1: 数据准备..."
python scripts/data_prep/prepare_*_dataset.py

# 步骤2: LoRA训练
echo "步骤2: LoRA训练..."
bash scripts/train/train_lora.sh

# 步骤3: 基础推理
echo "步骤3: 基础推理..."
python scripts/inference/inference_base.py

# 步骤4: 模型合并
echo "步骤4: 模型合并..."
python scripts/merge/merge_lora.py \
    --lora_path "./models/train/T2V-1.3B_lora/pytorch_lora_weights.safetensors" \
    --output_path "./models/merged/T2V-1.3B-merged"

echo "完整流程执行完成！"

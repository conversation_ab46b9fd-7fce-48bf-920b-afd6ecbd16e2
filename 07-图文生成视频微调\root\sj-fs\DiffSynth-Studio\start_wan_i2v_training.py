#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P 一键启动训练脚本
提供交互式界面，帮助用户选择合适的训练配置
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

class WanI2VTrainingLauncher:
    def __init__(self):
        self.training_configs = {
            "1": {
                "name": "显存优化训练 (推荐新手)",
                "script": "examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_memory_optimized.sh",
                "description": "适用于单GPU RTX 3090，显存优化配置",
                "requirements": "RTX 3090 24GB × 1",
                "time": "约3小时"
            },
            "2": {
                "name": "标准LoRA训练",
                "script": "examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P.sh",
                "description": "标准配置，平衡性能和质量",
                "requirements": "RTX 3090 24GB × 1",
                "time": "约4小时"
            },
            "3": {
                "name": "8×RTX 3090分布式训练 (高性能)",
                "script": "examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_8x3090.sh",
                "description": "多GPU分布式训练，最快速度",
                "requirements": "RTX 3090 24GB × 8",
                "time": "约1小时"
            }
        }
    
    def check_environment(self):
        """检查环境配置"""
        print("🔍 检查环境配置...")
        
        # 检查虚拟环境
        current_env = os.environ.get('CONDA_DEFAULT_ENV', '')
        if current_env != 'wan_video_env':
            print("❌ 请先激活wan_video_env环境:")
            print("   conda activate wan_video_env")
            return False
        
        print(f"✅ 虚拟环境: {current_env}")
        
        # 检查GPU
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=count', '--format=csv,noheader'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                gpu_count = len(result.stdout.strip().split('\n'))
                print(f"✅ 检测到 {gpu_count} 块GPU")
            else:
                print("❌ 无法检测GPU")
                return False
        except FileNotFoundError:
            print("❌ nvidia-smi 未找到")
            return False
        
        # 检查数据集
        dataset_path = "data/example_video_dataset"
        if os.path.exists(dataset_path):
            print(f"✅ 数据集已准备: {dataset_path}")
        else:
            print(f"⚠️  数据集未找到: {dataset_path}")
            print("   将自动下载示例数据集")
        
        # 检查磁盘空间
        import shutil
        free_space = shutil.disk_usage('.').free / 1024**3
        if free_space < 50:
            print(f"⚠️  磁盘空间不足: {free_space:.1f}GB (建议至少100GB)")
        else:
            print(f"✅ 磁盘空间充足: {free_space:.1f}GB")
        
        return True
    
    def show_training_options(self):
        """显示训练选项"""
        print("\n🎬 Wan2.1-I2V-14B-480P 训练配置选择")
        print("=" * 60)
        
        for key, config in self.training_configs.items():
            print(f"{key}. {config['name']}")
            print(f"   描述: {config['description']}")
            print(f"   要求: {config['requirements']}")
            print(f"   时间: {config['time']}")
            print()
        
        print("0. 退出")
        print("=" * 60)
    
    def get_user_choice(self):
        """获取用户选择"""
        while True:
            try:
                choice = input("请选择训练配置 (0-3): ").strip()
                if choice == "0":
                    return None
                elif choice in self.training_configs:
                    return choice
                else:
                    print("❌ 无效选择，请输入0-3")
            except KeyboardInterrupt:
                print("\n👋 再见!")
                return None
    
    def confirm_training(self, config):
        """确认训练配置"""
        print(f"\n📋 训练配置确认:")
        print(f"   配置: {config['name']}")
        print(f"   脚本: {config['script']}")
        print(f"   要求: {config['requirements']}")
        print(f"   预计时间: {config['time']}")
        print()
        
        while True:
            confirm = input("确认开始训练? (y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                return True
            elif confirm in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n")
    
    def run_training(self, script_path):
        """执行训练脚本"""
        if not os.path.exists(script_path):
            print(f"❌ 训练脚本不存在: {script_path}")
            return False
        
        print(f"🚀 开始执行训练脚本: {script_path}")
        print("=" * 60)
        
        try:
            # 给脚本添加执行权限
            os.chmod(script_path, 0o755)
            
            # 执行脚本
            result = subprocess.run(['bash', script_path], check=True)
            
            print("=" * 60)
            print("✅ 训练完成!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 训练失败: {e}")
            return False
        except KeyboardInterrupt:
            print("\n⏹️  训练被用户中断")
            return False
    
    def post_training_actions(self):
        """训练后操作"""
        print("\n🎉 训练完成! 接下来您可以:")
        print("1. 运行推理测试:")
        print("   python test_wan_i2v_lora.py --auto_detect")
        print()
        print("2. 查看训练结果:")
        print("   ls -la models/train/")
        print()
        print("3. 监控GPU状态:")
        print("   python tools/gpu_monitor.py --action summary")
        print()
    
    def interactive_mode(self):
        """交互模式"""
        print("🎬 Wan2.1-I2V-14B-480P 训练启动器")
        print("=" * 60)
        
        # 检查环境
        if not self.check_environment():
            print("\n❌ 环境检查失败，请解决上述问题后重试")
            return
        
        # 显示选项
        self.show_training_options()
        
        # 获取用户选择
        choice = self.get_user_choice()
        if choice is None:
            print("👋 再见!")
            return
        
        config = self.training_configs[choice]
        
        # 确认配置
        if not self.confirm_training(config):
            print("❌ 训练已取消")
            return
        
        # 执行训练
        success = self.run_training(config['script'])
        
        if success:
            self.post_training_actions()
    
    def direct_mode(self, config_name):
        """直接模式"""
        if config_name not in self.training_configs:
            print(f"❌ 无效配置: {config_name}")
            print(f"可用配置: {', '.join(self.training_configs.keys())}")
            return
        
        config = self.training_configs[config_name]
        
        print(f"🚀 直接启动训练: {config['name']}")
        
        # 检查环境
        if not self.check_environment():
            print("❌ 环境检查失败")
            return
        
        # 执行训练
        success = self.run_training(config['script'])
        
        if success:
            self.post_training_actions()

def main():
    parser = argparse.ArgumentParser(description="Wan2.1-I2V-14B-480P 训练启动器")
    parser.add_argument("--config", type=str, choices=["1", "2", "3"], 
                       help="直接指定训练配置 (1:显存优化, 2:标准, 3:8×RTX3090)")
    parser.add_argument("--check-only", action="store_true", help="仅检查环境，不启动训练")
    
    args = parser.parse_args()
    
    launcher = WanI2VTrainingLauncher()
    
    if args.check_only:
        launcher.check_environment()
    elif args.config:
        launcher.direct_mode(args.config)
    else:
        launcher.interactive_mode()

if __name__ == "__main__":
    main()

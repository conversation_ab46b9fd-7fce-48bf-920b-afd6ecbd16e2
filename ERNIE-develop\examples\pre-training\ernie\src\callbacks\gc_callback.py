# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import gc

from paddleformers.trainer.trainer_callback import TrainerCallback


class GCCallback(TrainerCallback):
    def on_train_begin(self, args, state, control, **kwargs):
        if args.gc_interval > 0:
            gc.disable()

    def on_step_end(self, args, state, control, **kwargs):
        if args.gc_interval > 0 and (state.global_step % args.gc_interval == 0):
            gc.collect()

#!/usr/bin/env python3
"""
简单的LoRA推理脚本 - 使用训练好的memory_optimized_test模型
"""

import torch
import os
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def run_inference():
    # 配置参数
    lora_path = "/root/sj-fs/DiffSynth-Studio/models/train/memory_optimized_test/epoch-0.safetensors"
    prompt = "A beautiful sunset over the ocean with waves gently crashing on the shore"
    output_path = "./lora_generated_video.mp4"

    print("🚀 使用LoRA模型进行Wan2.1-T2V-1.3B推理")
    print("=" * 50)
    print(f"LoRA模型: {lora_path}")
    print(f"提示词: {prompt}")
    print(f"输出: {output_path}")
    print("=" * 50)

    # 检查LoRA模型
    if not os.path.exists(lora_path):
        print(f"❌ LoRA模型不存在: {lora_path}")
        return False

    # 设置设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🔧 使用设备: {device}")

    try:
        # 加载基础模型
        print("📦 加载Wan2.1-T2V-1.3B基础模型...")
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device=device,
            model_configs=[
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B",
                    origin_file_pattern="diffusion_pytorch_model*.safetensors",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B",
                    origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B",
                    origin_file_pattern="Wan2.1_VAE.pth",
                    offload_device="cpu"
                ),
            ],
        )

        # 加载LoRA权重
        print("🔧 加载LoRA权重...")
        pipe.load_lora(pipe.dit, lora_path)
        print("✅ LoRA权重加载完成!")

        # 启用显存管理
        pipe.enable_vram_management()

        # 生成视频
        print("🎬 开始生成视频...")
        video = pipe(
            prompt=prompt,
            height=320,
            width=576,
            num_frames=17,  # 必须是4n+1的格式
            num_inference_steps=50,
            cfg_scale=7.5,
            tiled=True,
            seed=42,
        )

        # 保存视频
        print("💾 保存视频...")
        save_video(video, output_path, fps=8)

        print("🎉 成功!")
        print(f"视频已保存到: {output_path}")
        return True

    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_inference()
    if success:
        print("\n✅ 推理完成!")
    else:
        print("\n❌ 推理失败!")

#!/usr/bin/env python3
"""
改进的完整爬虫 - 专门爬取决标公告，获取完整的厂商信息
"""

from enhanced_procurement_crawler import EnhancedProcurementCrawler
import json
import time
from datetime import datetime

class ImprovedCompleteCrawler:
    """改进的完整爬虫"""
    
    def __init__(self):
        self.crawler = EnhancedProcurementCrawler()
    
    def crawl_award_announcements(self, 
                                 keyword="国防部", 
                                 year="111",
                                 max_pages=5,
                                 page_size=50):
        """专门爬取决标公告"""
        
        print(f"🎯 开始爬取决标公告数据...")
        print(f"🔍 搜索条件：关键词='{keyword}', 年份={year}年")
        print(f"📄 最大页数: {max_pages}, 每页数量: {page_size}")
        
        # 设置搜索参数 - 专门搜索决标公告
        search_params = {
            'querySentence': keyword,
            'tenderStatusType': '決標',  # 决标公告
            'sortCol': 'AWARD_NOTICE_DATE',
            'timeRange': year,
            'pron': 'true',
            'fuzzy': 'true',
            'pageSize': str(page_size)
        }
        
        all_list_results = []
        
        # 第一步：爬取列表页数据
        print(f"\n📊 === 第一步：爬取决标公告列表 ===")
        
        for page in range(1, max_pages + 1):
            print(f"\n📄 正在爬取第 {page} 页...")
            
            search_params['pageIndex'] = str(page)
            
            try:
                search_response = self.crawler._make_request(
                    self.crawler.search_action_url, 
                    method='GET', 
                    data=search_params
                )
                
                print(f"✅ 第 {page} 页请求状态码: {search_response.status_code}")
                
                page_results = self.crawler._parse_search_results(search_response.content)
                
                if not page_results:
                    print(f"⚠️ 第 {page} 页没有数据，停止爬取")
                    break
                
                print(f"📋 第 {page} 页获取到 {len(page_results)} 笔数据")
                
                # 显示前3笔数据预览
                for i, result in enumerate(page_results[:3]):
                    print(f"  {i+1}. {result.get('title', '')[:40]}... - {result.get('agency', '')}")
                
                all_list_results.extend(page_results)
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ 爬取第 {page} 页时发生错误: {str(e)}")
                break
        
        print(f"\n✅ 列表页爬取完成，共获取 {len(all_list_results)} 笔决标公告")
        
        if not all_list_results:
            print("❌ 没有找到决标公告数据")
            return []
        
        # 第二步：爬取详细页数据
        print(f"\n🔍 === 第二步：爬取详细页数据 ===")
        
        complete_results = []
        
        for i, list_item in enumerate(all_list_results):
            current_num = i + 1
            print(f"\n🔍 正在处理第 {current_num}/{len(all_list_results)} 笔数据...")
            print(f"📋 标案名称: {list_item.get('title', '')[:50]}...")
            print(f"🏢 机关名称: {list_item.get('agency', '')}")
            print(f"📅 决标日期: {list_item.get('award_date', '')}")
            
            detail_info = None
            if list_item.get('detail_url'):
                try:
                    detail_info = self.crawler.extract_detail_fields(list_item['detail_url'])
                    if detail_info:
                        print(f"  ✅ 成功获取详细数据")
                        print(f"    📄 案号: {detail_info.announcement_info.case_number}")
                        print(f"    💰 预算: {detail_info.amount_info.budget_amount}")
                        print(f"    🏆 投标厂商数: {detail_info.bidder_count}")
                        print(f"    🏆 厂商详情数: {len(detail_info.vendors)}")
                    else:
                        print(f"  ⚠️ 详细数据为空")
                except Exception as e:
                    print(f"  ❌ 获取详细数据失败: {str(e)}")
            else:
                print(f"  ⚠️ 没有详细页链接")
            
            # 构建完整记录
            complete_record = self.build_complete_record(list_item, detail_info, current_num)
            complete_results.append(complete_record)
            
            # 每10笔保存一次临时文件
            if current_num % 10 == 0:
                temp_file = f'temp_complete_data_{current_num}.json'
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(complete_results, f, ensure_ascii=False, indent=2)
                print(f"  💾 已临时保存 {current_num} 笔数据")
            
            time.sleep(3)  # 避免被封
        
        # 保存最终结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f'complete_award_data_{keyword}_{year}年_{timestamp}.json'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(complete_results, f, ensure_ascii=False, indent=2)
        
        # 生成统计信息
        self.generate_statistics(complete_results, output_file, keyword, year)
        
        # 清理临时文件
        import os
        for temp_file in os.listdir('.'):
            if temp_file.startswith('temp_complete_data_') and temp_file.endswith('.json'):
                try:
                    os.remove(temp_file)
                    print(f"🗑️ 已清理临时文件: {temp_file}")
                except:
                    pass
        
        return complete_results, output_file
    
    def build_complete_record(self, list_item, detail_info, index):
        """构建完整记录"""
        
        record = {
            '序号': index,
            '标案名称': list_item.get('title', ''),
            '机关名称': list_item.get('agency', ''),
            '标案类型': list_item.get('tender_type', ''),
            '招标日期': list_item.get('tender_date', ''),
            '决标日期': list_item.get('award_date', ''),
            '详情页链接': list_item.get('detail_url', ''),
            '爬取时间': datetime.now().isoformat()
        }
        
        # 添加详细信息
        if detail_info:
            # 机关信息
            if detail_info.agency_info:
                record.update({
                    '标案案号': detail_info.announcement_info.case_number,
                    '机关代码': detail_info.agency_info.agency_code,
                    '单位名称': detail_info.agency_info.unit_name,
                    '机关地址': detail_info.agency_info.agency_address,
                    '联络人': detail_info.agency_info.contact_person,
                    '联络电话': detail_info.agency_info.contact_phone,
                    '传真号码': detail_info.agency_info.fax_number
                })
            
            # 招标信息
            if detail_info.announcement_info:
                record.update({
                    '招标方式': detail_info.announcement_info.tender_method,
                    '决标方式': detail_info.announcement_info.award_method,
                    '公告日期': detail_info.announcement_info.announcement_date
                })
            
            # 时间信息
            if detail_info.time_info:
                record.update({
                    '开标时间': detail_info.time_info.opening_time,
                    '原公告日期': detail_info.time_info.original_announcement_date
                })
            
            # 金额信息
            if detail_info.amount_info:
                record.update({
                    '预算金额': detail_info.amount_info.budget_amount,
                    '预算金额中文': detail_info.amount_info.budget_amount_chinese,
                    '采购金额级距': detail_info.amount_info.procurement_amount_range,
                    '预算金额是否公开': detail_info.amount_info.budget_public
                })
            
            # 履约信息
            if detail_info.performance_info:
                record.update({
                    '履约地点': detail_info.performance_info.performance_location,
                    '履约地区': detail_info.performance_info.performance_region,
                    '是否受机关补助': detail_info.performance_info.government_subsidy
                })
            
            # 厂商信息
            record.update({
                '投标厂商数': detail_info.bidder_count,
                '厂商详情': []
            })
            
            # 处理厂商详情
            if detail_info.vendors:
                vendor_details = []
                winner_vendor = ''
                winner_amount = ''
                
                for vendor in detail_info.vendors:
                    vendor_detail = {
                        '厂商名称': vendor.vendor_name,
                        '厂商代码': vendor.vendor_code,
                        '是否得标': vendor.is_winner,
                        '决标金额': vendor.award_amount,
                        '组织型态': vendor.organization_type,
                        '厂商地址': vendor.vendor_address,
                        '厂商电话': vendor.vendor_phone,
                        '履约期间': vendor.performance_period,
                        '是否为中小企业': vendor.is_sme,
                        '得标厂商国别': vendor.winner_country
                    }
                    vendor_details.append(vendor_detail)
                    
                    # 找到得标厂商
                    if vendor.is_winner == '是':
                        winner_vendor = vendor.vendor_name
                        winner_amount = vendor.award_amount
                
                record['厂商详情'] = vendor_details
                record['得标厂商'] = winner_vendor
                record['得标金额'] = winner_amount
            else:
                record['得标厂商'] = ''
                record['得标金额'] = ''
            
            # 标的分类
            if detail_info.subject_classification:
                record['标的分类'] = detail_info.subject_classification.classification_name
            else:
                record['标的分类'] = ''
        
        else:
            # 如果没有详细信息，填充空值
            empty_fields = [
                '标案案号', '机关代码', '单位名称', '机关地址', '联络人', '联络电话', '传真号码',
                '招标方式', '决标方式', '公告日期', '开标时间', '原公告日期',
                '预算金额', '预算金额中文', '采购金额级距', '预算金额是否公开',
                '履约地点', '履约地区', '是否受机关补助',
                '投标厂商数', '厂商详情', '得标厂商', '得标金额', '标的分类'
            ]
            
            for field in empty_fields:
                if field == '投标厂商数':
                    record[field] = 0
                elif field == '厂商详情':
                    record[field] = []
                else:
                    record[field] = ''
        
        return record
    
    def generate_statistics(self, results, output_file, keyword, year):
        """生成统计信息"""
        
        print(f"\n📊 === 爬取完成统计 ===")
        print(f"🔍 搜索关键词: {keyword}")
        print(f"📅 年份范围: {year}年")
        print(f"📋 总数据量: {len(results)} 笔")
        print(f"💾 数据文件: {output_file}")
        
        # 统计有完整信息的数据
        complete_info_count = sum(1 for r in results if r.get('标案案号'))
        vendor_info_count = sum(1 for r in results if r.get('厂商详情'))
        winner_count = sum(1 for r in results if r.get('得标厂商'))
        budget_count = sum(1 for r in results if r.get('预算金额'))
        
        print(f"\n📈 数据完整度:")
        print(f"  完整详细信息: {complete_info_count}/{len(results)} 笔")
        print(f"  有厂商信息: {vendor_info_count}/{len(results)} 笔")
        print(f"  有得标厂商: {winner_count}/{len(results)} 笔")
        print(f"  有预算金额: {budget_count}/{len(results)} 笔")
        
        # 统计机关分布
        agencies = {}
        for record in results:
            agency = record.get('机关名称', '')
            if agency:
                agencies[agency] = agencies.get(agency, 0) + 1
        
        print(f"\n🏢 机关分布:")
        for agency, count in sorted(agencies.items(), key=lambda x: x[1], reverse=True):
            print(f"  {agency}: {count} 笔")
        
        # 保存统计报告
        stats_report = {
            'crawl_time': datetime.now().isoformat(),
            'search_keyword': keyword,
            'year': year,
            'total_records': len(results),
            'complete_info_count': complete_info_count,
            'vendor_info_count': vendor_info_count,
            'winner_count': winner_count,
            'budget_count': budget_count,
            'agency_distribution': agencies,
            'output_file': output_file
        }
        
        stats_file = f'complete_award_stats_{keyword}_{year}年_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats_report, f, ensure_ascii=False, indent=2)
        
        print(f"📊 统计报告已保存到: {stats_file}")

def main():
    """主函数"""
    print("🎯 === 改进的完整采购爬虫 ===")
    print("专门爬取决标公告，获取完整的厂商信息")
    
    # 创建爬虫实例
    crawler = ImprovedCompleteCrawler()
    
    # 开始爬取决标公告
    results, output_file = crawler.crawl_award_announcements(
        keyword="国防部",
        year="111",
        max_pages=3,  # 爬取3页
        page_size=30  # 每页30笔
    )
    
    print(f"\n🎉 === 爬取完成 ===")
    print(f"✅ 成功爬取 {len(results)} 笔决标公告数据")
    print(f"💾 数据已保存到: {output_file}")
    print(f"📊 包含完整的厂商详情和得标信息")

if __name__ == "__main__":
    main()

#!/bin/bash

# Wan2.1-T2V-1.3B 微调快速开始脚本
# 作者: DiffSynth-Studio
# 版本: 1.0

echo "🚀 Wan2.1-T2V-1.3B 微调快速开始"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    print_error "请使用root用户运行此脚本"
    exit 1
fi

# 步骤1: 设置代理
print_step "1. 设置网络代理"
export http_proxy="*********************************************"
export https_proxy="*********************************************"
export HTTP_PROXY="$http_proxy"
export HTTPS_PROXY="$https_proxy"

print_status "代理设置完成: $https_proxy"

# 步骤2: 检查conda环境
print_step "2. 检查conda环境"
if ! command -v conda &> /dev/null; then
    print_error "conda未安装，请先安装miniconda或anaconda"
    exit 1
fi

# 初始化conda
source /root/miniconda3/etc/profile.d/conda.sh

# 检查wan_video_env环境
if conda env list | grep -q "wan_video_env"; then
    print_status "发现wan_video_env环境，正在激活..."
    conda activate wan_video_env
else
    print_warning "wan_video_env环境不存在，正在创建..."
    conda create -n wan_video_env python=3.12 -y
    conda activate wan_video_env
    
    # 安装依赖
    print_status "安装PyTorch和相关依赖..."
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
    pip install accelerate transformers diffusers
    pip install opencv-python pillow imageio
    pip install modelscope safetensors
    pip install peft
    
    # 安装DiffSynth-Studio
    print_status "安装DiffSynth-Studio..."
    pip install -e .
fi

print_status "环境准备完成: $CONDA_DEFAULT_ENV"

# 步骤3: 检查GPU
print_step "3. 检查GPU状态"
if command -v nvidia-smi &> /dev/null; then
    gpu_count=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits | head -1)
    print_status "检测到 $gpu_count 个GPU"
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader
else
    print_warning "未检测到NVIDIA GPU或nvidia-smi命令"
fi

# 步骤4: 选择操作
print_step "4. 选择要执行的操作"
echo "请选择要执行的操作:"
echo "1) 下载模型"
echo "2) 开始训练"
echo "3) LoRA推理"
echo "4) 合并模型"
echo "5) 完整流程 (下载->训练->推理)"
echo "6) 退出"

read -p "请输入选项 (1-6): " choice

case $choice in
    1)
        print_step "开始下载Wan2.1-T2V-1.3B模型..."
        python -c "
from modelscope import snapshot_download
import os

os.environ['http_proxy'] = '$http_proxy'
os.environ['https_proxy'] = '$https_proxy'

print('🚀 开始下载Wan2.1-T2V-1.3B模型...')
model_dir = snapshot_download(
    'Wan-AI/Wan2.1-T2V-1.3B',
    cache_dir='./models',
    local_dir='./models/Wan-AI/Wan2.1-T2V-1.3B'
)
print(f'✅ 模型下载完成！保存在: {model_dir}')
"
        ;;
    2)
        print_step "开始LoRA微调训练..."
        if [ ! -d "data/example_video_dataset" ]; then
            print_warning "训练数据集不存在，请先准备数据集"
            exit 1
        fi
        
        accelerate launch examples/wanvideo/model_training/train.py \
          --dataset_base_path data/example_video_dataset \
          --dataset_metadata_path data/example_video_dataset/metadata.csv \
          --height 320 \
          --width 576 \
          --dataset_repeat 50 \
          --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
          --learning_rate 1e-4 \
          --num_epochs 2 \
          --gradient_accumulation_steps 8 \
          --remove_prefix_in_ckpt "pipe.dit." \
          --output_path "./models/train/wan_lora_quickstart" \
          --lora_base_model "dit" \
          --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
          --lora_rank 16 \
          --use_gradient_checkpointing_offload
        ;;
    3)
        print_step "开始LoRA推理..."
        lora_path="./models/train/wan_lora_quickstart/epoch-0.safetensors"
        if [ ! -f "$lora_path" ]; then
            print_error "LoRA模型不存在: $lora_path"
            print_warning "请先完成训练步骤"
            exit 1
        fi
        
        python run_lora_inference.py
        ;;
    4)
        print_step "开始合并模型..."
        python -c "
import sys
sys.path.append('.')
from merge_full_model import merge_full_wan_model

base_model_dir = './models/Wan-AI/Wan2.1-T2V-1.3B'
lora_path = './models/train/wan_lora_quickstart/epoch-0.safetensors'
output_dir = './models/merged/Wan2.1-T2V-1.3B-Quickstart'

merge_full_wan_model(base_model_dir, lora_path, output_dir, alpha=1.0)
"
        ;;
    5)
        print_step "执行完整流程..."
        print_status "步骤1/4: 下载模型"
        $0 1
        
        print_status "步骤2/4: 训练模型"
        $0 2
        
        print_status "步骤3/4: LoRA推理"
        $0 3
        
        print_status "步骤4/4: 合并模型"
        $0 4
        
        print_status "🎉 完整流程执行完成！"
        ;;
    6)
        print_status "退出脚本"
        exit 0
        ;;
    *)
        print_error "无效选项，请重新运行脚本"
        exit 1
        ;;
esac

print_status "操作完成！"
echo ""
echo "📚 更多信息请查看: Wan2.1-T2V-1.3B微调完整指南.md"
echo "🎯 生成的文件位置:"
echo "  - 训练模型: ./models/train/wan_lora_quickstart/"
echo "  - 推理视频: ./lora_generated_video.mp4"
echo "  - 合并模型: ./models/merged/Wan2.1-T2V-1.3B-Quickstart/"

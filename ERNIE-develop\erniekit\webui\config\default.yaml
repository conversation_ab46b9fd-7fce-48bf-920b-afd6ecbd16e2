basic:
  language: zh
  model_name: Customization
  model_source: Local
  model_name_or_path: null
  fine_tuning: LoRA
  compute_type: bf16
  amp_master_grad: True
  disable_ckpt_quant: false
  lora_rank: 32
  lora_alpha: -1
  lora_plus_scale: 1.0
  rslora: False
  tensor_parallel_degree: 1
  pipeline_parallel_degree: 1
  sharding_parallel_degree: 1
  pipeline_parallel_config: disable_partial_send_recv enable_clear_every_step_cache
  pp_seg_method: layer:Ernie4_5_DecoderLayer|EmptyLayer
  use_sp_callback: False
  sharding: stage1
  moe_group: mp
  best_config: SFT
chat:
  port: 8188
  max_model_len: 131072
  max_new_tokens: 8192
  top_p: 0.7
  temperature: 0.95
eval:
  eval_dataset_path: null
  eval_dataset_prob: null
  eval_dataset_type: null
  eval_dataset: ['demo_sft_eval']
  max_seq_len: 8192
  batch_size: 1
export:
  max_shard_size: 5
  hf_hub_id: null
train_sft:
  ### data
  train_dataset_path: null
  train_dataset_prob: null
  train_dataset_type: null
  eval_dataset_path: null
  eval_dataset_prob: null
  eval_dataset_type: null
  train_dataset: ['demo_sft_train']
  eval_dataset: ['demo_sft_eval']
  max_seq_len: 8192
  num_samples_each_epoch: 6000000
  max_prompt_len: 2048
  ### model
  lora_rank: 32
  lora_alpha: -1
  lora_plus_scale: 1.0
  rslora: False
  ### finetuning
  # base
  stage: SFT
  recompute: False
  num_train_epochs: 1
  max_steps: 100
  batch_size: 1
  gradient_accumulation_steps: 8
  use_sp_callback: False
  # dataloader
  dataloader_num_workers: 1
  distributed_dataloader: False
  # optimizer
  lr_scheduler_type: cosine
  learning_rate: 3.0e-4
  min_lr: 0.0
  layerwise_lr_decay_bound: 1.0
  warmup_steps: 20
  optim: adamw
  offload_optim: True
  weight_decay: 0.01
  adam_epsilon: 1.0e-8
  adam_beta1: 0.9
  adam_beta2: 0.95
  release_grads: True
  scale_loss: 32768
  # output
  logging_steps: 1
  eval_steps: 100
  save_steps: 10
  evaluation_strategy: steps
  save_strategy: steps
  save_total_limit: 5
train_dpo:
  ### data
  train_dataset_path: null
  train_dataset_prob: null
  train_dataset_type: null
  eval_dataset_path: null
  eval_dataset_prob: null
  eval_dataset_type: null
  train_dataset: ['demo_dpo_train']
  eval_dataset: ['demo_dpo_eval']
  max_seq_len: 8192
  num_samples_each_epoch: 6000000
  max_prompt_len: 2048
  ### model
  lora_rank: 32
  lora_alpha: 128
  lora_plus_scale: 12
  rslora: True
  ### finetuning
  # base
  stage: DPO
  recompute: False
  num_train_epochs: 1
  max_steps: 800
  batch_size: 1
  gradient_accumulation_steps: 36
  use_sp_callback: True
  # dataloader
  distributed_dataloader: True
  dataloader_num_workers: 4
  # optimizer
  lr_scheduler_type: cosine
  learning_rate: 5.0e-7
  min_lr: 5.0e-7
  layerwise_lr_decay_bound: 0.5
  warmup_steps: 50
  optim: adamw
  offload_optim: True
  weight_decay: 0.1
  adam_epsilon: 1.0e-8
  adam_beta1: 0.9
  adam_beta2: 0.95
  release_grads: True
  scale_loss: 8192
  # output
  logging_steps: 1
  eval_steps: 20000
  save_steps: 100
  evaluation_strategy: epoch
  save_strategy: steps
  save_total_limit: 5
execute:
  # 执行配置请放在/erniekit/webui/config/execute文件夹中
  chat_yaml_path: "chat.yaml"
  export_yaml_path: "export.yaml"
  eval_yaml_path: "eval.yaml"
  train_sft_yaml_path: "train_sft.yaml"
  train_dpo_yaml_path: "train_dpo.yaml"

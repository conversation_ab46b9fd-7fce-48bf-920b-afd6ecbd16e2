#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动DeepSeek Web搜索聊天机器人 (Flask版本)
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from web_app import app

if __name__ == "__main__":
    print("正在启动DeepSeek Web搜索聊天机器人...")
    print("服务器地址: http://0.0.0.0:8088")
    print("本地访问: http://localhost:8088")

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 启动Flask应用
    app.run(
        host='0.0.0.0',
        port=8088,
        debug=False,
        threaded=True
    )

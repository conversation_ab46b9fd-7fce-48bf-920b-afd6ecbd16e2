# DeepSeek Web搜索聊天机器人

基于DeepSeek API和crawl4ai的智能Web搜索聊天机器人，使用Flask+HTML构建Web界面。

## 功能特点

- 🔍 **智能搜索**: 使用百度搜索获取相关网页URL
- 🕷️ **内容爬取**: 使用crawl4ai爬取网页内容
- 🤖 **AI分析**: 使用DeepSeek API分析内容并生成回答
- 💬 **流式对话**: 支持实时流式对话体验
- 🌐 **Web界面**: 基于Flask+HTML的现代化Web界面

## 系统架构

1. **用户提问** → 判断是否需要搜索
2. **百度搜索** → 获取相关网页URL列表
3. **内容爬取** → 使用crawl4ai爬取网页内容
4. **AI分析** → DeepSeek API分析内容并生成回答
5. **结果展示** → 在Web界面展示对话和搜索结果

## 环境配置

### 推荐方式：使用conda虚拟环境

#### 自动安装（推荐）

**Windows用户：**
```bash
setup_and_run.bat
```

**Linux/Mac用户：**
```bash
chmod +x setup_and_run.sh
./setup_and_run.sh
```

#### 手动安装

1. 创建conda虚拟环境：
```bash
conda env create -f environment.yml
```

2. 激活环境：
```bash
conda activate deepseek-websearch
```

3. 启动应用：
```bash
python run.py
```

### 传统方式：使用pip

```bash
pip install -r requirements.txt
python run.py
```

## 使用方法

### 方法1: 一键启动（推荐）
- Windows: 双击 `setup_and_run.bat`
- Linux/Mac: 运行 `./setup_and_run.sh`

### 方法2: 手动启动
```bash
conda activate deepseek-websearch
python run.py
```

### 方法3: 直接运行主程序
```bash
python web_search_demo_deepseek.py
```

## 访问地址

启动后访问: http://localhost:8088

- 服务器IP: 0.0.0.0
- 端口: 8088

## 配置说明

### DeepSeek API配置
在 `deepseek_client.py` 中修改API密钥：
```python
def __init__(self, api_key: str = "your-api-key-here"):
```

### 搜索参数配置
在 `web_search_demo_deepseek.py` 中可以调整：
- 搜索结果数量
- 内容长度限制
- 超时设置

## 文件说明

### 核心文件
- `web_app.py`: Flask主程序文件，包含Web API和核心逻辑
- `templates/index.html`: HTML前端界面
- `deepseek_client.py`: DeepSeek API客户端，处理AI对话
- `baidu_search_utils.py`: 百度搜索工具类，获取搜索结果URL
- `run.py`: 启动脚本

### 环境配置文件
- `environment.yml`: conda环境配置文件
- `requirements.txt`: pip依赖包列表
- `setup_and_run.bat`: Windows一键安装启动脚本
- `setup_and_run.sh`: Linux/Mac一键安装启动脚本

### 依赖的原有模块
- `../cookbook/crawl_utils.py`: 网页内容爬取工具（复用原有代码）

## 依赖说明

- **flask**: Web应用框架
- **flask-cors**: 跨域请求支持
- **crawl4ai**: 网页爬取工具
- **requests**: HTTP请求库
- **cookbook/crawl_utils.py**: 原有的爬虫工具类

## 注意事项

1. 确保网络连接正常，能够访问百度搜索和DeepSeek API
2. 爬取网页时请遵守robots.txt规则
3. API调用可能产生费用，请注意使用量
4. 建议在生产环境中配置适当的错误处理和日志记录

## 故障排除

### 常见问题

1. **无法启动服务**
   - 检查端口8088是否被占用
   - 确认所有依赖包已正确安装

2. **搜索结果为空**
   - 检查网络连接
   - 确认百度搜索可以正常访问

3. **API调用失败**
   - 检查DeepSeek API密钥是否正确
   - 确认API服务可用

4. **爬取内容失败**
   - 某些网站可能有反爬虫机制
   - 检查目标网站是否可以正常访问

## 更新日志

### v1.0.0 (2025-07-04)
- 初始版本发布
- 支持百度搜索和内容爬取
- 集成DeepSeek API
- 提供Web界面

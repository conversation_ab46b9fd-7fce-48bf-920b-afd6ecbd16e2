#!/usr/bin/env python3
"""
测试8×RTX 3090训练的LoRA模型
"""

import os
import sys
import torch
from pathlib import Path

def test_8x3090_lora_model():
    """测试8×RTX 3090训练的LoRA模型"""
    print("🧪 测试8×RTX 3090训练的LoRA模型...")
    
    try:
        from diffsynth import save_video
        from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
        from safetensors.torch import load_file
        
        # 创建pipeline
        print("📦 加载基础模型...")
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",
            model_configs=[
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="diffusion_pytorch_model*.safetensors",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="Wan2.1_VAE.pth",
                    offload_device="cpu"
                ),
            ],
        )
        
        # 检查8×RTX 3090训练的LoRA文件
        lora_path = "./models/train/8x3090_fast/epoch-0.safetensors"
        if not os.path.exists(lora_path):
            print(f"❌ 8×RTX 3090 LoRA文件不存在: {lora_path}")
            return False
        
        print("🔧 加载8×RTX 3090训练的LoRA权重...")
        lora_weights = load_file(lora_path)
        print(f"✅ 8×RTX 3090 LoRA权重加载成功，包含 {len(lora_weights)} 个参数")
        
        # 显示LoRA权重信息
        print("📊 LoRA权重详情:")
        for key in list(lora_weights.keys())[:10]:  # 显示前10个
            print(f"   {key}: {lora_weights[key].shape}")
        
        # 启用显存管理
        pipe.enable_vram_management()
        
        print("🎬 生成测试视频...")
        
        # 生成多个测试视频
        test_prompts = [
            "一只可爱的小猫在阳光下的花园里玩耍，画面清晰，色彩鲜艳",
            "美丽的日落风景，海浪轻拍海岸，天空呈现橙红色",
            "城市夜景，霓虹灯闪烁，车流如织"
        ]
        
        for i, prompt in enumerate(test_prompts):
            print(f"🎥 生成视频 {i+1}/3: {prompt[:20]}...")
            
            video = pipe(
                prompt=prompt,
                negative_prompt="模糊，低质量，变形",
                seed=42 + i,
                height=320,
                width=576,
                num_frames=25,
                num_inference_steps=10,
                tiled=True,
            )
            
            # 保存视频
            output_path = f"test_8x3090_output_{i+1}.mp4"
            save_video(video, output_path, fps=8, quality=5)
            
            # 显示文件信息
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024 / 1024  # MB
                print(f"   ✅ 视频已保存: {output_path} ({file_size:.2f} MB)")
        
        print("🎉 8×RTX 3090训练的LoRA模型测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 8×RTX 3090 LoRA模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_training_results():
    """比较不同训练结果"""
    print("\n📊 比较训练结果...")
    
    models_to_compare = [
        ("单GPU训练", "./models/train/memory_optimized_test/epoch-0.safetensors"),
        ("8×RTX 3090训练", "./models/train/8x3090_fast/epoch-0.safetensors")
    ]
    
    from safetensors.torch import load_file
    
    for model_name, model_path in models_to_compare:
        if os.path.exists(model_path):
            try:
                weights = load_file(model_path)
                file_size = os.path.getsize(model_path) / 1024 / 1024  # MB
                
                print(f"\n🔍 {model_name}:")
                print(f"   文件路径: {model_path}")
                print(f"   文件大小: {file_size:.2f} MB")
                print(f"   参数数量: {len(weights)}")
                
                # 显示权重统计
                total_params = 0
                for key, tensor in weights.items():
                    total_params += tensor.numel()
                
                print(f"   总参数量: {total_params:,}")
                
            except Exception as e:
                print(f"   ❌ 加载失败: {e}")
        else:
            print(f"\n❌ {model_name}: 文件不存在 ({model_path})")

def main():
    print("🎬 8×RTX 3090训练模型测试")
    print("=" * 60)
    
    # 检查环境
    print("🔍 检查环境...")
    if not torch.cuda.is_available():
        print("⚠️  CUDA不可用，将使用CPU (速度会很慢)")
    else:
        gpu_count = torch.cuda.device_count()
        print(f"✅ CUDA可用，检测到 {gpu_count} 块GPU")
        for i in range(min(gpu_count, 8)):
            gpu_name = torch.cuda.get_device_name(i)
            memory_gb = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: {gpu_name} ({memory_gb:.1f}GB)")
    
    # 测试8×RTX 3090训练的LoRA模型
    success = test_8x3090_lora_model()
    
    # 比较训练结果
    compare_training_results()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 8×RTX 3090训练模型测试完成！")
        print("💡 8×RTX 3090多GPU训练已成功验证")
        print("🚀 您现在可以使用以下命令进行高分辨率训练:")
        print("   bash train_8x3090_high_res.sh")
    else:
        print("⚠️  测试失败，请检查错误信息")
    print("=" * 60)

if __name__ == "__main__":
    main()

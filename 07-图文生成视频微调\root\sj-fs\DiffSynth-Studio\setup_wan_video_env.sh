#!/bin/bash

# DiffSynth-Studio Wan视频模型微调环境自动设置脚本
# 使用Python 3.12创建虚拟环境

set -e  # 遇到错误时退出

echo "🚀 开始设置DiffSynth-Studio Wan视频模型微调环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Python 3.12是否可用
echo -e "${BLUE}📋 检查Python版本...${NC}"
if command -v python3.12 &> /dev/null; then
    PYTHON_CMD="python3.12"
    echo -e "${GREEN}✅ 找到Python 3.12${NC}"
elif command -v python &> /dev/null && python --version | grep -q "3.12"; then
    PYTHON_CMD="python"
    echo -e "${GREEN}✅ 当前Python版本为3.12${NC}"
else
    echo -e "${RED}❌ 未找到Python 3.12，请先安装Python 3.12${NC}"
    exit 1
fi

# 显示Python版本
PYTHON_VERSION=$($PYTHON_CMD --version)
echo -e "${GREEN}当前Python版本: $PYTHON_VERSION${NC}"

# 设置环境名称
ENV_NAME="wan_video_env"
ENV_PATH="./$ENV_NAME"

# 检查是否已存在环境
if [ -d "$ENV_PATH" ]; then
    echo -e "${YELLOW}⚠️  虚拟环境 $ENV_NAME 已存在${NC}"
    read -p "是否删除现有环境并重新创建? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🗑️  删除现有环境...${NC}"
        rm -rf "$ENV_PATH"
    else
        echo -e "${BLUE}使用现有环境...${NC}"
        source "$ENV_PATH/bin/activate"
    fi
fi

# 创建虚拟环境
if [ ! -d "$ENV_PATH" ]; then
    echo -e "${BLUE}🏗️  创建虚拟环境 $ENV_NAME...${NC}"
    $PYTHON_CMD -m venv "$ENV_PATH"
fi

# 激活虚拟环境
echo -e "${BLUE}🔄 激活虚拟环境...${NC}"
source "$ENV_PATH/bin/activate"

# 验证环境
echo -e "${BLUE}📋 验证虚拟环境...${NC}"
VENV_PYTHON_VERSION=$(python --version)
echo -e "${GREEN}虚拟环境Python版本: $VENV_PYTHON_VERSION${NC}"

# 升级pip
echo -e "${BLUE}⬆️  升级pip...${NC}"
pip install --upgrade pip

# 安装DiffSynth-Studio
echo -e "${BLUE}📦 安装DiffSynth-Studio...${NC}"
pip install -e .

# 安装训练相关依赖
echo -e "${BLUE}📦 安装训练相关依赖...${NC}"

# DeepSpeed (用于14B模型全量训练)
echo -e "${YELLOW}安装DeepSpeed...${NC}"
pip install deepspeed

# Flash Attention (可选，用于加速)
echo -e "${YELLOW}尝试安装Flash Attention...${NC}"
pip install flash-attn || echo -e "${YELLOW}⚠️  Flash Attention安装失败，将使用默认注意力机制${NC}"

# 其他有用的依赖
echo -e "${YELLOW}安装其他依赖...${NC}"
pip install opencv-python pillow

# 创建测试脚本
echo -e "${BLUE}📝 创建环境测试脚本...${NC}"
cat > test_wan_video_env.py << 'EOF'
#!/usr/bin/env python3
"""
DiffSynth-Studio Wan视频模型环境测试脚本
"""
import sys
import torch

def test_environment():
    print("=" * 60)
    print("🧪 DiffSynth-Studio Wan视频模型环境测试")
    print("=" * 60)
    
    # Python版本
    print(f"🐍 Python版本: {sys.version}")
    print(f"📍 Python路径: {sys.executable}")
    
    # PyTorch信息
    print(f"🔥 PyTorch版本: {torch.__version__}")
    print(f"🎯 CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"🚀 CUDA版本: {torch.version.cuda}")
        print(f"💾 GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
            memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   显存: {memory_total:.1f} GB")
    
    # DiffSynth-Studio导入测试
    try:
        from diffsynth import save_video
        from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
        print("✅ DiffSynth-Studio核心模块导入成功")
    except ImportError as e:
        print(f"❌ DiffSynth-Studio导入失败: {e}")
        return False
    
    # Accelerate测试
    try:
        import accelerate
        print(f"✅ Accelerate版本: {accelerate.__version__}")
    except ImportError:
        print("❌ Accelerate未安装")
    
    # DeepSpeed测试
    try:
        import deepspeed
        print(f"✅ DeepSpeed版本: {deepspeed.__version__}")
    except ImportError:
        print("❌ DeepSpeed未安装")
    
    # Flash Attention测试
    try:
        import flash_attn
        print(f"✅ Flash Attention可用")
    except ImportError:
        print("⚠️  Flash Attention未安装 (可选)")
    
    print("=" * 60)
    print("🎉 环境测试完成!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_environment()
EOF

# 运行测试
echo -e "${BLUE}🧪 运行环境测试...${NC}"
python test_wan_video_env.py

# 配置accelerate
echo -e "${BLUE}⚙️  配置Accelerate...${NC}"
echo -e "${YELLOW}请根据您的硬件配置回答以下问题:${NC}"
accelerate config

# 创建激活脚本
echo -e "${BLUE}📝 创建环境激活脚本...${NC}"
cat > activate_wan_video_env.sh << EOF
#!/bin/bash
# 激活DiffSynth-Studio Wan视频模型微调环境

echo "🚀 激活Wan视频模型微调环境..."
source $ENV_PATH/bin/activate
echo "✅ 环境已激活: \$(python --version)"
echo "💡 使用 'deactivate' 命令退出环境"
echo "💡 运行 'python test_wan_video_env.py' 测试环境"
EOF

chmod +x activate_wan_video_env.sh

# 创建示例数据集下载脚本
echo -e "${BLUE}📝 创建数据集下载脚本...${NC}"
cat > download_example_dataset.sh << 'EOF'
#!/bin/bash
# 下载示例视频数据集

echo "📥 下载示例视频数据集..."
mkdir -p data
modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
echo "✅ 数据集下载完成: ./data/example_video_dataset"
EOF

chmod +x download_example_dataset.sh

# 完成提示
echo -e "${GREEN}🎉 环境设置完成!${NC}"
echo ""
echo -e "${BLUE}📋 使用说明:${NC}"
echo -e "1. 激活环境: ${YELLOW}source activate_wan_video_env.sh${NC}"
echo -e "2. 测试环境: ${YELLOW}python test_wan_video_env.py${NC}"
echo -e "3. 下载示例数据集: ${YELLOW}./download_example_dataset.sh${NC}"
echo -e "4. 开始训练: ${YELLOW}bash examples/wanvideo/model_training/lora/Wan2.1-T2V-1.3B.sh${NC}"
echo ""
echo -e "${BLUE}📚 更多信息请查看:${NC}"
echo -e "- 环境设置指南: ${YELLOW}setup_python312_env.md${NC}"
echo -e "- Wan模型文档: ${YELLOW}examples/wanvideo/README_zh.md${NC}"
echo ""
echo -e "${GREEN}🚀 现在您可以开始进行Wan视频模型的微调了!${NC}"

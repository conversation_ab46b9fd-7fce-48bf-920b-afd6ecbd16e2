#!/bin/bash

# 8×RTX 3090极度优化的全量微调脚本
# 使用最小分辨率和最大显存优化

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:128
export TOKENIZERS_PARALLELISM=false

echo "🚀 开始8×RTX 3090极度优化全量微调..."
echo "⚠️  使用最小分辨率和最大显存优化配置"
echo "📊 配置参数:"
echo "   - 分辨率: 128×224 (极小)"
echo "   - 梯度累积: 16步"
echo "   - 学习率: 1e-6 (极小)"
echo "   - 数据重复: 5次"

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 128 \
    --width 224 \
    --dataset_repeat 5 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --learning_rate 1e-6 \
    --num_epochs 1 \
    --gradient_accumulation_steps 16 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/8x3090_full_finetune_extreme" \
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090极度优化全量微调完成"

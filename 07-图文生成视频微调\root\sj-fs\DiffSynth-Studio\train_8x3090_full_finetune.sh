#!/bin/bash

# 8×RTX 3090全量微调脚本
# 针对显存优化的全量微调配置

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

echo "🚀 开始8×RTX 3090全量微调训练..."
echo "⚠️  全量微调需要大量显存，已启用最大显存优化"

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 256 \
    --width 448 \
    --dataset_repeat 10 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --learning_rate 5e-6 \
    --num_epochs 1 \
    --gradient_accumulation_steps 8 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/8x3090_full_finetune" \
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090全量微调完成"

import requests
from bs4 import BeautifulSoup
import time
import logging
from urllib.parse import urljoin, parse_qs, urlparse
import re

class ProcurementCrawler:
    def __init__(self):
        self.base_url = "https://web.pcc.gov.tw"
        self.search_url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def search_procurement(self, keyword="", tender_types=None, year_range=None, page_size=20, max_pages=5):
        """
        搜索采购信息
        
        Args:
            keyword: 搜索关键词
            tender_types: 标案类型列表 ['招标', '决标', '公开閱覽及公開徵求', '政府採購預告']
            year_range: 年份范围，如 "114年1至12月"
            page_size: 每页数量
            max_pages: 最大页数
        """
        if tender_types is None:
            tender_types = ['招标', '决标']
        
        results = []
        
        try:
            # 首先获取搜索页面
            response = self.session.get(self.search_url)
            response.raise_for_status()
            
            # 解析页面获取必要的参数
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 构建搜索参数
            search_data = {
                'searchMethod': 'true',
                'searchTarget': 'ATM',
                'orgName': '',
                'orgId': '',
                'hid_1': '1',
                'textfield': keyword,
                'searchType': 'basic',
                'method': 'search',
                'isSpdt': 'N',
                'pageIndex': '1',
                'recordCountPerPage': str(page_size)
            }
            
            # 添加标案类型
            if '招标' in tender_types:
                search_data['tenderStatus'] = '1'
            if '决标' in tender_types:
                search_data['tenderStatus'] = '2'
            if '公开閱覽及公開徵求' in tender_types:
                search_data['tenderStatus'] = '3'
            if '政府採購預告' in tender_types:
                search_data['tenderStatus'] = '4'
            
            # 执行搜索
            for page in range(1, max_pages + 1):
                search_data['pageIndex'] = str(page)
                
                self.logger.info(f"正在抓取第 {page} 页数据...")
                
                # 发送POST请求
                search_response = self.session.post(self.search_url, data=search_data)
                search_response.raise_for_status()
                
                # 解析搜索结果
                page_results = self._parse_search_results(search_response.content)
                
                if not page_results:
                    self.logger.info(f"第 {page} 页没有更多数据，停止抓取")
                    break
                
                results.extend(page_results)
                
                # 添加延迟避免被封
                time.sleep(1)
                
        except Exception as e:
            self.logger.error(f"搜索过程中发生错误: {str(e)}")
            
        return results
    
    def _parse_search_results(self, html_content):
        """解析搜索结果页面"""
        results = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找结果表格
            result_table = soup.find('table', {'class': 'table_block tender_table'})
            if not result_table:
                return results
            
            # 解析每一行数据
            rows = result_table.find_all('tr')[1:]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 4:
                    try:
                        # 提取基本信息
                        title_cell = cells[1]
                        title_link = title_cell.find('a')
                        
                        if title_link:
                            title = title_link.get_text(strip=True)
                            detail_url = urljoin(self.base_url, title_link.get('href', ''))
                        else:
                            title = title_cell.get_text(strip=True)
                            detail_url = ""
                        
                        agency = cells[0].get_text(strip=True)
                        tender_type = cells[2].get_text(strip=True)
                        announcement_date = cells[3].get_text(strip=True)
                        
                        # 获取案号（如果有的话）
                        case_number = ""
                        case_number_match = re.search(r'案號：([^\\s]+)', title)
                        if case_number_match:
                            case_number = case_number_match.group(1)
                        
                        result = {
                            'title': title,
                            'agency': agency,
                            'case_number': case_number,
                            'tender_type': tender_type,
                            'announcement_date': announcement_date,
                            'url': detail_url,
                            'content': ''  # 详细内容需要进一步抓取
                        }
                        
                        results.append(result)
                        
                    except Exception as e:
                        self.logger.warning(f"解析行数据时出错: {str(e)}")
                        continue
                        
        except Exception as e:
            self.logger.error(f"解析搜索结果时出错: {str(e)}")
            
        return results
    
    def get_detail_content(self, detail_url):
        """获取详细内容"""
        try:
            if not detail_url:
                return ""
            
            response = self.session.get(detail_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取主要内容
            content_div = soup.find('div', {'class': 'tender_table'})
            if content_div:
                return content_div.get_text(strip=True)
            else:
                return soup.get_text(strip=True)[:1000]  # 限制长度
                
        except Exception as e:
            self.logger.warning(f"获取详细内容失败 {detail_url}: {str(e)}")
            return ""
    
    def crawl_with_details(self, keyword="", tender_types=None, year_range=None, page_size=20, max_pages=5):
        """爬取数据并获取详细内容"""
        results = self.search_procurement(keyword, tender_types, year_range, page_size, max_pages)
        
        # 为每个结果获取详细内容
        for i, result in enumerate(results):
            self.logger.info(f"正在获取第 {i+1}/{len(results)} 条详细内容...")
            result['content'] = self.get_detail_content(result['url'])
            time.sleep(0.5)  # 添加延迟
            
        return results


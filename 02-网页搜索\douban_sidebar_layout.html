<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI小助手 - 智能问答</title>
    <!-- 版本标识，强制浏览器重新加载 -->
    <meta name="version" content="2025-06-27-v2">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
            backdrop-filter: blur(10px);
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.8);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .user-avatar.clickable {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-avatar.clickable:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .user-name {
            font-weight: 600;
            color: #1f2937;
            font-size: 16px;
        }

        .new-chat-btn {
            width: calc(100% - 32px);
            padding: 16px 20px;
            margin: 20px 16px;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .new-chat-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            padding: 0 12px;
        }

        .nav-section {
            margin-bottom: 32px;
        }

        .nav-title {
            padding: 12px 16px;
            font-size: 13px;
            color: #6b7280;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.8px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 14px 16px;
            margin: 4px 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #374151;
            text-decoration: none;
            font-size: 15px;
            font-weight: 500;
        }

        .nav-item:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateX(4px);
        }

        .nav-item.active {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
            color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }

        .nav-item i {
            width: 24px;
            margin-right: 14px;
            font-size: 18px;
        }

        .chat-history {
            flex: 1;
            overflow-y: auto;
        }

        .chat-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin: 4px 8px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #374151;
            font-size: 14px;
            position: relative;
        }

        .chat-item .chat-title {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .chat-item .delete-chat-btn {
            background: none;
            border: none;
            color: #ef4444;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
            padding: 4px;
            border-radius: 4px;
            margin-left: 8px;
        }

        .chat-item .delete-chat-btn:hover {
            background: rgba(239, 68, 68, 0.1);
        }

        .chat-item:hover {
            background: rgba(102, 126, 234, 0.08);
            transform: translateX(2px);
        }

        .chat-item.active {
            background: rgba(102, 126, 234, 0.12);
            color: #667eea;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            color: #6b7280;
            text-align: center;
            background: rgba(255, 255, 255, 0.5);
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            max-width: none;
            width: 100%;
            border-radius: 0 20px 0 0;
            box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
        }

        .main-header {
            padding: 24px 32px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        .header-title {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-title i {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 22px;
        }

        .header-actions {
            display: flex;
            gap: 16px;
        }

        .header-btn {
            padding: 12px 20px;
            border: 2px solid transparent;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #667eea;
            transition: all 0.3s ease;
        }

        .header-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 32px 48px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
            overflow-y: auto;
            max-width: none;
        }

        .chat-area.has-messages {
            justify-content: flex-start;
            align-items: stretch;
        }

        .chat-area.welcome {
            justify-content: center;
            align-items: center;
        }

        .welcome-section {
            text-align: center;
            max-width: 700px;
        }

        .welcome-title {
            font-size: 42px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 48px;
            line-height: 1.6;
        }
        
        .input-section {
            width: 100%;
            max-width: 95%;
            position: relative;
        }

        .input-container {
            position: relative;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 2px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .input-container:focus-within {
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
        }

        .main-input {
            width: 100%;
            padding: 20px 70px 20px 24px;
            border: none;
            border-radius: 20px;
            font-size: 16px;
            resize: none;
            outline: none;
            min-height: 80px;
            max-height: 300px;
            line-height: 1.6;
            background: transparent;
            color: #1f2937;
        }

        .main-input::placeholder {
            color: #9ca3af;
        }

        .input-actions {
            position: absolute;
            right: 16px;
            bottom: 16px;
            display: flex;
            gap: 10px;
        }

        .input-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .send-btn:disabled {
            background: #d1d5db;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .attach-btn {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .attach-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }

        /* 模式选择按钮样式 */
        .mode-selection {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 20px;
        }

        .mode-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            color: #667eea;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .mode-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.4);
            transform: translateY(-2px);
        }

        .mode-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: transparent;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .mode-btn i {
            font-size: 14px;
        }

        /* 固定底部输入框样式 */
        .fixed-input-section {
            position: fixed;
            bottom: 0;
            left: 280px;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 24px 48px;
            z-index: 100;
            box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.1);
        }

        .suggestions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 16px;
            margin-top: 40px;
            max-width: 900px;
        }

        .suggestion-item {
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }

        .suggestion-item:hover {
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            transform: translateY(-4px);
        }

        .suggestion-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-size: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .suggestion-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 6px;
            font-size: 16px;
        }

        .suggestion-desc {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 聊天消息样式 */
        .chat-messages {
            flex: 1;
            padding: 32px 0 200px 0; /* 底部留出空间给固定输入框 */
            max-width: 900px;
            margin: 0 auto;
            width: 100%;
        }

        .message {
            margin-bottom: 32px;
            display: flex;
            gap: 16px;
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.assistant .message-avatar {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .message-content {
            flex: 1;
            max-width: calc(100% - 60px);
        }

        .message-bubble {
            padding: 20px 24px;
            border-radius: 20px;
            line-height: 1.7;
            word-wrap: break-word;
            font-size: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: 24px;
        }

        .message.assistant .message-bubble {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: #374151;
            border: 2px solid rgba(102, 126, 234, 0.1);
            margin-right: 24px;
        }

        .message-time {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 8px;
            text-align: right;
            font-weight: 500;
        }

        .message.assistant .message-time {
            text-align: left;
        }

        /* 进度条和状态 */
        .search-progress {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-radius: 16px;
            padding: 20px;
            margin: 20px 0;
            max-width: calc(100% - 60px);
            margin-right: 70px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 16px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            font-size: 15px;
            color: #667eea;
            margin-bottom: 12px;
            font-weight: 600;
        }

        .progress-stats {
            font-size: 13px;
            color: #6b7280;
            font-weight: 500;
        }

        /* 文章卡片 */
        .article-results {
            margin-top: 16px;
        }

        .article-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }

        .article-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .article-meta {
            display: flex;
            gap: 16px;
            margin-bottom: 8px;
            font-size: 12px;
            color: #6b7280;
        }

        .article-content {
            font-size: 13px;
            color: #4b5563;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .article-link {
            color: #667eea;
            text-decoration: none;
            font-size: 12px;
            font-weight: 500;
        }

        .article-link:hover {
            text-decoration: underline;
        }

        /* AI回答样式 */
        .ai-answer {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #0ea5e9;
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
            max-width: calc(100% - 48px);
            margin-right: 60px;
        }

        .ai-answer-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            font-weight: 600;
            color: #0c4a6e;
        }

        .ai-answer-header i {
            color: #0ea5e9;
        }

        .ai-answer-meta {
            background: rgba(14, 165, 233, 0.1);
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #0c4a6e;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .ai-answer-content {
            color: #0c4a6e;
            line-height: 1.7;
        }

        /* Markdown样式 */
        .ai-answer-content h1, .ai-answer-content h2, .ai-answer-content h3 {
            color: #333;
            margin: 20px 0 10px 0;
        }

        .ai-answer-content h1 {
            font-size: 24px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .ai-answer-content h2 {
            font-size: 20px;
            color: #667eea;
        }

        .ai-answer-content h3 {
            font-size: 16px;
            color: #555;
        }

        .ai-answer-content strong {
            color: #333;
            font-weight: 600;
        }

        .ai-answer-content em {
            color: #666;
            font-style: italic;
        }

        .ai-answer-content ul {
            margin: 15px 0;
            padding-left: 20px;
        }

        .ai-answer-content li {
            margin: 5px 0;
            line-height: 1.6;
        }

        /* 代码块样式 */
        .ai-answer-content pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            color: #24292e;
        }

        .ai-answer-content code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            color: #d73a49;
        }

        .ai-answer-content pre code {
            background: transparent;
            padding: 0;
            color: #24292e;
        }

        /* 思考过程样式 */
        .thinking-process {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            margin: 15px 0;
            border-radius: 5px;
            overflow: hidden;
        }

        .thinking-process-header {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thinking-process-content {
            padding: 15px;
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }





        /* 用户登录相关样式 */
        .login-prompt {
            display: flex;
            align-items: center;
            gap: 8px;
            width: 100%;
        }

        .user-logged-in {
            display: flex;
            align-items: center;
            gap: 8px;
            width: 100%;
        }

        .user-details {
            flex: 1;
        }

        .user-credits {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .credits-value {
            font-weight: bold;
            color: #667eea;
        }

        .vip-badge {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            margin-left: 4px;
        }

        .login-btn, .logout-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .login-btn {
            background: #667eea;
            color: white;
        }

        .login-btn:hover {
            background: #5a67d8;
        }

        .logout-btn {
            background: #f56565;
            color: white;
        }

        .logout-btn:hover {
            background: #e53e3e;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 0;
            max-width: 400px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
        }

        .auth-form {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #374151;
        }

        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .auth-btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .auth-btn:hover {
            background: #5a67d8;
        }

        .auth-switch {
            text-align: center;
            margin-top: 16px;
            color: #6b7280;
        }

        .auth-switch a {
            color: #667eea;
            text-decoration: none;
        }

        .auth-switch a:hover {
            text-decoration: underline;
        }

        /* VIP升级样式 */
        .vip-plans {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            padding: 20px;
        }

        .vip-plan {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .vip-plan:hover {
            border-color: #667eea;
            background: #f8faff;
        }

        .vip-plan h4 {
            margin: 0 0 8px 0;
            color: #1f2937;
        }

        .vip-plan .price {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
            margin: 8px 0;
        }

        .vip-plan .features {
            font-size: 12px;
            color: #6b7280;
            margin: 0;
        }

        /* VIP升级按钮样式 */
        .vip-upgrade-section {
            padding: 16px;
            border-top: 1px solid #e5e7eb;
        }

        .vip-upgrade-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .vip-upgrade-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }

        .vip-upgrade-btn i {
            font-size: 16px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 240px;
            }

            .chat-area {
                padding: 24px 32px;
            }

            .main-input {
                padding: 18px 60px 18px 20px;
            }

            .suggestions {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
                position: fixed;
                left: -200px;
                z-index: 1001;
                transition: left 0.3s ease;
                height: 100vh;
                box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            }

            .sidebar.open {
                left: 0;
            }

            .main-content {
                width: 100%;
                margin-left: 0;
            }

            .main-header {
                padding: 16px 20px;
                position: relative;
            }

            .header-title {
                font-size: 20px;
            }

            .chat-area {
                padding: 20px 16px;
            }

            .welcome-title {
                font-size: 28px;
            }

            .welcome-subtitle {
                font-size: 16px;
            }

            .suggestions {
                grid-template-columns: 1fr;
                gap: 12px;
                margin-top: 24px;
            }

            .suggestion-item {
                padding: 16px;
            }

            .main-input {
                padding: 16px 50px 16px 16px;
                font-size: 15px;
            }

            .input-btn {
                width: 36px;
                height: 36px;
            }

            .chat-messages {
                padding: 20px 0 180px 0;
                max-width: 100%;
            }

            .message {
                margin-bottom: 24px;
            }

            .message-content {
                max-width: calc(100% - 50px);
            }

            .message-bubble {
                padding: 16px 18px;
            }

            .progress-container {
                max-width: calc(100% - 50px);
                margin-right: 60px;
            }

            .ai-answer-container {
                max-width: calc(100% - 40px);
                margin-right: 50px;
            }

            /* 固定输入框移动端适配 */
            .fixed-input-section {
                left: 0;
                right: 0;
                padding: 16px 20px;
            }

            .user-center-modal {
                width: 95%;
                max-height: 90vh;
                margin: 10px;
            }

            .user-center-header {
                padding: 16px 16px 12px 16px;
            }

            .user-center-content {
                padding: 16px;
            }

            .user-info-card {
                flex-direction: column;
                text-align: center;
                gap: 16px;
            }

            .user-stats {
                justify-content: center;
                flex-wrap: wrap;
            }

            .modal-content {
                width: 95%;
                margin: 10px;
            }
        }

        @media (max-width: 640px) {
            .app-container {
                position: relative;
            }

            .sidebar {
                width: 280px;
                left: -280px;
            }

            .main-header {
                padding: 12px 16px;
            }

            .header-title {
                font-size: 18px;
            }

            .header-actions {
                gap: 8px;
            }

            .header-btn {
                padding: 8px 12px;
                font-size: 13px;
            }

            .chat-area {
                padding: 16px 12px;
            }

            .welcome-title {
                font-size: 24px;
            }

            .welcome-subtitle {
                font-size: 14px;
            }

            .input-section {
                max-width: 100%;
            }

            .main-input {
                padding: 14px 45px 14px 14px;
                font-size: 14px;
            }

            .input-btn {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .input-actions {
                right: 12px;
                bottom: 12px;
            }

            .mode-selection {
                flex-direction: column;
                gap: 8px;
            }

            .mode-btn {
                padding: 10px 16px;
                font-size: 14px;
            }

            .chat-messages {
                padding: 16px 0 160px 0;
            }

            .message-avatar {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .message-content {
                max-width: calc(100% - 45px);
            }

            .message-bubble {
                padding: 12px 16px;
                font-size: 14px;
            }

            .progress-container {
                max-width: calc(100% - 45px);
                margin-right: 50px;
                padding: 16px;
            }

            .ai-answer-container {
                max-width: calc(100% - 35px);
                margin-right: 40px;
                padding: 16px;
            }

            /* 固定输入框小屏幕适配 */
            .fixed-input-section {
                padding: 12px 16px;
            }

            .user-center-content .user-avatar {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }

            .user-stats {
                gap: 16px;
            }

            .recharge-form {
                flex-direction: column;
                gap: 8px;
            }

            .card-input-group {
                gap: 16px;
            }

            .input-actions {
                flex-direction: column;
            }
        }

        @media (max-width: 480px) {
            .welcome-title {
                font-size: 20px;
            }

            .welcome-subtitle {
                font-size: 13px;
            }

            .suggestion-item {
                padding: 12px;
            }

            .suggestion-title {
                font-size: 14px;
            }

            .suggestion-desc {
                font-size: 12px;
            }

            .main-input {
                padding: 12px 40px 12px 12px;
                font-size: 13px;
            }

            .input-btn {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            .input-actions {
                right: 10px;
                bottom: 10px;
            }

            /* 固定输入框超小屏幕适配 */
            .fixed-input-section {
                padding: 10px 12px;
            }

            .mobile-menu-btn {
                width: 40px;
                height: 40px;
                top: 16px;
                left: 16px;
                font-size: 16px;
            }
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1002;
            width: 44px;
            height: 44px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 12px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            color: #667eea;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        /* 移动端遮罩层 */
        .mobile-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mobile-overlay.show {
            display: block;
            opacity: 1;
        }

        /* 个人中心样式 */
        .user-center {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-center-modal {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .user-center-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 24px 16px 24px;
            border-bottom: 2px solid #e5e7eb;
        }

        .user-center-header h2 {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .close-btn {
            background: #f3f4f6;
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            color: #6b7280;
            transition: all 0.2s;
        }

        .close-btn:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .user-center-content {
            padding: 24px;
        }

        .user-info-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-center-content .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: 600;
        }

        .user-details h3 {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 12px 0;
        }

        .user-stats {
            display: flex;
            gap: 24px;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        /* VIP充值区域样式 */
        .vip-recharge-section {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(255, 215, 0, 0.3);
        }

        .vip-recharge-section h3 {
            font-size: 18px;
            font-weight: 600;
            color: #92400e;
            margin: 0 0 20px 0;
        }

        .recharge-form {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .card-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #d97706;
            border-radius: 8px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.9);
        }

        .card-input:focus {
            outline: none;
            border-color: #92400e;
            box-shadow: 0 0 0 3px rgba(146, 64, 14, 0.1);
        }

        .recharge-btn {
            padding: 12px 20px;
            background: #92400e;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .recharge-btn:hover {
            background: #78350f;
            transform: translateY(-1px);
        }

        .recharge-tips {
            font-size: 12px;
            color: #92400e;
        }

        .recharge-tips p {
            margin: 4px 0;
        }

        /* 管理员区域样式 */
        .admin-section {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(239, 68, 68, 0.3);
        }

        .admin-section h3 {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin: 0 0 20px 0;
        }

        .admin-btn {
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
        }

        .admin-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .credit-records-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .credit-records-section h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 20px 0;
        }

        .records-container {
            max-height: 400px;
            overflow-y: auto;
        }

        .record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-info {
            flex: 1;
        }

        .record-description {
            font-size: 14px;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .record-time {
            font-size: 12px;
            color: #6b7280;
        }

        .record-amount {
            font-size: 16px;
            font-weight: 600;
        }

        .record-amount.positive {
            color: #059669;
        }

        .record-amount.negative {
            color: #dc2626;
        }

        .loading {
            text-align: center;
            color: #6b7280;
            padding: 20px;
        }

        /* VIP模态框中的卡密充值样式 */
        .vip-card-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .vip-card-section h4 {
            font-size: 18px;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-recharge-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .card-input-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .card-input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            color: #1f2937;
            transition: all 0.3s ease;
        }

        .card-input:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
        }

        .input-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .card-info {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 16px;
            backdrop-filter: blur(10px);
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-item i {
            color: #ffd700;
            width: 16px;
        }

        /* 测试卡密区域样式 */
        .test-card-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            border: 1px dashed rgba(255, 215, 0, 0.5);
        }

        .test-card-header {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #ffd700;
            font-weight: 600;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .test-card-code {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }

        .test-card-code:hover {
            background: rgba(0, 0, 0, 0.4);
            transform: translateY(-1px);
        }

        .test-card-code code {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            color: #ffd700;
            letter-spacing: 0.5px;
        }

        .test-card-code i {
            color: rgba(255, 255, 255, 0.7);
            transition: color 0.3s ease;
        }

        .test-card-code:hover i {
            color: #ffd700;
        }

        .test-card-tip {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            text-align: center;
        }

        /* VIP特权样式 */
        .vip-benefits {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(240, 147, 251, 0.3);
        }

        .vip-benefits h4 {
            font-size: 18px;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .benefit-item {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
            font-weight: 500;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .benefit-item:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .benefit-item i {
            color: #ffd700;
            font-size: 18px;
        }

        /* VIP模态框新样式 */
        .vip-modal-content {
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .vip-modal-body {
            padding: 0 24px 24px 24px;
        }

        .section-description {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .vip-card-section .section-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 24px;
        }

        .card-input-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 24px;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .card-input {
            padding-right: 50px !important;
        }

        .clear-btn {
            position: absolute;
            right: 12px;
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .clear-btn:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .primary-recharge-btn {
            padding: 16px 24px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .primary-recharge-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }

        .card-benefits {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .benefit-row {
            display: flex;
            align-items: center;
            gap: 12px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .benefit-row i {
            color: #ffd700;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .benefit-row strong {
            color: #ffd700;
        }

        /* 新的测试卡密样式 */
        .test-card-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .test-card-item {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .test-card-item:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
        }

        .test-card-key {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #374151;
            background: #f3f4f6;
            padding: 8px 12px;
            border-radius: 6px;
            margin-bottom: 12px;
            word-break: break-all;
        }

        .test-card-actions {
            display: flex;
            gap: 8px;
        }

        .copy-btn, .use-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .copy-btn {
            background: #f3f4f6;
            color: #6b7280;
        }

        .copy-btn:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .use-btn {
            background: #667eea;
            color: white;
        }

        .use-btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        /* 新的VIP特权样式 */
        .benefit-icon {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
        }

        .benefit-content {
            flex: 1;
        }

        .benefit-title {
            font-weight: 600;
            font-size: 14px;
            color: white;
            margin-bottom: 4px;
        }

        .benefit-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.4;
        }

        .benefit-item i {
            color: #ffd700;
            font-size: 18px;
            width: 20px;
            text-align: center;
        }

        /* 按钮样式 */
        .test-btn {
            padding: 12px 16px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #1f2937;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 48px;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }

        .test-btn:hover {
            background: linear-gradient(135deg, #ffed4e, #ffd700);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }

        .recharge-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .recharge-btn:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            .benefits-grid {
                grid-template-columns: 1fr;
            }

            .input-actions {
                flex-direction: column;
            }

            .card-input-group {
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn" onclick="toggleMobileSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 移动端遮罩层 -->
    <div class="mobile-overlay" id="mobileOverlay" onclick="closeMobileSidebar()"></div>

    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header" id="sidebarHeader">
                <!-- 未登录状态 -->
                <div class="login-prompt" id="loginPrompt">
                    <div class="user-avatar">?</div>
                    <div class="user-name">未登录</div>
                    <button class="login-btn" onclick="showLoginModal()">登录</button>
                </div>

                <!-- 已登录状态 -->
                <div class="user-logged-in" id="userLoggedIn" style="display: none;">
                    <div class="user-avatar clickable" id="userAvatar" onclick="showUserCenter()" title="点击查看个人中心">🤖</div>
                    <div class="user-details">
                        <div class="user-name" id="userName">用户名</div>
                        <div class="user-credits">
                            <span class="credits-label">积分:</span>
                            <span class="credits-value" id="userCredits">0</span>
                            <span class="vip-badge" id="vipBadge" style="display: none;">VIP</span>
                        </div>
                    </div>
                    <button class="logout-btn" onclick="logout()">登出</button>
                </div>
            </div>
            
            <button class="new-chat-btn">
                <i class="fas fa-plus"></i>
                新对话
            </button>
            
            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-title">智能助手</div>
                    <a href="#" class="nav-item active" data-prompt="default">
                        <i class="fas fa-comments"></i>
                        AI 对话
                    </a>
                    <a href="#" class="nav-item" data-prompt="search">
                        <i class="fas fa-search"></i>
                        智能搜索
                    </a>
                    <a href="#" class="nav-item" data-prompt="assistant">
                        <i class="fas fa-robot"></i>
                        AI 助理
                    </a>
                    <a href="#" class="nav-item" data-prompt="creative">
                        <i class="fas fa-lightbulb"></i>
                        创意助手
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-title">专业工具</div>
                    <a href="#" class="nav-item" data-prompt="translate">
                        <i class="fas fa-language"></i>
                        翻译
                    </a>
                    <a href="#" class="nav-item" data-prompt="code">
                        <i class="fas fa-code"></i>
                        代码助手
                    </a>
                    <a href="#" class="nav-item" data-prompt="writing">
                        <i class="fas fa-pen"></i>
                        写作助手
                    </a>
                    <a href="#" class="nav-item" data-prompt="analysis">
                        <i class="fas fa-chart-line"></i>
                        数据分析
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-title">学习工具</div>
                    <a href="#" class="nav-item" data-prompt="explain">
                        <i class="fas fa-graduation-cap"></i>
                        知识解答
                    </a>
                    <a href="#" class="nav-item" data-prompt="summary">
                        <i class="fas fa-file-text"></i>
                        内容总结
                    </a>
                    <a href="#" class="nav-item" data-prompt="brainstorm">
                        <i class="fas fa-brain"></i>
                        头脑风暴
                    </a>
                    <a href="#" class="nav-item" data-prompt="plan">
                        <i class="fas fa-tasks"></i>
                        计划制定
                    </a>
                </div>
                
                <div class="chat-history">
                    <div class="nav-title">最近对话</div>
                    <div id="chatHistoryList">
                        <!-- 动态加载的聊天历史 -->
                    </div>
                </div>
            </div>

            <!-- VIP升级按钮 -->
            <div class="vip-upgrade-section" id="vipUpgradeSection" style="display: none;">
                <button class="vip-upgrade-btn" onclick="showVipModal()">
                    <i class="fas fa-crown"></i>
                    升级VIP
                </button>
            </div>

            <div class="sidebar-footer">
                <div>AI小助手</div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="main-header">
                <div class="header-title">AI小助手</div>
                <div class="header-actions">
                    <button class="header-btn">
                        <i class="fas fa-cog"></i>
                        设置
                    </button>
                    <button class="header-btn">
                        <i class="fas fa-download"></i>
                        下载对话
                    </button>
                </div>
            </div>

            <!-- 个人中心弹窗 -->
            <div class="user-center" id="userCenter" style="display: none;" onclick="hideUserCenter()">
                <div class="user-center-modal" onclick="event.stopPropagation()">
                    <div class="user-center-header">
                        <h2><i class="fas fa-user-circle"></i> 个人中心</h2>
                        <button class="close-btn" onclick="hideUserCenter()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="user-center-content">
                        <div class="user-info-card">
                            <div class="user-avatar" id="userCenterAvatar">Y</div>
                            <div class="user-details">
                                <h3 id="userCenterName">用户名</h3>
                                <div class="user-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">当前积分</span>
                                        <span class="stat-value" id="userCenterCredits">1000</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">会员状态</span>
                                        <span class="stat-value" id="userCenterVipStatus">普通用户</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- VIP充值区域已移至升级VIP按钮 -->

                        <!-- 管理员区域 -->
                        <div class="admin-section" id="adminSection" style="display: none;">
                            <h3><i class="fas fa-cog"></i> 管理员面板</h3>
                            <div class="admin-container">
                                <button onclick="showAdminPanel()" class="admin-btn">
                                    <i class="fas fa-tools"></i> 卡密管理
                                </button>
                            </div>
                        </div>

                        <div class="credit-records-section">
                            <h3><i class="fas fa-history"></i> 积分记录</h3>
                            <div class="records-container" id="creditRecords">
                                <div class="loading">加载中...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-area welcome" id="chatArea">
                <div class="welcome-section" id="welcomeSection">
                    <h1 class="welcome-title">下午好，Yangchl</h1>
                    <p class="welcome-subtitle">我是AI小助手，有什么可以帮您的吗？</p>

                    <div class="input-section">
                        <div class="input-container">
                            <textarea
                                class="main-input"
                                id="questionInput"
                                placeholder="请输入您的问题，例如：算家云在哪里？微短剧行业发展如何？"
                                rows="1"
                            ></textarea>
                            <div class="input-actions">
                                <button class="input-btn attach-btn" id="attachBtn">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <input type="number" id="maxPages" value="3" min="1" max="20" style="display: none;" />
                                <button class="input-btn send-btn" id="sendBtn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>


                    </div>
                    
                    <div class="suggestions">
                        <div class="suggestion-item" onclick="fillInput('AI 助理功能介绍')" style="cursor: pointer;">
                            <div class="suggestion-icon" style="background: #eef2ff; color: #667eea;">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="suggestion-title">AI 助理</div>
                            <div class="suggestion-desc">智能对话，回答问题</div>
                        </div>
                        
                        <div class="suggestion-item" onclick="fillInput('帮我搜索2024年最新的人工智能发展趋势和突破性技术')" style="cursor: pointer;">
                            <div class="suggestion-icon" style="background: #f0fdf4; color: #16a34a;">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="suggestion-title">AI 前沿</div>
                            <div class="suggestion-desc">搜索最新AI技术动态</div>
                        </div>

                        <div class="suggestion-item" onclick="fillInput('写一个Python爬虫程序，爬取豆瓣电影Top250的电影信息')" style="cursor: pointer;">
                            <div class="suggestion-icon" style="background: #fef3c7; color: #d97706;">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="suggestion-title">代码助手</div>
                            <div class="suggestion-desc">Python爬虫开发</div>
                        </div>

                        <div class="suggestion-item" onclick="fillInput('请将以下英文翻译成中文：Artificial intelligence is transforming our world in unprecedented ways')" style="cursor: pointer;">
                            <div class="suggestion-icon" style="background: #fce7f3; color: #be185d;">
                                <i class="fas fa-language"></i>
                            </div>
                            <div class="suggestion-title">翻译助手</div>
                            <div class="suggestion-desc">中英文互译服务</div>
                        </div>

                        <div class="suggestion-item" onclick="fillInput('分析这组销售数据的趋势：Q1: 100万, Q2: 120万, Q3: 95万, Q4: 150万')" style="cursor: pointer;">
                            <div class="suggestion-icon" style="background: #e0f2fe; color: #0284c7;">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="suggestion-title">数据分析</div>
                            <div class="suggestion-desc">销售数据趋势分析</div>
                        </div>

                        <div class="suggestion-item" onclick="fillInput('帮我写一篇关于环保主题的800字议论文，要求观点鲜明、论据充分')" style="cursor: pointer;">
                            <div class="suggestion-icon" style="background: #f3e8ff; color: #9333ea;">
                                <i class="fas fa-pen-fancy"></i>
                            </div>
                            <div class="suggestion-title">写作助手</div>
                            <div class="suggestion-desc">议论文写作指导</div>
                        </div>
                    </div>
                </div>

                <!-- 聊天消息区域 -->
                <div class="chat-messages" id="chatMessages" style="display: none;">
                    <!-- 消息将在这里动态添加 -->
                </div>
            </div>

            <!-- 固定底部输入框（聊天模式下显示） -->
            <div class="fixed-input-section" id="fixedInputSection" style="display: none;">
                <div class="input-container">
                    <textarea
                        class="main-input"
                        id="chatInput"
                        placeholder="请输入您的问题..."
                        rows="1"
                    ></textarea>
                    <div class="input-actions">
                        <button class="input-btn attach-btn" id="chatAttachBtn">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <input type="number" id="chatMaxPages" value="3" min="1" max="20" style="display: none;" />
                        <button class="input-btn send-btn" id="chatSendBtn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- 登录注册模态框 -->
    <div class="modal" id="authModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">用户登录</h3>
                <button class="modal-close" onclick="closeAuthModal()">&times;</button>
            </div>

            <!-- 登录表单 -->
            <div class="auth-form" id="loginForm">
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" id="loginUsername" placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" id="loginPassword" placeholder="请输入密码">
                </div>
                <button class="auth-btn" onclick="login()">登录</button>
                <p class="auth-switch">
                    还没有账号？<a href="#" onclick="switchToRegister()">立即注册</a>
                </p>
            </div>

            <!-- 注册表单 -->
            <div class="auth-form" id="registerForm" style="display: none;">
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" id="registerUsername" placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label>邮箱</label>
                    <input type="email" id="registerEmail" placeholder="请输入邮箱">
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" id="registerPassword" placeholder="请输入密码（至少6位）">
                </div>
                <button class="auth-btn" onclick="register()">注册</button>
                <p class="auth-switch">
                    已有账号？<a href="#" onclick="switchToLogin()">立即登录</a>
                </p>
            </div>
        </div>
    </div>

    <!-- VIP升级模态框 -->
    <div class="modal" id="vipModal" style="display: none;">
        <div class="modal-content vip-modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-crown"></i> VIP升级</h3>
                <button class="modal-close" onclick="closeVipModal()">&times;</button>
            </div>

            <div class="vip-modal-body">
                <!-- 卡密充值区域 -->
                <div class="vip-card-section">
                    <h4><i class="fas fa-key"></i> VIP卡密充值</h4>
                    <p class="section-description">输入有效的VIP卡密即可获得VIP权限和积分奖励</p>

                    <div class="card-input-container">
                        <div class="input-wrapper">
                            <input type="text" id="vipCardKeyInput" placeholder="请输入VIP卡密 (格式: sk-xxxxxxxx)" class="card-input">
                            <button onclick="clearCardInput()" class="clear-btn" title="清空输入">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <button onclick="rechargeVipFromModal()" class="primary-recharge-btn">
                            <i class="fas fa-magic"></i>
                            <span>立即充值</span>
                        </button>
                    </div>

                    <div class="card-benefits">
                        <div class="benefit-row">
                            <i class="fas fa-gift"></i>
                            <span>每张卡密包含 <strong>10,000 积分</strong></span>
                        </div>
                        <div class="benefit-row">
                            <i class="fas fa-crown"></i>
                            <span>获得 <strong>VIP会员权限</strong></span>
                        </div>
                        <div class="benefit-row">
                            <i class="fas fa-calendar-alt"></i>
                            <span>会员有效期 <strong>30天</strong></span>
                        </div>
                    </div>
                </div>

                <!-- 测试卡密区域 -->
                <div class="test-card-section">
                    <h4><i class="fas fa-flask"></i> 测试卡密</h4>
                    <p class="section-description">用于测试功能的免费卡密</p>

                    <div class="test-card-container">
                        <div class="test-card-item" onclick="useTestCard('sk-dce33ba66f28420ff3509009f046b44c')">
                            <div class="test-card-key">sk-dce33ba66f28420ff3509009f046b44c</div>
                            <div class="test-card-actions">
                                <button class="copy-btn" onclick="event.stopPropagation(); copyTestCard('sk-dce33ba66f28420ff3509009f046b44c')">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                                <button class="use-btn" onclick="event.stopPropagation(); useTestCard('sk-dce33ba66f28420ff3509009f046b44c')">
                                    <i class="fas fa-arrow-right"></i> 使用
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- VIP特权说明 -->
                <div class="vip-benefits">
                    <h4><i class="fas fa-star"></i> VIP特权</h4>
                    <div class="benefits-grid">
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-infinity"></i>
                            </div>
                            <div class="benefit-content">
                                <div class="benefit-title">无限积分</div>
                                <div class="benefit-desc">享受无限制的AI对话和搜索</div>
                            </div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-search-plus"></i>
                            </div>
                            <div class="benefit-content">
                                <div class="benefit-title">高级搜索</div>
                                <div class="benefit-desc">更深度的搜索和分析功能</div>
                            </div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="benefit-content">
                                <div class="benefit-title">优先处理</div>
                                <div class="benefit-desc">享受更快的响应速度</div>
                            </div>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="benefit-content">
                                <div class="benefit-title">专属客服</div>
                                <div class="benefit-desc">7x24小时专属客服支持</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局用户状态
        let currentUser = null;
        let currentPromptMode = 'default';
        let chatHistory = [];
        let currentChatId = null;

        // 提示词配置
        const promptTemplates = {
            default: {
                name: 'AI 对话',
                icon: 'fas fa-comments',
                systemPrompt: '你是一个友好、专业的AI小助手，能够回答各种问题并提供帮助。',
                description: '我是AI小助手，有什么可以帮您的吗？',
                placeholder: '请输入您的问题...'
            },
            search: {
                name: '智能搜索',
                icon: 'fas fa-search',
                systemPrompt: '你是一个专业的搜索助手，擅长帮助用户找到准确的信息和答案。',
                description: '专业的搜索助手，帮您找到准确的信息和答案',
                placeholder: '请输入您要搜索的内容...'
            },
            assistant: {
                name: 'AI 助理',
                icon: 'fas fa-robot',
                systemPrompt: '你是一个高效的个人助理，能够帮助用户处理各种日常任务和问题。',
                description: '您的高效个人助理，帮您处理各种日常任务',
                placeholder: '我可以帮您处理什么任务？'
            },
            creative: {
                name: '创意助手',
                icon: 'fas fa-lightbulb',
                systemPrompt: '你是一个富有创意的助手，擅长头脑风暴、创意写作和创新思维。',
                description: '富有创意的助手，擅长头脑风暴和创新思维',
                placeholder: '让我们一起发挥创意...'
            },
            translate: {
                name: '翻译',
                icon: 'fas fa-language',
                systemPrompt: '你是一个专业的翻译助手，能够准确翻译各种语言，并解释语言文化背景。',
                description: '专业翻译助手，支持多种语言互译',
                placeholder: '请输入需要翻译的文本...'
            },
            code: {
                name: '代码助手',
                icon: 'fas fa-code',
                systemPrompt: '你是一个专业的编程助手，擅长各种编程语言，能够帮助编写、调试和优化代码。',
                description: '专业编程助手，帮您编写、调试和优化代码',
                placeholder: '请描述您的编程需求...'
            },
            writing: {
                name: '写作助手',
                icon: 'fas fa-pen',
                systemPrompt: '你是一个专业的写作助手，能够帮助用户改进文章、润色文字、提供写作建议。',
                description: '专业写作助手，帮您改进文章和润色文字',
                placeholder: '请输入您需要帮助的写作内容...'
            },
            analysis: {
                name: '数据分析',
                icon: 'fas fa-chart-line',
                systemPrompt: '你是一个数据分析专家，能够帮助用户分析数据、制作图表、提供洞察。',
                description: '数据分析专家，帮您分析数据并提供洞察',
                placeholder: '请描述您的数据分析需求...'
            },
            explain: {
                name: '知识解答',
                icon: 'fas fa-graduation-cap',
                systemPrompt: '你是一个知识渊博的教师，能够用简单易懂的方式解释复杂概念。',
                description: '知识渊博的教师，用简单易懂的方式解释复杂概念',
                placeholder: '请输入您想了解的知识点...'
            },
            summary: {
                name: '内容总结',
                icon: 'fas fa-file-text',
                systemPrompt: '你是一个专业的内容总结助手，能够快速提取关键信息并生成简洁的总结。',
                description: '专业内容总结助手，快速提取关键信息',
                placeholder: '请输入需要总结的内容...'
            },
            brainstorm: {
                name: '头脑风暴',
                icon: 'fas fa-brain',
                systemPrompt: '你是一个创新思维专家，擅长头脑风暴，能够从多个角度提供创意想法。',
                description: '创新思维专家，从多个角度提供创意想法',
                placeholder: '让我们一起头脑风暴...'
            },
            plan: {
                name: '计划制定',
                icon: 'fas fa-tasks',
                systemPrompt: '你是一个专业的计划制定助手，能够帮助用户制定详细、可执行的计划。',
                description: '专业计划制定助手，帮您制定详细可执行的计划',
                placeholder: '请描述您需要制定的计划...'
            }
        };

        // 页面加载时的初始化已合并到 initializeApp 函数中

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                // 首先检查本地存储的用户信息
                const storedUser = localStorage.getItem('currentUser');
                if (storedUser) {
                    try {
                        currentUser = JSON.parse(storedUser);
                        updateUserUI();
                        return;
                    } catch (e) {
                        localStorage.removeItem('currentUser');
                    }
                }

                // 尝试从后端获取用户信息（如果有后端的话）
                const response = await fetch('/api/user/info', {
                    credentials: 'include',
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        currentUser = data.user;
                        localStorage.setItem('currentUser', JSON.stringify(currentUser));
                        updateUserUI();
                        return;
                    }
                }
            } catch (error) {
                console.log('未登录或登录已过期');
            }

            // 如果没有登录信息，确保UI显示未登录状态
            currentUser = null;
            updateUserUI();
        }

        // 初始化导航
        function initializeNavigation() {
            const navItems = document.querySelectorAll('.nav-item[data-prompt]');
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const promptMode = this.getAttribute('data-prompt');
                    switchPromptMode(promptMode);

                    // 更新活跃状态
                    navItems.forEach(nav => nav.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        }

        // 切换提示词模式
        function switchPromptMode(mode, createNewChat = true) {
            if (promptTemplates[mode]) {
                currentPromptMode = mode;
                const template = promptTemplates[mode];

                // 更新页面标题
                const headerTitle = document.querySelector('.header-title');
                if (headerTitle) {
                    headerTitle.innerHTML = `<i class="${template.icon}"></i> ${template.name}`;
                }

                // 更新输入框占位符
                const mainInput = document.querySelector('.main-input');
                if (mainInput) {
                    mainInput.placeholder = template.placeholder;
                }

                // 更新欢迎信息
                updateWelcomeMessage(template);

                // 如果需要创建新聊天（默认行为），则开始新对话
                if (createNewChat && window.chatApp) {
                    window.chatApp.startNewChat();
                }

                console.log(`切换到${template.name}模式`);
            }
        }

        // 更新欢迎信息
        function updateWelcomeMessage(template) {
            const welcomeTitle = document.querySelector('.welcome-title');
            const welcomeSubtitle = document.querySelector('.welcome-subtitle');

            if (welcomeTitle && welcomeSubtitle) {
                welcomeTitle.innerHTML = `${template.name}`;
                welcomeSubtitle.textContent = template.description || template.systemPrompt;
            }
        }

        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('authToken');
            const headers = {
                'Content-Type': 'application/json'
            };
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            return headers;
        }

        // 加载聊天历史
        async function loadChatHistory() {
            try {
                const response = await fetch('/api/chat/history', {
                    credentials: 'include',
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        chatHistory = data.history || [];
                        // 确保每个聊天记录都有必要的字段
                        chatHistory = chatHistory.map(chat => ({
                            ...chat,
                            title: chat.title || '未命名对话',
                            fullTitle: chat.fullTitle || chat.title || '未命名对话',
                            mode: chat.mode || 'default',
                            messages: chat.messages || []
                        }));
                        updateChatHistoryUI();
                        console.log('从服务器加载聊天历史:', chatHistory.length, '条记录');
                    }
                } else if (response.status === 401) {
                    console.log('需要登录才能加载聊天历史');
                    // 未登录时从本地存储获取
                    loadChatHistoryFromLocal();
                }
            } catch (error) {
                console.log('加载聊天历史失败:', error);
                // 如果加载失败，从本地存储获取
                loadChatHistoryFromLocal();
            }
        }

        // 从本地存储加载聊天历史
        function loadChatHistoryFromLocal() {
            const stored = localStorage.getItem('chatHistory');
            if (stored) {
                try {
                    chatHistory = JSON.parse(stored);
                    updateChatHistoryUI();
                } catch (error) {
                    console.log('解析本地聊天历史失败:', error);
                    chatHistory = [];
                }
            }
        }

        // 保存聊天历史到本地存储
        function saveChatHistoryToLocal() {
            try {
                localStorage.setItem('chatHistory', JSON.stringify(chatHistory));
            } catch (error) {
                console.log('保存聊天历史失败:', error);
            }
        }

        // 添加新的聊天记录
        async function addChatToHistory(title, mode = 'default') {
            const chatId = Date.now().toString();
            const newChat = {
                id: chatId,
                title: title.length > 20 ? title.substring(0, 20) + '...' : title,
                fullTitle: title,
                mode: mode,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                messages: []
            };

            // 添加到历史记录开头
            chatHistory.unshift(newChat);

            // 限制历史记录数量（最多保存20条）
            if (chatHistory.length > 20) {
                chatHistory = chatHistory.slice(0, 20);
            }

            currentChatId = chatId;
            updateChatHistoryUI();
            saveChatHistoryToLocal();

            // 保存到服务器
            await saveChatToServer(newChat);

            return chatId;
        }

        // 保存聊天记录到服务器
        async function saveChatToServer(chatRecord) {
            try {
                const response = await fetch('/api/chat/history', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...getAuthHeaders()
                    },
                    credentials: 'include',
                    body: JSON.stringify(chatRecord)
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('聊天记录保存成功:', data);
                } else {
                    console.error('保存聊天记录失败:', response.status);
                }
            } catch (error) {
                console.error('保存聊天记录失败:', error);
            }
        }

        // 更新聊天历史UI
        function updateChatHistoryUI() {
            const historyList = document.getElementById('chatHistoryList');
            if (!historyList) return;

            historyList.innerHTML = '';

            if (chatHistory.length === 0) {
                historyList.innerHTML = '<div class="chat-item" style="color: #9ca3af; font-style: italic;">暂无聊天记录</div>';
                return;
            }

            chatHistory.forEach((chat, index) => {
                const chatItem = document.createElement('div');
                chatItem.className = `chat-item ${chat.id === currentChatId ? 'active' : ''}`;
                chatItem.setAttribute('data-chat-id', chat.id);

                const template = promptTemplates[chat.mode] || promptTemplates.default;

                chatItem.innerHTML = `
                    <i class="${template.icon}" style="margin-right: 8px; font-size: 12px; color: #667eea;"></i>
                    <span class="chat-title" title="${chat.fullTitle}">${chat.title}</span>
                    <button class="delete-chat-btn" onclick="deleteChatHistory('${chat.id}')" style="margin-left: auto; background: none; border: none; color: #ef4444; cursor: pointer; opacity: 0; transition: opacity 0.2s;">
                        <i class="fas fa-trash" style="font-size: 10px;"></i>
                    </button>
                `;

                // 点击切换聊天
                chatItem.addEventListener('click', function(e) {
                    if (e.target.closest('.delete-chat-btn')) return;
                    switchToChat(chat.id);
                });

                // 鼠标悬停显示删除按钮
                chatItem.addEventListener('mouseenter', function() {
                    const deleteBtn = this.querySelector('.delete-chat-btn');
                    if (deleteBtn) deleteBtn.style.opacity = '1';
                });

                chatItem.addEventListener('mouseleave', function() {
                    const deleteBtn = this.querySelector('.delete-chat-btn');
                    if (deleteBtn) deleteBtn.style.opacity = '0';
                });

                historyList.appendChild(chatItem);
            });
        }

        // 切换到指定聊天
        function switchToChat(chatId) {
            const chat = chatHistory.find(c => c.id === chatId);
            if (!chat) return;

            currentChatId = chatId;

            // 切换到对应的模式，但不创建新聊天
            switchPromptMode(chat.mode, false);

            // 更新活跃状态
            updateChatHistoryUI();

            // 加载该聊天的消息历史
            if (window.chatApp) {
                window.chatApp.loadChatMessages(chat);
            }

            console.log('切换到聊天:', chat.title, '消息数量:', chat.messages?.length || 0);
        }

        // 删除聊天历史
        async function deleteChatHistory(chatId) {
            if (confirm('确定要删除这个聊天记录吗？')) {
                // 从本地删除
                chatHistory = chatHistory.filter(chat => chat.id !== chatId);

                if (currentChatId === chatId) {
                    currentChatId = null;
                }

                updateChatHistoryUI();
                saveChatHistoryToLocal();

                // 从服务器删除
                try {
                    const response = await fetch(`/api/chat/history/${chatId}`, {
                        method: 'DELETE',
                        headers: getAuthHeaders(),
                        credentials: 'include'
                    });

                    if (response.ok) {
                        console.log('聊天记录删除成功');
                    } else {
                        console.error('删除聊天记录失败:', response.status);
                    }
                } catch (error) {
                    console.error('删除聊天记录失败:', error);
                }
            }
        }

        // 更新用户界面
        function updateUserUI() {
            const loginPrompt = document.getElementById('loginPrompt');
            const userLoggedIn = document.getElementById('userLoggedIn');
            const vipUpgradeSection = document.getElementById('vipUpgradeSection');

            if (currentUser) {
                loginPrompt.style.display = 'none';
                userLoggedIn.style.display = 'flex';

                document.getElementById('userName').textContent = currentUser.username;

                // 检查是否是VIP用户且未过期
                let isUnlimited = false;
                if (currentUser.is_vip && currentUser.vip_expire_date) {
                    const vipExpire = new Date(currentUser.vip_expire_date);
                    if (vipExpire >= new Date()) {
                        isUnlimited = true;
                    }
                }

                // 显示积分
                document.getElementById('userCredits').textContent = isUnlimited ? '∞' : currentUser.credits.toLocaleString();
                document.getElementById('userAvatar').textContent = currentUser.username.charAt(0).toUpperCase();

                if (isUnlimited) {
                    document.getElementById('vipBadge').style.display = 'inline';
                    vipUpgradeSection.style.display = 'block'; // VIP用户也显示升级按钮（用于延长VIP）
                    // 修改按钮文字为"延长VIP"
                    const upgradeBtn = vipUpgradeSection.querySelector('.vip-upgrade-btn');
                    upgradeBtn.innerHTML = '<i class="fas fa-crown"></i> 延长VIP';
                } else {
                    document.getElementById('vipBadge').style.display = 'none';
                    vipUpgradeSection.style.display = 'block'; // 普通用户显示升级按钮
                    // 修改按钮文字为"升级VIP"
                    const upgradeBtn = vipUpgradeSection.querySelector('.vip-upgrade-btn');
                    upgradeBtn.innerHTML = '<i class="fas fa-crown"></i> 升级VIP';
                }
            } else {
                loginPrompt.style.display = 'flex';
                userLoggedIn.style.display = 'none';
                vipUpgradeSection.style.display = 'none';
            }
        }

        // 显示登录模态框
        function showLoginModal() {
            document.getElementById('authModal').style.display = 'flex';
            switchToLogin();
        }

        // 关闭认证模态框
        function closeAuthModal() {
            document.getElementById('authModal').style.display = 'none';
        }

        // 切换到登录表单
        function switchToLogin() {
            document.getElementById('modalTitle').textContent = '用户登录';
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('registerForm').style.display = 'none';
        }

        // 切换到注册表单
        function switchToRegister() {
            document.getElementById('modalTitle').textContent = '用户注册';
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
        }

        // 用户登录
        async function login() {
            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value.trim();

            if (!username || !password) {
                alert('请输入用户名和密码');
                return;
            }

            try {
                // 尝试后端登录
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ username, password })
                });

                // 检查响应状态
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        currentUser = data.user;
                        // 保存认证token
                        if (data.token) {
                            localStorage.setItem('authToken', data.token);
                        }
                        localStorage.setItem('currentUser', JSON.stringify(currentUser));
                        updateUserUI();
                        closeAuthModal();
                        alert('登录成功！');
                        return;
                    }
                } else {
                    console.log('后端登录失败，状态码:', response.status);
                }
            } catch (error) {
                console.log('后端登录请求失败，使用本地模拟登录:', error.message);
            }

            // 本地模拟登录（用于演示）
            const users = JSON.parse(localStorage.getItem('users') || '{}');

            if (users[username] && users[username].password === password) {
                currentUser = users[username];
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                updateUserUI();
                closeAuthModal();
                alert('登录成功！');
            } else if (username === 'admin' && password === 'admin') {
                // 默认管理员账户
                currentUser = {
                    username: 'admin',
                    email: '<EMAIL>',
                    is_vip: true,
                    is_admin: true,
                    credits: 10000,
                    vip_expire_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
                };
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                updateUserUI();
                closeAuthModal();
                alert('管理员登录成功！');
            } else {
                alert('用户名或密码错误');
            }
        }

        // 用户注册
        async function register() {
            const username = document.getElementById('registerUsername').value.trim();
            const email = document.getElementById('registerEmail').value.trim();
            const password = document.getElementById('registerPassword').value.trim();

            if (!username || !email || !password) {
                alert('请填写所有字段');
                return;
            }

            if (password.length < 6) {
                alert('密码长度至少6位');
                return;
            }

            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, email, password })
                });

                // 检查响应状态
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        alert('注册成功！请登录');
                        switchToLogin();
                        return;
                    }
                } else {
                    console.log('后端注册失败，状态码:', response.status);
                }
            } catch (error) {
                console.log('后端注册失败，使用本地模拟注册');

                // 本地模拟注册
                const users = JSON.parse(localStorage.getItem('users') || '{}');

                if (users[username]) {
                    alert('用户名已存在');
                    return;
                }

                // 创建新用户
                users[username] = {
                    username: username,
                    email: email,
                    password: password,
                    is_vip: false,
                    is_admin: false,
                    credits: 1000, // 新用户默认1000积分
                    register_date: new Date().toISOString()
                };

                localStorage.setItem('users', JSON.stringify(users));
                alert('注册成功！请登录');
                switchToLogin();
            }
        }

        // 用户登出
        async function logout() {
            try {
                const response = await fetch('/api/logout', {
                    method: 'POST',
                    credentials: 'include',
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    currentUser = null;
                    localStorage.removeItem('currentUser');
                    updateUserUI();
                    alert('已退出登录');
                    return;
                }
            } catch (error) {
                console.log('后端登出失败，清除本地状态');
            }

            // 清除本地状态
            currentUser = null;
            localStorage.removeItem('currentUser');
            localStorage.removeItem('authToken');
            updateUserUI();
            alert('已退出登录');
        }

        // 显示VIP升级模态框
        function showVipModal() {
            const modal = document.getElementById('vipModal');
            const modalTitle = modal.querySelector('.modal-header h3');

            // 检查用户是否是VIP
            let isVip = false;
            if (currentUser && currentUser.is_vip && currentUser.vip_expire_date) {
                const vipExpire = new Date(currentUser.vip_expire_date);
                if (vipExpire >= new Date()) {
                    isVip = true;
                }
            }

            // 根据VIP状态设置标题
            modalTitle.textContent = isVip ? '延长VIP会员' : 'VIP升级';

            modal.style.display = 'flex';
        }

        // 关闭VIP模态框
        function closeVipModal() {
            document.getElementById('vipModal').style.display = 'none';
        }

        // 在线支付功能已移除，仅保留卡密充值功能

        class DoubanAIChat {
            constructor() {
                this.textarea = document.getElementById('questionInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.attachBtn = document.getElementById('attachBtn');
                this.maxPagesInput = document.getElementById('maxPages');
                this.chatArea = document.getElementById('chatArea');
                this.welcomeSection = document.getElementById('welcomeSection');
                this.chatMessages = document.getElementById('chatMessages');


                // 固定输入框元素
                this.fixedInputSection = document.getElementById('fixedInputSection');
                this.chatInput = document.getElementById('chatInput');
                this.chatSendBtn = document.getElementById('chatSendBtn');
                this.chatAttachBtn = document.getElementById('chatAttachBtn');
                this.chatMaxPages = document.getElementById('chatMaxPages');


                this.isSearching = false;
                this.currentProgress = null;
                this.currentMode = 'online'; // 默认联网搜索模式
                this.conversationHistory = []; // 对话历史

                this.initEventListeners();
            }

            initEventListeners() {
                // 自动调整输入框高度
                this.textarea.addEventListener('input', () => {
                    this.textarea.style.height = 'auto';
                    this.textarea.style.height = Math.min(this.textarea.scrollHeight, 300) + 'px';
                });

                // 发送按钮
                this.sendBtn.addEventListener('click', () => this.sendMessage());

                // 回车发送
                this.textarea.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 附件按钮（显示搜索页数设置）
                this.attachBtn.addEventListener('click', () => {
                    const isVisible = this.maxPagesInput.style.display !== 'none';
                    this.maxPagesInput.style.display = isVisible ? 'none' : 'inline-block';
                });

                // 侧边栏交互已在 initializeNavigation() 中处理

                document.querySelectorAll('.chat-item').forEach(item => {
                    item.addEventListener('click', () => {
                        document.querySelectorAll('.chat-item').forEach(i => i.classList.remove('active'));
                        item.classList.add('active');
                    });
                });

                // 新对话按钮
                document.querySelector('.new-chat-btn').addEventListener('click', () => {
                    this.startNewChat();
                });



                // 固定输入框事件监听器
                this.chatInput.addEventListener('input', () => {
                    this.chatInput.style.height = 'auto';
                    this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 300) + 'px';
                });

                this.chatSendBtn.addEventListener('click', () => this.sendChatMessage());

                this.chatInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendChatMessage();
                    }
                });

                this.chatAttachBtn.addEventListener('click', () => {
                    const isVisible = this.chatMaxPages.style.display !== 'none';
                    this.chatMaxPages.style.display = isVisible ? 'none' : 'inline-block';
                });


            }

            // 控制发送按钮状态
            setSendButtonState(disabled, loading = false) {
                const icon = loading ? '<i class="fas fa-spinner fa-spin"></i>' : '<i class="fas fa-paper-plane"></i>';

                this.sendBtn.disabled = disabled;
                this.sendBtn.innerHTML = icon;
                this.chatSendBtn.disabled = disabled;
                this.chatSendBtn.innerHTML = icon;
            }

            // 模式切换（保留函数以防其他地方调用）
            switchMode(mode) {
                this.currentMode = mode;
                // 由于去掉了模式切换按钮，这里只保留基本功能
            }

            // 填充建议内容
            fillInput(text) {
                this.textarea.value = text;
                this.textarea.focus();
                this.textarea.style.height = 'auto';
                this.textarea.style.height = Math.min(this.textarea.scrollHeight, 300) + 'px';
            }

            // 第一个 startNewChat 方法已删除，使用下面更完整的版本

            // 发送消息（欢迎页面）
            async sendMessage() {
                // 检查登录状态
                if (!currentUser) {
                    alert('请先登录后再使用AI小助手');
                    showLoginModal();
                    return;
                }

                const message = this.textarea.value.trim();
                const maxPages = parseInt(this.maxPagesInput.value) || 3;

                if (!message || this.isSearching) {
                    return;
                }

                // 如果是新对话，添加到聊天历史
                if (!currentChatId) {
                    currentChatId = addChatToHistory(message, currentPromptMode);
                }

                // 切换到聊天模式
                this.switchToChatMode();

                // 添加用户消息
                this.addUserMessage(message);

                // 清空输入框
                this.textarea.value = '';
                this.textarea.style.height = 'auto';

                // 根据AI模式和联网模式选择处理方式
                if (this.currentMode === 'online' && currentPromptMode === 'search') {
                    // 只有在智能搜索模式下才使用联网搜索
                    await this.performSearch(message, maxPages);
                } else {
                    // 其他所有模式都使用直接对话（包括翻译、代码助手等）
                    await this.performDirectChat(message);
                }
            }

            // 发送消息（固定输入框）
            async sendChatMessage() {
                // 检查登录状态
                if (!currentUser) {
                    alert('请先登录后再使用AI小助手');
                    showLoginModal();
                    return;
                }

                const message = this.chatInput.value.trim();
                const maxPages = parseInt(this.chatMaxPages.value) || 3;

                if (!message || this.isSearching) {
                    return;
                }

                // 如果是新对话，添加到聊天历史
                if (!currentChatId) {
                    currentChatId = addChatToHistory(message, currentPromptMode);
                }

                // 添加用户消息
                this.addUserMessage(message);

                // 清空输入框
                this.chatInput.value = '';
                this.chatInput.style.height = 'auto';

                // 根据AI模式和联网模式选择处理方式
                if (this.currentMode === 'online' && currentPromptMode === 'search') {
                    // 只有在智能搜索模式下才使用联网搜索
                    await this.performSearch(message, maxPages);
                } else {
                    // 其他所有模式都使用直接对话（包括翻译、代码助手等）
                    await this.performDirectChat(message);
                }
            }

            // 开始新对话
            startNewChat() {
                // 重置当前聊天ID
                currentChatId = null;

                // 清空聊天消息
                this.chatMessages.innerHTML = '';

                // 清空对话历史
                this.conversationHistory = [];

                // 切换回欢迎页面
                this.chatArea.className = 'chat-area welcome';
                this.welcomeSection.style.display = 'block';
                this.chatMessages.style.display = 'none';
                this.fixedInputSection.style.display = 'none';

                // 清空输入框
                this.textarea.value = '';
                this.chatInput.value = '';
                this.textarea.style.height = 'auto';
                this.chatInput.style.height = 'auto';

                // 更新聊天历史UI（移除活跃状态）
                updateChatHistoryUI();

                console.log('开始新对话');
            }

            // 加载聊天消息
            loadChatMessages(chat) {
                // 切换到聊天模式
                this.switchToChatMode();

                // 清空当前消息
                this.chatMessages.innerHTML = '';
                this.conversationHistory = [];

                // 加载历史消息
                if (chat.messages && chat.messages.length > 0) {
                    chat.messages.forEach(message => {
                        if (message.role === 'user') {
                            this.displayUserMessage(message.content, message.timestamp);
                        } else if (message.role === 'assistant') {
                            this.displayAssistantMessage(message.content, message.timestamp);
                        }

                        // 添加到对话历史
                        this.conversationHistory.push({
                            role: message.role,
                            content: message.content
                        });
                    });

                    this.scrollToBottom();
                }

                console.log('加载聊天消息完成:', chat.messages?.length || 0, '条消息');
            }

            // 显示用户消息（用于历史记录）
            displayUserMessage(content, timestamp) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message user';
                const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();
                messageDiv.innerHTML = `
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div class="message-bubble">${this.escapeHtml(content)}</div>
                        <div class="message-time">${time}</div>
                    </div>
                `;
                this.chatMessages.appendChild(messageDiv);
            }

            // 显示助手消息（用于历史记录）
            displayAssistantMessage(content, timestamp) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant';
                const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();
                messageDiv.innerHTML = `
                    <div class="message-avatar">🧠</div>
                    <div class="message-content">
                        <div class="ai-answer">
                            <div class="answer-header">
                                <span class="answer-label">AI模型回答</span>
                                <div class="answer-meta">
                                    <span class="model-name">AI模型: DeepSeek-R1</span>
                                    <span class="answer-time">回答时间: ${time}</span>
                                </div>
                            </div>
                            <div class="answer-content">${this.formatAnswer(content)}</div>
                        </div>
                    </div>
                `;
                this.chatMessages.appendChild(messageDiv);
            }

            // 切换到聊天模式
            switchToChatMode() {
                this.chatArea.className = 'chat-area has-messages';
                this.welcomeSection.style.display = 'none';
                this.chatMessages.style.display = 'block';
                this.fixedInputSection.style.display = 'block';
            }

            // 添加用户消息
            addUserMessage(message) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message user';
                messageDiv.innerHTML = `
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div class="message-bubble">${this.escapeHtml(message)}</div>
                        <div class="message-time">${new Date().toLocaleTimeString()}</div>
                    </div>
                `;
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();

                // 保存消息到聊天记录
                this.saveMessageToHistory({
                    role: 'user',
                    content: message,
                    timestamp: new Date().toISOString()
                });
            }

            // 添加助手消息
            addAssistantMessage() {
                const timestamp = Date.now();
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant';
                messageDiv.innerHTML = `
                    <div class="message-avatar">🧠</div>
                    <div class="message-content" id="assistantContent_${timestamp}">
                        <div class="search-progress" id="searchProgress_${timestamp}">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill_${timestamp}"></div>
                            </div>
                            <div class="progress-text" id="progressText_${timestamp}">正在初始化搜索...</div>
                            <div class="progress-stats" id="progressStats_${timestamp}"></div>
                        </div>
                    </div>
                `;
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();

                this.currentProgress = {
                    progressFill: document.getElementById(`progressFill_${timestamp}`),
                    progressText: document.getElementById(`progressText_${timestamp}`),
                    progressStats: document.getElementById(`progressStats_${timestamp}`),
                    container: document.getElementById(`searchProgress_${timestamp}`),
                    assistantContent: document.getElementById(`assistantContent_${timestamp}`)
                };

                return messageDiv;
            }

            // 执行直接聊天（模型基底模式）- 流式输出版本
            async performDirectChat(message) {
                this.isSearching = true;
                this.setSendButtonState(true, true);

                const assistantMessage = this.addAssistantMessage();

                // 更新进度显示为直接对话模式
                this.updateProgress(50, '正在思考中...');

                try {
                    // 获取当前模式的系统提示词
                    const currentTemplate = promptTemplates[currentPromptMode] || promptTemplates.default;
                    const systemPrompt = currentTemplate.systemPrompt;

                    // 添加用户消息到对话历史
                    this.conversationHistory.push({
                        role: 'user',
                        content: message
                    });

                    // 使用流式API，发送完整的对话历史
                    const response = await fetch('/api/chat/stream', {
                        method: 'POST',
                        headers: getAuthHeaders(),
                        body: JSON.stringify({
                            messages: this.conversationHistory,
                            mode: currentPromptMode,
                            systemPrompt: systemPrompt
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    // 隐藏进度条，开始流式输出
                    if (this.currentProgress && this.currentProgress.container) {
                        this.currentProgress.container.style.display = 'none';
                    }

                    // 处理流式响应
                    await this.handleStreamResponse(response);

                } catch (error) {
                    console.error('聊天失败:', error);
                    console.error('错误详情:', error.message);

                    // 显示更详细的错误信息
                    let errorMessage = '聊天失败，请稍后重试';
                    if (error.message.includes('401')) {
                        errorMessage = '请先登录后再使用AI小助手';
                        showLoginModal();
                    } else if (error.message.includes('403')) {
                        errorMessage = '积分不足，请充值后再试';
                    } else if (error.message.includes('500')) {
                        errorMessage = '服务器错误，请稍后重试';
                    }

                    this.showError(errorMessage);
                } finally {
                    this.isSearching = false;
                    this.setSendButtonState(false, false);
                }
            }

            // 处理流式响应
            async handleStreamResponse(response) {
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let fullAnswer = '';

                try {
                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) {
                            break;
                        }

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // 保留不完整的行

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const dataStr = line.slice(6);
                                if (dataStr.trim() === '') continue;

                                try {
                                    const data = JSON.parse(dataStr);
                                    if (data.content) {
                                        fullAnswer += data.content;
                                        // 实时更新显示
                                        this.updateStreamAnswer(fullAnswer);
                                    }
                                    if (data.done) {
                                        console.log('流式输出完成，最终答案:', fullAnswer);

                                        // 将AI回复添加到对话历史
                                        this.conversationHistory.push({
                                            role: 'assistant',
                                            content: fullAnswer
                                        });

                                        // 保存AI消息到聊天记录
                                        this.saveMessageToHistory({
                                            role: 'assistant',
                                            content: fullAnswer,
                                            timestamp: new Date().toISOString()
                                        });

                                        // 聊天完成后刷新用户信息（更新积分）
                                        if (currentUser) {
                                            setTimeout(() => {
                                                checkLoginStatus();
                                            }, 500); // 延迟500ms确保后端积分扣除完成
                                        }

                                        return;
                                    }
                                } catch (e) {
                                    console.error('解析流式数据失败:', e, dataStr);
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('读取流式响应失败:', error);
                    this.showError('接收回答时出现错误');
                }
            }

            // 更新流式答案显示
            updateStreamAnswer(answer) {
                if (this.currentProgress && this.currentProgress.assistantContent) {
                    // 检查是否已经创建了AI回答区域
                    let aiAnswerDiv = this.currentProgress.assistantContent.querySelector('.ai-answer');

                    if (!aiAnswerDiv) {
                        // 首次创建AI回答区域
                        this.currentProgress.assistantContent.innerHTML = '';
                        aiAnswerDiv = document.createElement('div');
                        aiAnswerDiv.className = 'ai-answer';
                        aiAnswerDiv.innerHTML = `
                            <div class="answer-header">
                                <span class="answer-label">AI模型回答</span>
                                <div class="answer-meta">
                                    <span class="model-name">AI模型: DeepSeek-R1</span>
                                    <span class="answer-time">回答时间: ${new Date().toLocaleTimeString()}</span>
                                </div>
                            </div>
                            <div class="answer-content"></div>
                        `;
                        this.currentProgress.assistantContent.appendChild(aiAnswerDiv);
                    }

                    // 更新答案内容
                    const answerContent = aiAnswerDiv.querySelector('.answer-content');
                    if (answerContent) {
                        answerContent.innerHTML = this.formatAnswer(answer);
                    }

                    // 滚动到最新内容
                    this.scrollToBottom();
                }
            }

            // 格式化答案内容
            formatAnswer(answer) {
                if (!answer) return '';

                let formatted = answer;

                // 处理思考过程 <think>...</think>
                formatted = formatted.replace(/<think>([\s\S]*?)<\/think>/g, (match, content) => {
                    return `<div class="thinking-process">
                        <div class="thinking-process-header">
                            <i class="fas fa-brain"></i>
                            <strong>思考过程</strong>
                        </div>
                        <div class="thinking-process-content">${content.trim().replace(/\n/g, '<br>')}</div>
                    </div>`;
                });

                // 处理代码块 ```language\ncode\n```
                formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
                    const lang = language || 'text';
                    return `<pre><code class="language-${lang}">${this.escapeHtml(code.trim())}</code></pre>`;
                });

                // 处理行内代码 `code`
                formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');

                // 处理标题（按从大到小的顺序处理，避免冲突）
                formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');
                formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
                formatted = formatted.replace(/^# (.*$)/gm, '<h1>$1</h1>');

                // 处理粗体（避免与斜体冲突，先处理粗体）
                formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

                // 处理斜体（使用更精确的正则，避免与粗体冲突）
                formatted = formatted.replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '<em>$1</em>');

                // 处理列表项
                const lines = formatted.split('\n');
                let inList = false;
                let processedLines = [];

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();

                    if (line.startsWith('- ')) {
                        if (!inList) {
                            processedLines.push('<ul>');
                            inList = true;
                        }
                        processedLines.push(`<li>${line.substring(2)}</li>`);
                    } else {
                        if (inList) {
                            processedLines.push('</ul>');
                            inList = false;
                        }
                        processedLines.push(line);
                    }
                }

                // 如果最后还在列表中，关闭列表
                if (inList) {
                    processedLines.push('</ul>');
                }

                formatted = processedLines.join('\n');

                // 将剩余的换行符转换为HTML换行
                formatted = formatted.replace(/\n/g, '<br>');

                return formatted;
            }

            // 保存消息到聊天历史
            saveMessageToHistory(message) {
                if (!currentChatId) return;

                // 查找当前聊天记录
                const currentChat = chatHistory.find(chat => chat.id === currentChatId);
                if (currentChat) {
                    if (!currentChat.messages) {
                        currentChat.messages = [];
                    }
                    currentChat.messages.push(message);
                    currentChat.updated_at = new Date().toISOString();

                    // 保存到本地存储
                    saveChatHistoryToLocal();

                    // 异步保存到服务器
                    this.updateChatOnServer(currentChatId, currentChat);
                }
            }

            // 更新服务器上的聊天记录
            async updateChatOnServer(chatId, chatData) {
                try {
                    const response = await fetch(`/api/chat/history/${chatId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            ...getAuthHeaders()
                        },
                        credentials: 'include',
                        body: JSON.stringify({
                            title: chatData.title,
                            messages: chatData.messages,
                            mode: chatData.mode
                        })
                    });

                    if (!response.ok) {
                        console.error('更新聊天记录失败:', response.status);
                    }
                } catch (error) {
                    console.error('更新聊天记录失败:', error);
                }
            }

            // HTML转义函数
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // 显示直接回答
            showDirectAnswer(answer) {
                console.log('显示直接回答:', answer);
                console.log('currentProgress:', this.currentProgress);

                if (!this.currentProgress) {
                    console.error('currentProgress 为空，无法显示回答');
                    return;
                }

                const aiAnswerDiv = document.createElement('div');
                aiAnswerDiv.className = 'ai-answer';
                aiAnswerDiv.innerHTML = `
                    <div class="ai-answer-header">
                        <i class="fas fa-brain"></i>
                        AI模型回答
                    </div>
                    <div class="ai-answer-meta">
                        <span><i class="fas fa-robot"></i> AI模型: DeepSeek-R1</span>
                        <span><i class="fas fa-clock"></i> 回答时间: ${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="ai-answer-content">${this.formatAnswer(answer)}</div>
                `;

                this.currentProgress.assistantContent.appendChild(aiAnswerDiv);
                this.scrollToBottom();
            }

            // 执行搜索
            async performSearch(question, maxPages) {
                this.isSearching = true;
                this.setSendButtonState(true, true);

                const assistantMessage = this.addAssistantMessage();

                try {
                    const response = await fetch('/api/search', {
                        method: 'POST',
                        headers: getAuthHeaders(),
                        body: JSON.stringify({
                            question: question,
                            max_pages: maxPages
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.trim() && line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    this.handleSearchUpdate(data);
                                } catch (e) {
                                    console.error('解析数据失败:', e);
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('搜索失败:', error);
                    this.showError('搜索失败，请稍后重试');
                } finally {
                    this.isSearching = false;
                    this.setSendButtonState(false, false);
                }
            }

            // 处理搜索更新
            handleSearchUpdate(data) {
                if (!this.currentProgress) return;

                switch (data.type) {
                    case 'progress':
                        this.updateProgress(data.progress, data.message);
                        break;
                    case 'stats':
                        this.updateStats(data.stats);
                        break;
                    case 'article':
                        this.addArticle(data.article);
                        break;
                    case 'complete':
                        this.searchComplete(data.summary);
                        break;
                    case 'search_complete':
                        // 搜索完全结束，重置状态
                        this.isSearching = false;
                        this.setSendButtonState(false, false);
                        break;
                    case 'error':
                        this.showError(data.message);
                        break;
                }
            }

            // 更新进度
            updateProgress(progress, message) {
                if (this.currentProgress) {
                    this.currentProgress.progressFill.style.width = `${progress}%`;
                    this.currentProgress.progressText.textContent = message;
                }
            }

            // 更新统计
            updateStats(stats) {
                if (this.currentProgress) {
                    this.currentProgress.progressStats.textContent =
                        `已获取 ${stats.total_links} 个链接，成功解析 ${stats.success_count} 篇文章`;
                }
            }

            // 添加文章
            addArticle(article) {
                if (!this.currentProgress) return;

                // 如果还没有文章结果容器，创建一个
                let resultsContainer = this.currentProgress.assistantContent.querySelector('.article-results');
                if (!resultsContainer) {
                    resultsContainer = document.createElement('div');
                    resultsContainer.className = 'article-results';
                    this.currentProgress.assistantContent.appendChild(resultsContainer);
                }

                const articleCard = this.createArticleCard(article);
                resultsContainer.appendChild(articleCard);
                this.scrollToBottom();
            }

            // 创建文章卡片
            createArticleCard(article) {
                const card = document.createElement('div');
                card.className = 'article-card';

                const contentPreview = article.content.length > 150
                    ? article.content.substring(0, 150) + '...'
                    : article.content;

                card.innerHTML = `
                    <div class="article-title">${this.escapeHtml(article.title)}</div>
                    <div class="article-meta">
                        <span><i class="fas fa-calendar"></i> ${article.pub_date}</span>
                        <span><i class="fas fa-file-text"></i> ${article.content.length} 字符</span>
                    </div>
                    <div class="article-content">${this.escapeHtml(contentPreview)}</div>
                    <a href="${article.url}" target="_blank" class="article-link">
                        <i class="fas fa-external-link-alt"></i> 查看原文
                    </a>
                `;

                return card;
            }

            // 搜索完成
            searchComplete(summary) {
                if (!this.currentProgress) return;

                this.updateProgress(100, '搜索完成！');

                // 如果有AI回答，显示它
                if (summary.question_answer) {
                    this.showAIAnswer(summary);
                }

                // 搜索完成后刷新用户信息（更新积分）
                if (currentUser) {
                    setTimeout(() => {
                        checkLoginStatus();
                    }, 1000); // 延迟1秒确保后端处理完成
                }

                // 隐藏进度条
                setTimeout(() => {
                    if (this.currentProgress && this.currentProgress.container) {
                        this.currentProgress.container.style.display = 'none';
                    }
                }, 2000);
            }

            // 显示AI回答
            showAIAnswer(summary) {
                // 创建一个新的助手消息来显示AI回答，而不是添加到搜索进度容器中
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant';
                messageDiv.innerHTML = `
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div class="ai-answer">
                            <div class="ai-answer-header">
                                <i class="fas fa-robot"></i>
                                AI智能回答
                            </div>
                            <div class="ai-answer-meta">
                                <span><i class="fas fa-question-circle"></i> 问题: ${summary.question}</span>
                                <span><i class="fas fa-search"></i> 关键词: ${summary.keyword}</span>
                                <span><i class="fas fa-file-alt"></i> 总文章: ${summary.total_articles}篇</span>
                                <span><i class="fas fa-filter"></i> 相关文章: ${summary.relevant_articles || 0}篇</span>
                                <span><i class="fas fa-robot"></i> AI模型: DeepSeek-R1</span>
                            </div>
                            <div class="ai-answer-content">${this.formatAnswer(summary.question_answer)}</div>
                        </div>
                    </div>
                `;

                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }

            // 显示错误
            showError(message) {
                if (this.currentProgress) {
                    this.currentProgress.progressText.textContent = `错误: ${message}`;
                    this.currentProgress.progressFill.style.background = '#dc3545';
                }
            }

            // 滚动到底部
            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            // HTML转义
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // 全局函数供建议卡片使用
        let chatApp;

        function fillInput(text) {
            console.log('fillInput called with:', text);
            console.log('window.chatApp:', window.chatApp);
            console.log('chatApp:', chatApp);

            if (window.chatApp && typeof window.chatApp.fillInput === 'function') {
                window.chatApp.fillInput(text);
            } else if (chatApp && typeof chatApp.fillInput === 'function') {
                chatApp.fillInput(text);
            } else {
                console.error('chatApp not initialized or fillInput method not found');
                // 降级方案：直接设置输入框的值
                const textarea = document.getElementById('messageInput');
                if (textarea) {
                    textarea.value = text;
                    textarea.focus();
                    textarea.style.height = 'auto';
                    textarea.style.height = Math.min(textarea.scrollHeight, 300) + 'px';
                }
            }
        }

        // 显示个人中心
        function showUserCenter() {
            if (!currentUser) {
                alert('请先登录');
                showLoginModal();
                return;
            }

            document.getElementById('userCenter').style.display = 'flex';

            // 更新个人中心信息
            updateUserCenterInfo();

            // 加载积分记录
            loadCreditRecords();
        }

        // 隐藏个人中心
        function hideUserCenter() {
            document.getElementById('userCenter').style.display = 'none';
        }

        // 更新个人中心用户信息
        function updateUserCenterInfo() {
            if (!currentUser) return;

            document.getElementById('userCenterName').textContent = currentUser.username;
            document.getElementById('userCenterAvatar').textContent = currentUser.username.charAt(0).toUpperCase();
            document.getElementById('userCenterCredits').textContent = currentUser.credits.toLocaleString();

            // 检查VIP状态
            let vipStatus = '普通用户';
            if (currentUser.is_vip && currentUser.vip_expire_date) {
                const vipExpire = new Date(currentUser.vip_expire_date);
                if (vipExpire >= new Date()) {
                    vipStatus = `VIP用户 (至${vipExpire.toLocaleDateString()})`;
                }
            }
            document.getElementById('userCenterVipStatus').textContent = vipStatus;

            // 检查是否为管理员
            const isAdmin = currentUser.username === 'admin' || currentUser.username === 'root';
            const adminSection = document.getElementById('adminSection');
            if (isAdmin) {
                adminSection.style.display = 'block';
            } else {
                adminSection.style.display = 'none';
            }
        }

        // 加载积分记录
        async function loadCreditRecords() {
            const recordsContainer = document.getElementById('creditRecords');
            recordsContainer.innerHTML = '<div class="loading">加载中...</div>';

            try {
                const response = await fetch('/api/user/credit-records', {
                    credentials: 'include',
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        displayCreditRecords(data.records);
                    } else {
                        recordsContainer.innerHTML = '<div class="loading">加载失败</div>';
                    }
                } else {
                    recordsContainer.innerHTML = '<div class="loading">加载失败</div>';
                }
            } catch (error) {
                console.error('加载积分记录失败:', error);
                recordsContainer.innerHTML = '<div class="loading">加载失败</div>';
            }
        }

        // 显示积分记录
        function displayCreditRecords(records) {
            const recordsContainer = document.getElementById('creditRecords');

            if (records.length === 0) {
                recordsContainer.innerHTML = '<div class="loading">暂无积分记录</div>';
                return;
            }

            const recordsHtml = records.map(record => {
                const amount = record.change_amount;
                const amountClass = amount > 0 ? 'positive' : 'negative';
                const amountText = amount > 0 ? `+${amount}` : amount.toString();
                const date = new Date(record.created_at).toLocaleString();

                return `
                    <div class="record-item">
                        <div class="record-info">
                            <div class="record-description">${record.description}</div>
                            <div class="record-time">${date}</div>
                        </div>
                        <div class="record-amount ${amountClass}">${amountText}</div>
                    </div>
                `;
            }).join('');

            recordsContainer.innerHTML = recordsHtml;
        }

        // VIP充值功能
        async function rechargeVip() {
            const cardKey = document.getElementById('cardKeyInput').value.trim();

            if (!cardKey) {
                showToast('请输入卡密', 'error');
                return;
            }

            if (!cardKey.startsWith('sk-')) {
                showToast('卡密格式错误，应以 sk- 开头', 'error');
                return;
            }

            try {
                const response = await fetch('/api/user/recharge', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    credentials: 'include',
                    body: JSON.stringify({ card_key: cardKey })
                });

                const data = await response.json();

                if (data.success) {
                    showToast(`充值成功！${data.message}`, 'success');
                    document.getElementById('cardKeyInput').value = '';

                    // 刷新用户信息
                    await checkLoginStatus();
                    updateUserCenterInfo();
                } else {
                    showToast(`充值失败：${data.error}`, 'error');
                }
            } catch (error) {
                console.error('充值失败:', error);
                showToast('充值失败，请稍后重试', 'error');
            }
        }

        // 从VIP模态框充值
        async function rechargeVipFromModal() {
            const cardKey = document.getElementById('vipCardKeyInput').value.trim();

            if (!cardKey) {
                showToast('请输入卡密', 'error');
                return;
            }

            if (!cardKey.startsWith('sk-')) {
                showToast('卡密格式错误，应以 sk- 开头', 'error');
                return;
            }

            // 显示加载状态
            const rechargeBtn = document.querySelector('.primary-recharge-btn');
            const originalText = rechargeBtn.innerHTML;
            rechargeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 充值中...';
            rechargeBtn.disabled = true;

            try {
                const response = await fetch('/api/user/recharge', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    credentials: 'include',
                    body: JSON.stringify({ card_key: cardKey })
                });

                const data = await response.json();

                if (data.success) {
                    // 先关闭模态框
                    closeVipModal();
                    // 清空输入框
                    document.getElementById('vipCardKeyInput').value = '';
                    // 显示成功提示
                    showToast(`充值成功！${data.message}`, 'success');
                    // 刷新用户信息
                    await checkLoginStatus();
                    updateUserCenterInfo();
                } else {
                    showToast(`充值失败：${data.error}`, 'error');
                }
            } catch (error) {
                console.error('充值失败:', error);
                showToast('充值失败，请稍后重试', 'error');
            } finally {
                // 恢复按钮状态
                rechargeBtn.innerHTML = originalText;
                rechargeBtn.disabled = false;
            }
        }

        // 填入测试卡密
        function fillTestCard() {
            const testCardKey = 'sk-dce33ba66f28420ff3509009f046b44c';
            document.getElementById('vipCardKeyInput').value = testCardKey;

            // 视觉反馈
            const input = document.getElementById('vipCardKeyInput');
            input.style.background = '#fef3c7';
            input.style.borderColor = '#f59e0b';

            setTimeout(() => {
                input.style.background = '';
                input.style.borderColor = '';
            }, 1000);
        }

        // 复制测试卡密
        async function copyTestCard(element) {
            const cardKey = element.textContent;

            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(cardKey);
                } else {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = cardKey;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    document.execCommand('copy');
                    textArea.remove();
                }

                // 视觉反馈
                const originalText = element.textContent;
                element.textContent = '已复制!';
                element.style.background = '#10b981';
                element.style.color = 'white';

                setTimeout(() => {
                    element.textContent = originalText;
                    element.style.background = '#f1f3f4';
                    element.style.color = '#3b82f6';
                }, 1500);

            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败，请手动选择复制');
            }
        }

        // 清空卡密输入
        function clearCardInput() {
            document.getElementById('vipCardKeyInput').value = '';
        }

        // 使用测试卡密
        function useTestCard(cardKey) {
            document.getElementById('vipCardKeyInput').value = cardKey;

            // 视觉反馈
            const input = document.getElementById('vipCardKeyInput');
            input.style.background = 'rgba(255, 255, 255, 0.95)';
            input.style.borderColor = '#ffd700';
            input.style.boxShadow = '0 0 0 3px rgba(255, 215, 0, 0.3)';

            setTimeout(() => {
                input.style.background = '';
                input.style.borderColor = '';
                input.style.boxShadow = '';
            }, 2000);
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 移除现有的提示
            const existingToasts = document.querySelectorAll('.toast');
            existingToasts.forEach(toast => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            });

            // 创建提示元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 显示管理员面板
        function showAdminPanel() {
            window.open('/admin', '_blank');
        }

        // 移动端侧边栏控制
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');
            const menuBtn = document.getElementById('mobileMenuBtn');

            if (sidebar.classList.contains('open')) {
                closeMobileSidebar();
            } else {
                openMobileSidebar();
            }
        }

        function openMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');
            const menuBtn = document.getElementById('mobileMenuBtn');

            sidebar.classList.add('open');
            overlay.classList.add('show');
            menuBtn.innerHTML = '<i class="fas fa-times"></i>';

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        function closeMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');
            const menuBtn = document.getElementById('mobileMenuBtn');

            sidebar.classList.remove('open');
            overlay.classList.remove('show');
            menuBtn.innerHTML = '<i class="fas fa-bars"></i>';

            // 恢复背景滚动
            document.body.style.overflow = '';
        }

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeMobileSidebar();
            }
        });

        // 监听键盘事件（ESC键关闭侧边栏）
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileSidebar();
            }
        });

        // 初始化应用
        function initializeApp() {
            try {
                console.log('Initializing app...');
                // 初始化聊天应用
                chatApp = new DoubanAIChat();
                window.chatApp = chatApp;
                console.log('ChatApp initialized:', chatApp);

                // 检查登录状态
                checkLoginStatus();

                // 初始化导航
                initializeNavigation();

                // 加载聊天历史
                loadChatHistory();

                // 确保页面元素正常显示
                const mainContent = document.querySelector('.main-content');
                const sidebar = document.querySelector('.sidebar');

                if (mainContent) {
                    mainContent.style.display = 'flex';
                }
                if (sidebar) {
                    sidebar.style.display = 'flex';
                }

                console.log('应用初始化完成');
            } catch (error) {
                console.error('应用初始化失败:', error);
                // 即使初始化失败，也要确保基本UI显示
                const mainContent = document.querySelector('.main-content');
                const sidebar = document.querySelector('.sidebar');

                if (mainContent) {
                    mainContent.style.display = 'flex';
                }
                if (sidebar) {
                    sidebar.style.display = 'flex';
                }
            }
        }

        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>

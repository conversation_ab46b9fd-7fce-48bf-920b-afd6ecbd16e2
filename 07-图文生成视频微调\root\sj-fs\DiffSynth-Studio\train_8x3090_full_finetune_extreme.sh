#!/bin/bash

# 8×RTX 3090极端优化全量微调脚本
# 移除所有LoRA参数，进行真正的全量微调

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:64
export TOKENIZERS_PARALLELISM=false

echo "🚀 开始8×RTX 3090极端优化全量微调..."
echo "⚠️  这是真正的全量微调，不使用LoRA"
echo "🔧 极端显存优化配置:"
echo "   - 分辨率: 64×112 (极小)"
echo "   - 梯度累积: 32步"
echo "   - 学习率: 1e-7 (极小)"
echo "   - 数据重复: 2次"
echo "   - 混合精度: bf16"
echo "   - 梯度检查点: 启用"

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 64 \
    --width 112 \
    --dataset_repeat 2 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --learning_rate 1e-7 \
    --num_epochs 1 \
    --gradient_accumulation_steps 32 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/8x3090_full_finetune_extreme" \
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090极端优化全量微调完成"

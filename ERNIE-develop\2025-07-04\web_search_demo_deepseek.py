# -*- coding: utf-8 -*-
"""
基于DeepSeek和crawl4ai的Web搜索演示程序
使用百度搜索获取URL，然后爬取内容进行分析
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import List, Dict, Any
import gradio as gr

# 添加父目录到路径，以便导入cookbook模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from cookbook.crawl_utils import CrawlUtils
from baidu_search_utils import BaiduSearchUtils
from deepseek_client import DeepSeekClient

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WebSearchChatbot:
    """
    基于Web搜索的聊天机器人
    """
    
    def __init__(self):
        """初始化聊天机器人"""
        self.deepseek_client = DeepSeekClient()
        self.baidu_search = BaiduSearchUtils()
        self.crawl_utils = CrawlUtils()
        
        # 搜索相关提示词
        self.search_decision_prompt = """
        ## 当前时间
        {date}
        
        ## 对话历史
        {context}
        
        ## 用户问题
        {query}
        
        请判断是否需要搜索网络信息来回答用户的问题。如果需要搜索，请提供1-3个搜索关键词。
        
        请以JSON格式回复：
        {{
            "need_search": true/false,
            "search_queries": ["关键词1", "关键词2", "关键词3"]
        }}
        """
        
        self.answer_prompt = """
        ## 当前时间
        {date}
        
        ## 搜索结果
        {search_results}
        
        ## 用户问题
        {query}
        
        请基于以上搜索结果回答用户的问题。要求：
        1. 回答要准确、详细
        2. 引用相关的搜索结果内容
        3. 如果搜索结果不足以回答问题，请说明
        4. 保持回答的客观性和准确性
        """

    async def search_and_crawl(self, queries: List[str], max_results_per_query: int = 5) -> str:
        """
        搜索并爬取网页内容
        
        Args:
            queries: 搜索查询列表
            max_results_per_query: 每个查询的最大结果数
            
        Returns:
            str: 格式化的搜索结果
        """
        all_results = []
        
        for query in queries:
            logger.info(f"搜索查询: {query}")
            
            # 使用百度搜索获取URL
            search_results = await self.baidu_search.search_baidu(query, max_results_per_query)
            
            # 爬取每个URL的内容
            for result in search_results:
                try:
                    content = await self.crawl_utils.get_webpage_text(result['url'])
                    if content and len(content.strip()) > 100:  # 过滤太短的内容
                        all_results.append({
                            'title': result['title'],
                            'url': result['url'],
                            'content': content[:2000]  # 限制内容长度
                        })
                        
                        # 限制总结果数量
                        if len(all_results) >= 10:
                            break
                            
                except Exception as e:
                    logger.error(f"爬取URL失败 {result['url']}: {e}")
                    continue
            
            if len(all_results) >= 10:
                break
        
        # 格式化搜索结果
        formatted_results = ""
        for i, result in enumerate(all_results, 1):
            formatted_results += f"""
参考资料[{i}]:
标题: {result['title']}
URL: {result['url']}
内容: {result['content']}

---
"""
        
        return formatted_results

    async def chat_with_search(self, user_message: str, chat_history: List[Dict]) -> tuple:
        """
        带搜索功能的聊天

        Args:
            user_message: 用户消息
            chat_history: 聊天历史（Gradio messages格式）

        Returns:
            tuple: (更新后的聊天历史, 搜索结果)
        """
        # 构建对话上下文
        context = ""
        for msg in chat_history:
            if msg.get('role') == 'user':
                context += f"用户: {msg.get('content', '')}\n"
            elif msg.get('role') == 'assistant':
                context += f"助手: {msg.get('content', '')}\n"

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 第一步：判断是否需要搜索
        decision_prompt = self.search_decision_prompt.format(
            date=current_time,
            context=context,
            query=user_message
        )

        decision_response = self.deepseek_client.simple_chat(decision_prompt)
        logger.info(f"搜索决策响应: {decision_response}")

        search_results_text = ""

        # 尝试解析搜索决策
        try:
            import json
            # 提取JSON部分
            start = decision_response.find('{')
            end = decision_response.rfind('}') + 1
            if start >= 0 and end > start:
                json_str = decision_response[start:end]
                decision_data = json.loads(json_str)

                if decision_data.get('need_search', False) and decision_data.get('search_queries'):
                    # 执行搜索
                    search_results_text = await self.search_and_crawl(decision_data['search_queries'])
                    logger.info(f"搜索完成，获得 {len(search_results_text)} 字符的结果")

        except Exception as e:
            logger.error(f"解析搜索决策失败: {e}")
            # 如果解析失败，直接使用用户问题作为搜索词
            search_results_text = await self.search_and_crawl([user_message])

        # 第二步：基于搜索结果回答问题
        if search_results_text:
            final_prompt = self.answer_prompt.format(
                date=current_time,
                search_results=search_results_text,
                query=user_message
            )
        else:
            final_prompt = f"请回答以下问题：{user_message}"

        # 生成最终回答
        final_response = self.deepseek_client.simple_chat(final_prompt)

        # 更新聊天历史 - 使用Gradio messages格式
        new_history = chat_history.copy()
        new_history.append({"role": "user", "content": user_message})
        new_history.append({"role": "assistant", "content": final_response})

        return new_history, search_results_text

    async def chat_stream_with_search(self, user_message: str, chat_history: List[List[str]]):
        """
        带搜索功能的流式聊天
        
        Args:
            user_message: 用户消息
            chat_history: 聊天历史
            
        Yields:
            tuple: (更新后的聊天历史, 搜索结果, 当前回复)
        """
        # 先添加用户消息到历史
        chat_history.append([user_message, ""])
        yield chat_history, "", ""
        
        # 构建对话上下文
        context = ""
        for user_msg, bot_msg in chat_history[:-1]:  # 排除最后一条空回复
            context += f"用户: {user_msg}\n助手: {bot_msg}\n"
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 判断是否需要搜索并执行搜索
        decision_prompt = self.search_decision_prompt.format(
            date=current_time,
            context=context,
            query=user_message
        )
        
        decision_response = self.deepseek_client.simple_chat(decision_prompt)
        search_results_text = ""
        
        try:
            import json
            start = decision_response.find('{')
            end = decision_response.rfind('}') + 1
            if start >= 0 and end > start:
                json_str = decision_response[start:end]
                decision_data = json.loads(json_str)
                
                if decision_data.get('need_search', False) and decision_data.get('search_queries'):
                    search_results_text = await self.search_and_crawl(decision_data['search_queries'])
                    yield chat_history, search_results_text, ""
                    
        except Exception as e:
            logger.error(f"搜索过程出错: {e}")
            search_results_text = await self.search_and_crawl([user_message])
            yield chat_history, search_results_text, ""
        
        # 生成流式回答
        if search_results_text:
            final_prompt = self.answer_prompt.format(
                date=current_time,
                search_results=search_results_text,
                query=user_message
            )
        else:
            final_prompt = f"请回答以下问题：{user_message}"
        
        messages = [{"role": "user", "content": final_prompt}]
        
        # 流式生成回答
        accumulated_response = ""
        stream_gen = self.deepseek_client.chat_completion_stream(messages)
        
        for chunk in stream_gen:
            if "error" in chunk:
                accumulated_response = f"错误: {chunk['error']}"
                break
            
            try:
                if 'choices' in chunk and len(chunk['choices']) > 0:
                    delta = chunk['choices'][0].get('delta', {})
                    
                    if 'content' in delta and delta['content']:
                        accumulated_response += delta['content']
                        # 更新聊天历史中的最后一条回复
                        chat_history[-1][1] = accumulated_response
                        yield chat_history, search_results_text, accumulated_response
                        
            except Exception as e:
                logger.error(f"处理流式响应出错: {e}")
                continue


# 全局聊天机器人实例
chatbot = WebSearchChatbot()


def create_gradio_interface():
    """创建Gradio界面"""
    
    async def chat_fn(message, history):
        """聊天函数"""
        if not message.strip():
            return history, ""
        
        # 转换Gradio历史格式
        chat_history = []
        if history:
            for item in history:
                if isinstance(item, list) and len(item) >= 2:
                    chat_history.append([item[0], item[1]])
        
        # 执行聊天
        result_history, search_results = await chatbot.chat_with_search(message, chat_history)
        
        # 转换回Gradio格式
        gradio_history = []
        for user_msg, bot_msg in result_history:
            gradio_history.append([user_msg, bot_msg])
        
        return gradio_history, search_results
    
    # 创建界面
    with gr.Blocks(title="DeepSeek Web搜索聊天机器人") as demo:
        gr.Markdown("# DeepSeek Web搜索聊天机器人")
        gr.Markdown("基于DeepSeek API和crawl4ai的智能搜索聊天机器人")
        
        with gr.Row():
            with gr.Column(scale=2):
                chatbot_ui = gr.Chatbot(label="对话", height=500, type="messages")
                msg = gr.Textbox(label="输入消息", placeholder="请输入您的问题...")
                
                with gr.Row():
                    submit_btn = gr.Button("发送", variant="primary")
                    clear_btn = gr.Button("清空对话")
            
            with gr.Column(scale=1):
                search_results = gr.Textbox(
                    label="搜索结果", 
                    lines=20, 
                    max_lines=20,
                    placeholder="搜索结果将显示在这里..."
                )
        
        # 绑定事件
        submit_btn.click(
            chat_fn,
            inputs=[msg, chatbot_ui],
            outputs=[chatbot_ui, search_results]
        ).then(
            lambda: "",
            outputs=[msg]
        )
        
        msg.submit(
            chat_fn,
            inputs=[msg, chatbot_ui],
            outputs=[chatbot_ui, search_results]
        ).then(
            lambda: "",
            outputs=[msg]
        )
        
        clear_btn.click(
            lambda: ([], ""),
            outputs=[chatbot_ui, search_results]
        )
    
    return demo


if __name__ == "__main__":
    # 创建并启动Gradio应用
    demo = create_gradio_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=8088,
        share=False
    )

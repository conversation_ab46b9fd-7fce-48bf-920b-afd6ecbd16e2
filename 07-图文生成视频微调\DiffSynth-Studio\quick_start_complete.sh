#!/bin/bash
# Wan2.1-I2V-14B-480P 完整流程快速启动脚本
# 基于完整对话历程的端到端解决方案

set -e

echo "🚀 Wan2.1-I2V-14B-480P 完整流程启动"
echo "基于完整对话历程的端到端解决方案"
echo "=" * 60

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查参数
SKIP_TRAINING=false
EPOCHS=5

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-training)
            SKIP_TRAINING=true
            shift
            ;;
        --epochs)
            EPOCHS="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-training    跳过训练，直接进行推理"
            echo "  --epochs N         训练轮数 (默认: 5)"
            echo "  -h, --help         显示帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 项目配置
PROJECT_ROOT="/root/sj-tmp/DiffSynth-Studio"
CONDA_ENV="wan_video_env"

log_step "第1步: 环境准备"

# 检查项目目录
if [ ! -d "$PROJECT_ROOT" ]; then
    log_error "项目目录不存在: $PROJECT_ROOT"
    exit 1
fi

cd "$PROJECT_ROOT"
log_info "工作目录: $(pwd)"

# 激活conda环境
log_info "激活conda环境: $CONDA_ENV"
source /root/miniconda3/etc/profile.d/conda.sh
conda activate "$CONDA_ENV"

# 验证环境
log_info "验证Python环境..."
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}')"

log_step "第2步: GPU状态检查"

# 检查GPU状态
log_info "GPU状态:"
nvidia-smi --query-gpu=index,name,memory.total,memory.used,utilization.gpu --format=csv,noheader,nounits

log_step "第3步: 配置文件准备"

# 创建accelerate配置
log_info "创建accelerate配置文件..."
cat > accelerate_config.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
use_cpu: false
EOF

log_info "✅ accelerate_config.yaml 已创建"

# 检查训练数据
log_step "第4步: 训练数据检查"

DATA_DIR="data/example_video_dataset"
METADATA_FILE="$DATA_DIR/metadata.csv"

if [ ! -d "$DATA_DIR" ]; then
    log_warn "训练数据目录不存在，创建示例数据..."
    mkdir -p "$DATA_DIR"
    
    # 创建示例metadata.csv
    cat > "$METADATA_FILE" << EOF
video_path,prompt
example_video.mp4,A beautiful example video for training
EOF
    
    log_info "✅ 创建了示例数据集"
else
    log_info "✅ 训练数据目录存在"
fi

# 设置环境变量
log_step "第5步: 环境变量设置"

export NCCL_TIMEOUT=1800
export TOKENIZERS_PARALLELISM=false

log_info "设置环境变量:"
log_info "  NCCL_TIMEOUT=1800"
log_info "  TOKENIZERS_PARALLELISM=false"

# 训练阶段
if [ "$SKIP_TRAINING" = false ]; then
    log_step "第6步: 多卡训练执行"
    
    log_info "开始多卡训练 ($EPOCHS epochs)..."
    log_info "这可能需要较长时间，请耐心等待..."
    
    # 训练命令（基于成功验证的配置）
    accelerate launch --config_file accelerate_config.yaml \
      examples/wanvideo/model_training/train.py \
      --dataset_base_path data/example_video_dataset \
      --dataset_metadata_path data/example_video_dataset/metadata.csv \
      --height 480 --width 832 --dataset_repeat 1 \
      --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
      --learning_rate 1e-4 --num_epochs "$EPOCHS" --gradient_accumulation_steps 1 \
      --remove_prefix_in_ckpt "pipe.dit." \
      --output_path "./models/train/Wan2.1-I2V-14B-480P_lora_final" \
      --lora_base_model "dit" --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
      --lora_rank 8 --extra_inputs "input_image" --mixed_precision "bf16"
    
    if [ $? -eq 0 ]; then
        log_info "✅ 训练完成"
    else
        log_error "❌ 训练失败"
        exit 1
    fi
else
    log_info "⏭️  跳过训练阶段"
fi

# 检查训练结果
log_step "第7步: 训练结果验证"

LORA_DIR="./models/train/Wan2.1-I2V-14B-480P_lora_final"

if [ -d "$LORA_DIR" ]; then
    log_info "✅ 训练输出目录存在: $LORA_DIR"
    
    # 检查epoch文件
    log_info "检查点文件:"
    for i in {0..9}; do
        EPOCH_FILE="$LORA_DIR/epoch-$i.safetensors"
        if [ -f "$EPOCH_FILE" ]; then
            FILE_SIZE=$(du -h "$EPOCH_FILE" | cut -f1)
            log_info "  ✅ epoch-$i.safetensors ($FILE_SIZE)"
        fi
    done
else
    log_error "❌ 训练输出目录不存在: $LORA_DIR"
    exit 1
fi

# 推理阶段
log_step "第8步: 推理执行"

log_info "开始推理..."
log_info "使用final_working_inference.py脚本"

# 检查推理脚本
if [ ! -f "final_working_inference.py" ]; then
    log_error "❌ 推理脚本不存在: final_working_inference.py"
    exit 1
fi

# 运行推理
python final_working_inference.py

if [ $? -eq 0 ]; then
    log_info "✅ 推理完成"
else
    log_error "❌ 推理失败"
    exit 1
fi

# 结果验证
log_step "第9步: 结果验证"

log_info "检查生成的视频文件..."

FOUND_VIDEO=false
for i in {4..0}; do
    VIDEO_FILE="final_lora_inference_epoch$i.mp4"
    if [ -f "$VIDEO_FILE" ]; then
        FILE_SIZE=$(du -h "$VIDEO_FILE" | cut -f1)
        log_info "✅ 找到视频: $VIDEO_FILE ($FILE_SIZE)"
        FOUND_VIDEO=true
        break
    fi
done

if [ "$FOUND_VIDEO" = false ]; then
    # 检查其他可能的输出文件
    for VIDEO_FILE in *.mp4; do
        if [ -f "$VIDEO_FILE" ]; then
            FILE_SIZE=$(du -h "$VIDEO_FILE" | cut -f1)
            log_info "✅ 找到视频: $VIDEO_FILE ($FILE_SIZE)"
            FOUND_VIDEO=true
            break
        fi
    done
fi

if [ "$FOUND_VIDEO" = false ]; then
    log_warn "⚠️  未找到生成的视频文件"
else
    log_info "✅ 视频生成成功"
fi

# 最终GPU状态
log_step "第10步: 最终状态"

log_info "最终GPU状态:"
nvidia-smi --query-gpu=index,name,memory.used,utilization.gpu --format=csv,noheader,nounits

# 总结
echo ""
echo "=" * 60
echo "🎉 Wan2.1-I2V-14B-480P 完整流程执行完成!"
echo "=" * 60

log_info "执行总结:"
log_info "✅ 环境配置完成"
log_info "✅ 配置文件创建"

if [ "$SKIP_TRAINING" = false ]; then
    log_info "✅ 多卡训练完成 ($EPOCHS epochs)"
fi

log_info "✅ LoRA权重验证"
log_info "✅ 推理执行完成"

if [ "$FOUND_VIDEO" = true ]; then
    log_info "✅ 视频生成成功"
else
    log_warn "⚠️  视频生成需要验证"
fi

echo ""
log_info "🚀 您的Wan2.1-I2V-14B-480P多卡微调到推理流程已完全打通!"
log_info "📁 检查当前目录的视频文件以查看生成结果"
log_info "📚 查看生成的文档了解详细信息"

echo ""
echo "生成的文件:"
echo "  📖 Wan2.1-I2V-14B-480P_完整实现指南.md"
echo "  🎯 complete_pipeline.py"
echo "  🚀 final_working_inference.py"
echo "  🔧 verify_inference_results.py"

echo ""
echo "下一步建议:"
echo "  1. 查看生成的视频文件"
echo "  2. 尝试不同的提示词进行推理"
echo "  3. 调整推理参数优化质量/速度"
echo "  4. 使用不同的epoch检查点对比效果"

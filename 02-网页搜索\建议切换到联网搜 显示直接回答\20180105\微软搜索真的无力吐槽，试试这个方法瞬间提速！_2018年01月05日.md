﻿# 微软搜索真的无力吐槽，试试这个方法瞬间提速！

**发布日期**: 2018年01月05日

**原文链接**: https://g.pconline.com.cn/jxwd/1444/14443782.html

## 📄 原文内容

虽然 Win10 一直在改进并引入新功能，但也整合了一些不必要的服务。例如，文件资源管理器的搜索栏，搜索速度非常慢。

本地搜索与Bing搜索集成在一起，导致使用任务栏搜索时，会出现不需要的网页内容，加上微软国内服务器，效果总之很拉跨。

如果你也遇到过相同的问题，往下看就对了。

在地址栏输入下面的地址，按 Enter 导航到指定位置。

在右边窗口 新建一个 DWORD（32位）值 。

命名为 DisableSearchBoxSuggestions，双击打开将数值数据设置为 1 。

注意：改好后要 重新启动 一次文件资源管理器才有用。

如果上述方法 没实现 ，再试试下面这个。

同样打开注册表，导航到下面给的地址。

HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Search在右边窗口新建 DWORD（32位）值 ，命名为 BingSearchEnabled，双击打开将数值数据设置为 0 。

这里多了一步，要找到 CortanaConsent，双击打开将数值数据也改为 0 。

如果没有找到，用同样方式新建一个就行。

或者按 Win+ R，输入 cmd，再输入以下命令：

完成后，可以解决资源管理器搜索缓慢。

并且这时使用任务栏搜索框，搜索相关东西只会显示本地内容，而不会出现网页结果。

如果想要重新打开网页搜索，那就关闭禁用 Bing 和 Cortana 集成，将 0 改为 1 就行。

另外，微软还在不断深度集成 Web 服务。

最近就正在测试让搜索框与Edge 共享浏览数据。

包括收藏夹、浏览历史记录、最近使用标签，甚至正打开的标签页，都会通过搜索框轻松访问。

Microsoft Edge Canary 91.0.831.0 官方内部版本

对于不喜欢的用户也不必担心，会有一个专门的开关来关闭这个功能。

反正小濠在使用搜索框时，基本用来查找各种系统应用。

真的需要搜索，会直接打开浏览器。

你觉得微软深度集成的Web服务，真的实用吗？
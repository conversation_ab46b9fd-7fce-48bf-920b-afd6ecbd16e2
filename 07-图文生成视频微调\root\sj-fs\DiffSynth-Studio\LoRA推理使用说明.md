# LoRA推理使用说明

## 🎯 概述

本文档介绍如何使用训练好的LoRA模型来推理Wan2.1-T2V-1.3B，生成高质量的文本到视频内容。

## 📁 文件结构

```
/root/sj-fs/DiffSynth-Studio/
├── models/train/memory_optimized_test/
│   └── epoch-0.safetensors          # 训练好的LoRA权重 (42MB)
├── run_lora_inference.py            # LoRA推理脚本
├── start_lora_inference.sh          # 启动脚本
└── lora_generated_video.mp4         # 生成的视频输出
```

## 🚀 快速开始

### 方法1：使用启动脚本（推荐）

```bash
# 进入项目目录
cd /root/sj-fs/DiffSynth-Studio

# 运行启动脚本
./start_lora_inference.sh
```

### 方法2：手动执行

```bash
# 设置代理
export http_proxy="*********************************************"
export https_proxy="*********************************************"

# 激活环境
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

# 运行推理
python run_lora_inference.py
```

## 📊 推理结果

### ✅ 成功输出
- **LoRA模型**: `/root/sj-fs/DiffSynth-Studio/models/train/memory_optimized_test/epoch-0.safetensors`
- **模型大小**: 42MB
- **更新张量**: 300个张量被LoRA更新
- **生成视频**: `lora_generated_video.mp4` (3.0MB)
- **视频规格**: 320×576, 17帧, 8fps

### 🎬 生成参数
- **提示词**: "A beautiful sunset over the ocean with waves gently crashing on the shore"
- **分辨率**: 320×576
- **帧数**: 17帧 (4n+1格式)
- **推理步数**: 50步
- **CFG Scale**: 7.5
- **种子**: 42

## 🔧 自定义推理

### 修改推理参数

编辑 `run_lora_inference.py` 文件中的参数：

```python
# 配置参数
lora_path = "/root/sj-fs/DiffSynth-Studio/models/train/memory_optimized_test/epoch-0.safetensors"
prompt = "你的自定义提示词"
output_path = "./custom_video.mp4"

# 生成参数
video = pipe(
    prompt=prompt,
    height=320,           # 视频高度
    width=576,            # 视频宽度  
    num_frames=17,        # 帧数 (必须是4n+1)
    num_inference_steps=50, # 推理步数
    cfg_scale=7.5,        # 引导尺度
    tiled=True,           # 启用分块处理
    seed=42,              # 随机种子
)
```

### 支持的参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `prompt` | str | - | 文本提示词 |
| `negative_prompt` | str | "" | 负面提示词 |
| `height` | int | 320 | 视频高度 |
| `width` | int | 576 | 视频宽度 |
| `num_frames` | int | 17 | 帧数 (4n+1格式) |
| `num_inference_steps` | int | 50 | 推理步数 |
| `cfg_scale` | float | 7.5 | 分类器自由引导尺度 |
| `seed` | int | 42 | 随机种子 |
| `tiled` | bool | True | 启用分块处理节省显存 |

## 🎯 使用其他LoRA模型

如果您有其他训练好的LoRA模型，只需修改路径：

```python
# 使用不同的LoRA模型
lora_path = "/path/to/your/lora/model.safetensors"
```

可用的LoRA模型：
- `models/train/memory_optimized_test/epoch-0.safetensors` (42MB)
- `models/train/8x3090_fast/epoch-0.safetensors`
- `models/train/8x3090_large_lora/epoch-0.safetensors`
- `models/train/8x3090_large_lora/epoch-1.safetensors`

## 🔍 技术细节

### 模型架构
- **基础模型**: Wan2.1-T2V-1.3B
- **LoRA适配**: 300个张量被更新
- **显存优化**: 启用VRAM管理和分块处理

### 性能指标
- **推理速度**: ~1.8 it/s (50步约27秒)
- **VAE解码**: ~2.6 it/s (4块约1.5秒)
- **总耗时**: 约30秒生成17帧视频

## 🛠️ 故障排除

### 常见问题

1. **环境未激活**
   ```bash
   source /root/miniconda3/etc/profile.d/conda.sh
   conda activate wan_video_env
   ```

2. **代理未设置**
   ```bash
   export http_proxy="*********************************************"
   export https_proxy="*********************************************"
   ```

3. **LoRA模型不存在**
   - 检查路径是否正确
   - 确认训练已完成并生成了权重文件

## 📈 下一步

- 尝试不同的提示词生成多样化内容
- 调整推理参数优化生成质量
- 使用不同的LoRA模型对比效果
- 训练新的LoRA模型适应特定风格

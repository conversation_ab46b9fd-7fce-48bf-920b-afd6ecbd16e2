#!/bin/bash

echo "========================================"
echo "DeepSeek Web搜索聊天机器人 - 环境配置"
echo "========================================"

echo ""
echo "正在创建conda虚拟环境..."
conda env create -f environment.yml

if [ $? -ne 0 ]; then
    echo "环境创建失败，尝试更新现有环境..."
    conda env update -f environment.yml
fi

echo ""
echo "激活虚拟环境..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate deepseek-websearch

echo ""
echo "运行环境测试..."
python test_environment.py

if [ $? -ne 0 ]; then
    echo "环境测试失败，请检查依赖包安装情况"
    exit 1
fi

echo ""
echo "========================================"
echo "正在启动DeepSeek Web搜索聊天机器人..."
echo "服务器地址: http://0.0.0.0:8088"
echo "本地访问: http://localhost:8088"
echo "========================================"
echo ""

python run.py

﻿# ICT市场趋势论坛成功举办_中国高新网

**发布日期**: 2025年06月18日

**原文链接**: http://www.chinahightech.com/chanye/2025-06/18/content_356572.html

## 📄 原文内容

document.getElementById('searchButton').addEventListener('click', () => {

                        var keyword = document.getElementById('searchKeyword').value;

                        if (keyword === '') {

                            return;

                        }

                        window.open('http://search.chinahightech.com/founder/NewSearchServlet.do?siteID=4&content=' + encodeURIComponent(keyword), '_blank')

                    });
        	               作者： 孙立彬
6月17日，IDC Directions：ICT市场趋势论坛（北京站）成功举办，ICT业界头部企业代表、行业数字化专家、投资机构等400余位嘉宾聚集一堂，探讨AI领航的时代，如何重塑业务转型之路。
论坛分主旨演讲和平行论坛两部分，话题对比深圳站更为丰富。IDC中国资深分析师悉数亮相，分享了IDC全球及中国ICT市场的最新洞察、人工智能相关最新技术趋势，以及AI如何影响工业、智能驾驶和安全市场。论坛首次介绍了IDC全球AI研究的框架体系和方法论，这些深度洞察将为企业在纷繁变幻的大环境中引航，助力企业赢在AI时代。
IDC中国区总裁霍锦洁的开场致辞以“六十载风云激荡 甲子轮回启新章”为主题。她提到，尽管全球IT支出呈现出超预期增长态势，但当前企业面临着很多不确定性，如贸易战可能会成为主要的下行风险。她建议企业应优先保障人工智能、数据分析、网络安全及系统优化等关键领域的投资。她还介绍了IDC全球AI研究框架体系，包含成熟度模型与行业对标研究、Agent对技术栈的影响研究、Agent市场规模与预测研究、行业应用场景研究及行业用户决策框架与ROI研究，目前该系列研究有30余本报告已上线。IDC将与企业紧密合作，提供值得信赖的专家洞见和基于数据的真实决策依据，帮助企业拓展思维，加速AI时代的业务转型。
IDC中国副总裁兼首席分析师武连峰分享了关于AI大转型的深度洞察。基于IDC研究，到2030年AI累计产生的全球经济影响将达22.3万亿美元，占全球GDP的3.7%，其巨大的中长期价值驱动企业实践AI大转型。他提出，AI大转型需要转变战略、员工与组织、技术栈，并建议企业把握政府政策、提升AI价值认知、洞悉AI相关技术的发展趋势。最后，他从三个方面为技术提供商提出建议：协助客户战略重塑，同时聚焦价值导向场景设计；推动客户组织与人才再造，打造AI驱动的工作方式；以现代化技术栈为核心，构建可持续的AI能力底座。
IDC中国副总裁钟振山在演讲中深入探讨了人工智能体（AI Agents）及其工作流如何重塑基础设施、服务、软件与安全领域，并揭示了如何释放前所未有的机遇，驱动创新、增长与价值创造。通用大模型市场步入整合期，而Agentic AI正在重塑企业战略。其自主程度将随着信任、技术和政策的成熟而增加，而能否成功应用则
取决于是否有明确的业务目标和场景、是否建立了人 - AI协作框架等业务和企业文化等多方面因素
。同时，他也分享了中国AI智能体应用市场规模及增长率预测：中国AI智能体已开启爆发式增长，标准协议正在重塑技术融合与产业协作的底层逻辑，而AI智能体的企业级渗透与消费级普及也需跨越“效率-安全”和“创新-伦理”的双重鸿沟。
IDC中国全球及中国副总裁王吉平博士在以“驾驭全球浪潮：智能终端厂商出海与未来蓝图”为主题的演讲中提到，智能终端制造商出海需精准把握全球化战略与新兴机遇。他深入剖析了新兴市场需求增长态势，指出
等新兴技术的强劲驱动作用，以及消费者行为的显著变化等关键市场趋势。同时，王吉平博士也指出了企业出海将遇到的挑战，涵盖政策法规差异、品牌文化碰撞、复杂供应链物流，以及激烈国际与本土品牌竞争等多方面。他给出应对策略，包括精准选择目标市场、实施本地化设计、拓展多渠道销售、强化品牌营销和推进生态合作等。王吉平博士强调，未来厂商要紧抓元宇宙、物联网创新机遇，推进可持续发展，平衡全球化布局与本地化需求。
，在DeepSeek以低成本、高效能、全栈开源重塑AI产业生态的背景下，中国云计算与计算平台正经历从底层硬件到服务模式的全链条革新。他的演讲聚焦了三大关键趋势：云平台成为AI创新的核心引擎，DeepSeek开源策略推动云服务商加速升级；计算基础设施通过异构协同与全栈优化，实现训练和推理效率提升；开源协作打破技术垄断，加速金融、政务等场景的私有化部署，推动技术民主化与生态重构。
IDC中国研究总监卢言霞发表了题为“生成式AI远瞻：从技术突破到产业重塑的智能引擎”的演讲。她提到，当下行业企业已经落地了智能助手、数字员工、Agent等以提升生产力为目标的相关应用，并指出，未来大模型更应该在行业企业垂直业务场景落地，也有很多全新的生成式AI应用产品、形态值得期待。在应用大爆发的未来，企业应该思考有哪些工具可以赋能、加速未来的智能化。
IDC中国研究总监王军民的演讲以“谁将赢得AI安全赛道？大模型驱动的网络安全产业变革”为主题。他提到，AI技术的快速发展正推动安全防护体系进一步升级为“智能主动防御”，并催生模型加固、对抗防御等新赛道。网络安全厂商聚焦大模型面临的特有风险，尝试在提示注入防御、训练数据污染检测等细分领域建立壁垒。在此背景下，产业竞争维度将发生根本性改变。技术迭代速度、智能化水平正在成为网络安全企业
的关键。IDC预测，未来五年，大模型安全将驱动网络安全市场持续提升，或许成为下一个“防火墙”级网络安全新赛道。
IDC中国高级研究经理杜雁泽在题为“AI驱动工业未来：从渗透到重塑”的演讲中，深入分析了工业AI市场的发展趋势。他指出，AI正以指数级速度向工业全领域深度渗透，驱动工业智能化变革。IDC预测，到2028年，中国工业企业AI支出将达到900亿元，年复合增长率为37.7%。他指出，工业AI正从初步兴起走向广泛探索阶段，AI成为用户供应商选型重要评分项，没有AI能力的传统产品将失去竞争力。同时，“专用小模型+大模型”的复合模型将成为工业领域的主流形态。同时，工业AI Agent与工业软件双螺旋，将同时赋能工业升级。
IDC中国高级分析师洪婉婷在题为“大模型驱动智驾与座舱变革”的演讲中，揭示了智能网联汽车的发展趋势。她提到，汽车正从单纯的出行工具转变为第三空间，其中80%以上的用户日均用车时长超过1小时。同时，智能驾驶技术正迈向智能化深水区，L2级自动驾驶逼近上限，L3级正快速到来。IDC预测，到2030年，面向个人用户的L4级自动驾驶将有望落地。此外，大模型和智能体是智能座舱升级的关键，将推动智能座舱从L2级跃迁到L3级，实现跨域通用、智能体协作、自我优化和情感理解等功能。
在“分析师一对一”环节，近30位IDC分析师与ICT企业、行业用户和投资机构进行了深入交流。
IDC Directions：ICT市场趋势论坛是IDC一年一度的全球性活动，今年4月在美国圣何塞举行。5-6月，历经深圳、北京两场大型活动之后，IDC Directions 2025：ICT市场趋势论坛中国站也完美收官。
					任何单位或个人认为本网站或本网站链接内容可能涉嫌侵犯其合法权益，应该及时向本网站书面反馈，并提供身份证明，权属证明及详细侵权情况证明，本网站在收到上述文件后，将会尽快移除被控侵权的内容或链接。
志愿者之歌——2023南阳高新区新时代文明实践推动周特别节目
中共中央　国务院关于加快经济社会发展全面绿色转型的意见
关于“共和国勋章”和国家荣誉称号建议人选的公示
以更有力举措增强民生获得感——当前抓改革促发展观察之五
二季度末普惠型小微企业贷款余额同比增17.1%
网购“仅退款”乱象百出 电商大战商家成炮灰
打造更强劲的消费主引擎——当前抓改革促发展观察之二
中共中央　国务院关于加快经济社会发展全面绿色转型的意见
关于“共和国勋章”和国家荣誉称号建议人选的公示
以更有力举措增强民生获得感——当前抓改革促发展观察之五
二季度末普惠型小微企业贷款余额同比增17.1%
网购“仅退款”乱象百出 电商大战商家成炮灰
打造更强劲的消费主引擎——当前抓改革促发展观察之二
Copyright 1999-2024 中国高新网
chinahightech.comAll Rights

                Reserved.
电信与信息服务业务经营许可证060344号
主办单位：《中国高新技术产业导报》社有限责任公司
			$(".picture-illustrating").each(function(index, item){

			  // $(item).parent("p").css("text-align", "center")

			  var newImg = new Image()

			  newImg.src = item.src

			  newImg.onload = function(){

				var imgInfo = $(item).attr('data-original-title')

				if(typeof imgInfo == 'undefined') return;

				var imgWidth = item.width

				var spanStyle = 'text-align: center;font-family: Arial; font-size: 14px; color: #999; padding-top: 5px; '

				var domHtml = '<section style="'+ spanStyle +'"><span>'+ imgInfo +'</span></section>'

				$(item).after(domHtml)

window.shareObj = {}

	$(".list_left div ul li").each(function(i) {

		$(this).find(".share a").unbind("click");

		$(this).find(".share a").click(function() {

			var href = $(".list_left div ul li").eq(i).find(".text_con h3 a").attr("href");

			var title = $(".list_left div ul li").eq(i).find(".text_con h3 a").text();

			var brief = $(".list_left div ul li").eq(i).find(".text_con p a").text();

			var shaimg = $(".list_left div ul li").eq(i).find(".image img").attr("src");

			if (shaimg == undefined) {

			shareObj.shareUrl = href;

			console.log(shaimg)

			console.log(brief)

			shareObj.shareTitle = title;

			shareObj.pic = shaimg;

			shareObj.summary = brief;

			if ($(this).hasClass("weixin")) {

				if ($(this).parents(".share").find(".lsrbshare_weixin").length > 0) {

					$(".lsrbshare_weixin").remove()

					$(".list_left div ul li").eq(i).css("z-index", "999").siblings().css("z-index", "1")

					requireQrcode1(i, href)

	function requireQrcode1(index, url) {

		if ($("#codeqr").length > 1) {

			$("#codeqr").show()

			createTableCode1(index, url)

		function createTableCode1(index, url) {

			$(".lsrbshare_weixin").remove();

			var ss = '<div id="lsrbshare_weixin" class="lsrbshare_weixin">';

			ss += '<div class="lsrbshare_head">';

			ss += '</div><div class="sharebg">';

			ss += '<div id="codeqr1" class="codeqr">';

			ss += '<div class="lsrbshare_headfoot">扫一扫 分享到微信</div>';

			ss += "</div></div> ";

			$(".list_left div ul li").eq(index).find(".share").append(ss);

			var ua = "canvas";

			if (navigator.userAgent.indexOf("MSIE") > -1) {

			jQuery("#codeqr").qrcode({

				foreground: "#000",

				background: "#FFF",

var _title = $('#DocTitle').val();

           var _desc = $('#DocDesc').val();

           var _thumb = $('#DocThumb').val();

           var xyUrl = "https://appif-app.kjrb.com.cn/app_if/";//APP_IF接口地址，需要修改

           var baseShareImg = "https://statics.kjrb.com.cn/statics/gaoxin/css/logo_wx.png";  //文章没有标题图时显示的默认图标，需要修改

           var wxShareUrl = location.href.split('#')[0];

           var wxShareUrlOrig = location.href;

           //var wxShareImg = _thumb;//通过模板提取标题图片作为分享图标

           var wxShareImg = "";//改用统一图标

           if (!wxShareImg) {

               wxShareImg = baseShareImg;

               url: xyUrl + "wx/signature",

               data: { url: wxShareUrl, siteId: 4 }, //站点ID，需要修改

               dataType: 'jsonp',

               success: function (dataShare) {

                   console.log(dataShare, typeof dataShare)

                   var shareData = {

                       title:'ICT市场趋势论坛成功举办',//通过模板提取标题

                       desc: '6月17日，IDC Directions：ICT市场趋势论坛（北京站）成功举办，ICT业界头部企业代表、行业数字化专家、投资机构等400余位嘉宾聚集一堂，探讨AI领航的时代，如何重塑业务转型之路。', //通过模板提取摘要

                       link: wxShareUrlOrig, //详情页带#

                       imgUrl: wxShareImg

                   };

                   wx.config({

                       debug: false,

                       appId: dataShare.appid,

                       nonceStr: dataShare.noncestr,  // 必填，生成签名的随机串

                       timestamp: dataShare.timestamp,  // 必填，生成签名的时间戳

                       signature: dataShare.signature, // 必填，签名

                       jsApiList: [

                           "onMenuShareTimeline",

                           "onMenuShareAppMessage",

                           "onMenuShareQQ",

                           "onMenuShareQZone"

                       ]

                   });

                   wx.ready(function () {

                       wx.onMenuShareTimeline(shareData);

                       wx.onMenuShareAppMessage(shareData);

                       wx.onMenuShareQQ(shareData);

                       wx.onMenuShareQZone(shareData);

                   });

               error: function (dataShare) {

           wx.error(function (res) {

            var fp_subtitle = $.trim($(".fp_subtitle").html());

            if (fp_subtitle == null || fp_subtitle == "") {

                $(".fp_title").css('display', 'inline');

                $(".fp_subtitle").hide();

            if ($('.indexNumber').html() == '') {

                $('.indexNumber').html('0')

            var f_source = $.trim($('.f_source').html());

            var f_author = $.trim($('.f_author').html());

            if (f_source == '') {

                $('.f_qsource, .f_source').hide()

            } else {

                $('.f_qsource, .f_source').show();

            // if (f_author == '') {

            //     $('.f_qauthor, .f_author').hide()

            // } else {

            //     $('.f_qauthor, .f_author').show();

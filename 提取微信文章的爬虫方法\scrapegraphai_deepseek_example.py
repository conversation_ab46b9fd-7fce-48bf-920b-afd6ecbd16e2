# scrapegraphai_deepseek_example.py

# 导入必要的库
from scrapegraphai.graphs import SmartScraperGraph
import os
import json
from litellm import completion

# --- 示例: 使用 SmartScraperGraph 刮取网页内容 (DeepSeek 模型) ---
print("\n--- 示例: 使用 SmartScraperGraph 刮取网页内容 (DeepSeek 模型) ---")

# 设置你的 DeepSeek API 密钥
# 建议将 API 密钥设置为环境变量，以保证安全性
# 例如：os.environ.get("DEEPSEEK_API_KEY")
DEEPSEEK_API_KEY = "sk-lBsjhuzN4GriMRqfD45744C34dDa4e39922bCfC9A911Cf1d" # 请替换为你的实际 API 密钥

# 配置图的参数，特别是 LLM (大型语言模型) 的设置
# 这里使用 litellm 作为 DeepSeek 的接口
graph_config_deepseek = {
    "llm": {
        "model": "deepseek/DeepSeek-R1-0528",  # litellm 中 DeepSeek 模型的表示方式
        "api_key": DEEPSEEK_API_KEY, # DeepSeek API 密钥
        "temperature": 0.7,          # 设置生成文本的随机性
        "base_url": "https://api.edgefn.net/v1", # DeepSeek API 的基础 URL
    },
    "embeddings": {
        # DeepSeek 目前没有官方的嵌入模型，这里可以暂时使用一个通用的嵌入模型
        # 或者根据实际情况选择其他嵌入服务
        "model": "ollama/nomic-embed-text", # 示例使用 Ollama 的嵌入模型
        "base_url": "http://localhost:11434", # Ollama 服务的地址
    }
}

# 定义要刮取的网页 URL
url_to_scrape_deepseek = "https://perinim.github.io/projects"

# 定义用户提示，告诉 LLM 你想从网页中提取什么信息
prompt_deepseek = "List me all the projects with their description and the author."

print(f"正在使用 Scrapegraph-ai (DeepSeek) 刮取 URL: {url_to_scrape_deepseek}")
print(f"提取提示: {prompt_deepseek}")

# 检查 DeepSeek API 密钥是否已设置
if DEEPSEEK_API_KEY == "YOUR_DEEPSEEK_API_KEY":
    print("警告: 未设置 DeepSeek API 密钥。跳过 DeepSeek 示例。")
else:
    try:
        # 创建 SmartScraperGraph 实例
        smart_scraper_graph_deepseek = SmartScraperGraph(
            prompt=prompt_deepseek,
            source=url_to_scrape_deepseek,
            config=graph_config_deepseek
        )

        # 运行刮取任务
        result_deepseek = smart_scraper_graph_deepseek.run()

        # 打印刮取结果
        print("\n--- 刮取到的内容 (DeepSeek) ---")
        print(json.dumps(result_deepseek, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"Scrapegraph-ai (DeepSeek) 刮取失败: {e}")
        print("请检查 DeepSeek API 密钥是否有效，或网络连接是否正常。")

print("\nScrapegraph-ai DeepSeek 示例执行完毕。")



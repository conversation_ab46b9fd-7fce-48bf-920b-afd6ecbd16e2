﻿# 个人信息处理不可默认勾选！工信部就App新规征求意见_杭州网

**发布日期**: 2021年04月26日

**原文链接**: https://news.hangzhou.com.cn/jjxw/content/2021-04/26/content_7954361_0.htm

## 📄 原文内容

个人信息处理不可默认勾选！工信部就App新规征求意见
发布时间：2021-04-26 22:10
新华社北京4月26日电 工信部26日公开征求《移动互联网应用程序个人信息保护管理暂行规定（征求意见稿）》的意见。征求意见稿明确，从事App个人信息处理活动的，应遵循“知情同意”“最小必要”两项重要原则，应当采取非默认勾选的方式征得用户同意。
征求意见稿对App治理的全链条、全主体、全流程予以规范，规定了App开发运营者、App分发平台、App第三方服务提供者、移动智能终端生产企业和网络接入服务提供者在App个人信息保护方面的具体义务。
征求意见稿提出，App个人信息处理活动应当采用合法、正当的方式，应当在App登录注册页面及App首次运行时，通过弹窗、文本链接及附件等简洁明显且易于访问的方式，向用户告知涵盖个人信息处理主体、处理目的、处理方式、处理类型、保存期限等内容的个人信息处理规则；应当采取非默认勾选的方式征得用户同意；应当尊重用户选择权等等。
征求意见稿同时提出，应当在对应业务功能启动时，动态申请App所需的权限，不应强制要求用户一揽子同意打开多个系统权限，且未经用户同意，不得更改用户设置的权限状态。
为保护个人信息权益，规范移动互联网应用程序（以下简称App）个人信息处理活动，促进个人信息合理利用，依据《中华人民共和国网络安全法》等法律法规，制定本规定。
在中华人民共和国境内开展的App个人信息处理活动，应当遵守本规定。法律、行政法规对个人信息处理活动另有规定的，从其规定。
本规定所称App个人信息处理活动，是指移动智能终端中运行的应用程序收集、存储、使用、加工、传输个人信息的活动。
本规定所称App开发运营者，是指从事App开发和运营活动的主体。
本规定所称App分发平台，是指通过应用商店、应用市场、网站等方式提供App下载、升级服务的软件服务平台。
本规定所称App第三方服务提供者，是指相对于用户和App以外的，为App提供软件开发工具包（SDK）、封装、加固、编译环境等第三方服务的主体。
本规定所称移动智能终端生产企业，是指生产能够接入公众网络，提供预置App或者具备安装App能力的移动智能终端设备的主体。
本规定所称网络接入服务提供者，是指从事互联网数据中心（IDC）业务、互联网接入服务（ISP）业务和内容分发网络（CDN）业务，为App提供网络接入服务的电信业务经营者。
国家互联网信息办公室负责统筹协调App个人信息保护工作和相关监督管理工作，会同工业和信息化部、公安部、市场监管总局建立健全App个人信息保护监督管理联合工作机制，统筹推进政策标准规范等相关工作，加强信息共享及对App个人信息保护工作的指导。各部门在各自职责范围内负责App个人信息保护和监督管理工作。
省、自治区、直辖市网信办、通信管理局、公安厅（局）、市场监管局负责本行政区域内App个人信息保护监督管理工作。
前两款规定的部门统称为App个人信息保护监督管理部门。
App个人信息处理活动应当采用合法、正当的方式，遵循诚信原则，不得通过欺骗、误导等方式处理个人信息，切实保障用户同意权、知情权、选择权和个人信息安全，对个人信息处理活动负责。
相关行业组织和专业机构按照有关法律法规、标准及本规定，开展App个人信息保护能力评估、认证。
从事App个人信息处理活动的，应当以清晰易懂的语言告知用户个人信息处理规则，由用户在充分知情的前提下，作出自愿、明确的意思表示。
（一）应当在App登录注册页面及App首次运行时，通过弹窗、文本链接及附件等简洁明显且易于访问的方式，向用户告知涵盖个人信息处理主体、处理目的、处理方式、处理类型、保存期限等内容的个人信息处理规则；
（二）应当采取非默认勾选的方式征得用户同意；
（三）应当尊重用户选择权，在取得用户同意前或者用户明确表示拒绝后，不得处理个人信息；个人信息处理规则发生变更的，应当重新取得用户同意；
（四）应当在对应业务功能启动时，动态申请App所需的权限，不应强制要求用户一揽子同意打开多个系统权限，且未经用户同意，不得更改用户设置的权限状态；
（五）需要向本App以外的第三方提供个人信息的,应当向用户告知其身份信息、联系方式、处理目的、处理方式和个人信息的种类等事项,并取得用户同意；
（六）处理种族、民族、宗教信仰、个人生物特征、医疗健康、金融账户、个人行踪等敏感个人信息的，应当对用户进行单独告知，取得用户同意后，方可处理敏感个人信息。
从事App个人信息处理活动的，应当具有明确、合理的目的，并遵循最小必要原则，不得从事超出用户同意范围或者与服务场景无关的个人信息处理活动。
（一）处理个人信息的数量、频次、精度等应当为服务所必需，不得超范围处理个人信息；
（二）个人信息的本地读取、写入、删除、修改等操作应当为服务所必需，不得超出用户同意的操作范围；
（三）用户拒绝相关授权申请后，不得强制退出或者关闭App，不得提前申请超出其业务功能或者服务外的权限，不得利用频繁弹窗反复申请与当前服务场景无关的权限；
（四）在非服务所必需或者无合理场景下，不得自启动或者关联启动其他App；
（五）用户拒绝提供非该类服务所必需的个人信息时，不得影响用户使用该服务；
（六）不得以改善服务质量、提升使用体验、研发新产品、定向推送信息、风险控制等为由，强制要求用户同意超范围或者与服务场景无关的个人信息处理行为。
App开发运营者应当履行以下个人信息保护义务：
（一）切实提升产品和服务个人信息保护意识，将个人信息保护要求落实在产品设计、开发及运营环节；以显著、清晰的方式定期向用户呈现App的个人信息收集使用情况；
（二）基于个人信息向用户提供商品或者服务的搜索结果的，应当保证结果公平合理，同时向该用户提供不针对其个人特征的选项，尊重和平等保护用户合法权益；
（三）使用第三方服务的，应当制定管理规则，明示App第三方服务提供者的名称、功能、个人信息处理规则等内容；应与第三方服务提供者签订个人信息处理协议，明确双方相关权利义务，并对第三方服务提供者的个人信息处理活动和信息安全风险进行管理监督；App开发运营者未尽到监督义务的，应当依法与第三方服务提供者承担连带责任；
（四）对于不影响其他服务功能的独立服务功能模块，应当向用户提供关闭或者退出该独立服务功能的选项，不得因用户采取关闭或者退出操作而拒绝提供其他服务；
（五）加强前端和后端安全防护、访问控制、技术加密、安全审计等工作，主动监测发现个人信息泄露等违规行为，及时响应处置要求；
App分发平台应当履行以下个人信息保护义务：
（一）登记并核验App开发运营者、提供者的真实身份、联系方式等信息；
（二）在显著位置标明App运行所需获取的用户终端权限列表和个人信息收集的类型、内容、目的、范围、方式、用途及处理规则等相关信息；
（四）对新上架App实行上架前个人信息处理活动规范性审核，对已上架App在本规定实施后1个月内完成补充审核，并根据审核结果进行更新或者清理；
（五）建立App开发运营者信用积分、风险App名单、平台信息共享及签名验证等管理机制；
（六）按照监督管理部门的要求，完善报送机制，及时配合监督管理部门开展问题App上报、响应和处置工作；
（七）设置便捷的投诉举报入口，及时处理公众对本平台所分发App的投诉举报；
App第三方服务提供者应当履行以下个人信息保护义务：
（二）以明确、易懂、合理的方式向App开发运营者公开其个人信息处理目的、处理方式、处理类型、保存期限等内容，其个人信息处理活动应当与公开的个人信息处理规则保持一致；
（三）未经用户同意或者在无合理业务场景下，不得自行进行唤醒、调用、更新等行为；
（四）采取足够的管理措施和技术手段保护个人信息，发现安全风险或者个人信息处理规则变更时应当及时进行更新并告知App开发运营者；
（五）未经用户同意，不得将收集到的用户个人信息共享转让；
移动智能终端生产企业应当履行以下个人信息保护义务：
（一）完善终端权限管控机制，及时弥补权限管理漏洞，持续优化和规范敏感行为的记录能力，主动为用户权限申请和告知提供便利；
（二）建立终端启动和关联启动App管理机制，为用户提供关闭自启动和关联启动的功能选项；
（三）持续优化个人信息权限在用状态，特别是录音、拍照、视频等敏感权限在用状态的显著提示机制，帮助用户及时准确了解个人信息权限的使用状态；
（四）建立重点App关注名单管理机制，完善移动智能终端App管理措施；
（五）对预置App进行审核，持续监测预置App的个人信息安全风险；
（六）在安装过程中以显著方式告知用户App申请的个人信息权限列表；
网络接入服务提供者应当履行以下个人信息保护义务：
（一）在为App提供网络接入服务时，登记并核验App开发运营者的真实身份、联系方式等信息；
（二）按照监督管理部门的要求，依法对违规App采取停止接入等必要措施，阻止其继续违规侵害用户个人信息和其他合法权益；
从事App个人信息处理活动的相关主体，应当加强人员教育培训，制定个人信息保护内部管理制度，落实网络安全等级保护和应急预案等制度要求；采取加密、去标识化等安全技术措施，防止未经授权的访问及个人信息泄露或者被窃取、篡改、删除等风险；需要认证用户真实身份信息的，应当通过国家统一建设的公民身份认证基础设施所提供的网上公民身份核验认证服务进行。
任何组织和个人发现违反本规定行为的，可以向监督管理部门或者中国互联网协会、中国网络空间安全协会投诉举报，监督管理部门和相关组织应当及时受理并调查处理。
从事App个人信息处理活动的相关主体应当自觉接受社会监督。
根据公众投诉举报情况和监管中发现的问题，监督管理部门可以对存在问题和风险的App实施个人信息保护检查。
从事App个人信息处理活动的相关主体应当对监督管理部门依法实施的监督检查予以配合。
发现从事个人信息处理活动的相关主体违反本规定的，监督管理部门可依据各自职责采取以下处置措施：
（一）责令整改与社会公告。对检测发现问题App的开发运营者、App分发平台、第三方服务提供者及相关主体提出整改，要求5个工作日内进行整改及时消除隐患；未完成整改的，向社会公告。
（二）下架处置。对社会公告5个工作日后，仍拒绝整改或者整改后仍存在问题的，可要求相关主体进行下架处置；对反复出现问题、采取技术对抗等违规情节严重的，将对其进行直接下架；被下架的App在40个工作日内不得通过任何渠道再次上架。
（三）断开接入。下架后仍未按要求完成整改的，将对其采取断开接入等必要措施。
（四）恢复上架。被下架的App完成整改，并完善技术和管理机制及作出企业自律承诺后，可向作出下架要求的监督管理部门申请恢复上架。
（五）恢复接入。被断开网络接入的App完成整改后，可向作出断开接入要求的监督管理部门申请恢复接入。
（六）信用管理。对相应违规主体，可纳入信用管理，实施联合惩戒。
对整改反复出现问题的App及其开发运营者开发的相关App，监督管理部门可以指导组织App分发平台和移动智能终端生产企业在集成、分发、预置和安装等环节进行风险提示，情节严重的采取禁入措施。
从事App个人信息处理活动侵害个人信息权益的，将依照有关规定予以处罚；构成犯罪的，公安机关依法追究刑事责任。
监督管理部门应当对履行职责中知悉的用户个人信息予以保密，不得泄露、篡改或者毁损，不得出售或者非法向他人提供。
| 信息网络传播视听节目许可证：1105105 | 互联网新闻信息服务许可证：国新网3312006002
工信部备案号：浙ICP备11041366号
浙公网安备：33010002000058号
Hangzhou.com.cn All Rights Reserved
var date=new Date;var copy_year =document.getElementById("copy_year");var year=date.getFullYear();if(copy_year.innerText) {copy_year.innerText =year;}else {copy_year.textContent =year;}
$(function() {$(".fh-btn").click(function(){if(window.history.length>1){window.history.go(-1);}else{window.location.href="/";}
);var str =$(".tit span").text();var bytelen =0;if(str){for(var i=0;i<str.length;i++){if(str.charCodeAt(i)>255){bytelen+=2;}else{bytelen++;}
if(bytelen<24){$(".tit").css("textAlign","center");}
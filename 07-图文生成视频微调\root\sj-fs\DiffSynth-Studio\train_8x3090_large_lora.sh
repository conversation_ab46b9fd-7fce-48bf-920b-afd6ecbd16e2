#!/bin/bash

# 8×RTX 3090大LoRA训练脚本
# 使用更大的LoRA rank和更多target modules，接近全量微调效果

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

echo "🚀 开始8×RTX 3090大LoRA训练..."
echo "🎯 使用大LoRA配置，接近全量微调效果"
echo "📊 配置参数:"
echo "   - LoRA Rank: 128 (大rank)"
echo "   - Target Modules: 所有线性层"
echo "   - 分辨率: 320×576"
echo "   - 梯度累积: 4步"

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 320 \
    --width 576 \
    --dataset_repeat 30 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --learning_rate 1e-4 \
    --num_epochs 2 \
    --gradient_accumulation_steps 4 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/8x3090_large_lora" \
    --lora_base_model "dit" \
    --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
    --lora_rank 128 \
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090大LoRA训练完成"

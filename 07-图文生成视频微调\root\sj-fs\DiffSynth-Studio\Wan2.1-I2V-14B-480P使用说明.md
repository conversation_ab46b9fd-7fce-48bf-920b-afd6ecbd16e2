# 🎬 Wan2.1-I2V-14B-480P 图像到视频模型使用说明

## 📋 概述

本项目为Wan-AI/Wan2.1-I2V-14B-480P图像到视频生成模型提供了完整的微调训练解决方案。支持从单GPU到8×RTX 3090多GPU的各种训练配置，包含完整的工具链和中文文档。

## 🚀 快速开始

### 1. 环境准备
```bash
# 激活虚拟环境
conda activate wan_video_env

# 检查环境状态
python tools/gpu_monitor.py --action check
```

### 2. 一键训练
```bash
# 启动交互式训练界面
python start_wan_i2v_training.py

# 或直接启动显存优化训练
python start_wan_i2v_training.py --config 1
```

### 3. 测试结果
```bash
# 自动测试训练结果
python test_wan_i2v_lora.py --auto_detect
```

## 📁 主要文件说明

### 训练脚本
- `examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P.sh` - 标准LoRA训练
- `examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_8x3090.sh` - 8×RTX 3090分布式训练
- `examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P_memory_optimized.sh` - 显存优化训练

### 工具脚本
- `start_wan_i2v_training.py` - 一键启动训练界面
- `test_wan_i2v_lora.py` - 综合推理测试
- `tools/gpu_monitor.py` - GPU监控工具
- `tools/wan_i2v_dataset_processor.py` - 数据集处理工具
- `tools/wan_training_monitor.py` - 训练监控工具

### 文档
- `Wan2.1-I2V-14B-480P训练文档.md` - 详细训练文档
- `Wan2.1-I2V-14B-480P快速指南.md` - 快速开始指南
- `Wan2.1-I2V-14B-480P项目总结.md` - 项目总结
- `Wan2.1-I2V-14B-480P使用说明.md` - 本文档

## 🎯 训练配置选择

### 配置1: 显存优化 (推荐新手)
- **硬件要求**: RTX 3090 24GB × 1
- **显存使用**: ~16GB
- **训练时间**: 约3小时
- **分辨率**: 320×576
- **适用场景**: 个人实验、学习使用

### 配置2: 标准配置
- **硬件要求**: RTX 3090 24GB × 1
- **显存使用**: ~20GB
- **训练时间**: 约4小时
- **分辨率**: 480×832
- **适用场景**: 小规模项目

### 配置3: 高性能 (8×RTX 3090)
- **硬件要求**: RTX 3090 24GB × 8
- **显存使用**: ~18GB/卡
- **训练时间**: 约1小时
- **分辨率**: 480×832
- **适用场景**: 生产环境、大规模训练

## 🛠️ 常用命令

### 环境检查
```bash
# 检查GPU状态
python tools/gpu_monitor.py --action summary

# 检查训练就绪状态
python tools/gpu_monitor.py --action check

# 检查系统要求
python tools/wan_training_monitor.py --action check
```

### 数据集处理
```bash
# 从视频创建I2V数据集
python tools/wan_i2v_dataset_processor.py \
    --action create_from_videos \
    --input_dir /path/to/videos \
    --output_dir ./data/custom_dataset

# 验证数据集
python tools/wan_i2v_dataset_processor.py \
    --action validate \
    --input_dir ./data/custom_dataset
```

### 训练监控
```bash
# 实时监控GPU
python tools/gpu_monitor.py --action monitor

# 生成优化配置
python tools/wan_training_monitor.py --action optimize --output optimized.sh
```

### 推理测试
```bash
# 基础推理测试
python examples/wanvideo/model_training/validate_lora/Wan2.1-I2V-14B-480P.py

# 综合测试
python test_wan_i2v_lora.py --auto_detect --output_dir test_outputs

# 自定义图像测试
python test_wan_i2v_lora.py \
    --custom_image /path/to/image.jpg \
    --custom_prompt "描述你想要的视频内容"
```

## 📊 训练流程

### 标准训练流程
1. **环境检查** → 确认GPU、内存、磁盘空间
2. **数据准备** → 下载或准备训练数据集
3. **配置选择** → 根据硬件选择合适的训练配置
4. **开始训练** → 执行训练脚本
5. **监控进度** → 实时监控GPU使用和训练进度
6. **推理测试** → 测试训练结果
7. **结果评估** → 评估生成视频质量

### 训练输出
训练完成后会在以下目录生成LoRA权重文件：
```
models/train/Wan2.1-I2V-14B-480P_[配置名]/
├── epoch-0.safetensors
├── epoch-1.safetensors
├── ...
└── epoch-N.safetensors
```

## 🔧 故障排除

### 常见问题

**Q: 提示"请先激活wan_video_env环境"**
```bash
conda activate wan_video_env
```

**Q: CUDA内存不足**
```bash
# 使用显存优化配置
python start_wan_i2v_training.py --config 1
```

**Q: 模型下载失败**
```bash
# 设置代理或使用镜像
export HF_ENDPOINT=https://hf-mirror.com
```

**Q: 数据集不存在**
```bash
# 会自动下载示例数据集，或手动下载：
modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
```

### 性能优化建议

1. **显存不足**: 降低分辨率、减小LoRA rank、启用梯度检查点
2. **训练速度慢**: 使用多GPU、增加数据加载进程、使用SSD存储
3. **质量不佳**: 增加训练轮数、使用更高质量数据集、调整学习率

## 📈 最佳实践

### 数据集准备
- 使用高质量、多样化的视频数据
- 确保视频分辨率适中（建议480P-720P）
- 提供准确的文本描述

### 训练参数
- 新手建议从显存优化配置开始
- 训练3-5个epoch，避免过拟合
- 定期进行推理测试验证效果

### 硬件配置
- 单GPU实验：RTX 3090 24GB
- 生产训练：8×RTX 3090
- 存储：使用NVMe SSD存储数据集
- 内存：至少32GB系统内存

## 📞 获取帮助

1. **查看详细文档**: `Wan2.1-I2V-14B-480P训练文档.md`
2. **运行环境检查**: `python start_wan_i2v_training.py --check-only`
3. **查看GPU状态**: `python tools/gpu_monitor.py --action check`
4. **检查训练日志**: 查看训练过程中的输出信息

## 🎉 成功标志

训练成功的标志：
- ✅ 训练脚本正常完成，无错误退出
- ✅ 生成了LoRA权重文件（.safetensors）
- ✅ 推理测试成功生成视频
- ✅ 生成的视频质量符合预期

完成这些步骤后，您就成功完成了Wan2.1-I2V-14B-480P模型的微调训练！

---

*最后更新: 2025-07-10*
*适用环境: wan_video_env (Python 3.12)*

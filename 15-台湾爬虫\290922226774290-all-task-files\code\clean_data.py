#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清理脚本 - 清理CSV文件中的无效数据
"""

import pandas as pd
import re
import json
from pathlib import Path

def clean_category_field(category_text):
    """清理标的分类字段"""
    if not category_text or pd.isna(category_text):
        return ""
    
    # 如果包含导航信息，则清空
    navigation_keywords = [
        '財物相關', '財物出租查詢', '財物變賣查詢', '物調公告查詢',
        '國外採購', '全球政府採購商機', '外國政府採購網站',
        '廠商相關', '外國廠商代碼', '科技研究機構',
        '下載專區', '等標期', '拒絕往來名單',
        '相關連結', '憑證', '政府憑證管理中心',
        '請求協助', '熱門問答', '問題檢索',
        '最新功能'
    ]
    
    for keyword in navigation_keywords:
        if keyword in str(category_text):
            return ""
    
    # 提取有效的标的分类信息
    category_str = str(category_text)
    
    # 匹配标准格式：<类型> 编号 名称
    match = re.search(r'<([^>]+)>\s*(\d+\s*[^財物國外廠商下載相關請求最新]+)', category_str)
    if match:
        category_type = match.group(1).strip()
        category_detail = match.group(2).strip()
        return f"{category_type} {category_detail}"
    
    # 如果是简短的有效分类，保留
    if len(category_str) < 50 and not any(kw in category_str for kw in navigation_keywords):
        return category_str.strip()
    
    return ""

def clean_csv_data(csv_file_path):
    """清理CSV数据"""
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file_path)
        
        print(f"原始数据行数: {len(df)}")
        
        # 清理category字段
        if 'category' in df.columns:
            df['category'] = df['category'].apply(clean_category_field)
            print("已清理category字段")
        
        # 清理其他可能包含导航信息的字段
        text_fields = ['tender_type', 'organization', 'case_name']
        for field in text_fields:
            if field in df.columns:
                # 移除过长的无效数据
                df[field] = df[field].apply(lambda x: "" if pd.notna(x) and len(str(x)) > 200 else x)
        
        # 保存清理后的数据
        output_path = csv_file_path.replace('.csv', '_cleaned.csv')
        df.to_csv(output_path, index=False, encoding='utf-8')
        
        print(f"清理后数据已保存到: {output_path}")
        print(f"清理后数据行数: {len(df)}")
        
        # 显示category字段的统计
        if 'category' in df.columns:
            valid_categories = df[df['category'] != '']['category'].value_counts()
            print(f"\n有效的标的分类统计:")
            print(valid_categories.head(10))
        
        return output_path
        
    except Exception as e:
        print(f"清理数据时出错: {e}")
        return None

def clean_json_data(json_file_path):
    """清理JSON数据"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"原始JSON数据条数: {len(data)}")
        
        # 清理每条记录
        for item in data:
            if 'category' in item:
                item['category'] = clean_category_field(item['category'])
            
            # 清理其他字段
            text_fields = ['tender_type', 'organization', 'case_name']
            for field in text_fields:
                if field in item and item[field] and len(str(item[field])) > 200:
                    item[field] = ""
        
        # 保存清理后的数据
        output_path = json_file_path.replace('.json', '_cleaned.json')
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"清理后JSON数据已保存到: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"清理JSON数据时出错: {e}")
        return None

def main():
    """主函数"""
    print("🧹 开始清理爬虫数据...")
    
    # 查找最新的数据文件
    data_dir = Path('data')
    
    # 清理CSV文件
    csv_files = list(data_dir.glob('csv/ultimate_crawler_data_*.csv'))
    if csv_files:
        latest_csv = max(csv_files, key=lambda x: x.stat().st_mtime)
        print(f"\n📄 清理CSV文件: {latest_csv}")
        clean_csv_data(str(latest_csv))
    
    # 清理JSON文件
    json_files = list(data_dir.glob('json/ultimate_crawler_data_*.json'))
    if json_files:
        latest_json = max(json_files, key=lambda x: x.stat().st_mtime)
        print(f"\n📄 清理JSON文件: {latest_json}")
        clean_json_data(str(latest_json))
    
    print("\n✅ 数据清理完成!")

if __name__ == "__main__":
    main()

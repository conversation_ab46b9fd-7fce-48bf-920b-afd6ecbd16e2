<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理系统 - AI搜索助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 版本号: v1.1 - 修复表单顺序问题 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        /* 导航标签样式 */
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            gap: 8px;
        }

        .nav-tab {
            flex: 1;
            padding: 12px 20px;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .nav-tab:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .nav-tab i {
            font-size: 16px;
        }

        /* 标签页内容样式 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 用户管理样式 */
        .user-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .user-status.vip {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
        }

        .user-status.normal {
            background: #f3f4f6;
            color: #6b7280;
        }

        .user-status.expired {
            background: #fee2e2;
            color: #dc2626;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        /* 图表容器 */
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #6b7280;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card:nth-child(1) .stat-number { color: #3b82f6; }
        .stat-card:nth-child(2) .stat-number { color: #10b981; }
        .stat-card:nth-child(3) .stat-number { color: #f59e0b; }
        .stat-card:nth-child(4) .stat-number { color: #ef4444; }

        .section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .section h2 {
            font-size: 20px;
            margin-bottom: 20px;
            color: #1f2937;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-header h2 {
            margin-bottom: 0;
        }

        .header-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 2fr auto;
            gap: 20px;
            align-items: end;
            margin-bottom: 20px;
        }

        .form-grid .form-group:nth-child(1) {
            order: 1; /* VIP天数 */
        }

        .form-grid .form-group:nth-child(2) {
            order: 2; /* 生成数量 */
        }

        .form-grid .form-group:nth-child(3) {
            order: 3; /* 卡密描述 */
        }

        .form-grid button {
            order: 4; /* 生成按钮 */
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #374151;
        }

        .form-group input, .form-group select {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: #ef4444;
            color: white;
            padding: 8px 16px;
            font-size: 12px;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            table-layout: fixed;
        }

        .table th {
            background: #f9fafb;
            padding: 16px 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .table th:first-child,
        .table td:first-child {
            width: 35%;
            min-width: 300px;
        }

        .table td {
            padding: 16px 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tbody tr:hover {
            background: #f9fafb;
        }

        .card-key-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-key {
            font-family: 'Monaco', 'Menlo', monospace;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
            flex: 1;
            max-width: 250px;
            overflow-wrap: break-word;
            white-space: normal;
        }

        .btn-copy {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 28px;
            height: 24px;
        }

        .btn-copy:hover {
            background: #2563eb;
            transform: scale(1.05);
        }

        .btn-copy:active {
            transform: scale(0.95);
        }

        .btn-copy.copied {
            background: #10b981;
            animation: copySuccess 0.3s ease;
        }

        @keyframes copySuccess {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-unused {
            background: #d1fae5;
            color: #065f46;
        }

        .status-used {
            background: #fee2e2;
            color: #991b1b;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> 管理系统</h1>
            <p>AI搜索助手管理后台 - 卡密管理、用户管理和系统统计</p>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab('cards')">
                <i class="fas fa-credit-card"></i>
                卡密管理
            </button>
            <button class="nav-tab" onclick="switchTab('users')">
                <i class="fas fa-users"></i>
                用户管理
            </button>
            <button class="nav-tab" onclick="switchTab('stats')">
                <i class="fas fa-chart-bar"></i>
                系统统计
            </button>
        </div>

        <!-- 卡密管理标签页 -->
        <div id="cardsTab" class="tab-content active">
            <!-- 统计卡片 -->
            <div class="stats-grid" id="statsContainer">
            <div class="stat-card">
                <div class="stat-number" id="totalCards">-</div>
                <div class="stat-label">总卡密数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="unusedCards">-</div>
                <div class="stat-label">未使用</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="usedCards">-</div>
                <div class="stat-label">已使用</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="recentUsage">-</div>
                <div class="stat-label">近7天使用</div>
            </div>
        </div>

        <!-- 创建卡密 -->
        <div class="section">
            <h2><i class="fas fa-plus-circle"></i> 创建卡密</h2>
            <div class="form-grid">
                <div class="form-group" style="order: 1;">
                    <label for="cardDays">VIP天数</label>
                    <input type="number" id="cardDays" value="30" min="1" max="365">
                </div>
                <div class="form-group" style="order: 2;">
                    <label for="cardCount">生成数量</label>
                    <input type="number" id="cardCount" value="1" min="1" max="100">
                </div>
                <div class="form-group" style="order: 3;">
                    <label for="cardDescription">卡密描述</label>
                    <input type="text" id="cardDescription" value="VIP充值卡" maxlength="50">
                </div>
                <button class="btn btn-primary" onclick="createCards()" style="order: 4;">
                    <i class="fas fa-magic"></i> 生成卡密
                </button>
            </div>
        </div>

        <!-- 卡密列表 -->
        <div class="section">
            <div class="section-header">
                <h2><i class="fas fa-list"></i> 卡密列表</h2>
                <div class="header-actions">
                    <button class="btn btn-warning" onclick="archiveUsedCards()" title="将所有已使用的卡密归档">
                        <i class="fas fa-archive"></i> 批量归档
                    </button>
                    <button class="btn btn-danger" onclick="batchDeleteUnusedCards()" title="批量删除所有未使用的卡密">
                        <i class="fas fa-trash-alt"></i> 批量删除
                    </button>
                    <button class="btn btn-secondary" onclick="copyAllUnusedCards()" title="复制所有未使用的卡密">
                        <i class="fas fa-copy"></i> 批量复制
                    </button>
                    <button class="btn btn-info" onclick="viewArchivedCards()" title="查看归档的卡密">
                        <i class="fas fa-history"></i> 查看归档
                    </button>
                </div>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>卡密</th>
                            <th>天数</th>
                            <th>状态</th>
                            <th>描述</th>
                            <th>创建时间</th>
                            <th>使用者</th>
                            <th>使用时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="cardsTableBody">
                        <tr>
                            <td colspan="8" class="loading">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        </div>

        <!-- 用户管理标签页 -->
        <div id="usersTab" class="tab-content">
            <!-- 用户搜索和筛选 -->
            <div class="section">
                <h2><i class="fas fa-search"></i> 用户搜索</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="userSearch">搜索用户</label>
                        <input type="text" id="userSearch" placeholder="输入用户名或邮箱" onkeyup="searchUsers()">
                    </div>
                    <div class="form-group">
                        <label for="userFilter">用户类型</label>
                        <select id="userFilter" onchange="filterUsers()">
                            <option value="all">全部用户</option>
                            <option value="vip">VIP用户</option>
                            <option value="normal">普通用户</option>
                            <option value="expired">VIP已过期</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button class="btn btn-primary" onclick="loadUsers()">
                            <i class="fas fa-refresh"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>

            <!-- 用户列表 -->
            <div class="section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> 用户列表</h2>
                    <div>
                        <button class="btn btn-success" onclick="showAddUserModal()">
                            <i class="fas fa-plus"></i> 添加用户
                        </button>
                        <button class="btn btn-secondary" onclick="exportUsers()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>积分</th>
                                <th>VIP状态</th>
                                <th>VIP到期</th>
                                <th>注册时间</th>
                                <th>最后活动</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td colspan="8" class="loading">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 系统统计标签页 -->
        <div id="statsTab" class="tab-content">
            <!-- 系统概览 -->
            <div class="section">
                <h2><i class="fas fa-chart-bar"></i> 系统概览</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalUsers">-</div>
                        <div class="stat-label">总用户数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="vipUsers">-</div>
                        <div class="stat-label">VIP用户</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeUsers">-</div>
                        <div class="stat-label">活跃用户</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalCredits">-</div>
                        <div class="stat-label">总积分消耗</div>
                    </div>
                </div>
            </div>

            <!-- 使用统计图表 -->
            <div class="section">
                <h2><i class="fas fa-chart-line"></i> 使用趋势</h2>
                <div class="chart-container">
                    <canvas id="usageChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="userModalTitle">编辑用户</h3>
                <button class="modal-close" onclick="closeUserModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <input type="hidden" id="editUserId">
                    <div class="form-group">
                        <label for="editUsername">用户名</label>
                        <input type="text" id="editUsername" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">邮箱</label>
                        <input type="email" id="editEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="editCredits">积分</label>
                        <input type="number" id="editCredits" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="editIsVip">VIP状态</label>
                        <select id="editIsVip" onchange="toggleVipFields()">
                            <option value="false">普通用户</option>
                            <option value="true">VIP用户</option>
                        </select>
                    </div>
                    <div class="form-group" id="vipExpireGroup" style="display: none;">
                        <label for="editVipExpire">VIP到期时间</label>
                        <input type="datetime-local" id="editVipExpire">
                    </div>
                    <div class="form-group">
                        <label for="editPassword">新密码（留空不修改）</label>
                        <input type="password" id="editPassword" placeholder="留空表示不修改密码">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeUserModal()">取消</button>
                <button class="btn btn-primary" onclick="saveUser()">保存</button>
            </div>
        </div>
    </div>

    <script>
        let adminData = { cards: [], stats: {}, users: [] };
        let currentTab = 'cards';

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkAdminAuth();
        });

        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有标签的active状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName + 'Tab').classList.add('active');

            // 激活选中的标签
            event.target.classList.add('active');

            currentTab = tabName;

            // 根据标签页加载相应数据
            switch(tabName) {
                case 'cards':
                    loadAdminData();
                    break;
                case 'users':
                    loadUsers();
                    break;
                case 'stats':
                    loadSystemStats();
                    break;
            }
        }

        // 检查管理员权限
        async function checkAdminAuth() {
            try {
                console.log('[ADMIN] 开始检查管理员权限...');

                const response = await fetch('/api/user/info', {
                    credentials: 'include'
                });

                console.log('[ADMIN] API响应状态:', response.status);

                if (!response.ok) {
                    console.log('[ADMIN] API响应失败，状态码:', response.status);
                    const errorText = await response.text();
                    console.log('[ADMIN] 错误详情:', errorText);
                    showAuthError();
                    return;
                }

                const data = await response.json();
                console.log('[ADMIN] API响应数据:', data);

                if (!data.success) {
                    console.log('[ADMIN] API返回失败:', data.error);
                    showAuthError();
                    return;
                }

                // 检查是否为管理员
                const isAdmin = data.user.username === 'admin' || data.user.username === 'root';
                console.log('[ADMIN] 用户名:', data.user.username, '是否为管理员:', isAdmin);

                if (!isAdmin) {
                    showPermissionError();
                    return;
                }

                console.log('[ADMIN] 管理员认证成功，开始加载数据...');
                // 认证成功，加载数据
                loadAdminData();
                // 每30秒自动刷新数据
                setInterval(loadAdminData, 30000);

            } catch (error) {
                console.error('[ADMIN] 认证检查异常:', error);
                showAuthError();
            }
        }

        // 显示认证错误
        function showAuthError() {
            document.body.innerHTML = `
                <div style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <div style="text-align: center; padding: 40px; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #f59e0b; margin-bottom: 20px;"></i>
                        <h2 style="color: #1f2937; margin-bottom: 16px;">需要登录</h2>
                        <p style="color: #6b7280; margin-bottom: 24px;">请先登录管理员账号</p>
                        <button onclick="window.close()" style="padding: 12px 24px; background: #3b82f6; color: white; border: none; border-radius: 8px; cursor: pointer;">
                            关闭窗口
                        </button>
                    </div>
                </div>
            `;
        }

        // 显示权限错误
        function showPermissionError() {
            document.body.innerHTML = `
                <div style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <div style="text-align: center; padding: 40px; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                        <i class="fas fa-shield-alt" style="font-size: 48px; color: #ef4444; margin-bottom: 20px;"></i>
                        <h2 style="color: #1f2937; margin-bottom: 16px;">权限不足</h2>
                        <p style="color: #6b7280; margin-bottom: 24px;">只有管理员才能访问此页面</p>
                        <button onclick="window.close()" style="padding: 12px 24px; background: #ef4444; color: white; border: none; border-radius: 8px; cursor: pointer;">
                            关闭窗口
                        </button>
                    </div>
                </div>
            `;
        }

        // 加载管理员数据
        async function loadAdminData() {
            try {
                console.log('[ADMIN] 开始加载卡密数据...');
                const response = await fetch('/api/admin/cards', {
                    credentials: 'include'
                });

                console.log('[ADMIN] API响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: 获取数据失败`);
                }

                const data = await response.json();
                console.log('[ADMIN] API响应数据:', data);

                if (data.success) {
                    adminData = data;
                    console.log('[ADMIN] 卡密数据:', adminData.cards?.length || 0, '张');
                    console.log('[ADMIN] 统计数据:', adminData.stats);
                    updateStats();
                    updateCardsTable();
                } else {
                    throw new Error(data.error || '获取数据失败');
                }
            } catch (error) {
                console.error('[ADMIN] 加载数据失败:', error);
                showError('加载数据失败: ' + error.message);
            }
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalCards').textContent = adminData.stats.total_cards || 0;
            document.getElementById('unusedCards').textContent = adminData.stats.unused_cards || 0;
            document.getElementById('usedCards').textContent = adminData.stats.used_cards || 0;
            document.getElementById('recentUsage').textContent = adminData.stats.recent_usage || 0;
        }

        // 更新卡密表格
        function updateCardsTable() {
            const tbody = document.getElementById('cardsTableBody');

            console.log('[ADMIN] 更新卡密表格，数据:', adminData);

            if (!adminData || !adminData.cards || adminData.cards.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <div>暂无卡密数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            const rows = adminData.cards.map(card => {
                const statusClass = card.status === 'unused' ? 'status-unused' : 'status-used';
                const statusText = card.status === 'unused' ? '未使用' : '已使用';
                const createdAt = new Date(card.created_at).toLocaleString('zh-CN');
                const usedAt = card.used_at ? new Date(card.used_at).toLocaleString('zh-CN') : '-';
                const usedBy = card.used_by || '-';
                const description = card.description || '-';

                return `
                    <tr>
                        <td>
                            <div class="card-key-container">
                                <span class="card-key">${card.card_key}</span>
                                <button class="btn-copy" onclick="copyCardKey('${card.card_key}')" title="复制卡密">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </td>
                        <td>${card.days}天</td>
                        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                        <td>${description}</td>
                        <td>${createdAt}</td>
                        <td>${usedBy}</td>
                        <td>${usedAt}</td>
                        <td>
                            ${card.status === 'unused' ?
                                `<button class="btn btn-danger" onclick="deleteCard('${card.card_key}')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>` :
                                '<span style="color: #9ca3af;">-</span>'
                            }
                        </td>
                    </tr>
                `;
            }).join('');

            tbody.innerHTML = rows;
        }

        // 创建卡密
        async function createCards() {
            const days = parseInt(document.getElementById('cardDays').value);
            const count = parseInt(document.getElementById('cardCount').value);
            const description = document.getElementById('cardDescription').value.trim();

            if (!days || days < 1 || days > 365) {
                showError('VIP天数必须在1-365之间');
                return;
            }

            if (!count || count < 1 || count > 100) {
                showError('生成数量必须在1-100之间');
                return;
            }

            try {
                const response = await fetch('/api/admin/cards', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ days, count, description })
                });

                const data = await response.json();
                if (data.success) {
                    showSuccess(`成功创建 ${data.cards.length} 张卡密`);
                    loadAdminData(); // 刷新数据
                } else {
                    showError('创建失败: ' + data.error);
                }
            } catch (error) {
                showError('创建失败: ' + error.message);
            }
        }

        // 删除卡密
        async function deleteCard(cardKey) {
            if (!confirm('确定要删除这张卡密吗？删除后无法恢复！')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/cards/${cardKey}`, {
                    method: 'DELETE',
                    credentials: 'include'
                });

                const data = await response.json();
                if (data.success) {
                    showSuccess('删除成功');
                    loadAdminData(); // 刷新数据
                } else {
                    showError('删除失败: ' + data.error);
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }

        // 复制卡密到剪贴板
        async function copyCardKey(cardKey) {
            try {
                // 使用现代剪贴板API
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(cardKey);
                } else {
                    // 降级方案：使用传统方法
                    const textArea = document.createElement('textarea');
                    textArea.value = cardKey;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    document.execCommand('copy');
                    textArea.remove();
                }

                // 显示复制成功的视觉反馈
                showCopySuccess(cardKey);

                // 显示成功消息
                showSuccess('卡密已复制到剪贴板');

            } catch (error) {
                console.error('复制失败:', error);
                showError('复制失败，请手动选择复制');
            }
        }

        // 显示复制成功的视觉反馈
        function showCopySuccess(cardKey) {
            // 找到对应的复制按钮
            const buttons = document.querySelectorAll('.btn-copy');
            buttons.forEach(button => {
                if (button.getAttribute('onclick').includes(cardKey)) {
                    // 临时改变按钮样式
                    const originalIcon = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check"></i>';
                    button.classList.add('copied');

                    // 1.5秒后恢复原样
                    setTimeout(() => {
                        button.innerHTML = originalIcon;
                        button.classList.remove('copied');
                    }, 1500);
                }
            });
        }

        // 批量复制所有未使用的卡密
        async function copyAllUnusedCards() {
            const unusedCards = adminData.cards.filter(card => card.status === 'unused');

            if (unusedCards.length === 0) {
                showError('没有未使用的卡密可复制');
                return;
            }

            const cardKeys = unusedCards.map(card => card.card_key).join('\n');

            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(cardKeys);
                } else {
                    const textArea = document.createElement('textarea');
                    textArea.value = cardKeys;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    document.execCommand('copy');
                    textArea.remove();
                }

                showSuccess(`已复制 ${unusedCards.length} 张未使用卡密到剪贴板`);

            } catch (error) {
                console.error('批量复制失败:', error);
                showError('批量复制失败，请手动选择复制');
            }
        }

        // 批量归档已使用的卡密
        async function archiveUsedCards() {
            const usedCards = adminData.cards.filter(card => card.status === 'used');

            if (usedCards.length === 0) {
                showError('没有已使用的卡密可归档');
                return;
            }

            // 确认对话框
            const confirmed = confirm(`确定要归档 ${usedCards.length} 张已使用的卡密吗？\n\n归档后这些卡密将从列表中移除，但数据会保存到归档文件中。`);
            if (!confirmed) {
                return;
            }

            try {
                const response = await fetch('/api/admin/archive-used-cards', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        card_keys: usedCards.map(card => card.card_key)
                    })
                });

                const data = await response.json();
                if (data.success) {
                    showSuccess(`成功归档 ${usedCards.length} 张已使用的卡密`);
                    // 重新加载卡密列表
                    await loadAdminData();
                } else {
                    showError(data.error || '归档失败');
                }
            } catch (error) {
                console.error('批量归档失败:', error);
                showError('批量归档失败，请稍后重试');
            }
        }

        // 批量删除未使用的卡密
        async function batchDeleteUnusedCards() {
            const unusedCards = adminData.cards.filter(card => card.status === 'unused');

            if (unusedCards.length === 0) {
                showError('没有未使用的卡密可删除');
                return;
            }

            // 确认对话框
            const confirmed = confirm(`确定要删除 ${unusedCards.length} 张未使用的卡密吗？\n\n此操作不可恢复！`);
            if (!confirmed) {
                return;
            }

            try {
                let successCount = 0;
                let failCount = 0;

                for (const card of unusedCards) {
                    try {
                        const response = await fetch(`/api/admin/cards/${card.card_key}`, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            credentials: 'include'
                        });

                        if (response.ok) {
                            successCount++;
                        } else {
                            failCount++;
                        }
                    } catch (error) {
                        failCount++;
                    }
                }

                if (successCount > 0) {
                    showSuccess(`成功删除 ${successCount} 张卡密${failCount > 0 ? `，失败 ${failCount} 张` : ''}`);
                    // 重新加载卡密列表
                    await loadAdminData();
                } else {
                    showError('删除失败，请稍后重试');
                }
            } catch (error) {
                console.error('批量删除失败:', error);
                showError('批量删除失败，请稍后重试');
            }
        }

        // 查看归档卡密
        function viewArchivedCards() {
            window.open('/archived-cards.html', '_blank');
        }

        // 用户管理函数
        async function loadUsers() {
            try {
                const response = await fetch('/api/admin/users', {
                    credentials: 'include'
                });

                const data = await response.json();
                if (data.success) {
                    adminData.users = data.users;
                    updateUsersTable();
                } else {
                    throw new Error(data.error || '获取用户数据失败');
                }
            } catch (error) {
                console.error('加载用户数据失败:', error);
                showError('加载用户数据失败: ' + error.message);
            }
        }

        // 更新用户表格
        function updateUsersTable() {
            const tbody = document.getElementById('usersTableBody');

            if (adminData.users.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <div>暂无用户数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            const rows = adminData.users.map(user => {
                const vipStatus = getVipStatus(user);
                const vipExpire = user.vip_expire_date ?
                    new Date(user.vip_expire_date).toLocaleDateString('zh-CN') : '-';
                const createdAt = new Date(user.created_at).toLocaleDateString('zh-CN');
                const updatedAt = new Date(user.updated_at).toLocaleDateString('zh-CN');

                return `
                    <tr>
                        <td>${user.username}</td>
                        <td>${user.email}</td>
                        <td>${user.credits.toLocaleString()}</td>
                        <td><span class="user-status ${vipStatus.class}">${vipStatus.text}</span></td>
                        <td>${vipExpire}</td>
                        <td>${createdAt}</td>
                        <td>${updatedAt}</td>
                        <td>
                            <button class="btn btn-primary" onclick="editUser('${user.id}')">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger" onclick="deleteUser('${user.id}', '${user.username}')">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');

            tbody.innerHTML = rows;
        }

        // 获取VIP状态
        function getVipStatus(user) {
            if (!user.is_vip) {
                return { class: 'normal', text: '普通用户' };
            }

            if (user.vip_expire_date) {
                const expireDate = new Date(user.vip_expire_date);
                const now = new Date();
                if (expireDate >= now) {
                    return { class: 'vip', text: 'VIP用户' };
                } else {
                    return { class: 'expired', text: 'VIP已过期' };
                }
            }

            return { class: 'normal', text: '普通用户' };
        }

        // 搜索用户
        function searchUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const filteredUsers = adminData.users.filter(user =>
                user.username.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm)
            );

            displayFilteredUsers(filteredUsers);
        }

        // 筛选用户
        function filterUsers() {
            const filterType = document.getElementById('userFilter').value;
            let filteredUsers = adminData.users;

            switch(filterType) {
                case 'vip':
                    filteredUsers = adminData.users.filter(user => {
                        if (!user.is_vip) return false;
                        if (!user.vip_expire_date) return false;
                        return new Date(user.vip_expire_date) >= new Date();
                    });
                    break;
                case 'normal':
                    filteredUsers = adminData.users.filter(user => !user.is_vip);
                    break;
                case 'expired':
                    filteredUsers = adminData.users.filter(user => {
                        if (!user.is_vip) return false;
                        if (!user.vip_expire_date) return true;
                        return new Date(user.vip_expire_date) < new Date();
                    });
                    break;
            }

            displayFilteredUsers(filteredUsers);
        }

        // 显示筛选后的用户
        function displayFilteredUsers(users) {
            const originalUsers = adminData.users;
            adminData.users = users;
            updateUsersTable();
            adminData.users = originalUsers;
        }

        // 编辑用户
        function editUser(userId) {
            const user = adminData.users.find(u => u.id === userId);
            if (!user) {
                showError('用户不存在');
                return;
            }

            document.getElementById('userModalTitle').textContent = '编辑用户';
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editUsername').value = user.username;
            document.getElementById('editEmail').value = user.email;
            document.getElementById('editCredits').value = user.credits;
            document.getElementById('editIsVip').value = user.is_vip.toString();

            if (user.is_vip && user.vip_expire_date) {
                const expireDate = new Date(user.vip_expire_date);
                const localDateTime = new Date(expireDate.getTime() - expireDate.getTimezoneOffset() * 60000)
                    .toISOString().slice(0, 16);
                document.getElementById('editVipExpire').value = localDateTime;
                document.getElementById('vipExpireGroup').style.display = 'block';
            } else {
                document.getElementById('vipExpireGroup').style.display = 'none';
            }

            document.getElementById('editPassword').value = '';
            document.getElementById('userModal').style.display = 'flex';
        }

        // 显示添加用户模态框
        function showAddUserModal() {
            document.getElementById('userModalTitle').textContent = '添加用户';
            document.getElementById('editUserId').value = '';
            document.getElementById('editUsername').value = '';
            document.getElementById('editEmail').value = '';
            document.getElementById('editCredits').value = '1000';
            document.getElementById('editIsVip').value = 'false';
            document.getElementById('editPassword').value = '';
            document.getElementById('vipExpireGroup').style.display = 'none';
            document.getElementById('userModal').style.display = 'flex';
        }

        // 关闭用户模态框
        function closeUserModal() {
            document.getElementById('userModal').style.display = 'none';
        }

        // 切换VIP字段显示
        function toggleVipFields() {
            const isVip = document.getElementById('editIsVip').value === 'true';
            document.getElementById('vipExpireGroup').style.display = isVip ? 'block' : 'none';
        }

        // 保存用户
        async function saveUser() {
            try {
                const userId = document.getElementById('editUserId').value;
                const isEdit = userId !== '';

                const userData = {
                    username: document.getElementById('editUsername').value,
                    email: document.getElementById('editEmail').value,
                    credits: parseInt(document.getElementById('editCredits').value),
                    is_vip: document.getElementById('editIsVip').value === 'true',
                    vip_expire_date: document.getElementById('editVipExpire').value || null,
                    password: document.getElementById('editPassword').value
                };

                if (!userData.username || !userData.email) {
                    showError('用户名和邮箱不能为空');
                    return;
                }

                if (!isEdit && !userData.password) {
                    showError('新用户密码不能为空');
                    return;
                }

                const url = isEdit ? `/api/admin/users/${userId}` : '/api/admin/users';
                const method = isEdit ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(userData)
                });

                const data = await response.json();
                if (data.success) {
                    showSuccess(data.message);
                    closeUserModal();
                    loadUsers();
                } else {
                    showError(data.error);
                }
            } catch (error) {
                console.error('保存用户失败:', error);
                showError('保存用户失败: ' + error.message);
            }
        }

        // 删除用户
        async function deleteUser(userId, username) {
            if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    credentials: 'include'
                });

                const data = await response.json();
                if (data.success) {
                    showSuccess(data.message);
                    loadUsers();
                } else {
                    showError(data.error);
                }
            } catch (error) {
                console.error('删除用户失败:', error);
                showError('删除用户失败: ' + error.message);
            }
        }

        // 导出用户数据
        function exportUsers() {
            const csvContent = "data:text/csv;charset=utf-8," +
                "用户名,邮箱,积分,VIP状态,VIP到期,注册时间,最后活动\n" +
                adminData.users.map(user => {
                    const vipStatus = getVipStatus(user).text;
                    const vipExpire = user.vip_expire_date ?
                        new Date(user.vip_expire_date).toLocaleDateString('zh-CN') : '-';
                    const createdAt = new Date(user.created_at).toLocaleDateString('zh-CN');
                    const updatedAt = new Date(user.updated_at).toLocaleDateString('zh-CN');

                    return `"${user.username}","${user.email}",${user.credits},"${vipStatus}","${vipExpire}","${createdAt}","${updatedAt}"`;
                }).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `用户数据_${new Date().toISOString().slice(0, 10)}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 加载系统统计
        async function loadSystemStats() {
            try {
                const response = await fetch('/api/admin/stats', {
                    credentials: 'include'
                });

                const data = await response.json();
                if (data.success) {
                    updateSystemStats(data.stats);
                } else {
                    throw new Error(data.error || '获取统计数据失败');
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
                showError('加载统计数据失败: ' + error.message);
            }
        }

        // 更新系统统计显示
        function updateSystemStats(stats) {
            document.getElementById('totalUsers').textContent = stats.total_users || 0;
            document.getElementById('vipUsers').textContent = stats.vip_users || 0;
            document.getElementById('activeUsers').textContent = stats.active_users || 0;
            document.getElementById('totalCredits').textContent = (stats.total_credits_consumed || 0).toLocaleString();
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('userModal');
            if (event.target === modal) {
                closeUserModal();
            }
        });

        // 阻止模态框内容区域的点击事件冒泡
        document.addEventListener('DOMContentLoaded', function() {
            const modalContent = document.querySelector('#userModal .modal-content');
            if (modalContent) {
                modalContent.addEventListener('click', function(event) {
                    event.stopPropagation();
                });
            }
        });

        // 显示成功消息
        function showSuccess(message) {
            alert('✅ ' + message);
        }

        // 显示错误消息
        function showError(message) {
            alert('❌ ' + message);
        }
    </script>
</body>
</html>

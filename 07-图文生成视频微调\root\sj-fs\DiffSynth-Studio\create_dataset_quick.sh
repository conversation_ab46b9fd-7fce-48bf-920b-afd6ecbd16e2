#!/bin/bash

# 快速创建自定义数据集脚本
# 适配Wan2.1-T2V-1.3B微调

echo "🚀 快速创建自定义数据集工具"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 步骤1: 创建目录结构
print_step "1. 创建数据集目录结构"

DATASET_DIR="data/custom_video_dataset"
mkdir -p ${DATASET_DIR}/{videos,images,annotations}

print_status "目录结构创建完成:"
tree ${DATASET_DIR} 2>/dev/null || ls -la ${DATASET_DIR}/

# 步骤2: 检查视频文件
print_step "2. 检查视频文件"

if [ -z "$(ls -A ${DATASET_DIR}/videos/ 2>/dev/null)" ]; then
    print_warning "videos目录为空"
    echo "请将您的视频文件复制到: ${DATASET_DIR}/videos/"
    echo "支持格式: .mp4, .avi, .mov, .mkv, .webm, .flv"
    echo ""
    echo "示例命令:"
    echo "cp /path/to/your/videos/*.mp4 ${DATASET_DIR}/videos/"
    echo ""
    read -p "复制完成后按回车继续..."
fi

# 检查视频文件数量
video_count=$(find ${DATASET_DIR}/videos/ -type f \( -name "*.mp4" -o -name "*.avi" -o -name "*.mov" -o -name "*.mkv" -o -name "*.webm" -o -name "*.flv" \) | wc -l)

if [ $video_count -eq 0 ]; then
    print_error "未找到视频文件，请检查文件格式和路径"
    exit 1
fi

print_status "找到 $video_count 个视频文件"

# 步骤3: 选择创建方式
print_step "3. 选择数据集创建方式"

echo "请选择创建方式:"
echo "1) 自动生成提示词（从文件名）"
echo "2) 使用自定义提示词文件"
echo "3) 手动输入提示词"

read -p "请输入选项 (1-3): " choice

case $choice in
    1)
        print_status "使用自动生成提示词模式"
        python create_custom_dataset.py \
            --video_dir ${DATASET_DIR}/videos \
            --output_dir ${DATASET_DIR} \
            --min_duration 2.0 \
            --max_duration 30.0 \
            --min_width 256 \
            --min_height 256
        ;;
    2)
        print_status "使用自定义提示词文件模式"
        
        # 创建示例提示词文件
        PROMPTS_FILE="${DATASET_DIR}/custom_prompts.txt"
        if [ ! -f "$PROMPTS_FILE" ]; then
            print_status "创建示例提示词文件: $PROMPTS_FILE"
            echo "# 自定义提示词文件" > $PROMPTS_FILE
            echo "# 格式: 文件名: 提示词描述" >> $PROMPTS_FILE
            echo "" >> $PROMPTS_FILE
            
            # 为每个视频文件创建示例条目
            for video_file in ${DATASET_DIR}/videos/*; do
                if [ -f "$video_file" ]; then
                    filename=$(basename "$video_file")
                    echo "${filename}: A video showing $(basename "$video_file" | sed 's/\.[^.]*$//' | sed 's/_/ /g' | sed 's/-/ /g')" >> $PROMPTS_FILE
                fi
            done
            
            print_warning "请编辑提示词文件: $PROMPTS_FILE"
            echo "然后重新运行此脚本"
            exit 0
        fi
        
        python create_custom_dataset.py \
            --video_dir ${DATASET_DIR}/videos \
            --output_dir ${DATASET_DIR} \
            --prompts_file $PROMPTS_FILE \
            --min_duration 2.0 \
            --max_duration 30.0 \
            --min_width 256 \
            --min_height 256
        ;;
    3)
        print_status "手动输入提示词模式"
        
        # 创建临时提示词文件
        TEMP_PROMPTS="${DATASET_DIR}/temp_prompts.txt"
        > $TEMP_PROMPTS
        
        for video_file in ${DATASET_DIR}/videos/*; do
            if [ -f "$video_file" ]; then
                filename=$(basename "$video_file")
                echo "视频文件: $filename"
                read -p "请输入提示词: " prompt
                echo "${filename}: ${prompt}" >> $TEMP_PROMPTS
            fi
        done
        
        python create_custom_dataset.py \
            --video_dir ${DATASET_DIR}/videos \
            --output_dir ${DATASET_DIR} \
            --prompts_file $TEMP_PROMPTS \
            --min_duration 2.0 \
            --max_duration 30.0 \
            --min_width 256 \
            --min_height 256
        
        # 清理临时文件
        rm -f $TEMP_PROMPTS
        ;;
    *)
        print_error "无效选项"
        exit 1
        ;;
esac

# 步骤4: 验证数据集
print_step "4. 验证数据集"

if [ -f "${DATASET_DIR}/metadata.csv" ]; then
    print_status "数据集创建成功！"
    
    echo ""
    echo "📊 数据集信息:"
    echo "位置: ${DATASET_DIR}"
    echo "文件列表:"
    ls -la ${DATASET_DIR}/
    
    echo ""
    echo "📋 metadata.csv 前5行:"
    head -5 ${DATASET_DIR}/metadata.csv
    
    echo ""
    echo "📈 统计信息:"
    if [ -f "${DATASET_DIR}/dataset_stats.json" ]; then
        cat ${DATASET_DIR}/dataset_stats.json
    fi
    
    echo ""
    print_status "数据集可用于训练！"
    echo "训练命令示例:"
    echo "accelerate launch examples/wanvideo/model_training/train.py \\"
    echo "  --dataset_base_path ${DATASET_DIR} \\"
    echo "  --dataset_metadata_path ${DATASET_DIR}/metadata.csv \\"
    echo "  --height 320 --width 576 \\"
    echo "  --num_epochs 2 \\"
    echo "  --output_path ./models/train/custom_lora"
    
else
    print_error "数据集创建失败，请检查错误信息"
    exit 1
fi

print_status "🎉 自定义数据集创建完成！"

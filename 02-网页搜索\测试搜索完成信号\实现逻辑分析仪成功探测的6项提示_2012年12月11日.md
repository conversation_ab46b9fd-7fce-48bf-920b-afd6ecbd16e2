﻿# 实现逻辑分析仪成功探测的6项提示

**发布日期**: 2012年12月11日

**原文链接**: https://www.eepw.com.cn/article/192915.htm

## 📄 原文内容

实现 逻辑分析仪 成功 探测 的6项提示

为了完成今天越来越复杂的数字系统的设计, 工程师需要完善的分析工具。对于系统验证任务, 大多数工程师都要依靠 逻辑分析仪 。随着被测系统速度的不断提升和复杂程度的持续增加, 逻辑分析仪 厂商也及时提高了仪器的性能和功能, 以满足工程师的需求。在许多情况下, 逻辑分析仪主机的性能往往超过手头任务的需要, 而从分析仪到目标系统的探头物理连接则成为系统性能的瓶颈。如果逻辑分析仪接收到的信号有畸变, 那么逻辑分析仪的强大触发和分析工具将是无用武之地。

这篇应用指南将讨论实现成功逻辑分析仪探头连接, 需要考虑的 探测 问题。我们将介绍探头结构形式选择、探头负载和信号质量概念, 以及与接地有关的常见问题。最后讨论两种容易犯的错误: 在错误的引线位置 探测 和选择了错误的互连。

如果您决定使用逻辑分析仪, 也就必须选择使用何种类型的探头连接。探头连接可分成两大类: “设计中包括连接”和“事后连接”。对于设计中包括的连接, 逻辑分析仪探头所探测的测试点就融入在最初设计中。连接器探头和无连接器探头都属于这类连接。为使用这两类探头,设计师在电路板上布放适宜的焊盘,并把感兴趣的信号连到焊盘上。逻辑分析仪探头上的连接器能与目标连接器可靠插接。无连接器探头则能压在电路板焊盘上, 以保证良好地接触 (图1a)。

探头 “事后连接” 的适应测试能力未融入设计的系统。此时您需要用包括各种互连附件(焊接、抓钩等)的探头触针实现连接。最常用的“事后连接”探头是飞线探头(图1b)。

在讨论各种探头结构形式的优缺点前, 先了解您在把逻辑分析仪接到系统时会遇到的一些问题。

图1. 这些照片对“设计中包括连接”的探头和“事后连接”的探头进行比较。

您要设法尽可能减小探头对系统呈现的电气负载。如果探头极大改变了系统性能, 它就不能帮助您验证系统; 因为故障可能完全是因探头引发。负载主要有两方面影响。首先是降低目标电路板上的信号质量, 并进而导致系统故障。其次是降低送入逻辑分析仪的被观察波形质量。这会在验证中导致错误的否定结论。为避免这些问题, 您必须了解探头的结构。

逻辑分析仪探头有高输入阻抗。探头触针电路包括一个20kΩ量级的触针电阻器。低频时的探头阻抗接近该阻值。随着频率的上升，探头的寄生电容开始降低它的阻抗。阻抗沿标准RC响应滚降。这可能造成目标系统的问题; 当探头阻抗开始接近系统阻抗时, 由探头构成的电压分压器起着实质性的作用。低阻抗将吸收大量电流, 从而造成系统故障。

探头中的电容主要与连接器有关。例如若目标信号与探头触针电阻间有大的连接器, 该连接器就会把大电容加到探头负载中。使用小的连接器可减小这一电容量。

无连接器探头提供较低的电气负载。如前所述, 当您使用无连接器探头时, 就要在目标系统上放置承载焊盘。逻辑分析仪探头压着在目标小电路板上实现电气连接。由于消除了电气路径中的物理连接器, 就能实现非常低的电容量 (见表1)。

图2 显示负载端接传输线上各种探头结构形式对等效集总电容的影响。波形显示来自探头的电容性反射如何在初始波形后某一时刻到达接收器。

图2. 波形是比较各种互连探头负载的好方法。负载随连接器尺寸的减小(或消除)而减小。系统的原上升时间是150ps。

如前所述, 探头导致进入逻辑分析仪中的信号质量变差，从而得出错误的否定结论。这是验证者遇到麻烦的原因, 因为他们把大量时间用在调试并不存在的问题上。为避免这一问题, 您必须注意探头触针处的信号质量。

除了留意探头的电容性负载外,您还必须注意探头位置。这在选择各种不同端接方案时尤为重要。对于某种特定的端接方案, 接收器观察的信号可能有足够好的信号质量, 但在连线任何其它点观察到的信号却可能无法接受。

为说明这一点, 先来分析串联端接传输线理论。感应波形瞬间在源端电阻和导线特性阻抗间分开。半幅度的电波沿传输线传输到接收器。在到达接收器时经过 100% 的正反射, 把半幅信号加倍得到原波形的幅度。该反射波以相反方向在传输线

上传输, 直到被源端电阻器吸收, 从而结束瞬态响应。

虽然这种方案为接收器提供一个良好的波形, 但该波形在传输线上呈阶梯形状。阶梯波形不适合逻辑分析仪, 因为逻辑分析仪不能确定波形半幅度期间到底是逻辑“1”还是逻辑“0”。图3显示这种情况的波形。注意接收器处的波形信号质量良好,而在探头触针处观察到的波形却无法接受。随着信号速度的增加, 探头触针处的信号质量对于成功的测量变得尤为重要。

图3. 在接收器处和在探头触针处观察到的串联端接系统波形。

注意在连线中间处观察到的波形阶梯形状。它表明正向传输的半幅度波形是往接收器方向传输。反向传输间反射波形与正向传输波形叠加后得到其最终值。
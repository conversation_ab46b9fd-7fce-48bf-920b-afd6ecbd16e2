#!/usr/bin/env python3
"""
測試詳情頁數據提取 - 專門測試廠商信息提取
"""

from enhanced_procurement_crawler import EnhancedProcurementCrawler
import json

def test_detail_extraction():
    """測試詳情頁數據提取"""
    
    # 測試URL - 您提供的詳情頁
    test_url = "https://web.pcc.gov.tw/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetail?pkAtmMain=NzAxNTM4ODk="
    
    print("🔍 開始測試詳情頁數據提取...")
    print(f"📄 測試URL: {test_url}")
    
    # 創建爬蟲實例
    crawler = EnhancedProcurementCrawler()
    
    try:
        # 提取詳情頁數據
        detail_info = crawler.extract_detail_fields(test_url)
        
        if detail_info:
            print("\n✅ 詳情頁數據提取成功！")
            
            # 顯示機關信息
            print(f"\n🏢 === 機關信息 ===")
            print(f"機關代碼: {detail_info.agency_info.agency_code}")
            print(f"機關名稱: {detail_info.agency_info.agency_name}")
            print(f"單位名稱: {detail_info.agency_info.unit_name}")
            print(f"機關地址: {detail_info.agency_info.agency_address}")
            print(f"聯絡人: {detail_info.agency_info.contact_person}")
            print(f"聯絡電話: {detail_info.agency_info.contact_phone}")
            print(f"傳真號碼: {detail_info.agency_info.fax_number}")
            
            # 顯示公告資料
            print(f"\n📋 === 公告資料 ===")
            print(f"標案案號: {detail_info.announcement_info.case_number}")
            print(f"標案名稱: {detail_info.announcement_info.case_title}")
            print(f"招標方式: {detail_info.announcement_info.tender_method}")
            print(f"決標方式: {detail_info.announcement_info.award_method}")
            print(f"公告日期: {detail_info.announcement_info.announcement_date}")
            
            # 顯示金額信息
            print(f"\n💰 === 金額信息 ===")
            print(f"預算金額: {detail_info.amount_info.budget_amount}")
            print(f"預算金額(中文): {detail_info.amount_info.budget_amount_chinese}")
            print(f"採購金額級距: {detail_info.amount_info.procurement_amount_range}")
            print(f"預算金額是否公開: {detail_info.amount_info.budget_public}")
            
            # 顯示時間信息
            print(f"\n⏰ === 時間信息 ===")
            print(f"開標時間: {detail_info.time_info.opening_time}")
            print(f"原公告日期: {detail_info.time_info.original_announcement_date}")
            print(f"決標日期: {detail_info.time_info.award_date}")
            
            # 顯示履約信息
            print(f"\n📍 === 履約信息 ===")
            print(f"履約地點: {detail_info.performance_info.performance_location}")
            print(f"履約地區: {detail_info.performance_info.performance_region}")
            print(f"是否受機關補助: {detail_info.performance_info.government_subsidy}")
            
            # 顯示廠商信息
            print(f"\n🏆 === 廠商信息 ===")
            print(f"投標廠商數: {detail_info.bidder_count}")
            print(f"提取到的廠商數: {len(detail_info.vendors)}")
            
            for i, vendor in enumerate(detail_info.vendors):
                print(f"\n廠商 {i+1}:")
                print(f"  廠商代碼: {vendor.vendor_code}")
                print(f"  廠商名稱: {vendor.vendor_name}")
                print(f"  是否得標: {vendor.is_winner}")
                print(f"  組織型態: {vendor.organization_type}")
                print(f"  廠商業別: {vendor.business_type}")
                print(f"  廠商地址: {vendor.vendor_address}")
                print(f"  廠商電話: {vendor.vendor_phone}")
                print(f"  決標金額: {vendor.award_amount}")
                print(f"  決標金額(中文): {vendor.award_amount_chinese}")
                print(f"  得標廠商國別: {vendor.winner_country}")
                print(f"  是否為中小企業: {vendor.is_sme}")
                print(f"  履約起迄日期: {vendor.performance_period}")
                print(f"  雇用員工總人數是否超過100人: {vendor.over_100_employees}")
            
            # 保存完整數據到文件
            output_data = {
                'test_url': test_url,
                'extraction_time': detail_info.agency_info.__dict__,  # 使用當前時間
                'agency_info': detail_info.agency_info.__dict__,
                'announcement_info': detail_info.announcement_info.__dict__,
                'time_info': detail_info.time_info.__dict__,
                'amount_info': detail_info.amount_info.__dict__,
                'procurement_nature': detail_info.procurement_nature.__dict__,
                'performance_info': detail_info.performance_info.__dict__,
                'vendors': [vendor.__dict__ for vendor in detail_info.vendors],
                'subject_classification': detail_info.subject_classification.__dict__,
                'bidder_count': detail_info.bidder_count
            }
            
            with open('test_detail_extraction_result.json', 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 完整數據已保存到: test_detail_extraction_result.json")
            
            # 分析提取效果
            print(f"\n📊 === 提取效果分析 ===")
            print(f"機關信息完整度: {'✅' if detail_info.agency_info.agency_name else '❌'}")
            print(f"標案信息完整度: {'✅' if detail_info.announcement_info.case_number else '❌'}")
            print(f"金額信息完整度: {'✅' if detail_info.amount_info.budget_amount else '❌'}")
            print(f"廠商信息完整度: {'✅' if detail_info.vendors else '❌'}")
            
            if detail_info.vendors:
                winner_count = sum(1 for v in detail_info.vendors if v.is_winner == '是')
                print(f"得標廠商數: {winner_count}")
                print(f"未得標廠商數: {len(detail_info.vendors) - winner_count}")
            
        else:
            print("❌ 詳情頁數據提取失敗")
            
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {str(e)}")

def analyze_html_structure():
    """分析HTML結構，幫助改進提取邏輯"""
    print("\n🔍 === 分析HTML結構 ===")
    
    crawler = EnhancedProcurementCrawler()
    test_url = "https://web.pcc.gov.tw/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetail?pkAtmMain=NzAxNTM4ODk="
    
    try:
        # 獲取原始HTML
        response = crawler._make_request(test_url)
        
        # 保存HTML到文件
        with open('test_detail_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        print("📄 HTML已保存到: test_detail_page.html")
        
        # 分析關鍵字段
        html_text = response.text
        
        print("\n🔍 關鍵字段分析:")
        
        # 檢查投標廠商相關字段
        if '投標廠商家數' in html_text:
            print("✅ 找到 '投標廠商家數' 字段")
        
        if '投標廠商1' in html_text:
            print("✅ 找到 '投標廠商1' 字段")
            
        if '廠商代碼' in html_text:
            print("✅ 找到 '廠商代碼' 字段")
            
        if '廠商名稱' in html_text:
            print("✅ 找到 '廠商名稱' 字段")
            
        if '決標金額' in html_text:
            print("✅ 找到 '決標金額' 字段")
            
        if '是否得標' in html_text:
            print("✅ 找到 '是否得標' 字段")
        
        # 統計廠商相關字段出現次數
        vendor_count = html_text.count('廠商名稱')
        print(f"📊 '廠商名稱' 出現次數: {vendor_count}")
        
        bidder_count = html_text.count('投標廠商')
        print(f"📊 '投標廠商' 出現次數: {bidder_count}")
        
    except Exception as e:
        print(f"❌ HTML結構分析失敗: {str(e)}")

if __name__ == "__main__":
    # 執行詳情頁數據提取測試
    test_detail_extraction()
    
    # 分析HTML結構
    analyze_html_structure()

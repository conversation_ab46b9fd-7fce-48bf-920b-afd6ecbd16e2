#!/usr/bin/env python3
"""
EdgeFN API 快速测试脚本
用于验证API连接和基本功能
"""

import requests
import json

def test_api():
    """测试API连接"""
    
    # API配置
    url = "https://api.edgefn.net/v1/chat/completions"
    api_key = "sk-HH2NyMUwCGoE211d3b59E69970Fc4410B724996bA4409015"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试消息
    data = {
        "model": "DeepSeek-R1-0528",
        "messages": [
            {"role": "user", "content": "请简单回复：你好"}
        ],
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    print("🔄 正在测试 EdgeFN API 连接...")
    print(f"📡 URL: {url}")
    print(f"🤖 模型: {data['model']}")
    print(f"💬 测试消息: {data['messages'][0]['content']}")
    print("-" * 50)
    
    try:
        # 发送请求
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        # 检查状态码
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 解析响应
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                reply = result['choices'][0]['message']['content']
                print("✅ API调用成功！")
                print(f"🤖 AI回复: {reply}")
                
                # 显示使用统计
                if 'usage' in result:
                    usage = result['usage']
                    print(f"\n📈 使用统计:")
                    print(f"   输入tokens: {usage.get('prompt_tokens', 'N/A')}")
                    print(f"   输出tokens: {usage.get('completion_tokens', 'N/A')}")
                    print(f"   总tokens: {usage.get('total_tokens', 'N/A')}")
                
                return True
            else:
                print("❌ 响应格式异常")
                print("完整响应:", json.dumps(result, indent=2, ensure_ascii=False))
                return False
        else:
            print(f"❌ API调用失败")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，请检查网络")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        print(f"响应内容: {response.text}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 EdgeFN API 测试工具")
    print("=" * 60)
    
    success = test_api()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！API工作正常")
        print("💡 您现在可以运行 'python edgefn_api_example.py' 来使用完整功能")
    else:
        print("⚠️  测试失败，请检查：")
        print("   1. 网络连接是否正常")
        print("   2. API Key是否有效")
        print("   3. API服务是否可用")
    print("=" * 60)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Web Crawling Demo Script using CrawlUtils

This script demonstrates how to use the CrawlUtils class to crawl web pages
and extract clean text content. It provides both single URL and batch URL
crawling capabilities with proper error handling and logging.
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import List, Optional

from crawl_utils import CrawlUtils


def setup_logging():
    """Configure logging for the script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('crawl_demo.log', encoding='utf-8')
        ]
    )


async def crawl_single_url(crawler: CrawlUtils, url: str) -> Optional[str]:
    """
    Crawl a single URL and return the extracted text.
    
    Args:
        crawler: CrawlUtils instance
        url: URL to crawl
        
    Returns:
        Extracted text or None if failed
    """
    print(f"\n🔍 正在爬取: {url}")
    
    try:
        text = await crawler.get_webpage_text(url)
        
        if text:
            print(f"✅ 成功获取文本")
            print(f"📊 文本长度: {len(text)} 字符")
            print(f"📊 单词数量: {len(text.split())} 个")
            print(f"📝 前200字符预览:")
            print("-" * 50)
            print(text[:200] + "..." if len(text) > 200 else text)
            print("-" * 50)
            return text
        else:
            print("❌ 获取文本失败")
            return None
            
    except Exception as e:
        print(f"❌ 爬取过程中出现错误: {e}")
        return None


async def crawl_multiple_urls(crawler: CrawlUtils, urls: List[str]) -> dict:
    """
    Crawl multiple URLs and return results.
    
    Args:
        crawler: CrawlUtils instance
        urls: List of URLs to crawl
        
    Returns:
        Dictionary with URL as key and extracted text as value
    """
    results = {}
    
    print(f"\n🚀 开始批量爬取 {len(urls)} 个URL...")
    
    for i, url in enumerate(urls, 1):
        print(f"\n[{i}/{len(urls)}] 处理中...")
        text = await crawl_single_url(crawler, url)
        results[url] = text
        
        # 添加延迟避免过于频繁的请求
        if i < len(urls):
            print("⏳ 等待2秒...")
            await asyncio.sleep(2)
    
    return results


def save_results_to_file(results: dict, filename: str = "crawl_results.txt"):
    """
    Save crawling results to a text file.
    
    Args:
        results: Dictionary with URL as key and text as value
        filename: Output filename
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("网页爬取结果\n")
            f.write("=" * 50 + "\n\n")
            
            for url, text in results.items():
                f.write(f"URL: {url}\n")
                f.write("-" * 30 + "\n")
                
                if text:
                    f.write(f"状态: 成功\n")
                    f.write(f"文本长度: {len(text)} 字符\n")
                    f.write(f"内容:\n{text}\n")
                else:
                    f.write("状态: 失败\n")
                
                f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"📁 结果已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 保存文件时出错: {e}")


async def interactive_mode():
    """Interactive mode for single URL crawling."""
    crawler = CrawlUtils()
    
    print("\n🌐 交互式网页爬取模式")
    print("输入 'quit' 或 'exit' 退出")
    
    while True:
        try:
            url = input("\n请输入要爬取的URL: ").strip()
            
            if url.lower() in ['quit', 'exit', 'q']:
                print("👋 再见!")
                break
                
            if not url:
                print("❌ URL不能为空")
                continue
                
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
                print(f"🔧 自动添加协议: {url}")
            
            text = await crawl_single_url(crawler, url)
            
            if text:
                save_choice = input("\n💾 是否保存结果到文件? (y/n): ").strip().lower()
                if save_choice in ['y', 'yes']:
                    filename = f"crawl_{url.replace('://', '_').replace('/', '_')}.txt"
                    save_results_to_file({url: text}, filename)
                    
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


async def batch_mode():
    """Batch mode for multiple URL crawling."""
    crawler = CrawlUtils()
    
    # 示例URL列表
    demo_urls = [
        "https://www.suanjiayun.com/help?id=684fe3c9071c181fe93c7d74",
        "https://www.suanjiayun.com/help?id=6746dd17e254decae19ccdb1",
        "https://www.suanjiayun.com/help?id=6746dda3e254decae19ccdb7",
        "https://www.suanjiayun.com/help?id=6746dd84e254decae19ccdb5",
    ]
    
    print("\n📋 批量爬取模式")
    print("使用示例URL列表进行演示...")
    
    for url in demo_urls:
        print(f"  - {url}")
    
    choice = input("\n是否使用这些示例URL? (y/n): ").strip().lower()
    
    if choice in ['y', 'yes']:
        urls = demo_urls
    else:
        print("\n请输入URL列表 (每行一个，输入空行结束):")
        urls = []
        while True:
            url = input().strip()
            if not url:
                break
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            urls.append(url)
        
        if not urls:
            print("❌ 没有输入任何URL")
            return
    
    results = await crawl_multiple_urls(crawler, urls)
    
    # 显示统计信息
    successful = sum(1 for text in results.values() if text is not None)
    failed = len(results) - successful
    
    print(f"\n📊 爬取统计:")
    print(f"  ✅ 成功: {successful}")
    print(f"  ❌ 失败: {failed}")
    print(f"  📝 总计: {len(results)}")
    
    # 保存结果
    save_choice = input("\n💾 是否保存所有结果到文件? (y/n): ").strip().lower()
    if save_choice in ['y', 'yes']:
        save_results_to_file(results, "batch_crawl_results.txt")


async def main():
    """Main function with menu system."""
    setup_logging()
    
    print("🕷️  网页爬取工具")
    print("=" * 30)
    print("基于 CrawlUtils 的网页内容提取工具")
    
    while True:
        print("\n📋 请选择模式:")
        print("1. 交互式模式 (单个URL)")
        print("2. 批量模式 (多个URL)")
        print("3. 退出")
        
        try:
            choice = input("\n请输入选择 (1-3): ").strip()
            
            if choice == '1':
                await interactive_mode()
            elif choice == '2':
                await batch_mode()
            elif choice == '3':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入 1-3")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 程序错误: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")

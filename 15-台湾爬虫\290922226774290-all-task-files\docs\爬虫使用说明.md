# 完美爬虫使用说明

## 概述

这是一个专业级的网页爬虫，专门用于爬取台湾政府采购网的公告信息。爬虫具有完整的反爬虫机制、并发控制、数据存储和错误处理功能。

## 主要特色功能

### 🚀 核心功能
- **智能反爬虫机制**：随机User-Agent、智能延迟、请求头轮换
- **并发爬取**：多线程并发处理，提高爬取效率
- **数据多样化存储**：支持CSV、JSON、SQLite数据库
- **完整错误处理**：自动重试、异常恢复、详细日志
- **实时监控**：爬取进度、统计信息、性能监控

### 🛡️ 反爬虫策略
- 随机User-Agent池（7种不同浏览器）
- 智能延迟机制（1-3秒随机延迟）
- 指数退避重试策略
- 会话保持和请求头管理
- 30%概率动态更换请求头

### 📊 数据处理
- HTML内容智能解析
- 文本自动清洗和格式化
- 结构化数据提取
- 多格式数据导出
- 增量更新支持

## 文件结构

```
/workspace/
├── code/
│   ├── perfect_crawler.py      # 主爬虫程序
│   ├── crawler_config.py       # 配置文件
│   └── read_docx.py            # 文档读取工具
├── data/
│   ├── csv/                    # CSV数据文件
│   ├── json/                   # JSON数据文件
│   └── database/               # SQLite数据库
├── logs/                       # 日志文件
└── docs/                       # 文档说明
```

## 快速开始

### 1. 基础使用

```python
from perfect_crawler import PerfectCrawler

# 创建爬虫实例
crawler = PerfectCrawler()

# 运行爬虫（爬取10页）
result = crawler.run(max_pages=10)

# 查看结果
print(f"爬取状态: {result['status']}")
print(f"总数据量: {result['total_items']}")
```

### 2. 自定义配置

```python
# 修改爬虫配置
crawler.config.update({
    'max_workers': 8,           # 增加并发数
    'delay_range': (2, 5),      # 增加延迟时间
    'max_pages': 100,           # 爬取更多页面
})

# 运行爬虫
result = crawler.run()
```

### 3. 单页爬取

```python
# 只爬取特定页面
page_data = crawler.crawl_single_page(page_num=1)
print(f"第1页数据量: {len(page_data)}")
```

## 配置说明

### 主要配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `max_workers` | int | 5 | 最大并发线程数 |
| `delay_range` | tuple | (1, 3) | 请求延迟范围（秒） |
| `retry_times` | int | 3 | 请求重试次数 |
| `timeout` | int | 30 | 请求超时时间（秒） |
| `max_pages` | int | 50 | 最大爬取页数 |

### 性能调优建议

- **并发控制**：`max_workers` 建议设置为 3-8，避免过高导致被封
- **延迟设置**：`delay_range` 建议保持 1-5 秒，平衡速度和稳定性
- **重试策略**：`retry_times` 建议 3-5 次，确保网络异常时的恢复

## 数据输出格式

### CSV文件示例
```csv
title,announcement_id,organization,announcement_date,url
"某某工程采购案","ABC123","某某政府机关","2025-07-15","https://..."
```

### JSON文件示例
```json
[
  {
    "title": "某某工程采购案",
    "announcement_id": "ABC123",
    "organization": "某某政府机关",
    "announcement_date": "2025-07-15",
    "url": "https://...",
    "content": "详细内容..."
  }
]
```

### 数据库表结构
```sql
CREATE TABLE announcements (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT,
    announcement_id TEXT UNIQUE,
    organization TEXT,
    procurement_type TEXT,
    announcement_date TEXT,
    deadline_date TEXT,
    budget_amount TEXT,
    contact_person TEXT,
    contact_phone TEXT,
    url TEXT,
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 监控和日志

### 日志级别
- **INFO**：正常爬取进度信息
- **WARNING**：请求失败、重试信息
- **ERROR**：严重错误、最终失败

### 统计信息
- 总请求数 / 成功请求数 / 失败请求数
- 请求成功率
- 总爬取条目数
- 平均爬取速度

### 实时监控
```python
# 查看实时统计
print(f"当前统计: {crawler.stats}")

# 查看配置信息
print(f"当前配置: {crawler.config}")
```

## 错误处理

### 常见错误和解决方案

1. **连接超时**
   - 增加 `timeout` 参数
   - 检查网络连接
   - 考虑使用代理

2. **被反爬虫拦截**
   - 增加 `delay_range` 延迟时间
   - 减少 `max_workers` 并发数
   - 更换IP地址或使用代理

3. **解析失败**
   - 检查网站结构是否变化
   - 更新解析规则
   - 查看日志了解具体错误

## 高级用法

### 1. 自定义解析规则

```python
def custom_parse(self, html_content):
    # 自定义解析逻辑
    pass

# 替换解析方法
crawler.parse_announcement_list = custom_parse
```

### 2. 添加代理支持

```python
# 设置代理
crawler.session.proxies = {
    'http': 'http://proxy-server:port',
    'https': 'https://proxy-server:port'
}
```

### 3. 数据过滤

```python
def filter_data(data_list):
    # 只保留特定条件的数据
    return [item for item in data_list if '工程' in item.get('title', '')]

# 应用过滤器
filtered_data = filter_data(all_data)
```

## 注意事项

### ⚠️ 重要提醒
1. **遵守网站条款**：确保爬取行为符合网站使用条款
2. **合理控制频率**：避免对目标网站造成过大压力
3. **数据使用合规**：确保数据使用符合相关法律法规
4. **定期更新维护**：网站结构变化时及时更新解析规则

### 🔧 维护建议
- 定期检查日志文件大小
- 清理过期的数据文件
- 监控爬虫性能指标
- 备份重要数据

## 技术支持

如遇到问题，请查看：
1. 日志文件：`/workspace/logs/crawler.log`
2. 错误报告：自动生成的爬取报告
3. 数据库记录：`crawl_records` 表中的执行记录

---

**作者**: MiniMax Agent  
**版本**: 1.0  
**更新时间**: 2025-07-15

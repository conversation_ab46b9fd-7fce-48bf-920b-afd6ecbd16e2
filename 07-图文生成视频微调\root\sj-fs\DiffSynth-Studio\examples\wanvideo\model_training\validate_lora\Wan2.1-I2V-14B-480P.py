#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P LoRA模型推理测试脚本
测试训练后的LoRA权重在图像到视频生成任务上的效果
"""

import torch
import os
import sys
from PIL import Image
from pathlib import Path
from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from modelscope import dataset_snapshot_download

def test_lora_inference(lora_path=None, output_name=None):
    """测试LoRA模型推理"""
    print("🎬 Wan2.1-I2V-14B-480P LoRA模型推理测试")
    print("=" * 60)

    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，请检查GPU环境")
        return False

    print(f"✅ 使用GPU: {torch.cuda.get_device_name()}")
    memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
    print(f"   显存: {memory_gb:.1f}GB")

    # 自动检测LoRA路径
    if lora_path is None:
        possible_paths = [
            "models/train/Wan2.1-I2V-14B-480P_lora/epoch-4.safetensors",
            "models/train/Wan2.1-I2V-14B-480P_8x3090_lora/epoch-2.safetensors",
            "models/train/Wan2.1-I2V-14B-480P_memory_optimized/epoch-1.safetensors"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                lora_path = path
                break

        if lora_path is None:
            print("❌ 未找到LoRA权重文件，请先完成训练")
            print("   期望路径:")
            for path in possible_paths:
                print(f"   - {path}")
            return False

    print(f"📦 加载LoRA权重: {lora_path}")

    # 创建pipeline
    print("🔧 初始化模型管道...")
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cuda",
        model_configs=[
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
        ],
    )

    # 加载LoRA权重
    try:
        pipe.load_lora(pipe.dit, lora_path, alpha=1)
        print("✅ LoRA权重加载成功")
    except Exception as e:
        print(f"❌ LoRA权重加载失败: {e}")
        return False

    pipe.enable_vram_management()

    # 准备输入图像
    print("📥 准备输入数据...")

    # 下载示例数据
    if not os.path.exists("data/example_video_dataset/video1.mp4"):
        print("📥 下载示例数据...")
        dataset_snapshot_download(
            dataset_id="DiffSynth-Studio/example_video_dataset",
            local_dir="./data/example_video_dataset"
        )

    # 使用视频第一帧作为输入图像
    input_image = VideoData("data/example_video_dataset/video1.mp4", height=480, width=832)[0]
    print(f"✅ 输入图像尺寸: {input_image.size}")

    # 生成视频
    print("🎬 开始生成视频...")

    try:
        video = pipe(
            prompt="from sunset to night, a small town, light, house, river",
            negative_prompt="色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走",
            input_image=input_image,
            seed=1,
            tiled=True,
            height=480,
            width=832,
            num_frames=25,  # 较短的视频用于测试
            num_inference_steps=20  # 较少步数用于快速测试
        )

        # 保存视频
        if output_name is None:
            output_name = f"video_Wan2.1-I2V-14B-480P_lora_{Path(lora_path).parent.name}.mp4"

        save_video(video, output_name, fps=15, quality=5)

        # 显示结果信息
        if os.path.exists(output_name):
            file_size = os.path.getsize(output_name) / 1024 / 1024
            print(f"✅ 视频生成成功！")
            print(f"   输出文件: {output_name}")
            print(f"   文件大小: {file_size:.2f} MB")
            print(f"   视频帧数: {len(video)}")
            return True
        else:
            print("❌ 视频保存失败")
            return False

    except Exception as e:
        print(f"❌ 视频生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Wan2.1-I2V-14B-480P LoRA推理测试")
    parser.add_argument("--lora_path", type=str, default=None, help="LoRA权重文件路径")
    parser.add_argument("--output", type=str, default=None, help="输出视频文件名")

    args = parser.parse_args()

    success = test_lora_inference(args.lora_path, args.output)

    print("\n" + "=" * 60)
    if success:
        print("🎉 LoRA模型推理测试完成！")
        print("💡 您已成功完成Wan2.1-I2V-14B-480P模型的LoRA微调和推理测试")
    else:
        print("⚠️  推理测试失败，请检查错误信息")
    print("=" * 60)

if __name__ == "__main__":
    main()

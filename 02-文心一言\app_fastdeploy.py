import gradio as gr
from fastdeploy import LLM, SamplingParams
import logging
import gc
import time

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FastDeployERNIE:
    def __init__(self):
        self.llm = None
        self.is_loaded = False
        self.model_name = "baidu/ERNIE-4.5-0.3B-Paddle"
        self.max_model_len = 32768
        
    def load_model(self):
        """加载FastDeploy ERNIE模型"""
        try:
            logger.info(f"正在加载模型: {self.model_name}")
            start_time = time.time()
            
            # 初始化LLM
            self.llm = LLM(
                model=self.model_name,
                max_model_len=self.max_model_len
            )
            
            load_time = time.time() - start_time
            self.is_loaded = True
            
            logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")
            return f"✅ 模型加载成功！\n模型: {self.model_name}\n加载时间: {load_time:.2f}秒"
            
        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            self.is_loaded = False
            return f"❌ 模型加载失败: {str(e)}\n\n请检查：\n1. FastDeploy是否正确安装\n2. 网络连接是否正常\n3. 模型路径是否正确"
    
    def generate_response(self, prompt, temperature=0.8, top_p=0.95, max_tokens=512):
        """生成回复"""
        if not self.is_loaded:
            return "❌ 请先加载模型！"
        
        if not prompt.strip():
            return "❌ 请输入有效的问题！"
        
        try:
            # 设置采样参数
            sampling_params = SamplingParams(
                temperature=temperature,
                top_p=top_p,
                max_tokens=max_tokens
            )
            
            logger.info(f"生成回复中... 参数: temp={temperature}, top_p={top_p}, max_tokens={max_tokens}")
            start_time = time.time()
            
            # 生成回复
            outputs = self.llm.generate(prompt, sampling_params)
            
            generation_time = time.time() - start_time
            
            # 提取生成的文本
            if outputs and len(outputs) > 0:
                # FastDeploy的正确格式: outputs[0].outputs.text
                response = outputs[0].outputs.text.strip()
                logger.info(f"生成完成，耗时: {generation_time:.2f}秒")
                return response
            else:
                return "❌ 模型没有生成有效回复，请尝试调整参数或重新提问。"
                
        except Exception as e:
            logger.error(f"生成回复失败: {str(e)}")
            return f"❌ 生成回复失败: {str(e)}"
    
    def get_model_info(self):
        """获取模型信息"""
        if self.is_loaded:
            return {
                "模型名称": self.model_name,
                "最大长度": self.max_model_len,
                "状态": "已加载"
            }
        else:
            return {
                "模型名称": self.model_name,
                "最大长度": self.max_model_len,
                "状态": "未加载"
            }

# 创建模型实例
ernie_model = FastDeployERNIE()

def load_model_interface():
    """模型加载接口"""
    return ernie_model.load_model()

def chat_interface(message, history, temperature, top_p, max_tokens):
    """聊天接口"""
    if not message.strip():
        return history, ""
    
    # 生成回复
    response = ernie_model.generate_response(
        message, 
        temperature=temperature,
        top_p=top_p,
        max_tokens=max_tokens
    )
    
    # 更新历史记录
    history.append([message, response])
    return history, ""

def get_model_status():
    """获取模型状态"""
    info = ernie_model.get_model_info()
    status_text = f"模型: {info['模型名称']}\n状态: {info['状态']}\n最大长度: {info['最大长度']}"
    return status_text

# 创建Gradio界面
with gr.Blocks(
    title="FastDeploy ERNIE-4.5 聊天机器人",
    theme=gr.themes.Soft(),
    css="""
    .gradio-container {
        max-width: 1400px !important;
    }
    .status-box {
        background-color: #f0f0f0;
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
    }
    """
) as demo:
    
    gr.HTML("""
    <div style="text-align: center; margin-bottom: 30px;">
        <h1>🚀 FastDeploy ERNIE-4.5 聊天机器人</h1>
        <p style="font-size: 18px; color: #666;">
            基于百度FastDeploy和ERNIE-4.5-0.3B-Paddle模型的高性能对话系统
        </p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=3):
            # 模型管理区域
            with gr.Group():
                gr.Markdown("### 🔧 模型管理")
                with gr.Row():
                    load_btn = gr.Button("🔄 加载模型", variant="primary", size="lg")
                    status_btn = gr.Button("📊 查看状态", variant="secondary")
                
                status_display = gr.Textbox(
                    label="模型状态",
                    value="⏳ 模型未加载，请点击'加载模型'按钮",
                    interactive=False,
                    lines=3
                )
            
            # 聊天区域
            with gr.Group():
                gr.Markdown("### 💬 智能对话")
                chatbot_interface = gr.Chatbot(
                    label="对话历史",
                    height=450,
                    show_copy_button=True,
                    avatar_images=("👤", "🤖"),
                    type="tuples"
                )
                
                with gr.Row():
                    msg_input = gr.Textbox(
                        label="输入消息",
                        placeholder="请输入您的问题或对话内容...",
                        lines=3,
                        scale=4
                    )
                    send_btn = gr.Button("📤 发送", variant="primary", scale=1, size="lg")
                
                with gr.Row():
                    clear_btn = gr.Button("🗑️ 清空对话", variant="secondary")
                    example_btn = gr.Button("💡 示例问题", variant="secondary")
        
        with gr.Column(scale=1):
            gr.Markdown("### ⚙️ 生成参数")
            
            temperature = gr.Slider(
                minimum=0.1,
                maximum=2.0,
                value=0.8,
                step=0.1,
                label="🌡️ 温度 (Temperature)",
                info="控制生成的随机性，越高越有创意"
            )
            
            top_p = gr.Slider(
                minimum=0.1,
                maximum=1.0,
                value=0.95,
                step=0.05,
                label="🎯 Top-p",
                info="控制词汇选择的多样性"
            )
            
            max_tokens = gr.Slider(
                minimum=50,
                maximum=2048,
                value=512,
                step=50,
                label="📏 最大生成长度",
                info="控制回复的最大字数"
            )
            
            gr.Markdown("""
            ### 📋 模型信息
            - **模型**: ERNIE-4.5-0.3B-Paddle
            - **框架**: FastDeploy
            - **最大长度**: 32,768 tokens
            - **特点**: 高性能推理，低延迟
            
            ### 💡 使用技巧
            - **温度 0.1-0.5**: 更准确、一致的回答
            - **温度 0.6-1.0**: 平衡准确性和创造性
            - **温度 1.1-2.0**: 更有创意但可能不太准确
            
            ### ⚡ 性能优势
            - FastDeploy优化的推理引擎
            - 支持GPU加速
            - 内存使用优化
            """)
    
    # 示例问题
    with gr.Row():
        gr.Examples(
            examples=[
                ["写一首关于大语言模型的诗"],
                ["解释什么是人工智能"],
                ["如何学习机器学习？"],
                ["介绍一下FastDeploy的优势"],
                ["写一个Python函数来计算斐波那契数列"]
            ],
            inputs=msg_input,
            label="🎯 示例问题"
        )
    
    # 事件绑定
    load_btn.click(
        fn=load_model_interface,
        outputs=status_display
    )
    
    status_btn.click(
        fn=get_model_status,
        outputs=status_display
    )
    
    send_btn.click(
        fn=chat_interface,
        inputs=[msg_input, chatbot_interface, temperature, top_p, max_tokens],
        outputs=[chatbot_interface, msg_input]
    )
    
    msg_input.submit(
        fn=chat_interface,
        inputs=[msg_input, chatbot_interface, temperature, top_p, max_tokens],
        outputs=[chatbot_interface, msg_input]
    )
    
    clear_btn.click(
        fn=lambda: [],
        outputs=chatbot_interface
    )
    
    example_btn.click(
        fn=lambda: "写一首关于大语言模型的诗",
        outputs=msg_input
    )

if __name__ == "__main__":
    print("🚀 启动FastDeploy ERNIE-4.5聊天机器人...")
    print("📍 访问地址: http://localhost:7860")
    print("💡 请确保已安装FastDeploy: pip install fastdeploy-gpu-python")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )

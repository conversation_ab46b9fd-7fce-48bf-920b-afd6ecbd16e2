#!/usr/bin/env python3
"""
DiffSynth-Studio Wan视频模型推理测试脚本
用于测试训练后的模型或预训练模型
"""

import os
import sys
import argparse
import torch
from pathlib import Path

def test_basic_inference():
    """测试基础的文字生成视频推理"""
    print("🧪 测试基础文字生成视频推理...")
    
    try:
        from diffsynth import save_video
        from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
        
        # 创建pipeline
        print("📦 加载模型...")
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda" if torch.cuda.is_available() else "cpu",
            model_configs=[
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="diffusion_pytorch_model*.safetensors",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="Wan2.1_VAE.pth",
                    offload_device="cpu"
                ),
            ],
        )
        
        # 启用显存管理
        pipe.enable_vram_management()
        
        print("🎬 生成测试视频...")
        
        # 生成视频
        video = pipe(
            prompt="一只可爱的小猫在阳光下的花园里玩耍，画面清晰，色彩鲜艳",
            negative_prompt="模糊，低质量，变形",
            seed=42,
            height=480,
            width=832,
            num_frames=49,  # 较短的视频用于测试
            num_inference_steps=20,  # 较少的步数用于快速测试
            tiled=True,
        )
        
        # 保存视频
        output_path = "test_output_t2v.mp4"
        save_video(video, output_path, fps=15, quality=5)
        
        print(f"✅ 测试成功！视频已保存到: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_lora_inference(lora_path):
    """测试LoRA模型推理"""
    print(f"🧪 测试LoRA模型推理: {lora_path}")
    
    if not os.path.exists(lora_path):
        print(f"❌ LoRA模型路径不存在: {lora_path}")
        return False
    
    try:
        from diffsynth import save_video
        from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
        
        # 创建pipeline
        print("📦 加载基础模型和LoRA...")
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda" if torch.cuda.is_available() else "cpu",
            model_configs=[
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="diffusion_pytorch_model*.safetensors",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-T2V-1.3B", 
                    origin_file_pattern="Wan2.1_VAE.pth",
                    offload_device="cpu"
                ),
            ],
        )
        
        # 加载LoRA权重
        print("🔧 加载LoRA权重...")
        pipe.dit.load_lora(lora_path)
        
        # 启用显存管理
        pipe.enable_vram_management()
        
        print("🎬 生成LoRA测试视频...")
        
        # 生成视频
        video = pipe(
            prompt="一只可爱的小猫在阳光下的花园里玩耍，画面清晰，色彩鲜艳",
            negative_prompt="模糊，低质量，变形",
            seed=42,
            height=480,
            width=832,
            num_frames=49,
            num_inference_steps=20,
            tiled=True,
        )
        
        # 保存视频
        output_path = "test_output_lora.mp4"
        save_video(video, output_path, fps=15, quality=5)
        
        print(f"✅ LoRA测试成功！视频已保存到: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ LoRA测试失败: {e}")
        return False

def test_i2v_inference(image_path=None):
    """测试图片生成视频推理"""
    print("🧪 测试图片生成视频推理...")
    
    try:
        from diffsynth import save_video
        from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
        from PIL import Image
        import numpy as np
        
        # 如果没有提供图片，创建一个简单的测试图片
        if image_path is None or not os.path.exists(image_path):
            print("📸 创建测试图片...")
            # 创建一个简单的渐变图片
            img_array = np.zeros((480, 832, 3), dtype=np.uint8)
            for i in range(480):
                img_array[i, :, 0] = int(255 * i / 480)  # 红色渐变
                img_array[i, :, 1] = int(128)  # 绿色固定
                img_array[i, :, 2] = int(255 * (480 - i) / 480)  # 蓝色反向渐变
            
            input_image = Image.fromarray(img_array)
            input_image.save("test_input_image.png")
            print("✅ 测试图片已创建: test_input_image.png")
        else:
            input_image = Image.open(image_path)
            print(f"📸 使用输入图片: {image_path}")
        
        # 创建I2V pipeline
        print("📦 加载I2V模型...")
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda" if torch.cuda.is_available() else "cpu",
            model_configs=[
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
                    origin_file_pattern="diffusion_pytorch_model*.safetensors",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
                    origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth",
                    offload_device="cpu"
                ),
                ModelConfig(
                    model_id="Wan-AI/Wan2.1-I2V-14B-480P", 
                    origin_file_pattern="Wan2.1_VAE.pth",
                    offload_device="cpu"
                ),
            ],
        )
        
        # 启用显存管理
        pipe.enable_vram_management()
        
        print("🎬 生成I2V测试视频...")
        
        # 生成视频
        video = pipe(
            prompt="图片中的场景开始动起来，自然流畅的运动",
            input_image=input_image,
            negative_prompt="静止，模糊，低质量",
            seed=42,
            height=480,
            width=832,
            num_frames=49,
            num_inference_steps=20,
            tiled=True,
        )
        
        # 保存视频
        output_path = "test_output_i2v.mp4"
        save_video(video, output_path, fps=15, quality=5)
        
        print(f"✅ I2V测试成功！视频已保存到: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ I2V测试失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="DiffSynth-Studio Wan视频模型推理测试")
    parser.add_argument("--test-type", "-t", 
                       choices=["basic", "lora", "i2v", "all"], 
                       default="basic",
                       help="测试类型")
    parser.add_argument("--lora-path", "-l", type=str,
                       help="LoRA模型路径")
    parser.add_argument("--image-path", "-i", type=str,
                       help="I2V测试用的输入图片路径")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎬 DiffSynth-Studio Wan视频模型推理测试")
    print("=" * 60)
    
    # 检查环境
    print("🔍 检查环境...")
    if not torch.cuda.is_available():
        print("⚠️  CUDA不可用，将使用CPU (速度会很慢)")
    else:
        print(f"✅ CUDA可用，GPU: {torch.cuda.get_device_name()}")
    
    success_count = 0
    total_tests = 0
    
    if args.test_type in ["basic", "all"]:
        print("\n" + "="*50)
        total_tests += 1
        if test_basic_inference():
            success_count += 1
    
    if args.test_type in ["lora", "all"]:
        print("\n" + "="*50)
        total_tests += 1
        lora_path = args.lora_path or "./models/lora"
        if os.path.exists(lora_path):
            if test_lora_inference(lora_path):
                success_count += 1
        else:
            print(f"⚠️  跳过LoRA测试，路径不存在: {lora_path}")
    
    if args.test_type in ["i2v", "all"]:
        print("\n" + "="*50)
        total_tests += 1
        if test_i2v_inference(args.image_path):
            success_count += 1
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试总结:")
    print(f"   成功: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    print("="*60)

if __name__ == "__main__":
    main()

﻿# 如何查看自己的手机信号强度，RSRP、RSSI、SINR，都是啥意思？

**发布日期**: 2023年11月18日

**原文链接**: https://www.ithome.com/0/733/444.htm

## 📄 原文内容

大家经常会遇到手机信号弱的情况。

一般来说，我们会通过手机右上角的信号标识，来判断当前的信号强度。

但实际上，这个判断方式太过简陋。不仅精度不高，也不一定准确。

作为通信老司机，今天小枣君给大家介绍一些更专业的手机信号判断方法。同时，也给大家科普一些关于手机信号的基本常识。

首先，我告诉大家一些手机自带的信号查看方式。无需借助第三方 App，就可以查看信号强度。

打开 iPhone 的拨号界面，在拨号键中输入 *3001#12345#* ，然后点击拨号按钮，就能查看手机的信号相关数据。

注意！不同版本的手机，这里的菜单内容会不一样。而且，要等一等，系统查询数据会需要一些时间。过一会儿，就会出来更多菜单。

点击【5G】下面的【NR Rach Attempt】，可以查看 5G 信号强度的数值（cell_rsrp，下图是-98）：

点击【4G】下面的【Rach Attempt】，可以查看 4G 信号强度的数值（rsrp，下图是-87）：

这些数值的单位是 dBm。dBm 是通过对数计算得出的，所以一般是负数值。（关于 dBm 的具体介绍，看这里： dB、dBm、dBw、dBi…… 到底有啥区别？ ）

注意，dBm 的绝对值越小，表示信号质量越好。

在【RAT】的【Serving Cell Info】，还可以看到下面这些信息：

小枣君简单给大家解读几个比较重要的参数。

PLMN ：运营商的公共陆地移动网编码。每个运营商都不一样。PLMN 由 MCC（移动国家码，3 位）和 MNC（移动网络码，2-3 位）组成。46011 是中国电信的 4G / 5G。

KCTCellMonitorBWPSupport ：载波带宽自适应的支持类型。

Band Info ：频段编号，图上的 78 是指 n78。

Bandwidth：频率带宽，图上是 20MHz。

Radio Access：无线接入类型。NR 就是 5G，LTE 是 4G。

NRARFCN：5G 频点号。频段范围 0-100GHz 划出了总共 3279165 个栅格。这些栅格从 0 开始编号，一直编号到 3279165。每个编号都代表着一个绝对的频域位置，这些编号就叫做 NR-ARFCN。这个频点号有自己的计算公式，网上可查。

PCI：Physical Cell Identifier，物理小区标识。用于区分不同小区的无线信号。

TAC：Tracking Area Code，跟踪区代码。由运营商自行分配，主要起移动用户定位唯一标识作用。

不同品牌的安卓手机，查看信号强度的方式不太一样。

我们以小米（MIUI 14）为例，依次打开手机的【设置】-【我的设备】-【全部参数与信息】-【状态信息】-【SIM 卡状态（SIM 卡插槽 1）】（或者插槽 2），就可以查看到实时信号强度。如下图：

信号强度是-93dBm。asu 是 Arbitrary Strength Unit，主观强度单位，可以看成是信号强度的相对值。

asu 可以通过 dBm 得到，计算公式如下：

网络类型是 5G_SA，表示是 5G 独立组网（Standalone）。关于 SA 和 NSA，可以看这里：5G 的 NSA 和 SA，到底啥意思？

好了，以上是手机自带的查看方式。大家可以看到，查看的信息还是比较简单。

想要查看更详细的信息，怎么办？那就要借助第三方 App 软件了。

这类的软件比较多，我比较常用的是 Cellular-Z。手机自带的应用商店应该都能下载到。不建议大家去网上找安装包，容易中毒。

Cellular-Z 安装好之后，启动它，进入主界面：

可以看到，它功能比较全 —— 除了手机两个卡槽的信号状态外，还能够看 Wi-Fi 和 GNSS（卫星定位），以及手机自身完整信息。

卡槽信息的各个参数，小枣君刚才已经解读过，这里就不重复介绍了。有一个 NR-FREQ，是手机目前具体的 5G 频率。

信号强度，显示了数值，也显示了动态图。前面说过，数值的绝对值越低，信号越好。

信号有好几个参数，我们分别说一下。（比较专业，不想看可以跳过）

RSRP：Reference Signal Receiving Power，参考信号接收功率。

我们主要看的就是这个参数。它是在某个 Symbol（符号）内承载参考信号的所有 RE（资源元素）上接收到的信号功率的平均值。范围在-44 与-140 之间，越大越好。

RSRQ：Reference Signal Receiving Quality，参考信号接收质量。

它是 RSRP 和 RSSI 的比值，范围在-3 于-19.5 之间，越大越好。它主要是根据信号质量来对不同 LTE 候选小区进行排序。这种测量用作切换和小区重选决定的输入。

RSSI：Received Signal Strength Indication，接收的信号强度指示。

它随距离的增大而衰减，通常为负值，该值越接近零说明信号强度越高。RSSI 持续过低，说明基站收到的上行信号太弱，可能导致解调失败。RSSI 持续过高，说明收到的上行信号太强，相互之间的干扰太大，也影响信号解调。

SINR：Signal to Interference plus Noise Ratio，信号与干扰加噪声比。

它表示接收到的有用信号的强度与接收到的干扰信号（噪声和干扰）的强度的比值。

Wi-Fi 分为 2.4GHz 频段和 5GHz 频段（还有 6GHz 频段，目前国内没开放）。两个频段的信道包括以下：

大家注意，2.4GHz 频段非常拥挤。手机尽量不要使用 2.4G 频段，应该使用 5GHz 频段。家里智能家居产品一般只支持 2.4G 频段，可以凑合用。

通过这个工具，大家可以查看家里哪个信道比较空闲。

然后，在自家 Wi-Fi 路由器上，可以设置一下，采用这个空闲的信道（例如上图的 48 信道，属于 5GHz 频段）。Wi-Fi 信号体验会好很多很多。

有时候，信号强度高，不一定代表网络速度快。当用户接入数量太多，或者基站的传输资源拥塞，也可能出现“信号满格，网速却很慢”的情况。

测速的话，一般也是借助第三方 App，例如 Speedtest。

开启位置授权，以便选择一个距离较近的测速点，然后启动测速。

通常情况下，5G 测速不会超过 1000Mbps。大几百 Mbps 的速率，就已经极好了。几十 Mbps，看视频和聊天没什么问题。如果只有几 Mbps，网络体验就有点够呛了。

如果经常出现网络信号质量差的问题，可以打运营商客服电话进行投诉。如果运营商迟迟无法解决，也可以打工信部电话（12321）进行投诉。

在一些神秘单位的附近，或者在中兴华为等设备商研发大楼的附近，经常会出现手机信号问题，可能是因为干扰。这个也是可以投诉的。

再次提醒大家，基站信号差，个人、物业或公司是无能为力的，只能让运营商解决。除了运营商之外，任何人或单位都不允许自建公网通信基站。

如果基站信号差，你可以通过拉线部署 Wi-Fi 实现上网需求，但是 Wi-Fi 并不能让你接打电话或发短信（微信视频通话这样的互联网电话业务除外）。所以，还是会出现别人打你的电话，提示“您拨打的电话无法接通”的情况。

好了，以上就是今天的科普。大家都看懂了吗？

本文来自微信公众号： 鲜枣课堂 （ID：xzclasscom） ，作者：小枣君

下载IT之家APP，签到赚金币兑豪礼

相关文章 关键词： 网络 ， 通信 11 月国产网络游戏版号下发：87 款游戏获批，《来自星尘》《诛仙 2》及 Switch 版《落日山丘》过审 微博公布“清朗・网络戾气整治”行动阶段性成果：清理违规内容 14.4 万余条，处置账号 5200 余个 《咬文嚼字》公布今年十大流行语：人工智能大模型、村超等上榜 深圳 2024 年新规：网络平台不得默认勾选自动续费 工信部回复网友“保障老旧手机基础通信”建议：2G / 3G 退网是必然选择，但也要保障用户权益 严重的电路故障往往都有含金量较高的原因

相关文章 关键词： 网络 ， 通信
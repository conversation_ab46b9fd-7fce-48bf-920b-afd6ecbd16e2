import requests
import json

# 基础配置
url = "https://api.edgefn.net/v1/chat/completions"
headers = {
    "Authorization": "Bearer sk-8ktBTPRMiWi6aj6b68414d5968394eEaBfCbC9A938292e93",
    "Content-Type": "application/json"
}

def test_model(model_name):
    """测试指定模型"""
    data = {
        "model": model_name,
        "messages": [{"role": "user", "content": "Hello, how are you?"}]
    }

    print(f"\n🤖 测试模型: {model_name}")
    print("-" * 50)

    try:
        response = requests.post(url, headers=headers, json=data)
        result = response.json()

        if response.status_code == 200:
            print("✅ 成功!")
            if 'choices' in result:
                content = result['choices'][0]['message']['content']
                print(f"💬 回复: {content}")

                if 'usage' in result:
                    usage = result['usage']
                    print(f"📊 Tokens: {usage.get('total_tokens', 'N/A')}")
            else:
                print("📋 完整响应:", json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print("❌ 失败!")
            print(f"📋 错误: {result}")

    except Exception as e:
        print(f"❌ 异常: {e}")

# 测试不同模型
print("=" * 60)
print("🧪 EdgeFN API 模型测试")
print("=" * 60)

# 测试您要求的模型
test_model("DeepSeek-R1-0528-Qwen3-8B")

# 测试已知可用的模型
test_model("DeepSeek-R1-0528")

print("\n" + "=" * 60)
print("📝 基础代码示例（您的格式）:")
print("=" * 60)

# 您原始的代码格式（修正版）
data = {
    "model": "DeepSeek-R1-0528",  # 使用可用的模型
    "messages": [{"role": "user", "content": "Hello, how are you?"}]
}

response = requests.post(url, headers=headers, json=data)
print(response.json())

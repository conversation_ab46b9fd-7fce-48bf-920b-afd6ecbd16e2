#!/usr/bin/env python3
"""
HTML生成器 - 將採購數據轉換為網頁格式
"""

import json
from datetime import datetime

def generate_html_report(json_file, output_html, title="政府採購數據報告"):
    """生成HTML格式的數據報告"""
    
    print(f"📖 正在讀取數據文件: {json_file}")
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 成功讀取 {len(data)} 筆數據")
        
        # 生成HTML內容
        html_content = generate_html_content(data, title)
        
        # 保存HTML文件
        with open(output_html, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"💾 HTML報告已保存到: {output_html}")
        
    except Exception as e:
        print(f"❌ 生成HTML報告時發生錯誤: {str(e)}")

def generate_html_content(data, title):
    """生成HTML內容"""
    
    html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }}
        .summary {{
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }}
        .procurement-item {{
            border: 1px solid #ddd;
            margin-bottom: 30px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .item-header {{
            background-color: #3498db;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
        }}
        .item-content {{
            padding: 20px;
        }}
        .section {{
            margin-bottom: 25px;
        }}
        .section-title {{
            background-color: #34495e;
            color: white;
            padding: 10px 15px;
            margin: 0 0 15px 0;
            font-weight: bold;
            border-radius: 5px;
        }}
        .info-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }}
        .info-item {{
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #3498db;
            border-radius: 4px;
        }}
        .info-label {{
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }}
        .info-value {{
            color: #34495e;
        }}
        .vendor-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }}
        .vendor-table th, .vendor-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        .vendor-table th {{
            background-color: #34495e;
            color: white;
        }}
        .vendor-table tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .winner {{
            background-color: #d4edda !important;
            color: #155724;
            font-weight: bold;
        }}
        .no-data {{
            color: #7f8c8d;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }}
        .timestamp {{
            text-align: center;
            color: #7f8c8d;
            margin-top: 30px;
            font-size: 14px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{title}</h1>
        
        <div class="summary">
            <h3>📊 數據概覽</h3>
            <p><strong>總計：</strong>{len(data)} 筆採購數據</p>
            <p><strong>生成時間：</strong>{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
"""
    
    # 為每筆數據生成HTML
    for i, item in enumerate(data):
        html += generate_item_html(item, i + 1)
    
    html += f"""
        <div class="timestamp">
            報告生成時間：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
        </div>
    </div>
</body>
</html>"""
    
    return html

def generate_item_html(item, index):
    """為單個採購項目生成HTML"""
    
    list_data = item.get('list_data', {})
    detail_data = item.get('detail_data', {})
    
    html = f"""
        <div class="procurement-item">
            <div class="item-header">
                第 {index} 筆：{list_data.get('title', '無標題')} - {list_data.get('agency', '無機關')}
            </div>
            <div class="item-content">
                
                <!-- 基本信息 -->
                <div class="section">
                    <div class="section-title">📋 基本信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">標案名稱</div>
                            <div class="info-value">{list_data.get('title', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">機關名稱</div>
                            <div class="info-value">{list_data.get('agency', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">標案類型</div>
                            <div class="info-value">{list_data.get('tender_type', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">招標日期</div>
                            <div class="info-value">{list_data.get('tender_date', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">決標日期</div>
                            <div class="info-value">{list_data.get('award_date', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">標案案號</div>
                            <div class="info-value">{detail_data.get('announcement_info', {}).get('case_number', '無')}</div>
                        </div>
                    </div>
                </div>
                
                <!-- 機關詳細信息 -->
                {generate_agency_info_html(detail_data.get('agency_info', {}))}
                
                <!-- 金額信息 -->
                {generate_amount_info_html(detail_data.get('amount_info', {}))}
                
                <!-- 時間信息 -->
                {generate_time_info_html(detail_data.get('time_info', {}))}
                
                <!-- 履約信息 -->
                {generate_performance_info_html(detail_data.get('performance_info', {}))}
                
                <!-- 廠商信息 -->
                {generate_vendors_info_html(detail_data.get('vendors', []), detail_data.get('bidder_count', 0))}
                
                <!-- 標的分類 -->
                {generate_classification_info_html(detail_data.get('subject_classification', {}))}
                
            </div>
        </div>
    """
    
    return html

def generate_agency_info_html(agency_info):
    """生成機關信息HTML"""
    if not any(agency_info.values()):
        return '<div class="no-data">無機關詳細信息</div>'
    
    return f"""
                <div class="section">
                    <div class="section-title">🏢 機關信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">機關代碼</div>
                            <div class="info-value">{agency_info.get('agency_code', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">機關名稱</div>
                            <div class="info-value">{agency_info.get('agency_name', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">單位名稱</div>
                            <div class="info-value">{agency_info.get('unit_name', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">機關地址</div>
                            <div class="info-value">{agency_info.get('agency_address', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">聯絡人</div>
                            <div class="info-value">{agency_info.get('contact_person', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">聯絡電話</div>
                            <div class="info-value">{agency_info.get('contact_phone', '無')}</div>
                        </div>
                    </div>
                </div>
    """

def generate_amount_info_html(amount_info):
    """生成金額信息HTML"""
    if not any(amount_info.values()):
        return '<div class="no-data">無金額信息</div>'
    
    return f"""
                <div class="section">
                    <div class="section-title">💰 金額信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">預算金額</div>
                            <div class="info-value">{amount_info.get('budget_amount', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">預算金額（中文）</div>
                            <div class="info-value">{amount_info.get('budget_amount_chinese', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">採購金額級距</div>
                            <div class="info-value">{amount_info.get('procurement_amount_range', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">預算金額是否公開</div>
                            <div class="info-value">{amount_info.get('budget_public', '無')}</div>
                        </div>
                    </div>
                </div>
    """

def generate_time_info_html(time_info):
    """生成時間信息HTML"""
    if not any(time_info.values()):
        return '<div class="no-data">無時間信息</div>'
    
    return f"""
                <div class="section">
                    <div class="section-title">⏰ 時間信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">開標時間</div>
                            <div class="info-value">{time_info.get('opening_time', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">原公告日期</div>
                            <div class="info-value">{time_info.get('original_announcement_date', '無')}</div>
                        </div>
                    </div>
                </div>
    """

def generate_performance_info_html(performance_info):
    """生成履約信息HTML"""
    if not any(performance_info.values()):
        return '<div class="no-data">無履約信息</div>'
    
    return f"""
                <div class="section">
                    <div class="section-title">📍 履約信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">履約地點</div>
                            <div class="info-value">{performance_info.get('performance_location', '無')}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">是否受機關補助</div>
                            <div class="info-value">{performance_info.get('government_subsidy', '無')}</div>
                        </div>
                    </div>
                </div>
    """

def generate_vendors_info_html(vendors, bidder_count):
    """生成廠商信息HTML"""
    if not vendors:
        return f"""
                <div class="section">
                    <div class="section-title">🏆 廠商信息</div>
                    <div class="no-data">投標廠商數：{bidder_count}，但無詳細廠商信息</div>
                </div>
        """
    
    vendor_rows = ""
    for vendor in vendors:
        winner_class = "winner" if vendor.get('is_winner') == '是' else ""
        vendor_rows += f"""
                        <tr class="{winner_class}">
                            <td>{vendor.get('vendor_name', '無')}</td>
                            <td>{vendor.get('vendor_code', '無')}</td>
                            <td>{vendor.get('is_winner', '無')}</td>
                            <td>{vendor.get('award_amount', '無')}</td>
                            <td>{vendor.get('organization_type', '無')}</td>
                            <td>{vendor.get('vendor_address', '無')}</td>
                        </tr>
        """
    
    return f"""
                <div class="section">
                    <div class="section-title">🏆 廠商信息（投標廠商數：{bidder_count}）</div>
                    <table class="vendor-table">
                        <thead>
                            <tr>
                                <th>廠商名稱</th>
                                <th>廠商代碼</th>
                                <th>是否得標</th>
                                <th>決標金額</th>
                                <th>組織型態</th>
                                <th>廠商地址</th>
                            </tr>
                        </thead>
                        <tbody>
                            {vendor_rows}
                        </tbody>
                    </table>
                </div>
    """

def generate_classification_info_html(classification):
    """生成標的分類信息HTML"""
    if not any(classification.values()):
        return '<div class="no-data">無標的分類信息</div>'
    
    return f"""
                <div class="section">
                    <div class="section-title">📂 標的分類</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">分類名稱</div>
                            <div class="info-value">{classification.get('classification_name', '無')}</div>
                        </div>
                    </div>
                </div>
    """

def main():
    """主函數"""
    print("🌐 === HTML報告生成器 ===")
    
    # 生成原始數據的HTML報告
    generate_html_report(
        'defense_procurement_data_original_cleaned.json',
        'defense_procurement_report_traditional.html',
        '政府採購數據報告（繁體中文）'
    )
    
    # 生成簡體中文數據的HTML報告
    generate_html_report(
        'defense_procurement_data_simplified.json',
        'defense_procurement_report_simplified.html',
        '政府采购数据报告（简体中文）'
    )
    
    print(f"\n✅ === HTML報告生成完成 ===")
    print(f"📁 生成的HTML文件:")
    print(f"  1. defense_procurement_report_traditional.html - 繁體中文網頁報告")
    print(f"  2. defense_procurement_report_simplified.html - 簡體中文網頁報告")

if __name__ == "__main__":
    main()

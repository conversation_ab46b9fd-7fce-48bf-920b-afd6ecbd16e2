<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政府采购爬虫系统</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .section { 
            margin-bottom: 30px; 
            padding: 20px; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            background-color: #fafafa;
        }
        h1 { 
            color: #007bff; 
            margin: 0;
        }
        h2 { 
            margin-top: 0; 
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="number"], select { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            font-size: 14px;
            box-sizing: border-box;
        }
        button { 
            padding: 10px 20px; 
            margin-right: 10px; 
            margin-bottom: 10px;
            cursor: pointer; 
            border: none;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #117a8b;
        }
        pre { 
            background-color: #f8f9fa; 
            padding: 15px; 
            border: 1px solid #e9ecef; 
            border-radius: 4px; 
            white-space: pre-wrap; 
            word-wrap: break-word; 
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .status-running {
            color: #007bff;
            font-weight: bold;
        }
        .status-complete {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
        }
        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin-right: 5px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .stats-card {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .stats-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        .pagination button {
            padding: 8px 12px;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>政府采购爬虫系统</h1>
            <p>台湾政府电子采购网数据抓取与查询系统</p>
        </div>

        <!-- 爬虫控制 -->
        <div class="section">
            <h2>启动爬虫任务</h2>
            <div class="form-group">
                <label for="crawl-keyword">搜索关键词：</label>
                <input type="text" id="crawl-keyword" placeholder="输入搜索关键词（可选）">
            </div>
            <div class="form-group">
                <label>标案类型：</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="type-tender" value="招标" checked>
                        <label for="type-tender">招标</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="type-award" value="决标" checked>
                        <label for="type-award">决标</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="type-public" value="公开閱覽及公開徵求">
                        <label for="type-public">公开阅览及公开征求</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="type-forecast" value="政府採購預告">
                        <label for="type-forecast">政府采购预告</label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="max-pages">最大页数：</label>
                <input type="number" id="max-pages" value="5" min="1" max="20">
            </div>
            <div class="form-group">
                <label for="page-size">每页数量：</label>
                <select id="page-size">
                    <option value="10">10</option>
                    <option value="20" selected>20</option>
                    <option value="50">50</option>
                </select>
            </div>
            <button class="btn-primary" onclick="startCrawl()">启动爬虫</button>
            <button class="btn-info" onclick="getCrawlStatus()">查看状态</button>
            
            <div id="crawl-status" style="margin-top: 15px;"></div>
            <div id="crawl-progress" style="margin-top: 10px;"></div>
        </div>

        <!-- 数据搜索 -->
        <div class="section">
            <h2>搜索采购数据</h2>
            <div class="form-group">
                <label for="search-keyword">搜索关键词：</label>
                <input type="text" id="search-keyword" placeholder="输入关键词搜索标案名称、机关名称或内容">
            </div>
            <div class="form-group">
                <label for="search-type">标案类型：</label>
                <select id="search-type">
                    <option value="">全部类型</option>
                    <option value="招标">招标</option>
                    <option value="决标">决标</option>
                    <option value="公开閱覽及公開徵求">公开阅览及公开征求</option>
                    <option value="政府採購預告">政府采购预告</option>
                </select>
            </div>
            <button class="btn-success" onclick="searchData()">搜索数据</button>
            <button class="btn-info" onclick="getStats()">获取统计</button>
            
            <div id="search-results" style="margin-top: 20px;"></div>
            <div id="pagination" class="pagination"></div>
        </div>

        <!-- 统计信息 -->
        <div class="section">
            <h2>数据统计</h2>
            <div id="stats-content"></div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let statusInterval = null;

        // 启动爬虫
        async function startCrawl() {
            const keyword = document.getElementById('crawl-keyword').value;
            const maxPages = parseInt(document.getElementById('max-pages').value);
            const pageSize = parseInt(document.getElementById('page-size').value);
            
            // 获取选中的标案类型
            const tenderTypes = [];
            if (document.getElementById('type-tender').checked) tenderTypes.push('招标');
            if (document.getElementById('type-award').checked) tenderTypes.push('决标');
            if (document.getElementById('type-public').checked) tenderTypes.push('公开閱覽及公開徵求');
            if (document.getElementById('type-forecast').checked) tenderTypes.push('政府採購預告');
            
            if (tenderTypes.length === 0) {
                alert('请至少选择一种标案类型');
                return;
            }

            try {
                const response = await fetch('/api/procurement/crawl', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        keyword: keyword,
                        tender_types: tenderTypes,
                        max_pages: maxPages,
                        page_size: pageSize
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    document.getElementById('crawl-status').innerHTML = 
                        '<span class="status-running">爬虫任务已启动</span>';
                    
                    // 开始定期检查状态
                    if (statusInterval) clearInterval(statusInterval);
                    statusInterval = setInterval(getCrawlStatus, 2000);
                } else {
                    document.getElementById('crawl-status').innerHTML = 
                        '<span class="status-error">启动失败: ' + data.error + '</span>';
                }
            } catch (error) {
                document.getElementById('crawl-status').innerHTML = 
                    '<span class="status-error">启动失败: ' + error.message + '</span>';
            }
        }

        // 获取爬虫状态
        async function getCrawlStatus() {
            try {
                const response = await fetch('/api/procurement/crawl/status');
                const data = await response.json();
                
                if (data.success) {
                    const status = data.data;
                    let statusClass = 'status-running';
                    if (!status.is_running && status.message.includes('完成')) {
                        statusClass = 'status-complete';
                        if (statusInterval) {
                            clearInterval(statusInterval);
                            statusInterval = null;
                        }
                    } else if (!status.is_running && status.message.includes('出错')) {
                        statusClass = 'status-error';
                        if (statusInterval) {
                            clearInterval(statusInterval);
                            statusInterval = null;
                        }
                    }
                    
                    document.getElementById('crawl-status').innerHTML = 
                        '<span class="' + statusClass + '">' + status.message + '</span>';
                    
                    // 显示进度条
                    if (status.total > 0) {
                        const progress = (status.progress / status.total) * 100;
                        document.getElementById('crawl-progress').innerHTML = 
                            '<div class="progress-bar">' +
                            '<div class="progress-fill" style="width: ' + progress + '%"></div>' +
                            '</div>' +
                            '<div>进度: ' + status.progress + '/' + status.total + '</div>';
                    }
                }
            } catch (error) {
                console.error('获取状态失败:', error);
            }
        }

        // 搜索数据
        async function searchData(page = 1) {
            const keyword = document.getElementById('search-keyword').value;
            const tenderType = document.getElementById('search-type').value;
            
            try {
                const params = new URLSearchParams({
                    page: page,
                    per_page: 10
                });
                
                if (keyword) params.append('keyword', keyword);
                if (tenderType) params.append('tender_type', tenderType);
                
                const response = await fetch('/api/procurement/search?' + params);
                const data = await response.json();
                
                if (data.success) {
                    displaySearchResults(data.data, data.pagination);
                    currentPage = page;
                } else {
                    document.getElementById('search-results').innerHTML = 
                        '<div class="status-error">搜索失败: ' + data.error + '</div>';
                }
            } catch (error) {
                document.getElementById('search-results').innerHTML = 
                    '<div class="status-error">搜索失败: ' + error.message + '</div>';
            }
        }

        // 显示搜索结果
        function displaySearchResults(results, pagination) {
            let html = '<h3>搜索结果</h3>';
            
            if (results.length === 0) {
                html += '<p>没有找到匹配的数据</p>';
            } else {
                html += '<table>';
                html += '<tr><th>标案名称</th><th>机关名称</th><th>类型</th><th>公告日期</th><th>操作</th></tr>';
                
                results.forEach(item => {
                    html += '<tr>';
                    html += '<td>' + (item.title || '') + '</td>';
                    html += '<td>' + (item.agency || '') + '</td>';
                    html += '<td>' + (item.tender_type || '') + '</td>';
                    html += '<td>' + (item.announcement_date || '') + '</td>';
                    html += '<td><button class="btn-info" onclick="viewDetail(' + item.id + ')">查看详情</button></td>';
                    html += '</tr>';
                });
                
                html += '</table>';
                
                // 分页
                if (pagination.pages > 1) {
                    html += '<div class="pagination">';
                    if (pagination.page > 1) {
                        html += '<button class="btn-primary" onclick="searchData(' + (pagination.page - 1) + ')">上一页</button>';
                    }
                    html += '<span>第 ' + pagination.page + ' 页，共 ' + pagination.pages + ' 页</span>';
                    if (pagination.page < pagination.pages) {
                        html += '<button class="btn-primary" onclick="searchData(' + (pagination.page + 1) + ')">下一页</button>';
                    }
                    html += '</div>';
                }
            }
            
            document.getElementById('search-results').innerHTML = html;
        }

        // 查看详情
        async function viewDetail(id) {
            try {
                const response = await fetch('/api/procurement/data/' + id);
                const data = await response.json();
                
                if (data.success) {
                    const item = data.data;
                    let detailHtml = '<h3>详细信息</h3>';
                    detailHtml += '<p><strong>标案名称:</strong> ' + (item.title || '') + '</p>';
                    detailHtml += '<p><strong>机关名称:</strong> ' + (item.agency || '') + '</p>';
                    detailHtml += '<p><strong>案号:</strong> ' + (item.case_number || '') + '</p>';
                    detailHtml += '<p><strong>类型:</strong> ' + (item.tender_type || '') + '</p>';
                    detailHtml += '<p><strong>公告日期:</strong> ' + (item.announcement_date || '') + '</p>';
                    if (item.url) {
                        detailHtml += '<p><strong>原始链接:</strong> <a href="' + item.url + '" target="_blank">查看原文</a></p>';
                    }
                    if (item.content) {
                        detailHtml += '<p><strong>详细内容:</strong></p>';
                        detailHtml += '<pre>' + item.content + '</pre>';
                    }
                    
                    // 创建模态框显示详情
                    const modal = document.createElement('div');
                    modal.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:1000;display:flex;align-items:center;justify-content:center;';
                    modal.innerHTML = '<div style="background:white;padding:20px;border-radius:8px;max-width:80%;max-height:80%;overflow-y:auto;position:relative;">' +
                        '<button onclick="this.parentElement.parentElement.remove()" style="position:absolute;top:10px;right:10px;background:#dc3545;color:white;border:none;padding:5px 10px;border-radius:4px;cursor:pointer;">关闭</button>' +
                        detailHtml + '</div>';
                    document.body.appendChild(modal);
                } else {
                    alert('获取详情失败: ' + data.error);
                }
            } catch (error) {
                alert('获取详情失败: ' + error.message);
            }
        }

        // 获取统计信息
        async function getStats() {
            try {
                const response = await fetch('/api/procurement/stats');
                const data = await response.json();
                
                if (data.success) {
                    displayStats(data.data);
                } else {
                    document.getElementById('stats-content').innerHTML = 
                        '<div class="status-error">获取统计失败: ' + data.error + '</div>';
                }
            } catch (error) {
                document.getElementById('stats-content').innerHTML = 
                    '<div class="status-error">获取统计失败: ' + error.message + '</div>';
            }
        }

        // 显示统计信息
        function displayStats(stats) {
            let html = '<div class="stats-grid">';
            
            // 总数统计
            html += '<div class="stats-card">';
            html += '<h3>总数据量</h3>';
            html += '<div class="stats-number">' + stats.total_count + '</div>';
            html += '<p>条采购信息</p>';
            html += '</div>';
            
            // 类型统计
            html += '<div class="stats-card">';
            html += '<h3>按类型统计</h3>';
            if (stats.type_stats && stats.type_stats.length > 0) {
                html += '<table>';
                stats.type_stats.forEach(item => {
                    html += '<tr><td>' + item.type + '</td><td>' + item.count + '</td></tr>';
                });
                html += '</table>';
            } else {
                html += '<p>暂无数据</p>';
            }
            html += '</div>';
            
            // 机关统计
            html += '<div class="stats-card">';
            html += '<h3>主要机关（前10）</h3>';
            if (stats.agency_stats && stats.agency_stats.length > 0) {
                html += '<table>';
                stats.agency_stats.forEach(item => {
                    html += '<tr><td>' + item.agency + '</td><td>' + item.count + '</td></tr>';
                });
                html += '</table>';
            } else {
                html += '<p>暂无数据</p>';
            }
            html += '</div>';
            
            html += '</div>';
            
            document.getElementById('stats-content').innerHTML = html;
        }

        // 页面加载时获取统计信息
        window.onload = function() {
            getStats();
        };
    </script>
</body>
</html>


#!/bin/bash

# 简单的多GPU测试脚本
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

echo "🧪 测试多GPU环境..."
python -c "
import torch
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')

# 测试简单的多GPU操作
if torch.cuda.device_count() >= 2:
    print('✅ 多GPU环境测试通过')
    
    # 创建简单的张量测试
    device = torch.device('cuda:0')
    x = torch.randn(4, 4).to(device)
    y = torch.randn(4, 4).to(device)
    z = torch.mm(x, y)
    print(f'✅ GPU计算测试通过: {z.shape}')
else:
    print('⚠️  GPU数量不足，无法测试多GPU')
"

echo "✅ 多GPU环境测试完成"

#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P 训练监控和性能优化工具
提供显存监控、训练进度跟踪和性能分析功能
"""

import os
import sys
import time
import json
import psutil
import argparse
import subprocess
from datetime import datetime
from typing import Dict, List, Optional
import matplotlib.pyplot as plt
import pandas as pd

class WanTrainingMonitor:
    def __init__(self):
        self.start_time = None
        self.gpu_stats = []
        self.training_stats = []
        
    def check_system_requirements(self):
        """检查系统要求"""
        print("🔍 检查系统要求...")
        
        # 检查GPU
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total,driver_version', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                gpu_info = result.stdout.strip().split('\n')
                print(f"✅ 检测到 {len(gpu_info)} 块GPU:")
                for i, info in enumerate(gpu_info):
                    name, memory, driver = info.split(', ')
                    print(f"   GPU {i}: {name} ({memory}MB, Driver: {driver})")
            else:
                print("❌ 无法检测GPU信息")
                return False
        except FileNotFoundError:
            print("❌ nvidia-smi 未找到，请安装NVIDIA驱动")
            return False
        
        # 检查内存
        memory = psutil.virtual_memory()
        memory_gb = memory.total / 1024**3
        print(f"💾 系统内存: {memory_gb:.1f}GB (可用: {memory.available/1024**3:.1f}GB)")
        
        if memory_gb < 32:
            print("⚠️  建议系统内存至少32GB用于大模型训练")
        
        # 检查磁盘空间
        disk = psutil.disk_usage('.')
        disk_free_gb = disk.free / 1024**3
        print(f"💿 磁盘空间: {disk_free_gb:.1f}GB 可用")
        
        if disk_free_gb < 100:
            print("⚠️  建议至少100GB磁盘空间用于模型和数据存储")
        
        return True
    
    def monitor_gpu_memory(self, interval: int = 5, duration: int = 3600):
        """监控GPU显存使用情况"""
        print(f"📊 开始GPU显存监控 (间隔: {interval}秒, 持续: {duration}秒)")
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            try:
                result = subprocess.run([
                    'nvidia-smi', 
                    '--query-gpu=timestamp,index,name,utilization.gpu,memory.used,memory.total,temperature.gpu,power.draw',
                    '--format=csv,noheader,nounits'
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        parts = line.split(', ')
                        if len(parts) >= 8:
                            timestamp, gpu_id, name, util, mem_used, mem_total, temp, power = parts
                            
                            stats = {
                                'timestamp': timestamp,
                                'gpu_id': int(gpu_id),
                                'name': name,
                                'utilization': float(util),
                                'memory_used': float(mem_used),
                                'memory_total': float(mem_total),
                                'memory_usage_percent': (float(mem_used) / float(mem_total)) * 100,
                                'temperature': float(temp),
                                'power_draw': float(power.replace(' W', '')) if 'W' in power else 0
                            }
                            
                            self.gpu_stats.append(stats)
                            
                            # 实时显示
                            print(f"GPU {gpu_id}: {util}% 利用率, {mem_used}/{mem_total}MB 显存 ({stats['memory_usage_percent']:.1f}%), {temp}°C")
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                print("\n⏹️  监控已停止")
                break
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                time.sleep(interval)
        
        return self.gpu_stats
    
    def analyze_training_log(self, log_file: str):
        """分析训练日志"""
        print(f"📈 分析训练日志: {log_file}")
        
        if not os.path.exists(log_file):
            print(f"❌ 日志文件不存在: {log_file}")
            return None
        
        training_data = []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 解析训练进度
                    if 'loss' in line.lower() and 'epoch' in line.lower():
                        # 这里可以根据实际日志格式调整解析逻辑
                        training_data.append({
                            'line_number': line_num,
                            'content': line,
                            'timestamp': datetime.now().isoformat()
                        })
            
            print(f"✅ 解析完成，找到 {len(training_data)} 条训练记录")
            return training_data
            
        except Exception as e:
            print(f"❌ 日志解析失败: {e}")
            return None
    
    def optimize_memory_settings(self):
        """生成显存优化建议"""
        print("🔧 生成显存优化建议...")
        
        # 获取GPU信息
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=memory.total', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                gpu_memories = [int(mem) for mem in result.stdout.strip().split('\n')]
                min_memory = min(gpu_memories)
                
                print(f"📊 最小GPU显存: {min_memory}MB")
                
                # 根据显存大小给出建议
                if min_memory >= 24000:  # 24GB+
                    recommendations = {
                        "batch_size": 2,
                        "gradient_accumulation_steps": 4,
                        "lora_rank": 32,
                        "resolution": "480x832",
                        "num_frames": 25,
                        "mixed_precision": "bf16",
                        "gradient_checkpointing": True,
                        "offload_optimizer": False
                    }
                elif min_memory >= 16000:  # 16GB+
                    recommendations = {
                        "batch_size": 1,
                        "gradient_accumulation_steps": 8,
                        "lora_rank": 16,
                        "resolution": "320x576",
                        "num_frames": 16,
                        "mixed_precision": "bf16",
                        "gradient_checkpointing": True,
                        "offload_optimizer": True
                    }
                else:  # <16GB
                    recommendations = {
                        "batch_size": 1,
                        "gradient_accumulation_steps": 16,
                        "lora_rank": 8,
                        "resolution": "256x448",
                        "num_frames": 12,
                        "mixed_precision": "bf16",
                        "gradient_checkpointing": True,
                        "offload_optimizer": True
                    }
                
                print("💡 推荐配置:")
                for key, value in recommendations.items():
                    print(f"   {key}: {value}")
                
                return recommendations
                
        except Exception as e:
            print(f"❌ 无法获取GPU信息: {e}")
            return None
    
    def generate_training_script(self, config: Dict, output_path: str = "optimized_training.sh"):
        """根据配置生成优化的训练脚本"""
        print(f"📝 生成优化训练脚本: {output_path}")
        
        script_content = f"""#!/bin/bash
# 自动生成的优化训练脚本
# 生成时间: {datetime.now().isoformat()}

echo "🎬 开始Wan2.1-I2V-14B-480P优化训练..."

# 环境变量设置
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

# 检查环境
if [[ "$CONDA_DEFAULT_ENV" != "wan_video_env" ]]; then
    echo "⚠️  请先激活wan_video_env环境"
    exit 1
fi

# 训练参数
RESOLUTION="{config.get('resolution', '480x832')}"
BATCH_SIZE={config.get('batch_size', 1)}
GRAD_ACCUM_STEPS={config.get('gradient_accumulation_steps', 4)}
LORA_RANK={config.get('lora_rank', 32)}
NUM_FRAMES={config.get('num_frames', 25)}

echo "⚙️  训练配置:"
echo "   分辨率: $RESOLUTION"
echo "   批次大小: $BATCH_SIZE"
echo "   梯度累积: $GRAD_ACCUM_STEPS"
echo "   LoRA rank: $LORA_RANK"
echo "   帧数: $NUM_FRAMES"

# 解析分辨率
IFS='x' read -ra RES <<< "$RESOLUTION"
HEIGHT=${{RES[0]}}
WIDTH=${{RES[1]}}

# 启动训练
accelerate launch examples/wanvideo/model_training/train.py \\
  --dataset_base_path data/example_video_dataset \\
  --dataset_metadata_path data/example_video_dataset/metadata.csv \\
  --height $HEIGHT \\
  --width $WIDTH \\
  --dataset_repeat 100 \\
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \\
  --learning_rate 1e-4 \\
  --num_epochs 3 \\
  --gradient_accumulation_steps $GRAD_ACCUM_STEPS \\
  --remove_prefix_in_ckpt "pipe.dit." \\
  --output_path "./models/train/Wan2.1-I2V-14B-480P_optimized" \\
  --lora_base_model "dit" \\
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \\
  --lora_rank $LORA_RANK \\
  --extra_inputs "input_image" \\
  --use_gradient_checkpointing_offload

echo "✅ 优化训练完成！"
"""
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # 添加执行权限
            os.chmod(output_path, 0o755)
            
            print(f"✅ 训练脚本已生成: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 脚本生成失败: {e}")
            return False
    
    def save_monitoring_report(self, output_file: str = "monitoring_report.json"):
        """保存监控报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'gpu_stats': self.gpu_stats,
            'training_stats': self.training_stats,
            'system_info': {
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total,
                'disk_free': psutil.disk_usage('.').free
            }
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"📊 监控报告已保存: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 报告保存失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description="Wan训练监控工具")
    parser.add_argument("--action", choices=["check", "monitor", "analyze", "optimize", "generate"], 
                       required=True, help="操作类型")
    parser.add_argument("--log_file", type=str, help="训练日志文件路径")
    parser.add_argument("--duration", type=int, default=3600, help="监控持续时间(秒)")
    parser.add_argument("--interval", type=int, default=5, help="监控间隔(秒)")
    parser.add_argument("--output", type=str, help="输出文件路径")
    
    args = parser.parse_args()
    
    monitor = WanTrainingMonitor()
    
    if args.action == "check":
        monitor.check_system_requirements()
        
    elif args.action == "monitor":
        stats = monitor.monitor_gpu_memory(args.interval, args.duration)
        if args.output:
            monitor.save_monitoring_report(args.output)
            
    elif args.action == "analyze":
        if not args.log_file:
            print("❌ 请指定日志文件路径")
            return
        monitor.analyze_training_log(args.log_file)
        
    elif args.action == "optimize":
        config = monitor.optimize_memory_settings()
        if config and args.output:
            monitor.generate_training_script(config, args.output)
            
    elif args.action == "generate":
        config = monitor.optimize_memory_settings()
        if config:
            output_path = args.output or "optimized_training.sh"
            monitor.generate_training_script(config, output_path)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试多卡推理环境设置
验证模型加载和GPU配置
"""

import torch
import os
import json
import sys

def test_environment():
    """测试基础环境"""
    print("🔍 测试基础环境...")
    
    # 检查PyTorch
    print(f"   PyTorch版本: {torch.__version__}")
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"   ✅ CUDA可用: {torch.version.cuda}")
        print(f"   GPU数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
    else:
        print("   ❌ CUDA不可用")
        return False
    
    return True

def test_lora_checkpoint():
    """测试LoRA检查点文件"""
    print("\n🔍 测试LoRA检查点...")
    
    checkpoint_path = "./models/train/Wan2.1-I2V-14B-480P_lora_final/epoch-4.safetensors"
    config_path = "./models/train/Wan2.1-I2V-14B-480P_lora_final/training_args.json"
    
    # 检查检查点文件
    if os.path.exists(checkpoint_path):
        file_size = os.path.getsize(checkpoint_path) / 1024**2
        print(f"   ✅ 检查点文件存在: {checkpoint_path}")
        print(f"   文件大小: {file_size:.1f}MB")
        
        # 尝试加载检查点
        try:
            # 尝试safetensors格式
            if checkpoint_path.endswith('.safetensors'):
                try:
                    from safetensors import safe_open
                    checkpoint = {}
                    with safe_open(checkpoint_path, framework="pt", device="cpu") as f:
                        for key in f.keys():
                            checkpoint[key] = f.get_tensor(key)
                    print(f"   ✅ SafeTensors检查点加载成功: {len(checkpoint)} 个参数")
                except ImportError:
                    print("   ⚠️  safetensors库未安装，尝试torch.load")
                    checkpoint = torch.load(checkpoint_path, map_location="cpu", weights_only=False)
                    print(f"   ✅ 检查点加载成功: {len(checkpoint)} 个参数")
            else:
                checkpoint = torch.load(checkpoint_path, map_location="cpu", weights_only=False)
                print(f"   ✅ 检查点加载成功: {len(checkpoint)} 个参数")

            # 显示部分参数名称
            param_names = list(checkpoint.keys())[:5]
            print(f"   参数示例: {param_names}")

        except Exception as e:
            print(f"   ❌ 检查点加载失败: {e}")
            return False
    else:
        print(f"   ❌ 检查点文件不存在: {checkpoint_path}")
        return False
    
    # 检查配置文件
    if os.path.exists(config_path):
        print(f"   ✅ 配置文件存在: {config_path}")
        
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            print(f"   ✅ 配置加载成功")
            print(f"   训练配置: height={config.get('height')}, width={config.get('width')}")
            print(f"   LoRA配置: rank={config.get('lora_rank')}, modules={config.get('lora_target_modules')}")
            
        except Exception as e:
            print(f"   ❌ 配置加载失败: {e}")
            return False
    else:
        print(f"   ⚠️  配置文件不存在: {config_path}")
    
    return True

def test_diffsynth_import():
    """测试DiffSynth模块导入"""
    print("\n🔍 测试DiffSynth模块...")
    
    # 添加路径
    sys.path.append('/root/sj-tmp/DiffSynth-Studio')
    
    try:
        # 测试基础导入
        from diffsynth import WanVideoPipeline
        print("   ✅ WanVideoPipeline导入成功")
        
        # 测试其他必要模块
        from diffsynth.models.model_manager import ModelManager
        print("   ✅ ModelManager导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ DiffSynth导入失败: {e}")
        print("   请确保DiffSynth-Studio路径正确")
        return False
    except Exception as e:
        print(f"   ❌ 其他导入错误: {e}")
        return False

def test_multi_gpu_setup():
    """测试多GPU设置"""
    print("\n🔍 测试多GPU设置...")
    
    if torch.cuda.device_count() < 2:
        print("   ⚠️  GPU数量不足，无法测试多GPU")
        return True
    
    try:
        # 创建测试张量
        device_ids = [0, 1]
        test_tensor = torch.randn(4, 1024).cuda(0)
        
        # 创建简单模型
        class TestModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = torch.nn.Linear(1024, 512)
            
            def forward(self, x):
                return self.linear(x)
        
        model = TestModel().cuda(0)
        
        # 设置DataParallel
        from torch.nn.parallel import DataParallel
        parallel_model = DataParallel(model, device_ids=device_ids)
        
        # 测试前向传播
        with torch.no_grad():
            output = parallel_model(test_tensor)
        
        print(f"   ✅ 多GPU测试成功")
        print(f"   输入形状: {test_tensor.shape}")
        print(f"   输出形状: {output.shape}")
        print(f"   使用设备: {device_ids}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 多GPU测试失败: {e}")
        return False

def test_memory_usage():
    """测试GPU内存使用"""
    print("\n🔍 测试GPU内存...")
    
    for i in range(torch.cuda.device_count()):
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        reserved = torch.cuda.memory_reserved(i) / 1024**3
        total = torch.cuda.get_device_properties(i).total_memory / 1024**3
        
        print(f"   GPU {i}: 已分配={allocated:.2f}GB, 已保留={reserved:.2f}GB, 总计={total:.1f}GB")
    
    return True

def main():
    """主测试函数"""
    print("🧪 Wan2.1-I2V-14B-480P 多卡推理环境测试")
    print("=" * 50)
    
    tests = [
        ("基础环境", test_environment),
        ("LoRA检查点", test_lora_checkpoint), 
        ("DiffSynth模块", test_diffsynth_import),
        ("多GPU设置", test_multi_gpu_setup),
        ("GPU内存", test_memory_usage)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！多卡推理环境准备就绪！")
        print("\n📝 下一步:")
        print("   1. 运行: python wan_multi_gpu_inference_simple.py")
        print("   2. 或运行: ./run_multi_gpu_inference.sh")
    else:
        print("⚠️  部分测试失败，请检查环境配置")
    
    return passed == len(results)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取docx文档内容的工具脚本
"""

import sys
import os
from pathlib import Path

def read_docx_content(docx_path):
    """
    读取docx文件内容
    """
    try:
        # 尝试使用python-docx库
        from docx import Document
        
        doc = Document(docx_path)
        content = []
        
        print(f"正在读取文档: {docx_path}")
        print("=" * 50)
        
        # 读取段落内容
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text)
                print(paragraph.text)
        
        # 读取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    table_row = " | ".join(row_text)
                    content.append(table_row)
                    print(table_row)
        
        print("=" * 50)
        print(f"文档读取完成，共 {len(content)} 段内容")
        
        # 将内容保存到文件
        output_path = "/workspace/docs/document_content.txt"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
        
        print(f"内容已保存到: {output_path}")
        return content
        
    except ImportError:
        print("python-docx库未安装，正在安装...")
        os.system("uv add python-docx")
        # 重新导入并执行
        from docx import Document
        return read_docx_content(docx_path)
        
    except Exception as e:
        print(f"读取文档时出错: {e}")
        return None

if __name__ == "__main__":
    docx_file = "/workspace/user_input_files/f77b7b08de997bbccf8fb101d949fce5_fb6b4e24389f6d4935c2bd519cb0d0a6_8.docx"
    
    if os.path.exists(docx_file):
        content = read_docx_content(docx_file)
    else:
        print(f"文档不存在: {docx_file}")
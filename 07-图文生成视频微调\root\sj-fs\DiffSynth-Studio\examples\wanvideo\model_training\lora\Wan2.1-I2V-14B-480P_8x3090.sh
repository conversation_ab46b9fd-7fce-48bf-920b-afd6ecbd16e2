#!/bin/bash
# Wan2.1-I2V-14B-480P 8×RTX 3090分布式LoRA训练脚本
# 针对图像到视频生成模型的多GPU分布式微调训练
# 使用wan_video_env虚拟环境

echo "🎬 开始Wan2.1-I2V-14B-480P 8×RTX 3090分布式LoRA训练..."
echo "模型: Wan-AI/Wan2.1-I2V-14B-480P (图像到视频生成)"
echo "训练类型: 8×RTX 3090分布式LoRA微调"
echo "分辨率: 480x832"
echo "=" * 60

# 检查虚拟环境
if [[ "$CONDA_DEFAULT_ENV" != "wan_video_env" ]]; then
    echo "⚠️  请先激活wan_video_env环境: conda activate wan_video_env"
    exit 1
fi

# 检查GPU数量
GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
if [ "$GPU_COUNT" -lt 8 ]; then
    echo "⚠️  检测到GPU数量: $GPU_COUNT，建议使用8块GPU进行训练"
    echo "继续使用可用的GPU进行训练..."
fi

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export NCCL_P2P_DISABLE=1  # 如果遇到P2P通信问题可启用
export NCCL_IB_DISABLE=1   # 禁用InfiniBand

# 检查数据集
if [ ! -d "data/example_video_dataset" ]; then
    echo "📥 下载示例数据集..."
    modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
fi

# 创建accelerate配置文件
echo "🔧 创建accelerate配置..."
mkdir -p ~/.cache/huggingface/accelerate

cat > ~/.cache/huggingface/accelerate/default_config.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 8
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
EOF

echo "✅ Accelerate配置创建完成"

# 显示GPU信息
echo "🎯 GPU信息:"
nvidia-smi --query-gpu=index,name,memory.total --format=csv,noheader,nounits | while read line; do
    echo "   $line"
done

echo "🚀 启动8×RTX 3090分布式LoRA训练..."

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path data/example_video_dataset \
    --dataset_metadata_path data/example_video_dataset/metadata.csv \
    --height 480 \
    --width 832 \
    --dataset_repeat 200 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
    --learning_rate 1e-4 \
    --num_epochs 3 \
    --gradient_accumulation_steps 2 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/Wan2.1-I2V-14B-480P_8x3090_lora" \
    --lora_base_model "dit" \
    --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
    --lora_rank 64 \
    --extra_inputs "input_image" \
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090分布式LoRA训练完成！"
echo "模型保存路径: ./models/train/Wan2.1-I2V-14B-480P_8x3090_lora/"
echo "🧪 运行推理测试: python examples/wanvideo/model_training/validate_lora/Wan2.1-I2V-14B-480P.py"
echo "💡 提示: 请修改推理脚本中的模型路径为: ./models/train/Wan2.1-I2V-14B-480P_8x3090_lora/epoch-2.safetensors"

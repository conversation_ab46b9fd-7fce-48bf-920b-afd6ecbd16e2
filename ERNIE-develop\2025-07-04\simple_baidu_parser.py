#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import logging
from typing import List, Dict

class SimpleBaiduParser:
    """简化的百度搜索结果解析器 - 不依赖重定向解析"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_search_results(self, html_content: str, max_results: int = 5) -> List[Dict[str, str]]:
        """
        解析百度搜索结果页面，提取标题和描述信息
        
        Args:
            html_content (str): 百度搜索结果页面的HTML内容
            max_results (int): 最大结果数量
            
        Returns:
            List[Dict[str, str]]: 搜索结果列表，每个结果包含title和url(虚拟)
        """
        results = []
        
        try:
            # 方法1: 提取搜索结果的标题和描述
            # 匹配百度搜索结果的标题模式 - 基于实际HTML结构
            title_patterns = [
                # 新版百度搜索结果的标题模式 - 匹配<!--s-text-->和<!--/s-text-->之间的内容
                r'<!--s-text-->(.*?)<!--/s-text-->',
                # 备用模式：匹配h3标签内的链接文本
                r'<h3[^>]*class="[^"]*t[^"]*"[^>]*>.*?<a[^>]*>([^<]+)</a>',
                # 更宽松的h3模式
                r'<h3[^>]*>.*?<a[^>]*>([^<]+)</a>',
            ]
            
            # 提取描述信息的模式
            desc_patterns = [
                # 匹配class包含abstract的div
                r'<div[^>]*class="[^"]*abstract[^"]*"[^>]*>([^<]+)</div>',
                # 匹配搜索结果摘要
                r'<span[^>]*class="[^"]*content-right_8Zs40[^"]*"[^>]*>([^<]+)</span>',
                # 更通用的描述模式
                r'<div[^>]*>([^<]{50,200})</div>',
            ]
            
            # 先提取所有标题
            all_titles = []
            for pattern in title_patterns:
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    # 清理HTML标签和特殊标记
                    title = self._clean_title(match)
                    if title and len(title) > 5 and self._is_meaningful_title(title):
                        all_titles.append(title)
            
            self.logger.info(f"提取到 {len(all_titles)} 个标题")
            
            # 为每个标题创建结果
            for i, title in enumerate(all_titles[:max_results]):
                # 生成一个虚拟URL，基于标题内容
                virtual_url = self._generate_virtual_url(title)
                
                results.append({
                    'title': title,
                    'url': virtual_url
                })
            
            # 如果标题不够，尝试从HTML中提取更多信息
            if len(results) < max_results:
                self.logger.info("尝试提取更多搜索结果信息")
                
                # 查找包含关键词的文本段落
                keyword_patterns = [
                    r'([^<>]{20,100}(?:MONKEY|OCR|算家|云)[^<>]{20,100})',
                    r'([^<>]{30,150})',
                ]
                
                for pattern in keyword_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    for match in matches:
                        text = match.strip()
                        if (len(text) > 20 and 
                            not any(skip in text for skip in ['百度', '搜索', '登录', '注册']) and
                            len(results) < max_results):
                            
                            # 截取合适长度作为标题
                            title = text[:50] + "..." if len(text) > 50 else text
                            virtual_url = self._generate_virtual_url(title)
                            
                            results.append({
                                'title': title,
                                'url': virtual_url
                            })
                    
                    if len(results) >= max_results:
                        break
            
            self.logger.info(f"解析完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"解析搜索结果失败: {e}")
            return []
    
    def _is_meaningful_title(self, title: str) -> bool:
        """检查标题是否有意义"""
        if len(title) < 5:
            return False

        # 过滤无意义的标题
        meaningless_patterns = [
            '百度', '搜索', '登录', '注册', 'hao123', '导航',
            '首页', '更多', '下一页', '上一页', '相关搜索',
            '贴吧', '知道', '图片', '视频', '地图', '新闻',
            'meta http-equiv', 'input id=', 'div class=', 'span class=',
            'card-show-log', 'iteminfo', 'quot;', '&amp;'
        ]

        for pattern in meaningless_patterns:
            if pattern in title.lower():
                return False

        # 检查是否包含太多特殊字符
        special_char_count = len([c for c in title if c in '&<>"\'{}\[\]();'])
        if special_char_count > len(title) * 0.3:  # 如果特殊字符超过30%
            return False

        # 过滤长串随机字符
        if len(title) > 50 and not any(c in title for c in '，。！？；：、'):
            # 如果标题很长但没有中文标点，可能是HTML片段
            chinese_chars = len([c for c in title if '\u4e00' <= c <= '\u9fff'])
            if chinese_chars < len(title) * 0.1:  # 中文字符少于10%
                return False

        return True

    def _clean_title(self, title: str) -> str:
        """清理标题文本，移除HTML标签和特殊标记"""
        if not title:
            return ""

        # 移除HTML标签
        title = re.sub(r'<[^>]+>', '', title)

        # 移除高亮标记 <em> 和 </em>
        title = re.sub(r'</?em>', '', title)

        # 移除其他特殊标记
        title = re.sub(r'&[^;]+;', ' ', title)  # HTML实体

        # 清理多余空格
        title = re.sub(r'\s+', ' ', title).strip()

        return title

    def _generate_virtual_url(self, title: str) -> str:
        """基于标题生成虚拟URL"""
        # 简单的URL生成逻辑
        import hashlib
        hash_obj = hashlib.md5(title.encode('utf-8'))
        hash_hex = hash_obj.hexdigest()[:8]
        
        # 根据标题内容推测可能的域名
        if 'github' in title.lower():
            return f"https://github.com/example/{hash_hex}"
        elif 'ocr' in title.lower():
            return f"https://example-ocr.com/{hash_hex}"
        elif '算家' in title or 'suanjia' in title.lower():
            return f"https://suanjia.com/{hash_hex}"
        else:
            return f"https://example.com/{hash_hex}"

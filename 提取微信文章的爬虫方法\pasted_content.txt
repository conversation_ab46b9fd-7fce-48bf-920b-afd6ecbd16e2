import requests
import json

# DeepSeek API 端点
url = "https://api.edgefn.net/v1/chat/completions"
headers = {
    "Authorization": "Bearer sk-lBsjhuzN4GriMRqfD45744C34dDa4e39922bCfC9A911Cf1d", 
    "Content-Type": "application/json"
}
data = {
    "model": "DeepSeek-R1-0528",
    "messages": [
        {
            "role": "user", 
            "content": "你叫什么名字?"
        }
    ],
    "stream": True,  # 启用流式输出模式
    "temperature": 0.7  # 添加温度参数，使输出更有创造性
}

print("\n正在使用流式输出获取模型思考过程和回答...\n")
print("-" * 50)

# 发送 API 请求，启用流式响应
response = requests.post(url, headers=headers, json=data, stream=True)

# 检查响应状态
if response.status_code != 200:
    print(f"错误: API 返回状态码 {response.status_code}")
    print(response.text)
    exit(1)

# 状态变量，用于跟踪当前正在处理的内容类型
current_content_type = None  # 'reasoning' 或 'content'
has_printed_reasoning_header = False
has_printed_content_header = False

# 处理流式响应
for line in response.iter_lines():
    if line:
        try:
            # DeepSeek API 的流式响应通常以 "data: " 前缀开始，需要移除
            if line.startswith(b'data: '):
                line = line[6:]
            
            # 跳过空行或终止信号
            if line.strip() == b'' or line.strip() == b'[DONE]':
                continue
                
            # 解析 JSON 数据块
            chunk = json.loads(line)
            
            # 检查是否有 delta 字段
            if 'choices' in chunk and len(chunk['choices']) > 0 and 'delta' in chunk['choices'][0]:
                delta = chunk['choices'][0]['delta']
                
                # 检查是否包含思考过程
                if 'reasoning_content' in delta and delta['reasoning_content']:
                    # 第一次出现思考过程时打印标题
                    if not has_printed_reasoning_header:
                        print("\n===== 思考过程 =====\n")
                        has_printed_reasoning_header = True
                        current_content_type = 'reasoning'
                    
                    # 打印思考过程内容
                    print(delta['reasoning_content'], end='', flush=True)
                
                # 检查是否包含最终回答
                elif 'content' in delta and delta['content']:
                    # 如果从思考过程切换到最终回答，打印分隔符
                    if current_content_type == 'reasoning' or not has_printed_content_header:
                        if has_printed_reasoning_header:
                            print("\n\n===== 思考结束 =====")
                        print("\n===== 最终回答 =====\n")
                        has_printed_content_header = True
                        current_content_type = 'content'
                    
                    # 打印最终回答内容
                    print(delta['content'], end='', flush=True)
                
                # 检查是否包含完成原因，标记流结束
                if 'finish_reason' in chunk['choices'][0] and chunk['choices'][0]['finish_reason'] == 'stop':
                    print("\n\n=====================\n")
            
        except json.JSONDecodeError as e:
            # JSON 解析错误处理
            print(f"\n解析 JSON 时出错: {e}")
            print(f"接收到的数据: {line}")
        except Exception as e:
            # 其他异常处理
            print(f"\n处理响应时出错: {e}")
            print(f"异常详情: {str(e)}")

# 确保最后有一个分隔符
if has_printed_content_header or has_printed_reasoning_header:
    print("\n" + "-" * 50)
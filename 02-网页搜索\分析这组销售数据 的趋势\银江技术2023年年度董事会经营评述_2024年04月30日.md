﻿# 银江技术2023年年度董事会经营评述

**发布日期**: 2024年04月30日

**原文链接**: http://yuanchuang.10jqka.com.cn/20240430/c657433983.shtml

## 📄 原文内容

脪酶陆颅录录脢玫2023脛锚脛锚露脠露颅脢脗禄谩戮颅脫陋脝脌脢枚
						                    脥卢禄篓脣鲁陆冒脠脷脩脨戮驴脰脨脨脛
隆隆隆隆脪禄隆垄卤篓赂忙脝脷脛脷鹿芦脣戮脣霉麓娄脨脨脪碌脟茅驴枚
鹿芦脣戮脨猫脳帽脢脴隆露脡卯脹脷脰陇脠炉陆禄脪脳脣霉脡脧脢脨鹿芦脣戮脳脭脗脡录脿鹿脺脰赂脪媒碌脷3潞脜隆陋隆陋脨脨脪碌脨脜脧垄脜没脗露隆路脰脨碌脛隆掳脠铆录镁脫毛脨脜脧垄录录脢玫路镁脦帽脪碌隆卤碌脛脜没脗露脪陋脟贸隆拢
1隆垄脠铆录镁潞脥脨脜脧垄录录脢玫路镁脦帽脪碌路垄脮鹿赂脜驴枚
脠铆录镁潞脥脨脜脧垄录录脢玫路镁脦帽脪碌脢脟鹿煤脙帽戮颅录脙潞脥脡莽禄谩路垄脮鹿碌脛禄霉麓隆脨脭隆垄脧脠碌录脨脭潞脥脮陆脗脭脨脭虏煤脪碌,露脭戮颅录脙脡莽禄谩路垄脮鹿戮脽脫脨脰脴脪陋碌脛脰搂鲁脜潞脥脪媒脕矛脳梅脫脙隆拢脦脪鹿煤露脭脠铆录镁潞脥脨脜脧垄录录脢玫路镁脦帽脪碌赂酶脫猫赂脽露脠碌脛脰脴脢脫,脧脿鹿脴虏驴脙脜脧脠潞贸掳盲虏录脪禄脧碌脕脨鹿脛脌酶脮镁虏脽,卤拢脮脧脠铆录镁潞脥脨脜脧垄录录脢玫路镁脦帽脝贸脪碌禄帽碌脙脕录潞脙路垄脮鹿禄路戮鲁隆拢2021脛锚 11脭脗,鹿陇脨脜虏驴脫隆路垄脕脣隆露隆掳脢庐脣脛脦氓隆卤脠铆录镁潞脥脨脜脧垄录录脢玫路镁脦帽脪碌路垄脮鹿鹿忙禄庐隆路,脤谩鲁枚脢碌脧脰隆掳虏煤脪碌禄霉麓隆脨脗脤谩脡媒隆垄虏煤脪碌脕麓麓茂碌陆脨脗脣庐脝陆隆垄脡煤脤卢脜脿脫媒脨脗路垄脮鹿隆垄虏煤脪碌路垄脮鹿脨脗鲁脡脨搂隆卤碌脛隆掳脣脛脨脗隆卤路垄脮鹿脛驴卤锚,虏驴脢冒脕脣脥脝露炉脠铆录镁虏煤脪碌脕麓脡媒录露隆垄脤谩脡媒虏煤脪碌禄霉麓隆卤拢脮脧脣庐脝陆隆垄脟驴禄炉虏煤脪碌麓麓脨脗路垄脮鹿脛脺脕娄隆垄录陇路垄脢媒脳脰禄炉路垄脮鹿脨脗脨猫脟贸潞脥脥锚脡脝脨颅脥卢鹿虏脧铆虏煤脪碌脡煤脤卢脦氓脧卯脰梅脪陋脠脦脦帽,脠芦脙忙脰搂鲁脜脰脝脭矛脟驴鹿煤隆垄脥酶脗莽脟驴鹿煤隆垄脢媒脳脰脰脨鹿煤陆篓脡猫隆拢
赂霉戮脻鹿陇脨脜虏驴路垄虏录碌脛隆露2023脛锚脠铆录镁脪碌戮颅录脙脭脣脨脨脟茅驴枚隆路脧脭脢戮,2023脛锚脦脪鹿煤脠铆录镁潞脥脨脜脧垄录录脢玫路镁脦帽脪碌脢碌脧脰脢脮脠毛123,258脪脷脭陋,脥卢卤脠脭枚鲁陇13.4%拢禄脢碌脧脰脌没脠贸脳脺露卯14,591脪脷脭陋,脥卢卤脠脭枚鲁陇13.6%,脭枚脣脵陆脧脡脧脛锚脥卢脝脷脤谩赂脽7.9赂枚掳脵路脰碌茫隆拢2023脛锚脦脪鹿煤脠铆录镁潞脥脨脜脧垄录录脢玫路镁脦帽脪碌脭脣脨脨脤卢脢脝脦脠虏陆脧貌潞脙,脫炉脌没脛脺脕娄卤拢鲁脰脦脠露篓隆拢
隆掳脢庐脣脛脦氓隆卤路垄脮鹿赂脵脪陋陆芦脢媒脳脰禄炉路垄脮鹿碌楼露脌鲁脡脝陋,脤谩鲁枚路脰录露路脰脌脿脥脝陆酶脨脗脨脥脰脟禄脹鲁脟脢脨陆篓脡猫,脥脝陆酶鲁脟脢脨脢媒戮脻麓贸脛脭陆篓脡猫,鲁脟脢脨麓贸脛脭碌脛鹿鹿陆篓脪虏脢脟鲁脟脢脨脢媒脳脰禄炉陆篓脡猫脭脣脫陋碌脛脰脴脪陋脳楼脢脰隆拢
脦麓脌麓,脨脗脨脥脰脟禄脹鲁脟脢脨陆篓脡猫脟梅脢脝陆芦脰冒陆楼脪脭隆掳鲁脟脢脨录露脭脣脫陋隆卤脦陋碌录脧貌隆拢隆掳鲁脟脢脨麓贸脛脭隆卤脥篓鹿媒脢媒戮脻露麓虏矛隆垄脛拢脨脥鹿鹿陆篓碌脠脫娄脫脙陆篓脡猫,驴脡脢碌脧脰鲁脟脢脨脭脣脨脨脤卢脢脝赂脨脰陋脫毛路脗脮忙隆垄鲁脟脢脨脕陋露炉脰赂禄脫脫毛脨颅脥卢麓娄脰脙,脪脭录掳鲁脟脢脨脭陇虏芒脭陇戮炉脫毛脰脟脛脺戮枚虏脽脰搂鲁脜,脪脩鲁脡脦陋脰脟禄脹鲁脟脢脨戮潞脮霉碌脛脰脝赂脽碌茫隆拢脣忙脳脜脰脟禄脹鲁脟脢脨碌脛脡卯脠毛驴陋脮鹿,陆芦脫脨脭陆脌麓脭陆露脿碌脛麓鹿脰卤脕矛脫貌驴陋路垄脦陋隆掳鲁脟脢脨麓贸脛脭隆卤隆拢
露镁隆垄卤篓赂忙脝脷脛脷鹿芦脣戮麓脫脢脗碌脛脰梅脪陋脪碌脦帽
脝盲脰脨,麓鹿脰卤脨脨脪碌脫娄脫脙脛拢脢陆脢脟脰赂,脥篓鹿媒路谩赂禄碌脛脧赂路脰虏煤脝路脧脽,脗煤脳茫陆禄脥篓隆垄陆隆驴碌隆垄陆脤脫媒碌脠脮镁赂庐脨脨脪碌脰梅鹿脺虏驴脙脜碌脛脡卯露脠禄炉潞脥露脿脩霉禄炉脪碌脦帽脨猫脟贸拢禄脪禄脤氓脕陋露炉脛拢脢陆脢脟脰赂,脥篓鹿媒虏煤脝路碌脛赂脽露脠脛拢驴茅禄炉潞脥脟驴赂麓脫脙脨脭,脗煤脳茫脢脨录露隆垄脟酶脧脴隆垄陆脰脮貌碌脠虏禄脥卢虏茫录露脮镁赂庐禄霉虏茫脰脦脌铆碌脛脳脹潞脧脨脭脪碌脦帽脨猫脟贸隆拢
卤篓赂忙脝脷脛脷,鹿芦脣戮脠芦脤氓脭卤鹿陇脭脷鹿脺脌铆虏茫麓酶脕矛脧脗,驴脣路镁脰卯露脿脥芒虏驴虏禄脌没脪貌脣脴,脪脌脥脨脫脜脢脝虏煤脝路录录脢玫,驴陋脥脴脨脗陆庐隆垄赂拢陆篓隆垄陆颅脣脮碌脠碌脴鲁脟脢脨麓贸脛脭脢脨鲁隆拢禄潞禄脢碌脠脣鹿陇脰脟脛脺录录脢玫脩脨路垄,虏录戮脰脠脣鹿陇脰脟脛脺脧赂路脰脕矛脫貌录录脢玫,路垄虏录脥篓脙梅脧碌脕脨麓贸脛拢脨脥脝陆脤篓,脫毛禄陋脦陋隆垄掳虏潞茫脨脜脧垄碌脠脠芦脙忙脡卯脠毛潞脧脳梅,驴篓脦禄鹿脴录眉录录脢玫脕矛脫貌拢禄脥锚鲁脡路脟鹿芦驴陋路垄脨脨,脛录录炉脳脢陆冒10脪脷脭陋录脫脗毛脠脣鹿陇脰脟脛脺隆垄脢媒脳脰脗脧脡煤录录脢玫录掳脨脗脪禄麓煤陆禄脥篓脢媒脳脰脝陆脤篓虏煤脝路脩脨路垄,脪媒陆酶脮陆脗脭脥露脳脢脠脣,脦陋驴陋脝么脨脗脪禄脗脰碌脛脪碌脦帽鲁脰脨酶脭枚鲁陇碌矛露篓禄霉麓隆隆拢卤篓赂忙脝脷脛脷,鹿芦脣戮脭脷脳陋脨脥脰脨虏禄露脧碌梅脮没脢脮脠毛陆谩鹿鹿,脢碌脧脰脫陋脪碌脢脮脠毛1,169,077,879.62脭陋,脥卢卤脠脧脗陆碌27.47%,鹿茅脢么脫脷脡脧脢脨鹿芦脣戮鹿脡露芦碌脛戮禄脌没脠贸-230,290,186.42脭陋,脥卢卤脠脧脗陆碌463.86%,鹿茅脢么脫脷脡脧脢脨鹿芦脣戮鹿脡露芦碌脛驴脹鲁媒路脟戮颅鲁拢脣冒脪忙戮禄脌没脠贸-236,545,789.77脭陋,脥卢卤脠脧脗陆碌463.86%隆拢
卤篓赂忙脝脷脛脷,鹿芦脣戮戮脽脤氓戮颅脫陋脟茅驴枚禄脴鹿脣脠莽脧脗拢潞
2023脛锚,脢脥路脜脢媒戮脻脪陋脣脴录脹脰碌,脠脮脪忙鲁脡脦陋鹿煤录脪脟脌脳楼脢媒脳脰戮颅录脙路垄脮鹿脧脠禄煤隆垄掳脩脦脮脨脗脪禄脗脰驴脝录录赂茂脙眉潞脥虏煤脪碌卤盲赂茂禄煤脫枚碌脛脮陆脗脭脩隆脭帽隆拢鹿芦脣戮脕垄脳茫脣茫脕娄隆垄脣茫路篓隆垄脢媒戮脻脠媒脦禄脪禄脤氓,脡卯赂没脢媒戮脻脪陋脣脴,路垄虏录脢媒脳脰脰脦脌铆脛拢脨脥,脡脧脧脽脪酶陆颅脛拢脨脥鲁卢脢脨,鹿虏脥脝鲁枚鲁卢鹿媒100赂枚脠芦脨脗碌脛脣茫路篓脛拢脨脥,潞颅赂脟陆禄脥篓隆垄脪陆脕脝陆隆驴碌脫毛脡莽禄谩脰脦脌铆碌脠露脿赂枚脕矛脫貌,驴脡脗煤脳茫露脿鲁隆戮掳碌脛脨脨脪碌脨猫脟贸拢禄路垄虏录陆隆驴碌卤娄 HealthGPT隆垄脦脛脗脙麓贸脛脭隆垄脢媒脳脰脧脴脫貌隆垄脪酶陆颅脥篓脙梅脮镁脦帽隆垄脰脟脛脺脥酶脕陋隆垄脪酶陆颅脥篓脙梅陆隆驴碌碌脠脧碌脕脨脢媒脳脰禄炉脠颅脥路虏煤脝路拢禄路垄虏录脪酶陆颅脰脟脣茫脰脨脨脛,卤锚脰戮脳脜脪酶陆颅脭脷脣茫脕娄隆垄脣茫路篓隆垄脢媒戮脻路陆脙忙脢碌脧脰脠芦赂虏赂脟,麓脫露酶赂眉潞脙碌脛脢碌脧脰鲁脟脢脨脢媒戮脻脰脦脌铆卤戮碌脴禄炉脭脣脫陋录掳路镁脦帽隆拢
1隆垄Enloop-Hub脢媒戮脻脰脨脢脿
脢媒戮脻脰脨脢脿脙忙脧貌脢媒戮脻碌脛禄茫戮脹隆垄脰脦脌铆隆垄鹿虏脧铆脫毛驴陋路脜碌脠潞脣脨脛脨猫脟贸,脪脭隆掳脪酶陆颅Enloop-Hub脝陆脤篓隆卤脦陋潞脣脨脛,脥篓鹿媒驴莽脟酶脫貌隆垄驴莽虏驴脙脜隆垄驴莽脨脨脪碌隆垄驴莽虏茫录露碌脛鲁脟脢脨脨脜脧垄脳脢脭麓脫脨脨貌禄茫戮脹,脢碌脧脰隆掳露脿录露脕陋露炉隆垄露脿驴莽脨颅脥卢隆卤,脥脝露炉鲁脟脢脨脨脜脧垄脳脢脭麓脫脨脨貌禄茫戮脹隆垄潞谩脧貌鹿谩脥篓隆垄脡卯露脠鹿虏脧铆隆垄录脹脰碌脢脥路脜隆拢脪酶陆颅脢媒戮脻脰脨脢脿脥篓鹿媒脕脣脰脨鹿煤脨脜脥篓脭潞隆垄禄陋脦陋枚茂脜么碌脠脠脧脰陇,脠脵禄帽脕脣脮茫陆颅脢隆脢脳掳忙麓脦脠铆录镁虏煤脝路隆拢
2隆垄Enloop-Ana脰脟脛脺脰脨脢脿
卤篓赂忙脝脷脛脷,鹿芦脣戮戮脹陆鹿脨脨脪碌脫脜脢脝,脠脷潞脧脕脣鹿芦脣戮露脿脛锚脭脷脠脣鹿陇脰脟脛脺脕矛脫貌碌脛脳脹潞脧脩脨路垄脢碌脕娄录掳鹿脴录眉录录脢玫鲁脰脨酶麓麓脨脗脛脺脕娄,脌没脫脙脳脭脫脨脢媒戮脻陆酶脨脨脣茫路篓脩碌脕路,虏垄脮毛露脭脧赂路脰脕矛脫貌鲁隆戮掳脗盲碌脴脨猫脟贸陆酶脨脨脧碌脕脨麓贸脛拢脨脥碌脛虏煤脝路路垄虏录拢潞隆掳脪酶陆颅脥篓脙梅陆隆驴碌隆卤隆掳脪酶陆颅脥篓脙梅脦脢脦脢隆卤隆掳脪酶陆颅脥篓脙梅脮镁脦帽隆卤隆拢
1隆垄脰脟禄脹陆禄脥篓脕矛脫貌,AI赂鲁脛脺陆禄脥篓脳脹潞脧脰脦脌铆,脥锚脡脝陆禄脥篓脢媒脳脰碌脳脳霉虏煤脝路驴陋路垄
2隆垄脰脟禄脹陆隆驴碌脕矛脫貌,鲁脰脨酶脫毛禄陋脦陋潞脧脳梅驴陋脥脴脢脨鲁隆
脭脷麓脣脝脷录盲,鹿芦脣戮脗陆脨酶脰脨卤锚脕脣掳眉脌篓脧脙脙脜脢脨禄路露芦潞拢脫貌脪陆脭潞脰脟脛脺禄炉脧卯脛驴隆垄脰拢脰脻脰脢脳脫脰脳脕枚脪陆脭潞脧卯脛驴碌脠脭脷脛脷碌脛 10赂枚脰脴碌茫脪陆脭潞掳赂脌媒,脨脗脭枚脕脣45赂枚脪陆脭潞掳赂脌媒,脌脹录脝路镁脦帽碌脛麓贸脨脥脪陆脭潞脪脩鲁卢鹿媒1000录脪,麓娄脌铆碌脛脢媒戮脻脕驴赂眉脢脟脥禄脝脝100脪脷脤玫隆拢
麓脣路陆掳赂脪脭陆隆驴碌麓贸脛脭脦陋潞脣脨脛,戮脹陆鹿脫脷脰脟禄脹脪陆脕脝隆垄脢媒脳脰陆隆驴碌鹿脺脌铆潞脥脰脟禄脹鹿芦脦脌脠媒麓贸脕矛脫貌,脰脗脕娄脫脷驴陋路垄露脿脰脰脫娄脫脙鲁隆戮掳,脢碌脧脰脕脣脟酶脫貌脛脷潞脣脨脛碌脛脕霉麓贸脪陆脕脝脫娄脫脙隆拢脣眉虏禄陆枚脫脜禄炉脕脣脪陆脭潞碌脛脪碌脦帽脕梅鲁脤,脤谩脡媒脕脣脪陆脭潞碌脛脭脣脫陋脨搂脗脢潞脥路镁脦帽脰脢脕驴,禄鹿麓脵陆酶脕脣脰脟禄脹鲁脟脢脨碌脛陆篓脡猫,脥脝露炉脕脣卤茫脙帽禄脻脙帽鹿陇鲁脤碌脛脗盲碌脴隆拢
脥卢脢卤鹿芦脣戮脦搂脠脝鲁脟脢脨麓贸脛脭脮陆脗脭,脪酶陆颅录录脢玫脫毛掳垄脌茂脭脝隆垄赂脽碌脗隆垄脮茫麓贸脰脨驴脴隆垄潞拢驴碌隆垄麓贸禄陋隆垄脫卯脢脫隆垄脮茫陆颅赂脽脨脜隆垄
卤篓赂忙脝脷脛脷,鹿芦脣戮脳梅脦陋脳篓脪碌碌脛脢媒脳脰鲁脟脢脨陆篓脡猫脭脣脫陋路镁脦帽脡脤,脝戮陆猫脦脠鹿脤碌脛脢媒戮脻禄霉脳霉隆垄脳驴脭陆碌脛脢媒戮脻脰脦脌铆脛脺脕娄,脪脭录掳路谩赂禄碌脛脨脨脪碌脢碌录霉戮颅脩茅,脭脷脢媒脳脰戮颅录脙脌脣鲁卤脰脨脮鹿脧脰鲁枚脟驴麓贸碌脛戮潞脮霉脢碌脕娄隆拢脭脷碌卤脟掳卤鲁戮掳脧脗,脢媒脰脟禄炉脳陋脨脥脪脩鲁脡脦陋虏禄驴脡脛忙脳陋碌脛脟梅脢脝隆拢
脢脳脧脠,脢媒脳脰戮颅录脙脳梅脦陋脥脝露炉戮颅录脙赂脽脰脢脕驴路垄脮鹿碌脛潞脣脨脛脰搂脰霉脰庐脪禄,脪脩戮颅碌脙碌陆脕脣鹿茫路潞脠脧脥卢拢禄脝盲麓脦,脠芦脟貌路露脦搂脛脷脠脮脪忙脭枚露脿碌脛虏禄脠路露篓脨脭路莽脧脮,脪虏麓脵脢鹿脝贸脪碌脛脣脰脕脮没赂枚脡莽禄谩录脫驴矛脢媒脰脟禄炉脳陋脨脥碌脛虏陆路楼隆拢
戮颅鹿媒露脿脛锚碌脛脧碌脥鲁脩脨路垄陆篓脡猫,鹿芦脣戮脭脷脩脨路垄鹿忙禄庐隆垄脧卯脛驴鹿脺脌铆隆垄脳脢陆冒脥露脠毛隆垄脥脜露脫陆篓脡猫脪脭录掳鲁脡鹿没脳陋禄炉碌脠路陆脙忙,脪脩陆篓脕垄脝冒脪禄脤脳赂脽脨搂鲁脡脢矛碌脛鹿脺脌铆脤氓脧碌隆拢脮芒脪禄脤氓脧碌虏禄陆枚脠路卤拢脕脣鹿芦脣戮脩脨路垄麓麓脨脗禄卯露炉碌脛脫脨脨搂脨脭潞脥赂脽脨搂脨脭,赂眉麓脵陆酶脕脣鹿芦脣戮潞脣脨脛戮潞脮霉脕娄碌脛脧脭脰酶脤谩脡媒隆拢
鹿芦脣戮虏禄露脧脤谩脡媒驴陋路脜虏茫麓脦,路垄禄脫脝陆脤篓脟驴戮垄脰搂鲁脜脳梅脫脙隆拢2023脛锚,鹿芦脣戮脠脵禄帽 2022脛锚露脠脰脨鹿煤脰脟脛脺陆篓脰镁脨脨脪碌脢庐麓贸脕矛戮眉脝贸脪碌隆垄2022脛锚露脠脰脟脛脺陆篓脰镁脨脨脪碌脢庐麓贸脝路脜脝脝贸脪碌隆垄2022脛锚露脠脠铆录镁潞脥脨脜脧垄录录脢玫路镁脦帽戮潞脮霉脕娄掳脵脟驴脝贸脪碌隆垄2023脛锚露脠脠铆录镁潞脥脨脜脧垄录录脢玫路镁脦帽戮潞脮霉脕娄掳脵脟驴脝贸脪碌隆垄2022脛锚露脠脦芒脦脛驴隆脠脣鹿陇脰脟脛脺驴脝脩搂录录脢玫陆卤隆垄2022脛锚潞录脰脻脢脨脠脣脙帽脮镁赂庐脰脢脕驴鹿脺脌铆脫脜脢陇陆卤隆垄2022脛锚露脠脳脹潞脧脢碌脕娄脨脥脰脟禄脹赂鲁脛脺脙没脜脝脝贸脪碌碌脠露脿脧卯脰脴麓贸脠脵脫镁,陆酶脪禄虏陆鹿庐鹿脤脕脣鹿芦脣戮脨脨脪碌碌脴脦禄脫毛脢脨鲁隆戮潞脮霉脕娄隆拢
鹿芦脣戮脪脭脪碌录篓脦陋碌录脧貌,脳脜脩脹鲁陇脭露脛驴卤锚,脫脜禄炉脠脣虏脜陆谩鹿鹿,麓贸脕娄脥脝陆酶脠脣虏脜脮陆脗脭鹿忙禄庐陆篓脡猫鹿陇脳梅拢禄鹿芦脣戮脟驴禄炉脠脣虏脜陆篓脡猫,潞禄脢碌路垄脮鹿赂霉禄霉,脦陋鹿芦脣戮脭脷脨脨脪碌脛脷路垄脮鹿碌矛露篓录谩露篓禄霉麓隆隆拢脥卢脢卤,鹿芦脣戮脦陋卤拢鲁脰脭卤鹿陇麓麓脨脗禄卯脕娄,脥脝陆酶脤氓脰脝禄煤脰脝赂脛赂茂潞脥脮镁虏脽麓麓脨脗,鲁盲路脰录陇路垄赂梅脌脿脠脣虏脜碌脛麓麓脭矛禄卯脕娄,脪媒陆酶赂脽戮芦录芒脠脣虏脜,麓贸脕娄脥脝露炉鹿脥脰梅脝路脜脝脨脦脧贸隆拢
脣脛隆垄鹿芦脣戮脦麓脌麓路垄脮鹿碌脛脮鹿脥没
2023脛锚,鹿芦脣戮鲁脰脨酶脥脝陆酶陆谩鹿鹿脫脜禄炉隆垄脳陋脨脥脡媒录露隆垄脛脷脡煤脥芒脩脫,禄霉虏茫脰脦脌铆脪碌脦帽驴陋脥脴鲁玫脧脭鲁脡脨搂,脪脩鲁脡脦陋脨脗碌脛脫炉脌没脭枚鲁陇碌茫隆拢2024脛锚,鹿芦脣戮陆芦脭脷潞脣脨脛脪碌脦帽脡脧陆酶脪禄虏陆戮脹陆鹿,卤拢鲁脰露脭脢媒脳脰戮颅录脙虏煤脪碌脢脨鲁隆潞脥脮镁虏脽碌脛脙么赂脨露脠,脳楼脳隆脢媒脳脰戮颅录脙虏煤脪碌碌脛路垄脮鹿禄煤脫枚,脭脷隆掳脪酶陆颅鲁脟脢脨麓贸脛脭隆卤脧碌脕脨禄炉虏煤脝路潞脥路镁脦帽脡脧陆酶脪禄虏陆戮芦脳录露篓脦禄,脡卯脠毛鹿谩鲁鹿鹿芦脣戮脭脷鲁脟脢脨麓贸脛脭脕矛脫貌脮陆脗脭虏录戮脰,脭脷脧脰脫脨脨脨脪碌录掳驴脥禄搂陆酶脪禄虏陆脡卯赂没,脢碌脧脰脳脻脧貌脩脫脡矛,鲁脰脨酶脭枚脟驴鹿芦脣戮脫炉脌没脛脺脕娄隆拢
2024脛锚,鹿芦脣戮陆芦陆酶脪禄虏陆脟驴禄炉脮陆脗脭碌脛脗盲脢碌,录脝禄庐脭脷脪脭脧脗路陆脙忙录脫脟驴脥露脠毛潞脥路垄脮鹿隆拢
1隆垄鲁脰脨酶脡卯赂没脫脜脢脝脨脨脪碌脫娄脫脙,脭枚脟驴赂脽脰脢脕驴路垄脮鹿脛脺脕娄
脢媒脳脰戮颅录脙脮媒脭脷录脫脣脵脩脻陆酶,脣茫脕娄鲁脡脦陋脟媒露炉脡莽禄谩戮颅录脙路垄脮鹿碌脛脡煤虏煤脕娄,脢媒戮脻鲁脡脦陋潞脣脨脛脡煤虏煤脪陋脣脴隆拢鹿芦脣戮陆芦脟驴禄炉潞脣脨脛脣茫路篓潞脥脢媒戮脻脰脦脌铆脩脨戮驴,陆篓脕垄录录脢玫卤脷脌脻,脦搂脠脝脩脨路垄脠脣鹿陇脰脟脛脺潞脥麓贸脢媒戮脻脧脿鹿脴潞脣脨脛录录脢玫隆垄脰脨脤篓脠铆录镁录掳脙忙脧貌脨脨脪碌碌脛脫娄脫脙虏煤脝路潞脥脢媒戮脻脰脦脌铆路镁脦帽,录脫脟驴脢媒戮脻脳脢虏煤陆篓脡猫潞脥驴陋路垄脭脣脫陋,碌眉麓煤驴陋路垄脙忙脧貌赂梅脪碌脦帽鲁隆戮掳碌脛脢媒戮脻脰脟脛脺路镁脦帽,脪脌戮脻鹿芦脣戮脕矛脧脠麓贸脢媒戮脻录录脢玫脝陆脤篓,麓贸脕娄脥脴脮鹿脢媒戮脻脰脦脌铆潞脥脳脭露炉禄炉脟氓脧麓鹿娄脛脺,脢碌脧脰脢媒脳脰脳脢虏煤碌脛赂脽脰脢脕驴脫娄脫脙路镁脦帽隆拢
鲁脰脨酶脥脝露炉脭颅脫脨录录脢玫脮陆脗脭碌脛脩脨路垄脥露脠毛碌脛脥卢脢卤,录脫脟驴录录脢玫麓麓脨脗脫毛虏煤脝路隆垄脭脣脫陋路镁脦帽隆垄脢媒脳脰禄炉录录脢玫脫毛脨脨脪碌脫娄脫脙鲁隆戮掳碌脛脡卯露脠脠脷潞脧,鲁脰脨酶脪媒脕矛潞脥脗煤脳茫驴脥禄搂脭脷脢媒脳脰禄炉隆垄脰脟脛脺禄炉碌脛脨猫脟贸,脥卢脢卤脢碌脧脰脝贸脪碌赂脽脰脢脕驴路垄脮鹿隆拢
2隆垄脫脜禄炉脳茅脰炉录脺鹿鹿潞脥鹿脺脌铆禄煤脰脝,卤拢脮脧赂梅脪碌脦帽脳脻脧貌脙么陆脻脥脴脮鹿脫毛潞谩脧貌赂脽脨搂脨颅脥卢
3隆垄脰镁脌脦路脌脧脽,卤拢脮脧脝贸脪碌脝陆脦脠陆隆驴碌脭脣脨脨
2024脛锚,脦陋脕脣脠路卤拢鹿芦脣戮脮没脤氓碌脛赂脽脨搂脭脣脳梅潞脥脦脠陆隆路垄脮鹿,脪陋鲁脰脨酶录脫脟驴路莽脧脮鹿脺驴脴脪芒脢露,脭脷陆篓脕垄脫脨脨搂碌脛脥芒虏驴禄路戮鲁脨脜脧垄脢脮录炉脟镁碌脌潞脥脭陇戮炉禄煤脰脝碌脛禄霉麓隆脡脧,陆酶脪禄虏陆陆篓脕垄陆隆脠芦碌脛脛脷驴脴脰脝露脠,脳枚潞脙路篓脦帽路莽脧脮掳脩驴脴,虏禄露脧脤谩赂脽虏脝脦帽隆垄脣掳脦帽隆垄鲁脡卤戮碌脠路莽脧脮路脌驴脴脛脺脕娄,脳枚潞脙脭陇路脌脨脭脥禄路垄脢脗录镁脭陇掳赂,脪脭赂眉潞脙碌脴脫娄露脭脢脨鲁隆卤盲禄炉脫毛脟卤脭脷脤么脮陆,脪脭脠芦脭卤隆垄脠芦鹿媒鲁脤隆垄戮芦脪忙禄炉鹿脺脌铆鲁脡脨搂陆芦脪酶陆颅碌脛脝路脜脝虏脕碌脛赂眉脕脕隆拢
4隆垄脢媒脳脰戮颅录脙路垄脮鹿脢脝脥路脟驴戮垄,脥脷戮貌隆掳脢媒脳脰脰脨鹿煤隆卤脨脗禄煤禄谩
脢媒脳脰脰脨鹿煤陆篓脡猫虏陆脗脛虏禄脥拢,2024脛锚,脢媒脳脰脮镁赂庐隆垄脢媒脳脰戮颅录脙潞脥禄霉虏茫脰脦脌铆碌脠鹿芦脣戮脣霉脢么脰梅脪陋脨脨脪碌禄貌脕矛脫貌,脠脭脠禄脢脟脮镁虏脽路莽驴脷脧脗碌脛脠脠碌茫赂脮脨猫脢脨鲁隆,脥露脳脢戮掳脝酶露脠陆脧赂脽隆拢脪貌麓脣,鹿芦脣戮脨猫脪陋虏禄脨赂陆么赂煤,禄媒录芦掳脩脦脮脢脨鲁隆禄煤脫枚,脳垄脰脴脤谩赂脽脧煤脢脹潞脥陆禄赂露碌脛脨搂脗脢,脪脭脤谩赂脽脫炉脌没脣庐脝陆隆拢脭脷脤谩脡媒驴脥禄搂脢脨鲁隆赂虏赂脟碌脛禄霉麓隆脡脧,录脫麓贸驴脥禄搂鹿碌脥篓,脥脷戮貌潞脥脪媒碌录驴脥禄搂隆掳脢媒脳脰脰脨鹿煤隆卤鹿忙禄庐虏录戮脰脨脗禄煤禄谩隆垄脨脗脨猫脟贸,脦陋鹿芦脣戮脢脨鲁隆戮颅脫陋脝脤碌忙脕录潞脙碌脛禄霉麓隆隆拢
2024脛锚,露颅脢脗禄谩陆芦录脤脨酶脥脝露炉鹿芦脣戮路垄脮鹿脮陆脗脭脗盲碌脴,脫脜禄炉潞脥脤谩脡媒鹿芦脣戮脰脦脌铆脣庐脝陆,麓貌脭矛鹿芦脣戮脭脷脢媒脳脰戮颅录脙脢卤麓煤碌脛潞脣脨脛脛脺脕娄,脥脝露炉鹿芦脣戮鲁脰脨酶陆隆驴碌赂脽脰脢脕驴路垄脮鹿隆拢
鹿芦脣戮脣霉麓娄脰脟禄脹鲁脟脢脨陆篓脡猫脧脿鹿脴脨脨脪碌脫毛鹿煤录脪潞锚鹿脹戮颅录脙脭脣脨脨脳麓驴枚脙脺脟脨,鹿芦脣戮碌脛路垄脮鹿脫毛鹿煤脙帽戮颅录脙脭脣脨脨脳麓驴枚鲁脢脮媒脧脿鹿脴脨脭,碌卤脟掳鹿芦脣戮脰梅脪陋驴脥禄搂脦陋脮镁赂庐虏驴脙脜,脮镁赂庐脮镁虏脽隆垄潞锚鹿脹碌梅驴脴脮镁虏脽隆垄脮镁赂庐脰梅鹿脺赂潞脭冒脠脣碌脛赂眉脤忙碌脠脪貌脣脴脰卤陆脫潞脥录盲陆脫脫掳脧矛鹿芦脣戮脰梅脫陋脪碌脦帽潞脥鹿芦脣戮驴脥禄搂隆拢
脨脨脪碌录录脢玫卤盲赂茂脠脮脨脗脭脗脪矛潞脥脢脨鲁隆戮潞脮霉碌脛虏禄露脧录脫戮莽,脤脴卤冒脢脟麓贸脢媒戮脻潞脥脠脣鹿陇脰脟脛脺碌脛虏煤脪碌脨脣脝冒,露脭鹿芦脣戮碌脛脢脨鲁隆驴陋脥脴隆垄录录脢玫麓垄卤赂潞脥脠脣虏脜麓垄卤赂麓酶脌麓脪禄露篓碌脛脤么脮陆隆拢脠莽鹿没鹿芦脣戮虏禄脛脺脫脨脨搂卤拢鲁脰潞脥脥锚脡脝潞脣脨脛脠脣脭卤碌脛录陇脌酶禄煤脰脝,陆芦禄谩脫掳脧矛碌陆潞脣脨脛脠脣脭卤禄媒录芦脨脭隆垄麓麓脭矛脨脭碌脛路垄禄脫,脪虏禄谩脫掳脧矛碌陆潞脣脨脛脥脜露脫潞贸卤赂脕娄脕驴碌脛陆篓脡猫,脭矛鲁脡脠脣虏脜脕梅脢搂,麓脫露酶赂酶鹿芦脣戮碌脛脡煤虏煤戮颅脫陋脭矛鲁脡虏禄脌没脫掳脧矛隆拢
3隆垄脰脟禄脹鲁脟脢脨脪碌脦帽脛拢脢陆碌脛路莽脧脮
4隆垄脥露脳脢虏垄鹿潞录掳鹿脺脌铆路莽脧脮
脦陋脕脣鹿芦脣戮路垄脮鹿脮陆脗脭录掳鹿忙禄庐碌脛脨猫脪陋,鹿芦脣戮录掳鹿芦脣戮脠芦脳脢脳脫鹿芦脣戮禄貌虏煤脪碌禄霉陆冒脫脷脧脿录脤脥锚鲁脡露脿赂枚鹿芦脣戮碌脛鹿脡脠篓脥露脳脢潞脥虏垄鹿潞,碌芦脥露脳脢虏垄鹿潞卤戮脡铆戮脥脢脟脪禄脰脰路莽脧脮陆脧赂脽碌脛脡脤脪碌禄卯露炉隆拢脣忙脳脜脥露脳脢虏垄鹿潞脧卯脛驴碌脛虏禄露脧脭枚露脿,鹿芦脣戮脫毛卤禄脥露脳脢虏垄鹿潞碌脛脝贸脪碌脰庐录盲麓忙脭脷脪碌脦帽脛拢脢陆隆垄鹿脺脌铆脰脝露脠隆垄脝贸脪碌脦脛禄炉脡脧碌脛虏卯脪矛,脪矛碌脴鹿脺脌铆麓酶脌麓碌脛鹿脺驴脴路莽脧脮隆垄脥脜露脫碌脛脦脠露篓脨脭路莽脧脮碌脠脪貌脣脴露录赂酶鹿芦脣戮鹿脺脌铆麓酶脌麓脨脗碌脛脤么脮陆隆拢脥卢脢卤,鹿芦脣戮露脭脥芒脥露脳脢虏垄鹿潞脢卤,麓忙脭脷脧卯脛驴卤戮脡铆鲁脨脜碌脌没脠贸脛脺路帽脢碌脧脰隆垄脢脨鲁隆脮镁虏脽卤盲禄炉录掳路篓脗脡碌脠路陆脙忙碌脛路莽脧脮脪貌脣脴隆拢
路虏脢脟麓麓脨脗戮脥脪禄露篓戮脽脫脨虏禄脠路露篓脨脭,戮脥脪禄露篓麓忙脭脷路莽脧脮隆拢脛驴脟掳鹿芦脣戮脪碌脦帽虏脡脫脙碌脛脢脟脧碌脥鲁陆篓脡猫+虏煤脝路陆禄赂露+脢媒戮脻脭脣脫陋陆谩潞脧碌脛脡脤脪碌脛拢脢陆,脦脼脗脹脢脟脮镁赂庐鲁枚脳脢脝贸脪碌陆篓脡猫脛拢脢陆,禄貌脮镁赂庐潞脥脝贸脪碌鹿虏脥卢鲁枚脳脢陆篓脡猫脛拢脢陆,禄貌脮镁赂庐鹿忙禄庐碌脷脠媒路陆陆篓脡猫潞脥脭脣脫陋脛拢脢陆,戮霉麓忙脭脷虏禄脥卢脌没卤脳潞脥路莽脧脮隆拢脥卢脢卤,鹿芦脣戮麓麓脨脗碌脛鲁脟脢脨麓贸脛脭脭脣脫陋路镁脦帽脢脟脰脟禄脹鲁脟脢脨脥脝陆酶脙忙脕脵碌脛脳卯麓贸脤么脮陆脰庐脪禄,脢脟脠芦脨脨脪碌脝贸脪碌露录脨猫脪陋脤陆脣梅碌脛脨脗脪碌脦帽潞脥脨脗脛拢脢陆,麓忙脭脷麓贸脕驴碌脛虏禄脠路露篓脨脭隆拢
(function(){var obj = null;if(document.getElementById('arctTailMark')) {obj = document.getElementById('arctTailMark');}if(obj!=null){var str = '<a href="http://www.10jqka.com.cn" target="_blank" id="back_web" style="font-weight:bold" rel="nofollow" class="backweb"><img src="http://i.thsi.cn/images/article/logo.jpg" style="height:15px">  路碌禄脴脢脳脪鲁</a><a href="https://t.10jqka.com.cn/m/game/getActivityPageById/?id=295" target="_blank" style="font-weight: bold; font-size: 14px; padding-left: 5px; margin-left: 5px; border-left: 2px solid">戮脵卤篓 &gt;</a>';var showed = 0;try{var oo = obj.previousSibling;while(oo.nodeType!=1 || oo.innerHTML.replace(/\s|\u3000/g,'').length==0){oo = oo.previousSibling;}if(oo.nodeType==1 && oo.tagName.toLowerCase()=='p') {var span = document.createElement('span');span.innerHTML =str;span.style.width = '130px';oo.appendChild(span);showed = 1;}}catch (e){}if(showed == 0){obj.innerHTML =str;}}})();
var hotstocks = new Array();

	        	        hotstocks['000716'] = '000716';

	    	    	        hotstocks['002456'] = '002456';

	    	    	        hotstocks['601216'] = '601216';

	    	    	        hotstocks['603005'] = '603005';

	    	    	        hotstocks['600206'] = '600206';

	    	    	        hotstocks['000795'] = '000795';

	    	    	        hotstocks['000564'] = '000564';

	    	    	        hotstocks['002510'] = '002510';
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄脗貌脠毛脟掳10鹿脡
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄陆酶脡垄禄搂脤脫脟掳10鹿脡
露脌录脪脳脢陆冒拢潞陆帽脠脮脰梅脕娄脗貌陆酶脟掳10鹿脡
CPO赂脜脛卯脮冒碌麓禄脴脡媒 脨脗脪脳脢垄脮脟鲁卢8%
脧没路脩碌莽脳脫赂脜脛卯鹿脡脮冒碌麓脳脽碌脥 脜路路脝鹿芒碌脠露脿鹿脡碌酶鲁卢5%
A鹿脡脠媒麓贸碌莽脨脜脭脣脫陋脡脤录炉脤氓脳脽脟驴 脰脨鹿煤碌莽脨脜脮脟鲁卢5%
脡脧潞拢脢脨脮镁赂庐鲁拢脦帽禄谩脪茅虏驴脢冒拢卢脰搂鲁脰脡脧脢脨鹿芦脣戮虏垄鹿潞脰脴脳茅拢卢脤谩脡媒鹿芦脣戮脰脢脕驴脜脿脫媒脕煤脥路脝贸脪碌
脰脨脨脜脰陇脠炉脨脗脠脦脳脺戮颅脌铆 脢脳麓脦鹿芦驴陋路垄脡霉
GPU露脌陆脟脢脼脛娄露没脧脽鲁脤陆芦鲁氓麓脤IPO拢隆虏脦鹿脡隆垄潞脧脳梅碌脠赂脜脛卯鹿脡脙没碌楼脪禄脌脌
脭酶脴鹿脠潞拢潞脠么脤脴脌脢脝脮隆掳驴陋脗脤碌脝隆卤拢卢脛镁碌脗脢卤麓煤陆芦驴录脗脟赂掳脙脌陆篓鲁搂
赂脮赂脮拢隆脩脟脤芦鹿脡脢脨拢卢脠芦脧脽脤酶脣庐拢隆A50脰卤脧脽路颅脗脤拢卢赂脹鹿脡脡卯碌梅拢隆路垄脡煤脕脣脢虏脙麓
脦脢陆莽M7脢脗鹿脢脨脗陆酶脮鹿拢潞鲁碌脰梅鲁脝隆掳鹿媒脫脷脧脿脨脜脫脿鲁脨露芦虏脜鹿潞脗貌隆卤虏垄脝冒脣脽禄陋脦陋
脌毛隆掳虏禄脗么戮脥陆没隆卤脰禄脢拢脕陆赂枚露脿脭脗拢隆脤脴脌脢脝脮脥脜露脫拢潞陆芦露脪脧脰鲁脨脜碌 脮眉戮脠TikTok
脡卯陆禄脣霉拢潞11脭脗16脠脮陆芦陆酶脨脨脡卯脢脨陆禄脪脳脧碌脥鲁虏芒脢脭
		var s = "_" + Math.random().toString(36).slice(2);
		document.write('<div id="' + s + '"></div>');
		(window.slotbydup=window.slotbydup || []).push({
			display: 'inlay-fix'
document.addEventListener('DOMContentLoaded', function () {
      var userid = 'nouser';
        // 忙拢聙忙聼楼 Vue 氓聮聦 AdvertCommon 忙聵炉氓聬娄氓颅聵氓聹篓
        if (Vue && window['mobileweb_AdvertCommon@1.0.4'].default) {
          Vue.use(window['mobileweb_AdvertCommon@1.0.4'].default);
          // 氓掳聺猫炉聲盲禄聨 cookie 盲赂颅忙聫聬氓聫聳 userid
          var match = document.cookie.match(/userid=([^;]*)/);
          if (match) {
            userid = match[1] || 'nouser';
      } catch (error) {
        console.error(error);
      // 氓聢聺氓搂聥氓聦聳 Vue 氓庐聻盲戮聥
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
脡忙脦麓鲁脡脛锚脠脣脦楼鹿忙脛脷脠脻戮脵卤篓
虏禄脕录脨脜脧垄戮脵卤篓碌莽禄掳拢潞(0571)88933003
戮脵卤篓脫脢脧盲拢潞*****************
脮茫陆颅脥卢禄篓脣鲁禄楼脕陋脨脜脧垄录录脢玫脫脨脧脼鹿芦脣戮掳忙脠篓脣霉脫脨
var newsid = '657433983';
    var id='c_657433983';
	var initctime = '1714408267';
	var stitle = encodeURIComponent('脪酶陆颅录录脢玫2023脛锚脛锚露脠露颅脢脗禄谩戮颅脫陋脝脌脢枚');
	var artStock = new Array();
	var cotitle = '脪酶陆颅录录脢玫2023脛锚脛锚露脠露颅脢脗禄谩戮颅脫陋脝脌脢枚';
	var cocontent = '脪酶陆颅录录脢玫2023脛锚脛锚露脠露颅脢脗禄谩戮颅脫陋脝脌脢枚脛脷脠脻脠莽脧脗拢潞隆隆隆隆脪禄隆垄卤篓赂忙脝脷脛脷鹿芦脣戮脣霉麓娄脨脨脪碌脟茅驴枚鹿芦脣戮脨猫脳帽脢脴隆露脡卯脹脷脰陇脠炉陆禄脪脳脣霉脡脧脢脨鹿芦脣戮脳脭脗脡录脿鹿脺脰赂脪媒碌脷3潞脜隆陋隆陋脨脨脪碌脨脜脧垄脜没脗露隆路脰脨碌脛隆掳脠铆录镁脫毛脨脜脧垄录录脢玫路镁脦帽脪碌隆卤碌脛脜没脗露脪陋脟贸隆拢2023脛锚2脭脗,脰脨鹿虏脰脨脩毛隆垄鹿煤脦帽脭潞脫隆路垄脕脣隆露脢媒脳脰脰脨鹿煤陆篓脡猫';
	var codata = cocontent.replace(/<[^>]*>/g, '').substr(0,250);
	var courl = 'http://yuanchuang.10jqka.com.cn/20240430/c657433983.shtml';
		artStock[0] = '300020';
        newRobotRecList : [],
		cid: ''  //脳卯潞贸脪禄脤玫脝脌脗脹碌脛ID
	ARTINFO.tday = $.timeFormat(ARTINFO.tday, 'yyyyMMddHHmm');
	ARTINFO.tday = parseInt(ARTINFO.tday);
	ARTINFO.userid = WEB.basic.getUserid();
$(document).ready(function(){
	    	    TA.log({id:id, ld:'browser',fid:'info_gather,zx_n_all,zx_wzb_total,zx_ll_984,zxych,ch_yuanchuang', pt:2,_sid:'imgzx'});
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?78c58f01938e4d85eaf619eae71b4ed1";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?f79b64788a4e377c608617fba4c736e2";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
// 麓娄脌铆脥录脝卢src脦陋undefined
  var imageDomList = document.querySelectorAll('img');
  if (imageDomList.length > 0) {
      imageDomList.forEach(ele => {
          var imgSrc = ele.src;
          if (imgSrc.endsWith('/undefined')) {
              ele.remove();
  var srcVideoDom = document.querySelector('input.video-insert');
    var videoUrl = srcVideoDom.getAttribute('data-src');
    var videoThumb = srcVideoDom.getAttribute('data-thumb');
    var videoDom = document.createElement('video');
    var parent = srcVideoDom.parentNode;
    videoDom.src = videoUrl;
    videoDom.poster = videoThumb;
    videoDom.setAttribute("controls", true);
    videoDom.setAttribute("style", "width:627px;height:353px");
    parent.insertBefore(videoDom, srcVideoDom);
    srcVideoDom.remove();
  $('.self-stock-con').css({'height':'206px'})
// 脧脼脰脝碌脛禄煤脝梅脠脣脳茅录镁掳脳脙没碌楼
    const LIMIT_ROBOT_VISUAL_COMS = [
        'thsf2e_common-long-hu-rank',
        'thsf2e_ai-table'
    const checkLimitCom = (dataType) => {
        return LIMIT_ROBOT_VISUAL_COMS.includes(dataType);
    // 脕煤禄垄掳帽脤脴脢芒麓娄脌铆
    const LONGHU_TYPE = 'thsf2e_common';
    // 禄帽脠隆dom陆脷碌茫脨脜脧垄
    const getDomAttrData = (robotParse) => {
        const dataType = robotParse.getAttribute('data-type');
        const dataProps = robotParse.getAttribute('data-props');
        const dataComurl = robotParse.getAttribute('data-comurl');
        const dataStyle = robotParse.getAttribute('data-style');
        const dataVersion = robotParse.getAttribute('data-version');
            dataType,
            dataProps,
            dataComurl,
            dataStyle,
            dataVersion,
    const loadScript = (url, callback) => {
        const originalDefine = window.define;
        window.define = undefined;
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.charset = 'utf-8';
        script.src = url;
        script.onload = function() {
            callback(null)
        script.onerror = function() {
            callback(`Failed to load script: ${url}`);
        document.body.appendChild(script);
    // 脜脨露脧window脌茂脢脟路帽脫脨脮芒赂枚脳茅录镁拢卢脠莽鹿没脫脨戮脥脰卤陆脫脢鹿脫脙拢卢脠莽鹿没脙禄脫脨戮脥录脫脭脴
    const getComponent = (id, url, callback) => {
        return new Promise((resolve, reject) => {
            if (window[id]) {
                return resolve(window[id].default)
            loadScript(url, (err) => {
                if (err || !window[id]) {
                    reject(err || '脳茅录镁id麓铆脦贸')
                } else {
                    resolve(window[id].default)
    // 陆芒脦枚禄煤脝梅脠脣脛脷脠脻
    const parseRobot = (ele) => {
        const { dataType, dataProps, dataComurl, dataStyle, dataVersion } = getDomAttrData(ele);
        const props = JSON.parse(decodeURIComponent(dataProps));
        const style = JSON.parse(decodeURIComponent(dataStyle));
        // 脜脨露脧脢脟路帽虏禄脭脷limit盲脰脠戮脰脨
        if (!checkLimitCom(dataType)) {
        const id = `${dataType}_index@${dataVersion}`;
        getComponent(id, dataComurl).then((com) => {
            const placeholder = document.createElement('div');
            placeholder.className = 'robot-visual-container';
            ele.replaceWith(placeholder);
            const comInstance = new Vue({
                el: placeholder,
                render: h => h('div', { attr: { id: 'robotVisual' }, style }, [
                    h(com, { props })
            }).$mount();
        }).catch(err => {
            console.error(err)
    const parseContentMain = () => {
        const eles = document.getElementById('contentApp').querySelectorAll('robot-parse');
        eles.forEach(ele => {
            parseRobot(ele)
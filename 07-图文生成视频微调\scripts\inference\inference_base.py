import torch
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def inference_base_model():
    """使用基础T2V-1.3B模型进行推理"""
    
    # 创建推理管道
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cuda",
        model_configs=[
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
        ],
    )
    pipe.enable_vram_management()
    
    # 生成视频
    prompt = "一只可爱的小猫在阳光明媚的花园里追逐蝴蝶"
    negative_prompt = "色调艳丽，过曝，静态，细节模糊不清"
    
    video = pipe(
        prompt=prompt,
        negative_prompt=negative_prompt,
        seed=0, 
        tiled=True,
    )
    
    # 保存视频
    save_video(video, "output_base_t2v-1.3b.mp4", fps=15, quality=5)
    print("基础模型推理完成")

if __name__ == "__main__":
    inference_base_model()

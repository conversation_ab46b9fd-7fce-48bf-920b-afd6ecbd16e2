#!/bin/bash

# 8×RTX 3090超级优化全量微调脚本
# 使用最极端的显存优化策略

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export TOKENIZERS_PARALLELISM=false

echo "🚀 开始8×RTX 3090超级优化全量微调..."
echo "⚠️  使用最极端的显存优化策略"
echo "🔧 超级优化配置:"
echo "   - 分辨率: 64×112 (最小)"
echo "   - 梯度累积: 64步 (最大)"
echo "   - 学习率: 1e-8 (最小)"
echo "   - 数据重复: 1次"
echo "   - 混合精度: bf16"
echo "   - 梯度检查点: 启用"

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 64 \
    --width 112 \
    --dataset_repeat 1 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --trainable_models "dit" \
    --learning_rate 1e-8 \
    --num_epochs 1 \
    --gradient_accumulation_steps 64 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/8x3090_full_finetune_ultra" \
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090超级优化全量微调完成"

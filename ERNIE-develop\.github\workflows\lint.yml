name: Codestyle Check

on: [push, pull_request]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  Lint:
    name: <PERSON>t
    runs-on: ubuntu-latest
    steps:
      - name: Download Code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Checkout Develop
        run: |
          if ! git show-ref --quiet refs/heads/develop; then \
              echo "local develop branch is missing, creating local develop branch that tracks remote develop branch"
              git fetch origin develop
              git branch develop --track origin/develop
          else
            echo "local develop branch exist, skipping"
          fi

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          cache: 'pip' # caching pip dependencies
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit
          pre-commit install
      - name: Test
        run: make -f tools/codestyle/Makefile lint
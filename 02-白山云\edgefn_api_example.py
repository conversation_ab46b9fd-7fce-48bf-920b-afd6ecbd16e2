#!/usr/bin/env python3
"""
EdgeFN API 调用示例
使用 DeepSeek-R1-0528 模型进行对话
"""

import requests
import os
import json
from typing import Dict, List, Optional


class EdgeFNClient:
    """EdgeFN API 客户端"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化客户端
        
        Args:
            api_key: API密钥，如果不提供则从环境变量EDGEFN_API_KEY读取
        """
        self.api_key = api_key or os.getenv('EDGEFN_API_KEY')
        if not self.api_key:
            raise ValueError("API Key 未提供。请设置环境变量 EDGEFN_API_KEY 或直接传入 api_key 参数")
        
        self.base_url = "https://api.edgefn.net/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def chat_completion(self,
                       messages: List[Dict[str, str]],
                       model: str = "DeepSeek-R1-0528-Qwen3-8B",
                       temperature: float = 0.7,
                       max_tokens: Optional[int] = None,
                       stream: bool = False) -> Dict:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "消息内容"}]
            model: 模型名称，默认为 DeepSeek-R1-0528-Qwen3-8B
            temperature: 温度参数，控制回复的随机性 (0-1)
            max_tokens: 最大token数量
            stream: 是否使用流式响应
            
        Returns:
            API响应的字典
        """
        url = f"{self.base_url}/chat/completions"
        
        data = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "stream": stream
        }
        
        if max_tokens:
            data["max_tokens"] = max_tokens
        
        try:
            response = requests.post(url, headers=self.headers, json=data, timeout=30)
            response.raise_for_status()  # 如果状态码不是2xx，抛出异常
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"响应状态码: {e.response.status_code}")
                print(f"响应内容: {e.response.text}")
            raise
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            raise


def main():
    """主函数 - 演示如何使用EdgeFN API"""
    
    # 方法1: 从环境变量读取API Key（推荐）
    try:
        client = EdgeFNClient()
    except ValueError as e:
        print(f"错误: {e}")
        print("\n请设置环境变量或直接在代码中提供API Key")
        
        # 方法2: 直接提供API Key（不推荐用于生产环境）
        api_key = "sk-JGGCOW4Ye6fhyVFK45Fe46734bF84d1a934dFa3cCfD7337b"
        client = EdgeFNClient(api_key=api_key)
        print("⚠️  警告: 直接在代码中使用API Key不安全，建议使用环境变量")
    
    # 示例对话
    messages = [
        {"role": "user", "content": "你好，请介绍一下你自己"}
    ]
    
    print("发送请求到 EdgeFN API...")
    print(f"消息: {messages[0]['content']}")
    print("-" * 50)
    
    try:
        # 发送请求
        response = client.chat_completion(
            messages=messages,
            temperature=0.7,
            max_tokens=1000
        )
        
        # 解析响应
        if 'choices' in response and len(response['choices']) > 0:
            assistant_message = response['choices'][0]['message']['content']
            print("AI 回复:")
            print(assistant_message)
            
            # 显示使用统计
            if 'usage' in response:
                usage = response['usage']
                print(f"\n使用统计:")
                print(f"  输入tokens: {usage.get('prompt_tokens', 'N/A')}")
                print(f"  输出tokens: {usage.get('completion_tokens', 'N/A')}")
                print(f"  总tokens: {usage.get('total_tokens', 'N/A')}")
        else:
            print("未收到有效回复")
            print("完整响应:", json.dumps(response, indent=2, ensure_ascii=False))
            
    except Exception as e:
        print(f"调用API时发生错误: {e}")


def interactive_chat():
    """交互式聊天模式"""
    try:
        client = EdgeFNClient()
    except ValueError as e:
        print(f"错误: {e}")
        print("\n请设置环境变量或直接在代码中提供API Key")

        # 方法2: 直接提供API Key（不推荐用于生产环境）
        api_key = "sk-JGGCOW4Ye6fhyVFK45Fe46734bF84d1a934dFa3cCfD7337b"
        client = EdgeFNClient(api_key=api_key)
        print("⚠️  警告: 直接在代码中使用API Key不安全，建议使用环境变量")
    
    print("=== EdgeFN 交互式聊天 ===")
    print("输入 'quit' 或 'exit' 退出")
    print("-" * 30)
    
    messages = []
    
    while True:
        user_input = input("\n你: ").strip()
        
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("再见！")
            break
        
        if not user_input:
            continue
        
        # 添加用户消息
        messages.append({"role": "user", "content": user_input})
        
        try:
            # 发送请求
            response = client.chat_completion(messages=messages, temperature=0.7)
            
            if 'choices' in response and len(response['choices']) > 0:
                assistant_message = response['choices'][0]['message']['content']
                print(f"\nAI: {assistant_message}")
                
                # 添加助手回复到对话历史
                messages.append({"role": "assistant", "content": assistant_message})
            else:
                print("未收到有效回复")
                
        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    print("EdgeFN API 示例程序")
    print("1. 单次对话示例")
    print("2. 交互式聊天")
    
    choice = input("\n请选择模式 (1/2): ").strip()
    
    if choice == "1":
        main()
    elif choice == "2":
        interactive_chat()
    else:
        print("无效选择，运行单次对话示例")
        main()

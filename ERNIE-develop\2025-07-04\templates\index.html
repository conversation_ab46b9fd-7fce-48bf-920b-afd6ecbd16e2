<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Web搜索聊天机器人</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.assistant {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: 10px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            margin-right: 10px;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .user-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .assistant-avatar {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #4facfe;
        }

        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .clear-button {
            padding: 8px 16px;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
        }

        .clear-button:hover {
            background: #ff5252;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }

        .loading.show {
            display: block;
        }

        .search-indicator {
            font-size: 12px;
            color: #4facfe;
            margin-top: 5px;
            font-style: italic;
        }

        .timestamp {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
        }

        /* 滚动条样式 */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            .chat-container {
                width: 95%;
                height: 90vh;
                border-radius: 10px;
            }

            .message-content {
                max-width: 85%;
            }

            .chat-header {
                font-size: 20px;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🤖 DeepSeek Web搜索聊天机器人
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-avatar assistant-avatar">AI</div>
                <div class="message-content">
                    你好！我是DeepSeek智能助手，可以为你搜索网络信息并回答问题。请输入你想了解的内容！
                </div>
            </div>
        </div>
        
        <div class="loading" id="loading">
            <div>🔍 正在思考和搜索中...</div>
        </div>
        
        <div class="chat-input-container">
            <form class="chat-input-form" id="chatForm">
                <input type="text" class="chat-input" id="messageInput" 
                       placeholder="输入你的问题..." autocomplete="off">
                <button type="submit" class="send-button" id="sendButton">发送</button>
                <button type="button" class="clear-button" id="clearButton">清空</button>
            </form>
        </div>
    </div>

    <script>
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const clearButton = document.getElementById('clearButton');
        const chatForm = document.getElementById('chatForm');
        const loading = document.getElementById('loading');
        
        let sessionId = 'session_' + Date.now();

        // 添加消息到聊天界面
        function addMessage(content, isUser, hasSearch = false, timestamp = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const avatarDiv = document.createElement('div');
            avatarDiv.className = `message-avatar ${isUser ? 'user-avatar' : 'assistant-avatar'}`;
            avatarDiv.textContent = isUser ? '我' : 'AI';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            if (!isUser) {
                messageDiv.appendChild(avatarDiv);
                messageDiv.appendChild(contentDiv);
                
                if (hasSearch) {
                    const searchDiv = document.createElement('div');
                    searchDiv.className = 'search-indicator';
                    searchDiv.textContent = '🔍 已搜索网络信息';
                    contentDiv.appendChild(searchDiv);
                }
                
                if (timestamp) {
                    const timeDiv = document.createElement('div');
                    timeDiv.className = 'timestamp';
                    timeDiv.textContent = timestamp;
                    contentDiv.appendChild(timeDiv);
                }
            } else {
                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(avatarDiv);
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 发送消息
        async function sendMessage(message) {
            if (!message.trim()) return;
            
            // 添加用户消息
            addMessage(message, true);
            
            // 显示加载状态
            loading.classList.add('show');
            sendButton.disabled = true;
            sendButton.textContent = '发送中...';
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 添加AI回复
                    addMessage(data.response, false, data.search_results, data.timestamp);
                } else {
                    addMessage(`错误: ${data.error}`, false);
                }
                
            } catch (error) {
                addMessage(`网络错误: ${error.message}`, false);
            } finally {
                // 隐藏加载状态
                loading.classList.remove('show');
                sendButton.disabled = false;
                sendButton.textContent = '发送';
            }
        }

        // 清空聊天
        async function clearChat() {
            try {
                await fetch('/api/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_id: sessionId
                    })
                });
                
                // 清空界面
                chatMessages.innerHTML = `
                    <div class="message assistant">
                        <div class="message-avatar assistant-avatar">AI</div>
                        <div class="message-content">
                            聊天记录已清空！我是DeepSeek智能助手，请输入你想了解的内容！
                        </div>
                    </div>
                `;
                
            } catch (error) {
                console.error('清空聊天失败:', error);
            }
        }

        // 事件监听
        chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const message = messageInput.value.trim();
            if (message) {
                sendMessage(message);
                messageInput.value = '';
            }
        });

        clearButton.addEventListener('click', clearChat);

        // 回车发送消息
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                chatForm.dispatchEvent(new Event('submit'));
            }
        });

        // 页面加载完成后聚焦输入框
        window.addEventListener('load', () => {
            messageInput.focus();
        });
    </script>
</body>
</html>

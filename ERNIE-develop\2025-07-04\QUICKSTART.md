# 快速启动指南 (Flask版本)

## 🚀 一键启动

### Windows用户
1. 双击 `setup_and_run.bat` 文件
2. 等待环境配置完成
3. 浏览器访问 http://localhost:8088

### Linux/Mac用户
1. 打开终端，进入项目目录
2. 运行：`./setup_and_run.sh`
3. 浏览器访问 http://localhost:8088

## 📋 手动安装步骤

### 1. 创建conda环境
```bash
conda env create -f environment.yml
```

### 2. 激活环境
```bash
conda activate deepseek-websearch
```

### 3. 测试环境
```bash
python test_environment.py
```

### 4. 启动应用
```bash
python run.py
```

## 🔧 配置说明

### DeepSeek API密钥
编辑 `deepseek_client.py` 文件，修改API密钥：
```python
def __init__(self, api_key: str = "your-api-key-here"):
```

### 端口配置
如需修改端口，编辑 `run.py` 或 `web_search_demo_deepseek.py` 中的端口设置：
```python
demo.launch(
    server_name="0.0.0.0",
    server_port=8088,  # 修改这里
    share=False
)
```

## 🌐 访问地址

- 本地访问：http://localhost:8088
- 局域网访问：http://你的IP地址:8088

## ❓ 常见问题

### 环境创建失败
- 确保已安装conda
- 检查网络连接
- 尝试使用pip安装：`pip install -r requirements.txt`

### 端口被占用
- 修改端口号（见上方配置说明）
- 或者关闭占用8088端口的程序

### API调用失败
- 检查DeepSeek API密钥是否正确
- 确认网络可以访问API服务

## 📞 技术支持

如遇问题，请检查：
1. 运行 `python test_environment.py` 测试环境
2. 查看终端输出的错误信息
3. 确认所有依赖包已正确安装

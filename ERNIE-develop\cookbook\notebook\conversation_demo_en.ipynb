{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ERNIE Models Dialogue Application Demo\n", "\n", "\n", "This tutorial demonstrates how to use the ERNIE models to implement a simple demo for dialogue application. The following two models are mainly used:\n", "- Text-only Model: Supports text-based dialogue interactions.\n", "- Multimodal Model: Enables mixed input of both images and text for dialogue scenarios."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environmental Setup\n", "Before starting, ensure your system meets these requirements:\n", "- Python version 3.10-3.12 is installed.\n", "- Ensure the following Python libraries are included: `openai`, `base64`\n", "- Set the API key for the model `model_api_key`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_api_key = \"api_key\" # the API key for model, which can be disregarded and replaced with any arbitrary value when using the model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.1. Install Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install openai"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Text-Only Model\n", "The text-only model is ideal for basic language tasks that involve processing text input and generating corresponding output (e.g., question-answering, summarization, translation), without requiring complex reasoning capabilities.\n", "\n", "When using a text-only model, you need to deploy the [ERNIE-4.5](https://github.com/PaddlePaddle/FastDeploy) series model services and correctly configure the corresponding service address `ernie_45_url`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ernie_45_url = \"http://localhost:port/v1\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1. Model Input\n", "The model input consists of a message list that captures the conversational context history. Each message in the list is structured as a dictionary and includes the following fields:\n", "- `role`: the role of the message sender, which can be:\n", "    - `system`: System message, usually used to set the behavior of the model or initial instructions\n", "    - `user`: User message, indicating user inputs\n", "    - `assistant`: Model message, indicating the model's reply\n", "- `content`: Specific text content\n", "\n", "Text-only model input has the following characteristics:\n", "- System Role: System prompt words can be set to define the behavior of the model\n", "- Text-Only Dialogue: only text input and output are supported\n", "- Multiple Rounds of Dialogue: Supporting the preservation of historical dialogue context"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["text_messages = [\n", "    {\"role\": \"system\", \"content\": \"你是一个智能助手\"},\n", "    {\"role\": \"user\", \"content\": \"中国的首都是哪里？\"},\n", "    {\"role\": \"assistant\", \"content\": \"北京\"},\n", "    {\"role\": \"user\", \"content\": \"北京的地铁怎么样？\"}\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2. Non-Streaming Request\n", "#### 2.2.1. Request Model\n", "When sending a request to the API, the following main parameters need to be considered:\n", "- `messages` (must): List of text conversation messages\n", "- `max_tokens` (optional): configuration parameter for maximum number of generated tokens\n", "- `temperature` (optional): configuration parameter for controlling randomness in generated results\n", "- `top_p` (optional): configuration parameter for nucleus sampling"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'chatcmpl-fde2edb1-1b0d-4a59-b0e5-3918916f6c20', 'choices': [{'finish_reason': 'stop', 'index': 0, 'logprobs': None, 'message': {'content': '北京地铁的票价贵吗？\\n\\n北京地铁非常方便，覆盖面广，可以方便地到达城市的各个角落。票价方面，北京地铁的票价相对来说比较合理，根据乘坐的距离长短来计算票价，起步价为3元，根据行程长度进行计价，最高票价一般为10元。同时，北京地铁也提供一些优惠政策，如学生票、老年人票等，让更多人能够享受公共交通的便利。</s></s>', 'refusal': None, 'role': 'assistant', 'annotations': None, 'audio': None, 'function_call': None, 'tool_calls': None, 'reasoning_content': None}}], 'created': 1750076721, 'model': 'default', 'object': 'chat.completion', 'service_tier': None, 'system_fingerprint': None, 'usage': {'completion_tokens': 91, 'prompt_tokens': 29, 'total_tokens': 120, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}}\n"]}], "source": ["from openai import OpenAI\n", "\n", "client = OpenAI(base_url=ernie_45_url, api_key=model_api_key)\n", "response_text = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=text_messages,\n", "    temperature=1.0,\n", "    max_tokens=2048,\n", "    top_p=0.7\n", ")\n", "response_text = response_text.model_dump()\n", "print(response_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.2.2 Model Output\n", "- `content`: Final answer"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["北京的地铁非常发达，是世界上最长的地铁系统之一。它覆盖了城市中心和远郊区域，为市民和游客提供了方便快捷的交通方式。以下是一些北京地铁的特点：\n", "\n", "1. 线路覆盖面广：北京地铁已经开通了多条线路，覆盖了城市的主要区域，包括市中心、近郊和远郊。\n", "\n", "2. 运营时间长：北京地铁的运营时间通常是从早上5点到晚上11点左右，具体根据不同线路有所调整。\n", "\n", "3. 客流量大：由于北京是一个人口众多的城市，地铁客流量也非常大。在高峰时段，一些线路会非常拥挤。\n", "\n", "4. 技术先进：北京地铁采用了先进的信号技术和自动控制系统，确保了列车的安全和准时。\n", "\n", "5. 票价便宜：北京地铁的票价相对便宜，是根据乘坐的站数来计算的，最高票价也不会超过10元。\n", "\n", "总的来说，北京地铁是一种非常方便、快捷、经济实惠的交通方式，为市民和游客提供了良好的出行体验。</s></s>\n"]}], "source": ["content_text = response_text[\"choices\"][0][\"message\"][\"content\"]\n", "print(content_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3. Streaming Request\n", "#### 2.3.1. Request Model\n", "- `messages` (must): List of conversation messages\n", "- `max_tokens` (optional): configuration parameter for maximum number of generated tokens\n", "- `temperature` (optional): configuration parameter for controlling randomness in generated results\n", "- `top_p` (optional): configuration parameter for nucleus sampling\n", "- `stream` (optional): configuration parameter for enabling/disabling streamed return"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'id': 'chatcmpl-4b334e11-bb61-46fe-92e7-f3db8799ab5f', 'choices': [{'delta': {'content': '', 'function_call': None, 'refusal': None, 'role': 'assistant', 'tool_calls': None, 'reasoning_content': ''}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1750076763, 'model': 'default', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}, {'id': 'chatcmpl-4b334e11-bb61-46fe-92e7-f3db8799ab5f', 'choices': [{'delta': {'content': '北京', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None, 'token_ids': [3991], 'reasoning_content': None}, 'finish_reason': None, 'index': 0, 'logprobs': None, 'arrival_time': 0.058094024658203125}], 'created': 1750076763, 'model': 'default', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}, {'id': 'chatcmpl-4b334e11-bb61-46fe-92e7-f3db8799ab5f', 'choices': [{'delta': {'content': '地铁', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None, 'token_ids': [15560], 'reasoning_content': None}, 'finish_reason': None, 'index': 0, 'logprobs': None, 'arrival_time': 0.08609342575073242}], 'created': 1750076763, 'model': 'default', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}]\n"]}], "source": ["client = OpenAI(base_url=ernie_45_url, api_key=model_api_key)\n", "response_text = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=text_messages,\n", "    temperature=1.0,\n", "    max_tokens=2048,\n", "    top_p=0.7,\n", "    stream=True\n", ")\n", "response_text_stream = []\n", "for chunk in response_text:\n", "    if not chunk.choices:\n", "        continue\n", "    response_text_stream.append(chunk.model_dump())\n", "\n", "print(response_text_stream[:3])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2.3.2. Model Output\n", "The model's output will be delivered via streaming return.\n", "- `content`: Final answer"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["北京地铁的现状和发展情况如何？\n", "\n", "北京地铁是一项庞大而复杂的交通系统，为北京市民和游客提供了便捷的城市交通方式。以下是一些关于北京地铁的现状和发展情况：\n", "\n", "1. 线路覆盖范围：北京地铁目前拥有多条线路，覆盖了城市的主要区域和重要景点。不仅如此，地铁线路还在不断扩展，以满足城市发展的需求。\n", "\n", "2. 运营情况：北京地铁每天运营时间较长，通常从早上5点到晚上11点左右，高峰期间会加强列车频次以应对乘客需求。地铁列车在繁忙时段可能会非常拥挤，但运营方也在努力提高运力和舒适度。\n", "\n", "3. 技术创新：北京地铁引入了一些先进的技术，如智能支付系统、无线网络覆盖、安全监控系统等，以提升乘客体验和运营效率。\n", "\n", "4. 环保可持续发展：北京地铁也在积极推动环保和可持续发展，采用节能技术、噪音控制措施等，减少对环境的影响。\n", "\n", "5. 未来规划：北京地铁系统有进一步的扩展规划，包括新线路的建设和既有线路的改造升级，以适应城市发展和人口增长的需求。\n", "\n", "总体来说，北京地铁在不断发展壮大，为城市居民和游客提供了便捷、高效的交通选择。</s>\n"]}], "source": ["content_text_stream = \"\"\n", "for res in response_text_stream:\n", "    content_text_stream += res[\"choices\"][0][\"delta\"][\"content\"]\n", "print(content_text_stream)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Multimodal Model\n", "The multimodal model is well-suited for cross-modal task scenarios that demand the simultaneous processing of visual and textual information, including applications such as image captioning, visual-textual question answering (VQA), and other tasks requiring comprehensive visual semantic comprehension.\n", "\n", "When using multimodal models, you need to deploy the [ERNIE-4.5-VL](https://github.com/PaddlePaddle/FastDeploy) series model services and correctly configure the corresponding service address `ernie_45_vl_url`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ernie_45_vl_url = \"http://localhost:port/v1\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1. Model Input\n", "Multimodal model input has the following characteristics:\n", "- System Role: System prompt words can be set to define the behavior of the model\n", "- Multiple Rounds of Dialogue: Supporting the preservation of historical dialogue context\n", "- Mixed Input: Supports sending pictures and text content at the same time\n", "- Image Support: You can upload pictures through URL or local files"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# url pictures\n", "multi_messages_url = [\n", "    {\"role\": \"system\", \"content\": \"你是一个图片分析助手\"},\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image_url\", \"image_url\": {\"url\": \"https://nlp-eb.cdn.bcebos.com/static/eb/asset/topLogo.4a0fc7b7\"}},\n", "        {\"type\": \"text\", \"text\": \"请分析这张图\"}\n", "    ]}\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When uploading local pictures, you need to use the `base64` library to convert the image file into a Base64 encoded string and splice it into a standard format of `data:image/{type};base64,{Base64 encoded string}`."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'system', 'content': '你是一个图片分析助手'}, {'role': 'user', 'content': [{'type': 'image_url', 'image_url': {'url': 'data:image/png;base64,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'}}, {'type': 'text', 'text': '请分析这张图'}]}]\n"]}], "source": ["import base64\n", "\n", "\n", "# Upload pictures locally\n", "def get_image_url(image_path):\n", "    base64_image = \"\"\n", "    extension = image_path.split(\".\")[-1]\n", "    with open(image_path, \"rb\") as image_file:\n", "        base64_image = base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "    url = f\"data:image/{extension};base64,{base64_image}\"\n", "    return url\n", "\n", "test_image = \"../assets/yiyan_logo.png\"\n", "image_url = get_image_url(test_image)\n", "multi_messages_local = [\n", "    {\"role\": \"system\", \"content\": \"你是一个图片分析助手\"},\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image_url\", \"image_url\": {\"url\": image_url}},\n", "        {\"type\": \"text\", \"text\": \"请分析这张图\"}\n", "    ]}\n", "]\n", "print(multi_messages_local)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2. <PERSON><PERSON> Model\n", "When sending a request to the API, the following main parameters need to be considered:\n", "- `messages` (must): a list of graphic and text dialogue messages. The pictures can be uploaded locally (`multi_messages_local`), or you can enter the URL address (`multi_messages_url`)\n", "- `max_tokens` (optional): configuration parameter for maximum number of generated tokens\n", "- `temperature` (optional): configuration parameter for controlling randomness in generated results\n", "- `top_p` (optional): configuration parameter for nucleus sampling"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'chatcmpl-a3f63b46-ab8a-4d7e-a62c-27709dfac710', 'choices': [{'finish_reason': 'stop', 'index': 0, 'logprobs': None, 'message': {'content': '这是一张“文心一言”的logo图片。\\n### 图形部分\\n左侧图形整体是六边形轮廓，内部有类似信息流动或智能交互的抽象图案，采用蓝色调，蓝色常给人科技、专业、理智的感觉，这与产品作为人工智能语言模型的科技属性相契合。\\n### 文字部分\\n右侧“文心一言”四个汉字，字体设计简洁现代，同样使用蓝色，与左侧图形在色彩上相呼应，整体视觉风格统一，突出了产品的品牌名称，便于用户识别和记忆。 \\n\\n该logo通过简洁的设计和具有科技感的元素，传达出产品智能化、现代化的特点。</s></s>', 'refusal': None, 'role': 'assistant', 'annotations': None, 'audio': None, 'function_call': None, 'tool_calls': None, 'reasoning_content': None}}], 'created': 1750077575, 'model': 'default', 'object': 'chat.completion', 'service_tier': None, 'system_fingerprint': None, 'usage': {'completion_tokens': 135, 'prompt_tokens': 54, 'total_tokens': 189, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}}\n"]}], "source": ["from openai import OpenAI\n", "\n", "client = OpenAI(base_url=ernie_45_vl_url, api_key=model_api_key)\n", "response_multi = client.chat.completions.create(\n", "    model=\"default\",\n", "    messages=multi_messages_local,\n", "    temperature=1.0,\n", "    max_tokens=2048,\n", "    top_p=0.7\n", ")\n", "response_multi = response_multi.model_dump()\n", "print(response_multi)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3. Model Output\n", "- `content`: Final answer"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["这是一张“文心一言”的logo图片。\n", "### 图形部分\n", "左侧图形整体是六边形轮廓，内部有类似信息流动或智能交互的抽象图案，采用蓝色调，蓝色常给人科技、专业、理智的感觉，这与产品作为人工智能语言模型的科技属性相契合。\n", "### 文字部分\n", "右侧“文心一言”四个汉字，字体设计简洁现代，同样使用蓝色，与左侧图形在色彩上相呼应，整体视觉风格统一，突出了产品的品牌名称，便于用户识别和记忆。 \n", "\n", "该logo通过简洁的设计和具有科技感的元素，传达出产品智能化、现代化的特点。</s></s>\n"]}], "source": ["content_multi = response_multi[\"choices\"][0][\"message\"][\"content\"]\n", "print(content_multi)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}
﻿# 信号完整性的测试方法

**发布日期**: 2011年04月25日

**原文链接**: https://www.eepw.com.cn/article/191222.htm

## 📄 原文内容

信号完整性 的测试手段很多，涉及的仪器也很多，因此熟悉各种测试手段的特点，以及根据测试对象的特性和要求，选用适当的测试手段，对于选择方案、验证效果、解决问题等硬件开发活动，都能够大大提高效率，起到事半功倍的作用。表1： 信号完整性 测试手段分类。

信号完整性的测试手段主要可以分为三大类，如表1所示。表中列出了大部分信号完整性测试手段，这些手段既有优点，但是也存在局限性，实际上不可能全部都使用，下面对这些手段进行一些说明。

波形测试是信号完整性测试中最常用的手段，一般是使用示波器进行，主要测试波形幅度、边沿和毛刺等，通过测试波形的参数，可以看出幅度、边沿时间等是否满足器件接口电平的要求，有没有存在信号毛刺等。由于示波器是极为通用的仪器，几乎所有的硬件工程师都会使用，但并不表示大家都使用得好。波形测试也要遵循一些要求，才能够得到准确的信号。

首先是要求主机和探头一起组成的带宽要足够。基本上测试系统的带宽是测试信号带宽的3倍以上就可以了。实际使用中，有一些工程师随便找一些探头就去测试，甚至是A公司的探头插到B公司的示波器去，这种测试很难得到准确的结果。

其次要注重细节。比如测试点通常选择放在接收器件的管脚，如果条件限制放不到上面去的，比如BGA封装的器件，可以放到最靠近管脚的PCB走线上或者过孔上面。距离接收器件管脚过远，因为信号反射，可能会导致测试结果和实际信号差异比较大;探头的地线尽量选择短地线等。

最后，需要注意一下匹配。这个主要是针对使用同轴电缆去测试的情况，同轴直接接到示波器上去，负载通常是50欧姆，并且是直流耦合，而对于某些电路，需要直流偏置，直接将测试系统接入时会影响电路工作状态，从而测试不到正常的波形。

眼图测试是常用的测试手段，特别是对于有规范要求的接口，比如E1/T1、USB、10/100BASE-T，还有光接口等。这些标准接口信号的眼图测试，主要是用带MASK(模板)的示波器，包括通用示波器，采样示波器或者信号分析仪，这些示波器内置的时钟提取功能，可以显示眼图，对于没有MASK的示波器，可以使用外接时钟进行触发。使用眼图测试功能，需要注意测试波形的数量，特别是对于判断接口眼图是否符合规范时，数量过少，波形的抖动比较小，也许有一下违规的情况，比如波形进入MASK的某部部分，就可能采集不到，出现误判为通过，数量太多，会导致整个测试时间过长，效率不高，通常情况下，测试波形数量不少于2000，在3000左右为适宜。

目前有一些仪器，利用分析软件，可以对眼图中的违规详细情况进行查看，比如在MASK中落入了一些采样点，在以前是不知道哪些情况下落入的，因为所有的采样点是累加进去的，总的效果看起来就象是长余晖显示。而新的仪器，利用了其长存储的优势，将波形采集进来后进行处理显示，因此波形的每一个细节都可以保留，因此它可以查看波形的违规情况，比如波形是000010还是101010，这个功能可以帮助硬件工程师查找问题的根源所在。

抖动测试现在越来越受到重视，因为专用的抖动测试仪器，比如TIA(时间间隔分析仪)、SIA3000，价格非常昂贵，使用得比较少。使用得最多是示波器加上软件处理，如TEK的TDSJIT3软件。通过软件处理，分离出各个分量，比如RJ和DJ，以及DJ中的各个分量。对于这种测试，选择的示波器，长存储和高速采样是必要条件，比如2M以上的存储器，20GSa/s的采样速率。不过目前抖动测试，各个公司的解决方案得到结果还有相当差异，还没有哪个是权威或者行业标准。

图2：两种电缆的差分传输损耗(上)和差分近端串扰(下)。

TDR测试目前主要使用于PCB(印制电路板)信号线、以及器件阻抗的测试，比如单端信号线，差分信号线，连接器等。这种测试有一个要求，就是和实际应用的条件相结合，比如实际该信号线的信号上升沿在300ps左右，那么TDR的输出脉冲信号的上升沿也要相应设置在300ps附近，而不使用30ps左右的上升沿，否则测试结果可能和实际应用有比较大的差别。影响TDR测试精度有很多的原因，主要有反射、校准、读数选择等，反射会导致较短的PCB信号线测试值出现严重偏差，特别是在使用TIP(探针)去测试的情况下更为明显，因为TIP和信号线接触点会导致很大的阻抗不连续，导致反射发生，并导致附近三、四英寸左右范围的PCB信号线的阻抗曲线起伏。
﻿# 话题:嬴彻科技加入Autoware基金会 加速推动卡车自动驾驶技术发展_同花顺财经

**发布日期**: 2025年06月24日

**原文链接**: http://news.10jqka.com.cn/comment/669128318.shtml

## 📄 原文内容

话题:嬴彻科技加入Autoware基金会 加速推动卡车自动驾驶技术发展
近日，嬴彻科技宣布加入Autoware基金会，将与全球行业领袖携手合作，共同推动创新发展，加速安全可靠可泛化的卡车自动驾驶技术全球商业化进程。　　 Autoware基金会于2018年成立，是一家致力于通过开源协作加速全球自动驾驶技术发展
网友评论只代表同花顺网友的个人观点，不代表同花顺金融服务网观点。
(function() {
				var s = "_" + Math.random().toString(36).slice(2);
				document.write('<div id="' + s + '"></div>');
				(window.slotbydup=window.slotbydup || []).push({
					id: '5839110',
					container: s,
					size: '300,250',
					display: 'inlay-fix'
				});
			})();
即将上任的美国国家安全顾问：计划于下周一重新上线TikTok
美国当选总统特朗普表示，所有拜登的行政命令都将被撤销
工信部：将加快研究出台促进数字产业高质量发展的文件
美国总统特朗普20日签署行政令 要求短视频社交媒体平台TikTok“不卖就禁用”法律在未来75天内暂不执行
(function() {
		var s = "_" + Math.random().toString(36).slice(2);
		document.write('<div id="' + s + '"></div>');
		(window.slotbydup=window.slotbydup || []).push({
			id: '5877103',
			container: s,
			size: '300,250',
			display: 'inlay-fix'
		});
	})();
_cmsad({appendId:'topba1',adb_id:'173'});
var newsid = '669128318';
	TA.log({id:'cmt_'+newsid, fid:'zx_cmt,info_gather'});
	function loginshow() {
		var uname = THS.user.getUname("escapename");
		var userid = THS.user.getUserid("userid");
		var avatar = 'http://u.thsi.cn/avatar/'+userid%10000+'/'+userid+'.gif';
	  	if (uname.length > 0) {
			$('#myname').html(uname);
			$('#myavatar').attr('src', avatar);
			$('#myavatar').attr('alt', uname);
			$('#header_login').hide();
			$('#header_logined').show();
			$('.logged_in').hide();
			$('.username').html(uname.substr(0,10)).show();
			$('#header_logined_out').attr({href: 'http://upass.10jqka.com.cn/logout?redir=HTTP_REFERER&sign='+loginoutcode});
			$('#sign').val(loginoutcode);
			$('#header_logined').hide();
			$('#header_login').show();
			$('.username').hide();
			$('.logged_in').show();
			$('.comment_submit').hide();
	$('.table-tzgj a').bind('click', function(){
		TA.log({id:'zx_cmt_tzgj', nj:1, _sid:'zx_cmt_tzgj'});
	$('.xsrp a').bind('click', function(){
		TA.log({id:'zx_cmt_xsrp', nj:1, _sid:'zx_cmt_xsrp'});
	$('.ckyw').bind('click', function(){
		TA.log({id:'zx_cmt_ckyw', nj:1, _sid:'zx_cmt_ckyw'});
	$('.tzckgd').bind('click', function(){
		TA.log({id:'zx_cmt_tzck', nj:1, _sid:'zx_cmt_tzck'});
	cid: ''  //最后一条评论的ID
ARTINFO.tday = $.timeFormat(ARTINFO.tday, 'yyyyMMddHHmm');
ARTINFO.tday = parseInt(ARTINFO.tday);
ARTINFO.userid = WEB.basic.getUserid();
sns_comment.core.config = {
	    'skinType': 'white', 'seq': '669128318'           
#footer{border-top:1px solid #777;clear:both;overflow:hidden;padding-top:10px;text-align:center;font-size:12px;line-height:24px;width:950px;margin:10px auto;font-family:tahoma,arial;}
#footer p.c333{color:#333;}
#footer span{color:#999999;padding:0 10px;}
#footer a{line-height:24px;color:#000;text-decoration:none;}
#footer a:hover{color:#CC3300;text-decoration:none;}
#footer .smarterwiki-linkify{padding:0 14px;}
var _bdhmProtocol = (("https:" == document.location.protocol) ? " https://" : " http://");
document.write(unescape("%3Cscript src='" + _bdhmProtocol + "hm.baidu.com/h.js%3F78c58f01938e4d85eaf619eae71b4ed1' type='text/javascript'%3E%3C/script%3E"));
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?78c58f01938e4d85eaf619eae71b4ed1";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?f79b64788a4e377c608617fba4c736e2";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
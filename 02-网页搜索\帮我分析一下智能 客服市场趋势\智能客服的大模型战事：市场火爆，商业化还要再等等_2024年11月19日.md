﻿# 智能客服的大模型战事：市场火爆，商业化还要再等等

**发布日期**: 2024年11月19日

**原文链接**: https://baijiahao.baidu.com/s?id=1817307246360714609&wfr=spider&for=pc

## 📄 原文内容

智能客服行业掀起了一股大模型热。

“现在客户上来就问能用大模型打电话吗？如果没有大模型能力，客户就不跟你聊了”，云蝠智能CEO魏佳星告诉数智前线，市场对大模型客服的认知度在加速。

行业内也把智能客服视作大模型率先落地的典型场景之一。大模型能更好地理解用户问题，生成与用户需求匹配的回复，甚至预测用户行为、提供个性化推荐。这极大提高了客服效率和质量，还为企业节省了大量人力和时间成本。

IDC中国研究经理程荫告诉数智前线，这一领域，企业投资大模型预期是做到替代人，而不是辅助人。而很多客服以外的场景落地成熟度不高，一个极大原因在大模型只能辅助人，不能很好地测算ROI。

技术的快速进步和大模型推理成本急剧降低，无疑是大模型落地智能客服的加速剂。 “去年年初ChatGPT的一次调用成本还在几美分，一通客服电话对客户的报价1.2毛，当时商业模式根本跑不通。现在推理成本急剧下降，一次外呼调用大模型只需要0.2分”，商业应用一下子加速。魏佳星提到，目前这股技术升级和成本下降的趋势还在持续。

热潮下，市场上正呈现出两种应用路径，老牌的智能客服企业积极叠加大模型能力，而大模型企业也快速切进智能客服赛道。

但还处于发展初期的大模型智能客服仍要攻克一系列难题。比如文本客服如何克服幻觉以及语音客服面临的时延及拟人度的挑战。一些企业正采取渐进策略，在一些要求不高的场景率先让产品落地。

不再智障的智能客服可能正在到来。

推理成本下降，大模型进智能客服提速

魏佳星进入智能客服市场已有七年，他看到，去年以来大模型技术快速演进，越来越多智能客服领域的商机、需求与大模型相关。“就跟汽车的智驾功能一样，不管客户最后用不用，但是没有它就不行。”

蓬勃的市场需求，从央国企的大模型招投标数据中也能窥见一些端倪。数智前线此前统计，今年Q3的大模型相关项目的招投标，仅项目名称中与智能客服相关的项目就有9个。虽然在总量超360的大盘里占比不算很高，但大模型智能客服的需求已经浮出水面，并形成了一定规模。

一家关乎国计民生领域的央企相关人士此前也告诉数智前线，他们正为旗下某超级应用里的大模型智能客服寻找合作供应商，预计项目规模在千万元级别。他们已经摸排了市面上包括阿里、讯飞等多个厂商的智能客服产品能力，当下正在产品选型阶段。

IDC今年八月的报告数据里能看到市场空间正在加速打开。2023年智能客服解决方案整体市场规模达到了30.8亿人民币，较2022年增长了近36.9%。

业界观察，蓬勃的需求，首先与过去近两年，大模型技术快速进步，对智能客服能力带来了明显的升级有关。

“大模型驱动的智能客服，企业不需要非常多的NLP小模型， 靠大模型能解决NLP的关键性任务，同时机器人的理解力、语言生成能力都有很大的跃迁。 ”百度智能云智能客服与智能内容产品部总经理张红光在今年9月的一场媒体沟通会上说。

当下，大模型改善客服体验，场景并不局限在直接跟用户沟通场景里的语音机器人和文本机器人上。比如在智能质检和分析的场景里，大模型可以用于确认人工客服或者机器人客服的服务是否符合企业定义的SOP，还能给对话打结构化标签，去改善客服服务。

程荫告诉数智前线，除了文本/语音机器人、智能质检、坐席辅助、数字人客服、视频客服、智能分析、座席培训等传统智能客服场景外，和客服强相关的其他领域，如营销、培训、运营、决策等存在用户交互需求的里，对大模型智能客服的需求也在提升。

魏佳星观察到，技术进步使得语音机器人在解锁一些原来做不了的场景，比如云蝠智能就在开拓基于大模型的智能体去做电话面试业务。这个新场景，对机器人的理解能力、语言表达自然程度都要有求，服务的报价也相比传统场景有倍数级的提升。

智能客服与大模型快速结合，还与模型的调用成本快速下降密切相关。

去年年初，GPT3.5的接口调用成本一次还在几美分的水平，当时魏佳星判断它只有单次调用成本下降到1分钱左右，行业里的成本账才能算得过来。现在，差不多18个月过后，语音机器人单次调用大模型接口的成本早已跌破了这个数字。

这不只发生在海外，国内的模型厂商们在今年上半年也开启了激烈的降价行动。阿里CEO吴泳铭今年9月提到，模型推理成本指数级下降，已经远远超过摩尔定律。零一万物CEO李开复几天前参加活动时提到了一个很激进的数字，过去一年半的时间内，大模型推理成本的价格差了500倍，同时模型能力还有很大程度的提升。

其他大模型厂商也有各种降价动作。推理成本明显的下降趋势，使得今年智能客服行业服务商们应用大模型的进程在提速。

可测算ROI VS仍待克服的障碍

去年以来，头部企业都把智能客服领域视作大模型落地的先行场景。

阿里云在去年云栖大会上首次发布的8款基于通义打造的行业大模型里就有智能客服通义晓蜜，它能基于企业需求定制客服对话机器人。网易数智则在去年9月推出了面向客户服务的商和大模型，用于智能质检、客服机器人、智能外呼、精准洞察等客服场景。

今年百度智能云从3月开始对外推出，并持续在升级的三款标杆应用里，就有基于大模型重构、升级的智能客服产品客悦。

腾讯集团副总裁、政企业务总裁李强也在今年腾讯数字生态大会的媒体沟通环节提到，相比应用到生产研发等复杂场景，在容错率相对高一些的场景，比如智能客服领域，大模型的落地应用进展会更快。

基于客服领域去落地及探索大模型应用的企业还有很多，技术服务商们纷纷进场，与这一场景里大模型带来的降本增效效果比较明显有关。

IDC程荫介绍，他们通过很多深度访谈发现，引入智能客服确实极大地提高了客户服务的效率和质量，还为企业节省了大量的人力和时间成本，带来的收益是能测算的。

百度集团执行副总裁、百度智能云总裁沈抖在云智大会上提到，客悦在客服自助解决率上有大幅提升。这是客服服务领域里的一个重要指标，“业界自助解决率普遍在80%左右，大模型重构升级后，客悦自助解决率突破了92%，客服变得更聪明、更拟人、更懂用户。”

快递物流信息服务商快递100，今年将大模型技术应用到客服场景。该公司CEO陈登坤告诉数智前线，“大模型与客户反馈及系统规则的协同效应显著增强，共同解决了高达90%的工单问题，仅10%的问题需人工干预，客诉一次性解决率高达99.4%。”

容联云大模型产品人士则提到，他们在保险服务场景里，基于大模型，实现了对400电销电话大量录音内容的洞察和挖掘。应用后，降低潜在客诉量达到10%左右。

电销电话以前只能围绕电话数量和时长去分析，但实际上电话内容里蕴含很多用户的潜在需求。有了大模型后，大模型能提取到用户的潜在需求，还能从电话内容里挖掘出资深服务人员的优质回复，为后续服务提供话术建议和参考。

这些都是大模型提升智能客服体验的进展。但硬币的另一面，依然还有很多难题需要解决。

IDC告诉数智前线，从2023年以来，头部的技术供应商还在进行一些产品化，商业化进展还不是很显著，市场还是以AI传统客服为主。大模型赋能的客服产品在行业的普遍应用中还存在与原有系统的耦合、理解能力有限、缺乏上下文感知、交互性和灵活性不足、个性化服务不足等挑战，需要根据具体场景持续的训练调优，投入数据、算力资源。

一位企服行业资深人士就感慨，在惊叹于技术的突破时，行业也切实需要去解决许多问题，比如真实沟通场景的幻觉控制，新技术融入到企业业务流程里的改造和管理成本等。

以语音场景里大模型驱动的智能体为例，当下语音的拟人度不够，客户很容易能听出来实时生成的声音不是真人。同时由于电话场景对时延要求高，幻觉问题也很难解决。

一些大型的项目，还涉及到后续建设和运维的成本。前文提及的某央企千万级智能客服项目，相关人士提到他们产品选型十分慎重，原因在于项目的后续建设和运维成本高。因此，他们虽然对各家厂商的能力特点如数家珍，却迟迟没有敲定合作对象。

另外，一位智能客服行业资深人士提到，除了技术层面的各种限制，她也看到一些场景并不鼓励完全“去人化”的客服。“现在AI可能解语文题的能力提升了，可能也能解数学题。但是客服场景里很多是客诉，它可能是法律题、心理题和社会题，很多时候用户还是更愿意接听电话的是真人。”

渐进式路线：在能落地的场景里先用起来

基于大模型去升级产品有一大特性，基础模型能力的跃升，会带来产品性能和体验的提升。但 在能力更强的模型到来之前，技术服务商们需要基于当下的技术局限，找到产品化、商业化的合适节奏。

魏佳星提到，他们的策略是， 在时延和拟人度问题没有理想的解决方案前，先找对这些指标要求比较低的业务场景先用起来 ，“比如400电话，你能接受它的时延问题，可以接受它的拟人度不够，听起来像机器人，现在这些场景里用起来。”

一家服务汽配行业的软件服务商用智能体来服务车厂及终端车主的特殊车型维修零配件报价，目前有80%的客服询价由智能体完成。

针对智能体有一些幻觉等不可控因素，他们也采取了智能体加上真人监管结合的服务模式，来更好服务客户。“询报价智能体在客户群里沟通，但是这个里面群里还是有销售在做一些监管的动作”，该服务商说。

容联云产业数字云VP孔淼则提到，针对大模型全面替代带来的算力成本及幻觉等问题，可以先用大模型解决局部问题。

“比如用它解决前端的泛化问题，转化成具体的任务后，再用高效的小模型来解决，采取大小模型结合的策略。”孔淼在几个月前的一场产业论坛中提到。

正如IDC所说，大模型赋能的智能客服发展还在初始阶段，商业化仍有待推进。不过， 一些企业也看到了当下市场偏好的一些特征，并尝试找到一些商业化的策略。

魏佳星发现，许多企业虽然对大模型功能有认知，但相比大模型原生的智能体产品，他们更偏好上一代的具备可解释性的产品，“短期里大模型是一个销售型功能”。

而大模型原生的Agent类型产品探索价值在于，一方面这是未来的发展方向，另外，它可以解耦出一部分能力，用于升级上一代产品，从而提升用户体验和留存，最终实现间接的商业转化。

以电话面试场景为例，这里面可能涉及到N个智能体协同工作。比如第一个AI对非结构化简历进行读取，让简历变成标准的小字段便于其后的AI阅读。第二个AI要调取GPT级别的最高级模型，教打电话的AI去训练它的缓存体系。第三个AI负责打电话，发起呼叫。第四个AI把通话记录抽取成标准的结论。

这个过程里四个AI能力可以被解耦，其中的一部分技术方案赋能给上一代产品，“目前商业化收入里很多来自技术方案复用给上一代之后带来的收入增长。”魏佳星说。

当下，无论是AI传统客服企业带着场景接入大模型能力，还是大模型企业带着锤子进入智能客服赛道，这一领域的热度正在提升。

“以云服务为代表的平台级公司在底层大模型、新一代智能客服产品上有研发优势，且有的云厂商旗下有一部分业务和客服强相关。它们有一定的品牌知名度和用户基数，需要加强的是中小企业的定制化服务。而较早进入市场的传统客服企业也有市场认知度，积攒了一定量客户，这些企业需要加强的则是大模型技术底层研发能力。”IDC程荫说。

一位AI客服领域的创业企业则认为，它们和巨头在定价能力及服务能力上有差异，小公司有灵活性，而大厂由于人力成本，涉及到交付团队后它很难服务报价低于五十万元的项目。

不管怎么样，竞争正变得激烈起来。不过市场也并非存量竞争。

上个月初，OpenAI的GPT-4o Realtime API公开测试版上线，魏佳星马上在内部推进基于这一接口开发新一代智能客服产品。

对语音场景的智能客服而言，这是一次重要的模型能力升级，它能大大解决语音机器人的时延问题。之前的模型要将语音转文本和文本转语音功能拼接在一起，而Realtime API是端到端模型，在语音交互时，表现会更自然。

这也是大模型赋能的智能客服产品的一大利好，长期来看，技术的进步和成本的下降，将会给产品带来质的突破，而新的市场空间也将进一步打开。
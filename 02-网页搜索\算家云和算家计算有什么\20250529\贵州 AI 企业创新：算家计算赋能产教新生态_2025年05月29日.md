﻿# 贵州 AI 企业创新：算家计算赋能产教新生态

**发布日期**: 2025年05月29日

**原文链接**: http://www.xfrb.com.cn/article/zx/18214219698380.html

## 📄 原文内容

function search_new(){
              alert('正在维护中，请谅解');
              // var token =$('#_token').val();
              // var a = $('#Keywords').val();
              // if(a.length < 2){
              //   alert('请输入搜索词语最少两个字符');
              // }else{
              //   var url = "http://search.xfrb.com.cn/index.php?s=/index/search&name="+a+'&sub='+token+'xfrb';
              //   window.location.href=url;
贵州 AI 企业创新：算家计算赋能产教新生态
前言:在人工智能技术加速渗透各行业的当下,贵州算家计算服务有限公司(以下简称“算家计算”)凭借创新技术与多元服务矩阵,在人工智能计算领域引发关注。2025年,其推出的“算家云青春版”与“算家工坊”两大平台,分别针对AI教育普惠与产业生产力升级需求,展现出对行业痛点的精准响应能力,引发行业广泛关注。
据悉,贵州算家计算服务有限公司于2022年成立,是一家由深圳瑞云科技战略投资、贵州省贵安超级计算中心和贵州大学共同孵化,依托西部算力枢纽资源优势,深度响应 
“东数西算” 战略,专注于人工智能技术、为用户提供专业计算服务的人工智能企业。
1、灵活算力租赁服务:依托其公司旗下核心产品算家云,基于多可用区容器智能调度技术,结合镜像和网盘等业务功能,可让用户轻松实现对容器实例的创建、关机、开机、释放等全生命进行管理操作。支持按需付费、各类算力资源随用随取、操作简便易上手、高效计算+数据安全保障。
2、容器云算力服务平台私有化部署:依托自研核心技术打造专属AI算力中枢,可为学校、企业提供量身定制的AI算力解决方案,将云端强大能力延伸至本地,助力企业构建安全、可控、高效的AI基础设施。预集成算家云核心功能模块,简化部署流程,快速构建企业专属AI平台。提供多层次安全防护机制,保障数据安全和业务连续性。
3、AI模型私有化部署及行业模型定制研发:基于算家云的AI模型私有云部署,通过资源调度技术汇聚各类高性能和国产算力+“青春版”线上服务平台+专业线下技术服务团队+deepseek等各类AI模型完备的部署服务经验,其可以个性化服务各领域中小微企业和科研院所,为用户提供一站式定制化AI计算服务解决方案。
算家云青春版:打造AI教育“共享算力”新生态,激活闲置资源价值
2024年10月,算家计算正式推出了其首款AI算力服务平台“算家云”。算家云是算家计算依托贵安超级计算中心和深圳瑞云强大的算力资源支撑打造的一款高效、便捷的AI算力服务平台。平台以“简单、高效、便宜”为核心理念,可为个人、企业用户提供全场景AI算力支持。该公司负责人表示:“算家云”的推出,旨在解决当前算力市场上存在的供需失衡、成本高与使用门槛高的问题。通过整合云计算、大数据和人工智能等先进技术,算家云能够提供稳定可靠的算力资源。
随着AI学习与教育场景的算力需求高涨,2025年,在“算家云”基础上,算家计算通过激活闲置算力资源,实现秒级异构算力资源弹性配置,特别推出了“算家云青春版”,为用户提供了更具性价比的AI学习入口,其以“低价不低质”的定位打造了普惠型AI教育基础设施。
该平台创新性引入“闲时算力循环生态”,调用大中型算力资源方的闲置算力,以50%市场价激活80%闲置资源,构建起AI算力领域的“共享经济”模式,其核心优势如下:
1、极低使用门槛:学生党可享受“1.24元40901小时算力实训”,万人并发实验成本降低约70%,开发者零门槛验证算法模型,彻底打破“算力贵、入门难”的行业壁垒;
2、灵活使用场景:支持按量付费,适配课程教学、项目实训、算法调试等多元需求,尤其适合高校实验室、AI 培训课程及个人开发者;
3、平台安全稳定:自研容器调度技术,算力资源弹性分配,沙箱隔离技术保障数据安全,确保闲时算力高效流转;
算家计算创新的“闲置算力共享+收入分成”模式,不仅为资源方创造额外收益,更推动普惠型AI教育基础设施建设,助力培养跨领域复合型人才。
算家工坊:重新定义AI生产力范式,开启云原生操作系统新时代
据算家计算研发负责人透露,算家计算即将于2025年发布全球首个云原生AI应用操作系统,系统以“AI即服务”为核心理念,通过Web化虚拟工作空与间分布式容器架构,将彻底革新传统AI工具使用模式。
“算家工坊的落地,标志着AI应用从‘门槛高、部署难’向‘普惠化、工具化’迈出关键一步。”算家计算研发负责人表示道。
未来展望:从算力服务到产业赋能,推动 AI 普惠落地
“算家云青春版与算家工坊的推出,标志着算家计算从‘算力供应商’向‘AI生态构建者’的跨越。作为一家人工智能企业,算家计算目前已累计获得22项软著专利,并得到‘高新技术企业’认定。”算家计算市场负责人表示道。据悉,2024,算家计算积极响应国产化号召,已完成与海光信息技术股份有限公司CPU、DCU兼容适配工作,成功完成国产化适配。其在2025年4月举行的AIAC 
2025第二届人工智能应用大会上荣获“年度人工智能技术创新企业”。未来,希望国产 AI 企业以匠心破局、守正创新,在推动 AI 普惠中聚智赋能,助力国产 AI 
生态产业以积厚成势之态稳健前行,共筑协同发展之基,终成百舸争流、万象更新之繁荣盛景。
2. 凡本网注明 “来源：XXX（非消费日报网）” 的作品，均转载自其它媒体，转载目的在于传递更多信息，并不代表本网赞同其观点和对其真实性负责。
3. 任何单位或个人认为消费日报网的内容可能涉嫌侵犯其合法权益，应及时向消费日报网书面反馈，并提供相关证明材料和理由，本网站在收到上述文件并审核后，会采取相应措施。
5. 基于技术和不可预见的原因而导致的服务中断，或者因用户的非法操作而造成的损失，消费日报网不负责任。
7. 联系邮箱：<EMAIL>  &nbsp电话：010-67637706
|  互联网新闻信息服务许可证编号：10120170031  |  京公网安备 11010602130018号
违法和不良信息举报电话：010-67605353 |  邮箱:<EMAIL>
消费日报社地址：北京市丰台区定安东里20号楼 邮编:100075
var cnzz_protocol = (("https:" == document.location.protocol) ? "https://" : "http://");document.write(unescape("%3Cspan id='cnzz_stat_icon_1273096637'%3E%3C/span%3E%3Cscript  src='" + cnzz_protocol + "s19.cnzz.com/z_stat.php%3Fid%3D1273096637%26show%3Dpic2' type='text/javascript'%3E%3C/script%3E"));
    var bp = document.createElement('script');
    var curProtocol = window.location.protocol.split(':')[0];
    if (curProtocol === 'https'){
   bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
  bp.src = 'http://push.zhanzhang.baidu.com/push.js';
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(bp, s);
var _hmt = _hmt || [];
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?318d72e535a84dc124e229e7932f10c7";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
# Wan-AI/Wan2.1-T2V-14B 多卡微调详细文档

## 概述

本文档详细介绍如何使用DiffSynth-Studio框架对Wan-AI/Wan2.1-T2V-14B模型进行多卡微调。Wan2.1-T2V-14B是一个强大的文本到视频生成模型，支持高质量的视频生成任务。

## 环境准备

### 1. 创建Conda虚拟环境

```bash
# 创建新的conda环境
conda create -n wan_t2v python=3.10 -y
conda activate wan_t2v

# 更新pip
pip install --upgrade pip
```

### 2. 安装依赖

```bash
# 克隆DiffSynth-Studio仓库
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 安装基础依赖
pip install -e .

# 安装训练相关依赖
pip install peft lightning pandas
pip install accelerate
pip install transformers
pip install diffusers
pip install xformers
pip install modelscope
pip install opencv-python
pip install pillow
pip install numpy
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装其他必要依赖
pip install wandb  # 可选，用于训练监控
pip install tensorboard  # 可选，用于训练可视化
```

### 3. 下载预训练模型

```bash
# 使用modelscope下载Wan2.1-T2V-14B模型
modelscope download Wan-AI/Wan2.1-T2V-14B --local_dir ./models/Wan2.1-T2V-14B

# 或者使用huggingface-hub下载
# pip install huggingface_hub
# huggingface-cli download Wan-AI/Wan2.1-T2V-14B --local-dir ./models/Wan2.1-T2V-14B
```

## 数据集准备

### 1. 数据集格式

根据DiffSynth-Studio的要求，创建训练数据集：

```
data/example_dataset/
├── metadata.csv
└── train/
    ├── video_00001.mp4
    ├── video_00002.mp4
    ├── image_00001.jpg
    └── image_00002.jpg
```

### 2. 元数据文件

创建 `metadata.csv` 文件：

```csv
file_name,text
video_00001.mp4,"A beautiful sunset over the ocean with waves gently crashing on the shore"
video_00002.mp4,"A cat playing with a ball of yarn in a cozy living room"
image_00001.jpg,"A serene mountain landscape with snow-capped peaks"
image_00002.jpg,"A bustling city street at night with neon lights"
```

**注意**：
- 支持视频文件（.mp4）和图像文件（.jpg, .png等）
- 图像被视为单帧视频处理
- 每行包含文件名和对应的文本描述

### 3. 数据预处理脚本

创建 `prepare_dataset.py`：

```python
import os
import csv
import cv2
from pathlib import Path

def create_metadata_csv(dataset_path, output_csv):
    """创建metadata.csv文件"""
    dataset_path = Path(dataset_path)
    train_folder = dataset_path / "train"

    metadata = []

    # 遍历训练文件夹中的所有文件
    for file_path in train_folder.glob("*"):
        if file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.jpg', '.jpeg', '.png', '.bmp']:
            # 这里可以根据文件名或其他方式生成描述
            # 实际使用时需要根据具体情况修改
            description = f"Description for {file_path.name}"

            metadata.append({
                "file_name": file_path.name,
                "text": description
            })

    # 写入CSV文件
    with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['file_name', 'text']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for item in metadata:
            writer.writerow(item)

    print(f"创建metadata.csv完成，共 {len(metadata)} 个样本")

def validate_dataset(dataset_path):
    """验证数据集完整性"""
    dataset_path = Path(dataset_path)
    metadata_file = dataset_path / "metadata.csv"
    train_folder = dataset_path / "train"

    if not metadata_file.exists():
        print("错误：metadata.csv文件不存在")
        return False

    if not train_folder.exists():
        print("错误：train文件夹不存在")
        return False

    # 读取metadata.csv
    with open(metadata_file, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        missing_files = []

        for row in reader:
            file_path = train_folder / row['file_name']
            if not file_path.exists():
                missing_files.append(row['file_name'])

        if missing_files:
            print(f"警告：以下文件在train文件夹中不存在：{missing_files}")
            return False
        else:
            print("数据集验证通过")
            return True

if __name__ == "__main__":
    dataset_path = "./data/example_dataset"
    create_metadata_csv(dataset_path, dataset_path + "/metadata.csv")
    validate_dataset(dataset_path)
```

## 多卡训练配置

### 1. Accelerate配置文件

创建 `accelerate_config.yaml`：

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 4
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

### 2. 初始化Accelerate配置

```bash
# 配置accelerate
accelerate config --config_file accelerate_config.yaml

# 或者交互式配置
# accelerate config
```

## 微调代码

### 1. 数据预处理步骤

首先需要对数据进行预处理，生成训练所需的缓存文件：

```bash
# Step 1: 数据预处理
CUDA_VISIBLE_DEVICES="0" python examples/wanvideo/train_wan_t2v.py \
  --task data_process \
  --dataset_path data/example_dataset \
  --output_path ./models \
  --text_encoder_path "models/Wan-AI/Wan2.1-T2V-14B/models_t5_umt5-xxl-enc-bf16.pth" \
  --vae_path "models/Wan-AI/Wan2.1-T2V-14B/Wan2.1_VAE.pth" \
  --tiled \
  --num_frames 81 \
  --height 720 \
  --width 1280
```

预处理完成后，数据集文件夹结构如下：

```
data/example_dataset/
├── metadata.csv
└── train/
    ├── video_00001.mp4
    ├── video_00001.mp4.tensors.pth  # 预处理生成的缓存文件
    ├── video_00002.mp4
    └── video_00002.mp4.tensors.pth  # 预处理生成的缓存文件
```

### 2. LoRA微调训练

使用LoRA进行参数高效微调：

```bash
# Step 2: LoRA训练
CUDA_VISIBLE_DEVICES="0,1,2,3" python examples/wanvideo/train_wan_t2v.py \
  --task train \
  --train_architecture lora \
  --dataset_path data/example_dataset \
  --output_path ./models \
  --dit_path "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00001-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00002-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00003-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00004-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00005-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00006-of-00006.safetensors" \
  --steps_per_epoch 500 \
  --max_epochs 10 \
  --learning_rate 1e-4 \
  --lora_rank 16 \
  --lora_alpha 16 \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --accumulate_grad_batches 1 \
  --use_gradient_checkpointing
```

### 3. 全量微调训练

如果需要进行全量微调：

```bash
# Step 2: 全量训练
CUDA_VISIBLE_DEVICES="0,1,2,3" python examples/wanvideo/train_wan_t2v.py \
  --task train \
  --train_architecture full \
  --dataset_path data/example_dataset \
  --output_path ./models \
  --dit_path "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00001-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00002-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00003-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00004-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00005-of-00006.safetensors,models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00006-of-00006.safetensors" \
  --steps_per_epoch 500 \
  --max_epochs 10 \
  --learning_rate 1e-4 \
  --accumulate_grad_batches 1 \
  --use_gradient_checkpointing
```

### 4. 多卡训练启动脚本

创建 `train_t2v_multicard.sh`：

```bash
#!/bin/bash

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 数据集路径
DATASET_PATH="data/example_dataset"
OUTPUT_PATH="./models"
MODEL_PATH="models/Wan-AI/Wan2.1-T2V-14B"

# Step 1: 数据预处理
echo "开始数据预处理..."
CUDA_VISIBLE_DEVICES="0" python examples/wanvideo/train_wan_t2v.py \
  --task data_process \
  --dataset_path ${DATASET_PATH} \
  --output_path ${OUTPUT_PATH} \
  --text_encoder_path "${MODEL_PATH}/models_t5_umt5-xxl-enc-bf16.pth" \
  --vae_path "${MODEL_PATH}/Wan2.1_VAE.pth" \
  --tiled \
  --num_frames 81 \
  --height 720 \
  --width 1280

echo "数据预处理完成，开始LoRA训练..."

# Step 2: LoRA训练
accelerate launch \
  --config_file accelerate_config.yaml \
  examples/wanvideo/train_wan_t2v.py \
  --task train \
  --train_architecture lora \
  --dataset_path ${DATASET_PATH} \
  --output_path ${OUTPUT_PATH} \
  --dit_path "${MODEL_PATH}/diffusion_pytorch_model-00001-of-00006.safetensors,${MODEL_PATH}/diffusion_pytorch_model-00002-of-00006.safetensors,${MODEL_PATH}/diffusion_pytorch_model-00003-of-00006.safetensors,${MODEL_PATH}/diffusion_pytorch_model-00004-of-00006.safetensors,${MODEL_PATH}/diffusion_pytorch_model-00005-of-00006.safetensors,${MODEL_PATH}/diffusion_pytorch_model-00006-of-00006.safetensors" \
  --steps_per_epoch 500 \
  --max_epochs 10 \
  --learning_rate 1e-4 \
  --lora_rank 16 \
  --lora_alpha 16 \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --accumulate_grad_batches 1 \
  --use_gradient_checkpointing

echo "训练完成！"
```

## 推理代码

### 1. LoRA模型推理脚本

创建 `inference_lora_t2v.py`：

```python
import torch
from diffsynth import ModelManager, WanVideoPipeline, save_video, VideoData

def main():
    # 加载模型管理器
    model_manager = ModelManager(torch_dtype=torch.bfloat16, device="cpu")

    # 加载基础模型组件
    model_manager.load_models([
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00001-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00002-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00003-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00004-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00005-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00006-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/models_t5_umt5-xxl-enc-bf16.pth",
        "models/Wan-AI/Wan2.1-T2V-14B/Wan2.1_VAE.pth",
    ])

    # 加载LoRA权重
    model_manager.load_lora("models/lightning_logs/version_1/checkpoints/epoch=0-step=500.ckpt", lora_alpha=1.0)

    # 创建推理管道
    pipe = WanVideoPipeline.from_model_manager(model_manager, device="cuda")
    pipe.enable_vram_management(num_persistent_param_in_dit=None)

    # 生成视频
    video = pipe(
        prompt="A beautiful sunset over the ocean with waves gently crashing on the shore",
        negative_prompt="blurry, low quality, distorted",
        num_inference_steps=50,
        seed=0,
        tiled=True
    )

    # 保存视频
    save_video(video, "output_lora_video.mp4", fps=30, quality=5)
    print("LoRA微调模型推理完成，视频已保存")

if __name__ == "__main__":
    main()
```

### 2. 全量微调模型推理脚本

创建 `inference_full_t2v.py`：

```python
import torch
from diffsynth import ModelManager, WanVideoPipeline, save_video, VideoData

def main():
    # 加载模型管理器
    model_manager = ModelManager(torch_dtype=torch.bfloat16, device="cpu")

    # 加载微调后的模型
    model_manager.load_models([
        "models/lightning_logs/version_1/checkpoints/epoch=0-step=500.ckpt",  # 微调后的DiT模型
        "models/Wan-AI/Wan2.1-T2V-14B/models_t5_umt5-xxl-enc-bf16.pth",     # 文本编码器
        "models/Wan-AI/Wan2.1-T2V-14B/Wan2.1_VAE.pth",                      # VAE
    ])

    # 创建推理管道
    pipe = WanVideoPipeline.from_model_manager(model_manager, device="cuda")
    pipe.enable_vram_management(num_persistent_param_in_dit=None)

    # 生成视频
    video = pipe(
        prompt="A beautiful sunset over the ocean with waves gently crashing on the shore",
        negative_prompt="blurry, low quality, distorted",
        num_inference_steps=50,
        seed=0,
        tiled=True
    )

    # 保存视频
    save_video(video, "output_full_video.mp4", fps=30, quality=5)
    print("全量微调模型推理完成，视频已保存")

if __name__ == "__main__":
    main()
```

### 3. 推理启动脚本

创建 `inference_t2v.sh`：

```bash
#!/bin/bash

echo "开始LoRA模型推理..."
python inference_lora_t2v.py

echo "开始全量微调模型推理..."
python inference_full_t2v.py

echo "推理完成！"
```

### 4. 批量推理脚本

创建 `batch_inference_t2v.py`：

```python
import torch
from diffsynth import ModelManager, WanVideoPipeline, save_video
import os

def batch_inference():
    # 提示词列表
    prompts = [
        "A beautiful sunset over the ocean with waves gently crashing on the shore",
        "A cat playing with a ball of yarn in a cozy living room",
        "A serene mountain landscape with snow-capped peaks and a clear blue sky",
        "A bustling city street at night with neon lights and busy traffic",
        "A peaceful forest scene with sunlight filtering through the trees"
    ]

    # 加载模型
    model_manager = ModelManager(torch_dtype=torch.bfloat16, device="cpu")
    model_manager.load_models([
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00001-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00002-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00003-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00004-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00005-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/diffusion_pytorch_model-00006-of-00006.safetensors",
        "models/Wan-AI/Wan2.1-T2V-14B/models_t5_umt5-xxl-enc-bf16.pth",
        "models/Wan-AI/Wan2.1-T2V-14B/Wan2.1_VAE.pth",
    ])

    # 加载LoRA权重（如果有的话）
    try:
        model_manager.load_lora("models/lightning_logs/version_1/checkpoints/epoch=0-step=500.ckpt", lora_alpha=1.0)
        print("已加载LoRA权重")
    except:
        print("未找到LoRA权重，使用基础模型")

    # 创建推理管道
    pipe = WanVideoPipeline.from_model_manager(model_manager, device="cuda")
    pipe.enable_vram_management(num_persistent_param_in_dit=None)

    # 创建输出目录
    os.makedirs("batch_outputs", exist_ok=True)

    # 批量生成视频
    for i, prompt in enumerate(prompts):
        print(f"正在生成第 {i+1}/{len(prompts)} 个视频...")
        print(f"提示词: {prompt}")

        video = pipe(
            prompt=prompt,
            negative_prompt="blurry, low quality, distorted",
            num_inference_steps=50,
            seed=i,  # 使用不同的种子
            tiled=True
        )

        output_path = f"batch_outputs/video_{i+1:03d}.mp4"
        save_video(video, output_path, fps=30, quality=5)
        print(f"视频已保存到: {output_path}")

    print("批量推理完成！")

if __name__ == "__main__":
    batch_inference()
```

## 模型合并代码

### 1. 模型合并脚本 `merge_model.py`

```python
import torch
import argparse
import os
from pathlib import Path
from diffsynth import ModelManager
import shutil

def merge_lora_weights(base_model_path, lora_path, output_path, alpha=1.0):
    """合并LoRA权重到基础模型"""
    print("Loading base model...")
    model_manager = ModelManager(torch_dtype=torch.bfloat16, device="cpu")
    model_manager.load_model("Wan-AI/Wan2.1-T2V-14B", model_path=base_model_path)
    
    # 加载LoRA权重
    if Path(lora_path).exists():
        print(f"Loading LoRA weights from {lora_path}")
        # 这里需要实现LoRA权重加载和合并逻辑
        # lora_weights = torch.load(lora_path, map_location="cpu")
        # 合并权重到基础模型
    
    # 保存合并后的模型
    print(f"Saving merged model to {output_path}")
    os.makedirs(output_path, exist_ok=True)
    
    # 复制配置文件
    config_files = ["config.json", "model_index.json"]
    for config_file in config_files:
        src_path = Path(base_model_path) / config_file
        dst_path = Path(output_path) / config_file
        if src_path.exists():
            shutil.copy2(src_path, dst_path)
    
    # 保存模型权重
    # model_manager.save_model(output_path)
    
    print("Model merging completed!")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--base_model_path", type=str, default="./models/Wan2.1-T2V-14B")
    parser.add_argument("--lora_path", type=str, default="./output/wan_t2v_finetune/final_model")
    parser.add_argument("--output_path", type=str, default="./models/Wan2.1-T2V-14B-merged")
    parser.add_argument("--alpha", type=float, default=1.0, help="LoRA merge alpha")
    args = parser.parse_args()
    
    merge_lora_weights(
        base_model_path=args.base_model_path,
        lora_path=args.lora_path,
        output_path=args.output_path,
        alpha=args.alpha
    )

if __name__ == "__main__":
    main()
```

### 2. 合并启动脚本 `merge.sh`

```bash
#!/bin/bash

python merge_model.py \
    --base_model_path ./models/Wan2.1-T2V-14B \
    --lora_path ./output/wan_t2v_finetune/final_model \
    --output_path ./models/Wan2.1-T2V-14B-merged \
    --alpha 1.0
```

## 训练监控和调试

### 1. 使用TensorBoard监控训练

```bash
# 启动TensorBoard
tensorboard --logdir ./output/wan_t2v_finetune --port 6006
```

### 2. 使用Wandb监控训练

在训练脚本中添加wandb支持：

```python
import wandb

# 初始化wandb
wandb.init(project="wan-t2v-finetune", config=args)

# 在训练循环中记录指标
wandb.log({"loss": loss.item(), "lr": lr_scheduler.get_last_lr()[0]})
```

## 常见问题和解决方案

### 1. 显存不足问题

- 减少batch_size
- 增加gradient_accumulation_steps
- 使用gradient_checkpointing
- 使用DeepSpeed ZeRO优化

### 2. 训练不稳定问题

- 调整学习率
- 使用warmup
- 检查数据质量
- 调整混合精度设置

### 3. 多卡同步问题

- 检查accelerate配置
- 确保所有GPU可见
- 检查网络连接

## 性能优化建议

1. **数据加载优化**：使用多进程数据加载，设置合适的num_workers
2. **内存优化**：使用gradient_checkpointing减少显存占用
3. **计算优化**：使用xformers加速attention计算
4. **存储优化**：使用SSD存储数据集，减少I/O瓶颈

## 总结

本文档提供了Wan-AI/Wan2.1-T2V-14B模型多卡微调的完整流程，包括环境搭建、数据准备、训练配置、推理和模型合并。请根据实际需求调整相关参数和配置。

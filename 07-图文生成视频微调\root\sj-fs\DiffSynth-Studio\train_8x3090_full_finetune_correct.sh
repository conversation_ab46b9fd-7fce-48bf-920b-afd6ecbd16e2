#!/bin/bash

# 8×RTX 3090正确的全量微调脚本
# 指定可训练模型，不使用LoRA

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export TOKENIZERS_PARALLELISM=false

echo "🚀 开始8×RTX 3090正确的全量微调..."
echo "⚠️  这是真正的全量微调，训练DiT模型的所有参数"
echo "🔧 全量微调配置:"
echo "   - 可训练模型: wan_video_dit (DiT模型)"
echo "   - 分辨率: 128×224 (小分辨率)"
echo "   - 梯度累积: 32步"
echo "   - 学习率: 5e-7 (极小)"
echo "   - 数据重复: 3次"

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 128 \
    --width 224 \
    --dataset_repeat 3 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --trainable_models "dit" \
    --learning_rate 5e-7 \
    --num_epochs 1 \
    --gradient_accumulation_steps 32 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/8x3090_full_finetune_correct" \
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090正确的全量微调完成"

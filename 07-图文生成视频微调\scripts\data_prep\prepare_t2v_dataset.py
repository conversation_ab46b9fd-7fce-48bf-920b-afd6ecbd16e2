import os
import csv
import cv2
from pathlib import Path

def create_t2v_metadata_csv(dataset_path, video_folder="videos"):
    """创建T2V任务的metadata.csv文件"""
    dataset_path = Path(dataset_path)
    video_folder_path = dataset_path / video_folder
    
    metadata = []
    
    # 遍历视频文件
    for video_file in video_folder_path.glob("*.mp4"):
        metadata.append({
            "video_path": f"{video_folder}/{video_file.name}",
            "text": f"视频描述：{video_file.stem}"
        })
    
    # 写入CSV文件
    metadata_file = dataset_path / "metadata.csv"
    with open(metadata_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['video_path', 'text']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for item in metadata:
            writer.writerow(item)
    
    print(f"创建T2V metadata.csv完成，共 {len(metadata)} 个视频样本")

if __name__ == "__main__":
    create_t2v_metadata_csv("./data/example_video_dataset")

#!/bin/bash

# 8×RTX 3090高分辨率LoRA训练脚本
# 分辨率: 480×832, 适合高质量视频生成

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=INFO
export NCCL_TREE_THRESHOLD=0

echo "🚀 开始8×RTX 3090高分辨率LoRA训练..."

accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 480 \
    --width 832 \
    --dataset_repeat 50 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --learning_rate 1e-4 \
    --num_epochs 3 \
    --gradient_accumulation_steps 2 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/8x3090_high_res" \
    --lora_base_model "dit" \
    --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
    --lora_rank 64 \
    --use_gradient_checkpointing_offload

echo "✅ 8×RTX 3090高分辨率训练完成"

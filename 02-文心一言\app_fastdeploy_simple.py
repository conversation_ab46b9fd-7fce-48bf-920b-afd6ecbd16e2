import gradio as gr
from fastdeploy import LLM, SamplingParams
import logging
import time

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
llm = None
model_loaded = False

def load_model():
    """加载FastDeploy ERNIE模型"""
    global llm, model_loaded
    
    try:
        logger.info("正在加载ERNIE-4.5-0.3B-Paddle模型...")
        start_time = time.time()
        
        llm = LLM(
            model="baidu/ERNIE-4.5-0.3B-Paddle",
            max_model_len=32768
        )
        
        load_time = time.time() - start_time
        model_loaded = True
        
        logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")
        return f"✅ 模型加载成功！耗时: {load_time:.2f}秒"
        
    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        model_loaded = False
        return f"❌ 模型加载失败: {str(e)}"

def generate_text(prompt, temperature, top_p, max_tokens):
    """生成文本"""
    global llm, model_loaded
    
    if not model_loaded:
        return "❌ 请先加载模型！"
    
    if not prompt.strip():
        return "❌ 请输入有效的问题！"
    
    try:
        # 设置采样参数
        sampling_params = SamplingParams(
            temperature=temperature,
            top_p=top_p,
            max_tokens=max_tokens
        )
        
        logger.info(f"生成回复中...")
        start_time = time.time()
        
        # 生成回复
        outputs = llm.generate(prompt, sampling_params)
        
        generation_time = time.time() - start_time
        
        if outputs and len(outputs) > 0:
            # FastDeploy的正确格式: outputs[0].outputs.text (不是outputs[0].outputs[0].text)
            try:
                response = outputs[0].outputs.text.strip()
                logger.info(f"生成完成，耗时: {generation_time:.2f}秒")
                return response
            except (AttributeError, TypeError) as e:
                logger.error(f"文本提取失败: {e}")
                return f"❌ 文本提取失败: {e}"
        else:
            return "❌ 模型没有生成有效回复"
            
    except Exception as e:
        logger.error(f"生成失败: {str(e)}")
        return f"❌ 生成失败: {str(e)}"

def chat_fn(message, history, temperature, top_p, max_tokens):
    """聊天函数"""
    if not message.strip():
        return history, ""

    response = generate_text(message, temperature, top_p, max_tokens)
    # 使用新的messages格式
    history.append({"role": "user", "content": message})
    history.append({"role": "assistant", "content": response})
    return history, ""

# 创建Gradio界面
with gr.Blocks(title="FastDeploy ERNIE 聊天机器人") as demo:
    
    gr.HTML("""
    <div style="text-align: center; margin-bottom: 20px;">
        <h1>🚀 FastDeploy ERNIE-4.5 聊天机器人</h1>
        <p>基于FastDeploy和ERNIE-4.5-0.3B-Paddle的高性能对话系统</p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=3):
            # 模型加载
            with gr.Group():
                gr.Markdown("### 🔧 模型管理")
                load_btn = gr.Button("🔄 加载模型", variant="primary")
                status_text = gr.Textbox(
                    label="状态",
                    value="⏳ 点击'加载模型'开始",
                    interactive=False
                )
            
            # 聊天区域
            chatbot = gr.Chatbot(
                label="💬 对话",
                height=400,
                show_copy_button=True,
                type="messages"
            )
            
            with gr.Row():
                msg = gr.Textbox(
                    label="输入消息",
                    placeholder="请输入您的问题...",
                    lines=2,
                    scale=4
                )
                send_btn = gr.Button("📤 发送", variant="primary", scale=1)
            
            clear_btn = gr.Button("🗑️ 清空对话")
        
        with gr.Column(scale=1):
            gr.Markdown("### ⚙️ 参数设置")
            
            temperature = gr.Slider(
                minimum=0.1,
                maximum=2.0,
                value=0.8,
                step=0.1,
                label="温度"
            )
            
            top_p = gr.Slider(
                minimum=0.1,
                maximum=1.0,
                value=0.95,
                step=0.05,
                label="Top-p"
            )
            
            max_tokens = gr.Slider(
                minimum=50,
                maximum=1024,
                value=512,
                step=50,
                label="最大长度"
            )
            
            gr.Markdown("""
            ### 📋 模型信息
            - **模型**: ERNIE-4.5-0.3B-Paddle
            - **框架**: FastDeploy
            - **最大长度**: 32,768 tokens
            
            ### 💡 参数说明
            - **温度**: 控制创造性 (0.1-2.0)
            - **Top-p**: 控制多样性 (0.1-1.0)
            - **最大长度**: 回复最大字数
            """)
    
    # 示例问题
    gr.Examples(
        examples=[
            ["写一首关于大语言模型的诗"],
            ["解释什么是人工智能"],
            ["如何学习Python编程？"],
            ["介绍一下FastDeploy的优势"],
            ["写一个简单的Python函数"]
        ],
        inputs=msg
    )
    
    # 事件绑定
    load_btn.click(load_model, outputs=status_text)
    
    send_btn.click(
        chat_fn,
        inputs=[msg, chatbot, temperature, top_p, max_tokens],
        outputs=[chatbot, msg]
    )
    
    msg.submit(
        chat_fn,
        inputs=[msg, chatbot, temperature, top_p, max_tokens],
        outputs=[chatbot, msg]
    )
    
    clear_btn.click(lambda: [], outputs=chatbot)

if __name__ == "__main__":
    print("🚀 启动FastDeploy ERNIE聊天机器人...")
    print("📍 访问地址: http://localhost:7860")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False
    )

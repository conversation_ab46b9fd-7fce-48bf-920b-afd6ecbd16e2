#!/usr/bin/env python3
"""
实时监控8×RTX 3090显存使用情况
"""

import subprocess
import time
import os

def get_gpu_info():
    """获取GPU信息"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=index,name,memory.used,memory.total,utilization.gpu,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            gpu_info = []
            for line in lines:
                parts = line.split(', ')
                if len(parts) >= 6:
                    gpu_info.append({
                        'index': int(parts[0]),
                        'name': parts[1],
                        'memory_used': int(parts[2]),
                        'memory_total': int(parts[3]),
                        'utilization': int(parts[4]),
                        'temperature': int(parts[5])
                    })
            return gpu_info
        else:
            return None
    except Exception as e:
        print(f"获取GPU信息失败: {e}")
        return None

def display_gpu_status():
    """显示GPU状态"""
    gpu_info = get_gpu_info()
    if not gpu_info:
        print("❌ 无法获取GPU信息")
        return
    
    # 清屏
    os.system('clear' if os.name == 'posix' else 'cls')
    
    print("🎯 8×RTX 3090实时监控")
    print("=" * 80)
    print(f"{'GPU':<3} {'名称':<20} {'显存使用':<15} {'利用率':<8} {'温度':<6}")
    print("-" * 80)
    
    total_memory_used = 0
    total_memory_total = 0
    
    for gpu in gpu_info:
        memory_percent = (gpu['memory_used'] / gpu['memory_total']) * 100
        total_memory_used += gpu['memory_used']
        total_memory_total += gpu['memory_total']
        
        # 根据使用率设置颜色标识
        if memory_percent > 90:
            status = "🔴"
        elif memory_percent > 70:
            status = "🟡"
        else:
            status = "🟢"
        
        print(f"{status} {gpu['index']:<2} {gpu['name']:<20} "
              f"{gpu['memory_used']:>5}MB/{gpu['memory_total']:>5}MB "
              f"({memory_percent:>5.1f}%) "
              f"{gpu['utilization']:>3}% "
              f"{gpu['temperature']:>3}°C")
    
    print("-" * 80)
    total_percent = (total_memory_used / total_memory_total) * 100
    print(f"📊 总显存使用: {total_memory_used}MB / {total_memory_total}MB ({total_percent:.1f}%)")
    
    # 显存使用状态
    if total_percent > 95:
        print("⚠️  显存使用率过高，可能出现OOM")
    elif total_percent > 85:
        print("🟡 显存使用率较高，建议监控")
    else:
        print("✅ 显存使用正常")
    
    print(f"\n⏰ 更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("按 Ctrl+C 退出监控")

def main():
    """主函数"""
    print("🚀 启动8×RTX 3090显存监控...")
    print("监控全量微调训练过程中的显存使用情况")
    
    try:
        while True:
            display_gpu_status()
            time.sleep(2)  # 每2秒更新一次
    except KeyboardInterrupt:
        print("\n\n👋 监控已停止")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
精准厂商爬虫 - 专门爬取有完整厂商信息的决标公告
"""

from enhanced_procurement_crawler import EnhancedProcurementCrawler
import json
import time
from datetime import datetime

def create_complete_data_with_vendors():
    """使用我们已知的有厂商信息的URL创建完整数据"""
    
    print("🎯 === 使用已知有厂商信息的URL创建完整数据 ===")
    
    # 我们知道这个URL有完整的厂商信息
    test_url = "https://web.pcc.gov.tw/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetail?pkAtmMain=NzAxNTM4ODk="
    
    crawler = EnhancedProcurementCrawler()
    
    try:
        # 提取详情页数据
        detail_info = crawler.extract_detail_fields(test_url)
        
        if detail_info:
            print("✅ 成功提取详情页数据")
            
            # 构建完整记录
            complete_record = {
                '序号': 1,
                '标案名称': '庫儲人力管理',
                '机关名称': '國防部',
                '标案类型': '決標公告',
                '招标日期': '111/11/02',
                '决标日期': '112/06/16',
                '详情页链接': test_url,
                '爬取时间': datetime.now().isoformat(),
                
                # 机关详细信息
                '标案案号': detail_info.announcement_info.case_number,
                '机关代码': detail_info.agency_info.agency_code,
                '单位名称': detail_info.agency_info.unit_name,
                '机关地址': detail_info.agency_info.agency_address,
                '联络人': detail_info.agency_info.contact_person,
                '联络电话': detail_info.agency_info.contact_phone,
                '传真号码': detail_info.agency_info.fax_number,
                
                # 招标信息
                '招标方式': detail_info.announcement_info.tender_method,
                '决标方式': detail_info.announcement_info.award_method,
                '公告日期': detail_info.announcement_info.announcement_date,
                
                # 时间信息
                '开标时间': detail_info.time_info.opening_time,
                '原公告日期': detail_info.time_info.original_announcement_date,
                
                # 金额信息
                '预算金额': detail_info.amount_info.budget_amount,
                '预算金额中文': detail_info.amount_info.budget_amount_chinese,
                '采购金额级距': detail_info.amount_info.procurement_amount_range,
                '预算金额是否公开': detail_info.amount_info.budget_public,
                
                # 履约信息
                '履约地点': detail_info.performance_info.performance_location,
                '履约地区': detail_info.performance_info.performance_region,
                '是否受机关补助': detail_info.performance_info.government_subsidy,
                
                # 厂商信息
                '投标厂商数': detail_info.bidder_count,
                '厂商详情': [],
                '得标厂商': '',
                '得标金额': '',
                
                # 标的分类
                '标的分类': detail_info.subject_classification.classification_name
            }
            
            # 处理厂商详情
            if detail_info.vendors:
                vendor_details = []
                winner_vendor = ''
                winner_amount = ''
                
                for vendor in detail_info.vendors:
                    vendor_detail = {
                        '厂商名称': vendor.vendor_name,
                        '厂商代码': vendor.vendor_code,
                        '是否得标': vendor.is_winner,
                        '决标金额': vendor.award_amount,
                        '组织型态': vendor.organization_type,
                        '厂商地址': vendor.vendor_address,
                        '厂商电话': vendor.vendor_phone,
                        '履约期间': vendor.performance_period,
                        '是否为中小企业': vendor.is_sme,
                        '得标厂商国别': vendor.winner_country,
                        '雇用员工总人数是否超过100人': vendor.over_100_employees
                    }
                    vendor_details.append(vendor_detail)
                    
                    # 找到得标厂商
                    if vendor.is_winner == '是':
                        winner_vendor = vendor.vendor_name
                        winner_amount = vendor.award_amount
                
                complete_record['厂商详情'] = vendor_details
                complete_record['得标厂商'] = winner_vendor
                complete_record['得标金额'] = winner_amount
                
                print(f"🏆 找到 {len(vendor_details)} 个厂商")
                print(f"🥇 得标厂商: {winner_vendor}")
                print(f"💰 得标金额: {winner_amount}")
            
            # 保存完整数据
            complete_data = [complete_record]
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f'complete_vendor_data_{timestamp}.json'
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(complete_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 完整数据已保存到: {output_file}")
            
            # 生成CSV版本
            generate_csv_from_complete_data(complete_data, output_file.replace('.json', '.csv'))
            
            # 生成厂商详情CSV
            generate_vendor_detail_csv(complete_data, output_file.replace('.json', '_vendors.csv'))
            
            return complete_data, output_file
            
        else:
            print("❌ 无法提取详情页数据")
            return [], ""
            
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")
        return [], ""

def generate_csv_from_complete_data(data, csv_file):
    """从完整数据生成CSV"""
    try:
        import csv
        
        if not data:
            return
        
        # 排除复杂字段
        simple_fields = []
        for key in data[0].keys():
            if key not in ['厂商详情']:
                simple_fields.append(key)
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=simple_fields)
            writer.writeheader()
            
            for item in data:
                simple_item = {}
                for field in simple_fields:
                    simple_item[field] = item.get(field, '')
                writer.writerow(simple_item)
        
        print(f"📊 CSV文件已生成: {csv_file}")
        
    except Exception as e:
        print(f"⚠️ CSV生成失败: {str(e)}")

def generate_vendor_detail_csv(data, csv_file):
    """生成厂商详情CSV"""
    try:
        import csv
        
        vendor_records = []
        
        for item in data:
            vendors = item.get('厂商详情', [])
            if vendors:
                for vendor in vendors:
                    vendor_record = {
                        '序号': item.get('序号', ''),
                        '标案名称': item.get('标案名称', ''),
                        '标案案号': item.get('标案案号', ''),
                        '机关名称': item.get('机关名称', ''),
                        '厂商名称': vendor.get('厂商名称', ''),
                        '厂商代码': vendor.get('厂商代码', ''),
                        '是否得标': vendor.get('是否得标', ''),
                        '决标金额': vendor.get('决标金额', ''),
                        '组织型态': vendor.get('组织型态', ''),
                        '厂商地址': vendor.get('厂商地址', ''),
                        '厂商电话': vendor.get('厂商电话', ''),
                        '履约期间': vendor.get('履约期间', ''),
                        '是否为中小企业': vendor.get('是否为中小企业', ''),
                        '得标厂商国别': vendor.get('得标厂商国别', ''),
                        '雇用员工总人数是否超过100人': vendor.get('雇用员工总人数是否超过100人', '')
                    }
                    vendor_records.append(vendor_record)
        
        if vendor_records:
            with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                fieldnames = list(vendor_records[0].keys())
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(vendor_records)
            
            print(f"🏢 厂商详情CSV已生成: {csv_file}")
            print(f"📊 厂商记录数: {len(vendor_records)}")
        
    except Exception as e:
        print(f"⚠️ 厂商CSV生成失败: {str(e)}")

def create_sample_data_structure():
    """创建示例数据结构，展示完整的字段"""
    
    print("📋 === 创建示例数据结构 ===")
    
    sample_data = {
        "说明": "这是完整的政府采购数据结构示例",
        "数据来源": "台湾政府采购网",
        "字段说明": {
            "基本信息": ["序号", "标案名称", "机关名称", "标案类型", "招标日期", "决标日期"],
            "机关信息": ["标案案号", "机关代码", "单位名称", "机关地址", "联络人", "联络电话", "传真号码"],
            "招标信息": ["招标方式", "决标方式", "公告日期", "开标时间", "原公告日期"],
            "金额信息": ["预算金额", "预算金额中文", "采购金额级距", "预算金额是否公开"],
            "履约信息": ["履约地点", "履约地区", "是否受机关补助"],
            "厂商信息": ["投标厂商数", "厂商详情", "得标厂商", "得标金额"],
            "其他信息": ["标的分类", "详情页链接", "爬取时间"]
        },
        "厂商详情字段": [
            "厂商名称", "厂商代码", "是否得标", "决标金额", "组织型态",
            "厂商地址", "厂商电话", "履约期间", "是否为中小企业", 
            "得标厂商国别", "雇用员工总人数是否超过100人"
        ],
        "示例数据": {
            "序号": 1,
            "标案名称": "庫儲人力管理",
            "机关名称": "國防部",
            "得标厂商": "鴻吉管理顧問股份有限公司",
            "得标金额": "18,178,000元",
            "投标厂商数": 3,
            "厂商详情": [
                {
                    "厂商名称": "鴻吉管理顧問股份有限公司",
                    "厂商代码": "12938805",
                    "是否得标": "是",
                    "决标金额": "18,178,000元"
                }
            ]
        }
    }
    
    with open('data_structure_sample.json', 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print("📄 示例数据结构已保存到: data_structure_sample.json")

def main():
    """主函数"""
    print("🎯 === 精准厂商爬虫 ===")
    print("专门获取有完整厂商信息的决标公告数据")
    
    # 创建示例数据结构
    create_sample_data_structure()
    
    # 使用已知的有厂商信息的URL创建完整数据
    complete_data, output_file = create_complete_data_with_vendors()
    
    if complete_data:
        print(f"\n🎉 === 数据创建成功 ===")
        print(f"✅ 成功创建 {len(complete_data)} 笔完整数据")
        print(f"💾 主数据文件: {output_file}")
        print(f"📊 CSV文件: {output_file.replace('.json', '.csv')}")
        print(f"🏢 厂商详情CSV: {output_file.replace('.json', '_vendors.csv')}")
        
        # 显示数据摘要
        item = complete_data[0]
        print(f"\n📋 数据摘要:")
        print(f"  标案名称: {item.get('标案名称', '')}")
        print(f"  机关名称: {item.get('机关名称', '')}")
        print(f"  预算金额: {item.get('预算金额', '')}")
        print(f"  得标厂商: {item.get('得标厂商', '')}")
        print(f"  得标金额: {item.get('得标金额', '')}")
        print(f"  厂商数量: {len(item.get('厂商详情', []))}")
        
    else:
        print("❌ 数据创建失败")
    
    print(f"\n💡 说明:")
    print(f"  - 这个脚本使用已知有完整厂商信息的URL")
    print(f"  - 生成的数据包含所有31个字段")
    print(f"  - 包含完整的厂商详情信息")
    print(f"  - 可以作为数据结构的参考模板")

if __name__ == "__main__":
    main()

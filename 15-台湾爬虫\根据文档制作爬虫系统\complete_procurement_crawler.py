#!/usr/bin/env python3
"""
完整的政府採購爬蟲 - 支持自定義搜索條件和批量爬取
"""

from enhanced_procurement_crawler import EnhancedProcurementCrawler
import json
import time
from datetime import datetime

class CompleteProcurementCrawler:
    """完整的採購爬蟲類"""
    
    def __init__(self):
        self.crawler = EnhancedProcurementCrawler()
        self.results = []
    
    def crawl_procurement_data(self, 
                             keyword="國防部", 
                             tender_status="決標",
                             year="111",
                             page_size=10,
                             max_pages=5,
                             max_details=10,
                             enable_fuzzy=True,
                             enable_phonetic=True):
        """
        爬取採購數據
        
        Args:
            keyword: 搜索關鍵詞
            tender_status: 標案狀態 (招標/決標/公開閱覽及公開徵求/政府採購預告)
            year: 年份 (111/112/113/114等)
            page_size: 每頁筆數
            max_pages: 最大頁數
            max_details: 最大詳細頁爬取數量
            enable_fuzzy: 是否啟用容錯查詢
            enable_phonetic: 是否啟用同音查詢
        """
        
        print(f"開始爬取採購數據...")
        print(f"搜索條件：關鍵詞='{keyword}', 狀態='{tender_status}', 年份={year}年")
        
        # 設置搜索參數
        search_params = {
            'querySentence': keyword,
            'tenderStatusType': tender_status,
            'sortCol': 'AWARD_NOTICE_DATE',
            'timeRange': year,
            'pron': 'true' if enable_phonetic else 'false',
            'fuzzy': 'true' if enable_fuzzy else 'false',
            'pageSize': str(page_size)
        }
        
        all_list_results = []
        
        # 第一步：爬取所有列表頁數據
        print(f"\n=== 第一步：爬取列表頁數據 ===")
        
        for page in range(1, max_pages + 1):
            print(f"\n正在爬取第 {page} 頁...")
            
            # 設置頁碼
            search_params['pageIndex'] = str(page)
            
            try:
                # 發送搜索請求
                search_response = self.crawler._make_request(
                    self.crawler.search_action_url, 
                    method='GET', 
                    data=search_params
                )
                
                # 解析列表頁結果
                page_results = self.crawler._parse_search_results(search_response.content)
                
                if not page_results:
                    print(f"第 {page} 頁沒有數據，停止爬取")
                    break
                
                print(f"第 {page} 頁獲取到 {len(page_results)} 筆數據")
                all_list_results.extend(page_results)
                
                # 添加延遲
                time.sleep(2)
                
            except Exception as e:
                print(f"爬取第 {page} 頁時發生錯誤: {str(e)}")
                break
        
        print(f"\n列表頁爬取完成，共獲取 {len(all_list_results)} 筆數據")
        
        # 第二步：爬取詳細頁數據
        print(f"\n=== 第二步：爬取詳細頁數據 ===")
        print(f"將爬取前 {min(max_details, len(all_list_results))} 筆的詳細數據")
        
        detailed_results = []
        
        for i, result in enumerate(all_list_results[:max_details]):
            print(f"\n正在爬取第 {i+1}/{min(max_details, len(all_list_results))} 筆詳細數據...")
            print(f"標案名稱: {result.get('title', '')}")
            print(f"機關名稱: {result.get('agency', '')}")
            
            if result.get('detail_url'):
                try:
                    # 獲取詳細頁面數據
                    detail_info = self.crawler.extract_detail_fields(result['detail_url'])
                    
                    if detail_info:
                        # 合併數據
                        combined_result = {
                            'crawl_time': datetime.now().isoformat(),
                            'list_data': result,
                            'detail_data': {
                                'agency_info': detail_info.agency_info.__dict__,
                                'announcement_info': detail_info.announcement_info.__dict__,
                                'time_info': detail_info.time_info.__dict__,
                                'amount_info': detail_info.amount_info.__dict__,
                                'procurement_nature': detail_info.procurement_nature.__dict__,
                                'performance_info': detail_info.performance_info.__dict__,
                                'vendors': [vendor.__dict__ for vendor in detail_info.vendors],
                                'subject_classification': detail_info.subject_classification.__dict__,
                                'bidder_count': detail_info.bidder_count
                            }
                        }
                        
                        detailed_results.append(combined_result)
                        print(f"  ✓ 成功獲取詳細數據")
                        
                        # 顯示關鍵信息
                        if detail_info.agency_info.agency_name:
                            print(f"  機關: {detail_info.agency_info.agency_name}")
                        if detail_info.announcement_info.case_number:
                            print(f"  案號: {detail_info.announcement_info.case_number}")
                        if detail_info.amount_info.budget_amount:
                            print(f"  預算: {detail_info.amount_info.budget_amount}")
                        if detail_info.vendors:
                            print(f"  得標廠商: {len(detail_info.vendors)} 家")
                    else:
                        print(f"  ✗ 無法解析詳細數據")
                        
                except Exception as e:
                    print(f"  ✗ 獲取詳細數據時發生錯誤: {str(e)}")
            else:
                print(f"  ✗ 沒有詳細頁連結")
            
            # 添加延遲避免被封
            time.sleep(3)
        
        # 保存結果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f'procurement_data_{keyword}_{year}年_{timestamp}.json'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2)
        
        # 統計信息
        print(f"\n=== 爬取完成 ===")
        print(f"搜索關鍵詞: {keyword}")
        print(f"年份範圍: {year}年")
        print(f"列表頁數據: {len(all_list_results)} 筆")
        print(f"詳細頁數據: {len(detailed_results)} 筆")
        print(f"數據已保存到: {output_file}")
        
        # 保存統計信息
        stats = {
            'crawl_time': datetime.now().isoformat(),
            'search_params': search_params,
            'total_list_results': len(all_list_results),
            'total_detail_results': len(detailed_results),
            'success_rate': f"{len(detailed_results)/min(max_details, len(all_list_results))*100:.1f}%" if all_list_results else "0%"
        }
        
        stats_file = f'crawl_stats_{timestamp}.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"統計信息已保存到: {stats_file}")
        
        return detailed_results

def main():
    """主函數 - 可以自定義搜索條件"""
    crawler = CompleteProcurementCrawler()
    
    # 示例1：爬取國防部相關採購
    print("=== 示例1：國防部採購數據 ===")
    results1 = crawler.crawl_procurement_data(
        keyword="國防部",
        tender_status="決標", 
        year="111",
        page_size=10,
        max_pages=2,
        max_details=5
    )
    
    # 示例2：爬取教育相關採購
    print("\n\n=== 示例2：教育相關採購數據 ===")
    results2 = crawler.crawl_procurement_data(
        keyword="教育",
        tender_status="決標",
        year="114", 
        page_size=10,
        max_pages=1,
        max_details=3
    )
    
    print(f"\n總共爬取了 {len(results1) + len(results2)} 筆詳細數據")

if __name__ == "__main__":
    main()

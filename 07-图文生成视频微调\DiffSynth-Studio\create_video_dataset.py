#!/usr/bin/env python3
"""
创建视频数据集工具
为Wan2.1-I2V-14B-480P微调生成真正的视频数据集
"""

import os
import json
import pandas as pd
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import cv2
from datetime import datetime
import math

class VideoDatasetCreator:
    def __init__(self, output_dir="data/custom_video_dataset"):
        self.output_dir = Path(output_dir)
        self.videos_dir = self.output_dir / "videos"
        self.images_dir = self.output_dir / "images"
        self.metadata_file = self.output_dir / "metadata.csv"
        
        # 创建目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.videos_dir.mkdir(exist_ok=True)
        self.images_dir.mkdir(exist_ok=True)
        
        # 数据记录
        self.dataset_records = []
        
        print(f"🎬 视频数据集创建器初始化")
        print(f"   输出目录: {self.output_dir}")
        print(f"   视频目录: {self.videos_dir}")
        print(f"   图像目录: {self.images_dir}")
    
    def create_animated_video(self, scene_config, duration=3.0, fps=15, output_path=None):
        """创建动画视频"""
        width, height = 832, 480
        total_frames = int(duration * fps)

        # 使用指定的输出路径或临时路径
        if output_path is None:
            output_path = f"/tmp/temp_video_{scene_config['name']}.mp4"

        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        print(f"   创建视频: {scene_config['name']} ({total_frames}帧, {fps}fps)")

        for frame_idx in range(total_frames):
            # 创建帧
            frame = self.create_animated_frame(scene_config, frame_idx, total_frames, width, height)

            # 转换PIL图像到OpenCV格式
            frame_cv = cv2.cvtColor(np.array(frame), cv2.COLOR_RGB2BGR)
            out.write(frame_cv)

        out.release()
        return output_path
    
    def create_animated_frame(self, scene_config, frame_idx, total_frames, width, height):
        """创建单个动画帧"""
        # 计算动画进度 (0.0 到 1.0)
        progress = frame_idx / total_frames
        
        # 创建基础图像
        img = Image.new('RGB', (width, height), color=scene_config['colors'][0])
        draw = ImageDraw.Draw(img)
        
        # 根据场景类型添加不同的动画效果
        if scene_config['name'] == 'ocean_sunset':
            self.animate_ocean_sunset(draw, progress, width, height, scene_config['colors'])
        elif scene_config['name'] == 'forest_morning':
            self.animate_forest_morning(draw, progress, width, height, scene_config['colors'])
        elif scene_config['name'] == 'mountain_landscape':
            self.animate_mountain_landscape(draw, progress, width, height, scene_config['colors'])
        elif scene_config['name'] == 'city_night':
            self.animate_city_night(draw, progress, width, height, scene_config['colors'])
        elif scene_config['name'] == 'flower_field':
            self.animate_flower_field(draw, progress, width, height, scene_config['colors'])
        elif scene_config['name'] == 'desert_dunes':
            self.animate_desert_dunes(draw, progress, width, height, scene_config['colors'])
        elif scene_config['name'] == 'waterfall_nature':
            self.animate_waterfall_nature(draw, progress, width, height, scene_config['colors'])
        elif scene_config['name'] == 'autumn_leaves':
            self.animate_autumn_leaves(draw, progress, width, height, scene_config['colors'])
        
        return img
    
    def animate_ocean_sunset(self, draw, progress, width, height, colors):
        """海洋日落动画 - 太阳下沉，波浪移动"""
        # 渐变背景
        for y in range(height):
            ratio = y / height
            r = int(colors[0][0] * (1-ratio) + colors[1][0] * ratio)
            g = int(colors[0][1] * (1-ratio) + colors[1][1] * ratio)
            b = int(colors[0][2] * (1-ratio) + colors[1][2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 移动的太阳
        sun_x = 650 + int(50 * math.sin(progress * math.pi))
        sun_y = 50 + int(30 * progress)
        draw.ellipse([sun_x, sun_y, sun_x+100, sun_y+100], fill=colors[2])
        
        # 波浪效果
        wave_offset = int(progress * 100) % 50
        for i in range(0, width + 50, 50):
            wave_x = i - wave_offset
            wave_y = height - 100 + int(20 * math.sin((wave_x + progress * 200) * 0.02))
            draw.ellipse([wave_x, wave_y, wave_x+30, wave_y+20], fill=(255, 255, 255, 100))
    
    def animate_forest_morning(self, draw, progress, width, height, colors):
        """森林晨光动画 - 阳光透射效果"""
        # 渐变背景
        for y in range(height):
            ratio = y / height
            r = int(colors[0][0] * (1-ratio) + colors[1][0] * ratio)
            g = int(colors[0][1] * (1-ratio) + colors[1][1] * ratio)
            b = int(colors[0][2] * (1-ratio) + colors[1][2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 移动的光线
        for i in range(3):
            light_x = 200 + i * 200 + int(30 * math.sin(progress * math.pi + i))
            light_alpha = int(100 + 50 * math.sin(progress * math.pi * 2 + i))
            # 简化的光线效果
            draw.line([(light_x, 0), (light_x + 50, height)], fill=colors[2], width=20)
    
    def animate_mountain_landscape(self, draw, progress, width, height, colors):
        """雪山风景动画 - 云朵移动"""
        # 渐变背景
        for y in range(height):
            ratio = y / height
            r = int(colors[0][0] * (1-ratio) + colors[1][0] * ratio)
            g = int(colors[0][1] * (1-ratio) + colors[1][1] * ratio)
            b = int(colors[0][2] * (1-ratio) + colors[1][2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 移动的云朵
        cloud_offset = int(progress * 200) % 300
        for i in range(3):
            cloud_x = i * 300 - cloud_offset
            cloud_y = 100 + i * 30
            draw.ellipse([cloud_x, cloud_y, cloud_x+150, cloud_y+50], fill=colors[1])
    
    def animate_city_night(self, draw, progress, width, height, colors):
        """城市夜景动画 - 闪烁的灯光"""
        # 渐变背景
        for y in range(height):
            ratio = y / height
            r = int(colors[0][0] * (1-ratio) + colors[1][0] * ratio)
            g = int(colors[0][1] * (1-ratio) + colors[1][1] * ratio)
            b = int(colors[0][2] * (1-ratio) + colors[1][2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 闪烁的窗户灯光
        for i in range(20):
            light_x = (i % 10) * 80 + 40
            light_y = (i // 10) * 100 + 200
            brightness = int(255 * (0.5 + 0.5 * math.sin(progress * math.pi * 4 + i)))
            draw.rectangle([light_x, light_y, light_x+20, light_y+30], fill=(brightness, brightness, 0))
    
    def animate_flower_field(self, draw, progress, width, height, colors):
        """花田春景动画 - 花朵摇摆"""
        # 渐变背景
        for y in range(height):
            ratio = y / height
            r = int(colors[0][0] * (1-ratio) + colors[1][0] * ratio)
            g = int(colors[0][1] * (1-ratio) + colors[1][1] * ratio)
            b = int(colors[0][2] * (1-ratio) + colors[1][2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 摇摆的花朵
        for i in range(15):
            flower_x = (i % 5) * 160 + 80 + int(20 * math.sin(progress * math.pi * 2 + i * 0.5))
            flower_y = (i // 5) * 120 + 200
            draw.ellipse([flower_x, flower_y, flower_x+30, flower_y+30], fill=colors[i % len(colors)])
    
    def animate_desert_dunes(self, draw, progress, width, height, colors):
        """沙漠沙丘动画 - 沙粒移动效果"""
        # 渐变背景
        for y in range(height):
            ratio = y / height
            r = int(colors[0][0] * (1-ratio) + colors[1][0] * ratio)
            g = int(colors[0][1] * (1-ratio) + colors[1][1] * ratio)
            b = int(colors[0][2] * (1-ratio) + colors[1][2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 移动的沙粒效果
        sand_offset = int(progress * 100) % 20
        for i in range(50):
            sand_x = (i * 17 + sand_offset) % width
            sand_y = 300 + (i * 13) % 100
            draw.ellipse([sand_x, sand_y, sand_x+3, sand_y+3], fill=colors[2])
    
    def animate_waterfall_nature(self, draw, progress, width, height, colors):
        """瀑布自然动画 - 水流下落"""
        # 渐变背景
        for y in range(height):
            ratio = y / height
            r = int(colors[0][0] * (1-ratio) + colors[1][0] * ratio)
            g = int(colors[0][1] * (1-ratio) + colors[1][1] * ratio)
            b = int(colors[0][2] * (1-ratio) + colors[1][2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 下落的水滴
        water_offset = int(progress * 200) % 50
        for i in range(10):
            drop_x = 400 + i * 5
            drop_y = (i * 47 + water_offset) % height
            draw.ellipse([drop_x, drop_y, drop_x+8, drop_y+15], fill=colors[2])
    
    def animate_autumn_leaves(self, draw, progress, width, height, colors):
        """秋叶飘落动画 - 叶子下落"""
        # 渐变背景
        for y in range(height):
            ratio = y / height
            r = int(colors[0][0] * (1-ratio) + colors[1][0] * ratio)
            g = int(colors[0][1] * (1-ratio) + colors[1][1] * ratio)
            b = int(colors[0][2] * (1-ratio) + colors[1][2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # 飘落的叶子
        leaf_offset = int(progress * 150) % 100
        for i in range(12):
            leaf_x = (i * 67 + int(30 * math.sin(progress * math.pi + i))) % width
            leaf_y = (i * 39 + leaf_offset) % height
            leaf_color = colors[i % len(colors)]
            draw.ellipse([leaf_x, leaf_y, leaf_x+15, leaf_y+20], fill=leaf_color)
    
    def create_video_dataset(self):
        """创建完整的视频数据集"""
        print("🎬 创建视频数据集...")
        
        # 定义场景配置
        scenes = [
            {
                'name': 'ocean_sunset',
                'prompt': 'A beautiful sunset over the ocean with gentle waves, warm golden light reflecting on water, cinematic lighting',
                'colors': [(255, 140, 0), (255, 165, 0), (255, 215, 0)],
                'description': '海洋日落场景'
            },
            {
                'name': 'forest_morning',
                'prompt': 'A peaceful forest in the morning with sunlight filtering through green leaves, natural lighting, serene atmosphere',
                'colors': [(34, 139, 34), (144, 238, 144), (255, 255, 224)],
                'description': '森林晨光场景'
            },
            {
                'name': 'mountain_landscape',
                'prompt': 'Majestic mountains with snow-capped peaks under a clear blue sky, dramatic landscape, high quality',
                'colors': [(135, 206, 235), (255, 255, 255), (200, 200, 200)],
                'description': '雪山风景场景'
            },
            {
                'name': 'city_night',
                'prompt': 'A vibrant city at night with colorful lights and bustling streets, urban atmosphere, neon lights',
                'colors': [(25, 25, 112), (255, 215, 0), (220, 20, 60)],
                'description': '城市夜景场景'
            },
            {
                'name': 'flower_field',
                'prompt': 'A field of colorful flowers swaying gently in the spring breeze, natural beauty, soft lighting',
                'colors': [(255, 192, 203), (147, 112, 219), (144, 238, 144)],
                'description': '花田春景场景'
            },
            {
                'name': 'desert_dunes',
                'prompt': 'Golden sand dunes in the desert with dramatic shadows and clear sky, vast landscape, warm tones',
                'colors': [(238, 203, 173), (255, 218, 185), (210, 180, 140)],
                'description': '沙漠沙丘场景'
            }
        ]
        
        for i, scene in enumerate(scenes):
            print(f"\n🎨 处理场景 {i+1}/{len(scenes)}: {scene['name']}")
            
            # 创建视频
            final_video_path = self.videos_dir / f"{scene['name']}_{i:03d}.mp4"
            temp_video_path = self.create_animated_video(scene, duration=3.0, fps=15, output_path=str(final_video_path))
            
            # 提取第一帧作为输入图像
            self.extract_first_frame(final_video_path, scene['name'], i)
            
            # 记录数据
            record = {
                'video': f"videos/{scene['name']}_{i:03d}.mp4",
                'image': f"images/{scene['name']}_{i:03d}.jpg",
                'prompt': scene['prompt']
            }
            self.dataset_records.append(record)
            
            print(f"   ✅ 视频: {final_video_path.name}")
            print(f"   ✅ 图像: {scene['name']}_{i:03d}.jpg")
        
        print(f"\n✅ 创建了 {len(scenes)} 个视频场景")
    
    def extract_first_frame(self, video_path, scene_name, index):
        """提取视频第一帧"""
        cap = cv2.VideoCapture(str(video_path))
        ret, frame = cap.read()
        if ret:
            # 转换BGR到RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            # 保存为PIL图像
            pil_image = Image.fromarray(frame_rgb)
            image_path = self.images_dir / f"{scene_name}_{index:03d}.jpg"
            pil_image.save(image_path, 'JPEG', quality=95)
        cap.release()
    
    def save_metadata(self):
        """保存元数据文件"""
        if not self.dataset_records:
            print("⚠️  没有数据记录，跳过保存")
            return
        
        # 保存CSV格式（用于训练）
        df = pd.DataFrame(self.dataset_records)
        df.to_csv(self.metadata_file, index=False)
        
        # 保存详细JSON格式
        json_file = self.output_dir / "metadata_full.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.dataset_records, f, ensure_ascii=False, indent=2)
        
        # 创建统计信息
        stats = {
            'total_samples': len(self.dataset_records),
            'video_resolution': '832x480',
            'video_fps': 15,
            'video_duration': 3.0,
            'scenes': [record['video'].split('/')[1].split('_')[0] for record in self.dataset_records],
            'creation_time': datetime.now().isoformat()
        }
        
        stats_file = self.output_dir / "dataset_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 元数据已保存:")
        print(f"   训练CSV: {self.metadata_file}")
        print(f"   完整JSON: {json_file}")
        print(f"   统计文件: {stats_file}")
    
    def print_summary(self):
        """打印数据集摘要"""
        if not self.dataset_records:
            print("📊 数据集为空")
            return
        
        print(f"\n📊 视频数据集摘要:")
        print(f"   总样本数: {len(self.dataset_records)}")
        print(f"   视频分辨率: 832×480")
        print(f"   视频帧率: 15fps")
        print(f"   视频时长: 3.0秒")
        print(f"   总帧数: 45帧/视频")
        
        print(f"\n🎬 场景列表:")
        for record in self.dataset_records:
            scene_name = record['video'].split('/')[1].split('_')[0]
            print(f"   • {scene_name}: {record['prompt'][:50]}...")
        
        print(f"\n📁 文件结构:")
        print(f"   {self.output_dir}/")
        print(f"   ├── metadata.csv          # 训练用CSV文件")
        print(f"   ├── metadata_full.json    # 完整元数据")
        print(f"   ├── dataset_stats.json    # 统计信息")
        print(f"   ├── videos/               # 视频文件目录")
        print(f"   └── images/               # 输入图像目录")

def main():
    """主函数"""
    print("🎬 Wan2.1-I2V-14B-480P 视频数据集创建工具")
    print("=" * 60)
    
    # 创建视频数据集创建器
    creator = VideoDatasetCreator()
    
    # 创建视频数据集
    creator.create_video_dataset()
    
    # 保存元数据
    creator.save_metadata()
    
    # 打印摘要
    creator.print_summary()
    
    print(f"\n🎉 视频数据集创建完成!")
    print(f"📁 数据集位置: {creator.output_dir}")
    print(f"📝 训练用CSV: {creator.metadata_file}")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 检查生成的视频: ls -la {creator.videos_dir}")
    print(f"   2. 运行训练命令（使用video列而不是image列）")
    print(f"   3. 验证训练结果")

if __name__ == "__main__":
    main()

{"scan_time": "2025-07-15T23:29:29.562727", "total_files": 43, "files": [{"filename": "complete_procurement_crawler.py", "size": 8580, "modified": "2025-07-15T22:05:17.089580", "type": "Python脚本"}, {"filename": "complete_vendor_data_20250715_232247.csv", "size": 649, "modified": "2025-07-15T23:22:47.443655", "type": "CSV数据"}, {"filename": "complete_vendor_data_20250715_232247.json", "size": 1019, "modified": "2025-07-15T23:22:47.436064", "type": "JSON数据"}, {"filename": "conversation_record.md", "size": 9319, "modified": "2025-07-15T23:27:46.447317", "type": "文档"}, {"filename": "conversation_summary_20250715_232929.json", "size": 2893, "modified": "2025-07-15T23:29:29.525070", "type": "JSON数据"}, {"filename": "debug_crawler.py", "size": 2735, "modified": "2025-07-15T21:46:50.467082", "type": "Python脚本"}, {"filename": "defense_procurement_crawler.py", "size": 5891, "modified": "2025-07-15T22:09:18.527644", "type": "Python脚本"}, {"filename": "defense_procurement_data.json", "size": 18400, "modified": "2025-07-15T22:13:12.329649", "type": "JSON数据"}, {"filename": "defense_procurement_data_original_cleaned.json", "size": 18176, "modified": "2025-07-15T22:30:04.421918", "type": "JSON数据"}, {"filename": "defense_procurement_data_simplified.json", "size": 18176, "modified": "2025-07-15T22:30:04.423764", "type": "JSON数据"}, {"filename": "defense_procurement_report_simplified.html", "size": 39894, "modified": "2025-07-15T22:32:02.420409", "type": "其他"}, {"filename": "defense_procurement_report_traditional.html", "size": 39894, "modified": "2025-07-15T22:32:02.418908", "type": "其他"}, {"filename": "enhanced_procurement_crawler.py", "size": 33106, "modified": "2025-07-15T22:23:39.965339", "type": "Python脚本"}, {"filename": "final_complete_data_generator.py", "size": 11668, "modified": "2025-07-15T23:24:16.622694", "type": "Python脚本"}, {"filename": "final_complete_procurement_data_20250715_232445.csv", "size": 1024, "modified": "2025-07-15T23:24:45.348037", "type": "CSV数据"}, {"filename": "final_complete_procurement_data_20250715_232445.json", "size": 3114, "modified": "2025-07-15T23:24:45.343337", "type": "JSON数据"}, {"filename": "final_complete_procurement_data_20250715_232445_summary.csv", "size": 292, "modified": "2025-07-15T23:24:45.351441", "type": "CSV数据"}, {"filename": "final_complete_procurement_data_20250715_232445_vendors.csv", "size": 899, "modified": "2025-07-15T23:24:45.350018", "type": "CSV数据"}, {"filename": "final_enhanced_crawler.py", "size": 18157, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "full_procurement_crawler.py", "size": 9443, "modified": "2025-07-15T22:10:24.733765", "type": "Python脚本"}, {"filename": "improved_complete_crawler.py", "size": 14770, "modified": "2025-07-15T23:14:40.387365", "type": "Python脚本"}, {"filename": "improved_vendor_extraction.py", "size": 12296, "modified": "2025-07-15T22:24:47.696118", "type": "Python脚本"}, {"filename": "improved_vendor_extraction_result.json", "size": 2197, "modified": "2025-07-15T22:25:07.271835", "type": "JSON数据"}, {"filename": "new_field_crawler.py", "size": 14860, "modified": "2025-07-15T22:54:41.174894", "type": "Python脚本"}, {"filename": "new_field_procurement_data_国防部_111年_20250715_225858.json", "size": 42428, "modified": "2025-07-15T22:58:58.187255", "type": "JSON数据"}, {"filename": "new_field_procurement_data_国防部_111年_20250715_225858_complete.csv", "size": 9900, "modified": "2025-07-15T23:00:09.689062", "type": "CSV数据"}, {"filename": "new_field_procurement_data_国防部_111年_20250715_225858_summary.csv", "size": 3480, "modified": "2025-07-15T23:00:09.694911", "type": "CSV数据"}, {"filename": "procurement.py", "size": 6949, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "procurement_complete_data.json", "size": 11918, "modified": "2025-07-15T13:00:12", "type": "JSON数据"}, {"filename": "procurement_crawler.py", "size": 7548, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "procurement_crawler_system.tar.gz", "size": 16800412, "modified": "2025-07-15T13:00:12", "type": "其他"}, {"filename": "procurement_data_custom_fields.csv", "size": 1365, "modified": "2025-07-15T22:38:10.613346", "type": "CSV数据"}, {"filename": "procurement_data_custom_fields.json", "size": 3268, "modified": "2025-07-15T22:38:10.610336", "type": "JSON数据"}, {"filename": "procurement_data_essential_fields.csv", "size": 2831, "modified": "2025-07-15T22:38:10.596909", "type": "CSV数据"}, {"filename": "procurement_data_essential_fields.json", "size": 8335, "modified": "2025-07-15T22:38:10.588054", "type": "JSON数据"}, {"filename": "procurement_data_minimal_fields.csv", "size": 1393, "modified": "2025-07-15T22:38:10.606834", "type": "CSV数据"}, {"filename": "procurement_data_minimal_fields.json", "size": 3449, "modified": "2025-07-15T22:38:10.600404", "type": "JSON数据"}, {"filename": "procurement_detailed_data.json", "size": 2, "modified": "2025-07-15T21:43:40.232678", "type": "JSON数据"}, {"filename": "save_conversation.py", "size": 10701, "modified": "2025-07-15T23:29:04.226188", "type": "Python脚本"}, {"filename": "targeted_vendor_crawler.py", "size": 12961, "modified": "2025-07-15T23:22:12.158551", "type": "Python脚本"}, {"filename": "test_crawler.py", "size": 1233, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "test_simple_crawler.py", "size": 4768, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "ultimate_procurement_crawler.py", "size": 10502, "modified": "2025-07-15T22:14:41.355100", "type": "Python脚本"}], "categories": {"scripts": [{"filename": "complete_procurement_crawler.py", "size": 8580, "modified": "2025-07-15T22:05:17.089580", "type": "Python脚本"}, {"filename": "debug_crawler.py", "size": 2735, "modified": "2025-07-15T21:46:50.467082", "type": "Python脚本"}, {"filename": "defense_procurement_crawler.py", "size": 5891, "modified": "2025-07-15T22:09:18.527644", "type": "Python脚本"}, {"filename": "enhanced_procurement_crawler.py", "size": 33106, "modified": "2025-07-15T22:23:39.965339", "type": "Python脚本"}, {"filename": "final_complete_data_generator.py", "size": 11668, "modified": "2025-07-15T23:24:16.622694", "type": "Python脚本"}, {"filename": "final_enhanced_crawler.py", "size": 18157, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "full_procurement_crawler.py", "size": 9443, "modified": "2025-07-15T22:10:24.733765", "type": "Python脚本"}, {"filename": "improved_complete_crawler.py", "size": 14770, "modified": "2025-07-15T23:14:40.387365", "type": "Python脚本"}, {"filename": "improved_vendor_extraction.py", "size": 12296, "modified": "2025-07-15T22:24:47.696118", "type": "Python脚本"}, {"filename": "new_field_crawler.py", "size": 14860, "modified": "2025-07-15T22:54:41.174894", "type": "Python脚本"}, {"filename": "procurement.py", "size": 6949, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "procurement_crawler.py", "size": 7548, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "save_conversation.py", "size": 10701, "modified": "2025-07-15T23:29:04.226188", "type": "Python脚本"}, {"filename": "targeted_vendor_crawler.py", "size": 12961, "modified": "2025-07-15T23:22:12.158551", "type": "Python脚本"}, {"filename": "test_crawler.py", "size": 1233, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "test_simple_crawler.py", "size": 4768, "modified": "2025-07-15T13:00:12", "type": "Python脚本"}, {"filename": "ultimate_procurement_crawler.py", "size": 10502, "modified": "2025-07-15T22:14:41.355100", "type": "Python脚本"}], "data": [{"filename": "complete_vendor_data_20250715_232247.csv", "size": 649, "modified": "2025-07-15T23:22:47.443655", "type": "CSV数据"}, {"filename": "complete_vendor_data_20250715_232247.json", "size": 1019, "modified": "2025-07-15T23:22:47.436064", "type": "JSON数据"}, {"filename": "conversation_summary_20250715_232929.json", "size": 2893, "modified": "2025-07-15T23:29:29.525070", "type": "JSON数据"}, {"filename": "defense_procurement_data.json", "size": 18400, "modified": "2025-07-15T22:13:12.329649", "type": "JSON数据"}, {"filename": "defense_procurement_data_original_cleaned.json", "size": 18176, "modified": "2025-07-15T22:30:04.421918", "type": "JSON数据"}, {"filename": "defense_procurement_data_simplified.json", "size": 18176, "modified": "2025-07-15T22:30:04.423764", "type": "JSON数据"}, {"filename": "final_complete_procurement_data_20250715_232445.csv", "size": 1024, "modified": "2025-07-15T23:24:45.348037", "type": "CSV数据"}, {"filename": "final_complete_procurement_data_20250715_232445.json", "size": 3114, "modified": "2025-07-15T23:24:45.343337", "type": "JSON数据"}, {"filename": "final_complete_procurement_data_20250715_232445_summary.csv", "size": 292, "modified": "2025-07-15T23:24:45.351441", "type": "CSV数据"}, {"filename": "final_complete_procurement_data_20250715_232445_vendors.csv", "size": 899, "modified": "2025-07-15T23:24:45.350018", "type": "CSV数据"}, {"filename": "improved_vendor_extraction_result.json", "size": 2197, "modified": "2025-07-15T22:25:07.271835", "type": "JSON数据"}, {"filename": "new_field_procurement_data_国防部_111年_20250715_225858.json", "size": 42428, "modified": "2025-07-15T22:58:58.187255", "type": "JSON数据"}, {"filename": "new_field_procurement_data_国防部_111年_20250715_225858_complete.csv", "size": 9900, "modified": "2025-07-15T23:00:09.689062", "type": "CSV数据"}, {"filename": "new_field_procurement_data_国防部_111年_20250715_225858_summary.csv", "size": 3480, "modified": "2025-07-15T23:00:09.694911", "type": "CSV数据"}, {"filename": "procurement_complete_data.json", "size": 11918, "modified": "2025-07-15T13:00:12", "type": "JSON数据"}, {"filename": "procurement_data_custom_fields.csv", "size": 1365, "modified": "2025-07-15T22:38:10.613346", "type": "CSV数据"}, {"filename": "procurement_data_custom_fields.json", "size": 3268, "modified": "2025-07-15T22:38:10.610336", "type": "JSON数据"}, {"filename": "procurement_data_essential_fields.csv", "size": 2831, "modified": "2025-07-15T22:38:10.596909", "type": "CSV数据"}, {"filename": "procurement_data_essential_fields.json", "size": 8335, "modified": "2025-07-15T22:38:10.588054", "type": "JSON数据"}, {"filename": "procurement_data_minimal_fields.csv", "size": 1393, "modified": "2025-07-15T22:38:10.606834", "type": "CSV数据"}, {"filename": "procurement_data_minimal_fields.json", "size": 3449, "modified": "2025-07-15T22:38:10.600404", "type": "JSON数据"}, {"filename": "procurement_detailed_data.json", "size": 2, "modified": "2025-07-15T21:43:40.232678", "type": "JSON数据"}], "docs": [{"filename": "conversation_record.md", "size": 9319, "modified": "2025-07-15T23:27:46.447317", "type": "文档"}]}}
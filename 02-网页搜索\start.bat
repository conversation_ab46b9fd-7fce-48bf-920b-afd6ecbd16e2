@echo off
echo ========================================
echo 智能网页搜索系统启动脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请先安装Python 3.7+
    pause
    exit /b 1
)

echo Python环境检查通过！
echo.

echo 正在安装依赖包...
pip install -r requirements.txt

echo.
echo 正在启动服务...
echo 访问地址: http://localhost:5000
echo 按 Ctrl+C 停止服务
echo.

python app.py

pause

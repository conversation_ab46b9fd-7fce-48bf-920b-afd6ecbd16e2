﻿# 采集个人信息须本人同意

**发布日期**: 2013年01月21日

**原文链接**: https://paper.dzwww.com/dzrb/content/20130316/Articel03006MT.htm

## 📄 原文内容

我国首部征信业法规15日起施行,企业拖欠工资、环保违法信息等纳入征信系统
本报记者　王　爽　杨　鹏  本报通讯员　刘伟厚
□　本报记者　王　爽　杨　鹏　　本报通讯员　刘伟厚
自3月15日起,酝酿10年之久的《征信业管理条例》正式施行。
济南市民蒋博文盼着这一天快点到来。这意味着再等几年,她因“无心之失”而被征信机构记录的“污点”将被永久地抹去。
蒋博文几年前贷款买了第一套房,但生性“马大哈”的她经常忘记日期,有了10多条不良拖欠记录,上了人行济南分行征信系统的“黑名单”。这样一来,不但申办二套房贷时遇到了麻烦,被“拉黑”的蒋博文连办信用卡或出国旅游都要找贷款行开具“非恶意欠款”证明。由于此前不良记录的保存期限没有明确的法律规定,蒋博文不知道要等到何时才能有个“清白之身”。
近些年来,征信在我国经济社会生活中扮演着越来越重要的角色。个人买房、办公司、企业贷款等通常都会被要求出具信用报告,甚至一些公司招聘也要求应聘者拿出个人信用报告。
1月21日《条例》出台,解决了我国征信业发展中无法可依的问题。《条例》规定不良信息保存期限自不良行为或者事件终止之日起为5年。
国际上一般都对个人的不良信息设定了保存时限,但期限并不相同。如英国规定保留6年；韩国保留5年；美国个人破产信息保留10年,其他负面信息保留7年,15万美元以上的负面信息不受保存期限限制。我国香港地区个人破产信息保留8年,败诉信息保留7年。
有学者认为,征信记录应分类、动态管理。以拖欠还贷为例,应根据欠款数额多少、时间长短、有无恶意等不同的违信情况来确定不同的记录保存期限。
个人信用报告被称之为个人的“经济身份证”。《条例》规定个人可以每年两次免费向征信机构查询自己的信用报告。
个人信用报告可到所在地中国人民银行各地分支行以及征信分中心查询。报告目前分为3个版本。一是2011个人版:供消费者了解自己信用状况,主要展示信息主体的信贷信息和负面的公共信息。二是银行专业版:主要供商业银行查询。三是社会版:供消费者开立股指期货账户,包括个人的执业资格记录、行政奖励和处罚记录、法院诉讼和强制执行记录、欠税记录、社会保险记录、住房公积金记录以及信用交易记录等。
记者从中国人民银行征信中心了解到,信用报告中看不到“良”或“不良”的字样。比如,某人有笔贷款逾期未还,报告中将记载为这笔贷款逾期,而不会记载“此人逾期还款,记录不良”等字样。此外,报告也不对欠款进行“善意”欠款或者“恶意”欠款的区分。对个人信用的判断,由报告使用机构自行综合判断。
目前,我省17个市的个人住房公积金信息已进入个人征信系统,拖欠职工工资、欠缴社会保险费、环保违法信息等也纳入了企业和个人征信系统。同时,人民银行济南分行不断扩大信用信息的应用范围,仅2012年全省为财政、农业、司法等部门提供企业系统查询607次,个人系统司法查询3038次。
中国人民银行济南分行征信管理处刘洪来处长认为,《征信业管理条例》的最大的特点是,《条例》充分尊重和保护了信息主体的权益。一是采集个人信息须本人同意,未经本人同意不得采集；向征信机构提供个人不良信息的,应当事先告知信息主体本人。二是明确规定禁止和限制征信机构采集的个人信息,包括:禁止采集个人的宗教信仰、基因、指纹、血型、疾病和病史信息以及法律、行政法规规定禁止采集的其他个人信息；征信机构不得采集个人的收入、存款、有价证券、不动产的信息和纳税数额信息。三是明确规定个人对本人信息享有查询、异议和投诉等权利,包括个人认为信息错误、遗漏的,可以向征信机构或信息提供者提出异议等。
此外,《条例》首次对从事个人征信业务的征信机构和从事企业征信业务的征信机构规定了不同的设立条件。
《条例》明确,中国人民银行及其派出机构是征信业监督管理部门,依法履行对征信业和金融信用信息基础数据库运行机构的监督管理职责。截至2012年末,金融信用信息基础数据库运行8年来已收录1800多万户企业、8亿多个人的有关信息。该数据库共接入山东省金融机构600多个,企业征信系统共收录山东省借款人60.5万户,开通查询用户9496户,日均查询3.9万次；个人征信系统收录山东省自然人5470.1万个,开通查询用户2.5万户,日均查询5.8万次。2012年,山东省25家金融机构监测网点利用征信系统,共拒绝高风险企业信贷业务2441笔,涉及金额385.8亿元；拒绝个人贷款申请8347笔,拒贷率为4.1%,涉及金额11.3亿元,征信系统识别和防范信贷风险的作用得到有效发挥。
var tagSpan = document.getElementById('contenttext');

	var ss = tagSpan.innerHTML;

	ss = ss.replace(/<SPAN class=FloatTitle>　　/g, "<SPAN class=FloatTitle>").replace(/<SPAN class=FloatTitle>　/g, "<SPAN class=FloatTitle>").replace(/<SPAN class=FloatTitle>/g, "<SPAN class=FloatTitle>&nbsp;&nbsp;&nbsp;&nbsp;<strong>").replace(/<\/SPAN>/g, "</SPAN></strong>");

	tagSpan.innerHTML = ss;
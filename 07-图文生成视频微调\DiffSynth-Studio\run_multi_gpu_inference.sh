#!/bin/bash
# Wan2.1-I2V-14B-480P 多卡推理启动脚本

set -e

echo "🎬 Wan2.1-I2V-14B-480P 多卡推理启动"
echo "=================================="

# 激活环境
echo "📦 激活conda环境..."
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

# 检查环境
echo "🔍 检查环境..."
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}')"

# 检查GPU状态
echo "🖥️  GPU状态:"
nvidia-smi --query-gpu=index,name,memory.total,memory.used,utilization.gpu --format=csv,noheader,nounits

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1
export TOKENIZERS_PARALLELISM=false

# 检查必要文件
LORA_CHECKPOINT="./models/train/Wan2.1-I2V-14B-480P_lora_final/epoch-4.safetensors"
if [ ! -f "$LORA_CHECKPOINT" ]; then
    echo "❌ 找不到LoRA检查点文件: $LORA_CHECKPOINT"
    echo "   请确保训练已完成并生成了检查点文件"
    exit 1
fi

echo "✅ 找到LoRA检查点: $LORA_CHECKPOINT"

# 创建输出目录
OUTPUT_DIR="./inference_outputs/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTPUT_DIR"
echo "📁 输出目录: $OUTPUT_DIR"

# 运行推理
echo "🚀 启动多卡推理..."
echo "=================================="

# 方式1: 使用简化脚本
echo "方式1: 运行简化推理脚本"
python wan_multi_gpu_inference_simple.py

echo ""
echo "=================================="

# 方式2: 使用完整脚本（如果需要）
echo "方式2: 运行完整推理脚本"
python multi_gpu_inference.py \
    --lora_path "$LORA_CHECKPOINT" \
    --prompt "A beautiful sunset over the ocean with gentle waves" \
    --output_path "$OUTPUT_DIR/sunset_video.mp4" \
    --gpu_ids "0,1" \
    --num_frames 81 \
    --num_inference_steps 50 \
    --guidance_scale 7.5

echo ""
echo "=================================="

# 方式3: 基准测试
echo "方式3: 运行性能基准测试"
python multi_gpu_inference.py \
    --lora_path "$LORA_CHECKPOINT" \
    --prompt "Performance benchmark test" \
    --gpu_ids "0,1" \
    --benchmark

echo ""
echo "=================================="

# 检查结果
echo "📊 检查生成结果..."
if [ -d "$OUTPUT_DIR" ]; then
    echo "输出文件:"
    ls -la "$OUTPUT_DIR"
else
    echo "⚠️  输出目录不存在"
fi

# GPU使用情况
echo ""
echo "🖥️  推理后GPU状态:"
nvidia-smi --query-gpu=index,name,memory.used,utilization.gpu --format=csv,noheader,nounits

echo ""
echo "🎉 多卡推理完成!"
echo "=================================="

﻿# 中国电影博物馆2024年财政预算信息_2024市级部门财政预算专题_首都之窗_北京市人民政府门户网站

**发布日期**: 2024年01月31日

**原文链接**: https://www.beijing.gov.cn/gongkai/caizheng/czzt/2024ys/202402/t20240221_3566390.html

## 📄 原文内容

@media only screen and (max-width: 640px) {body{min-width:auto;}}
    .m-share{float: left;margin-top:-5px;}
    .share:after{content: "";display: block;clear: both;}
    .share a{float: left;width: 26px;height: 26px;border-radius: 50%;margin-left: 13px;background: #c9c9c9;cursor: pointer;display: block;}
    .share a i{display: block;width: 26px;height: 26px;-webkit-transition: .4s all;-moz-transition: .4s all;-ms-transition: .4s all;transition: .4s all;}
    .share a:hover i{-webkit-transform: rotate(360deg);-moz-transform: rotate(360deg);-ms-transform: rotate(360deg);transform: rotate(360deg);}
    .share #share-icon{width: 26px;height: 26px;float: left;display: none;}
    .share #share-icon img{width: 100%;height: 100%;}
    .share .share-qqzone i{background: url("/images/cont_ico_share_20200422.png") 4px center no-repeat;}
    .share .share-qqzone:hover{background-color: #fc7354;}
    .share .share-wechat{position: relative;}
    .share .share-wechat i{background: url("/images/cont_ico_share_20200422.png") -30px center no-repeat;}
    .share .share-wechat:hover{background-color: #1fbc7d;}
    .share .share-weibo i{background: url("/images/cont_ico_share_20200422.png") -65px center no-repeat;}
    .share .share-qq:hover{background-color: #27a8f2;}
    .share .share-qq i{background: url("/images/cont_ico_share_20200422.png") -96px center no-repeat;}
    .share .share-weibo:hover{background-color: #e96157;}
    .share .bg-code{left: -36px;z-index: 10;}
    .share .qrcode{position: absolute;top: 36px;border: 1px solid #ccc;padding: 5px;background: #fff;display: none;width: 100px;height: 100px;left: -98%;z-index: 11;}
    .share .close-btn{position: absolute;background: #fff;color: #000;font-size: 12px;z-index: 12;width: 12px;height: 12px;line-height: 12px;text-align: center;right: -39px;top: 50px;display: none;cursor: pointer;}
    var title = "【" + $.trim(document.title) + "】";
    var description = $.trim($('meta[name="description"]').attr('content'));
    var portalUrl = window.location.href;
    function generateQRCode(rendermethod, picwidth, picheight, url) {
        $(".qrcode").qrcode({
            render: rendermethod, // 渲染方式有table方式（IE兼容）和canvas方式
            width: picwidth, //宽度
            height: picheight, //高度
            text: utf16to8(portalUrl), //内容
            typeNumber: -1, //计算模式
            correctLevel: 2, //二维码纠错级别
            background: "#ffffff", //背景颜色
            foreground: "#000000" //二维码颜色
    canvas_table = !!document.createElement('canvas').getContext ? 'canvas' : 'table';
    function init() {
        generateQRCode(canvas_table, 100, 100, window.location.href);
    function utf16to8(str) {
        var out, i, len, c;
        len = str.length;
        for(i = 0; i < len; i++) {
            c = str.charCodeAt(i);
            if((c >= 0x0001) && (c <= 0x007F)) {
                out += str.charAt(i);
            } else if(c > 0x07FF) {
                out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
                out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
                out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
                out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
                out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
    $(".share-wechat").on("click", function() {
        $(".bg-code,.qrcode").toggle();
    $(".close-btn").on("click", function(event) {
        $(".bg-code,.qrcode,.close-btn").hide();
        event.stopPropagation();
    function showToQzone() {
         var _desc = description;
         var _title = title;
         var _url = portalUrl;
         var _shareUrl = 'http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?';
         _shareUrl += 'url=' + encodeURIComponent(_url);   //参数url设置分享的内容链接|默认当前页location
         _shareUrl += '&title=' + encodeURIComponent(_title);    //参数title设置分享标题，可选参数
         window.open(_shareUrl,'','width=700,height=680,top=0,left=0,toolbar=no,menubar=no,scrollbars=no,resizable=1,location=yes,status=0'); 
    function showToSina(title, portalUrl, desc) {
        var _desc = desc;
        var _t = title + " " + _desc;
        var _url = portalUrl;
        var _appkey = "2806082167"; //你从微薄获得的appkey
        var _site = ''; //你的网站地址
        var _ralateUid = "";
        var _u = 'http://service.weibo.com/share/share.php?url=' + _url + '&appkey=' + _appkey + '&title=' + _t + '&ralateUid=' + _ralateUid + '&searchPic=false';
        window.open(_u, '', 'width=700, height=680, top=0, left=0, toolbar=no, menubar=no, scrollbars=no, location=yes, resizable=no, status=no');
    $('.share-qqzone').on('click', function() {
        showToQzone(title, portalUrl, description);
    $('.share-weibo').on('click', function() {
        showToSina(title, portalUrl, description);
function changeSize(size) {document.getElementById('mainText').style.fontSize=size+'px';}
根据中央机构编制委员会办公室批准的《关于成立中国电影博物馆的批复》（中央编办复字2005〔148〕号）等文件批复，设立中国电影博物馆。
中国电影博物馆职责为：电影艺术博览，电影文化交流，电影专业活动，电影藏品征集，电影公共服务，电影知识普及，电影放映服务，电影学术研究，电影社会教育，对公众特别是青少年进行电影文化和爱国主义教育。
设办公室、财务部、人事部、保障部、保卫部、研究部（馆刊编辑部）、信息传播部、藏品部、社会教育部、活动管理部、展陈部、影院部、开发部（基本建设办公室）13个内设机构和机关党委（党建办公室）。中国电影博物馆下属单位0个。
中国电影博物馆行政编制0人，实有人数0人；事业编制117人，实有人数112人；离退休人员23人，其中：离休0人，退休23人。
2024年度收入预算12312.57万元，比2023年年初预算数10774.00万元增加1538.57万元，增长14.28%。主要原因是按照全口径预算管理要求，进一步加强预算资金管理，将其他收入资金全口径纳入年度预算管理。
（一）本年财政拨款收入10112.00万元
1.一般公共预算拨款收入10112.00万元。
3.国有资本经营预算拨款收入0.00万元。
2024年支出预算12312.57万元，比2023年年初预算数10774.00万元增加1538.57万元，增长14.28%。主要原因是按照全口径预算管理要求，进一步加强预算资金管理，将其他收入资金全口径纳入年度预算管理，相关支出相应增长。
(一)基本支出。基本支出预算7072.50万元，占总支出预算57.44%，比2023年年初预算数7306.87万元减少234.37万元，下降3.21%。
（二）项目支出。项目支出预算5240.07万元，比2023年年初预算数3467.13万元增加1772.94万元，增长51.14%。其中：
中国电影博物馆因公出国（境）费用、公务接待费、公务用车购置和运行维护费开支单位包括中国电影博物馆本级1个所属单位。其他所属单位2024年无财政拨款安排的“三公”经费预算。
2024年财政拨款“三公”经费预算73.12万元，比2023年财政拨款“三公”经费预算减少13.19万元。其中：
1.因公出国（境）费用。2024年预算数22.67万元，比2023年年初预算数24.83万元减少2.16万元，主要原因：落实政府过紧日子要求，进一步压减一般性支出。
2.公务接待费。2024年预算数0.40万元，比2023年年初预算数0.00万元增加0.40万元，主要原因：疫情常态化管理后，文博行业的交流活动增加，交流活动接待实际需求增多。
3.公务用车购置和运行维护费。2024年预算数50.05万元，包括：公务用车购置费2024年预算数37.55万元，比2023年年初预算数48.98万元减少11.43万元，主要原因：2023年购置1辆大型客车，2024年无此需求，金额有所下降；公务用车运行维护费2024年预算数12.50万元，其中：公务用车燃油4.77万元，公务用车维修3.69万元，公务用车保险2.30万元，其他支出1.76万元。公务用车运行维护费2024年预算数与2023年年初预算数12.50万元持平。
2024年中国电影博物馆政府采购预算总额3861.87万元，其中：政府采购货物预算286.17万元，政府采购工程预算0.00万元，政府采购服务预算3575.70万元。
2024年中国电影博物馆政府购买服务预算总额0.00万元。
2024年，中国电影博物馆填报绩效目标的预算项目45个，占本部门本年预算项目45个的100.00%。填报绩效目标的项目支出预算5239.50万元，占本部门本年项目支出预算的100.00%。
本部门2024年无国有资本经营预算财政拨款安排的预算
截至2023年底，中国电影博物馆共有车辆9台，共计243.09万元；单位价值50万元以上的设备14台（套）、共计3506.25万元。2024年预算安排中，购置单位价值50万元以上的设备1台（套），共计80.31万元。
基本支出：指为保障机构正常运转、完成日常工作任务而发生的人员支出和公用支出。
项目支出：指在基本支出之外为完成特定行政任务或事业发展目标所发生的支出。
“三公”经费财政拨款预算数：指本部门当年部门预算中财政拨款安排的因公出国（境）费用、公务接待费、公务用车购置和运行维护费预算数。
政府采购：各级国家机关、事业单位和团体组织，使用财政性资金采购依法制定的集中采购目录以内的或者采购限额标准以上的货物、工程和服务的行为，是规范财政支出管理和强化预算约束的有效措施。
附件：中国电影博物馆2024年度部门预算报表
#qr_wrap{width: 135px;margin: 0 auto;padding-bottom:40px;}
    var text = window.location.href;
    $('#qr_wrap').qrcode({
        text: utf16to8(text),
    function utf16to8(str) { //转码
    var out, i, len, c;
    len = str.length;
    for (i = 0; i < len; i++) {
        c = str.charCodeAt(i);
        if ((c >= 0x0001) && (c <= 0x007F)) {
            out += str.charAt(i);
        } else if (c > 0x07FF) {
            out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
            out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
            out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
            out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
            out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
var oDl = document.getElementById('fontSize');
    var aA = oDl.getElementsByTagName('a');  
    for(var i = 0;i<aA.length;i++){
        aA[i].index  = i;     
        aA[i].onclick = function(){  
            for(var i = 0;i<aA.length;i++){
                aA[i].className = '';
            this.className = 'on';
.alert-mengban{position: fixed;top: 0px;left: 0px;z-index: 1000;background: #000000;opacity: 0.8 !important;filter: alpha(opacity=80) !important;width: 100%;height: 100%;display: none;}
        .alert-warning{position: fixed;left: 50%;top:-400px;margin-left:-300px; width: 600px;height: 270px;background:#fff;z-index: 1001;display: none;}
        .alert-delete{width: 100%;height: 38px;position: relative;}
        .alert-delete span{position: absolute;top:10px;right: 10px; width: 19px;height: 19px;background: url(/ywdt/images/delete-ks-20170807.png) center center no-repeat;cursor:pointer;}
        .alert-wzsm{width: 480px;height: 100px;margin: 15px auto 0; line-height: 35px;font-size: 24px;color: #000;text-align: center;font-family:"Microsoft YaHei"; padding-bottom: 15px;border-bottom: 1px solid #d4d4d4;}
        .alert-wzsm p{font-size:24px;font-family:"Microsoft YaHei";}
        .alert-footer{width: 100%; height: 105px;font-size: 24px;color: #000;}
        .alert-footer span{cursor: pointer;float: left;font-family:"Microsoft YaHei";}
        .continue{width: 124px;height: 42px;background: url(/ywdt/images/continue-ks-20170810.png) center center no-repeat;}
        .fangqi{line-height: 42px;font-size: 20px;color: #ab0d07;margin-left: 20px;}
        .xuanze{width: 210px;height: 42px;margin: 25px auto 0;}
        @media only screen and (max-width: 414px) {
            .alert-warning{position: fixed;left:2%;top:-400px;margin-left:0; width: 96%;height: auto;background: #fff;background-size: 100%; z-index: 1001;}
            .alert-wzsm{width: 80%;height: auto;margin: 15px auto 0; line-height: 28px;font-size: 18px;color: #000;text-align: center; }
            .alert-wzsm p{font-size:18px;}
            .alert-footer{width: 100%; height: 70px;line-height: 70px;font-size: 18px;color: white;margin-top: 10px;}
            .continue{ width: 124px;height: 42px;background: url(/ywdt/images/continue-ks-20170810.png) center center no-repeat;background-size: 100%;}
您访问的链接即将离开“首都之窗”门户网站 是否继续？
$("a").each(function(){
    var htm=$(this).html();
    $(this).click(function(){
		if(this.href!=""&&this.href.toLowerCase().indexOf("javascript")==-1&&this.href.toLowerCase().indexOf(".gov.cn")==-1&&this.href.toLowerCase().indexOf("javascript:preVious")==-1&&this.href.toLowerCase().indexOf("javascript:next")==-1){
		document.getElementById('outUrl').innerText=this.href;
	          document.getElementById('hash').click();
		$(".alert-mengban").fadeIn(200);
		$(".alert-warning").delay(200).show().animate({top:"75px"}, 300);
		$("#closets,.fangqi,.alert-mengban").click(function() {
			$(".alert-warning").animate({top:"-400px"}, 200).hide(300);
			$(".alert-mengban").delay(300).fadeOut(300);
		$(".continue").click(function(){			
		       $(".alert-warning").hide(200);
	                 $(".alert-mengban").delay(200).fadeOut(200);
/*$("select").on("change",function () {
 var opVal = $(this).find("option:selected").val();
 if(opVal!=""&&opVal.toLowerCase().indexOf("javascript")==-1&&opVal.toLowerCase().indexOf(".gov.cn")==-1&&opVal.toLowerCase().indexOf(".ebeijing.gov.cn")==-1&&opVal.toLowerCase().indexOf(".bj.gov.cn")==-1&&opVal.toLowerCase().indexOf("javascript:preVious")==-1&&opVal.toLowerCase().indexOf("javascript:next")==-1){
                document.getElementById('outUrl').innerText=opVal;
                document.getElementById('hash').click();
                $(".alert-mengban").fadeIn(200);
                $(".alert-warning").delay(200).show().animate({top:"75px"}, 300);
                $("#closets,.fangqi,.alert-mengban").click(function() {
                    $(".alert-warning").animate({top:"-400px"}, 200).hide(300);
                    $(".alert-mengban").delay(300).fadeOut(300);
                $(".continue").click(function(){
                    $(".alert-warning").hide(200);
                    $(".alert-mengban").delay(200).fadeOut(200);
                return false;
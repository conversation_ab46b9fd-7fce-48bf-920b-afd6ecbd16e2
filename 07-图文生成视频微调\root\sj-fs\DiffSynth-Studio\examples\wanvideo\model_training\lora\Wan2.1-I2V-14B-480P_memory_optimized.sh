#!/bin/bash
# Wan2.1-I2V-14B-480P 显存优化LoRA训练脚本
# 针对显存受限环境的优化训练配置
# 适用于单GPU RTX 3090 24GB显存

echo "🎬 开始Wan2.1-I2V-14B-480P 显存优化LoRA训练..."
echo "模型: Wan-AI/Wan2.1-I2V-14B-480P (图像到视频生成)"
echo "训练类型: 显存优化LoRA微调"
echo "分辨率: 320x576 (降低以节省显存)"
echo "=" * 60

# 检查虚拟环境
if [[ "$CONDA_DEFAULT_ENV" != "wan_video_env" ]]; then
    echo "⚠️  请先激活wan_video_env环境: conda activate wan_video_env"
    exit 1
fi

# 设置环境变量 - 显存优化
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128

# 检查GPU显存
echo "🔍 检查GPU显存..."
nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits | while read line; do
    echo "   $line"
done

# 检查数据集
if [ ! -d "data/example_video_dataset" ]; then
    echo "📥 下载示例数据集..."
    modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
fi

echo "🚀 启动显存优化LoRA训练..."
echo "⚙️  优化配置:"
echo "   - 分辨率: 320x576 (降低)"
echo "   - LoRA rank: 16 (降低)"
echo "   - 梯度累积: 8步"
echo "   - 启用梯度检查点卸载"
echo "   - 混合精度: bfloat16"

accelerate launch examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 320 \
  --width 576 \
  --dataset_repeat 50 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-4 \
  --num_epochs 2 \
  --gradient_accumulation_steps 8 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "./models/train/Wan2.1-I2V-14B-480P_memory_optimized" \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 16 \
  --extra_inputs "input_image" \
  --use_gradient_checkpointing_offload

echo "✅ 显存优化LoRA训练完成！"
echo "模型保存路径: ./models/train/Wan2.1-I2V-14B-480P_memory_optimized/"
echo "🧪 运行推理测试: python examples/wanvideo/model_training/validate_lora/Wan2.1-I2V-14B-480P.py"
echo "💡 提示: 请修改推理脚本中的模型路径为: ./models/train/Wan2.1-I2V-14B-480P_memory_optimized/epoch-1.safetensors"

# 显示训练后的显存使用情况
echo ""
echo "📊 训练后显存使用情况:"
nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader,nounits | while read line; do
    echo "   $line"
done

#!/usr/bin/env python3
"""
验证推理结果
检查生成的视频文件和质量
"""

import os
import time
from datetime import datetime

def check_inference_results():
    """检查推理结果"""
    print("🔍 验证Wan2.1-I2V-14B-480P推理结果")
    print("=" * 50)
    
    # 检查可能的输出文件
    possible_outputs = [
        "final_lora_inference_epoch4.mp4",
        "final_lora_inference_epoch3.mp4", 
        "final_lora_inference_epoch2.mp4",
        "final_lora_inference_epoch1.mp4",
        "final_lora_inference_epoch0.mp4",
        "final_lora_inference_epochbase.mp4",
        "gpu1_inference_epoch4.mp4",
        "lightweight_inference_epoch4.mp4"
    ]
    
    found_videos = []
    
    for video_file in possible_outputs:
        if os.path.exists(video_file):
            file_size = os.path.getsize(video_file) / 1024**2
            mod_time = os.path.getmtime(video_file)
            mod_time_str = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
            
            found_videos.append({
                'file': video_file,
                'size_mb': file_size,
                'modified': mod_time_str,
                'age_minutes': (time.time() - mod_time) / 60
            })
    
    if found_videos:
        print(f"✅ 找到 {len(found_videos)} 个生成的视频文件:")
        
        # 按修改时间排序，最新的在前
        found_videos.sort(key=lambda x: x['age_minutes'])
        
        for i, video in enumerate(found_videos):
            status = "🆕 最新" if i == 0 else f"📅 {video['age_minutes']:.1f}分钟前"
            print(f"   {i+1}. {video['file']}")
            print(f"      大小: {video['size_mb']:.1f}MB")
            print(f"      时间: {video['modified']} ({status})")
            
            # 基本质量检查
            if video['size_mb'] > 1.0:
                print(f"      质量: ✅ 正常大小")
            elif video['size_mb'] > 0.1:
                print(f"      质量: ⚠️  较小，可能质量较低")
            else:
                print(f"      质量: ❌ 文件过小，可能生成失败")
            print()
        
        # 推荐使用最新的文件
        latest_video = found_videos[0]
        print(f"📺 推荐查看最新生成的视频:")
        print(f"   文件: {latest_video['file']}")
        print(f"   大小: {latest_video['size_mb']:.1f}MB")
        print(f"   生成时间: {latest_video['modified']}")
        
        return True
    else:
        print("❌ 未找到生成的视频文件")
        print("\n🔍 可能的原因:")
        print("   1. 推理仍在进行中")
        print("   2. 推理过程中出现错误")
        print("   3. 输出路径不正确")
        
        return False

def check_training_results():
    """检查训练结果"""
    print("\n🔍 验证训练结果")
    print("-" * 30)
    
    lora_dir = "./models/train/Wan2.1-I2V-14B-480P_lora_final"
    
    if os.path.exists(lora_dir):
        print(f"✅ 训练输出目录存在: {lora_dir}")
        
        # 检查epoch文件
        epochs = []
        for i in range(10):
            epoch_file = f"{lora_dir}/epoch-{i}.safetensors"
            if os.path.exists(epoch_file):
                file_size = os.path.getsize(epoch_file) / 1024**2
                epochs.append((i, file_size))
        
        if epochs:
            print(f"✅ 找到 {len(epochs)} 个训练检查点:")
            for epoch, size in epochs:
                print(f"   epoch-{epoch}.safetensors ({size:.1f}MB)")
            
            latest_epoch = max(epochs, key=lambda x: x[0])
            print(f"\n🎯 最新检查点: epoch-{latest_epoch[0]} ({latest_epoch[1]:.1f}MB)")
        else:
            print("❌ 未找到训练检查点文件")
        
        # 检查训练配置
        config_file = f"{lora_dir}/training_args.json"
        if os.path.exists(config_file):
            print(f"✅ 训练配置文件存在: training_args.json")
        else:
            print("⚠️  训练配置文件缺失")
    else:
        print(f"❌ 训练输出目录不存在: {lora_dir}")

def check_system_status():
    """检查系统状态"""
    print("\n🔍 系统状态检查")
    print("-" * 30)
    
    # 检查GPU状态
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"✅ CUDA可用，{gpu_count} 张GPU")
            
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                usage_percent = (allocated / total) * 100
                
                print(f"   GPU {i}: {gpu_name}")
                print(f"           内存: {allocated:.1f}GB / {total:.1f}GB ({usage_percent:.1f}%)")
        else:
            print("❌ CUDA不可用")
    except ImportError:
        print("⚠️  PyTorch未安装或导入失败")
    
    # 检查磁盘空间
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        free_gb = free / 1024**3
        print(f"✅ 磁盘空间: {free_gb:.1f}GB 可用")
    except:
        print("⚠️  无法检查磁盘空间")

def main():
    """主函数"""
    print("🎬 Wan2.1-I2V-14B-480P 结果验证")
    print("=" * 60)
    
    # 检查推理结果
    inference_success = check_inference_results()
    
    # 检查训练结果
    check_training_results()
    
    # 检查系统状态
    check_system_status()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 验证总结")
    print("=" * 60)
    
    if inference_success:
        print("🎉 推理验证成功!")
        print("✅ 视频文件已生成")
        print("✅ 训练检查点完整")
        print("✅ 系统环境正常")
        
        print("\n📝 下一步建议:")
        print("   1. 查看生成的视频文件")
        print("   2. 尝试不同的提示词进行推理")
        print("   3. 调整推理参数优化质量/速度")
        print("   4. 使用不同的epoch检查点对比效果")
    else:
        print("⚠️  推理验证未完成")
        print("💡 建议:")
        print("   1. 检查推理进程是否仍在运行")
        print("   2. 查看推理脚本的输出日志")
        print("   3. 确认GPU内存是否充足")
        print("   4. 重新运行推理脚本")
    
    print(f"\n🕐 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()

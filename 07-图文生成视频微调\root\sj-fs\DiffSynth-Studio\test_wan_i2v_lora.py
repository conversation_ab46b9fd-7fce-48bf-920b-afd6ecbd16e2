#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P LoRA模型综合测试脚本
支持多种测试场景和自定义输入
"""

import torch
import os
import sys
import argparse
from PIL import Image
from pathlib import Path
from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from modelscope import dataset_snapshot_download

class WanI2VLoRATester:
    def __init__(self, lora_path=None, device="cuda"):
        self.device = device
        self.lora_path = lora_path
        self.pipe = None
        
    def setup_pipeline(self):
        """初始化模型管道"""
        print("🔧 初始化Wan2.1-I2V-14B-480P模型管道...")
        
        if not torch.cuda.is_available():
            print("❌ CUDA不可用，请检查GPU环境")
            return False
            
        print(f"✅ 使用GPU: {torch.cuda.get_device_name()}")
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"   显存: {memory_gb:.1f}GB")
        
        # 创建pipeline
        self.pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device=self.device,
            model_configs=[
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
            ],
        )
        
        # 加载LoRA权重
        if self.lora_path and os.path.exists(self.lora_path):
            try:
                self.pipe.load_lora(self.pipe.dit, self.lora_path, alpha=1)
                print(f"✅ LoRA权重加载成功: {self.lora_path}")
            except Exception as e:
                print(f"❌ LoRA权重加载失败: {e}")
                return False
        else:
            print("⚠️  未指定LoRA权重，使用原始模型")
            
        self.pipe.enable_vram_management()
        return True
        
    def auto_detect_lora(self):
        """自动检测LoRA权重文件"""
        possible_paths = [
            "models/train/Wan2.1-I2V-14B-480P_lora/epoch-4.safetensors",
            "models/train/Wan2.1-I2V-14B-480P_8x3090_lora/epoch-2.safetensors", 
            "models/train/Wan2.1-I2V-14B-480P_memory_optimized/epoch-1.safetensors"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.lora_path = path
                print(f"🔍 自动检测到LoRA权重: {path}")
                return True
                
        print("❌ 未找到LoRA权重文件")
        print("   期望路径:")
        for path in possible_paths:
            print(f"   - {path}")
        return False
        
    def test_basic_inference(self, output_dir="outputs"):
        """基础推理测试"""
        print("\n🧪 执行基础推理测试...")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 准备测试数据
        if not os.path.exists("data/example_video_dataset/video1.mp4"):
            print("📥 下载示例数据...")
            dataset_snapshot_download(
                dataset_id="DiffSynth-Studio/example_video_dataset",
                local_dir="./data/example_video_dataset"
            )
        
        # 测试用例
        test_cases = [
            {
                "name": "sunset_town",
                "prompt": "from sunset to night, a small town, light, house, river",
                "negative_prompt": "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量",
                "seed": 1
            },
            {
                "name": "nature_scene", 
                "prompt": "beautiful nature scene with flowing water, green trees, peaceful atmosphere",
                "negative_prompt": "ugly, blurry, low quality, distorted, static",
                "seed": 42
            },
            {
                "name": "urban_life",
                "prompt": "busy city street with people walking, cars moving, urban life",
                "negative_prompt": "empty, static, low quality, blurry",
                "seed": 123
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases):
            print(f"\n📹 测试 {i+1}/{len(test_cases)}: {test_case['name']}")
            print(f"   提示词: {test_case['prompt'][:50]}...")
            
            try:
                # 使用视频第一帧作为输入
                input_image = VideoData("data/example_video_dataset/video1.mp4", height=480, width=832)[0]
                
                video = self.pipe(
                    prompt=test_case["prompt"],
                    negative_prompt=test_case["negative_prompt"],
                    input_image=input_image,
                    seed=test_case["seed"],
                    tiled=True,
                    height=480,
                    width=832,
                    num_frames=25,
                    num_inference_steps=20
                )
                
                # 保存视频
                output_path = os.path.join(output_dir, f"{test_case['name']}_i2v.mp4")
                save_video(video, output_path, fps=15, quality=5)
                
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path) / 1024 / 1024
                    results.append({
                        "name": test_case["name"],
                        "success": True,
                        "output": output_path,
                        "size_mb": file_size
                    })
                    print(f"   ✅ 成功 - {output_path} ({file_size:.2f}MB)")
                else:
                    results.append({"name": test_case["name"], "success": False})
                    print(f"   ❌ 失败 - 文件未生成")
                    
            except Exception as e:
                results.append({"name": test_case["name"], "success": False, "error": str(e)})
                print(f"   ❌ 失败 - {e}")
                
        return results
        
    def test_custom_image(self, image_path, prompt, output_path=None):
        """自定义图像测试"""
        print(f"\n🖼️  自定义图像测试: {image_path}")
        
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return False
            
        try:
            # 加载图像
            input_image = Image.open(image_path).convert("RGB")
            print(f"✅ 图像加载成功: {input_image.size}")
            
            # 调整图像尺寸
            input_image = input_image.resize((832, 480))
            
            video = self.pipe(
                prompt=prompt,
                negative_prompt="low quality, blurry, static, distorted",
                input_image=input_image,
                seed=42,
                tiled=True,
                height=480,
                width=832,
                num_frames=25,
                num_inference_steps=25
            )
            
            # 保存视频
            if output_path is None:
                output_path = f"custom_i2v_{Path(image_path).stem}.mp4"
                
            save_video(video, output_path, fps=15, quality=5)
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024 / 1024
                print(f"✅ 自定义图像测试成功: {output_path} ({file_size:.2f}MB)")
                return True
            else:
                print("❌ 视频保存失败")
                return False
                
        except Exception as e:
            print(f"❌ 自定义图像测试失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description="Wan2.1-I2V-14B-480P LoRA综合测试")
    parser.add_argument("--lora_path", type=str, default=None, help="LoRA权重文件路径")
    parser.add_argument("--auto_detect", action="store_true", help="自动检测LoRA权重")
    parser.add_argument("--output_dir", type=str, default="outputs", help="输出目录")
    parser.add_argument("--custom_image", type=str, default=None, help="自定义输入图像路径")
    parser.add_argument("--custom_prompt", type=str, default="beautiful scene with motion", help="自定义提示词")
    
    args = parser.parse_args()
    
    print("🎬 Wan2.1-I2V-14B-480P LoRA综合测试")
    print("=" * 60)
    
    # 创建测试器
    tester = WanI2VLoRATester(lora_path=args.lora_path)
    
    # 自动检测LoRA权重
    if args.auto_detect and not args.lora_path:
        tester.auto_detect_lora()
    
    # 初始化管道
    if not tester.setup_pipeline():
        print("❌ 管道初始化失败")
        return
    
    # 执行基础测试
    results = tester.test_basic_inference(args.output_dir)
    
    # 执行自定义图像测试
    if args.custom_image:
        tester.test_custom_image(args.custom_image, args.custom_prompt)
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    success_count = sum(1 for r in results if r.get("success", False))
    print(f"   成功: {success_count}/{len(results)}")
    
    for result in results:
        status = "✅" if result.get("success", False) else "❌"
        print(f"   {status} {result['name']}")
        if result.get("output"):
            print(f"      输出: {result['output']}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Wan2.1-I2V-14B-480P 完整流水线
从环境检查到训练再到推理的完整自动化流程
"""

import os
import sys
import subprocess
import torch
import json
from datetime import datetime
from pathlib import Path

class Wan2IVPipeline:
    def __init__(self):
        self.project_root = "/root/sj-tmp/DiffSynth-Studio"
        self.conda_env = "wan_video_env"
        self.lora_output_dir = "./models/train/Wan2.1-I2V-14B-480P_lora_final"
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def run_command(self, command, check=True):
        """执行命令"""
        self.log(f"执行命令: {command}")
        try:
            result = subprocess.run(command, shell=True, check=check, 
                                  capture_output=True, text=True)
            if result.stdout:
                self.log(f"输出: {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            self.log(f"命令执行失败: {e}", "ERROR")
            if e.stderr:
                self.log(f"错误信息: {e.stderr}", "ERROR")
            raise
    
    def check_environment(self):
        """检查环境"""
        self.log("🔍 检查环境配置...")
        
        # 检查CUDA
        if not torch.cuda.is_available():
            raise RuntimeError("CUDA不可用")
        
        gpu_count = torch.cuda.device_count()
        self.log(f"✅ 检测到 {gpu_count} 张GPU")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            memory_gb = torch.cuda.get_device_properties(i).total_memory / 1024**3
            self.log(f"   GPU {i}: {gpu_name} ({memory_gb:.1f}GB)")
        
        # 检查项目目录
        if not os.path.exists(self.project_root):
            raise RuntimeError(f"项目目录不存在: {self.project_root}")
        
        os.chdir(self.project_root)
        self.log(f"✅ 工作目录: {self.project_root}")
        
        return True
    
    def setup_accelerate_config(self):
        """设置Accelerate配置"""
        self.log("⚙️ 设置Accelerate配置...")
        
        gpu_count = torch.cuda.device_count()
        config = {
            "compute_environment": "LOCAL_MACHINE",
            "distributed_type": "MULTI_GPU",
            "downcast_bf16": "no",
            "gpu_ids": "all",
            "machine_rank": 0,
            "main_training_function": "main",
            "mixed_precision": "bf16",
            "num_machines": 1,
            "num_processes": min(gpu_count, 2),  # 最多使用2张GPU
            "rdzv_backend": "static",
            "same_network": True,
            "use_cpu": False
        }
        
        with open("accelerate_config.yaml", "w") as f:
            import yaml
            yaml.dump(config, f, default_flow_style=False)
        
        self.log("✅ Accelerate配置已创建")
        return True
    
    def check_training_data(self):
        """检查训练数据"""
        self.log("📊 检查训练数据...")
        
        data_dir = "data/example_video_dataset"
        metadata_file = f"{data_dir}/metadata.csv"
        
        if not os.path.exists(data_dir):
            self.log("⚠️  训练数据目录不存在，创建示例数据...")
            os.makedirs(data_dir, exist_ok=True)
            
            # 创建示例metadata.csv
            with open(metadata_file, "w") as f:
                f.write("video_path,prompt\n")
                f.write("example_video.mp4,A beautiful example video\n")
            
            self.log("✅ 创建了示例数据集")
        else:
            self.log("✅ 训练数据目录存在")
        
        return True
    
    def run_training(self, num_epochs=5):
        """运行多卡训练"""
        self.log(f"🚀 开始多卡训练 ({num_epochs} epochs)...")
        
        # 设置环境变量
        env_vars = {
            "NCCL_TIMEOUT": "1800",
            "TOKENIZERS_PARALLELISM": "false"
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            self.log(f"设置环境变量: {key}={value}")
        
        # 训练命令
        train_cmd = f"""
        source /root/miniconda3/etc/profile.d/conda.sh && \
        conda activate {self.conda_env} && \
        accelerate launch --config_file accelerate_config.yaml \
          examples/wanvideo/model_training/train.py \
          --dataset_base_path data/example_video_dataset \
          --dataset_metadata_path data/example_video_dataset/metadata.csv \
          --height 480 --width 832 --dataset_repeat 1 \
          --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
          --learning_rate 1e-4 --num_epochs {num_epochs} --gradient_accumulation_steps 1 \
          --remove_prefix_in_ckpt "pipe.dit." \
          --output_path "{self.lora_output_dir}" \
          --lora_base_model "dit" --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
          --lora_rank 8 --extra_inputs "input_image" --mixed_precision "bf16"
        """
        
        try:
            self.run_command(train_cmd)
            self.log("✅ 训练完成")
            return True
        except subprocess.CalledProcessError:
            self.log("❌ 训练失败", "ERROR")
            return False
    
    def check_training_results(self):
        """检查训练结果"""
        self.log("🔍 检查训练结果...")
        
        if not os.path.exists(self.lora_output_dir):
            self.log("❌ 训练输出目录不存在", "ERROR")
            return False
        
        # 检查epoch文件
        epochs = []
        for i in range(10):
            epoch_file = f"{self.lora_output_dir}/epoch-{i}.safetensors"
            if os.path.exists(epoch_file):
                file_size = os.path.getsize(epoch_file) / 1024**2
                epochs.append((i, file_size))
        
        if epochs:
            self.log(f"✅ 找到 {len(epochs)} 个训练检查点:")
            for epoch, size in epochs:
                self.log(f"   epoch-{epoch}.safetensors ({size:.1f}MB)")
            
            latest_epoch = max(epochs, key=lambda x: x[0])
            self.log(f"🎯 最新检查点: epoch-{latest_epoch[0]} ({latest_epoch[1]:.1f}MB)")
            return True
        else:
            self.log("❌ 未找到训练检查点", "ERROR")
            return False
    
    def run_inference(self):
        """运行推理"""
        self.log("🎬 开始推理...")
        
        # 使用您提供的final_working_inference.py脚本
        inference_cmd = f"""
        source /root/miniconda3/etc/profile.d/conda.sh && \
        conda activate {self.conda_env} && \
        python final_working_inference.py
        """
        
        try:
            self.run_command(inference_cmd)
            self.log("✅ 推理完成")
            return True
        except subprocess.CalledProcessError:
            self.log("❌ 推理失败", "ERROR")
            return False
    
    def check_inference_results(self):
        """检查推理结果"""
        self.log("🔍 检查推理结果...")
        
        possible_outputs = [
            "final_lora_inference_epoch4.mp4",
            "final_lora_inference_epoch3.mp4",
            "final_lora_inference_epoch2.mp4",
            "final_lora_inference_epoch1.mp4",
            "final_lora_inference_epoch0.mp4"
        ]
        
        found_videos = []
        for video_file in possible_outputs:
            if os.path.exists(video_file):
                file_size = os.path.getsize(video_file) / 1024**2
                found_videos.append((video_file, file_size))
        
        if found_videos:
            self.log(f"✅ 找到 {len(found_videos)} 个生成的视频:")
            for video_file, size in found_videos:
                self.log(f"   {video_file} ({size:.1f}MB)")
            return True
        else:
            self.log("❌ 未找到生成的视频文件", "ERROR")
            return False
    
    def run_complete_pipeline(self, skip_training=False):
        """运行完整流水线"""
        self.log("🚀 开始Wan2.1-I2V-14B-480P完整流水线")
        self.log("=" * 60)
        
        try:
            # 1. 环境检查
            self.check_environment()
            
            # 2. 设置配置
            self.setup_accelerate_config()
            
            # 3. 检查数据
            self.check_training_data()
            
            # 4. 训练（可选跳过）
            if not skip_training:
                if not self.run_training():
                    return False
            
            # 5. 检查训练结果
            if not self.check_training_results():
                return False
            
            # 6. 运行推理
            if not self.run_inference():
                return False
            
            # 7. 检查推理结果
            if not self.check_inference_results():
                return False
            
            self.log("🎉 完整流水线执行成功!")
            return True
            
        except Exception as e:
            self.log(f"❌ 流水线执行失败: {e}", "ERROR")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Wan2.1-I2V-14B-480P 完整流水线")
    parser.add_argument("--skip-training", action="store_true", help="跳过训练，直接进行推理")
    parser.add_argument("--epochs", type=int, default=5, help="训练轮数")
    
    args = parser.parse_args()
    
    # 创建流水线实例
    pipeline = Wan2IVPipeline()
    
    # 运行完整流水线
    success = pipeline.run_complete_pipeline(skip_training=args.skip_training)
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 Wan2.1-I2V-14B-480P 流水线执行成功!")
        print("=" * 60)
        print("✅ 环境检查通过")
        print("✅ 配置文件创建")
        if not args.skip_training:
            print("✅ 多卡训练完成")
        print("✅ LoRA权重验证")
        print("✅ 推理执行成功")
        print("✅ 视频生成完成")
        print("\n🚀 您的完整微调到推理流程已成功运行!")
    else:
        print("\n❌ 流水线执行失败，请检查日志信息")
        sys.exit(1)

if __name__ == "__main__":
    main()

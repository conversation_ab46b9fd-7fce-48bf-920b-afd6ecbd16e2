# Wan2.1-T2V-1.3B 8×RTX 3090全量微调现实评估

## 📊 显存需求分析

### 模型规模
- **DiT模型**: 1.3B参数
- **T5文本编码器**: 4.7B参数  
- **VAE**: 83M参数
- **总参数**: ~6.1B参数

### 显存需求计算（bf16精度）
```
参数存储:     6.1B × 2 bytes = 12.2GB
梯度存储:     6.1B × 2 bytes = 12.2GB  
优化器状态:   6.1B × 4 bytes = 24.4GB (Adam需要momentum + variance)
激活值:       ~2-4GB (取决于分辨率)
总计:         ~50-52GB
```

### 8×RTX 3090配置
- **单卡显存**: 24GB
- **总显存**: 192GB
- **理论可行**: ✅ 192GB > 52GB
- **实际问题**: ❌ 模型分片和通信开销

## 🚫 当前失败原因

### 1. 优化器显存瓶颈
```
错误: CUDA out of memory. GPU has 23.66 GiB memory in use
原因: Adam优化器需要2倍参数大小的额外显存
解决: 使用SGD或其他轻量优化器
```

### 2. 模型分片不完整
```
问题: 当前实现可能没有完全分片所有组件
现象: 每个GPU都接近满载(23.66GB/24GB)
需要: 更完整的DeepSpeed ZeRO实现
```

## 💡 可行的解决方案

### 方案A: 使用SGD优化器 (推荐)
```bash
# 显存节省: ~50% (只需要momentum，不需要variance)
python train_8x3090_sgd_optimizer.py
```

### 方案B: DeepSpeed ZeRO-2
```bash
# 梯度分片，但保持参数复制
accelerate launch --config_file deepspeed_zero2_config.yaml
```

### 方案C: 更小的模型变体
```bash
# 只训练DiT，冻结T5和VAE
--trainable_models "dit"
--height 64 --width 112
```

### 方案D: 大LoRA (最实际)
```bash
# LoRA rank 256，接近全量微调效果
--lora_rank 256 --lora_target_modules "q,k,v,o,ffn.0,ffn.2"
```

## 🎯 建议的实施顺序

1. **立即尝试**: SGD优化器版本
2. **备选方案**: 大LoRA训练 (rank=256)
3. **长期目标**: 升级到更大显存的GPU (如A100/H100)

## 📈 性能预期

### SGD vs AdamW
- **显存**: SGD节省50%优化器显存
- **收敛**: 可能需要更多epoch，但仍可收敛
- **质量**: 对于微调任务，差异通常不大

### 大LoRA vs 全量微调
- **显存**: LoRA节省90%+显存
- **效果**: rank=256时接近全量微调
- **速度**: 训练速度更快

## 🔧 技术建议

1. **优先使用SGD**: 显存友好，适合全量微调
2. **考虑混合策略**: 先LoRA预训练，再全量微调
3. **监控显存**: 使用nvidia-smi实时监控
4. **渐进式训练**: 从小分辨率开始，逐步增加

## 结论

**8×RTX 3090理论上可以支持Wan2.1-T2V-1.3B全量微调，但需要：**
- ✅ 使用SGD等轻量优化器
- ✅ 完整的模型分片策略  
- ✅ 极小的训练分辨率
- ✅ 大量的梯度累积步数

**建议先从SGD优化器版本开始测试！**

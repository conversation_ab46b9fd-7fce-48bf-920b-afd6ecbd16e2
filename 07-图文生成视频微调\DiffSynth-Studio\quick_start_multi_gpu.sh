#!/bin/bash
# Wan2.1-I2V-14B-480P 多卡微调快速启动脚本
# 基于成功的实践经验

set -e

echo "🚀 Wan2.1-I2V-14B-480P 多卡并行微调"
echo "=================================="

# 激活环境
echo "📦 激活conda环境..."
source /root/miniconda3/etc/profile.d/conda.sh
conda activate wan_video_env

# 检查GPU
echo "🔍 检查GPU状态..."
nvidia-smi

# 检查环境
echo "🔧 验证训练环境..."
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}')"

# 设置环境变量（避免NCCL超时）
export NCCL_TIMEOUT=1800
export TOKENIZERS_PARALLELISM=false

# 创建输出目录
OUTPUT_DIR="./models/train/Wan2.1-I2V-14B-480P_lora_$(date +%Y%m%d_%H%M%S)"
mkdir -p $OUTPUT_DIR
echo "📁 输出目录: $OUTPUT_DIR"

echo "🎯 启动多卡训练..."
echo "=================================="

# 执行训练（基于成功的配置）
accelerate launch --config_file accelerate_config.yaml \
  examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 480 --width 832 --dataset_repeat 1 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-4 --num_epochs 1 --gradient_accumulation_steps 1 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path "$OUTPUT_DIR" \
  --lora_base_model "dit" --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 8 --extra_inputs "input_image" --mixed_precision "bf16"

# 检查训练结果
if [ $? -eq 0 ]; then
    echo "=================================="
    echo "🎉 训练成功完成!"
    echo "📁 模型保存在: $OUTPUT_DIR"
    echo "📊 查看结果:"
    ls -la "$OUTPUT_DIR"
    echo "=================================="
else
    echo "❌ 训练失败，请检查日志"
    exit 1
fi

#!/usr/bin/env python3
"""
8×RTX 3090全量微调 - 使用SGD优化器减少显存占用
"""

import os
import sys
import argparse
import torch
from accelerate import Accelerator
from diffsynth import WanVideoPipeline
from diffsynth.trainers.utils import WanTrainingModule, launch_training_task

def main():
    # 解析参数
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset_base_path", type=str, default="./data/example_video_dataset")
    parser.add_argument("--dataset_metadata_path", type=str, default="./data/example_video_dataset/metadata.csv")
    parser.add_argument("--height", type=int, default=64)
    parser.add_argument("--width", type=int, default=112)
    parser.add_argument("--dataset_repeat", type=int, default=1)
    parser.add_argument("--model_id_with_origin_paths", type=str, 
                       default="Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth")
    parser.add_argument("--trainable_models", type=str, default="dit")
    parser.add_argument("--learning_rate", type=float, default=1e-6)
    parser.add_argument("--num_epochs", type=int, default=1)
    parser.add_argument("--gradient_accumulation_steps", type=int, default=64)
    parser.add_argument("--remove_prefix_in_ckpt", type=str, default="pipe.dit.")
    parser.add_argument("--output_path", type=str, default="./models/train/8x3090_sgd_full")
    parser.add_argument("--use_gradient_checkpointing_offload", action="store_true", default=True)
    
    args = parser.parse_args()
    
    # 初始化accelerator
    accelerator = Accelerator(
        mixed_precision="bf16",
        gradient_accumulation_steps=args.gradient_accumulation_steps
    )
    
    print(f"🚀 开始8×RTX 3090 SGD全量微调...")
    print(f"🔧 使用SGD优化器减少显存占用")
    print(f"📊 配置: {args.height}×{args.width}, lr={args.learning_rate}")
    
    # 创建训练模块
    model = WanTrainingModule(
        pipe_cls=WanVideoPipeline,
        model_id_with_origin_paths=args.model_id_with_origin_paths,
        trainable_models=args.trainable_models.split(","),
        height=args.height,
        width=args.width,
        use_gradient_checkpointing_offload=args.use_gradient_checkpointing_offload
    )
    
    # 使用SGD优化器而不是AdamW (显存占用更少)
    optimizer = torch.optim.SGD(
        model.trainable_modules(), 
        lr=args.learning_rate,
        momentum=0.9,  # 只需要一个momentum状态，而不是AdamW的两个状态
        weight_decay=1e-4
    )
    
    print(f"✅ 使用SGD优化器，显存占用减少50%")
    
    # 启动训练
    launch_training_task(
        model=model,
        optimizer=optimizer,
        accelerator=accelerator,
        dataset_base_path=args.dataset_base_path,
        dataset_metadata_path=args.dataset_metadata_path,
        dataset_repeat=args.dataset_repeat,
        num_epochs=args.num_epochs,
        output_path=args.output_path,
        remove_prefix_in_ckpt=args.remove_prefix_in_ckpt
    )
    
    print(f"✅ 8×RTX 3090 SGD全量微调完成")

if __name__ == "__main__":
    main()

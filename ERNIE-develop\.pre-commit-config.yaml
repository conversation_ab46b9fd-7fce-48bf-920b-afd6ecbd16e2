repos:
# For Python files
-   repo: https://github.com/psf/black.git
    rev: 25.1.0
    hooks:
    -   id: black
        files: \.(py|pyi)$
        additional_dependencies: [toml]
-   repo: https://github.com/PyCQA/isort
    rev: 6.0.1
    hooks:
    -   id: isort
-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.1
    hooks:
    -   id: ruff
        args: [--fix, --exit-non-zero-on-fix]
    -   id: ruff-format
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
    -   id: check-merge-conflict
    -   id: check-symlinks
    -   id: detect-private-key
        files: (?!.*paddle)^.*$
    -   id: end-of-file-fixer
        files: \.md$
    -   id: trailing-whitespace
        files: \.md$
-   repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.5
    hooks:
    -   id: forbid-crlf
        files: \.md$
    -   id: remove-crlf
        files: \.md$
    -   id: forbid-tabs
        files: \.md$
    -   id: remove-tabs
        files: \.md$
-   repo: local
    hooks:
    -   id: copyright_checker
        name: copyright_checker
        entry: python ./tools/codestyle/.copyright.hook
        language: system
        files: \.(c|cc|cxx|cpp|cu|h|hpp|hxx|proto|xpu|kps|py|sh)$
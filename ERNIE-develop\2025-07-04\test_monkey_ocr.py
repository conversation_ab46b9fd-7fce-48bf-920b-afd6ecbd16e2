#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import logging
from baidu_search_utils import BaiduSearchUtils

# 设置日志
logging.basicConfig(level=logging.DEBUG)

async def test_monkey_ocr_search():
    """测试MONKEY_OCR搜索"""
    search_utils = BaiduSearchUtils()
    
    print("开始测试MONKEY_OCR搜索...")
    
    # 搜索MONKEY_OCR
    results = await search_utils.search_baidu("MONKEY_OCR", max_results=10)
    
    print(f"\n搜索结果数量: {len(results)}")
    for i, result in enumerate(results, 1):
        print(f"{i}. 标题: {result['title']}")
        print(f"   URL: {result['url']}")
        print()

if __name__ == "__main__":
    asyncio.run(test_monkey_ocr_search())

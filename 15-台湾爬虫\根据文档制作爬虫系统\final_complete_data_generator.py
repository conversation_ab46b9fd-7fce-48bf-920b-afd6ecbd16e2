#!/usr/bin/env python3
"""
最终完整数据生成器 - 使用已成功提取的厂商数据创建完整的JSON文件
"""

import json
from datetime import datetime

def create_final_complete_data():
    """创建最终的完整数据"""
    
    print("🎯 === 创建最终完整数据 ===")
    
    # 使用我们已经成功提取的厂商数据
    vendor_data = {
        "test_url": "https://web.pcc.gov.tw/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetail?pkAtmMain=NzAxNTM4ODk=",
        "bidder_count": 3,
        "extracted_vendor_count": 3,
        "vendors": [
            {
                "vendor_code": "12938805",
                "vendor_name": "鴻吉管理顧問股份有限公司",
                "is_winner": "是",
                "organization_type": "公司登記",
                "business_type": "其他",
                "vendor_address": "104 臺北市 中山區林森北路413號8樓之16",
                "vendor_phone": "(02) 25771572",
                "award_amount": "18,178,000元",
                "award_amount_chinese": "壹仟捌佰壹拾柒萬捌仟元",
                "winner_country": "中華民國(Republic of China (Taiwan))",
                "is_sme": "是",
                "performance_period": "112/01/01 － 112/12/31",
                "over_100_employees": "否"
            },
            {
                "vendor_code": "85061858",
                "vendor_name": "德力邦管理顧問有限公司",
                "is_winner": "否",
                "organization_type": "公司登記",
                "business_type": "",
                "vendor_address": "",
                "vendor_phone": "",
                "award_amount": "19,480,000元",
                "award_amount_chinese": "壹仟玖佰肆拾捌萬元",
                "winner_country": "",
                "is_sme": "",
                "performance_period": "",
                "over_100_employees": ""
            },
            {
                "vendor_code": "34877818",
                "vendor_name": "有限責任新北市原住民機關勞務勞動合作社",
                "is_winner": "否",
                "organization_type": "合作社",
                "business_type": "",
                "vendor_address": "",
                "vendor_phone": "",
                "award_amount": "18,947,952元",
                "award_amount_chinese": "壹仟捌佰玖拾肆萬柒仟玖佰伍拾貳元",
                "winner_country": "",
                "is_sme": "",
                "performance_period": "",
                "over_100_employees": ""
            }
        ]
    }
    
    # 构建完整的数据记录
    complete_record = {
        '序号': 1,
        '标案名称': '庫儲人力管理',
        '机关名称': '國防部',
        '标案类型': '決標公告',
        '招标日期': '111/11/02',
        '决标日期': '112/06/16',
        '详情页链接': vendor_data["test_url"],
        '爬取时间': datetime.now().isoformat(),
        
        # 机关详细信息
        '标案案号': 'EC12047L076',
        '机关代码': '3.5',
        '单位名称': '國防採購室',
        '机关地址': '104 臺北市 中山區 北安路409號',
        '联络人': '徐小姐',
        '联络电话': '(02) 85099473',
        '传真号码': '(02) 85099475',
        
        # 招标信息
        '招标方式': '公開招標',
        '决标方式': '最低標',
        '公告日期': '113/04/23',
        
        # 时间信息
        '开标时间': '111/11/23 14:00',
        '原公告日期': '111/11/02',
        
        # 金额信息
        '预算金额': '19,484,000元',
        '预算金额中文': '壹仟玖佰肆拾捌萬肆仟元',
        '采购金额级距': '查核金額以上未達巨額',
        '预算金额是否公开': '是',
        
        # 履约信息
        '履约地点': '臺東縣台東市(原住民地區)',
        '履约地区': '東部地區－全區',
        '是否受机关补助': '否',
        
        # 厂商信息
        '投标厂商数': vendor_data["bidder_count"],
        '厂商详情': vendor_data["vendors"],
        '得标厂商': '鴻吉管理顧問股份有限公司',
        '得标金额': '18,178,000元',
        
        # 标的分类
        '标的分类': '<勞務類> 97 其他服務'
    }
    
    # 创建完整数据列表
    complete_data = [complete_record]
    
    # 保存JSON文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_file = f'final_complete_procurement_data_{timestamp}.json'
    
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(complete_data, f, ensure_ascii=False, indent=2)
    
    print(f"💾 完整JSON数据已保存到: {json_file}")
    
    # 生成CSV文件
    csv_file = generate_complete_csv(complete_data, json_file.replace('.json', '.csv'))
    
    # 生成厂商详情CSV
    vendor_csv_file = generate_vendor_csv(complete_data, json_file.replace('.json', '_vendors.csv'))
    
    # 生成摘要CSV
    summary_csv_file = generate_summary_csv(complete_data, json_file.replace('.json', '_summary.csv'))
    
    # 显示数据摘要
    print_data_summary(complete_record)
    
    return complete_data, json_file

def generate_complete_csv(data, csv_file):
    """生成完整的CSV文件"""
    try:
        import csv
        
        # 排除复杂字段
        simple_fields = []
        for key in data[0].keys():
            if key not in ['厂商详情']:
                simple_fields.append(key)
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=simple_fields)
            writer.writeheader()
            
            for item in data:
                simple_item = {}
                for field in simple_fields:
                    value = item.get(field, '')
                    # 清理文本格式
                    if isinstance(value, str):
                        value = value.replace('\r\n\t\t\t\t', ' ').replace('\r\n', ' ').replace('\t', ' ')
                        value = ' '.join(value.split())  # 移除多余空格
                    simple_item[field] = value
                writer.writerow(simple_item)
        
        print(f"📊 完整CSV文件已生成: {csv_file}")
        return csv_file
        
    except Exception as e:
        print(f"⚠️ CSV生成失败: {str(e)}")
        return ""

def generate_vendor_csv(data, csv_file):
    """生成厂商详情CSV"""
    try:
        import csv
        
        vendor_records = []
        
        for item in data:
            vendors = item.get('厂商详情', [])
            if vendors:
                for vendor in vendors:
                    vendor_record = {
                        '序号': item.get('序号', ''),
                        '标案名称': item.get('标案名称', ''),
                        '标案案号': item.get('标案案号', ''),
                        '机关名称': item.get('机关名称', ''),
                        '厂商名称': vendor.get('vendor_name', ''),
                        '厂商代码': vendor.get('vendor_code', ''),
                        '是否得标': vendor.get('is_winner', ''),
                        '决标金额': vendor.get('award_amount', ''),
                        '决标金额中文': vendor.get('award_amount_chinese', ''),
                        '组织型态': vendor.get('organization_type', ''),
                        '厂商地址': vendor.get('vendor_address', ''),
                        '厂商电话': vendor.get('vendor_phone', ''),
                        '履约期间': vendor.get('performance_period', ''),
                        '是否为中小企业': vendor.get('is_sme', ''),
                        '得标厂商国别': vendor.get('winner_country', ''),
                        '雇用员工总人数是否超过100人': vendor.get('over_100_employees', '')
                    }
                    vendor_records.append(vendor_record)
        
        if vendor_records:
            with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                fieldnames = list(vendor_records[0].keys())
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(vendor_records)
            
            print(f"🏢 厂商详情CSV已生成: {csv_file}")
            print(f"📊 厂商记录数: {len(vendor_records)}")
            return csv_file
        
    except Exception as e:
        print(f"⚠️ 厂商CSV生成失败: {str(e)}")
        return ""

def generate_summary_csv(data, csv_file):
    """生成摘要CSV"""
    try:
        import csv
        
        # 核心字段
        core_fields = [
            '序号', '标案名称', '机关名称', '标案案号', '招标日期', '决标日期',
            '预算金额', '招标方式', '得标厂商', '得标金额', '投标厂商数'
        ]
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=core_fields)
            writer.writeheader()
            
            for item in data:
                summary_record = {}
                for field in core_fields:
                    summary_record[field] = item.get(field, '')
                writer.writerow(summary_record)
        
        print(f"📋 摘要CSV已生成: {csv_file}")
        return csv_file
        
    except Exception as e:
        print(f"⚠️ 摘要CSV生成失败: {str(e)}")
        return ""

def print_data_summary(record):
    """打印数据摘要"""
    print(f"\n📊 === 数据摘要 ===")
    print(f"标案名称: {record.get('标案名称', '')}")
    print(f"机关名称: {record.get('机关名称', '')}")
    print(f"标案案号: {record.get('标案案号', '')}")
    print(f"预算金额: {record.get('预算金额', '')}")
    print(f"招标方式: {record.get('招标方式', '')}")
    print(f"得标厂商: {record.get('得标厂商', '')}")
    print(f"得标金额: {record.get('得标金额', '')}")
    print(f"投标厂商数: {record.get('投标厂商数', 0)}")
    print(f"厂商详情数: {len(record.get('厂商详情', []))}")
    
    print(f"\n🏆 厂商信息:")
    vendors = record.get('厂商详情', [])
    for i, vendor in enumerate(vendors):
        status = "✅ 得标" if vendor.get('is_winner') == '是' else "❌ 未得标"
        print(f"  {i+1}. {vendor.get('vendor_name', '')} - {vendor.get('award_amount', '')} ({status})")

def main():
    """主函数"""
    print("🎯 === 最终完整数据生成器 ===")
    print("使用已成功提取的厂商数据创建完整的JSON文件")
    
    # 创建完整数据
    complete_data, json_file = create_final_complete_data()
    
    print(f"\n🎉 === 数据生成完成 ===")
    print(f"✅ 成功生成 {len(complete_data)} 笔完整数据")
    print(f"📁 生成的文件:")
    print(f"  1. {json_file} - 完整JSON数据")
    print(f"  2. {json_file.replace('.json', '.csv')} - 完整CSV数据")
    print(f"  3. {json_file.replace('.json', '_vendors.csv')} - 厂商详情CSV")
    print(f"  4. {json_file.replace('.json', '_summary.csv')} - 摘要CSV")
    
    print(f"\n💡 数据特点:")
    print(f"  - 包含完整的31个字段")
    print(f"  - 包含3个厂商的详细信息")
    print(f"  - 包含得标厂商和金额信息")
    print(f"  - 数据格式已清理，适合Excel打开")
    print(f"  - 可以作为完整数据结构的参考模板")

if __name__ == "__main__":
    main()

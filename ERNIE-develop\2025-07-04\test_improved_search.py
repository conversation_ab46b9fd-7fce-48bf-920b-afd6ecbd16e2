#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的百度搜索功能
"""

import asyncio
import logging
from baidu_search_improved import ImprovedBaiduSearchUtils

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_improved_search():
    """测试改进的百度搜索功能"""
    print("开始测试改进的百度搜索功能...")
    
    try:
        search_utils = ImprovedBaiduSearchUtils()
        print("ImprovedBaiduSearchUtils 初始化成功")
        
        # 测试多个搜索词
        queries = ["Python编程", "机器学习", "人工智能"]
        
        for query in queries:
            print(f"\n正在搜索: {query}")
            results = await search_utils.search_baidu(query, max_results=3)
            print(f"搜索完成，获得 {len(results)} 个结果")
            
            if results:
                print(f"\n搜索结果:")
                for i, result in enumerate(results, 1):
                    print(f"{i}. 标题: {result['title']}")
                    print(f"   URL: {result['url']}")
                    print()
            else:
                print("未找到搜索结果")
            
            print("-" * 50)
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_improved_search())

# Wan2.1-I2V-14B-480P 多卡微调详细文档

## 1. 概述

本文档提供了Wan2.1-I2V-14B-480P模型从环境搭建、数据准备、多卡微调到推理部署的完整端到端流程。基于最新版DiffSynth-Studio，专门针对图像到视频生成任务，包含所有必要的代码和详细步骤。

## 2. 完整流程概览

```mermaid
flowchart LR
    %% 主流程
    A[环境准备] --> B[模型下载]
    B --> C[I2V数据集准备]
    C --> D[多卡LoRA微调]
    D --> E[LoRA推理测试]

    %% 子流程详情
    subgraph S1 [" "]
        A1[Python 3.12<br/>DiffSynth-Studio<br/>I2V训练依赖包]
    end

    subgraph S2 [" "]
        B1[Wan2.1-I2V-14B-480P<br/>14.2GB基础模型<br/>4个核心组件]
    end

    subgraph S3 [" "]
        C1[官方示例数据集]
        C2[自定义I2V数据集]
        C1 -.-> C3[metadata.csv验证<br/>图像编码器配置]
        C2 -.-> C3
    end

    subgraph S4 [" "]
        D1[Accelerate多卡配置]
        D2[I2V LoRA参数调优]
        D3[5轮训练输出]
        D1 --> D2 --> D3
    end

    subgraph S5 [" "]
        E1[加载LoRA权重<br/>图像条件生成<br/>验证I2V效果]
    end

    %% 连接主流程和子流程
    A -.-> S1
    B -.-> S2
    C -.-> S3
    D -.-> S4
    E -.-> S5

    %% 样式定义
    classDef mainFlow fill:#4CAF50,stroke:#2E7D32,stroke-width:3px,color:#fff
    classDef subFlow fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    classDef subDetail fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    classDef option fill:#FFF3E0,stroke:#FF9800,stroke-width:1px

    class A,B,C,D,E mainFlow
    class A1,B1,E1,D1,D2,D3 subFlow
    class C3 subDetail
    class C1,C2 option

    %% 隐藏子图边框
    style S1 fill:none,stroke:none
    style S2 fill:none,stroke:none
    style S3 fill:none,stroke:none
    style S4 fill:none,stroke:none
    style S5 fill:none,stroke:none
```

## 2. 完整流程概览

```mermaid
flowchart LR
    %% 主流程
    A[环境准备] --> B[模型下载]
    B --> C[I2V数据集准备]
    C --> D[多卡LoRA微调]
    D --> E[LoRA推理测试]
    E --> F[权重合并]
    F --> G[合并模型测试]

    %% 子流程详情
    subgraph S1 [" "]
        A1[Python 3.12<br/>DiffSynth-Studio<br/>I2V训练依赖包]
    end

    subgraph S2 [" "]
        B1[Wan2.1-I2V-14B-480P<br/>14.2GB基础模型<br/>4个核心组件]
    end

    subgraph S3 [" "]
        C1[图像-视频对数据集]
        C2[自定义I2V数据集]
        C1 -.-> C3[metadata.csv验证<br/>图像编码器配置]
        C2 -.-> C3
    end

    subgraph S4 [" "]
        D1[Accelerate多卡配置]
        D2[I2V LoRA参数调优]
        D3[5轮训练输出]
        D1 --> D2 --> D3
    end

    subgraph S5 [" "]
        E1[加载LoRA权重<br/>图像条件生成<br/>验证I2V效果]
    end

    subgraph S6 [" "]
        F1[Alpha=0.8]
        F2[Alpha=1.0]
        F3[Alpha=1.2]
        F4[完整I2V合并模型]
        F1 --> F4
        F2 --> F4
        F3 --> F4
    end

    subgraph S7 [" "]
        G1[I2V效果对比评估<br/>选择最佳Alpha<br/>性能验证]
    end

    %% 连接主流程和子流程
    A -.-> S1
    B -.-> S2
    C -.-> S3
    D -.-> S4
    E -.-> S5
    F -.-> S6
    G -.-> S7

    %% 样式定义
    classDef mainFlow fill:#4CAF50,stroke:#2E7D32,stroke-width:3px,color:#fff
    classDef subFlow fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    classDef subDetail fill:#F3E5F5,stroke:#9C27B0,stroke-width:1px
    classDef option fill:#FFF3E0,stroke:#FF9800,stroke-width:1px

    class A,B,C,D,E,F,G,H mainFlow
    class A1,B1,E1,G1,H1,D1,D2,D3,F4 subFlow
    class C3 subDetail
    class C1,C2,F1,F2,F3 option

    %% 隐藏子图边框
    style S1 fill:none,stroke:none
    style S2 fill:none,stroke:none
    style S3 fill:none,stroke:none
    style S4 fill:none,stroke:none
    style S5 fill:none,stroke:none
    style S6 fill:none,stroke:none
    style S7 fill:none,stroke:none
```

## 3. 环境搭建代码

### 3.1 Conda环境创建

```bash
# 创建Python 3.12环境
conda create -n wan_i2v_env python=3.12 -y

# 激活环境
conda activate wan_i2v_env

# 验证Python版本
python --version  # 输出: Python 3.12.11
```

### 3.2 代理设置（可选）

```bash
# 启用代理（如需要）
source /root/sj-data/Script/SJ-proxy.sh && proxy_on
```

### 3.3 核心依赖安装

```bash
# 克隆最新版DiffSynth-Studio仓库
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 安装DiffSynth-Studio核心库
pip install -e .

# 安装训练相关依赖
pip install deepspeed peft

# 安装图像处理相关依赖（I2V特需）
pip install opencv-python pillow imageio imageio-ffmpeg

# 验证安装
python -c "
import torch
import diffsynth
import accelerate
import deepspeed
import peft
import cv2
from PIL import Image
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
print('✅ 所有依赖安装成功')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
print(f'GPU数量: {torch.cuda.device_count()}')
print(f'OpenCV版本: {cv2.__version__}')
"
```

## 4. 模型下载代码

### 4.1 基础模型下载

```bash
# 安装ModelScope CLI工具
pip install modelscope

# 下载Wan2.1-I2V-14B-480P基础模型
modelscope download --model Wan-AI/Wan2.1-I2V-14B-480P --local_dir ./models/Wan-AI/Wan2.1-I2V-14B-480P

# 查看下载的模型文件
ls -la models/Wan-AI/Wan2.1-I2V-14B-480P/

# 查看模型大小
du -sh models/Wan-AI/Wan2.1-I2V-14B-480P/
```

### 4.2 I2V模型文件验证

```bash
# 验证关键模型文件（I2V特有）
python -c "
import os
from pathlib import Path

model_path = Path('./models/Wan-AI/Wan2.1-I2V-14B-480P')
required_files = [
    'diffusion_pytorch_model.safetensors',
    'models_t5_umt5-xxl-enc-bf16.pth',
    'Wan2.1_VAE.pth',
    'models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth',  # I2V特有的图像编码器
    'config.json'
]

print('=== I2V模型文件验证 ===')
for filename in required_files:
    file_path = model_path / filename
    if file_path.exists():
        size_mb = file_path.stat().st_size / (1024*1024)
        print(f'✅ {filename}: {size_mb:.1f} MB')
    else:
        print(f'❌ {filename}: 缺失')

# 特别检查CLIP图像编码器
clip_path = model_path / 'models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth'
if clip_path.exists():
    print(f'✅ CLIP图像编码器验证通过')
else:
    print(f'❌ CLIP图像编码器缺失，I2V功能将无法正常工作')
"
```

## 5. I2V数据集准备代码

### 5.1 方案选择

#### 5.1.1 官方示例数据集（推荐新手）

```bash
# 创建数据目录
mkdir -p data

# 下载官方示例视频数据集
modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset

# 验证数据集结构
ls -la data/example_video_dataset/
# 输出:
# metadata.csv
# video1.mp4
# video2.mp4
# image1.jpg  # I2V需要的图像文件
# image2.jpg
```

#### 5.1.2 自定义I2V数据集（推荐进阶用户）

```bash
# 创建自定义I2V数据集目录结构
mkdir -p data/custom_i2v_dataset/{videos,images}

# I2V数据集目录结构
# data/custom_i2v_dataset/
# ├── metadata.csv
# ├── videos/
# │   ├── video_001.mp4
# │   ├── video_002.mp4
# │   └── ...
# └── images/
#     ├── image_001.jpg
#     ├── image_002.jpg
#     └── ...
```

### 5.2 自定义I2V数据集准备脚本

创建 `prepare_custom_i2v_dataset.py`：

```python
import os
import csv
import cv2
from pathlib import Path
from PIL import Image

def create_i2v_metadata_csv(dataset_path, image_folder="images", video_folder="videos"):
    """创建I2V任务的metadata.csv文件"""
    dataset_path = Path(dataset_path)
    image_folder_path = dataset_path / image_folder
    video_folder_path = dataset_path / video_folder

    if not image_folder_path.exists():
        print(f"❌ 图像文件夹 {image_folder_path} 不存在")
        return False

    if not video_folder_path.exists():
        print(f"❌ 视频文件夹 {video_folder_path} 不存在")
        return False

    # 获取所有图像和视频文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']

    image_files = []
    for ext in image_extensions:
        image_files.extend(image_folder_path.glob(f'*{ext}'))

    video_files = []
    for ext in video_extensions:
        video_files.extend(video_folder_path.glob(f'*{ext}'))

    if not image_files:
        print(f"❌ 在 {image_folder_path} 中未找到图像文件")
        return False

    if not video_files:
        print(f"❌ 在 {video_folder_path} 中未找到视频文件")
        return False

    # 创建图像-视频对映射
    pairs = []
    for i, (image_file, video_file) in enumerate(zip(sorted(image_files), sorted(video_files))):
        try:
            # 验证图像文件
            image = Image.open(image_file)
            image_width, image_height = image.size

            # 验证视频文件
            cap = cv2.VideoCapture(str(video_file))
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            video_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            video_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            cap.release()

            if frame_count > 0 and fps > 0:
                pairs.append({
                    'image_file': image_file,
                    'video_file': video_file,
                    'image_size': (image_width, image_height),
                    'video_size': (video_width, video_height),
                    'frame_count': frame_count,
                    'fps': fps
                })
                print(f"✅ 找到有效对 {i+1}: {image_file.name} -> {video_file.name}")
                print(f"   图像: {image_width}x{image_height}, 视频: {video_width}x{video_height}, {frame_count}帧")
            else:
                print(f"❌ 无效视频: {video_file.name}")

        except Exception as e:
            print(f"❌ 处理文件对时出错: {str(e)}")

    if not pairs:
        print(f"❌ 未找到有效的图像-视频对")
        return False

    # 创建metadata.csv
    metadata_path = dataset_path / "metadata.csv"

    with open(metadata_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['video', 'prompt', 'input_image'])  # I2V格式

        for i, pair in enumerate(pairs):
            image_relative = f"{image_folder}/{pair['image_file'].name}"
            video_relative = f"{video_folder}/{pair['video_file'].name}"

            # 示例描述文本（实际使用时需要替换为真实描述）
            sample_texts = [
                "一艘小船正勇敢地乘风破浪前行，蔚蓝的大海波涛汹涌",
                "一只可爱的小猫在花园里玩耍，阳光洒在毛发上",
                "夕阳西下，海浪拍打着岩石，天空呈现出美丽的橙红色",
                "一朵花在微风中轻柔摇摆，花瓣晶莹剔透",
                "雨滴落在湖面上，泛起层层涟漪，远山如黛"
            ]

            text = sample_texts[i % len(sample_texts)]
            writer.writerow([video_relative, text, image_relative])

    print(f"✅ 创建I2V metadata.csv成功，包含 {len(pairs)} 个图像-视频对")
    return True

def main():
    dataset_path = "./data/custom_i2v_dataset"

    # 创建数据集目录
    os.makedirs(dataset_path, exist_ok=True)
    os.makedirs(f"{dataset_path}/images", exist_ok=True)
    os.makedirs(f"{dataset_path}/videos", exist_ok=True)

    print("📁 自定义I2V数据集目录已创建")
    print("请将图像文件放入 data/custom_i2v_dataset/images/ 文件夹中")
    print("请将对应的视频文件放入 data/custom_i2v_dataset/videos/ 文件夹中")
    print("然后运行此脚本生成metadata.csv文件")

    # 如果文件夹中有文件，则生成metadata.csv
    image_folder = Path(dataset_path) / "images"
    video_folder = Path(dataset_path) / "videos"

    if any(image_folder.iterdir()) and any(video_folder.iterdir()):
        create_i2v_metadata_csv(dataset_path)
    else:
        print("images或videos文件夹为空，请先添加文件")

if __name__ == "__main__":
    main()
```

### 5.3 I2V数据集验证代码

创建 `validate_i2v_dataset.py`：

```python
# 验证I2V数据集完整性
import pandas as pd
import os
from PIL import Image
import cv2

def validate_i2v_dataset(dataset_path):
    """验证I2V数据集完整性"""
    metadata_path = os.path.join(dataset_path, "metadata.csv")

    if not os.path.exists(metadata_path):
        print(f"❌ metadata.csv 不存在: {metadata_path}")
        return False

    # 读取元数据
    df = pd.read_csv(metadata_path)
    print(f"I2V数据集包含 {len(df)} 个图像-视频对")
    print("前5个样本:")
    print(df.head())

    # 验证文件存在性和格式
    missing_files = []
    valid_pairs = 0

    for _, row in df.iterrows():
        video_path = os.path.join(dataset_path, row['video'])
        image_path = os.path.join(dataset_path, row['input_image'])

        # 检查视频文件
        if os.path.exists(video_path):
            try:
                cap = cv2.VideoCapture(video_path)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                cap.release()

                if frame_count > 0 and fps > 0:
                    print(f"✅ 视频: {row['video']} ({frame_count}帧, {fps:.1f}fps)")
                else:
                    print(f"❌ 无效视频: {row['video']}")
                    missing_files.append(row['video'])
                    continue
            except:
                print(f"❌ 视频读取失败: {row['video']}")
                missing_files.append(row['video'])
                continue
        else:
            print(f"❌ 视频不存在: {row['video']}")
            missing_files.append(row['video'])
            continue

        # 检查图像文件
        if os.path.exists(image_path):
            try:
                image = Image.open(image_path)
                width, height = image.size
                print(f"✅ 图像: {row['input_image']} ({width}x{height})")
                valid_pairs += 1
            except:
                print(f"❌ 图像读取失败: {row['input_image']}")
                missing_files.append(row['input_image'])
        else:
            print(f"❌ 图像不存在: {row['input_image']}")
            missing_files.append(row['input_image'])

    if missing_files:
        print(f"\n❌ 缺少 {len(missing_files)} 个文件")
        return False
    else:
        print(f"\n✅ I2V数据集验证通过，共 {valid_pairs} 个有效的图像-视频对")
        return True

# 验证官方数据集
print("=== 验证官方示例数据集 ===")
validate_i2v_dataset("./data/example_video_dataset")

# 验证自定义数据集（如果存在）
custom_dataset_path = "./data/custom_i2v_dataset"
if os.path.exists(custom_dataset_path):
    print("\n=== 验证自定义I2V数据集 ===")
    validate_i2v_dataset(custom_dataset_path)
```

### 5.4 数据集准备执行

```bash
# 方案1：使用官方示例数据集
python validate_i2v_dataset.py

# 方案2：准备自定义I2V数据集
# 1. 将图像文件放入 data/custom_i2v_dataset/images/
# 2. 将对应的视频文件放入 data/custom_i2v_dataset/videos/
# 3. 运行准备脚本
python prepare_custom_i2v_dataset.py
# 4. 验证数据集
python validate_i2v_dataset.py
```

## 6. 多卡I2V LoRA微调代码

### 6.1 Accelerate多卡配置

创建 `accelerate_config.yaml`：

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all                    # 使用所有GPU，或指定如 "0,1,2,3"
machine_rank: 0
main_training_function: main
mixed_precision: bf16           # 推荐使用bf16混合精度
num_machines: 1
num_processes: 4                # I2V-14B推荐4卡训练
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

```bash
# 初始化accelerate配置
accelerate config --config_file accelerate_config.yaml

# 验证配置
accelerate env
```

### 6.2 I2V LoRA训练脚本

创建 `train_i2v_lora.sh`：

```bash
#!/bin/bash

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 数据集和模型路径
DATASET_BASE_PATH="data/example_video_dataset"  # 或 "data/custom_i2v_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"  # 对应调整
OUTPUT_PATH="./models/train/Wan2.1-I2V-14B-480P_lora"

echo "🚀 开始Wan2.1-I2V-14B-480P LoRA训练..."

accelerate launch --config_file accelerate_config.yaml examples/wanvideo/model_training/train.py \
  --dataset_base_path ${DATASET_BASE_PATH} \
  --dataset_metadata_path ${DATASET_METADATA_PATH} \
  --height 480 \
  --width 832 \
  --dataset_repeat 100 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path ${OUTPUT_PATH} \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 32 \
  --extra_inputs "input_image"

echo "✅ I2V LoRA训练完成！模型保存在: ${OUTPUT_PATH}"
```

### 6.3 训练执行

```bash
# 给脚本执行权限
chmod +x train_i2v_lora.sh

# 启动训练
./train_i2v_lora.sh
```

### 6.4 训练监控

```bash
# 查看训练进度
tail -f models/train/Wan2.1-I2V-14B-480P_lora/training.log

# 查看GPU使用情况
watch -n 1 nvidia-smi

# 查看训练输出文件
ls -la models/train/Wan2.1-I2V-14B-480P_lora/
```

## 7. I2V LoRA推理测试代码

在权重合并之前，先测试I2V LoRA权重是否正常工作。

### 7.1 I2V LoRA推理测试脚本

创建 `test_i2v_lora_inference.py`：

```python
#!/usr/bin/env python3
"""
I2V LoRA推理测试脚本
测试微调后的I2V LoRA权重是否正常工作
"""

import torch
import os
from PIL import Image
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def test_i2v_lora_inference():
    """测试I2V LoRA推理功能"""

    # 配置参数
    lora_path = "./models/train/Wan2.1-I2V-14B-480P_lora/pytorch_lora_weights.safetensors"
    test_image_path = "./data/example_video_dataset/image1.jpg"  # 测试图像

    # 检查LoRA文件是否存在
    if not os.path.exists(lora_path):
        print(f"❌ I2V LoRA权重文件不存在: {lora_path}")
        # 尝试查找其他可能的LoRA文件
        lora_dir = os.path.dirname(lora_path)
        if os.path.exists(lora_dir):
            lora_files = [f for f in os.listdir(lora_dir) if f.endswith('.safetensors')]
            if lora_files:
                lora_path = os.path.join(lora_dir, lora_files[0])
                print(f"🔄 使用找到的LoRA文件: {lora_path}")
            else:
                print("❌ 未找到任何LoRA权重文件")
                return False
        else:
            return False

    # 检查测试图像是否存在
    if not os.path.exists(test_image_path):
        print(f"❌ 测试图像不存在: {test_image_path}")
        # 尝试查找其他图像文件
        dataset_dir = "./data/example_video_dataset"
        if os.path.exists(dataset_dir):
            image_files = [f for f in os.listdir(dataset_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            if image_files:
                test_image_path = os.path.join(dataset_dir, image_files[0])
                print(f"🔄 使用找到的图像文件: {test_image_path}")
            else:
                print("❌ 未找到任何图像文件")
                return False
        else:
            return False

    print(f"✅ 找到I2V LoRA权重文件: {lora_path}")
    print(f"文件大小: {os.path.getsize(lora_path) / (1024*1024):.2f} MB")
    print(f"✅ 找到测试图像: {test_image_path}")

    try:
        print("\n=== 开始加载I2V模型 ===")

        # 创建I2V推理管道（包含CLIP图像编码器）
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cuda",
            model_configs=[
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
                ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
            ],
        )
        print("✅ I2V基础模型加载成功")

        # 加载LoRA权重
        print("\n=== 加载I2V LoRA权重 ===")
        pipe.load_lora(pipe.dit, lora_path, alpha=1.0)
        print("✅ I2V LoRA权重加载成功")

        # 启用显存管理
        pipe.enable_vram_management()
        print("✅ 显存管理启用成功")

        # 加载测试图像
        print(f"\n=== 加载测试图像 ===")
        input_image = Image.open(test_image_path)
        print(f"图像尺寸: {input_image.size}")
        print(f"图像模式: {input_image.mode}")

        # 生成测试视频
        test_prompts = [
            "一艘小船正勇敢地乘风破浪前行，蔚蓝的大海波涛汹涌",
            "基于输入图像生成动态视频，展现自然的美感",
            "图像中的场景开始动起来，微风轻拂，生机盎然"
        ]

        negative_prompt = "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量"

        for i, prompt in enumerate(test_prompts):
            print(f"\n=== 生成I2V测试视频 {i+1}/{len(test_prompts)} ===")
            print(f"提示词: {prompt}")

            video = pipe(
                prompt=prompt,
                negative_prompt=negative_prompt,
                input_image=input_image,  # I2V特有参数
                seed=i + 42,
                tiled=True,
            )

            output_path = f"i2v_lora_test_{i+1:02d}.mp4"
            save_video(video, output_path, fps=15, quality=5)

            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / (1024*1024)
                print(f"✅ I2V视频生成成功: {output_path} ({file_size:.2f} MB)")
            else:
                print(f"❌ I2V视频生成失败: {output_path}")

        print(f"\n✅ I2V LoRA推理测试完成！")
        return True

    except Exception as e:
        print(f"❌ I2V推理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== Wan2.1-I2V-14B-480P LoRA推理测试 ===")

    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return

    print(f"✅ CUDA可用，设备数量: {torch.cuda.device_count()}")
    print(f"当前设备: {torch.cuda.get_device_name()}")

    # 运行测试
    success = test_i2v_lora_inference()

    if success:
        print("\n🎉 I2V LoRA推理测试成功！")
    else:
        print("\n❌ I2V LoRA推理测试失败，请检查训练权重。")

if __name__ == "__main__":
    main()
```

### 7.2 运行I2V LoRA推理测试

```bash
python test_i2v_lora_inference.py
```

## 8. 快速开始示例

以下是Wan2.1-I2V-14B-480P模型的基础推理示例：

```python
# 创建快速推理脚本 quick_start_i2v.py
import torch
from PIL import Image
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from modelscope import dataset_snapshot_download

def quick_start_i2v():
    """I2V模型快速开始示例"""

    # 加载I2V模型
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cuda",
        model_configs=[
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
        ],
    )
    pipe.enable_vram_management()

    # 下载示例图像
    dataset_snapshot_download(
        dataset_id="DiffSynth-Studio/examples_in_diffsynth",
        local_dir="./",
        allow_file_pattern=f"data/examples/wan/input_image.jpg"
    )
    image = Image.open("data/examples/wan/input_image.jpg")

    # 图像到视频生成
    video = pipe(
        prompt="一艘小船正勇敢地乘风破浪前行。蔚蓝的大海波涛汹涌，白色的浪花拍打着船身，但小船毫不畏惧，坚定地驶向远方。",
        negative_prompt="色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量",
        input_image=image,  # I2V特有参数
        seed=0,
        tiled=True
    )
    save_video(video, "i2v_quick_start_output.mp4", fps=15, quality=5)
    print("✅ I2V快速开始示例完成！视频已保存到: i2v_quick_start_output.mp4")

if __name__ == "__main__":
    quick_start_i2v()
```

## 9. 完整流程总结

### 9.1 一键执行脚本

创建 `run_complete_i2v_pipeline.sh`：

```bash
#!/bin/bash

echo "=== Wan2.1-I2V-14B-480P 完整训练和推理流程 ==="

# 步骤1: 环境检查
echo "步骤1: 环境检查..."
python -c "
import torch
import diffsynth
print(f'✅ PyTorch: {torch.__version__}')
print(f'✅ CUDA: {torch.cuda.is_available()}')
print(f'✅ GPU数量: {torch.cuda.device_count()}')
"

# 步骤2: 模型下载验证
echo "步骤2: 模型下载验证..."
python -c "
import os
from pathlib import Path
model_path = Path('./models/Wan-AI/Wan2.1-I2V-14B-480P')
if model_path.exists():
    print('✅ I2V基础模型已下载')
else:
    print('❌ I2V基础模型未下载，请先运行模型下载步骤')
"

# 步骤3: 数据集准备和验证
echo "步骤3: I2V数据集验证..."
python validate_i2v_dataset.py

# 步骤4: LoRA训练
echo "步骤4: I2V LoRA训练..."
./train_i2v_lora.sh

# 步骤5: LoRA推理测试
echo "步骤5: I2V LoRA推理测试..."
python test_i2v_lora_inference.py

echo "✅ I2V完整流程执行完成！"
```

### 9.2 执行完整流程

```bash
# 给脚本执行权限
chmod +x run_complete_i2v_pipeline.sh

# 执行完整流程
./run_complete_i2v_pipeline.sh
```

### 9.3 预期输出文件

完成后将生成以下文件：
- `models/Wan-AI/Wan2.1-I2V-14B-480P/`: 基础模型文件
- `models/train/Wan2.1-I2V-14B-480P_lora/`: LoRA训练权重
- `i2v_lora_test_*.mp4`: LoRA推理测试视频
- `i2v_quick_start_output.mp4`: 快速开始示例视频

### 9.4 性能预期

**训练性能**：
- 4卡训练时间：约2-4小时（5个epoch）
- 显存需求：每卡约16-20GB
- LoRA权重大小：约100-200MB

**推理性能**：
- 单个视频生成时间：约30-60秒
- 输出视频规格：480P, 15fps, 2-8秒
- 推理显存需求：约12-16GB

## 10. 参数说明

### 10.1 训练参数详解

| 参数 | 值 | 说明 |
|------|----|----|
| `--height` | 480 | I2V-480P模型的标准高度 |
| `--width` | 832 | I2V-480P模型的标准宽度 |
| `--learning_rate` | 1e-4 | LoRA训练学习率 |
| `--num_epochs` | 5 | 训练轮数 |
| `--lora_rank` | 32 | LoRA权重矩阵的秩 |
| `--lora_target_modules` | "q,k,v,o,ffn.0,ffn.2" | LoRA应用的模块 |
| `--extra_inputs` | "input_image" | **I2V特有**：启用图像条件输入 |
| `--dataset_repeat` | 100 | 数据集重复次数 |

### 10.2 I2V模型组件说明

I2V-14B-480P模型包含以下核心组件：

1. **DiT模型** (`diffusion_pytorch_model*.safetensors`)：扩散变换器，负责视频生成
2. **T5文本编码器** (`models_t5_umt5-xxl-enc-bf16.pth`)：处理文本提示词
3. **VAE** (`Wan2.1_VAE.pth`)：视频编码解码器
4. **CLIP图像编码器** (`models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth`)：**I2V特有**，处理输入图像

### 10.3 推理参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `prompt` | - | 文本提示词，描述期望的视频内容 |
| `negative_prompt` | - | 负面提示词，描述不希望出现的内容 |
| `input_image` | - | **I2V特有**：输入图像，作为视频生成的条件 |
| `seed` | 0 | 随机种子，控制生成的随机性 |
| `tiled` | True | 是否使用分块推理，节省显存 |

## 11. 常见问题

### Q1: 训练时显存不足怎么办？
**A**: 可以尝试以下方法：
- 减少GPU数量：修改 `CUDA_VISIBLE_DEVICES=0,1`
- 降低分辨率：`--height 360 --width 640`
- 减少LoRA rank：`--lora_rank 16`
- 启用梯度检查点：`--gradient_checkpointing`
- 减少batch size

### Q2: I2V模型与T2V模型的区别？
**A**: 主要区别：
- **输入条件**：I2V需要输入图像作为条件：`input_image=image`
- **模型组件**：I2V包含CLIP图像编码器组件
- **训练参数**：训练时需要 `--extra_inputs "input_image"` 参数
- **数据格式**：数据集需要包含 `input_image` 列
- **应用场景**：I2V适合图像动画化，T2V适合纯文本生成

### Q3: 如何评估I2V模型效果？
**A**: 可以从以下方面评估：
- **时间一致性**：生成的视频帧之间是否连贯
- **图像保真度**：生成视频是否保持输入图像的主要特征
- **动作合理性**：生成的动作是否符合物理规律和提示词描述
- **视觉质量**：分辨率、清晰度、色彩等
- **语义一致性**：生成内容是否与文本提示词匹配

### Q4: 训练数据集有什么要求？
**A**: 数据集要求：
- **图像-视频对应**：每个视频都要有对应的关键帧图像
- **质量要求**：视频质量良好，无明显瑕疵，图像清晰
- **内容匹配**：文本描述准确，与视频内容和图像内容匹配
- **格式规范**：建议视频长度2-8秒，帧率15-30fps，分辨率480P或以上
- **数量建议**：至少100对图像-视频对，推荐1000对以上

### Q5: LoRA训练失败怎么办？
**A**: 排查步骤：
1. **检查数据集**：确保metadata.csv格式正确，文件路径存在
2. **检查模型下载**：确保所有4个模型组件都已下载
3. **检查显存**：确保有足够的GPU显存
4. **检查依赖**：确保所有依赖包版本正确
5. **查看日志**：检查训练日志中的具体错误信息

## 12. 总结

本文档提供了Wan2.1-I2V-14B-480P模型的完整微调流程，主要包括：

### 12.1 核心特点

- **专业的I2V功能**：支持从单张图像生成高质量480P视频
- **高效的LoRA训练**：相比全量微调，显存需求更低，训练速度更快
- **完整的工作流程**：从环境搭建到模型推理的端到端解决方案
- **详细的代码实现**：包含所有必要的脚本和配置文件
- **官方标准**：严格按照DiffSynth-Studio官方文档规范

### 12.2 主要步骤回顾

1. **环境搭建**：Python 3.12 + DiffSynth-Studio + I2V特需依赖
2. **模型下载**：Wan2.1-I2V-14B-480P (14.2GB) + 4个核心组件验证
3. **数据准备**：官方示例数据集或自定义I2V数据集
4. **多卡训练**：Accelerate配置 + I2V LoRA训练脚本
5. **推理测试**：LoRA推理 + 图像条件生成验证

### 12.3 预期效果

训练完成后，您将获得：
- **定制化的I2V模型**：适应您的特定数据域和应用场景
- **图像动画化能力**：能够根据输入图像和文本提示生成相应视频
- **保真度和动态性平衡**：保持输入图像主要特征的同时，添加合理的动态效果
- **高质量输出**：480P分辨率的流畅视频，适合多种应用场景

### 12.4 应用场景

- **内容创作**：为静态图片添加动态效果
- **广告制作**：产品图片动画化展示
- **教育培训**：历史图片、科学图表动画化
- **娱乐应用**：个人照片动画化处理
- **艺术创作**：艺术作品动态化表现

🎉 **恭喜！您已掌握了Wan2.1-I2V-14B-480P模型的完整微调方法！**

---

**文档版本**: v2.0
**更新日期**: 2025-07-19
**适用框架**: DiffSynth-Studio (最新版)
**模型版本**: Wan-AI/Wan2.1-I2V-14B-480P

## 4. 模型训练

### Step 1: 准备数据集

根据官方文档，I2V模型的数据集需要包含图像-视频对。数据集结构如下：

```
data/i2v_dataset/
├── metadata.csv
├── video1.mp4
├── video2.mp4
├── image1.jpg
└── image2.jpg
```

其中 `metadata.csv` 为元数据列表，I2V模型需要包含 `input_image` 列：

```csv
video,prompt,input_image
video1.mp4,"一艘小船正勇敢地乘风破浪前行，蔚蓝的大海波涛汹涌",image1.jpg
video2.mp4,"一只可爱的小猫在花园里玩耍，阳光洒在毛发上",image2.jpg
```

#### 下载官方示例数据集

```bash
# 下载官方示例数据集
modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
```

#### 自定义I2V数据集准备

如果您需要使用自定义数据集，请按照以下格式组织：

```python
# 创建自定义I2V数据集脚本 prepare_i2v_dataset.py
import os
import csv
from pathlib import Path

def create_i2v_metadata(dataset_path):
    """为I2V模型创建metadata.csv"""
    dataset_path = Path(dataset_path)

    # 扫描视频和图像文件
    video_files = list(dataset_path.glob("*.mp4")) + list(dataset_path.glob("*.avi"))
    image_files = list(dataset_path.glob("*.jpg")) + list(dataset_path.glob("*.png"))

    if not video_files or not image_files:
        print("❌ 请确保数据集目录中包含视频文件和对应的图像文件")
        return

    # 创建metadata.csv
    metadata_path = dataset_path / "metadata.csv"
    with open(metadata_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['video', 'prompt', 'input_image'])  # I2V必需的列

        # 假设视频和图像按顺序对应
        for i, (video_file, image_file) in enumerate(zip(video_files, image_files)):
            prompt = f"基于输入图像生成的动态视频内容 {i+1}"  # 请替换为实际的描述
            writer.writerow([video_file.name, prompt, image_file.name])

    print(f"✅ 创建I2V metadata.csv成功，包含 {min(len(video_files), len(image_files))} 个样本")

if __name__ == "__main__":
    create_i2v_metadata("./data/example_video_dataset")
```

**重要说明**：
- I2V模型需要在metadata.csv中包含 `input_image` 列
- 图像文件将作为视频生成的条件输入
- 支持的图像格式：jpg, jpeg, png, webp
- 支持的视频格式：mp4, avi, mov, wmv, mkv, flv, webm

### Step 2: 配置训练参数

根据官方训练脚本，创建I2V模型的LoRA训练配置：

```bash
# 创建训练脚本 train_i2v_lora.sh
#!/bin/bash

export CUDA_VISIBLE_DEVICES=0,1,2,3
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

accelerate launch --config_file examples/wanvideo/model_training/multi_gpu_config.yaml examples/wanvideo/model_training/train.py \
  --dataset_base_path data/example_video_dataset \
  --dataset_metadata_path data/example_video_dataset/metadata.csv \
  --height 480 \
  --width 832 \
  --dataset_repeat 100 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path ./models/train/Wan2.1-I2V-14B-480P_lora \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 32 \
  --extra_inputs "input_image"
```

**关键参数说明**：
- `--extra_inputs "input_image"`：I2V模型的关键参数，启用图像条件输入
- `--height 480 --width 832`：I2V-480P模型的标准分辨率
- `models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth`：I2V特有的CLIP图像编码器

### Step 3: 开始训练

使用官方提供的多GPU配置文件进行训练：

```bash
# 给训练脚本执行权限
chmod +x train_i2v_lora.sh

# 启动I2V LoRA训练
./train_i2v_lora.sh
```

训练过程中会自动：
1. 下载Wan2.1-I2V-14B-480P基础模型
2. 加载图像-视频对数据集
3. 使用LoRA方法微调DiT模型
4. 保存训练权重到 `./models/train/Wan2.1-I2V-14B-480P_lora/`

**训练监控**：
- 训练日志会显示损失值变化
- 每个epoch结束后会保存检查点
- 建议使用tensorboard监控训练进度

```bash
# 可选：启动tensorboard监控
tensorboard --logdir ./models/train/Wan2.1-I2V-14B-480P_lora/logs
```

## 5. 模型推理

### 基础推理

训练完成后，可以使用LoRA权重进行推理：

```python
# 创建推理脚本 inference_i2v_lora.py
import torch
from PIL import Image
from diffsynth import save_video
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

# 加载基础I2V模型
pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cuda",
    model_configs=[
        ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
        ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
        ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
        ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
    ],
)

# 加载LoRA权重
pipe.load_lora(pipe.dit, "./models/train/Wan2.1-I2V-14B-480P_lora/pytorch_lora_weights.safetensors", alpha=1.0)
pipe.enable_vram_management()

# 加载输入图像
image = Image.open("path/to/your/input_image.jpg")

# 生成视频
video = pipe(
    prompt="一艘小船正勇敢地乘风破浪前行，蔚蓝的大海波涛汹涌，白色的浪花拍打着船身",
    negative_prompt="色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量",
    input_image=image,  # I2V特有参数
    seed=0,
    tiled=True
)

save_video(video, "i2v_lora_output.mp4", fps=15, quality=5)
```

### 批量推理

```bash
# 运行推理脚本
python inference_i2v_lora.py
```

## 6. 参数说明

### 训练参数详解

| 参数 | 值 | 说明 |
|------|----|----|
| `--height` | 480 | I2V-480P模型的标准高度 |
| `--width` | 832 | I2V-480P模型的标准宽度 |
| `--learning_rate` | 1e-4 | LoRA训练学习率 |
| `--num_epochs` | 5 | 训练轮数 |
| `--lora_rank` | 32 | LoRA权重矩阵的秩 |
| `--lora_target_modules` | "q,k,v,o,ffn.0,ffn.2" | LoRA应用的模块 |
| `--extra_inputs` | "input_image" | **I2V特有**：启用图像条件输入 |

### 模型组件说明

I2V-14B-480P模型包含以下核心组件：

1. **DiT模型** (`diffusion_pytorch_model*.safetensors`)：扩散变换器，负责视频生成
2. **T5文本编码器** (`models_t5_umt5-xxl-enc-bf16.pth`)：处理文本提示词
3. **VAE** (`Wan2.1_VAE.pth`)：视频编码解码器
4. **CLIP图像编码器** (`models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth`)：**I2V特有**，处理输入图像

## 7. 常见问题

### Q1: 训练时显存不足怎么办？
**A**: 可以尝试以下方法：
- 减少GPU数量：修改 `CUDA_VISIBLE_DEVICES=0,1`
- 降低分辨率：`--height 360 --width 640`
- 减少LoRA rank：`--lora_rank 16`
- 启用梯度检查点

### Q2: I2V模型与T2V模型的区别？
**A**: 主要区别：
- I2V需要输入图像作为条件：`input_image=image`
- I2V包含CLIP图像编码器组件
- 训练时需要 `--extra_inputs "input_image"` 参数
- 数据集需要包含 `input_image` 列

### Q3: 如何评估I2V模型效果？
**A**: 可以从以下方面评估：
- **时间一致性**：生成的视频帧之间是否连贯
- **图像保真度**：生成视频是否保持输入图像的主要特征
- **动作合理性**：生成的动作是否符合物理规律和提示词描述
- **视觉质量**：分辨率、清晰度、色彩等

### Q4: 训练数据集有什么要求？
**A**: 数据集要求：
- 图像-视频对应关系明确
- 视频质量良好，无明显瑕疵
- 文本描述准确，与视频内容匹配
- 建议视频长度2-8秒，帧率15-30fps

## 8. 总结

本文档提供了Wan2.1-I2V-14B-480P模型的完整微调流程，主要包括：

1. **环境安装**：DiffSynth-Studio框架安装
2. **数据准备**：I2V数据集格式和准备方法
3. **模型训练**：LoRA微调配置和执行
4. **推理测试**：训练后的模型推理验证

### 关键特点

- **专业的I2V功能**：支持从单张图像生成高质量480P视频
- **高效的LoRA训练**：相比全量微调，显存需求更低，训练速度更快
- **完整的工作流程**：从数据准备到模型推理的端到端解决方案
- **官方标准**：严格按照DiffSynth-Studio官方文档规范

### 预期效果

训练完成后，您将获得：
- 定制化的I2V模型，适应您的特定数据域
- 能够根据输入图像和文本提示生成相应视频的能力
- 保持输入图像主要特征的同时，添加合理的动态效果

🎉 **恭喜！您已掌握了Wan2.1-I2V-14B-480P模型的完整微调方法！**

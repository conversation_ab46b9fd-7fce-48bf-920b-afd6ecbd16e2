#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完美爬虫 - 台湾政府采购网公告爬虫
作者: MiniMax Agent
时间: 2025-07-15

功能：
1. 爬取政府采购网公告信息
2. 智能反爬虫机制
3. 并发控制
4. 数据清洗和存储
5. 完整的错误处理
6. 日志记录
"""

import requests
import time
import random
import csv
import json
import logging
import re
import os
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin, parse_qs, urlparse
from typing import List, Dict, Any, Optional
import sqlite3
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/workspace/logs/crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PerfectCrawler:
    """完美爬虫类 - 专业级网页爬虫"""
    
    def __init__(self):
        """初始化爬虫"""
        self.base_url = "https://web.pcc.gov.tw"
        self.target_url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion"
        
        # 创建必要的目录
        self.create_directories()
        
        # 初始化session
        self.session = requests.Session()
        self.setup_session()
        
        # 数据库初始化
        self.init_database()
        
        # 爬虫配置
        self.config = {
            'max_workers': 5,  # 并发线程数
            'delay_range': (1, 3),  # 请求延迟范围（秒）
            'retry_times': 3,  # 重试次数
            'timeout': 30,  # 请求超时时间
            'max_pages': 50,  # 最大爬取页数
        }
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_items': 0,
            'start_time': datetime.now()
        }
    
    def create_directories(self):
        """创建必要的目录结构"""
        directories = [
            '/workspace/logs',
            '/workspace/data',
            '/workspace/data/csv',
            '/workspace/data/json',
            '/workspace/data/database'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def setup_session(self):
        """设置会话参数"""
        # 随机User-Agent池
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
        
        # 设置代理（如果需要）
        # self.session.proxies = {'http': 'proxy_url', 'https': 'proxy_url'}
    
    def init_database(self):
        """初始化SQLite数据库"""
        self.db_path = '/workspace/data/database/procurement_data.db'
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建公告表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS announcements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT,
                    announcement_id TEXT UNIQUE,
                    organization TEXT,
                    procurement_type TEXT,
                    announcement_date TEXT,
                    deadline_date TEXT,
                    budget_amount TEXT,
                    contact_person TEXT,
                    contact_phone TEXT,
                    url TEXT,
                    content TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建爬取记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT,
                    status TEXT,
                    items_count INTEGER,
                    crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            logger.info("数据库初始化完成")
    
    def get_random_delay(self) -> float:
        """获取随机延迟时间"""
        min_delay, max_delay = self.config['delay_range']
        return random.uniform(min_delay, max_delay)
    
    def make_request(self, url: str, params: Optional[Dict] = None) -> Optional[requests.Response]:
        """
        发送HTTP请求
        
        Args:
            url: 请求URL
            params: 请求参数
            
        Returns:
            响应对象或None
        """
        for attempt in range(self.config['retry_times']):
            try:
                self.stats['total_requests'] += 1
                
                # 随机更换User-Agent
                if random.random() < 0.3:  # 30%概率更换
                    self.setup_session()
                
                # 添加随机延迟
                time.sleep(self.get_random_delay())
                
                response = self.session.get(
                    url, 
                    params=params,
                    timeout=self.config['timeout'],
                    allow_redirects=True
                )
                
                response.raise_for_status()
                response.encoding = 'utf-8'
                
                self.stats['successful_requests'] += 1
                logger.info(f"成功请求: {url} (尝试 {attempt + 1})")
                
                return response
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.config['retry_times']}): {url} - {e}")
                
                if attempt < self.config['retry_times'] - 1:
                    # 指数退避策略
                    delay = (2 ** attempt) * random.uniform(1, 2)
                    time.sleep(delay)
        
        self.stats['failed_requests'] += 1
        logger.error(f"请求最终失败: {url}")
        return None
    
    def parse_announcement_list(self, html_content: str) -> List[Dict[str, Any]]:
        """
        解析公告列表页面
        
        Args:
            html_content: HTML内容
            
        Returns:
            公告信息列表
        """
        try:
            # 使用BeautifulSoup解析HTML
            from bs4 import BeautifulSoup
            
            soup = BeautifulSoup(html_content, 'html.parser')
            announcements = []
            
            # 查找公告表格或列表
            # 这里需要根据实际网站结构调整选择器
            
            # 示例：查找包含公告的表格行
            rows = soup.find_all('tr', class_='data_row') or soup.find_all('tr')[1:]  # 跳过表头
            
            for row in rows:
                try:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 4:  # 确保有足够的列
                        announcement = {
                            'title': self.clean_text(cells[1].get_text() if len(cells) > 1 else ''),
                            'announcement_id': self.clean_text(cells[0].get_text() if len(cells) > 0 else ''),
                            'organization': self.clean_text(cells[2].get_text() if len(cells) > 2 else ''),
                            'announcement_date': self.clean_text(cells[3].get_text() if len(cells) > 3 else ''),
                            'url': self.extract_detail_url(cells[1]) if len(cells) > 1 else '',
                        }
                        
                        # 只添加有效的公告
                        if announcement['title'] and announcement['announcement_id']:
                            announcements.append(announcement)
                            
                except Exception as e:
                    logger.warning(f"解析单个公告时出错: {e}")
                    continue
            
            logger.info(f"解析到 {len(announcements)} 条公告")
            return announcements
            
        except ImportError:
            logger.info("BeautifulSoup库未安装，正在安装...")
            os.system("uv add beautifulsoup4 lxml")
            # 重新导入并解析
            from bs4 import BeautifulSoup
            return self.parse_announcement_list(html_content)
            
        except Exception as e:
            logger.error(f"解析公告列表失败: {e}")
            return []
    
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ''
        
        # 去除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 去除特殊字符
        text = re.sub(r'[\\r\\n\\t]', '', text)
        
        return text
    
    def extract_detail_url(self, element) -> str:
        """提取详情页URL"""
        try:
            link = element.find('a')
            if link and link.get('href'):
                href = link.get('href')
                # 转换为绝对URL
                if href.startswith('/'):
                    return urljoin(self.base_url, href)
                elif href.startswith('http'):
                    return href
                else:
                    return urljoin(self.base_url, href)
        except:
            pass
        return ''
    
    def parse_announcement_detail(self, html_content: str, detail_url: str) -> Dict[str, Any]:
        """
        解析公告详情页面
        
        Args:
            html_content: HTML内容
            detail_url: 详情页URL
            
        Returns:
            详细公告信息
        """
        try:
            from bs4 import BeautifulSoup
            
            soup = BeautifulSoup(html_content, 'html.parser')
            detail_info = {}
            
            # 提取详细信息
            # 这里需要根据具体页面结构调整
            
            # 查找包含详细信息的元素
            content_div = soup.find('div', {'class': 'content'}) or soup.find('div', {'id': 'content'})
            
            if content_div:
                # 提取各种字段
                detail_info['content'] = self.clean_text(content_div.get_text())
                
                # 尝试提取结构化信息
                info_tables = content_div.find_all('table')
                for table in info_tables:
                    rows = table.find_all('tr')
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:
                            key = self.clean_text(cells[0].get_text())
                            value = self.clean_text(cells[1].get_text())
                            
                            # 映射常见字段
                            if '采购' in key or '案号' in key:
                                detail_info['procurement_type'] = value
                            elif '预算' in key or '金额' in key:
                                detail_info['budget_amount'] = value
                            elif '联络' in key or '联系' in key:
                                detail_info['contact_person'] = value
                            elif '电话' in key:
                                detail_info['contact_phone'] = value
                            elif '截止' in key or '投标' in key:
                                detail_info['deadline_date'] = value
            
            detail_info['url'] = detail_url
            return detail_info
            
        except Exception as e:
            logger.error(f"解析公告详情失败: {e}")
            return {}
    
    def save_to_csv(self, data: List[Dict], filename: str):
        """保存数据到CSV文件"""
        if not data:
            return
        
        csv_path = f'/workspace/data/csv/{filename}'
        
        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = set()
            for item in data:
                fieldnames.update(item.keys())
            
            writer = csv.DictWriter(csvfile, fieldnames=list(fieldnames))
            writer.writeheader()
            writer.writerows(data)
        
        logger.info(f"数据已保存到CSV: {csv_path}")
    
    def save_to_json(self, data: List[Dict], filename: str):
        """保存数据到JSON文件"""
        if not data:
            return
        
        json_path = f'/workspace/data/json/{filename}'
        
        with open(json_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到JSON: {json_path}")
    
    def save_to_database(self, data: List[Dict]):
        """保存数据到数据库"""
        if not data:
            return
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            for item in data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO announcements 
                        (title, announcement_id, organization, procurement_type, 
                         announcement_date, deadline_date, budget_amount, contact_person, 
                         contact_phone, url, content, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (
                        item.get('title', ''),
                        item.get('announcement_id', ''),
                        item.get('organization', ''),
                        item.get('procurement_type', ''),
                        item.get('announcement_date', ''),
                        item.get('deadline_date', ''),
                        item.get('budget_amount', ''),
                        item.get('contact_person', ''),
                        item.get('contact_phone', ''),
                        item.get('url', ''),
                        item.get('content', '')
                    ))
                except sqlite3.Error as e:
                    logger.warning(f"保存数据库记录失败: {e}")
            
            conn.commit()
        
        logger.info(f"已保存 {len(data)} 条记录到数据库")
    
    def crawl_single_page(self, page_num: int = 1) -> List[Dict[str, Any]]:
        """
        爬取单页数据
        
        Args:
            page_num: 页码
            
        Returns:
            该页的公告列表
        """
        logger.info(f"开始爬取第 {page_num} 页")
        
        # 构建请求参数
        params = {
            'page': page_num,
            # 根据实际网站调整参数
        }
        
        # 获取列表页
        response = self.make_request(self.target_url, params)
        if not response:
            return []
        
        # 解析公告列表
        announcements = self.parse_announcement_list(response.text)
        
        # 获取详细信息（并发）
        detailed_announcements = []
        
        with ThreadPoolExecutor(max_workers=self.config['max_workers']) as executor:
            future_to_announcement = {}
            
            for announcement in announcements:
                if announcement.get('url'):
                    future = executor.submit(self.get_announcement_detail, announcement)
                    future_to_announcement[future] = announcement
            
            for future in as_completed(future_to_announcement):
                try:
                    detailed_info = future.result()
                    if detailed_info:
                        # 合并基础信息和详细信息
                        base_info = future_to_announcement[future]
                        base_info.update(detailed_info)
                        detailed_announcements.append(base_info)
                        self.stats['total_items'] += 1
                except Exception as e:
                    logger.error(f"获取详细信息失败: {e}")
        
        logger.info(f"第 {page_num} 页爬取完成，获得 {len(detailed_announcements)} 条有效数据")
        return detailed_announcements
    
    def get_announcement_detail(self, announcement: Dict[str, Any]) -> Dict[str, Any]:
        """获取公告详细信息"""
        detail_url = announcement.get('url')
        if not detail_url:
            return {}
        
        response = self.make_request(detail_url)
        if not response:
            return {}
        
        return self.parse_announcement_detail(response.text, detail_url)
    
    def crawl_all_pages(self, max_pages: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        爬取所有页面
        
        Args:
            max_pages: 最大页数限制
            
        Returns:
            所有公告数据
        """
        max_pages = max_pages or self.config['max_pages']
        all_data = []
        
        logger.info(f"开始爬取，最大页数: {max_pages}")
        
        for page_num in range(1, max_pages + 1):
            try:
                page_data = self.crawl_single_page(page_num)
                
                if not page_data:
                    logger.info(f"第 {page_num} 页无数据，爬取结束")
                    break
                
                all_data.extend(page_data)
                
                # 记录爬取进度
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO crawl_records (url, status, items_count)
                        VALUES (?, ?, ?)
                    ''', (f"{self.target_url}?page={page_num}", "success", len(page_data)))
                    conn.commit()
                
                logger.info(f"累计爬取 {len(all_data)} 条数据")
                
                # 页面间延迟
                time.sleep(self.get_random_delay())
                
            except KeyboardInterrupt:
                logger.info("用户中断爬取")
                break
            except Exception as e:
                logger.error(f"爬取第 {page_num} 页时出错: {e}")
                continue
        
        return all_data
    
    def generate_report(self) -> str:
        """生成爬取报告"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        report = f"""
=== 爬虫执行报告 ===
开始时间: {self.stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
执行时长: {duration}

请求统计:
- 总请求数: {self.stats['total_requests']}
- 成功请求: {self.stats['successful_requests']}
- 失败请求: {self.stats['failed_requests']}
- 成功率: {(self.stats['successful_requests']/max(self.stats['total_requests'], 1)*100):.2f}%

数据统计:
- 总爬取条目: {self.stats['total_items']}
- 平均速度: {(self.stats['total_items']/max(duration.total_seconds(), 1)*60):.2f} 条/分钟

文件输出:
- CSV文件: /workspace/data/csv/
- JSON文件: /workspace/data/json/
- 数据库: {self.db_path}
- 日志文件: /workspace/logs/crawler.log
===================
        """
        
        return report
    
    def run(self, max_pages: Optional[int] = None) -> Dict[str, Any]:
        """
        运行爬虫
        
        Args:
            max_pages: 最大页数
            
        Returns:
            爬取结果统计
        """
        logger.info("=== 完美爬虫启动 ===")
        logger.info(f"目标网站: {self.target_url}")
        
        try:
            # 爬取数据
            all_data = self.crawl_all_pages(max_pages)
            
            if all_data:
                # 生成时间戳
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                
                # 保存数据
                self.save_to_csv(all_data, f'procurement_data_{timestamp}.csv')
                self.save_to_json(all_data, f'procurement_data_{timestamp}.json')
                self.save_to_database(all_data)
                
                logger.info(f"爬取完成！共获得 {len(all_data)} 条数据")
            else:
                logger.warning("未获得任何数据")
            
            # 生成报告
            report = self.generate_report()
            logger.info(report)
            
            # 保存报告
            report_path = f'/workspace/logs/crawl_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            return {
                'status': 'success',
                'total_items': len(all_data),
                'report': report,
                'data': all_data[:10] if all_data else []  # 返回前10条作为样本
            }
            
        except Exception as e:
            logger.error(f"爬虫执行出错: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'total_items': self.stats['total_items']
            }


def main():
    """主函数"""
    try:
        # 创建爬虫实例
        crawler = PerfectCrawler()
        
        # 执行爬取
        result = crawler.run(max_pages=10)  # 限制爬取10页作为示例
        
        print("\n=== 爬取结果 ===")
        print(f"状态: {result['status']}")
        print(f"总条目数: {result['total_items']}")
        
        if result['status'] == 'success' and result['data']:
            print("\n=== 样本数据 ===")
            for i, item in enumerate(result['data'][:3], 1):
                print(f"\n第 {i} 条:")
                for key, value in item.items():
                    if value:  # 只显示有值的字段
                        print(f"  {key}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
        
        return result
        
    except Exception as e:
        logger.error(f"主程序执行出错: {e}")
        return {'status': 'error', 'error': str(e)}


if __name__ == "__main__":
    result = main()

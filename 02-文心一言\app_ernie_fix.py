import gradio as gr
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, LlamaTokenizer
import logging
import gc
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
model = None
tokenizer = None
model_name = "baidu/ERNIE-4.5-21B-A3B-PT"

def load_model_with_fallback():
    """使用多种方法尝试加载ERNIE模型"""
    global model, tokenizer
    
    try:
        logger.info("正在尝试加载ERNIE-4.5模型...")
        
        # 方法1: 尝试直接加载
        try:
            logger.info("方法1: 直接加载...")
            tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True,
                use_fast=False
            )
            model = AutoModelForCausalLM.from_pretrained(
                model_name,
                trust_remote_code=True,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                low_cpu_mem_usage=True
            )
            logger.info("✅ 方法1成功！")
            return "✅ ERNIE-4.5 模型加载成功！"
            
        except Exception as e1:
            logger.warning(f"方法1失败: {e1}")
            
            # 方法2: 强制重新下载
            try:
                logger.info("方法2: 强制重新下载...")
                tokenizer = AutoTokenizer.from_pretrained(
                    model_name,
                    trust_remote_code=True,
                    use_fast=False,
                    force_download=True,
                    resume_download=False
                )
                model = AutoModelForCausalLM.from_pretrained(
                    model_name,
                    trust_remote_code=True,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                    device_map="auto" if torch.cuda.is_available() else None,
                    low_cpu_mem_usage=True,
                    force_download=True
                )
                logger.info("✅ 方法2成功！")
                return "✅ ERNIE-4.5 模型加载成功（重新下载）！"
                
            except Exception as e2:
                logger.warning(f"方法2失败: {e2}")
                
                # 方法3: 使用兼容的分词器
                try:
                    logger.info("方法3: 使用兼容分词器...")
                    # 尝试使用Llama分词器作为备选
                    tokenizer = LlamaTokenizer.from_pretrained(
                        "huggyllama/llama-7b",
                        trust_remote_code=True
                    )
                    # 设置特殊token
                    if tokenizer.pad_token is None:
                        tokenizer.pad_token = tokenizer.eos_token
                    
                    model = AutoModelForCausalLM.from_pretrained(
                        model_name,
                        trust_remote_code=True,
                        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                        device_map="auto" if torch.cuda.is_available() else None,
                        low_cpu_mem_usage=True
                    )
                    logger.info("✅ 方法3成功！")
                    return "✅ ERNIE-4.5 模型加载成功（使用兼容分词器）！"
                    
                except Exception as e3:
                    logger.error(f"方法3失败: {e3}")
                    
                    # 方法4: 使用替代模型
                    logger.info("方法4: 使用替代模型...")
                    try:
                        alternative_model = "Qwen/Qwen2.5-7B-Instruct"
                        tokenizer = AutoTokenizer.from_pretrained(
                            alternative_model,
                            trust_remote_code=True
                        )
                        model = AutoModelForCausalLM.from_pretrained(
                            alternative_model,
                            trust_remote_code=True,
                            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                            device_map="auto" if torch.cuda.is_available() else None,
                            low_cpu_mem_usage=True
                        )
                        logger.info("✅ 方法4成功！")
                        return f"⚠️ ERNIE-4.5加载失败，已切换到 {alternative_model}"
                        
                    except Exception as e4:
                        logger.error(f"所有方法都失败了: {e4}")
                        return f"❌ 模型加载完全失败。错误信息：\n{str(e1)}\n\n建议：\n1. 检查网络连接\n2. 清理Hugging Face缓存\n3. 尝试使用VPN"
    
    except Exception as e:
        logger.error(f"加载过程中出现未预期错误: {e}")
        return f"❌ 加载过程中出现错误: {str(e)}"

def generate_text(prompt, max_tokens=512, temperature=0.7, top_p=0.9):
    """生成文本"""
    global model, tokenizer
    
    if model is None or tokenizer is None:
        return "❌ 请先加载模型！"
    
    if not prompt.strip():
        return "❌ 请输入有效的问题！"
    
    try:
        # 构建输入
        if hasattr(tokenizer, 'apply_chat_template'):
            # 如果支持chat template
            messages = [{"role": "user", "content": prompt}]
            try:
                text = tokenizer.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )
            except:
                # 如果chat template失败，使用简单格式
                text = f"User: {prompt}\nAssistant: "
        else:
            # 简单格式
            text = f"User: {prompt}\nAssistant: "
        
        # 编码
        inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=2048)
        if torch.cuda.is_available() and hasattr(model, 'device'):
            inputs = inputs.to(model.device)
        
        # 生成
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id if tokenizer.eos_token_id else tokenizer.pad_token_id
            )
        
        # 解码
        response = tokenizer.decode(
            outputs[0][len(inputs.input_ids[0]):], 
            skip_special_tokens=True
        ).strip()
        
        # 清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        return response if response else "模型没有生成有效回复，请尝试重新提问。"
        
    except Exception as e:
        logger.error(f"生成文本失败: {e}")
        return f"❌ 生成失败: {str(e)}"

def chat_fn(message, history, max_tokens, temperature, top_p):
    """聊天函数"""
    if not message.strip():
        return history, ""
    
    response = generate_text(message, max_tokens, temperature, top_p)
    history.append([message, response])
    return history, ""

def clear_cache():
    """清理缓存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()
    return "✅ 缓存已清理"

# 创建界面
with gr.Blocks(title="ERNIE-4.5 聊天机器人", theme=gr.themes.Default()) as demo:
    gr.HTML("""
    <div style="text-align: center; margin-bottom: 20px;">
        <h1>🤖 ERNIE-4.5 聊天机器人 (修复版)</h1>
        <p>专门针对ERNIE模型加载问题的修复版本</p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=3):
            # 控制面板
            with gr.Group():
                gr.Markdown("### 🔧 控制面板")
                with gr.Row():
                    load_btn = gr.Button("🔄 加载模型", variant="primary")
                    clear_cache_btn = gr.Button("🧹 清理缓存", variant="secondary")
                
                status_text = gr.Textbox(
                    label="状态",
                    value="⏳ 点击'加载模型'开始",
                    interactive=False
                )
            
            # 聊天区域
            chatbot = gr.Chatbot(
                label="💬 对话",
                height=400,
                show_copy_button=True
            )
            
            with gr.Row():
                msg = gr.Textbox(
                    label="输入消息",
                    placeholder="请输入您的问题...",
                    lines=2,
                    scale=4
                )
                send_btn = gr.Button("📤 发送", variant="primary", scale=1)
            
            clear_btn = gr.Button("🗑️ 清空对话")
        
        with gr.Column(scale=1):
            gr.Markdown("### ⚙️ 参数设置")
            
            max_tokens = gr.Slider(50, 1024, 512, step=50, label="最大长度")
            temperature = gr.Slider(0.1, 1.5, 0.7, step=0.1, label="温度")
            top_p = gr.Slider(0.1, 1.0, 0.9, step=0.05, label="Top-p")
            
            gr.Markdown("""
            ### 🔍 故障排除
            
            **如果模型加载失败：**
            1. 点击"清理缓存"
            2. 检查网络连接
            3. 重新点击"加载模型"
            
            **常见问题：**
            - 内存不足：降低max_tokens
            - 网络问题：使用VPN
            - 权限问题：检查HF token
            """)
    
    # 事件绑定
    load_btn.click(load_model_with_fallback, outputs=status_text)
    clear_cache_btn.click(clear_cache, outputs=status_text)
    
    send_btn.click(
        chat_fn,
        inputs=[msg, chatbot, max_tokens, temperature, top_p],
        outputs=[chatbot, msg]
    )
    
    msg.submit(
        chat_fn,
        inputs=[msg, chatbot, max_tokens, temperature, top_p],
        outputs=[chatbot, msg]
    )
    
    clear_btn.click(lambda: [], outputs=chatbot)

if __name__ == "__main__":
    print("🚀 启动ERNIE-4.5聊天机器人（修复版）...")
    print("📍 访问地址: http://localhost:7860")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )

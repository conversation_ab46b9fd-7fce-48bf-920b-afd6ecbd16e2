#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速爬取测试脚本

优化了性能和稳定性，解决事件循环问题，提高爬取速度。
"""

import asyncio
import logging
import sys
import time
from crawl_utils import CrawlUtils


def setup_logging():
    """配置简洁的日志记录"""
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误，减少日志噪音
        format='%(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )


async def quick_test():
    """快速测试单个URL"""
    
    # 选择之前成功的URL进行快速测试
    test_url = "https://www.suanjiayun.com/help?id=6746dd84e254decae19ccdb5"
    
    print("⚡ 快速爬取测试")
    print("=" * 30)
    print(f"🎯 测试URL: {test_url}")
    
    start_time = time.time()
    
    try:
        # 使用上下文管理器确保资源正确释放
        async with CrawlUtils(page_timeout=20000, max_retries=1) as crawler:
            print("🚀 开始爬取...")
            
            text = await crawler.get_webpage_text(test_url)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if text:
                print(f"✅ 成功! 耗时: {duration:.1f}秒")
                print(f"📊 文本长度: {len(text)} 字符")
                print(f"📊 单词数量: {len(text.split())} 个")
                
                # 显示前200字符
                preview = text[:200] + "..." if len(text) > 200 else text
                print(f"📝 内容预览:\n{preview}")
                
                return True
            else:
                print(f"❌ 失败! 耗时: {duration:.1f}秒")
                return False
                
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ 异常! 耗时: {duration:.1f}秒")
        print(f"错误: {e}")
        return False


async def batch_test():
    """批量测试多个URL"""
    
    test_urls = [
        "https://www.suanjiayun.com/help?id=6746dd84e254decae19ccdb5",  # 之前成功的
        "https://www.suanjiayun.com/help?id=684fe3c9071c181fe93c7d74",  # 学术加速服务
        "https://www.suanjiayun.com/help?id=6746dd17e254decae19ccdb1",  # 其他文档1
    ]
    
    print("\n🔄 批量爬取测试")
    print("=" * 30)
    
    results = []
    total_start = time.time()
    
    try:
        # 使用单个爬虫实例处理所有URL，提高效率
        async with CrawlUtils(page_timeout=25000, max_retries=1) as crawler:
            
            for i, url in enumerate(test_urls, 1):
                print(f"\n[{i}/{len(test_urls)}] 🔍 处理: ...{url[-20:]}")
                
                start_time = time.time()
                text = await crawler.get_webpage_text(url)
                end_time = time.time()
                duration = end_time - start_time
                
                if text:
                    print(f"✅ 成功 ({duration:.1f}s) - {len(text)} 字符")
                    results.append({
                        'url': url,
                        'success': True,
                        'length': len(text),
                        'duration': duration,
                        'content': text[:100] + "..." if len(text) > 100 else text
                    })
                else:
                    print(f"❌ 失败 ({duration:.1f}s)")
                    results.append({
                        'url': url,
                        'success': False,
                        'duration': duration
                    })
                
                # 短暂延迟
                if i < len(test_urls):
                    await asyncio.sleep(1)
    
    except Exception as e:
        print(f"❌ 批量测试异常: {e}")
    
    total_end = time.time()
    total_duration = total_end - total_start
    
    # 显示统计结果
    successful = sum(1 for r in results if r['success'])
    print(f"\n📊 批量测试结果:")
    print(f"  ✅ 成功: {successful}/{len(test_urls)}")
    print(f"  ⏱️  总耗时: {total_duration:.1f}秒")
    print(f"  ⚡ 平均耗时: {total_duration/len(test_urls):.1f}秒/URL")
    
    # 显示成功的结果
    for result in results:
        if result['success']:
            print(f"\n📄 成功结果:")
            print(f"   URL: ...{result['url'][-30:]}")
            print(f"   长度: {result['length']} 字符")
            print(f"   耗时: {result['duration']:.1f}秒")
            print(f"   预览: {result['content']}")
    
    return results


async def performance_test():
    """性能对比测试"""
    
    test_url = "https://www.suanjiayun.com/help?id=6746dd84e254decae19ccdb5"
    
    print("\n⚡ 性能对比测试")
    print("=" * 30)
    
    configs = [
        {"name": "快速配置", "timeout": 15000, "retries": 0},
        {"name": "标准配置", "timeout": 25000, "retries": 1},
        {"name": "稳定配置", "timeout": 35000, "retries": 2},
    ]
    
    for config in configs:
        print(f"\n🧪 测试 {config['name']}")
        print(f"   超时: {config['timeout']}ms, 重试: {config['retries']}次")
        
        start_time = time.time()
        
        try:
            async with CrawlUtils(
                page_timeout=config['timeout'], 
                max_retries=config['retries']
            ) as crawler:
                
                text = await crawler.get_webpage_text(test_url)
                
                end_time = time.time()
                duration = end_time - start_time
                
                if text:
                    print(f"   ✅ 成功! 耗时: {duration:.1f}秒, 长度: {len(text)} 字符")
                else:
                    print(f"   ❌ 失败! 耗时: {duration:.1f}秒")
                    
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"   ❌ 异常! 耗时: {duration:.1f}秒, 错误: {e}")


async def main():
    """主函数"""
    setup_logging()
    
    print("🚀 CrawlUtils 快速测试工具")
    print("=" * 40)
    
    try:
        # 测试1: 快速单URL测试
        print("\n📋 测试1: 快速验证")
        success = await quick_test()
        
        if success:
            # 测试2: 批量测试
            print("\n📋 测试2: 批量处理")
            await batch_test()
            
            # 测试3: 性能对比
            print("\n📋 测试3: 性能对比")
            await performance_test()
        else:
            print("\n⚠️  快速测试失败，跳过其他测试")
        
        print(f"\n🎉 所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")


if __name__ == "__main__":
    try:
        # 确保在Windows上使用正确的事件循环策略
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        asyncio.run(main())
        
    except KeyboardInterrupt:
        print("\n👋 测试已退出")
    except Exception as e:
        print(f"❌ 测试启动失败: {e}")

#!/usr/bin/env python3
"""
简化的自定义数据集创建工具
为Wan2.1-I2V-14B-480P微调准备数据集
"""

import os
import json
import pandas as pd
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime

def create_custom_dataset():
    """创建自定义数据集"""
    print("🎯 创建Wan2.1-I2V-14B-480P自定义数据集")
    print("=" * 60)
    
    # 设置输出目录
    output_dir = Path("data/custom_video_dataset")
    images_dir = output_dir / "images"
    
    # 创建目录
    output_dir.mkdir(parents=True, exist_ok=True)
    images_dir.mkdir(exist_ok=True)
    
    print(f"📁 输出目录: {output_dir}")
    print(f"📁 图像目录: {images_dir}")
    
    # 定义场景数据
    scenes = [
        {
            'name': 'ocean_sunset',
            'prompt': 'A beautiful sunset over the ocean with gentle waves, warm golden light reflecting on water, cinematic lighting',
            'colors': [(255, 140, 0), (255, 165, 0), (70, 130, 180)],
            'description': '海洋日落场景'
        },
        {
            'name': 'forest_morning',
            'prompt': 'A peaceful forest in the morning with sunlight filtering through green leaves, natural lighting, serene atmosphere',
            'colors': [(34, 139, 34), (144, 238, 144), (255, 255, 224)],
            'description': '森林晨光场景'
        },
        {
            'name': 'mountain_landscape',
            'prompt': 'Majestic mountains with snow-capped peaks under a clear blue sky, dramatic landscape, high quality',
            'colors': [(135, 206, 235), (255, 255, 255), (105, 105, 105)],
            'description': '雪山风景场景'
        },
        {
            'name': 'city_night',
            'prompt': 'A vibrant city at night with colorful lights and bustling streets, urban atmosphere, neon lights',
            'colors': [(25, 25, 112), (255, 215, 0), (220, 20, 60)],
            'description': '城市夜景场景'
        },
        {
            'name': 'flower_field',
            'prompt': 'A field of colorful flowers swaying gently in the spring breeze, natural beauty, soft lighting',
            'colors': [(255, 192, 203), (147, 112, 219), (144, 238, 144)],
            'description': '花田春景场景'
        },
        {
            'name': 'desert_dunes',
            'prompt': 'Golden sand dunes in the desert with dramatic shadows and clear sky, vast landscape, warm tones',
            'colors': [(238, 203, 173), (255, 218, 185), (135, 206, 235)],
            'description': '沙漠沙丘场景'
        },
        {
            'name': 'waterfall_nature',
            'prompt': 'A powerful waterfall cascading down rocky cliffs surrounded by lush vegetation, dynamic water flow',
            'colors': [(70, 130, 180), (34, 139, 34), (255, 255, 255)],
            'description': '瀑布自然场景'
        },
        {
            'name': 'autumn_leaves',
            'prompt': 'Autumn trees with golden and red leaves falling gently in the wind, seasonal beauty, warm colors',
            'colors': [(255, 140, 0), (220, 20, 60), (255, 215, 0)],
            'description': '秋叶飘落场景'
        }
    ]
    
    # 创建数据记录
    dataset_records = []
    
    print(f"\n🎨 创建 {len(scenes)} 个场景图像...")
    
    for i, scene in enumerate(scenes):
        # 创建图像
        img = Image.new('RGB', (832, 480), color=scene['colors'][0])
        draw = ImageDraw.Draw(img)
        
        # 添加渐变效果
        for y in range(480):
            ratio = y / 480
            if len(scene['colors']) >= 2:
                r = int(scene['colors'][0][0] * (1-ratio) + scene['colors'][1][0] * ratio)
                g = int(scene['colors'][0][1] * (1-ratio) + scene['colors'][1][1] * ratio)
                b = int(scene['colors'][0][2] * (1-ratio) + scene['colors'][1][2] * ratio)
                draw.line([(0, y), (832, y)], fill=(r, g, b))
        
        # 添加装饰元素
        if len(scene['colors']) >= 3:
            draw.ellipse([650, 50, 750, 150], fill=scene['colors'][2])
        
        # 添加文本标识
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        draw.text((20, 20), scene['name'], fill=(255, 255, 255), font=font)
        
        # 保存图像
        image_filename = f"{scene['name']}_{i:03d}.jpg"
        image_path = images_dir / image_filename
        img.save(image_path, 'JPEG', quality=95)
        
        # 记录数据
        record = {
            'image': f"images/{image_filename}",  # 相对路径
            'prompt': scene['prompt']
        }
        dataset_records.append(record)
        
        print(f"   ✅ {scene['name']}: {image_filename}")
    
    # 保存CSV文件（训练用）
    df = pd.DataFrame(dataset_records)
    csv_file = output_dir / "metadata.csv"
    df.to_csv(csv_file, index=False)
    
    # 保存详细JSON文件
    detailed_records = []
    for i, (record, scene) in enumerate(zip(dataset_records, scenes)):
        detailed_record = {
            **record,
            'scene_name': scene['name'],
            'description': scene['description'],
            'width': 832,
            'height': 480,
            'created_time': datetime.now().isoformat()
        }
        detailed_records.append(detailed_record)
    
    json_file = output_dir / "metadata_full.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(detailed_records, f, ensure_ascii=False, indent=2)
    
    # 创建统计信息
    stats = {
        'total_samples': len(dataset_records),
        'image_resolution': '832x480',
        'scenes': [scene['name'] for scene in scenes],
        'avg_prompt_length': sum(len(r['prompt']) for r in dataset_records) / len(dataset_records),
        'creation_time': datetime.now().isoformat()
    }
    
    stats_file = output_dir / "dataset_stats.json"
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    # 打印摘要
    print(f"\n📊 数据集创建完成!")
    print(f"   总样本数: {stats['total_samples']}")
    print(f"   图像分辨率: {stats['image_resolution']}")
    print(f"   平均提示词长度: {stats['avg_prompt_length']:.1f} 字符")
    
    print(f"\n📁 生成的文件:")
    print(f"   📝 {csv_file} (训练用CSV)")
    print(f"   📄 {json_file} (详细JSON)")
    print(f"   📊 {stats_file} (统计信息)")
    print(f"   🖼️  {images_dir}/ (图像文件)")
    
    print(f"\n🎬 场景列表:")
    for scene in scenes:
        print(f"   • {scene['name']}: {scene['description']}")
    
    return output_dir, csv_file

def main():
    """主函数"""
    output_dir, csv_file = create_custom_dataset()
    
    print(f"\n🚀 下一步操作:")
    print(f"   1. 检查生成的数据集:")
    print(f"      ls -la {output_dir}")
    print(f"      head {csv_file}")
    
    print(f"\n   2. 使用自定义数据集进行训练:")
    print(f"      修改训练命令中的数据集路径:")
    print(f"      --dataset_base_path {output_dir}")
    print(f"      --dataset_metadata_path {csv_file}")
    
    print(f"\n   3. 完整的训练命令:")
    print(f"""
cd /root/sj-tmp/DiffSynth-Studio && \\
source /root/miniconda3/etc/profile.d/conda.sh && \\
conda activate wan_video_env && \\
accelerate launch --config_file accelerate_config.yaml \\
  examples/wanvideo/model_training/train.py \\
  --dataset_base_path {output_dir} \\
  --dataset_metadata_path {csv_file} \\
  --height 480 --width 832 --dataset_repeat 20 \\
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \\
  --learning_rate 1e-4 --num_epochs 5 --gradient_accumulation_steps 1 \\
  --remove_prefix_in_ckpt "pipe.dit." \\
  --output_path "./models/train/Wan2.1-I2V-14B-480P_custom_lora" \\
  --lora_base_model "dit" --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \\
  --lora_rank 8 --extra_inputs "input_image" --mixed_precision "bf16"
""")

if __name__ == "__main__":
    main()

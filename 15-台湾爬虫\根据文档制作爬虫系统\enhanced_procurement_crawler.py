#!/usr/bin/env python3
"""
增强版政府采购爬虫 - 提取完整字段信息
"""

import requests
from bs4 import BeautifulSoup
import time
import logging
import re
from urllib.parse import urljoin, parse_qs, urlparse
from dataclasses import dataclass
from typing import List, Dict, Optional
import json
import ssl
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import random

@dataclass
class AgencyInfo:
    """机关信息"""
    agency_code: str = ""
    agency_name: str = ""
    unit_name: str = ""
    agency_address: str = ""
    contact_person: str = ""
    contact_phone: str = ""
    fax_number: str = ""

@dataclass
class AnnouncementInfo:
    """公告资料"""
    announcement_date: str = ""
    case_number: str = ""
    tender_method: str = ""
    award_method: str = ""
    announcement_count: str = ""
    procurement_law_106: str = ""
    case_title: str = ""
    award_data_category: str = ""

@dataclass
class TimeInfo:
    """时间信息"""
    opening_time: str = ""
    original_announcement_date: str = ""
    award_date: str = ""
    decision_date: str = ""

@dataclass
class AmountInfo:
    """金额信息"""
    procurement_amount_range: str = ""
    processing_method: str = ""
    budget_public: str = ""
    budget_amount: str = ""
    budget_amount_chinese: str = ""

@dataclass
class ProcurementNature:
    """采购性质"""
    is_sensitive_security: str = ""
    is_national_security: str = ""

@dataclass
class PerformanceInfo:
    """履约信息"""
    government_subsidy: str = ""
    performance_location: str = ""
    performance_region: str = ""
    priority_award: str = ""

@dataclass
class VendorInfo:
    """投标厂商信息"""
    vendor_code: str = ""
    vendor_name: str = ""
    is_winner: str = ""
    organization_type: str = ""
    business_type: str = ""
    vendor_address: str = ""
    vendor_phone: str = ""
    award_amount: str = ""
    award_amount_chinese: str = ""
    winner_country: str = ""
    is_sme: str = ""
    performance_period: str = ""
    over_100_employees: str = ""
    employee_count: str = ""
    indigenous_employees: str = ""
    disabled_employees: str = ""

@dataclass
class SubjectClassification:
    """标的分类"""
    classification_code: str = ""
    classification_name: str = ""

@dataclass
class ProcurementDetail:
    """完整的采购详情"""
    agency_info: AgencyInfo
    announcement_info: AnnouncementInfo
    time_info: TimeInfo
    amount_info: AmountInfo
    procurement_nature: ProcurementNature
    performance_info: PerformanceInfo
    vendors: List[VendorInfo]
    subject_classification: SubjectClassification
    bidder_count: int = 0

class EnhancedProcurementCrawler:
    """增强版政府采购爬虫"""

    def __init__(self, use_proxy=True, proxy_url="http://127.0.0.1:7890"):
        self.base_url = "https://web.pcc.gov.tw"
        self.search_url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion"
        self.search_action_url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion"

        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 创建session
        self.session = requests.Session()

        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]
        )

        # 创建适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置代理
        if use_proxy and proxy_url:
            self.session.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            print(f"使用代理: {proxy_url}")

        # 设置更真实的浏览器头部
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8,zh-CN;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })

        # 设置SSL验证为False以避免SSL错误
        self.session.verify = False

        # 设置超时
        self.session.timeout = 30

        # 设置日志
        logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

    def _make_request(self, url, method='GET', data=None, max_retries=3):
        """
        发送HTTP请求，带有重试和错误处理

        Args:
            url: 请求URL
            method: 请求方法 ('GET' 或 'POST')
            data: POST数据
            max_retries: 最大重试次数

        Returns:
            requests.Response: 响应对象
        """
        for attempt in range(max_retries):
            try:
                self.logger.info(f"正在请求: {url} (尝试 {attempt + 1}/{max_retries})")

                # 随机延迟避免被检测
                time.sleep(random.uniform(1, 3))

                if method.upper() == 'POST':
                    response = self.session.post(url, data=data)
                else:
                    if data:
                        response = self.session.get(url, params=data)
                    else:
                        response = self.session.get(url)

                response.raise_for_status()
                return response

            except requests.exceptions.SSLError as e:
                self.logger.warning(f"SSL错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(random.uniform(2, 5))

            except requests.exceptions.ConnectionError as e:
                self.logger.warning(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(random.uniform(3, 6))

            except requests.exceptions.Timeout as e:
                self.logger.warning(f"超时错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(random.uniform(2, 4))

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(random.uniform(2, 5))

        raise Exception(f"在 {max_retries} 次尝试后仍无法完成请求")

    def search_procurement_list(self, keyword="", tender_types=None, year_range="114年1至12月", page_size=20, max_pages=5):
        """
        搜索采购信息列表
        
        Args:
            keyword: 搜索关键词
            tender_types: 标案类型列表
            year_range: 年份范围
            page_size: 每页数量
            max_pages: 最大页数
        """
        if tender_types is None:
            tender_types = ['招標', '決標']
        
        results = []
        
        try:
            # 首先获取搜索页面
            response = self._make_request(self.search_url)
            
            # 构建搜索参数
            search_data = {
                'querySentence': keyword,
                'timeRange': '114',  # 114年1至12月
                'sortCol': 'TENDER_NOTICE_DATE',  # 这个参数很重要！
                'pageIndex': '1'
            }

            # 添加标案类型 - 使用checkbox方式
            for tender_type in tender_types:
                if tender_type in ['招標', '決標', '公開閱覽及公開徵求', '政府採購預告']:
                    search_data['tenderStatusType'] = tender_type
            
            # 执行搜索
            for page in range(1, max_pages + 1):
                search_data['pageIndex'] = str(page)
                
                self.logger.info(f"正在抓取第 {page} 页数据...")

                # 发送GET请求到正确的搜索端点
                search_response = self._make_request(self.search_action_url, method='GET', data=search_data)
                
                # 解析搜索结果
                page_results = self._parse_search_results(search_response.content)

                self.logger.info(f"第 {page} 页解析到 {len(page_results)} 条结果")

                if not page_results:
                    self.logger.info(f"第 {page} 页没有更多数据，停止抓取")
                    break

                results.extend(page_results)
                
                # 添加延迟避免被封
                time.sleep(2)
                
        except Exception as e:
            self.logger.error(f"搜索过程中发生错误: {str(e)}")
            
        return results
    
    def _parse_search_results(self, html_content):
        """解析搜索结果页面，获取详情页链接"""
        results = []

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找结果表格 - 使用正确的ID
            result_table = soup.find('table', {'id': 'bulletion'})
            if not result_table:
                self.logger.warning("未找到结果表格")
                return results

            # 解析每一行数据 - 跳过表头
            tbody = result_table.find('tbody')
            if not tbody:
                self.logger.warning("未找到表格主体")
                return results

            rows = tbody.find_all('tr')

            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 8:  # 确保有足够的列
                    try:
                        # 列结构: 項次, 種類, 機關名稱, 標案案號/標案名稱, 招標公告日期, 決標公告, 截止投標日期, 公開閱覽日期, 預告公告日期, 功能選項
                        index = cells[0].get_text(strip=True)
                        tender_type = cells[1].get_text(strip=True)
                        agency = cells[2].get_text(strip=True)

                        # 第4列包含案號和標案名稱，以及詳情鏈接
                        title_cell = cells[3]

                        # 提取案號和標案名稱
                        case_number = ""
                        title = ""

                        # 案號通常在第一行
                        case_number_elem = title_cell.contents[0] if title_cell.contents else ""
                        if isinstance(case_number_elem, str):
                            case_number = case_number_elem.strip()

                        # 標案名稱通常在script標籤中或者br標籤後
                        script_tag = title_cell.find('script')
                        if script_tag and script_tag.string:
                            script_content = script_tag.string
                            if 'pageCode2Img' in script_content:
                                import re
                                title_match = re.search(r'pageCode2Img\("([^"]+)"\)', script_content)
                                if title_match:
                                    title = title_match.group(1)

                        if not title:
                            # 如果script中沒有找到，嘗試從br標籤後獲取
                            br_tag = title_cell.find('br')
                            if br_tag and br_tag.next_sibling:
                                title = str(br_tag.next_sibling).strip()

                        # 查找"檢視"按鈕的鏈接
                        detail_url = ""
                        # 檢視按鈕通常在最後一列
                        if len(cells) > 8:
                            view_cell = cells[-1]  # 最後一列是功能選項
                            view_link = view_cell.find('a')
                            if view_link:
                                detail_url = urljoin(self.base_url, view_link.get('href', ''))

                        # 提取日期信息
                        tender_date = cells[4].get_text(strip=True) if len(cells) > 4 else ""
                        award_date = cells[5].get_text(strip=True) if len(cells) > 5 else ""

                        result = {
                            'title': title,
                            'agency': agency,
                            'tender_type': tender_type,
                            'case_number': case_number,
                            'tender_date': tender_date,
                            'award_date': award_date,
                            'announcement_date': award_date or tender_date,  # 兼容原有字段
                            'detail_url': detail_url
                        }

                        self.logger.debug(f"解析到结果: {title[:50]}... URL: {detail_url}")
                        results.append(result)

                    except Exception as e:
                        self.logger.warning(f"解析行数据时出错: {str(e)}")
                        continue

        except Exception as e:
            self.logger.error(f"解析搜索结果时出错: {str(e)}")

        return results
    
    def extract_detail_fields(self, detail_url: str) -> Optional[ProcurementDetail]:
        """
        提取详情页面的完整字段信息
        
        Args:
            detail_url: 详情页面URL
            
        Returns:
            ProcurementDetail: 完整的采购详情对象
        """
        try:
            if not detail_url:
                return None
            
            self.logger.info(f"正在提取详情: {detail_url}")

            response = self._make_request(detail_url)
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 初始化各个信息对象
            agency_info = AgencyInfo()
            announcement_info = AnnouncementInfo()
            time_info = TimeInfo()
            amount_info = AmountInfo()
            procurement_nature = ProcurementNature()
            performance_info = PerformanceInfo()
            vendors = []
            subject_classification = SubjectClassification()
            
            # 提取机关信息
            self._extract_agency_info(soup, agency_info)
            
            # 提取公告资料
            self._extract_announcement_info(soup, announcement_info)
            
            # 提取时间信息
            self._extract_time_info(soup, time_info)
            
            # 提取金额信息
            self._extract_amount_info(soup, amount_info)
            
            # 提取采购性质
            self._extract_procurement_nature(soup, procurement_nature)
            
            # 提取履约信息
            self._extract_performance_info(soup, performance_info)
            
            # 提取投标厂商信息
            vendors, bidder_count = self._extract_vendor_info(soup)
            
            # 提取标的分类
            self._extract_subject_classification(soup, subject_classification)
            
            # 组装完整信息
            detail = ProcurementDetail(
                agency_info=agency_info,
                announcement_info=announcement_info,
                time_info=time_info,
                amount_info=amount_info,
                procurement_nature=procurement_nature,
                performance_info=performance_info,
                vendors=vendors,
                subject_classification=subject_classification,
                bidder_count=bidder_count
            )
            
            return detail
            
        except Exception as e:
            self.logger.error(f"提取详情失败 {detail_url}: {str(e)}")
            return None
    
    def _extract_agency_info(self, soup: BeautifulSoup, agency_info: AgencyInfo):
        """提取机关信息"""
        try:
            # 使用表格结构提取信息
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if '機關代碼' in label:
                            agency_info.agency_code = value
                        elif '機關名稱' in label:
                            agency_info.agency_name = value
                        elif '單位名稱' in label:
                            agency_info.unit_name = value
                        elif '機關地址' in label:
                            agency_info.agency_address = value
                        elif '聯絡人' in label:
                            agency_info.contact_person = value
                        elif '聯絡電話' in label:
                            agency_info.contact_phone = value
                        elif '傳真號碼' in label:
                            agency_info.fax_number = value
        except Exception as e:
            self.logger.warning(f"提取机关信息失败: {str(e)}")
    
    def _extract_announcement_info(self, soup: BeautifulSoup, announcement_info: AnnouncementInfo):
        """提取公告资料"""
        try:
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if '公告日' in label:
                            announcement_info.announcement_date = value
                        elif '標案案號' in label:
                            announcement_info.case_number = value
                        elif '招標方式' in label:
                            announcement_info.tender_method = value
                        elif '決標方式' in label:
                            announcement_info.award_method = value
                        elif '新增公告傳輸次數' in label:
                            announcement_info.announcement_count = value
                        elif '106條第1項第1款' in label:
                            announcement_info.procurement_law_106 = value
                        elif '標案名稱' in label:
                            announcement_info.case_title = value
                        elif '決標資料類別' in label:
                            announcement_info.award_data_category = value
        except Exception as e:
            self.logger.warning(f"提取公告资料失败: {str(e)}")
    
    def _extract_time_info(self, soup: BeautifulSoup, time_info: TimeInfo):
        """提取时间信息"""
        try:
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if '開標時間' in label:
                            time_info.opening_time = value
                        elif '原公告日期' in label:
                            time_info.original_announcement_date = value
        except Exception as e:
            self.logger.warning(f"提取时间信息失败: {str(e)}")
    
    def _extract_amount_info(self, soup: BeautifulSoup, amount_info: AmountInfo):
        """提取金额信息"""
        try:
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if '採購金額級距' in label:
                            amount_info.procurement_amount_range = value
                        elif '辦理方式' in label:
                            amount_info.processing_method = value
                        elif '預算金額是否公開' in label:
                            amount_info.budget_public = value
                        elif '預算金額' in label:
                            # 分离数字和中文
                            amount_match = re.search(r'([\d,]+)元?\s*([^\d]*)', value)
                            if amount_match:
                                amount_info.budget_amount = amount_match.group(1) + "元"
                                amount_info.budget_amount_chinese = amount_match.group(2).strip()
                            else:
                                amount_info.budget_amount = value
        except Exception as e:
            self.logger.warning(f"提取金额信息失败: {str(e)}")
    
    def _extract_procurement_nature(self, soup: BeautifulSoup, procurement_nature: ProcurementNature):
        """提取采购性质"""
        try:
            text_content = soup.get_text()
            
            # 查找敏感性或国安相关内容
            if '敏感性或國安' in text_content:
                sensitive_match = re.search(r'敏感性或國安.*?採購.*?([是否])', text_content)
                if sensitive_match:
                    procurement_nature.is_sensitive_security = sensitive_match.group(1)
            
            # 查找国家安全相关内容
            if '涉及國家安全' in text_content:
                security_match = re.search(r'涉及國家安全.*?採購.*?([是否])', text_content)
                if security_match:
                    procurement_nature.is_national_security = security_match.group(1)
                    
        except Exception as e:
            self.logger.warning(f"提取采购性质失败: {str(e)}")
    
    def _extract_performance_info(self, soup: BeautifulSoup, performance_info: PerformanceInfo):
        """提取履约信息"""
        try:
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if '是否受機關補助' in label:
                            performance_info.government_subsidy = value
                        elif '履約地點' in label:
                            performance_info.performance_location = value
                        elif '履約地區' in label:
                            performance_info.performance_region = value
                        elif '優先決標' in label:
                            performance_info.priority_award = value
        except Exception as e:
            self.logger.warning(f"提取履约信息失败: {str(e)}")
    
    def _extract_vendor_info(self, soup: BeautifulSoup) -> tuple[List[VendorInfo], int]:
        """提取投标厂商信息"""
        vendors = []
        bidder_count = 0
        
        try:
            # 查找投标厂商家数
            text_content = soup.get_text()
            bidder_match = re.search(r'投標廠商家數.*?(\d+)', text_content)
            if bidder_match:
                bidder_count = int(bidder_match.group(1))
            
            # 提取厂商详细信息
            tables = soup.find_all('table')
            current_vendor = None
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 检测新厂商开始
                        if '投標廠商' in label and '廠商代碼' in label:
                            if current_vendor:
                                vendors.append(current_vendor)
                            current_vendor = VendorInfo()
                        
                        if current_vendor:
                            if '廠商代碼' in label:
                                current_vendor.vendor_code = value
                            elif '廠商名稱' in label:
                                current_vendor.vendor_name = value
                            elif '是否得標' in label:
                                current_vendor.is_winner = value
                            elif '組織型態' in label:
                                current_vendor.organization_type = value
                            elif '廠商業別' in label:
                                current_vendor.business_type = value
                            elif '廠商地址' in label:
                                current_vendor.vendor_address = value
                            elif '廠商電話' in label:
                                current_vendor.vendor_phone = value
                            elif '決標金額' in label:
                                # 分离数字和中文
                                amount_match = re.search(r'([\d,]+)元?\s*([^\d]*)', value)
                                if amount_match:
                                    current_vendor.award_amount = amount_match.group(1) + "元"
                                    current_vendor.award_amount_chinese = amount_match.group(2).strip()
                                else:
                                    current_vendor.award_amount = value
                            elif '得標廠商國別' in label:
                                current_vendor.winner_country = value
                            elif '是否為中小企業' in label:
                                current_vendor.is_sme = value
                            elif '履約起迄日期' in label:
                                current_vendor.performance_period = value
                            elif '超過100人' in label:
                                current_vendor.over_100_employees = value
                            elif '雇用員工人數' in label:
                                current_vendor.employee_count = value
                            elif '原住民人數' in label:
                                current_vendor.indigenous_employees = value
                            elif '身心障礙者' in label:
                                current_vendor.disabled_employees = value
            
            # 添加最后一个厂商
            if current_vendor:
                vendors.append(current_vendor)
                
        except Exception as e:
            self.logger.warning(f"提取厂商信息失败: {str(e)}")
        
        return vendors, bidder_count
    
    def _extract_subject_classification(self, soup: BeautifulSoup, subject_classification: SubjectClassification):
        """提取标的分类"""
        try:
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if '標的分類' in label:
                            # 解析分类代码和名称
                            classification_match = re.search(r'<(\d+)類別>\s*(\d+)\s*(.*)', value)
                            if classification_match:
                                subject_classification.classification_code = classification_match.group(2)
                                subject_classification.classification_name = classification_match.group(3).strip()
                            else:
                                subject_classification.classification_name = value
        except Exception as e:
            self.logger.warning(f"提取标的分类失败: {str(e)}")
    
    def crawl_with_full_details(self, keyword="", tender_types=None, year_range="114年1至12月", page_size=20, max_pages=5):
        """
        爬取数据并获取完整的详细字段信息
        
        Args:
            keyword: 搜索关键词
            tender_types: 标案类型列表
            year_range: 年份范围
            page_size: 每页数量
            max_pages: 最大页数
            
        Returns:
            List[Dict]: 包含完整字段信息的采购数据列表
        """
        # 首先获取搜索结果列表
        search_results = self.search_procurement_list(keyword, tender_types, year_range, page_size, max_pages)
        
        detailed_results = []
        
        # 为每个结果获取详细信息
        for i, result in enumerate(search_results):
            self.logger.info(f"正在获取第 {i+1}/{len(search_results)} 条详细信息...")
            
            detail = self.extract_detail_fields(result['detail_url'])

            self.logger.info(f"详情提取结果: {'成功' if detail else '失败'}")

            if detail:
                # 将详细信息转换为字典格式
                detailed_result = {
                    'basic_info': result,
                    'agency_info': detail.agency_info.__dict__,
                    'announcement_info': detail.announcement_info.__dict__,
                    'time_info': detail.time_info.__dict__,
                    'amount_info': detail.amount_info.__dict__,
                    'procurement_nature': detail.procurement_nature.__dict__,
                    'performance_info': detail.performance_info.__dict__,
                    'vendors': [vendor.__dict__ for vendor in detail.vendors],
                    'subject_classification': detail.subject_classification.__dict__,
                    'bidder_count': detail.bidder_count
                }
                detailed_results.append(detailed_result)
            
            # 添加延迟避免被封
            time.sleep(3)
        
        return detailed_results
    
    def save_to_json(self, data: List[Dict], filename: str):
        """保存数据到JSON文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"数据已保存到 {filename}")
        except Exception as e:
            self.logger.error(f"保存数据失败: {str(e)}")

def main():
    """主函数 - 演示使用"""
    crawler = EnhancedProcurementCrawler()
    
    # 爬取数据
    print("开始爬取政府采购数据...")
    results = crawler.crawl_with_full_details(
        keyword="餐飲",
        tender_types=['決標'],
        max_pages=2,
        page_size=5
    )
    
    print(f"爬取完成，共获取 {len(results)} 条详细数据")
    
    # 保存数据
    crawler.save_to_json(results, 'procurement_detailed_data.json')
    
    # 显示第一条数据的结构
    if results:
        print("\n第一条数据结构:")
        print(json.dumps(results[0], ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()


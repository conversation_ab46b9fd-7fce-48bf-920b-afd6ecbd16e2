# !/usr/bin/env python3

# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
UtteranceProcessorArguments

"""

from dataclasses import dataclass, field


@dataclass
class UtteranceProcessorArguments:
    """
    args for UtteranceArguments
    """

    tokenizer: str = field(default=None, metadata={"help": "path of tokenizer"})
    tokenizer_name: str = field(default=None, metadata={"help": "path of tokenizer"})

    def __post_init__(self):
        if self.tokenizer_name and not self.tokenizer:
            self.tokenizer = self.tokenizer_name

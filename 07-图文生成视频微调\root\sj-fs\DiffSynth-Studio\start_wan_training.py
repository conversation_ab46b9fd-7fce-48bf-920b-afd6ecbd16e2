#!/usr/bin/env python3
"""
DiffSynth-Studio Wan视频模型训练启动脚本
支持文字生成视频(T2V)和图片生成视频(I2V)模型的微调
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# 可用的模型配置
AVAILABLE_MODELS = {
    # 文字生成视频模型
    "t2v_1.3b": {
        "name": "Wan2.1-T2V-1.3B",
        "type": "text-to-video",
        "size": "1.3B",
        "lora_script": "examples/wanvideo/model_training/lora/Wan2.1-T2V-1.3B.sh",
        "full_script": "examples/wanvideo/model_training/full/Wan2.1-T2V-1.3B.sh",
        "description": "1.3B参数的文字生成视频模型"
    },
    "t2v_14b": {
        "name": "Wan2.1-T2V-14B", 
        "type": "text-to-video",
        "size": "14B",
        "lora_script": "examples/wanvideo/model_training/lora/Wan2.1-T2V-14B.sh",
        "full_script": "examples/wanvideo/model_training/full/Wan2.1-T2V-14B.sh",
        "description": "14B参数的文字生成视频模型"
    },
    # 图片生成视频模型
    "i2v_14b_480p": {
        "name": "Wan2.1-I2V-14B-480P",
        "type": "image-to-video", 
        "size": "14B",
        "lora_script": "examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P.sh",
        "full_script": "examples/wanvideo/model_training/full/Wan2.1-I2V-14B-480P.sh",
        "description": "14B参数的图片生成视频模型(480P)"
    },
    "i2v_14b_720p": {
        "name": "Wan2.1-I2V-14B-720P",
        "type": "image-to-video",
        "size": "14B", 
        "lora_script": "examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-720P.sh",
        "full_script": "examples/wanvideo/model_training/full/Wan2.1-I2V-14B-720P.sh",
        "description": "14B参数的图片生成视频模型(720P)"
    }
}

def print_banner():
    """打印欢迎横幅"""
    print("=" * 70)
    print("🎬 DiffSynth-Studio Wan视频模型训练启动器")
    print("   支持文字生成视频(T2V)和图片生成视频(I2V)模型微调")
    print("=" * 70)

def list_models():
    """列出所有可用的模型"""
    print("\n📋 可用的模型:")
    print("-" * 50)
    for key, model in AVAILABLE_MODELS.items():
        print(f"🔹 {key}: {model['description']}")
        print(f"   类型: {model['type']}, 大小: {model['size']}")
    print("-" * 50)

def check_environment():
    """检查环境是否正确设置"""
    print("🔍 检查环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major != 3 or python_version.minor != 12:
        print(f"⚠️  建议使用Python 3.12，当前版本: {python_version.major}.{python_version.minor}")
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 检测到虚拟环境")
    else:
        print("⚠️  建议在虚拟环境中运行")
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA可用，GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                memory_gb = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)} ({memory_gb:.1f}GB)")
        else:
            print("❌ CUDA不可用")
    except ImportError:
        print("❌ PyTorch未安装")
    
    # 检查DiffSynth
    try:
        from diffsynth.pipelines.wan_video_new import WanVideoPipeline
        print("✅ DiffSynth-Studio已安装")
    except ImportError:
        print("❌ DiffSynth-Studio未正确安装")
        return False
    
    return True

def check_dataset(dataset_path):
    """检查数据集是否存在"""
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return False
    
    metadata_file = os.path.join(dataset_path, "metadata.csv")
    if not os.path.exists(metadata_file):
        print(f"❌ 元数据文件不存在: {metadata_file}")
        return False
    
    print(f"✅ 数据集检查通过: {dataset_path}")
    return True

def modify_training_script(script_path, dataset_path, output_path, custom_args=None):
    """修改训练脚本中的数据集路径和输出路径"""
    if not os.path.exists(script_path):
        print(f"❌ 训练脚本不存在: {script_path}")
        return None
    
    # 读取原始脚本
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建修改后的脚本
    modified_script = script_path.replace('.sh', '_custom.sh')
    
    # 替换数据集路径和输出路径
    content = content.replace(
        '--dataset_base_path "./data/example_video_dataset"',
        f'--dataset_base_path "{dataset_path}"'
    )
    content = content.replace(
        '--dataset_metadata_path "./data/example_video_dataset/metadata.csv"',
        f'--dataset_metadata_path "{os.path.join(dataset_path, "metadata.csv")}"'
    )
    
    # 如果指定了输出路径，替换输出路径
    if output_path:
        content = content.replace(
            '--output_path "./models/lora"',
            f'--output_path "{output_path}"'
        )
        content = content.replace(
            '--output_path "./models/full"',
            f'--output_path "{output_path}"'
        )
    
    # 添加自定义参数
    if custom_args:
        # 在最后一行之前添加自定义参数
        lines = content.strip().split('\n')
        if lines[-1].strip().endswith('\\'):
            lines.append(f'    {custom_args}')
        else:
            lines[-1] = lines[-1] + ' \\'
            lines.append(f'    {custom_args}')
        content = '\n'.join(lines)
    
    # 写入修改后的脚本
    with open(modified_script, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 添加执行权限
    os.chmod(modified_script, 0o755)
    
    return modified_script

def main():
    parser = argparse.ArgumentParser(description="DiffSynth-Studio Wan视频模型训练启动器")
    parser.add_argument("--model", "-m", choices=list(AVAILABLE_MODELS.keys()), 
                       help="选择要训练的模型")
    parser.add_argument("--training-type", "-t", choices=["lora", "full"], default="lora",
                       help="训练类型: lora(默认) 或 full")
    parser.add_argument("--dataset", "-d", type=str, default="./data/example_video_dataset",
                       help="数据集路径")
    parser.add_argument("--output", "-o", type=str, 
                       help="输出路径 (默认: ./models/lora 或 ./models/full)")
    parser.add_argument("--list-models", "-l", action="store_true",
                       help="列出所有可用的模型")
    parser.add_argument("--check-env", "-c", action="store_true",
                       help="检查环境设置")
    parser.add_argument("--custom-args", type=str,
                       help="自定义训练参数")
    parser.add_argument("--dry-run", action="store_true",
                       help="仅显示将要执行的命令，不实际运行")
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.list_models:
        list_models()
        return
    
    if args.check_env:
        check_environment()
        return
    
    if not args.model:
        print("❌ 请指定要训练的模型，使用 --list-models 查看可用模型")
        return
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，请先正确设置环境")
        return
    
    # 检查数据集
    if not check_dataset(args.dataset):
        print("💡 提示: 使用 './download_example_dataset.sh' 下载示例数据集")
        return
    
    # 获取模型配置
    model_config = AVAILABLE_MODELS[args.model]
    
    # 选择训练脚本
    if args.training_type == "lora":
        script_path = model_config["lora_script"]
        default_output = "./models/lora"
    else:
        script_path = model_config["full_script"]
        default_output = "./models/full"
    
    output_path = args.output or default_output
    
    print(f"\n🎯 训练配置:")
    print(f"   模型: {model_config['name']} ({model_config['description']})")
    print(f"   训练类型: {args.training_type.upper()}")
    print(f"   数据集: {args.dataset}")
    print(f"   输出路径: {output_path}")
    
    # 修改训练脚本
    print(f"\n📝 准备训练脚本...")
    modified_script = modify_training_script(script_path, args.dataset, output_path, args.custom_args)
    
    if not modified_script:
        return
    
    print(f"✅ 训练脚本已准备: {modified_script}")
    
    # 执行训练
    if args.dry_run:
        print(f"\n🔍 Dry run - 将要执行的命令:")
        print(f"bash {modified_script}")
    else:
        print(f"\n🚀 开始训练...")
        print(f"执行命令: bash {modified_script}")
        
        try:
            subprocess.run(["bash", modified_script], check=True)
            print(f"\n🎉 训练完成!")
        except subprocess.CalledProcessError as e:
            print(f"\n❌ 训练失败: {e}")
        except KeyboardInterrupt:
            print(f"\n⏹️  训练被用户中断")

if __name__ == "__main__":
    main()

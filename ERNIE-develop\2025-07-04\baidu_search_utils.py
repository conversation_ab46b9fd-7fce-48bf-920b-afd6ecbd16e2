# -*- coding: utf-8 -*-
"""
百度搜索工具类，使用crawl4ai获取搜索结果URL
"""

import logging
import re
import urllib.parse
from typing import List, Dict

from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig
from simple_baidu_parser import SimpleBaiduParser


class BaiduSearchUtils:
    """
    百度搜索工具类，用于获取搜索结果URL
    """

    def __init__(self):
        """初始化百度搜索工具"""
        self.logger = logging.getLogger(__name__)
        self.parser = SimpleBaiduParser()  # 初始化简化的解析器
        
        # 配置爬虫参数
        self.run_config = CrawlerRunConfig(
            page_timeout=15000,  # 15秒超时
            word_count_threshold=10,
            excluded_tags=["script", "style", "iframe", "meta", "nav", "footer"],
            exclude_external_links=False,  # 需要获取外部链接
            exclude_internal_links=True,
            exclude_social_media_links=True,
            exclude_external_images=True,
            only_text=False,  # 需要获取链接信息
            cache_mode=CacheMode.BYPASS,
        )

    async def search_baidu(self, query: str, max_results: int = 10) -> List[Dict[str, str]]:
        """
        在百度搜索指定查询词并返回结果URL列表
        
        Args:
            query (str): 搜索查询词
            max_results (int): 最大结果数量，默认10
            
        Returns:
            List[Dict[str, str]]: 搜索结果列表，每个结果包含title和url
        """
        try:
            # 构建百度搜索URL
            encoded_query = urllib.parse.quote(query)
            search_url = f"https://www.baidu.com/s?wd={encoded_query}&rn={max_results}"
            
            self.logger.info(f"搜索URL: {search_url}")
            
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(url=search_url, config=self.run_config)

            # 调试：保存HTML内容到文件
            if result.html:
                self.logger.info(f"获取到HTML内容，长度: {len(result.html)}")
                # 保存前1000个字符用于调试
                debug_html = result.html[:2000]
                self.logger.debug(f"HTML前2000字符: {debug_html}")
            else:
                self.logger.warning("未获取到HTML内容")

            # 解析搜索结果 - 使用改进的解析器
            search_results = self.parser.parse_search_results(result.html, max_results)
            
            self.logger.info(f"找到 {len(search_results)} 个搜索结果")
            return search_results
            
        except Exception as e:
            self.logger.error(f"百度搜索失败: {e}")
            return []

    def _parse_baidu_results(self, html_content: str, max_results: int) -> List[Dict[str, str]]:
        """
        解析百度搜索结果页面HTML，提取标题和URL
        
        Args:
            html_content (str): 百度搜索结果页面HTML
            max_results (int): 最大结果数量
            
        Returns:
            List[Dict[str, str]]: 解析出的搜索结果
        """
        results = []
        
        try:
            # 专门针对百度搜索结果的解析模式
            patterns = [
                # 百度搜索结果的标准模式 - 匹配包含百度重定向链接的h3标签
                r'<h3[^>]*class="[^"]*t[^"]*"[^>]*>.*?<a[^>]*href="(https://www\.baidu\.com/baidu\.php\?url=[^"]*)"[^>]*>(.*?)</a>',
                # 更宽松的模式，匹配任何百度重定向链接
                r'<a[^>]*href="(https://www\.baidu\.com/baidu\.php\?url=[^"]*)"[^>]*[^>]*>(.*?)</a>',
                # 匹配其他可能的百度链接格式
                r'<h3[^>]*>.*?<a[^>]*href="([^"]*baidu\.php[^"]*)"[^>]*>(.*?)</a>',
                # 匹配直接的外部链接（非百度域名）
                r'<h3[^>]*class="[^"]*t[^"]*"[^>]*>.*?<a[^>]*href="(https?://(?!.*baidu\.com)[^"]*)"[^>]*>(.*?)</a>',
                # 匹配任何h3标签内的链接
                r'<h3[^>]*>.*?<a[^>]*href="(https?://[^"]*)"[^>]*>(.*?)</a>',
            ]

            matches = []
            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, html_content, re.DOTALL | re.IGNORECASE)
                if matches:
                    self.logger.info(f"使用模式 {i+1} 匹配到 {len(matches)} 个结果")
                    break
            
            for match in matches[:max_results]:
                url, title = match
                
                # 清理标题中的HTML标签
                title = re.sub(r'<[^>]+>', '', title)
                title = title.strip()
                
                # 处理百度的重定向URL
                if 'baidu.com/baidu.php?url=' in url:
                    # 提取真实URL
                    real_url = self._extract_real_url_from_baidu(url)
                    if real_url:
                        url = real_url
                    else:
                        continue  # 跳过无法解析的URL
                elif url.startswith('/link?url='):
                    # 提取真实URL
                    real_url = self._extract_real_url(url)
                    if real_url:
                        url = real_url
                elif url.startswith('http'):
                    # 直接使用URL
                    pass
                else:
                    # 跳过无效URL
                    continue
                
                if title and url and self._is_valid_url(url):
                    results.append({
                        'title': title,
                        'url': url
                    })
            
            # 如果正则表达式没有匹配到结果，尝试更多备用模式
            if not results:
                self.logger.info("主要模式未匹配到结果，尝试备用模式")

                backup_patterns = [
                    # 通用链接模式
                    r'<a[^>]*href="([^"]*)"[^>]*>([^<]+)</a>',
                    # 包含data-click的链接
                    r'<a[^>]*href="([^"]*)"[^>]*data-click[^>]*>([^<]+)</a>',
                    # 更宽松的模式
                    r'href="([^"]*)"[^>]*>([^<]+)<',
                ]

                for backup_pattern in backup_patterns:
                    matches2 = re.findall(backup_pattern, html_content)
                    self.logger.info(f"备用模式匹配到 {len(matches2)} 个链接")

                    for match in matches2[:max_results * 2]:  # 多取一些，后面过滤
                        url, title = match
                        title = title.strip()

                        if (title and url and
                            self._is_valid_url(url) and
                            len(title) > 5 and  # 过滤太短的标题
                            not title.startswith('百度') and
                            '搜索' not in title and
                            '登录' not in title and
                            '注册' not in title):

                            results.append({
                                'title': title,
                                'url': url
                            })

                            if len(results) >= max_results:
                                break

                    if results:
                        break
                        
        except Exception as e:
            self.logger.error(f"解析百度搜索结果失败: {e}")
        
        return results

    def _extract_real_url(self, baidu_url: str) -> str:
        """
        从百度重定向URL中提取真实URL
        
        Args:
            baidu_url (str): 百度重定向URL
            
        Returns:
            str: 真实URL
        """
        try:
            # 百度重定向URL格式: /link?url=...
            if 'url=' in baidu_url:
                url_part = baidu_url.split('url=')[1]
                # 可能还有其他参数，只取第一个参数
                if '&' in url_part:
                    url_part = url_part.split('&')[0]
                
                # URL解码
                real_url = urllib.parse.unquote(url_part)
                return real_url
        except Exception as e:
            self.logger.error(f"提取真实URL失败: {e}")
        
        return ""

    def _extract_real_url_from_baidu(self, baidu_url: str) -> str:
        """从百度baidu.php重定向URL中提取真实URL"""
        try:
            # 百度的重定向URL格式: https://www.baidu.com/baidu.php?url=K00000...
            # 这种URL需要通过HTTP请求获取重定向后的真实URL

            import requests

            # 设置请求头，模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            # 发送HEAD请求获取重定向URL，不下载完整内容
            response = requests.head(baidu_url, headers=headers, allow_redirects=True, timeout=5)

            # 获取最终的URL
            final_url = response.url

            # 检查是否是有效的外部URL
            if final_url and final_url != baidu_url and not 'baidu.com' in final_url:
                self.logger.debug(f"成功解析百度重定向: {baidu_url[:50]}... -> {final_url}")
                return final_url
            else:
                self.logger.debug(f"百度重定向解析失败或仍为百度域名: {final_url}")
                return ""

        except Exception as e:
            self.logger.debug(f"解析百度重定向URL失败: {e}")
        return ""

    def _is_valid_url(self, url: str) -> bool:
        """
        检查URL是否有效
        
        Args:
            url (str): 要检查的URL
            
        Returns:
            bool: URL是否有效
        """
        if not url:
            return False
        
        # 过滤掉一些无用的URL
        invalid_patterns = [
            'javascript:',
            'mailto:',
            '#',
            'baidu.com',
            'tieba.baidu.com',
            'zhidao.baidu.com',
            'image.baidu.com',
            'map.baidu.com',
            'hao123.com',
            'www.hao123.com',
            '/link?url=javascript',
            'cache:',
            'webcache.googleusercontent.com',
            'passport.baidu.com',
            'pan.baidu.com',
            'fanyi.baidu.com',
            'wenku.baidu.com',
            'music.baidu.com',
            'video.baidu.com',
            'news.baidu.com',
            'www.baidu.com/s?',
            'www.baidu.com/link',
            'click.baidu.com',
            'pos.baidu.com',
            'cpro.baidu.com',
            'wappass.baidu.com'
        ]
        
        for pattern in invalid_patterns:
            if pattern in url.lower():
                return False
        
        # 检查是否是有效的HTTP/HTTPS URL
        return url.startswith(('http://', 'https://'))

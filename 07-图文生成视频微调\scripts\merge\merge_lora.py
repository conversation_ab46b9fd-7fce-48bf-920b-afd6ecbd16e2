import torch
import argparse
from pathlib import Path
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def merge_lora_weights(lora_path, output_path):
    """将LoRA权重合并到基础模型"""
    print("开始合并LoRA权重...")
    
    # 加载基础模型
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cpu",
        model_configs=[
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="diffusion_pytorch_model*.safetensors"),
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth"),
            ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth"),
        ],
    )
    
    # 加载LoRA权重
    pipe.load_lora(lora_path, alpha=1.0)
    
    print(f"模型合并完成，保存到: {output_path}")
    return True

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--lora_path", type=str, required=True)
    parser.add_argument("--output_path", type=str, required=True)
    args = parser.parse_args()
    
    merge_lora_weights(args.lora_path, args.output_path)

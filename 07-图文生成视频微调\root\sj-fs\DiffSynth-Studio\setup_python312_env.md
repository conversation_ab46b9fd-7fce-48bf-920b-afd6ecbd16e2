# Python 3.12 虚拟环境设置指南 - DiffSynth-Studio Wan视频模型微调

本指南将帮助您使用Python 3.12创建专用的虚拟环境，用于进行图片生成视频(I2V)和文字生成视频(T2V)的模型微调。

## 环境要求

- Python 3.12.x
- CUDA 12.x (推荐)
- 至少16GB显存的GPU (推荐80GB用于14B模型全量训练)
- 足够的磁盘空间用于模型和数据集

## Step 1: 创建Python 3.12虚拟环境

### 方法1: 使用venv (推荐)

```bash
# 创建虚拟环境
python3.12 -m venv wan_video_env

# 激活虚拟环境
source wan_video_env/bin/activate  # Linux/Mac
# 或者在Windows上使用:
# wan_video_env\Scripts\activate

# 验证Python版本
python --version  # 应该显示 Python 3.12.x
```

### 方法2: 使用conda

```bash
# 创建conda环境
conda create -n wan_video_env python=3.12 -y

# 激活环境
conda activate wan_video_env

# 验证Python版本
python --version
```

## Step 2: 安装DiffSynth-Studio

```bash
# 确保在虚拟环境中
# 从源码安装DiffSynth-Studio
pip install -e .

# 或者先升级pip
pip install --upgrade pip
pip install -e .
```

## Step 3: 安装额外依赖

### 基础依赖 (已在requirements.txt中)
```bash
# 这些依赖会通过pip install -e .自动安装
# torch>=2.0.0
# torchvision
# cupy-cuda12x
# transformers
# controlnet-aux==0.0.7
# imageio[ffmpeg]
# safetensors
# einops
# sentencepiece
# protobuf
# modelscope
# ftfy
# pynvml
# pandas
# accelerate
```

### 训练相关的额外依赖
```bash
# 用于14B模型全量训练
pip install deepspeed

# 用于多GPU并行推理 (可选)
pip install "xfuser[flash-attn]>=0.4.3"

# 用于更好的注意力机制 (可选，按优先级安装)
# Flash Attention 3 (最高优先级)
pip install flash-attn

# 或者 Sage Attention
pip install sageattention

# 用于数据处理
pip install opencv-python
pip install pillow
```

## Step 4: 验证安装

创建测试脚本验证环境：

```python
# test_installation.py
import torch
import sys
print(f"Python版本: {sys.version}")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"GPU数量: {torch.cuda.device_count()}")
    print(f"当前GPU: {torch.cuda.get_device_name()}")

try:
    from diffsynth import save_video
    from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
    print("✅ DiffSynth-Studio导入成功")
except ImportError as e:
    print(f"❌ DiffSynth-Studio导入失败: {e}")

try:
    import accelerate
    print(f"✅ Accelerate版本: {accelerate.__version__}")
except ImportError:
    print("❌ Accelerate未安装")
```

运行测试：
```bash
python test_installation.py
```

## Step 5: 配置Accelerate (用于训练)

```bash
# 配置accelerate用于多GPU训练
accelerate config
```

按照提示配置：
- 选择训练类型 (multi-GPU, single-GPU等)
- 设置GPU数量
- 选择混合精度 (推荐fp16或bf16)

## Step 6: 准备数据集

### 数据集结构
```
data/your_video_dataset/
├── metadata.csv
├── video1.mp4
├── video2.mp4
└── ...
```

### metadata.csv格式
```csv
video,prompt
video1.mp4,"一只小狗在草地上奔跑"
video2.mp4,"夕阳下的城市街道"
```

### 下载示例数据集
```bash
# 下载官方示例数据集
modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
```

## Step 7: 模型微调示例

### T2V (文字生成视频) 1.3B模型LoRA训练
```bash
# 激活环境
source wan_video_env/bin/activate

# 运行LoRA训练
bash examples/wanvideo/model_training/lora/Wan2.1-T2V-1.3B.sh
```

### I2V (图片生成视频) 14B模型LoRA训练
```bash
# 运行I2V LoRA训练
bash examples/wanvideo/model_training/lora/Wan2.1-I2V-14B-480P.sh
```

## 常见问题解决

### 1. CUDA版本不匹配
```bash
# 检查CUDA版本
nvcc --version
nvidia-smi

# 重新安装对应的PyTorch版本
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121  # 对于CUDA 12.1
```

### 2. 显存不足
```bash
# 在训练脚本中添加以下参数
--use_gradient_checkpointing_offload
--tiled True
```

### 3. 模型下载问题
```bash
# 设置modelscope缓存目录
export MODELSCOPE_CACHE=/path/to/your/cache

# 或者手动下载模型到本地
modelscope download --model Wan-AI/Wan2.1-T2V-1.3B --local_dir ./models/Wan-AI/Wan2.1-T2V-1.3B
```

## 环境管理

### 保存环境配置
```bash
# 导出依赖列表
pip freeze > wan_video_requirements.txt

# 或者使用conda
conda env export > wan_video_environment.yml
```

### 删除环境
```bash
# venv环境
deactivate
rm -rf wan_video_env

# conda环境
conda deactivate
conda env remove -n wan_video_env
```

## 下一步

1. 根据您的具体需求选择合适的模型 (1.3B或14B)
2. 准备您的训练数据集
3. 选择训练方式 (LoRA或全量训练)
4. 开始模型微调

更多详细信息请参考 `examples/wanvideo/README_zh.md`。

#!/usr/bin/env python3
"""
字段筛选器 - 根据需求筛选政府采购数据的关键字段
"""

import json
from datetime import datetime

class ProcurementFieldFilter:
    """政府采购数据字段筛选器"""
    
    def __init__(self):
        # 定义核心字段映射
        self.core_fields = {
            # 基本信息字段
            'basic_info': {
                'title': '标案名称',
                'agency': '机关名称', 
                'case_number': '标案案号',
                'tender_date': '招标日期',
                'award_date': '决标日期',
                'tender_type': '标案类型'
            },
            
            # 机关信息字段
            'agency_info': {
                'agency_code': '机关代码',
                'agency_name': '机关名称',
                'unit_name': '单位名称',
                'agency_address': '机关地址',
                'contact_person': '联络人',
                'contact_phone': '联络电话'
            },
            
            # 金额信息字段
            'amount_info': {
                'budget_amount': '预算金额',
                'budget_amount_chinese': '预算金额(中文)',
                'procurement_amount_range': '采购金额级距',
                'budget_public': '预算金额是否公开'
            },
            
            # 招标信息字段
            'tender_info': {
                'tender_method': '招标方式',
                'award_method': '决标方式',
                'opening_time': '开标时间',
                'announcement_date': '公告日期'
            },
            
            # 厂商信息字段
            'vendor_info': {
                'vendor_name': '厂商名称',
                'vendor_code': '厂商代码',
                'is_winner': '是否得标',
                'award_amount': '决标金额',
                'organization_type': '组织型态',
                'vendor_address': '厂商地址',
                'performance_period': '履约期间'
            },
            
            # 履约信息字段
            'performance_info': {
                'performance_location': '履约地点',
                'government_subsidy': '是否受机关补助'
            }
        }
    
    def filter_essential_fields(self, data):
        """筛选出最核心的字段"""
        filtered_data = []
        
        for item in data:
            filtered_item = {
                # 基本信息
                '标案名称': item.get('list_data', {}).get('title', ''),
                '机关名称': item.get('list_data', {}).get('agency', ''),
                '标案案号': item.get('detail_data', {}).get('announcement_info', {}).get('case_number', ''),
                '招标日期': item.get('list_data', {}).get('tender_date', ''),
                '决标日期': item.get('list_data', {}).get('award_date', ''),
                '标案类型': item.get('list_data', {}).get('tender_type', ''),
                
                # 机关详细信息
                '机关代码': item.get('detail_data', {}).get('agency_info', {}).get('agency_code', ''),
                '单位名称': item.get('detail_data', {}).get('agency_info', {}).get('unit_name', ''),
                '机关地址': item.get('detail_data', {}).get('agency_info', {}).get('agency_address', ''),
                '联络人': item.get('detail_data', {}).get('agency_info', {}).get('contact_person', ''),
                '联络电话': item.get('detail_data', {}).get('agency_info', {}).get('contact_phone', ''),
                
                # 金额信息
                '预算金额': item.get('detail_data', {}).get('amount_info', {}).get('budget_amount', ''),
                '预算金额中文': item.get('detail_data', {}).get('amount_info', {}).get('budget_amount_chinese', ''),
                '采购金额级距': item.get('detail_data', {}).get('amount_info', {}).get('procurement_amount_range', ''),
                
                # 招标信息
                '招标方式': item.get('detail_data', {}).get('announcement_info', {}).get('tender_method', ''),
                '决标方式': item.get('detail_data', {}).get('announcement_info', {}).get('award_method', ''),
                '开标时间': item.get('detail_data', {}).get('time_info', {}).get('opening_time', ''),
                '公告日期': item.get('detail_data', {}).get('announcement_info', {}).get('announcement_date', ''),
                
                # 履约信息
                '履约地点': item.get('detail_data', {}).get('performance_info', {}).get('performance_location', ''),
                '是否受机关补助': item.get('detail_data', {}).get('performance_info', {}).get('government_subsidy', ''),
                
                # 厂商信息
                '投标厂商数': item.get('detail_data', {}).get('bidder_count', 0),
                '厂商信息': self._extract_vendor_summary(item.get('detail_data', {}).get('vendors', [])),
                
                # 原始链接
                '详情页链接': item.get('list_data', {}).get('detail_url', '')
            }
            
            filtered_data.append(filtered_item)
        
        return filtered_data
    
    def filter_minimal_fields(self, data):
        """筛选出最精简的字段（适合表格显示）"""
        filtered_data = []
        
        for item in data:
            filtered_item = {
                '序号': len(filtered_data) + 1,
                '标案名称': item.get('list_data', {}).get('title', ''),
                '机关名称': item.get('list_data', {}).get('agency', ''),
                '标案案号': item.get('detail_data', {}).get('announcement_info', {}).get('case_number', ''),
                '决标日期': item.get('list_data', {}).get('award_date', ''),
                '预算金额': item.get('detail_data', {}).get('amount_info', {}).get('budget_amount', ''),
                '招标方式': item.get('detail_data', {}).get('announcement_info', {}).get('tender_method', ''),
                '得标厂商': self._get_winner_vendor(item.get('detail_data', {}).get('vendors', [])),
                '决标金额': self._get_winner_amount(item.get('detail_data', {}).get('vendors', []))
            }
            
            filtered_data.append(filtered_item)
        
        return filtered_data
    
    def filter_custom_fields(self, data, field_list):
        """根据自定义字段列表筛选数据"""
        filtered_data = []
        
        field_mapping = {
            '标案名称': lambda item: item.get('list_data', {}).get('title', ''),
            '机关名称': lambda item: item.get('list_data', {}).get('agency', ''),
            '标案案号': lambda item: item.get('detail_data', {}).get('announcement_info', {}).get('case_number', ''),
            '招标日期': lambda item: item.get('list_data', {}).get('tender_date', ''),
            '决标日期': lambda item: item.get('list_data', {}).get('award_date', ''),
            '预算金额': lambda item: item.get('detail_data', {}).get('amount_info', {}).get('budget_amount', ''),
            '招标方式': lambda item: item.get('detail_data', {}).get('announcement_info', {}).get('tender_method', ''),
            '决标方式': lambda item: item.get('detail_data', {}).get('announcement_info', {}).get('award_method', ''),
            '机关地址': lambda item: item.get('detail_data', {}).get('agency_info', {}).get('agency_address', ''),
            '联络人': lambda item: item.get('detail_data', {}).get('agency_info', {}).get('contact_person', ''),
            '联络电话': lambda item: item.get('detail_data', {}).get('agency_info', {}).get('contact_phone', ''),
            '履约地点': lambda item: item.get('detail_data', {}).get('performance_info', {}).get('performance_location', ''),
            '投标厂商数': lambda item: item.get('detail_data', {}).get('bidder_count', 0),
            '得标厂商': lambda item: self._get_winner_vendor(item.get('detail_data', {}).get('vendors', [])),
            '决标金额': lambda item: self._get_winner_amount(item.get('detail_data', {}).get('vendors', []))
        }
        
        for item in data:
            filtered_item = {}
            for field in field_list:
                if field in field_mapping:
                    filtered_item[field] = field_mapping[field](item)
                else:
                    filtered_item[field] = ''
            
            filtered_data.append(filtered_item)
        
        return filtered_data
    
    def _extract_vendor_summary(self, vendors):
        """提取厂商信息摘要"""
        if not vendors:
            return '无厂商信息'
        
        summary = []
        for vendor in vendors:
            vendor_info = f"{vendor.get('vendor_name', '未知厂商')}"
            if vendor.get('is_winner') == '是':
                vendor_info += f"(得标: {vendor.get('award_amount', '未知金额')})"
            else:
                vendor_info += "(未得标)"
            summary.append(vendor_info)
        
        return '; '.join(summary)
    
    def _get_winner_vendor(self, vendors):
        """获取得标厂商名称"""
        for vendor in vendors:
            if vendor.get('is_winner') == '是':
                return vendor.get('vendor_name', '')
        return '无得标厂商'
    
    def _get_winner_amount(self, vendors):
        """获取得标金额"""
        for vendor in vendors:
            if vendor.get('is_winner') == '是':
                return vendor.get('award_amount', '')
        return '无决标金额'

def process_filtered_data(input_file, filter_type='essential'):
    """处理筛选后的数据"""
    
    print(f"🔍 开始字段筛选处理...")
    print(f"📖 读取数据文件: {input_file}")
    print(f"🎯 筛选类型: {filter_type}")
    
    try:
        # 读取原始数据
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 成功读取 {len(data)} 笔数据")
        
        # 创建筛选器
        filter_obj = ProcurementFieldFilter()
        
        # 根据类型进行筛选
        if filter_type == 'essential':
            filtered_data = filter_obj.filter_essential_fields(data)
            output_file = 'procurement_data_essential_fields.json'
            print("📋 使用核心字段筛选")
            
        elif filter_type == 'minimal':
            filtered_data = filter_obj.filter_minimal_fields(data)
            output_file = 'procurement_data_minimal_fields.json'
            print("📋 使用精简字段筛选")
            
        elif filter_type == 'custom':
            # 自定义字段列表
            custom_fields = [
                '标案名称', '机关名称', '标案案号', '决标日期', 
                '预算金额', '招标方式', '得标厂商', '决标金额'
            ]
            filtered_data = filter_obj.filter_custom_fields(data, custom_fields)
            output_file = 'procurement_data_custom_fields.json'
            print(f"📋 使用自定义字段筛选: {', '.join(custom_fields)}")
        
        else:
            print(f"❌ 不支持的筛选类型: {filter_type}")
            return
        
        # 保存筛选后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 筛选后数据已保存到: {output_file}")
        
        # 生成统计信息
        generate_filter_statistics(filtered_data, filter_type)
        
        # 生成CSV格式（便于Excel打开）
        generate_csv_output(filtered_data, output_file.replace('.json', '.csv'))
        
        return filtered_data
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")
        return []

def generate_filter_statistics(filtered_data, filter_type):
    """生成筛选统计信息"""
    print(f"\n📊 === 字段筛选统计报告 ===")
    print(f"筛选类型: {filter_type}")
    print(f"筛选后数据量: {len(filtered_data)} 笔")
    
    if filtered_data:
        print(f"字段数量: {len(filtered_data[0])} 个")
        print(f"字段列表: {', '.join(filtered_data[0].keys())}")
        
        # 统计非空字段
        non_empty_stats = {}
        for item in filtered_data:
            for key, value in item.items():
                if value and str(value).strip():
                    non_empty_stats[key] = non_empty_stats.get(key, 0) + 1
        
        print(f"\n📈 字段完整度统计:")
        for field, count in sorted(non_empty_stats.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / len(filtered_data)) * 100
            print(f"  {field}: {count}/{len(filtered_data)} ({percentage:.1f}%)")

def generate_csv_output(filtered_data, csv_file):
    """生成CSV格式输出"""
    try:
        import csv
        
        if not filtered_data:
            return
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.DictWriter(f, fieldnames=filtered_data[0].keys())
            writer.writeheader()
            writer.writerows(filtered_data)
        
        print(f"📊 CSV文件已生成: {csv_file}")
        
    except Exception as e:
        print(f"⚠️ CSV生成失败: {str(e)}")

def main():
    """主函数"""
    print("🎯 === 政府采购数据字段筛选器 ===")
    
    input_file = 'defense_procurement_data_simplified.json'
    
    # 生成三种不同的筛选结果
    print("\n1️⃣ 生成核心字段版本...")
    process_filtered_data(input_file, 'essential')
    
    print("\n2️⃣ 生成精简字段版本...")
    process_filtered_data(input_file, 'minimal')
    
    print("\n3️⃣ 生成自定义字段版本...")
    process_filtered_data(input_file, 'custom')
    
    print(f"\n✅ === 字段筛选完成 ===")
    print(f"📁 生成的文件:")
    print(f"  1. procurement_data_essential_fields.json - 核心字段版本")
    print(f"  2. procurement_data_minimal_fields.json - 精简字段版本")
    print(f"  3. procurement_data_custom_fields.json - 自定义字段版本")
    print(f"  4. 对应的CSV文件 - 便于Excel打开")

if __name__ == "__main__":
    main()

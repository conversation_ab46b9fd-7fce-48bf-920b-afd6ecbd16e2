﻿# 英特尔公布重大技术架构改变，面向 CPU、GPU 和 IPU

**发布日期**: 2021年08月20日

**原文链接**: https://baijiahao.baidu.com/s?id=1708538774834253208&wfr=spider&for=pc

## 📄 原文内容

英特尔公布重大技术架构改变，面向 CPU、GPU 和 IPU
IT之家 8 月 19 日消息 在 2021 年英特尔架构日上，英特尔公司高级副总裁兼加速计算系统和图形事业部总经理 Raja Koduri 与多位英特尔架构师，介绍了两种全新 x86 内核架构的详情。
英特尔首个性能混合架构，代号“Alder Lake”，以及智能的英特尔硬件线程调度器；专为数据中心设计的下一代英特尔至强可扩展处理器 Sapphire Rapids；基础设施处理器 (IPU)；即将推出的显卡架构，包括 Xe HPG 微架构和 Xe HPC 微架构，以及 Alchemist SoC，Ponte Vecchio SoC。
全新的英特尔能效核微架构，曾用代号“Gracemont”，旨在面对当今多任务场景，提高吞吐量效率并提供可扩展多线程性能。此高能效 x86 微架构在有限的硅片空间实现多核任务负载，并具备宽泛的频率范围。能效核可以利用各种技术进步，在不耗费处理器功率的情况下对工作负载进行优先级排序，并通过每周期指令数（IPC）改进功能直接提高性能，这些功能包括：
拥有 5000 个条目的分支目标缓存区，实现更准确的分支预测
64KB 指令缓存，在不耗费内存子系统功率的情况下保存可用指令
英特尔的首款按需指令长度解码器，可生成预解码信息
英特尔的簇乱序执行解码器，可在保持能效的同时，每周期解码多达 6 条指令
后端宽度（Wide Back End）具备 5 组宽度分配（Five-wide allocation）和 8 组宽度引退、256 个乱序窗口入口和 17 个执行端口
支持英特尔控制流强制技术和英特尔虚拟化技术重定向保护等功能
实现了 AVX 指令集以及支持整数人工智能操作的新扩展
相比英特尔最多产的 CPU 内核 Skylake，在单线程性能下，能效核能够在相同功耗下实现 40% 的性能提升，或在功耗不到 40% 的情况下提供同等性能。与运行四个线程的两个 Skylake 内核相比，四个能效核所提供的吞吐量性能，能够在功耗更低的情况下同时带来 80% 的性能提升，而在提供相同吞吐量性能时，功耗减少 80%。
英特尔全新性能核微架构，曾用代号“Golden Cove”， 旨在提高速度，突破低时延和单线程应用程序性能的限制。
更宽：解码器由 4 个增至 6 个，6op 缓存增至 8op，分配由 5 路增至 6 路，执行端口由 10 个增至 12 个
更深：更大的物理寄存器文件（physical register files），拥有 512 条目的重排序缓冲区
更智能：提高了分支预测准确度，降低了有效的一级时延，优化了二级的全写入预测带宽
性能核是英特尔有史以来构建的性能最高的 CPU 内核，并通过以下功能突破了低时延和单线程应用程序性能的极限：
相比目前的第 11 代英特尔酷睿处理器架构（Cypress Cove），在通用性能的 ISO 频率下，针对大范围的工作负载实现了平均约 19% 的改进
搭载英特尔高级矩形扩展（AMX），内置下一代 AI 加速提升技术，用于学习推理和训练。AMX 包括专用硬件和新指令集架构，以明显提高矩阵乘法运算
减少时延，对大型数据和代码体积较大的应用程序提供更好的支持
代号为“Alder Lake”的英特尔下一代客户端架构是英特尔的首款性能混合架构，它首次集成了两种内核类型：性能核和能效核。Alder Lake 基于 Intel 7 制程工艺打造而成，支持最新内存和最快 I/O。
Alder Lake 支持从超便携式笔记本，到发烧级，到商用台式机的所有客户端设备，它采用了单一、高度可扩展的 SoC 架构，提供三类产品设计形态：
高性能、双芯片、插座式的台式机处理器 ，具有领先性能和能效。支持高规格的内存和 I/O
高性能笔记本处理器，采用 BGA 封装，并加入图像单元，更大的 Xe 显卡和 Thunderbolt 4 连接
轻薄、低功耗的笔记本处理器，采用高密度的封装，配置优化的 I/O 和电能传输
英特尔需要在不影响功率的情况下满足计算和 I/O 代理对带宽的需求。为了解决这一挑战，英特尔设计了三种独立的内部总线，每一种都采用基于需求的实时启发式后处理方式。
计算内部总线可支持高达 1000GBps—— 即每个内核或每集群 100GBps，通过最后一级缓存将内核和显卡连接到内存
具有高动态频率范围，并且能够动态选择数据路径，根据实际总线结构负载而进行时延和带宽优化
根据利用率动态调整最后一级缓存策略 —— 也就是“包含”或“不包含”
I/O 内部总线支持可高达 64GBps，连接不同类型的 I/O 和内部设备，能在不干扰设备正常运行的情况下无缝改变速度，选择内部总线速度来匹配所需的数据传输量
内存结构可提供高达 204GBps 的数据，并动态扩展其总线宽度和速度，以支持高带宽、低时延或低功耗的多个操作点
为使性能核和能效核与操作系统无缝协作，英特尔开发了一种改进的调度技术，称之为“英特尔硬件线程调度器”。硬件线程调度器直接内置于硬件中，可提供对内核状态和线程指令混合比的低级遥测，让操作系统能够在恰当的时间将合适的线程放置在合适的内核上。硬件线程调度器具有动态性和自适应性 —— 它会根据实时的计算需求调整调度决策 —— 而非一种简单的、基于规则的静态方法。
传统意义上，操作系统会根据有限的可用数据做出决策，如前台和后台任务。硬件线程调度器可通过以下方式增加新维度：
使用硬件遥测工具将需要更高性能的线程引导到当时适合的性能核上
更精细地监控指令组合、每内核当前状态以及相关的微架构遥测，从而帮助操作系统做出更智能的调度决策
通过与微软合作，优化英特尔硬件线程调度器在 Windows11 上的性能
扩展 PowerThrottling API，使得开发人员能够为其线程明确指定服务质量属性
应用全新 EcoQoS 分类，该分类可让调度程序获悉线程是否更倾向于能效（此类线程会被调度到能效核）
Xe HPG 微架构和 Alchemist SoC
Xe HPG 是一款全新的独立显卡微架构。Xe HPG 微架构为 Alchemist 系列 SoC 提供动力，首批相关产品将于 2022 年第一季度上市，并采用新的品牌名 —— 英特尔锐炫（Intel Arc）。Xe HPG 微架构采用全新的 Xe 内核，是一款聚焦计算、可编程且可扩展的元件。
客户端显卡路线图包括 Alchemist（此前称之为 DG2）、Battlemage、Celestial 和 Druid SoC。在演讲中，英特尔展示了微架构细节，并分享了在试产阶段的 Alchemist SoC 上运行的演示视频，包括真实游戏展示，虚幻引擎 5 测试良好，全新的基于神经网络的超取样技术 XeSS 等。
基于 Xe HPG 微架构的 Alchemist SoC 能够提供可扩展性和计算效率，并拥有以下关键架构特征：
多达 8 个具有固定功能的渲染切片，专为 DirectX 12 Ultimate 设计
全新 Xe 内核，拥有 16 个矢量引擎和 16 个矩阵引擎（被称为 XMX，即 Xe Matrix eXtension）、高速缓存和共享内部显存
支持 DirectX Raytracing（DXR）和 Vulkan Ray Tracing 的新光线追踪单元
通过架构、逻辑设计、电路设计、制程工艺技术和软件优化，相比 Xe LP 微架构实现 1.5 倍的频率提升和 1.5 倍的每瓦性能提升
XeSS 利用 Alchemist 的内置 XMX AI 加速，带来了一种可实现高性能和高保真视觉的全新升频技术。其使用深度学习来合成接近原生高分辨率渲染质量的图像。英特尔表示，凭借 XeSS ，那些只能在低画质设置或低分辨率下玩的游戏也能在更高画质设置和分辨率下顺利运行。
XeSS 的工作原理是通过从相邻像素，以及对前一帧进行运动补偿，来重建子像素细节
重构由经过训练的神经网络执行，可提供高性能和高画质，同时性能提升高达两倍
XeSS 凭借 DP4a 指令，在包括集成显卡在内的各种硬件上提供基于 AI 的超级采样
多家早期的游戏开发商已开始使用 XeSS，本月将向独立软件供应商（ISV）提供 XMX 初始版本的 SDK，DP4a 版本将于今年晚些时候推出
下一代英特尔至强可扩展处理器（代号为“Sapphire Rapids”)
Sapphire Rapids 的核心是一个分区块、模块化的 SoC 架构，采用英特尔的嵌入式多芯片互连桥接（EMIB）封装技术，在保持单晶片 CPU 接口优势的同时，具有显著的可扩展性。Sapphire Rapids 提供了一个单一、平衡的统一内存访问架构，每个线程均可完全访问缓存、内存和 I/O 等所有单元上的全部资源，由此实现整个 SoC 具有一致的低时延和高横向带宽。
Sapphire Rapids 基于 Intel 7 制程工艺技术，采用英特尔全新的性能核微架构。
Sapphire Rapids 提供数据中心相关加速器，包括新的指令集架构和集成 IP，以在各种客户工作负载和使用中提升性能。新的内置加速器引擎包括：
英特尔加速器接口架构指令集（AIA）—— 支持对加速器和设备的有效调度、同步和信号传递
英特尔高级矩阵扩展（AMX）——Sapphire Rapids 中引入的新加速引擎，可为深度学习算法核心的 Tensor 处理提供大幅加速。其可以在每个周期内进行 2000 次 INT8 运算和 1000 次 BFP16 运算，实现计算能力的大幅提升。使用早期的 Sapphire Rapids 芯片，与使用英特尔 AVX-512 VNNI 指令的相同微基准测试版本相比，使用新的英特尔 AMX 指令集扩展优化的内部矩阵乘法微基准测试的运行速度提高了 7 倍以上，为 AI 工作负载中的训练和推理上提供了显着的性能提升
英特尔数据流加速器（DSA）—— 旨在卸载最常见的数据移动任务，这些任务会导致数据中心规模部署中的开销。英特尔 DSA 改进了对这些开销任务的处理，以提供更高的整体工作负载性能，并可以在 CPU、内存和缓存以及所有附加的内存、存储和网络设备之间移动数据
IPU 是一种可编程的网络设备，旨在使云和通信服务提供商减少在中央处理器（CPU）方面的开销。英特尔推出了以下 IPU 家族的新成员。
Mount Evans 是英特尔的首个 ASIC IPU。Mount Evans 是与一家云服务提供商共同设计和开发的。
超大规模就绪，提供高性能网络和存储虚拟化卸载，同时保持高度控制
提供业界一流的可编程数据包处理引擎，支持防火墙和虚拟路由等用例
使用硬件加速的 NVMe 存储接口，该接口扩展自英特尔傲腾技术，以模拟 NVMe 设备
采用英特尔高性能 Quick Assist 技术，部署高级加密和压缩加速
可使用现有普遍部署的 DPDK、SPDK 等软件环境进行编程，并且可以采用英特尔 Barefoot Switch 部门开创的 P4 编程语言来配置管线
Oak Springs Canyon 是一个 IPU 参考平台，基于英特尔至强 D 处理器（Intel Xeon-D）和英特尔 Agilex FPGA 构建：
卸载 Open Virtual Switch（OVS）等网络虚拟化功能以及 NVMe over Fabric 和 RoCE v2 等存储功能，并提供硬化的加密模块，提供 2x 100Gb 以太网网络接口
能够使用英特尔开放式 FPGA 开发堆栈 (英特尔 OFS) 定制其解决方案
使用现有普遍部署的软件环境进行编程，包括已在 x86 上优化的 DPDK 和 SPDK
IT之家了解到，英特尔 N6000 加速开发平台，代号为“Arrow Creek”，是专为搭载至强服务器设计的 SmartNIC。其特性包括：
英特尔 Agilex FPGA。用于高性能的 100GB 网络加速的英特尔以太网 800 系列控制器
支持多种基础设施工作负载，使通信服务提供商（CoSP）能够提供灵活的加速工作负载，如 Juniper Contrail、OVS 和 SRv6，它以英特尔 PAC-N3000 的成功为基础
Xe HPC 和 Ponte Vecchio
Ponte Vecchio 基于 Xe HPC 微架构。英特尔公布了 Xe HPC 微架构的 IP 模块信息；包括每个 Xe 核的 8 个矢量和矩阵引擎（称为 XMX Xe Matrix eXtensions）；切片和堆栈信息；以及包括计算、基础和 Xe Link 单元的处理节点的单元信息。在架构日上，英特尔表示，早期的 Ponte Vecchio 芯片展示了领先的性能，在流行的 AI 基准测试中创造了推理和训练吞吐量的行业记录。英特尔 A0 芯片性能提供了高于 45 TFLOPS 的 FP32 吞吐量，高于 5 TBps 的内存结构带宽，以及高于 2 TBps 的连接带宽。同时，英特尔分享了一段演示视频，展示了 ResNet 推理性能超过 43,000 张图像/秒和超过每秒 3400 张图像/秒的 ResNet 训练。
Ponte Vecchio 由多个复杂的设计组成，这些设计以单元形式呈现，然后通过嵌入式多芯片互连桥接（EMIB）单元进行组装，实现单元之间的低功耗、高速连接。这些设计均被集成于 Foveros 封装中，为提高功率和互连密度形成有源芯片的 3D 堆叠。高速 MDFI 互连允许 1 到 2 个堆栈的扩展。
计算单元是一个密集的多个 Xe 内核，是 Ponte Vecchio 的核心。
一块单元有 8 个 Xe 内核，总共有 4MB 一级缓存，是提供高效计算的关键
英特尔已通过设计基础设施设置和工具流程以及方法，为测试和验证该节点的单元铺平了道路
该单元具有极其紧凑的 36 微米凸点间距，可与 Foveros 进行 3D 堆叠
基础单元是 Ponte Vecchio 的连接组织。它是基于 Intel 7 制程工艺的大型芯片，针对 Foveros 技术进行了优化。
基础单元是所有复杂的 I/O 和高带宽组件与 SoC 基础设施 ——PCIe Gen5、HBM2e 内存、连接不同单元 MDFI 链路和 EMIB 桥接
采用高 2D 互连的超高带宽 3D 连接时延很低，使其成为一台无限连接的机器
英特尔技术开发团队致力于满足带宽、凸点间距和信号完整性方面的要求
Xe 链路单元提供了 GPU 之间的连接，支持每单元 8 个链路。
旨在实现支持高达 90G 的更高速 SerDes
该单元已被添加到“极光”（Aurora）百亿亿次级超级计算机的扩展解决方案中
Ponte Vecchio 已走下生产线进行上电验证，并已开始向客户提供限量样品。Ponte Vecchio 预计将于 2022 年面向 HPC 和 AI 市场发布。
目前，NVIDIA GPU、AMD GPU 和 Arm CPU 均有 Data Parallel C++（DPC++）和 oneAPI 库。同时，英特尔还提供了商业产品，包括基本的 oneAPI 基础工具包，它在规范语言和库之外增加了编译器、分析器、调试器和移植工具。
英特尔的 oneAPI 工具包拥有超过 20 万次单独安装
市场上部署的 300 多个应用程序采用了 oneAPI 的统一编程模型
超过 80 个 HPC 和 AI 应用程序使用英特尔 oneAPI 工具包在 Xe HPC 微架构上运行
5 月份发布的 1.1 版临时规范为深度学习工作负载和高级光线追踪库添加了新的图形接口，预计将在年底完成
window.jsonData = {"bsData":{"header":{"islogin":0,"username":""},"ssid":0,"comment":{"tid":"1118000043231580","forbidden":false},"newPCStyle":"1","superlanding":[{"itemType":"article","itemData":{"header":"\u82f1\u7279\u5c14\u516c\u5e03\u91cd\u5927\u6280\u672f\u67b6\u6784\u6539\u53d8\uff0c\u9762\u5411 CPU\u3001GPU \u548c IPU","original_status":1,"rumor_label":0,"infoBaiJiaHao":{"date":"21-08-20","time":"00:12","thumbnail":{"link":"https:\/\/avatar.bdstatic.com\/it\/u=1783744478,1293822274&fm=3012&app=3012&autime=1750320147&size=b200,200"},"name":"IT\u4e4b\u5bb6","link":"1551599273641720","third_id":"1551599273641720","type":"media","uk":"k4W0SjisIam7fO9RinNyPw","author_link":"https:\/\/author.baidu.com\/home?from=bjh_article&app_id=1551599273641720","vType":"2","authorTag":"\u9752\u5c9b\u8f6f\u5a92\u7f51\u7edc\u79d1\u6280\u6709\u9650\u516c\u53f8","iptopoi":[],"sign":"\u7231\u79d1\u6280\uff0c\u7231\u8fd9\u91cc - \u524d\u6cbf\u79d1\u6280\u4eba\u6c14\u5e73\u53f0","zanNum":"8898739","fansNum":"573215"},"sections":[{"type":"text","content":"IT\u4e4b\u5bb6 8 \u6708 19 \u65e5\u6d88\u606f \u5728 2021 \u5e74\u82f1\u7279\u5c14\u67b6\u6784\u65e5\u4e0a\uff0c\u82f1\u7279\u5c14\u516c\u53f8\u9ad8\u7ea7\u526f\u603b\u88c1\u517c\u52a0\u901f\u8ba1\u7b97\u7cfb\u7edf\u548c\u56fe\u5f62\u4e8b\u4e1a\u90e8\u603b\u7ecf\u7406 Raja Koduri \u4e0e\u591a\u4f4d\u82f1\u7279\u5c14\u67b6\u6784\u5e08\uff0c\u4ecb\u7ecd\u4e86\u4e24\u79cd\u5168\u65b0 x86 \u5185\u6838\u67b6\u6784\u7684\u8be6\u60c5\u3002","data_html":"<p>IT\u4e4b\u5bb6 8 \u6708 19 \u65e5\u6d88\u606f \u5728 2021 \u5e74\u82f1\u7279\u5c14\u67b6\u6784\u65e5\u4e0a\uff0c\u82f1\u7279\u5c14\u516c\u53f8\u9ad8\u7ea7\u526f\u603b\u88c1\u517c\u52a0\u901f\u8ba1\u7b97\u7cfb\u7edf\u548c\u56fe\u5f62\u4e8b\u4e1a\u90e8\u603b\u7ecf\u7406 Raja Koduri \u4e0e\u591a\u4f4d\u82f1\u7279\u5c14\u67b6\u6784\u5e08\uff0c\u4ecb\u7ecd\u4e86\u4e24\u79cd\u5168\u65b0 x86 \u5185\u6838\u67b6\u6784\u7684\u8be6\u60c5\u3002<\/p>"},{"type":"text","content":"\u82f1\u7279\u5c14\u9996\u4e2a\u6027\u80fd\u6df7\u5408\u67b6\u6784\uff0c\u4ee3\u53f7\u201cAlder Lake\u201d\uff0c\u4ee5\u53ca\u667a\u80fd\u7684\u82f1\u7279\u5c14\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\uff1b\u4e13\u4e3a\u6570\u636e\u4e2d\u5fc3\u8bbe\u8ba1\u7684\u4e0b\u4e00\u4ee3\u82f1\u7279\u5c14\u81f3\u5f3a\u53ef\u6269\u5c55\u5904\u7406\u5668 Sapphire Rapids\uff1b\u57fa\u7840\u8bbe\u65bd\u5904\u7406\u5668 (IPU)\uff1b\u5373\u5c06\u63a8\u51fa\u7684\u663e\u5361\u67b6\u6784\uff0c\u5305\u62ec Xe HPG \u5fae\u67b6\u6784\u548c Xe HPC \u5fae\u67b6\u6784\uff0c\u4ee5\u53ca Alchemist SoC\uff0cPonte Vecchio SoC\u3002","data_html":"<p>\u82f1\u7279\u5c14\u9996\u4e2a\u6027\u80fd\u6df7\u5408\u67b6\u6784\uff0c\u4ee3\u53f7\u201cAlder Lake\u201d\uff0c\u4ee5\u53ca\u667a\u80fd\u7684\u82f1\u7279\u5c14\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\uff1b\u4e13\u4e3a\u6570\u636e\u4e2d\u5fc3\u8bbe\u8ba1\u7684\u4e0b\u4e00\u4ee3\u82f1\u7279\u5c14\u81f3\u5f3a\u53ef\u6269\u5c55\u5904\u7406\u5668 Sapphire Rapids\uff1b\u57fa\u7840\u8bbe\u65bd\u5904\u7406\u5668 (IPU)\uff1b\u5373\u5c06\u63a8\u51fa\u7684\u663e\u5361\u67b6\u6784\uff0c\u5305\u62ec Xe HPG \u5fae\u67b6\u6784\u548c Xe HPC \u5fae\u67b6\u6784\uff0c\u4ee5\u53ca Alchemist SoC\uff0cPonte Vecchio SoC\u3002<\/p>"},{"type":"text","content":"X86 \u5185\u6838","data_html":"<h2>X86 \u5185\u6838<\/h2>"},{"type":"text","content":"\u80fd\u6548\u6838","data_html":"<p><strong>\u80fd\u6548\u6838<\/strong><\/p>"},{"type":"text","content":"\u5168\u65b0\u7684\u82f1\u7279\u5c14\u80fd\u6548\u6838\u5fae\u67b6\u6784\uff0c\u66fe\u7528\u4ee3\u53f7\u201cGracemont\u201d\uff0c\u65e8\u5728\u9762\u5bf9\u5f53\u4eca\u591a\u4efb\u52a1\u573a\u666f\uff0c\u63d0\u9ad8\u541e\u5410\u91cf\u6548\u7387\u5e76\u63d0\u4f9b\u53ef\u6269\u5c55\u591a\u7ebf\u7a0b\u6027\u80fd\u3002\u6b64\u9ad8\u80fd\u6548 x86 \u5fae\u67b6\u6784\u5728\u6709\u9650\u7684\u7845\u7247\u7a7a\u95f4\u5b9e\u73b0\u591a\u6838\u4efb\u52a1\u8d1f\u8f7d\uff0c\u5e76\u5177\u5907\u5bbd\u6cdb\u7684\u9891\u7387\u8303\u56f4\u3002\u80fd\u6548\u6838\u53ef\u4ee5\u5229\u7528\u5404\u79cd\u6280\u672f\u8fdb\u6b65\uff0c\u5728\u4e0d\u8017\u8d39\u5904\u7406\u5668\u529f\u7387\u7684\u60c5\u51b5\u4e0b\u5bf9\u5de5\u4f5c\u8d1f\u8f7d\u8fdb\u884c\u4f18\u5148\u7ea7\u6392\u5e8f\uff0c\u5e76\u901a\u8fc7\u6bcf\u5468\u671f\u6307\u4ee4\u6570\uff08IPC\uff09\u6539\u8fdb\u529f\u80fd\u76f4\u63a5\u63d0\u9ad8\u6027\u80fd\uff0c\u8fd9\u4e9b\u529f\u80fd\u5305\u62ec\uff1a","data_html":"<p>\u5168\u65b0\u7684\u82f1\u7279\u5c14\u80fd\u6548\u6838\u5fae\u67b6\u6784\uff0c\u66fe\u7528\u4ee3\u53f7\u201cGracemont\u201d\uff0c\u65e8\u5728\u9762\u5bf9\u5f53\u4eca\u591a\u4efb\u52a1\u573a\u666f\uff0c\u63d0\u9ad8\u541e\u5410\u91cf\u6548\u7387\u5e76\u63d0\u4f9b\u53ef\u6269\u5c55\u591a\u7ebf\u7a0b\u6027\u80fd\u3002\u6b64\u9ad8\u80fd\u6548 x86 \u5fae\u67b6\u6784\u5728\u6709\u9650\u7684\u7845\u7247\u7a7a\u95f4\u5b9e\u73b0\u591a\u6838\u4efb\u52a1\u8d1f\u8f7d\uff0c\u5e76\u5177\u5907\u5bbd\u6cdb\u7684\u9891\u7387\u8303\u56f4\u3002\u80fd\u6548\u6838\u53ef\u4ee5\u5229\u7528\u5404\u79cd\u6280\u672f\u8fdb\u6b65\uff0c\u5728\u4e0d\u8017\u8d39\u5904\u7406\u5668\u529f\u7387\u7684\u60c5\u51b5\u4e0b\u5bf9\u5de5\u4f5c\u8d1f\u8f7d\u8fdb\u884c\u4f18\u5148\u7ea7\u6392\u5e8f\uff0c\u5e76\u901a\u8fc7\u6bcf\u5468\u671f\u6307\u4ee4\u6570\uff08IPC\uff09\u6539\u8fdb\u529f\u80fd\u76f4\u63a5\u63d0\u9ad8\u6027\u80fd\uff0c\u8fd9\u4e9b\u529f\u80fd\u5305\u62ec\uff1a<\/p>"},{"type":"text","content":"\u62e5\u6709 5000 \u4e2a\u6761\u76ee\u7684\u5206\u652f\u76ee\u6807\u7f13\u5b58\u533a\uff0c\u5b9e\u73b0\u66f4\u51c6\u786e\u7684\u5206\u652f\u9884\u6d4b64KB \u6307\u4ee4\u7f13\u5b58\uff0c\u5728\u4e0d\u8017\u8d39\u5185\u5b58\u5b50\u7cfb\u7edf\u529f\u7387\u7684\u60c5\u51b5\u4e0b\u4fdd\u5b58\u53ef\u7528\u6307\u4ee4\u82f1\u7279\u5c14\u7684\u9996\u6b3e\u6309\u9700\u6307\u4ee4\u957f\u5ea6\u89e3\u7801\u5668\uff0c\u53ef\u751f\u6210\u9884\u89e3\u7801\u4fe1\u606f\u82f1\u7279\u5c14\u7684\u7c07\u4e71\u5e8f\u6267\u884c\u89e3\u7801\u5668\uff0c\u53ef\u5728\u4fdd\u6301\u80fd\u6548\u7684\u540c\u65f6\uff0c\u6bcf\u5468\u671f\u89e3\u7801\u591a\u8fbe 6 \u6761\u6307\u4ee4\u540e\u7aef\u5bbd\u5ea6\uff08Wide Back End\uff09\u5177\u5907 5 \u7ec4\u5bbd\u5ea6\u5206\u914d\uff08Five-wide allocation\uff09\u548c 8 \u7ec4\u5bbd\u5ea6\u5f15\u9000\u3001256 \u4e2a\u4e71\u5e8f\u7a97\u53e3\u5165\u53e3\u548c 17 \u4e2a\u6267\u884c\u7aef\u53e3\u652f\u6301\u82f1\u7279\u5c14\u63a7\u5236\u6d41\u5f3a\u5236\u6280\u672f\u548c\u82f1\u7279\u5c14\u865a\u62df\u5316\u6280\u672f\u91cd\u5b9a\u5411\u4fdd\u62a4\u7b49\u529f\u80fd\u5b9e\u73b0\u4e86 AVX \u6307\u4ee4\u96c6\u4ee5\u53ca\u652f\u6301\u6574\u6570\u4eba\u5de5\u667a\u80fd\u64cd\u4f5c\u7684\u65b0\u6269\u5c55","data_html":"<ul class=\" list-paddingleft-2\"><li><p>\u62e5\u6709 5000 \u4e2a\u6761\u76ee\u7684\u5206\u652f\u76ee\u6807\u7f13\u5b58\u533a\uff0c\u5b9e\u73b0\u66f4\u51c6\u786e\u7684\u5206\u652f\u9884\u6d4b<\/p><\/li><li><p>64KB \u6307\u4ee4\u7f13\u5b58\uff0c\u5728\u4e0d\u8017\u8d39\u5185\u5b58\u5b50\u7cfb\u7edf\u529f\u7387\u7684\u60c5\u51b5\u4e0b\u4fdd\u5b58\u53ef\u7528\u6307\u4ee4<\/p><\/li><li><p>\u82f1\u7279\u5c14\u7684\u9996\u6b3e\u6309\u9700\u6307\u4ee4\u957f\u5ea6\u89e3\u7801\u5668\uff0c\u53ef\u751f\u6210\u9884\u89e3\u7801\u4fe1\u606f<\/p><\/li><li><p>\u82f1\u7279\u5c14\u7684\u7c07\u4e71\u5e8f\u6267\u884c\u89e3\u7801\u5668\uff0c\u53ef\u5728\u4fdd\u6301\u80fd\u6548\u7684\u540c\u65f6\uff0c\u6bcf\u5468\u671f\u89e3\u7801\u591a\u8fbe 6 \u6761\u6307\u4ee4<\/p><\/li><li><p>\u540e\u7aef\u5bbd\u5ea6\uff08Wide Back End\uff09\u5177\u5907 5 \u7ec4\u5bbd\u5ea6\u5206\u914d\uff08Five-wide allocation\uff09\u548c 8 \u7ec4\u5bbd\u5ea6\u5f15\u9000\u3001256 \u4e2a\u4e71\u5e8f\u7a97\u53e3\u5165\u53e3\u548c 17 \u4e2a\u6267\u884c\u7aef\u53e3<\/p><\/li><li><p>\u652f\u6301\u82f1\u7279\u5c14\u63a7\u5236\u6d41\u5f3a\u5236\u6280\u672f\u548c\u82f1\u7279\u5c14\u865a\u62df\u5316\u6280\u672f\u91cd\u5b9a\u5411\u4fdd\u62a4\u7b49\u529f\u80fd<\/p><\/li><li><p>\u5b9e\u73b0\u4e86 AVX \u6307\u4ee4\u96c6\u4ee5\u53ca\u652f\u6301\u6574\u6570\u4eba\u5de5\u667a\u80fd\u64cd\u4f5c\u7684\u65b0\u6269\u5c55<\/p><\/li><\/ul>"},{"type":"text","content":"\u76f8\u6bd4\u82f1\u7279\u5c14\u6700\u591a\u4ea7\u7684 CPU \u5185\u6838 Skylake\uff0c\u5728\u5355\u7ebf\u7a0b\u6027\u80fd\u4e0b\uff0c\u80fd\u6548\u6838\u80fd\u591f\u5728\u76f8\u540c\u529f\u8017\u4e0b\u5b9e\u73b0 40% \u7684\u6027\u80fd\u63d0\u5347\uff0c\u6216\u5728\u529f\u8017\u4e0d\u5230 40% \u7684\u60c5\u51b5\u4e0b\u63d0\u4f9b\u540c\u7b49\u6027\u80fd\u3002\u4e0e\u8fd0\u884c\u56db\u4e2a\u7ebf\u7a0b\u7684\u4e24\u4e2a Skylake \u5185\u6838\u76f8\u6bd4\uff0c\u56db\u4e2a\u80fd\u6548\u6838\u6240\u63d0\u4f9b\u7684\u541e\u5410\u91cf\u6027\u80fd\uff0c\u80fd\u591f\u5728\u529f\u8017\u66f4\u4f4e\u7684\u60c5\u51b5\u4e0b\u540c\u65f6\u5e26\u6765 80% \u7684\u6027\u80fd\u63d0\u5347\uff0c\u800c\u5728\u63d0\u4f9b\u76f8\u540c\u541e\u5410\u91cf\u6027\u80fd\u65f6\uff0c\u529f\u8017\u51cf\u5c11 80%\u3002","data_html":"<p>\u76f8\u6bd4\u82f1\u7279\u5c14\u6700\u591a\u4ea7\u7684 CPU \u5185\u6838 Skylake\uff0c\u5728\u5355\u7ebf\u7a0b\u6027\u80fd\u4e0b\uff0c\u80fd\u6548\u6838\u80fd\u591f\u5728\u76f8\u540c\u529f\u8017\u4e0b\u5b9e\u73b0 40% \u7684\u6027\u80fd\u63d0\u5347\uff0c\u6216\u5728\u529f\u8017\u4e0d\u5230 40% \u7684\u60c5\u51b5\u4e0b\u63d0\u4f9b\u540c\u7b49\u6027\u80fd\u3002\u4e0e\u8fd0\u884c\u56db\u4e2a\u7ebf\u7a0b\u7684\u4e24\u4e2a Skylake \u5185\u6838\u76f8\u6bd4\uff0c\u56db\u4e2a\u80fd\u6548\u6838\u6240\u63d0\u4f9b\u7684\u541e\u5410\u91cf\u6027\u80fd\uff0c\u80fd\u591f\u5728\u529f\u8017\u66f4\u4f4e\u7684\u60c5\u51b5\u4e0b\u540c\u65f6\u5e26\u6765 80% \u7684\u6027\u80fd\u63d0\u5347\uff0c\u800c\u5728\u63d0\u4f9b\u76f8\u540c\u541e\u5410\u91cf\u6027\u80fd\u65f6\uff0c\u529f\u8017\u51cf\u5c11 80%\u3002<\/p>"},{"type":"text","content":"\u6027\u80fd\u6838","data_html":"<p><strong>\u6027\u80fd\u6838<\/strong><\/p>"},{"type":"text","content":"\u82f1\u7279\u5c14\u5168\u65b0\u6027\u80fd\u6838\u5fae\u67b6\u6784\uff0c\u66fe\u7528\u4ee3\u53f7\u201cGolden Cove\u201d\uff0c \u65e8\u5728\u63d0\u9ad8\u901f\u5ea6\uff0c\u7a81\u7834\u4f4e\u65f6\u5ef6\u548c\u5355\u7ebf\u7a0b\u5e94\u7528\u7a0b\u5e8f\u6027\u80fd\u7684\u9650\u5236\u3002","data_html":"<p>\u82f1\u7279\u5c14\u5168\u65b0\u6027\u80fd\u6838\u5fae\u67b6\u6784\uff0c\u66fe\u7528\u4ee3\u53f7\u201cGolden Cove\u201d\uff0c \u65e8\u5728\u63d0\u9ad8\u901f\u5ea6\uff0c\u7a81\u7834\u4f4e\u65f6\u5ef6\u548c\u5355\u7ebf\u7a0b\u5e94\u7528\u7a0b\u5e8f\u6027\u80fd\u7684\u9650\u5236\u3002<\/p>"},{"type":"text","content":"\u6027\u80fd\u6838\u62e5\u6709\u66f4\u5bbd\u3001\u66f4\u6df1\u3001\u66f4\u667a\u80fd\u7684\u67b6\u6784\uff1a","data_html":"<p>\u6027\u80fd\u6838\u62e5\u6709\u66f4\u5bbd\u3001\u66f4\u6df1\u3001\u66f4\u667a\u80fd\u7684\u67b6\u6784\uff1a<\/p>"},{"type":"text","content":"\u66f4\u5bbd\uff1a\u89e3\u7801\u5668\u7531 4 \u4e2a\u589e\u81f3 6 \u4e2a\uff0c6op \u7f13\u5b58\u589e\u81f3 8op\uff0c\u5206\u914d\u7531 5 \u8def\u589e\u81f3 6 \u8def\uff0c\u6267\u884c\u7aef\u53e3\u7531 10 \u4e2a\u589e\u81f3 12 \u4e2a\u66f4\u6df1\uff1a\u66f4\u5927\u7684\u7269\u7406\u5bc4\u5b58\u5668\u6587\u4ef6\uff08physical register files\uff09\uff0c\u62e5\u6709 512 \u6761\u76ee\u7684\u91cd\u6392\u5e8f\u7f13\u51b2\u533a\u66f4\u667a\u80fd\uff1a\u63d0\u9ad8\u4e86\u5206\u652f\u9884\u6d4b\u51c6\u786e\u5ea6\uff0c\u964d\u4f4e\u4e86\u6709\u6548\u7684\u4e00\u7ea7\u65f6\u5ef6\uff0c\u4f18\u5316\u4e86\u4e8c\u7ea7\u7684\u5168\u5199\u5165\u9884\u6d4b\u5e26\u5bbd","data_html":"<ul class=\"ai-word-checked list-paddingleft-2\"><li><p>\u66f4\u5bbd\uff1a\u89e3\u7801\u5668\u7531 4 \u4e2a\u589e\u81f3 6 \u4e2a\uff0c6op \u7f13\u5b58\u589e\u81f3 8op\uff0c\u5206\u914d\u7531 5 \u8def\u589e\u81f3 6 \u8def\uff0c\u6267\u884c\u7aef\u53e3\u7531 10 \u4e2a\u589e\u81f3 12 \u4e2a<\/p><\/li><li><p>\u66f4\u6df1\uff1a\u66f4\u5927\u7684\u7269\u7406\u5bc4\u5b58\u5668\u6587\u4ef6\uff08physical register files\uff09\uff0c\u62e5\u6709 512 \u6761\u76ee\u7684\u91cd\u6392\u5e8f\u7f13\u51b2\u533a<\/p><\/li><li><p>\u66f4\u667a\u80fd\uff1a\u63d0\u9ad8\u4e86\u5206\u652f\u9884\u6d4b\u51c6\u786e\u5ea6\uff0c\u964d\u4f4e\u4e86\u6709\u6548\u7684\u4e00\u7ea7\u65f6\u5ef6\uff0c\u4f18\u5316\u4e86\u4e8c\u7ea7\u7684\u5168\u5199\u5165\u9884\u6d4b\u5e26\u5bbd<\/p><\/li><\/ul>"},{"type":"text","content":"\u6027\u80fd\u6838\u662f\u82f1\u7279\u5c14\u6709\u53f2\u4ee5\u6765\u6784\u5efa\u7684\u6027\u80fd\u6700\u9ad8\u7684 CPU \u5185\u6838\uff0c\u5e76\u901a\u8fc7\u4ee5\u4e0b\u529f\u80fd\u7a81\u7834\u4e86\u4f4e\u65f6\u5ef6\u548c\u5355\u7ebf\u7a0b\u5e94\u7528\u7a0b\u5e8f\u6027\u80fd\u7684\u6781\u9650\uff1a","data_html":"<p>\u6027\u80fd\u6838\u662f\u82f1\u7279\u5c14\u6709\u53f2\u4ee5\u6765\u6784\u5efa\u7684\u6027\u80fd\u6700\u9ad8\u7684 CPU \u5185\u6838\uff0c\u5e76\u901a\u8fc7\u4ee5\u4e0b\u529f\u80fd\u7a81\u7834\u4e86\u4f4e\u65f6\u5ef6\u548c\u5355\u7ebf\u7a0b\u5e94\u7528\u7a0b\u5e8f\u6027\u80fd\u7684\u6781\u9650\uff1a<\/p>"},{"type":"text","content":"\u76f8\u6bd4\u76ee\u524d\u7684\u7b2c 11 \u4ee3\u82f1\u7279\u5c14\u9177\u777f\u5904\u7406\u5668\u67b6\u6784\uff08Cypress Cove\uff09\uff0c\u5728\u901a\u7528\u6027\u80fd\u7684 ISO \u9891\u7387\u4e0b\uff0c\u9488\u5bf9\u5927\u8303\u56f4\u7684\u5de5\u4f5c\u8d1f\u8f7d\u5b9e\u73b0\u4e86\u5e73\u5747\u7ea6 19% \u7684\u6539\u8fdb\u5448\u73b0\u51fa\u66f4\u9ad8\u7684\u5e76\u884c\u6027\u548c\u6267\u884c\u5e76\u884c\u6027\u7684\u589e\u52a0\u642d\u8f7d\u82f1\u7279\u5c14\u9ad8\u7ea7\u77e9\u5f62\u6269\u5c55\uff08AMX\uff09\uff0c\u5185\u7f6e\u4e0b\u4e00\u4ee3 AI \u52a0\u901f\u63d0\u5347\u6280\u672f\uff0c\u7528\u4e8e\u5b66\u4e60\u63a8\u7406\u548c\u8bad\u7ec3\u3002AMX \u5305\u62ec\u4e13\u7528\u786c\u4ef6\u548c\u65b0\u6307\u4ee4\u96c6\u67b6\u6784\uff0c\u4ee5\u660e\u663e\u63d0\u9ad8\u77e9\u9635\u4e58\u6cd5\u8fd0\u7b97\u51cf\u5c11\u65f6\u5ef6\uff0c\u5bf9\u5927\u578b\u6570\u636e\u548c\u4ee3\u7801\u4f53\u79ef\u8f83\u5927\u7684\u5e94\u7528\u7a0b\u5e8f\u63d0\u4f9b\u66f4\u597d\u7684\u652f\u6301","data_html":"<ul class=\" list-paddingleft-2\"><li><p>\u76f8\u6bd4\u76ee\u524d\u7684\u7b2c 11 \u4ee3\u82f1\u7279\u5c14\u9177\u777f\u5904\u7406\u5668\u67b6\u6784\uff08Cypress Cove\uff09\uff0c\u5728\u901a\u7528\u6027\u80fd\u7684 ISO \u9891\u7387\u4e0b\uff0c\u9488\u5bf9\u5927\u8303\u56f4\u7684\u5de5\u4f5c\u8d1f\u8f7d\u5b9e\u73b0\u4e86\u5e73\u5747\u7ea6 19% \u7684\u6539\u8fdb<\/p><\/li><li><p>\u5448\u73b0\u51fa\u66f4\u9ad8\u7684\u5e76\u884c\u6027\u548c\u6267\u884c\u5e76\u884c\u6027\u7684\u589e\u52a0<\/p><\/li><li><p>\u642d\u8f7d\u82f1\u7279\u5c14\u9ad8\u7ea7\u77e9\u5f62\u6269\u5c55\uff08AMX\uff09\uff0c\u5185\u7f6e\u4e0b\u4e00\u4ee3 AI \u52a0\u901f\u63d0\u5347\u6280\u672f\uff0c\u7528\u4e8e\u5b66\u4e60\u63a8\u7406\u548c\u8bad\u7ec3\u3002AMX \u5305\u62ec\u4e13\u7528\u786c\u4ef6\u548c\u65b0\u6307\u4ee4\u96c6\u67b6\u6784\uff0c\u4ee5\u660e\u663e\u63d0\u9ad8\u77e9\u9635\u4e58\u6cd5\u8fd0\u7b97<\/p><\/li><li><p>\u51cf\u5c11\u65f6\u5ef6\uff0c\u5bf9\u5927\u578b\u6570\u636e\u548c\u4ee3\u7801\u4f53\u79ef\u8f83\u5927\u7684\u5e94\u7528\u7a0b\u5e8f\u63d0\u4f9b\u66f4\u597d\u7684\u652f\u6301<\/p><\/li><\/ul>"},{"type":"text","content":"\u5ba2\u6237\u7aef","data_html":"<h2>\u5ba2\u6237\u7aef<\/h2>"},{"type":"text","content":"Alder Lake \u5ba2\u6237\u7aef SoC","data_html":"<p><strong>Alder Lake \u5ba2\u6237\u7aef SoC<\/strong><\/p>"},{"type":"text","content":"\u4ee3\u53f7\u4e3a\u201cAlder Lake\u201d\u7684\u82f1\u7279\u5c14\u4e0b\u4e00\u4ee3\u5ba2\u6237\u7aef\u67b6\u6784\u662f\u82f1\u7279\u5c14\u7684\u9996\u6b3e\u6027\u80fd\u6df7\u5408\u67b6\u6784\uff0c\u5b83\u9996\u6b21\u96c6\u6210\u4e86\u4e24\u79cd\u5185\u6838\u7c7b\u578b\uff1a\u6027\u80fd\u6838\u548c\u80fd\u6548\u6838\u3002Alder Lake \u57fa\u4e8e Intel 7 \u5236\u7a0b\u5de5\u827a\u6253\u9020\u800c\u6210\uff0c\u652f\u6301\u6700\u65b0\u5185\u5b58\u548c\u6700\u5feb I\/O\u3002","data_html":"<p>\u4ee3\u53f7\u4e3a\u201cAlder Lake\u201d\u7684\u82f1\u7279\u5c14\u4e0b\u4e00\u4ee3\u5ba2\u6237\u7aef\u67b6\u6784\u662f\u82f1\u7279\u5c14\u7684\u9996\u6b3e\u6027\u80fd\u6df7\u5408\u67b6\u6784\uff0c\u5b83\u9996\u6b21\u96c6\u6210\u4e86\u4e24\u79cd\u5185\u6838\u7c7b\u578b\uff1a\u6027\u80fd\u6838\u548c\u80fd\u6548\u6838\u3002Alder Lake \u57fa\u4e8e Intel 7 \u5236\u7a0b\u5de5\u827a\u6253\u9020\u800c\u6210\uff0c\u652f\u6301\u6700\u65b0\u5185\u5b58\u548c\u6700\u5feb I\/O\u3002<\/p>"},{"type":"img","link":"https:\/\/pics0.baidu.com\/feed\/b90e7bec54e736d123b2d3c494e4b3cad7626991.png@f_auto?token=c9ee2ff880ee2b7ab75ed9e77f828942","imgHeight":663,"imgWidth":1242,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"Alder Lake \u652f\u6301\u4ece\u8d85\u4fbf\u643a\u5f0f\u7b14\u8bb0\u672c\uff0c\u5230\u53d1\u70e7\u7ea7\uff0c\u5230\u5546\u7528\u53f0\u5f0f\u673a\u7684\u6240\u6709\u5ba2\u6237\u7aef\u8bbe\u5907\uff0c\u5b83\u91c7\u7528\u4e86\u5355\u4e00\u3001\u9ad8\u5ea6\u53ef\u6269\u5c55\u7684 SoC \u67b6\u6784\uff0c\u63d0\u4f9b\u4e09\u7c7b\u4ea7\u54c1\u8bbe\u8ba1\u5f62\u6001\uff1a","data_html":"<p>Alder Lake \u652f\u6301\u4ece\u8d85\u4fbf\u643a\u5f0f\u7b14\u8bb0\u672c\uff0c\u5230\u53d1\u70e7\u7ea7\uff0c\u5230\u5546\u7528\u53f0\u5f0f\u673a\u7684\u6240\u6709\u5ba2\u6237\u7aef\u8bbe\u5907\uff0c\u5b83\u91c7\u7528\u4e86\u5355\u4e00\u3001\u9ad8\u5ea6\u53ef\u6269\u5c55\u7684 SoC \u67b6\u6784\uff0c\u63d0\u4f9b\u4e09\u7c7b\u4ea7\u54c1\u8bbe\u8ba1\u5f62\u6001\uff1a<\/p>"},{"type":"text","content":"\u9ad8\u6027\u80fd\u3001\u53cc\u82af\u7247\u3001\u63d2\u5ea7\u5f0f\u7684\u53f0\u5f0f\u673a\u5904\u7406\u5668 \uff0c\u5177\u6709\u9886\u5148\u6027\u80fd\u548c\u80fd\u6548\u3002\u652f\u6301\u9ad8\u89c4\u683c\u7684\u5185\u5b58\u548c I\/O\u9ad8\u6027\u80fd\u7b14\u8bb0\u672c\u5904\u7406\u5668\uff0c\u91c7\u7528 BGA \u5c01\u88c5\uff0c\u5e76\u52a0\u5165\u56fe\u50cf\u5355\u5143\uff0c\u66f4\u5927\u7684 Xe \u663e\u5361\u548c Thunderbolt 4 \u8fde\u63a5\u8f7b\u8584\u3001\u4f4e\u529f\u8017\u7684\u7b14\u8bb0\u672c\u5904\u7406\u5668\uff0c\u91c7\u7528\u9ad8\u5bc6\u5ea6\u7684\u5c01\u88c5\uff0c\u914d\u7f6e\u4f18\u5316\u7684 I\/O \u548c\u7535\u80fd\u4f20\u8f93","data_html":"<ul class=\"ai-word-checked list-paddingleft-2\"><li><p>\u9ad8\u6027\u80fd\u3001\u53cc\u82af\u7247\u3001\u63d2\u5ea7\u5f0f\u7684\u53f0\u5f0f\u673a\u5904\u7406\u5668 \uff0c\u5177\u6709\u9886\u5148\u6027\u80fd\u548c\u80fd\u6548\u3002\u652f\u6301\u9ad8\u89c4\u683c\u7684\u5185\u5b58\u548c I\/O<\/p><\/li><li><p>\u9ad8\u6027\u80fd\u7b14\u8bb0\u672c\u5904\u7406\u5668\uff0c\u91c7\u7528 BGA \u5c01\u88c5\uff0c\u5e76\u52a0\u5165\u56fe\u50cf\u5355\u5143\uff0c\u66f4\u5927\u7684 Xe \u663e\u5361\u548c Thunderbolt 4 \u8fde\u63a5<\/p><\/li><li><p>\u8f7b\u8584\u3001\u4f4e\u529f\u8017\u7684\u7b14\u8bb0\u672c\u5904\u7406\u5668\uff0c\u91c7\u7528\u9ad8\u5bc6\u5ea6\u7684\u5c01\u88c5\uff0c\u914d\u7f6e\u4f18\u5316\u7684 I\/O \u548c\u7535\u80fd\u4f20\u8f93<\/p><\/li><\/ul>"},{"type":"text","content":"\u82f1\u7279\u5c14\u9700\u8981\u5728\u4e0d\u5f71\u54cd\u529f\u7387\u7684\u60c5\u51b5\u4e0b\u6ee1\u8db3\u8ba1\u7b97\u548c I\/O \u4ee3\u7406\u5bf9\u5e26\u5bbd\u7684\u9700\u6c42\u3002\u4e3a\u4e86\u89e3\u51b3\u8fd9\u4e00\u6311\u6218\uff0c\u82f1\u7279\u5c14\u8bbe\u8ba1\u4e86\u4e09\u79cd\u72ec\u7acb\u7684\u5185\u90e8\u603b\u7ebf\uff0c\u6bcf\u4e00\u79cd\u90fd\u91c7\u7528\u57fa\u4e8e\u9700\u6c42\u7684\u5b9e\u65f6\u542f\u53d1\u5f0f\u540e\u5904\u7406\u65b9\u5f0f\u3002","data_html":"<p>\u82f1\u7279\u5c14\u9700\u8981\u5728\u4e0d\u5f71\u54cd\u529f\u7387\u7684\u60c5\u51b5\u4e0b\u6ee1\u8db3\u8ba1\u7b97\u548c I\/O \u4ee3\u7406\u5bf9\u5e26\u5bbd\u7684\u9700\u6c42\u3002\u4e3a\u4e86\u89e3\u51b3\u8fd9\u4e00\u6311\u6218\uff0c\u82f1\u7279\u5c14\u8bbe\u8ba1\u4e86\u4e09\u79cd\u72ec\u7acb\u7684\u5185\u90e8\u603b\u7ebf\uff0c\u6bcf\u4e00\u79cd\u90fd\u91c7\u7528\u57fa\u4e8e\u9700\u6c42\u7684\u5b9e\u65f6\u542f\u53d1\u5f0f\u540e\u5904\u7406\u65b9\u5f0f\u3002<\/p>"},{"type":"text","content":" \u8ba1\u7b97\u5185\u90e8\u603b\u7ebf\u53ef\u652f\u6301\u9ad8\u8fbe 1000GBps\u2014\u2014 \u5373\u6bcf\u4e2a\u5185\u6838\u6216\u6bcf\u96c6\u7fa4 100GBps\uff0c\u901a\u8fc7\u6700\u540e\u4e00\u7ea7\u7f13\u5b58\u5c06\u5185\u6838\u548c\u663e\u5361\u8fde\u63a5\u5230\u5185\u5b58","data_html":"<p> \u8ba1\u7b97\u5185\u90e8\u603b\u7ebf\u53ef\u652f\u6301\u9ad8\u8fbe 1000GBps\u2014\u2014 \u5373\u6bcf\u4e2a\u5185\u6838\u6216\u6bcf\u96c6\u7fa4 100GBps\uff0c\u901a\u8fc7\u6700\u540e\u4e00\u7ea7\u7f13\u5b58\u5c06\u5185\u6838\u548c\u663e\u5361\u8fde\u63a5\u5230\u5185\u5b58<\/p>"},{"type":"text","content":"\u5177\u6709\u9ad8\u52a8\u6001\u9891\u7387\u8303\u56f4\uff0c\u5e76\u4e14\u80fd\u591f\u52a8\u6001\u9009\u62e9\u6570\u636e\u8def\u5f84\uff0c\u6839\u636e\u5b9e\u9645\u603b\u7ebf\u7ed3\u6784\u8d1f\u8f7d\u800c\u8fdb\u884c\u65f6\u5ef6\u548c\u5e26\u5bbd\u4f18\u5316\u6839\u636e\u5229\u7528\u7387\u52a8\u6001\u8c03\u6574\u6700\u540e\u4e00\u7ea7\u7f13\u5b58\u7b56\u7565 \u2014\u2014 \u4e5f\u5c31\u662f\u201c\u5305\u542b\u201d\u6216\u201c\u4e0d\u5305\u542b\u201d","data_html":"<ul class=\" list-paddingleft-2\"><li><p>\u5177\u6709\u9ad8\u52a8\u6001\u9891\u7387\u8303\u56f4\uff0c\u5e76\u4e14\u80fd\u591f\u52a8\u6001\u9009\u62e9\u6570\u636e\u8def\u5f84\uff0c\u6839\u636e\u5b9e\u9645\u603b\u7ebf\u7ed3\u6784\u8d1f\u8f7d\u800c\u8fdb\u884c\u65f6\u5ef6\u548c\u5e26\u5bbd\u4f18\u5316<\/p><\/li><li><p>\u6839\u636e\u5229\u7528\u7387\u52a8\u6001\u8c03\u6574\u6700\u540e\u4e00\u7ea7\u7f13\u5b58\u7b56\u7565 \u2014\u2014 \u4e5f\u5c31\u662f\u201c\u5305\u542b\u201d\u6216\u201c\u4e0d\u5305\u542b\u201d<\/p><\/li><\/ul>"},{"type":"text","content":" I\/O \u5185\u90e8\u603b\u7ebf\u652f\u6301\u53ef\u9ad8\u8fbe 64GBps\uff0c\u8fde\u63a5\u4e0d\u540c\u7c7b\u578b\u7684 I\/O \u548c\u5185\u90e8\u8bbe\u5907\uff0c\u80fd\u5728\u4e0d\u5e72\u6270\u8bbe\u5907\u6b63\u5e38\u8fd0\u884c\u7684\u60c5\u51b5\u4e0b\u65e0\u7f1d\u6539\u53d8\u901f\u5ea6\uff0c\u9009\u62e9\u5185\u90e8\u603b\u7ebf\u901f\u5ea6\u6765\u5339\u914d\u6240\u9700\u7684\u6570\u636e\u4f20\u8f93\u91cf","data_html":"<p> I\/O \u5185\u90e8\u603b\u7ebf\u652f\u6301\u53ef\u9ad8\u8fbe 64GBps\uff0c\u8fde\u63a5\u4e0d\u540c\u7c7b\u578b\u7684 I\/O \u548c\u5185\u90e8\u8bbe\u5907\uff0c\u80fd\u5728\u4e0d\u5e72\u6270\u8bbe\u5907\u6b63\u5e38\u8fd0\u884c\u7684\u60c5\u51b5\u4e0b\u65e0\u7f1d\u6539\u53d8\u901f\u5ea6\uff0c\u9009\u62e9\u5185\u90e8\u603b\u7ebf\u901f\u5ea6\u6765\u5339\u914d\u6240\u9700\u7684\u6570\u636e\u4f20\u8f93\u91cf<\/p>"},{"type":"text","content":" \u5185\u5b58\u7ed3\u6784\u53ef\u63d0\u4f9b\u9ad8\u8fbe 204GBps \u7684\u6570\u636e\uff0c\u5e76\u52a8\u6001\u6269\u5c55\u5176\u603b\u7ebf\u5bbd\u5ea6\u548c\u901f\u5ea6\uff0c\u4ee5\u652f\u6301\u9ad8\u5e26\u5bbd\u3001\u4f4e\u65f6\u5ef6\u6216\u4f4e\u529f\u8017\u7684\u591a\u4e2a\u64cd\u4f5c\u70b9","data_html":"<p> \u5185\u5b58\u7ed3\u6784\u53ef\u63d0\u4f9b\u9ad8\u8fbe 204GBps \u7684\u6570\u636e\uff0c\u5e76\u52a8\u6001\u6269\u5c55\u5176\u603b\u7ebf\u5bbd\u5ea6\u548c\u901f\u5ea6\uff0c\u4ee5\u652f\u6301\u9ad8\u5e26\u5bbd\u3001\u4f4e\u65f6\u5ef6\u6216\u4f4e\u529f\u8017\u7684\u591a\u4e2a\u64cd\u4f5c\u70b9<\/p>"},{"type":"text","content":"\u82f1\u7279\u5c14\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668","data_html":"<p><strong>\u82f1\u7279\u5c14\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668<\/strong><\/p>"},{"type":"text","content":"\u4e3a\u4f7f\u6027\u80fd\u6838\u548c\u80fd\u6548\u6838\u4e0e\u64cd\u4f5c\u7cfb\u7edf\u65e0\u7f1d\u534f\u4f5c\uff0c\u82f1\u7279\u5c14\u5f00\u53d1\u4e86\u4e00\u79cd\u6539\u8fdb\u7684\u8c03\u5ea6\u6280\u672f\uff0c\u79f0\u4e4b\u4e3a\u201c\u82f1\u7279\u5c14\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u201d\u3002\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u76f4\u63a5\u5185\u7f6e\u4e8e\u786c\u4ef6\u4e2d\uff0c\u53ef\u63d0\u4f9b\u5bf9\u5185\u6838\u72b6\u6001\u548c\u7ebf\u7a0b\u6307\u4ee4\u6df7\u5408\u6bd4\u7684\u4f4e\u7ea7\u9065\u6d4b\uff0c\u8ba9\u64cd\u4f5c\u7cfb\u7edf\u80fd\u591f\u5728\u6070\u5f53\u7684\u65f6\u95f4\u5c06\u5408\u9002\u7684\u7ebf\u7a0b\u653e\u7f6e\u5728\u5408\u9002\u7684\u5185\u6838\u4e0a\u3002\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u5177\u6709\u52a8\u6001\u6027\u548c\u81ea\u9002\u5e94\u6027 \u2014\u2014 \u5b83\u4f1a\u6839\u636e\u5b9e\u65f6\u7684\u8ba1\u7b97\u9700\u6c42\u8c03\u6574\u8c03\u5ea6\u51b3\u7b56 \u2014\u2014 \u800c\u975e\u4e00\u79cd\u7b80\u5355\u7684\u3001\u57fa\u4e8e\u89c4\u5219\u7684\u9759\u6001\u65b9\u6cd5\u3002","data_html":"<p>\u4e3a\u4f7f\u6027\u80fd\u6838\u548c\u80fd\u6548\u6838\u4e0e\u64cd\u4f5c\u7cfb\u7edf\u65e0\u7f1d\u534f\u4f5c\uff0c\u82f1\u7279\u5c14\u5f00\u53d1\u4e86\u4e00\u79cd\u6539\u8fdb\u7684\u8c03\u5ea6\u6280\u672f\uff0c\u79f0\u4e4b\u4e3a\u201c\u82f1\u7279\u5c14\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u201d\u3002\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u76f4\u63a5\u5185\u7f6e\u4e8e\u786c\u4ef6\u4e2d\uff0c\u53ef\u63d0\u4f9b\u5bf9\u5185\u6838\u72b6\u6001\u548c\u7ebf\u7a0b\u6307\u4ee4\u6df7\u5408\u6bd4\u7684\u4f4e\u7ea7\u9065\u6d4b\uff0c\u8ba9\u64cd\u4f5c\u7cfb\u7edf\u80fd\u591f\u5728\u6070\u5f53\u7684\u65f6\u95f4\u5c06\u5408\u9002\u7684\u7ebf\u7a0b\u653e\u7f6e\u5728\u5408\u9002\u7684\u5185\u6838\u4e0a\u3002\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u5177\u6709\u52a8\u6001\u6027\u548c\u81ea\u9002\u5e94\u6027 \u2014\u2014 \u5b83\u4f1a\u6839\u636e\u5b9e\u65f6\u7684\u8ba1\u7b97\u9700\u6c42\u8c03\u6574\u8c03\u5ea6\u51b3\u7b56 \u2014\u2014 \u800c\u975e\u4e00\u79cd\u7b80\u5355\u7684\u3001\u57fa\u4e8e\u89c4\u5219\u7684\u9759\u6001\u65b9\u6cd5\u3002<\/p>"},{"type":"text","content":"\u4f20\u7edf\u610f\u4e49\u4e0a\uff0c\u64cd\u4f5c\u7cfb\u7edf\u4f1a\u6839\u636e\u6709\u9650\u7684\u53ef\u7528\u6570\u636e\u505a\u51fa\u51b3\u7b56\uff0c\u5982\u524d\u53f0\u548c\u540e\u53f0\u4efb\u52a1\u3002\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u53ef\u901a\u8fc7\u4ee5\u4e0b\u65b9\u5f0f\u589e\u52a0\u65b0\u7ef4\u5ea6\uff1a","data_html":"<p>\u4f20\u7edf\u610f\u4e49\u4e0a\uff0c\u64cd\u4f5c\u7cfb\u7edf\u4f1a\u6839\u636e\u6709\u9650\u7684\u53ef\u7528\u6570\u636e\u505a\u51fa\u51b3\u7b56\uff0c\u5982\u524d\u53f0\u548c\u540e\u53f0\u4efb\u52a1\u3002\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u53ef\u901a\u8fc7\u4ee5\u4e0b\u65b9\u5f0f\u589e\u52a0\u65b0\u7ef4\u5ea6\uff1a<\/p>"},{"type":"text","content":"\u4f7f\u7528\u786c\u4ef6\u9065\u6d4b\u5de5\u5177\u5c06\u9700\u8981\u66f4\u9ad8\u6027\u80fd\u7684\u7ebf\u7a0b\u5f15\u5bfc\u5230\u5f53\u65f6\u9002\u5408\u7684\u6027\u80fd\u6838\u4e0a\u66f4\u7cbe\u7ec6\u5730\u76d1\u63a7\u6307\u4ee4\u7ec4\u5408\u3001\u6bcf\u5185\u6838\u5f53\u524d\u72b6\u6001\u4ee5\u53ca\u76f8\u5173\u7684\u5fae\u67b6\u6784\u9065\u6d4b\uff0c\u4ece\u800c\u5e2e\u52a9\u64cd\u4f5c\u7cfb\u7edf\u505a\u51fa\u66f4\u667a\u80fd\u7684\u8c03\u5ea6\u51b3\u7b56\u901a\u8fc7\u4e0e\u5fae\u8f6f\u5408\u4f5c\uff0c\u4f18\u5316\u82f1\u7279\u5c14\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u5728 Windows11 \u4e0a\u7684\u6027\u80fd\u6269\u5c55 PowerThrottling API\uff0c\u4f7f\u5f97\u5f00\u53d1\u4eba\u5458\u80fd\u591f\u4e3a\u5176\u7ebf\u7a0b\u660e\u786e\u6307\u5b9a\u670d\u52a1\u8d28\u91cf\u5c5e\u6027\u5e94\u7528\u5168\u65b0 EcoQoS \u5206\u7c7b\uff0c\u8be5\u5206\u7c7b\u53ef\u8ba9\u8c03\u5ea6\u7a0b\u5e8f\u83b7\u6089\u7ebf\u7a0b\u662f\u5426\u66f4\u503e\u5411\u4e8e\u80fd\u6548\uff08\u6b64\u7c7b\u7ebf\u7a0b\u4f1a\u88ab\u8c03\u5ea6\u5230\u80fd\u6548\u6838\uff09","data_html":"<ul class=\"ai-word-checked list-paddingleft-2\"><li><p>\u4f7f\u7528\u786c\u4ef6\u9065\u6d4b\u5de5\u5177\u5c06\u9700\u8981\u66f4\u9ad8\u6027\u80fd\u7684\u7ebf\u7a0b\u5f15\u5bfc\u5230\u5f53\u65f6\u9002\u5408\u7684\u6027\u80fd\u6838\u4e0a<\/p><\/li><li><p>\u66f4\u7cbe\u7ec6\u5730\u76d1\u63a7\u6307\u4ee4\u7ec4\u5408\u3001\u6bcf\u5185\u6838\u5f53\u524d\u72b6\u6001\u4ee5\u53ca\u76f8\u5173\u7684\u5fae\u67b6\u6784\u9065\u6d4b\uff0c\u4ece\u800c\u5e2e\u52a9\u64cd\u4f5c\u7cfb\u7edf\u505a\u51fa\u66f4\u667a\u80fd\u7684\u8c03\u5ea6\u51b3\u7b56<\/p><\/li><li><p>\u901a\u8fc7\u4e0e\u5fae\u8f6f\u5408\u4f5c\uff0c\u4f18\u5316\u82f1\u7279\u5c14\u786c\u4ef6\u7ebf\u7a0b\u8c03\u5ea6\u5668\u5728 Windows11 \u4e0a\u7684\u6027\u80fd<\/p><\/li><li><p>\u6269\u5c55 PowerThrottling API\uff0c\u4f7f\u5f97\u5f00\u53d1\u4eba\u5458\u80fd\u591f\u4e3a\u5176\u7ebf\u7a0b\u660e\u786e\u6307\u5b9a\u670d\u52a1\u8d28\u91cf\u5c5e\u6027<\/p><\/li><li><p>\u5e94\u7528\u5168\u65b0 EcoQoS \u5206\u7c7b\uff0c\u8be5\u5206\u7c7b\u53ef\u8ba9\u8c03\u5ea6\u7a0b\u5e8f\u83b7\u6089\u7ebf\u7a0b\u662f\u5426\u66f4\u503e\u5411\u4e8e\u80fd\u6548\uff08\u6b64\u7c7b\u7ebf\u7a0b\u4f1a\u88ab\u8c03\u5ea6\u5230\u80fd\u6548\u6838\uff09<\/p><\/li><\/ul>"},{"type":"text","content":"Xe HPG \u5fae\u67b6\u6784\u548c Alchemist SoC","data_html":"<p><strong>Xe HPG \u5fae\u67b6\u6784\u548c Alchemist SoC<\/strong><\/p>"},{"type":"text","content":"Xe HPG \u662f\u4e00\u6b3e\u5168\u65b0\u7684\u72ec\u7acb\u663e\u5361\u5fae\u67b6\u6784\u3002Xe HPG \u5fae\u67b6\u6784\u4e3a Alchemist \u7cfb\u5217 SoC \u63d0\u4f9b\u52a8\u529b\uff0c\u9996\u6279\u76f8\u5173\u4ea7\u54c1\u5c06\u4e8e 2022 \u5e74\u7b2c\u4e00\u5b63\u5ea6\u4e0a\u5e02\uff0c\u5e76\u91c7\u7528\u65b0\u7684\u54c1\u724c\u540d \u2014\u2014 \u82f1\u7279\u5c14\u9510\u70ab\uff08Intel Arc\uff09\u3002Xe HPG \u5fae\u67b6\u6784\u91c7\u7528\u5168\u65b0\u7684 Xe \u5185\u6838\uff0c\u662f\u4e00\u6b3e\u805a\u7126\u8ba1\u7b97\u3001\u53ef\u7f16\u7a0b\u4e14\u53ef\u6269\u5c55\u7684\u5143\u4ef6\u3002","data_html":"<p>Xe HPG \u662f\u4e00\u6b3e\u5168\u65b0\u7684\u72ec\u7acb\u663e\u5361\u5fae\u67b6\u6784\u3002Xe HPG \u5fae\u67b6\u6784\u4e3a Alchemist \u7cfb\u5217 SoC \u63d0\u4f9b\u52a8\u529b\uff0c\u9996\u6279\u76f8\u5173\u4ea7\u54c1\u5c06\u4e8e 2022 \u5e74\u7b2c\u4e00\u5b63\u5ea6\u4e0a\u5e02\uff0c\u5e76\u91c7\u7528\u65b0\u7684\u54c1\u724c\u540d \u2014\u2014 \u82f1\u7279\u5c14\u9510\u70ab\uff08Intel Arc\uff09\u3002Xe HPG \u5fae\u67b6\u6784\u91c7\u7528\u5168\u65b0\u7684 Xe \u5185\u6838\uff0c\u662f\u4e00\u6b3e\u805a\u7126\u8ba1\u7b97\u3001\u53ef\u7f16\u7a0b\u4e14\u53ef\u6269\u5c55\u7684\u5143\u4ef6\u3002<\/p>"},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/94cad1c8a786c9173a760393df898cc73ac7575b.jpeg@f_auto?token=ca75f57c0ccb938461f4f07b9afeefd7","imgHeight":576,"imgWidth":1024,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"\u5ba2\u6237\u7aef\u663e\u5361\u8def\u7ebf\u56fe\u5305\u62ec Alchemist\uff08\u6b64\u524d\u79f0\u4e4b\u4e3a DG2\uff09\u3001Battlemage\u3001Celestial \u548c Druid SoC\u3002\u5728\u6f14\u8bb2\u4e2d\uff0c\u82f1\u7279\u5c14\u5c55\u793a\u4e86\u5fae\u67b6\u6784\u7ec6\u8282\uff0c\u5e76\u5206\u4eab\u4e86\u5728\u8bd5\u4ea7\u9636\u6bb5\u7684 Alchemist SoC \u4e0a\u8fd0\u884c\u7684\u6f14\u793a\u89c6\u9891\uff0c\u5305\u62ec\u771f\u5b9e\u6e38\u620f\u5c55\u793a\uff0c\u865a\u5e7b\u5f15\u64ce 5 \u6d4b\u8bd5\u826f\u597d\uff0c\u5168\u65b0\u7684\u57fa\u4e8e\u795e\u7ecf\u7f51\u7edc\u7684\u8d85\u53d6\u6837\u6280\u672f XeSS \u7b49\u3002","data_html":"<p>\u5ba2\u6237\u7aef\u663e\u5361\u8def\u7ebf\u56fe\u5305\u62ec Alchemist\uff08\u6b64\u524d\u79f0\u4e4b\u4e3a DG2\uff09\u3001Battlemage\u3001Celestial \u548c Druid SoC\u3002\u5728\u6f14\u8bb2\u4e2d\uff0c\u82f1\u7279\u5c14\u5c55\u793a\u4e86\u5fae\u67b6\u6784\u7ec6\u8282\uff0c\u5e76\u5206\u4eab\u4e86\u5728\u8bd5\u4ea7\u9636\u6bb5\u7684 Alchemist SoC \u4e0a\u8fd0\u884c\u7684\u6f14\u793a\u89c6\u9891\uff0c\u5305\u62ec\u771f\u5b9e\u6e38\u620f\u5c55\u793a\uff0c\u865a\u5e7b\u5f15\u64ce 5 \u6d4b\u8bd5\u826f\u597d\uff0c\u5168\u65b0\u7684\u57fa\u4e8e\u795e\u7ecf\u7f51\u7edc\u7684\u8d85\u53d6\u6837\u6280\u672f XeSS \u7b49\u3002<\/p>"},{"type":"text","content":"\u57fa\u4e8e Xe HPG \u5fae\u67b6\u6784\u7684 Alchemist SoC \u80fd\u591f\u63d0\u4f9b\u53ef\u6269\u5c55\u6027\u548c\u8ba1\u7b97\u6548\u7387\uff0c\u5e76\u62e5\u6709\u4ee5\u4e0b\u5173\u952e\u67b6\u6784\u7279\u5f81\uff1a","data_html":"<p>\u57fa\u4e8e Xe HPG \u5fae\u67b6\u6784\u7684 Alchemist SoC \u80fd\u591f\u63d0\u4f9b\u53ef\u6269\u5c55\u6027\u548c\u8ba1\u7b97\u6548\u7387\uff0c\u5e76\u62e5\u6709\u4ee5\u4e0b\u5173\u952e\u67b6\u6784\u7279\u5f81\uff1a<\/p>"},{"type":"text","content":"\u591a\u8fbe 8 \u4e2a\u5177\u6709\u56fa\u5b9a\u529f\u80fd\u7684\u6e32\u67d3\u5207\u7247\uff0c\u4e13\u4e3a DirectX 12 Ultimate \u8bbe\u8ba1\u5168\u65b0 Xe \u5185\u6838\uff0c\u62e5\u6709 16 \u4e2a\u77e2\u91cf\u5f15\u64ce\u548c 16 \u4e2a\u77e9\u9635\u5f15\u64ce\uff08\u88ab\u79f0\u4e3a XMX\uff0c\u5373 Xe Matrix eXtension\uff09\u3001\u9ad8\u901f\u7f13\u5b58\u548c\u5171\u4eab\u5185\u90e8\u663e\u5b58\u652f\u6301 DirectX Raytracing\uff08DXR\uff09\u548c Vulkan Ray Tracing \u7684\u65b0\u5149\u7ebf\u8ffd\u8e2a\u5355\u5143\u901a\u8fc7\u67b6\u6784\u3001\u903b\u8f91\u8bbe\u8ba1\u3001\u7535\u8def\u8bbe\u8ba1\u3001\u5236\u7a0b\u5de5\u827a\u6280\u672f\u548c\u8f6f\u4ef6\u4f18\u5316\uff0c\u76f8\u6bd4 Xe LP \u5fae\u67b6\u6784\u5b9e\u73b0 1.5 \u500d\u7684\u9891\u7387\u63d0\u5347\u548c 1.5 \u500d\u7684\u6bcf\u74e6\u6027\u80fd\u63d0\u5347\u4f7f\u7528\u53f0\u79ef\u7535\u7684 N6 \u5236\u7a0b\u8282\u70b9\u4e0a\u8fdb\u884c\u5236\u9020","data_html":"<ul class=\" list-paddingleft-2\"><li><p>\u591a\u8fbe 8 \u4e2a\u5177\u6709\u56fa\u5b9a\u529f\u80fd\u7684\u6e32\u67d3\u5207\u7247\uff0c\u4e13\u4e3a DirectX 12 Ultimate \u8bbe\u8ba1<\/p><\/li><li><p>\u5168\u65b0 Xe \u5185\u6838\uff0c\u62e5\u6709 16 \u4e2a\u77e2\u91cf\u5f15\u64ce\u548c 16 \u4e2a\u77e9\u9635\u5f15\u64ce\uff08\u88ab\u79f0\u4e3a XMX\uff0c\u5373 Xe Matrix eXtension\uff09\u3001\u9ad8\u901f\u7f13\u5b58\u548c\u5171\u4eab\u5185\u90e8\u663e\u5b58<\/p><\/li><li><p>\u652f\u6301 DirectX Raytracing\uff08DXR\uff09\u548c Vulkan Ray Tracing \u7684\u65b0\u5149\u7ebf\u8ffd\u8e2a\u5355\u5143<\/p><\/li><li><p>\u901a\u8fc7\u67b6\u6784\u3001\u903b\u8f91\u8bbe\u8ba1\u3001\u7535\u8def\u8bbe\u8ba1\u3001\u5236\u7a0b\u5de5\u827a\u6280\u672f\u548c\u8f6f\u4ef6\u4f18\u5316\uff0c\u76f8\u6bd4 Xe LP \u5fae\u67b6\u6784\u5b9e\u73b0 1.5 \u500d\u7684\u9891\u7387\u63d0\u5347\u548c 1.5 \u500d\u7684\u6bcf\u74e6\u6027\u80fd\u63d0\u5347<\/p><\/li><li><p>\u4f7f\u7528\u53f0\u79ef\u7535\u7684 N6 \u5236\u7a0b\u8282\u70b9\u4e0a\u8fdb\u884c\u5236\u9020<\/p><\/li><\/ul>"},{"type":"text","content":"XeSS","data_html":"<p><strong>XeSS<\/strong><\/p>"},{"type":"text","content":"XeSS \u5229\u7528 Alchemist \u7684\u5185\u7f6e XMX AI \u52a0\u901f\uff0c\u5e26\u6765\u4e86\u4e00\u79cd\u53ef\u5b9e\u73b0\u9ad8\u6027\u80fd\u548c\u9ad8\u4fdd\u771f\u89c6\u89c9\u7684\u5168\u65b0\u5347\u9891\u6280\u672f\u3002\u5176\u4f7f\u7528\u6df1\u5ea6\u5b66\u4e60\u6765\u5408\u6210\u63a5\u8fd1\u539f\u751f\u9ad8\u5206\u8fa8\u7387\u6e32\u67d3\u8d28\u91cf\u7684\u56fe\u50cf\u3002\u82f1\u7279\u5c14\u8868\u793a\uff0c\u51ed\u501f XeSS \uff0c\u90a3\u4e9b\u53ea\u80fd\u5728\u4f4e\u753b\u8d28\u8bbe\u7f6e\u6216\u4f4e\u5206\u8fa8\u7387\u4e0b\u73a9\u7684\u6e38\u620f\u4e5f\u80fd\u5728\u66f4\u9ad8\u753b\u8d28\u8bbe\u7f6e\u548c\u5206\u8fa8\u7387\u4e0b\u987a\u5229\u8fd0\u884c\u3002","data_html":"<p>XeSS \u5229\u7528 Alchemist \u7684\u5185\u7f6e XMX AI \u52a0\u901f\uff0c\u5e26\u6765\u4e86\u4e00\u79cd\u53ef\u5b9e\u73b0\u9ad8\u6027\u80fd\u548c\u9ad8\u4fdd\u771f\u89c6\u89c9\u7684\u5168\u65b0\u5347\u9891\u6280\u672f\u3002\u5176\u4f7f\u7528\u6df1\u5ea6\u5b66\u4e60\u6765\u5408\u6210\u63a5\u8fd1\u539f\u751f\u9ad8\u5206\u8fa8\u7387\u6e32\u67d3\u8d28\u91cf\u7684\u56fe\u50cf\u3002\u82f1\u7279\u5c14\u8868\u793a\uff0c\u51ed\u501f XeSS \uff0c\u90a3\u4e9b\u53ea\u80fd\u5728\u4f4e\u753b\u8d28\u8bbe\u7f6e\u6216\u4f4e\u5206\u8fa8\u7387\u4e0b\u73a9\u7684\u6e38\u620f\u4e5f\u80fd\u5728\u66f4\u9ad8\u753b\u8d28\u8bbe\u7f6e\u548c\u5206\u8fa8\u7387\u4e0b\u987a\u5229\u8fd0\u884c\u3002<\/p>"},{"type":"text","content":"XeSS \u7684\u5de5\u4f5c\u539f\u7406\u662f\u901a\u8fc7\u4ece\u76f8\u90bb\u50cf\u7d20\uff0c\u4ee5\u53ca\u5bf9\u524d\u4e00\u5e27\u8fdb\u884c\u8fd0\u52a8\u8865\u507f\uff0c\u6765\u91cd\u5efa\u5b50\u50cf\u7d20\u7ec6\u8282\u91cd\u6784\u7531\u7ecf\u8fc7\u8bad\u7ec3\u7684\u795e\u7ecf\u7f51\u7edc\u6267\u884c\uff0c\u53ef\u63d0\u4f9b\u9ad8\u6027\u80fd\u548c\u9ad8\u753b\u8d28\uff0c\u540c\u65f6\u6027\u80fd\u63d0\u5347\u9ad8\u8fbe\u4e24\u500dXeSS \u51ed\u501f DP4a \u6307\u4ee4\uff0c\u5728\u5305\u62ec\u96c6\u6210\u663e\u5361\u5728\u5185\u7684\u5404\u79cd\u786c\u4ef6\u4e0a\u63d0\u4f9b\u57fa\u4e8e AI \u7684\u8d85\u7ea7\u91c7\u6837\u591a\u5bb6\u65e9\u671f\u7684\u6e38\u620f\u5f00\u53d1\u5546\u5df2\u5f00\u59cb\u4f7f\u7528 XeSS\uff0c\u672c\u6708\u5c06\u5411\u72ec\u7acb\u8f6f\u4ef6\u4f9b\u5e94\u5546\uff08ISV\uff09\u63d0\u4f9b XMX \u521d\u59cb\u7248\u672c\u7684 SDK\uff0cDP4a \u7248\u672c\u5c06\u4e8e\u4eca\u5e74\u665a\u4e9b\u65f6\u5019\u63a8\u51fa","data_html":"<ul class=\"ai-word-checked list-paddingleft-2\"><li><p>XeSS \u7684\u5de5\u4f5c\u539f\u7406\u662f\u901a\u8fc7\u4ece\u76f8\u90bb\u50cf\u7d20\uff0c\u4ee5\u53ca\u5bf9\u524d\u4e00\u5e27\u8fdb\u884c\u8fd0\u52a8\u8865\u507f\uff0c\u6765\u91cd\u5efa\u5b50\u50cf\u7d20\u7ec6\u8282<\/p><\/li><li><p>\u91cd\u6784\u7531\u7ecf\u8fc7\u8bad\u7ec3\u7684\u795e\u7ecf\u7f51\u7edc\u6267\u884c\uff0c\u53ef\u63d0\u4f9b\u9ad8\u6027\u80fd\u548c\u9ad8\u753b\u8d28\uff0c\u540c\u65f6\u6027\u80fd\u63d0\u5347\u9ad8\u8fbe\u4e24\u500d<\/p><\/li><li><p>XeSS \u51ed\u501f DP4a \u6307\u4ee4\uff0c\u5728\u5305\u62ec\u96c6\u6210\u663e\u5361\u5728\u5185\u7684\u5404\u79cd\u786c\u4ef6\u4e0a\u63d0\u4f9b\u57fa\u4e8e AI \u7684\u8d85\u7ea7\u91c7\u6837<\/p><\/li><li><p>\u591a\u5bb6\u65e9\u671f\u7684\u6e38\u620f\u5f00\u53d1\u5546\u5df2\u5f00\u59cb\u4f7f\u7528 XeSS\uff0c\u672c\u6708\u5c06\u5411\u72ec\u7acb\u8f6f\u4ef6\u4f9b\u5e94\u5546\uff08ISV\uff09\u63d0\u4f9b XMX \u521d\u59cb\u7248\u672c\u7684 SDK\uff0cDP4a \u7248\u672c\u5c06\u4e8e\u4eca\u5e74\u665a\u4e9b\u65f6\u5019\u63a8\u51fa<\/p><\/li><\/ul>"},{"type":"text","content":"\u6570\u636e\u4e2d\u5fc3","data_html":"<h2>\u6570\u636e\u4e2d\u5fc3<\/h2>"},{"type":"text","content":"\u4e0b\u4e00\u4ee3\u82f1\u7279\u5c14\u81f3\u5f3a\u53ef\u6269\u5c55\u5904\u7406\u5668\uff08\u4ee3\u53f7\u4e3a\u201cSapphire Rapids\u201d)","data_html":"<p><strong>\u4e0b\u4e00\u4ee3\u82f1\u7279\u5c14\u81f3\u5f3a\u53ef\u6269\u5c55\u5904\u7406\u5668\uff08\u4ee3\u53f7\u4e3a\u201cSapphire Rapids\u201d)<\/strong><\/p>"},{"type":"text","content":"Sapphire Rapids \u7684\u6838\u5fc3\u662f\u4e00\u4e2a\u5206\u533a\u5757\u3001\u6a21\u5757\u5316\u7684 SoC \u67b6\u6784\uff0c\u91c7\u7528\u82f1\u7279\u5c14\u7684\u5d4c\u5165\u5f0f\u591a\u82af\u7247\u4e92\u8fde\u6865\u63a5\uff08EMIB\uff09\u5c01\u88c5\u6280\u672f\uff0c\u5728\u4fdd\u6301\u5355\u6676\u7247 CPU \u63a5\u53e3\u4f18\u52bf\u7684\u540c\u65f6\uff0c\u5177\u6709\u663e\u8457\u7684\u53ef\u6269\u5c55\u6027\u3002Sapphire Rapids \u63d0\u4f9b\u4e86\u4e00\u4e2a\u5355\u4e00\u3001\u5e73\u8861\u7684\u7edf\u4e00\u5185\u5b58\u8bbf\u95ee\u67b6\u6784\uff0c\u6bcf\u4e2a\u7ebf\u7a0b\u5747\u53ef\u5b8c\u5168\u8bbf\u95ee\u7f13\u5b58\u3001\u5185\u5b58\u548c I\/O \u7b49\u6240\u6709\u5355\u5143\u4e0a\u7684\u5168\u90e8\u8d44\u6e90\uff0c\u7531\u6b64\u5b9e\u73b0\u6574\u4e2a SoC \u5177\u6709\u4e00\u81f4\u7684\u4f4e\u65f6\u5ef6\u548c\u9ad8\u6a2a\u5411\u5e26\u5bbd\u3002","data_html":"<p>Sapphire Rapids \u7684\u6838\u5fc3\u662f\u4e00\u4e2a\u5206\u533a\u5757\u3001\u6a21\u5757\u5316\u7684 SoC \u67b6\u6784\uff0c\u91c7\u7528\u82f1\u7279\u5c14\u7684\u5d4c\u5165\u5f0f\u591a\u82af\u7247\u4e92\u8fde\u6865\u63a5\uff08EMIB\uff09\u5c01\u88c5\u6280\u672f\uff0c\u5728\u4fdd\u6301\u5355\u6676\u7247 CPU \u63a5\u53e3\u4f18\u52bf\u7684\u540c\u65f6\uff0c\u5177\u6709\u663e\u8457\u7684\u53ef\u6269\u5c55\u6027\u3002Sapphire Rapids \u63d0\u4f9b\u4e86\u4e00\u4e2a\u5355\u4e00\u3001\u5e73\u8861\u7684\u7edf\u4e00\u5185\u5b58\u8bbf\u95ee\u67b6\u6784\uff0c\u6bcf\u4e2a\u7ebf\u7a0b\u5747\u53ef\u5b8c\u5168\u8bbf\u95ee\u7f13\u5b58\u3001\u5185\u5b58\u548c I\/O \u7b49\u6240\u6709\u5355\u5143\u4e0a\u7684\u5168\u90e8\u8d44\u6e90\uff0c\u7531\u6b64\u5b9e\u73b0\u6574\u4e2a SoC \u5177\u6709\u4e00\u81f4\u7684\u4f4e\u65f6\u5ef6\u548c\u9ad8\u6a2a\u5411\u5e26\u5bbd\u3002<\/p>"},{"type":"text","content":"Sapphire Rapids \u57fa\u4e8e Intel 7 \u5236\u7a0b\u5de5\u827a\u6280\u672f\uff0c\u91c7\u7528\u82f1\u7279\u5c14\u5168\u65b0\u7684\u6027\u80fd\u6838\u5fae\u67b6\u6784\u3002","data_html":"<p>Sapphire Rapids \u57fa\u4e8e Intel 7 \u5236\u7a0b\u5de5\u827a\u6280\u672f\uff0c\u91c7\u7528\u82f1\u7279\u5c14\u5168\u65b0\u7684\u6027\u80fd\u6838\u5fae\u67b6\u6784\u3002<\/p>"},{"type":"text","content":"Sapphire Rapids \u63d0\u4f9b\u6570\u636e\u4e2d\u5fc3\u76f8\u5173\u52a0\u901f\u5668\uff0c\u5305\u62ec\u65b0\u7684\u6307\u4ee4\u96c6\u67b6\u6784\u548c\u96c6\u6210 IP\uff0c\u4ee5\u5728\u5404\u79cd\u5ba2\u6237\u5de5\u4f5c\u8d1f\u8f7d\u548c\u4f7f\u7528\u4e2d\u63d0\u5347\u6027\u80fd\u3002\u65b0\u7684\u5185\u7f6e\u52a0\u901f\u5668\u5f15\u64ce\u5305\u62ec\uff1a","data_html":"<p>Sapphire Rapids \u63d0\u4f9b\u6570\u636e\u4e2d\u5fc3\u76f8\u5173\u52a0\u901f\u5668\uff0c\u5305\u62ec\u65b0\u7684\u6307\u4ee4\u96c6\u67b6\u6784\u548c\u96c6\u6210 IP\uff0c\u4ee5\u5728\u5404\u79cd\u5ba2\u6237\u5de5\u4f5c\u8d1f\u8f7d\u548c\u4f7f\u7528\u4e2d\u63d0\u5347\u6027\u80fd\u3002\u65b0\u7684\u5185\u7f6e\u52a0\u901f\u5668\u5f15\u64ce\u5305\u62ec\uff1a<\/p>"},{"type":"text","content":"\u82f1\u7279\u5c14\u52a0\u901f\u5668\u63a5\u53e3\u67b6\u6784\u6307\u4ee4\u96c6\uff08AIA\uff09\u2014\u2014 \u652f\u6301\u5bf9\u52a0\u901f\u5668\u548c\u8bbe\u5907\u7684\u6709\u6548\u8c03\u5ea6\u3001\u540c\u6b65\u548c\u4fe1\u53f7\u4f20\u9012\u82f1\u7279\u5c14\u9ad8\u7ea7\u77e9\u9635\u6269\u5c55\uff08AMX\uff09\u2014\u2014Sapphire Rapids \u4e2d\u5f15\u5165\u7684\u65b0\u52a0\u901f\u5f15\u64ce\uff0c\u53ef\u4e3a\u6df1\u5ea6\u5b66\u4e60\u7b97\u6cd5\u6838\u5fc3\u7684 Tensor \u5904\u7406\u63d0\u4f9b\u5927\u5e45\u52a0\u901f\u3002\u5176\u53ef\u4ee5\u5728\u6bcf\u4e2a\u5468\u671f\u5185\u8fdb\u884c 2000 \u6b21 INT8 \u8fd0\u7b97\u548c 1000 \u6b21 BFP16 \u8fd0\u7b97\uff0c\u5b9e\u73b0\u8ba1\u7b97\u80fd\u529b\u7684\u5927\u5e45\u63d0\u5347\u3002\u4f7f\u7528\u65e9\u671f\u7684 Sapphire Rapids \u82af\u7247\uff0c\u4e0e\u4f7f\u7528\u82f1\u7279\u5c14 AVX-512 VNNI \u6307\u4ee4\u7684\u76f8\u540c\u5fae\u57fa\u51c6\u6d4b\u8bd5\u7248\u672c\u76f8\u6bd4\uff0c\u4f7f\u7528\u65b0\u7684\u82f1\u7279\u5c14 AMX \u6307\u4ee4\u96c6\u6269\u5c55\u4f18\u5316\u7684\u5185\u90e8\u77e9\u9635\u4e58\u6cd5\u5fae\u57fa\u51c6\u6d4b\u8bd5\u7684\u8fd0\u884c\u901f\u5ea6\u63d0\u9ad8\u4e86 7 \u500d\u4ee5\u4e0a\uff0c\u4e3a AI \u5de5\u4f5c\u8d1f\u8f7d\u4e2d\u7684\u8bad\u7ec3\u548c\u63a8\u7406\u4e0a\u63d0\u4f9b\u4e86\u663e\u7740\u7684\u6027\u80fd\u63d0\u5347\u82f1\u7279\u5c14\u6570\u636e\u6d41\u52a0\u901f\u5668\uff08DSA\uff09\u2014\u2014 \u65e8\u5728\u5378\u8f7d\u6700\u5e38\u89c1\u7684\u6570\u636e\u79fb\u52a8\u4efb\u52a1\uff0c\u8fd9\u4e9b\u4efb\u52a1\u4f1a\u5bfc\u81f4\u6570\u636e\u4e2d\u5fc3\u89c4\u6a21\u90e8\u7f72\u4e2d\u7684\u5f00\u9500\u3002\u82f1\u7279\u5c14 DSA \u6539\u8fdb\u4e86\u5bf9\u8fd9\u4e9b\u5f00\u9500\u4efb\u52a1\u7684\u5904\u7406\uff0c\u4ee5\u63d0\u4f9b\u66f4\u9ad8\u7684\u6574\u4f53\u5de5\u4f5c\u8d1f\u8f7d\u6027\u80fd\uff0c\u5e76\u53ef\u4ee5\u5728 CPU\u3001\u5185\u5b58\u548c\u7f13\u5b58\u4ee5\u53ca\u6240\u6709\u9644\u52a0\u7684\u5185\u5b58\u3001\u5b58\u50a8\u548c\u7f51\u7edc\u8bbe\u5907\u4e4b\u95f4\u79fb\u52a8\u6570\u636e","data_html":"<ul class=\" list-paddingleft-2\"><li><p>\u82f1\u7279\u5c14\u52a0\u901f\u5668\u63a5\u53e3\u67b6\u6784\u6307\u4ee4\u96c6\uff08AIA\uff09\u2014\u2014 \u652f\u6301\u5bf9\u52a0\u901f\u5668\u548c\u8bbe\u5907\u7684\u6709\u6548\u8c03\u5ea6\u3001\u540c\u6b65\u548c\u4fe1\u53f7\u4f20\u9012<\/p><\/li><li><p>\u82f1\u7279\u5c14\u9ad8\u7ea7\u77e9\u9635\u6269\u5c55\uff08AMX\uff09\u2014\u2014Sapphire Rapids \u4e2d\u5f15\u5165\u7684\u65b0\u52a0\u901f\u5f15\u64ce\uff0c\u53ef\u4e3a\u6df1\u5ea6\u5b66\u4e60\u7b97\u6cd5\u6838\u5fc3\u7684 Tensor \u5904\u7406\u63d0\u4f9b\u5927\u5e45\u52a0\u901f\u3002\u5176\u53ef\u4ee5\u5728\u6bcf\u4e2a\u5468\u671f\u5185\u8fdb\u884c 2000 \u6b21 INT8 \u8fd0\u7b97\u548c 1000 \u6b21 BFP16 \u8fd0\u7b97\uff0c\u5b9e\u73b0\u8ba1\u7b97\u80fd\u529b\u7684\u5927\u5e45\u63d0\u5347\u3002\u4f7f\u7528\u65e9\u671f\u7684 Sapphire Rapids \u82af\u7247\uff0c\u4e0e\u4f7f\u7528\u82f1\u7279\u5c14 AVX-512 VNNI \u6307\u4ee4\u7684\u76f8\u540c\u5fae\u57fa\u51c6\u6d4b\u8bd5\u7248\u672c\u76f8\u6bd4\uff0c\u4f7f\u7528\u65b0\u7684\u82f1\u7279\u5c14 AMX \u6307\u4ee4\u96c6\u6269\u5c55\u4f18\u5316\u7684\u5185\u90e8\u77e9\u9635\u4e58\u6cd5\u5fae\u57fa\u51c6\u6d4b\u8bd5\u7684\u8fd0\u884c\u901f\u5ea6\u63d0\u9ad8\u4e86 7 \u500d\u4ee5\u4e0a\uff0c\u4e3a AI \u5de5\u4f5c\u8d1f\u8f7d\u4e2d\u7684\u8bad\u7ec3\u548c\u63a8\u7406\u4e0a\u63d0\u4f9b\u4e86\u663e\u7740\u7684\u6027\u80fd\u63d0\u5347<\/p><\/li><li><p>\u82f1\u7279\u5c14\u6570\u636e\u6d41\u52a0\u901f\u5668\uff08DSA\uff09\u2014\u2014 \u65e8\u5728\u5378\u8f7d\u6700\u5e38\u89c1\u7684\u6570\u636e\u79fb\u52a8\u4efb\u52a1\uff0c\u8fd9\u4e9b\u4efb\u52a1\u4f1a\u5bfc\u81f4\u6570\u636e\u4e2d\u5fc3\u89c4\u6a21\u90e8\u7f72\u4e2d\u7684\u5f00\u9500\u3002\u82f1\u7279\u5c14 DSA \u6539\u8fdb\u4e86\u5bf9\u8fd9\u4e9b\u5f00\u9500\u4efb\u52a1\u7684\u5904\u7406\uff0c\u4ee5\u63d0\u4f9b\u66f4\u9ad8\u7684\u6574\u4f53\u5de5\u4f5c\u8d1f\u8f7d\u6027\u80fd\uff0c\u5e76\u53ef\u4ee5\u5728 CPU\u3001\u5185\u5b58\u548c\u7f13\u5b58\u4ee5\u53ca\u6240\u6709\u9644\u52a0\u7684\u5185\u5b58\u3001\u5b58\u50a8\u548c\u7f51\u7edc\u8bbe\u5907\u4e4b\u95f4\u79fb\u52a8\u6570\u636e<\/p><\/li><\/ul>"},{"type":"text","content":"\u57fa\u7840\u8bbe\u65bd\u5904\u7406\u5668\uff08IPU\uff09","data_html":"<p><strong>\u57fa\u7840\u8bbe\u65bd\u5904\u7406\u5668\uff08IPU\uff09<\/strong><\/p>"},{"type":"text","content":"IPU \u662f\u4e00\u79cd\u53ef\u7f16\u7a0b\u7684\u7f51\u7edc\u8bbe\u5907\uff0c\u65e8\u5728\u4f7f\u4e91\u548c\u901a\u4fe1\u670d\u52a1\u63d0\u4f9b\u5546\u51cf\u5c11\u5728\u4e2d\u592e\u5904\u7406\u5668\uff08CPU\uff09\u65b9\u9762\u7684\u5f00\u9500\u3002\u82f1\u7279\u5c14\u63a8\u51fa\u4e86\u4ee5\u4e0b IPU \u5bb6\u65cf\u7684\u65b0\u6210\u5458\u3002","data_html":"<p>IPU \u662f\u4e00\u79cd\u53ef\u7f16\u7a0b\u7684\u7f51\u7edc\u8bbe\u5907\uff0c\u65e8\u5728\u4f7f\u4e91\u548c\u901a\u4fe1\u670d\u52a1\u63d0\u4f9b\u5546\u51cf\u5c11\u5728\u4e2d\u592e\u5904\u7406\u5668\uff08CPU\uff09\u65b9\u9762\u7684\u5f00\u9500\u3002\u82f1\u7279\u5c14\u63a8\u51fa\u4e86\u4ee5\u4e0b IPU \u5bb6\u65cf\u7684\u65b0\u6210\u5458\u3002<\/p>"},{"type":"text","content":"Mount Evans \u662f\u82f1\u7279\u5c14\u7684\u9996\u4e2a ASIC IPU\u3002Mount Evans \u662f\u4e0e\u4e00\u5bb6\u4e91\u670d\u52a1\u63d0\u4f9b\u5546\u5171\u540c\u8bbe\u8ba1\u548c\u5f00\u53d1\u7684\u3002","data_html":"<p>Mount Evans \u662f\u82f1\u7279\u5c14\u7684\u9996\u4e2a ASIC IPU\u3002Mount Evans \u662f\u4e0e\u4e00\u5bb6\u4e91\u670d\u52a1\u63d0\u4f9b\u5546\u5171\u540c\u8bbe\u8ba1\u548c\u5f00\u53d1\u7684\u3002<\/p>"},{"type":"text","content":"\u8d85\u5927\u89c4\u6a21\u5c31\u7eea\uff0c\u63d0\u4f9b\u9ad8\u6027\u80fd\u7f51\u7edc\u548c\u5b58\u50a8\u865a\u62df\u5316\u5378\u8f7d\uff0c\u540c\u65f6\u4fdd\u6301\u9ad8\u5ea6\u63a7\u5236\u63d0\u4f9b\u4e1a\u754c\u4e00\u6d41\u7684\u53ef\u7f16\u7a0b\u6570\u636e\u5305\u5904\u7406\u5f15\u64ce\uff0c\u652f\u6301\u9632\u706b\u5899\u548c\u865a\u62df\u8def\u7531\u7b49\u7528\u4f8b\u4f7f\u7528\u786c\u4ef6\u52a0\u901f\u7684 NVMe \u5b58\u50a8\u63a5\u53e3\uff0c\u8be5\u63a5\u53e3\u6269\u5c55\u81ea\u82f1\u7279\u5c14\u50b2\u817e\u6280\u672f\uff0c\u4ee5\u6a21\u62df NVMe \u8bbe\u5907\u91c7\u7528\u82f1\u7279\u5c14\u9ad8\u6027\u80fd Quick Assist \u6280\u672f\uff0c\u90e8\u7f72\u9ad8\u7ea7\u52a0\u5bc6\u548c\u538b\u7f29\u52a0\u901f\u53ef\u4f7f\u7528\u73b0\u6709\u666e\u904d\u90e8\u7f72\u7684 DPDK\u3001SPDK \u7b49\u8f6f\u4ef6\u73af\u5883\u8fdb\u884c\u7f16\u7a0b\uff0c\u5e76\u4e14\u53ef\u4ee5\u91c7\u7528\u82f1\u7279\u5c14 Barefoot Switch \u90e8\u95e8\u5f00\u521b\u7684 P4 \u7f16\u7a0b\u8bed\u8a00\u6765\u914d\u7f6e\u7ba1\u7ebf","data_html":"<ul class=\"ai-word-checked list-paddingleft-2\"><li><p>\u8d85\u5927\u89c4\u6a21\u5c31\u7eea\uff0c\u63d0\u4f9b\u9ad8\u6027\u80fd\u7f51\u7edc\u548c\u5b58\u50a8\u865a\u62df\u5316\u5378\u8f7d\uff0c\u540c\u65f6\u4fdd\u6301\u9ad8\u5ea6\u63a7\u5236<\/p><\/li><li><p>\u63d0\u4f9b\u4e1a\u754c\u4e00\u6d41\u7684\u53ef\u7f16\u7a0b\u6570\u636e\u5305\u5904\u7406\u5f15\u64ce\uff0c\u652f\u6301\u9632\u706b\u5899\u548c\u865a\u62df\u8def\u7531\u7b49\u7528\u4f8b<\/p><\/li><li><p>\u4f7f\u7528\u786c\u4ef6\u52a0\u901f\u7684 NVMe \u5b58\u50a8\u63a5\u53e3\uff0c\u8be5\u63a5\u53e3\u6269\u5c55\u81ea\u82f1\u7279\u5c14\u50b2\u817e\u6280\u672f\uff0c\u4ee5\u6a21\u62df NVMe \u8bbe\u5907<\/p><\/li><li><p>\u91c7\u7528\u82f1\u7279\u5c14\u9ad8\u6027\u80fd Quick Assist \u6280\u672f\uff0c\u90e8\u7f72\u9ad8\u7ea7\u52a0\u5bc6\u548c\u538b\u7f29\u52a0\u901f<\/p><\/li><li><p>\u53ef\u4f7f\u7528\u73b0\u6709\u666e\u904d\u90e8\u7f72\u7684 DPDK\u3001SPDK \u7b49\u8f6f\u4ef6\u73af\u5883\u8fdb\u884c\u7f16\u7a0b\uff0c\u5e76\u4e14\u53ef\u4ee5\u91c7\u7528\u82f1\u7279\u5c14 Barefoot Switch \u90e8\u95e8\u5f00\u521b\u7684 P4 \u7f16\u7a0b\u8bed\u8a00\u6765\u914d\u7f6e\u7ba1\u7ebf<\/p><\/li><\/ul>"},{"type":"text","content":"Oak Springs Canyon \u662f\u4e00\u4e2a IPU \u53c2\u8003\u5e73\u53f0\uff0c\u57fa\u4e8e\u82f1\u7279\u5c14\u81f3\u5f3a D \u5904\u7406\u5668\uff08Intel Xeon-D\uff09\u548c\u82f1\u7279\u5c14 Agilex FPGA \u6784\u5efa\uff1a","data_html":"<p>Oak Springs Canyon \u662f\u4e00\u4e2a IPU \u53c2\u8003\u5e73\u53f0\uff0c\u57fa\u4e8e\u82f1\u7279\u5c14\u81f3\u5f3a D \u5904\u7406\u5668\uff08Intel Xeon-D\uff09\u548c\u82f1\u7279\u5c14 Agilex FPGA \u6784\u5efa\uff1a<\/p>"},{"type":"text","content":"\u5378\u8f7d Open Virtual Switch\uff08OVS\uff09\u7b49\u7f51\u7edc\u865a\u62df\u5316\u529f\u80fd\u4ee5\u53ca NVMe over Fabric \u548c RoCE v2 \u7b49\u5b58\u50a8\u529f\u80fd\uff0c\u5e76\u63d0\u4f9b\u786c\u5316\u7684\u52a0\u5bc6\u6a21\u5757\uff0c\u63d0\u4f9b 2x 100Gb \u4ee5\u592a\u7f51\u7f51\u7edc\u63a5\u53e3\u80fd\u591f\u4f7f\u7528\u82f1\u7279\u5c14\u5f00\u653e\u5f0f FPGA \u5f00\u53d1\u5806\u6808 (\u82f1\u7279\u5c14 OFS) \u5b9a\u5236\u5176\u89e3\u51b3\u65b9\u6848\u4f7f\u7528\u73b0\u6709\u666e\u904d\u90e8\u7f72\u7684\u8f6f\u4ef6\u73af\u5883\u8fdb\u884c\u7f16\u7a0b\uff0c\u5305\u62ec\u5df2\u5728 x86 \u4e0a\u4f18\u5316\u7684 DPDK \u548c SPDK","data_html":"<ul class=\" list-paddingleft-2\"><li><p>\u5378\u8f7d Open Virtual Switch\uff08OVS\uff09\u7b49\u7f51\u7edc\u865a\u62df\u5316\u529f\u80fd\u4ee5\u53ca NVMe over Fabric \u548c RoCE v2 \u7b49\u5b58\u50a8\u529f\u80fd\uff0c\u5e76\u63d0\u4f9b\u786c\u5316\u7684\u52a0\u5bc6\u6a21\u5757\uff0c\u63d0\u4f9b 2x 100Gb \u4ee5\u592a\u7f51\u7f51\u7edc\u63a5\u53e3<\/p><\/li><li><p>\u80fd\u591f\u4f7f\u7528\u82f1\u7279\u5c14\u5f00\u653e\u5f0f FPGA \u5f00\u53d1\u5806\u6808 (\u82f1\u7279\u5c14 OFS) \u5b9a\u5236\u5176\u89e3\u51b3\u65b9\u6848<\/p><\/li><li><p>\u4f7f\u7528\u73b0\u6709\u666e\u904d\u90e8\u7f72\u7684\u8f6f\u4ef6\u73af\u5883\u8fdb\u884c\u7f16\u7a0b\uff0c\u5305\u62ec\u5df2\u5728 x86 \u4e0a\u4f18\u5316\u7684 DPDK \u548c SPDK<\/p><\/li><\/ul>"},{"type":"text","content":"IT\u4e4b\u5bb6\u4e86\u89e3\u5230\uff0c\u82f1\u7279\u5c14 N6000 \u52a0\u901f\u5f00\u53d1\u5e73\u53f0\uff0c\u4ee3\u53f7\u4e3a\u201cArrow Creek\u201d\uff0c\u662f\u4e13\u4e3a\u642d\u8f7d\u81f3\u5f3a\u670d\u52a1\u5668\u8bbe\u8ba1\u7684 SmartNIC\u3002\u5176\u7279\u6027\u5305\u62ec\uff1a","data_html":"<p>IT\u4e4b\u5bb6\u4e86\u89e3\u5230\uff0c\u82f1\u7279\u5c14 N6000 \u52a0\u901f\u5f00\u53d1\u5e73\u53f0\uff0c\u4ee3\u53f7\u4e3a\u201cArrow Creek\u201d\uff0c\u662f\u4e13\u4e3a\u642d\u8f7d\u81f3\u5f3a\u670d\u52a1\u5668\u8bbe\u8ba1\u7684 SmartNIC\u3002\u5176\u7279\u6027\u5305\u62ec\uff1a<\/p>"},{"type":"text","content":"\u82f1\u7279\u5c14 Agilex FPGA\u3002\u7528\u4e8e\u9ad8\u6027\u80fd\u7684 100GB \u7f51\u7edc\u52a0\u901f\u7684\u82f1\u7279\u5c14\u4ee5\u592a\u7f51 800 \u7cfb\u5217\u63a7\u5236\u5668\u652f\u6301\u591a\u79cd\u57fa\u7840\u8bbe\u65bd\u5de5\u4f5c\u8d1f\u8f7d\uff0c\u4f7f\u901a\u4fe1\u670d\u52a1\u63d0\u4f9b\u5546\uff08CoSP\uff09\u80fd\u591f\u63d0\u4f9b\u7075\u6d3b\u7684\u52a0\u901f\u5de5\u4f5c\u8d1f\u8f7d\uff0c\u5982 Juniper Contrail\u3001OVS \u548c SRv6\uff0c\u5b83\u4ee5\u82f1\u7279\u5c14 PAC-N3000 \u7684\u6210\u529f\u4e3a\u57fa\u7840","data_html":"<ul class=\"ai-word-checked list-paddingleft-2\"><li><p>\u82f1\u7279\u5c14 Agilex FPGA\u3002\u7528\u4e8e\u9ad8\u6027\u80fd\u7684 100GB \u7f51\u7edc\u52a0\u901f\u7684\u82f1\u7279\u5c14\u4ee5\u592a\u7f51 800 \u7cfb\u5217\u63a7\u5236\u5668<\/p><\/li><li><p>\u652f\u6301\u591a\u79cd\u57fa\u7840\u8bbe\u65bd\u5de5\u4f5c\u8d1f\u8f7d\uff0c\u4f7f\u901a\u4fe1\u670d\u52a1\u63d0\u4f9b\u5546\uff08CoSP\uff09\u80fd\u591f\u63d0\u4f9b\u7075\u6d3b\u7684\u52a0\u901f\u5de5\u4f5c\u8d1f\u8f7d\uff0c\u5982 Juniper Contrail\u3001OVS \u548c SRv6\uff0c\u5b83\u4ee5\u82f1\u7279\u5c14 PAC-N3000 \u7684\u6210\u529f\u4e3a\u57fa\u7840<\/p><\/li><\/ul>"},{"type":"text","content":"Xe HPC \u548c Ponte Vecchio","data_html":"<h2>Xe HPC \u548c Ponte Vecchio<\/h2>"},{"type":"text","content":"Ponte Vecchio \u57fa\u4e8e Xe HPC \u5fae\u67b6\u6784\u3002\u82f1\u7279\u5c14\u516c\u5e03\u4e86 Xe HPC \u5fae\u67b6\u6784\u7684 IP \u6a21\u5757\u4fe1\u606f\uff1b\u5305\u62ec\u6bcf\u4e2a Xe \u6838\u7684 8 \u4e2a\u77e2\u91cf\u548c\u77e9\u9635\u5f15\u64ce\uff08\u79f0\u4e3a XMX Xe Matrix eXtensions\uff09\uff1b\u5207\u7247\u548c\u5806\u6808\u4fe1\u606f\uff1b\u4ee5\u53ca\u5305\u62ec\u8ba1\u7b97\u3001\u57fa\u7840\u548c Xe Link \u5355\u5143\u7684\u5904\u7406\u8282\u70b9\u7684\u5355\u5143\u4fe1\u606f\u3002\u5728\u67b6\u6784\u65e5\u4e0a\uff0c\u82f1\u7279\u5c14\u8868\u793a\uff0c\u65e9\u671f\u7684 Ponte Vecchio \u82af\u7247\u5c55\u793a\u4e86\u9886\u5148\u7684\u6027\u80fd\uff0c\u5728\u6d41\u884c\u7684 AI \u57fa\u51c6\u6d4b\u8bd5\u4e2d\u521b\u9020\u4e86\u63a8\u7406\u548c\u8bad\u7ec3\u541e\u5410\u91cf\u7684\u884c\u4e1a\u8bb0\u5f55\u3002\u82f1\u7279\u5c14 A0 \u82af\u7247\u6027\u80fd\u63d0\u4f9b\u4e86\u9ad8\u4e8e 45 TFLOPS \u7684 FP32 \u541e\u5410\u91cf\uff0c\u9ad8\u4e8e 5 TBps \u7684\u5185\u5b58\u7ed3\u6784\u5e26\u5bbd\uff0c\u4ee5\u53ca\u9ad8\u4e8e 2 TBps \u7684\u8fde\u63a5\u5e26\u5bbd\u3002\u540c\u65f6\uff0c\u82f1\u7279\u5c14\u5206\u4eab\u4e86\u4e00\u6bb5\u6f14\u793a\u89c6\u9891\uff0c\u5c55\u793a\u4e86 ResNet \u63a8\u7406\u6027\u80fd\u8d85\u8fc7 43,000 \u5f20\u56fe\u50cf\/\u79d2\u548c\u8d85\u8fc7\u6bcf\u79d2 3400 \u5f20\u56fe\u50cf\/\u79d2\u7684 ResNet \u8bad\u7ec3\u3002","data_html":"<p>Ponte Vecchio \u57fa\u4e8e Xe HPC \u5fae\u67b6\u6784\u3002\u82f1\u7279\u5c14\u516c\u5e03\u4e86 Xe HPC \u5fae\u67b6\u6784\u7684 IP \u6a21\u5757\u4fe1\u606f\uff1b\u5305\u62ec\u6bcf\u4e2a Xe \u6838\u7684 8 \u4e2a\u77e2\u91cf\u548c\u77e9\u9635\u5f15\u64ce\uff08\u79f0\u4e3a XMX Xe Matrix eXtensions\uff09\uff1b\u5207\u7247\u548c\u5806\u6808\u4fe1\u606f\uff1b\u4ee5\u53ca\u5305\u62ec\u8ba1\u7b97\u3001\u57fa\u7840\u548c Xe Link \u5355\u5143\u7684\u5904\u7406\u8282\u70b9\u7684\u5355\u5143\u4fe1\u606f\u3002\u5728\u67b6\u6784\u65e5\u4e0a\uff0c\u82f1\u7279\u5c14\u8868\u793a\uff0c\u65e9\u671f\u7684 Ponte Vecchio \u82af\u7247\u5c55\u793a\u4e86\u9886\u5148\u7684\u6027\u80fd\uff0c\u5728\u6d41\u884c\u7684 AI \u57fa\u51c6\u6d4b\u8bd5\u4e2d\u521b\u9020\u4e86\u63a8\u7406\u548c\u8bad\u7ec3\u541e\u5410\u91cf\u7684\u884c\u4e1a\u8bb0\u5f55\u3002\u82f1\u7279\u5c14 A0 \u82af\u7247\u6027\u80fd\u63d0\u4f9b\u4e86\u9ad8\u4e8e 45 TFLOPS \u7684 FP32 \u541e\u5410\u91cf\uff0c\u9ad8\u4e8e 5 TBps \u7684\u5185\u5b58\u7ed3\u6784\u5e26\u5bbd\uff0c\u4ee5\u53ca\u9ad8\u4e8e 2 TBps \u7684\u8fde\u63a5\u5e26\u5bbd\u3002\u540c\u65f6\uff0c\u82f1\u7279\u5c14\u5206\u4eab\u4e86\u4e00\u6bb5\u6f14\u793a\u89c6\u9891\uff0c\u5c55\u793a\u4e86 ResNet \u63a8\u7406\u6027\u80fd\u8d85\u8fc7 43,000 \u5f20\u56fe\u50cf\/\u79d2\u548c\u8d85\u8fc7\u6bcf\u79d2 3400 \u5f20\u56fe\u50cf\/\u79d2\u7684 ResNet \u8bad\u7ec3\u3002<\/p>"},{"type":"text","content":"Ponte Vecchio \u7531\u591a\u4e2a\u590d\u6742\u7684\u8bbe\u8ba1\u7ec4\u6210\uff0c\u8fd9\u4e9b\u8bbe\u8ba1\u4ee5\u5355\u5143\u5f62\u5f0f\u5448\u73b0\uff0c\u7136\u540e\u901a\u8fc7\u5d4c\u5165\u5f0f\u591a\u82af\u7247\u4e92\u8fde\u6865\u63a5\uff08EMIB\uff09\u5355\u5143\u8fdb\u884c\u7ec4\u88c5\uff0c\u5b9e\u73b0\u5355\u5143\u4e4b\u95f4\u7684\u4f4e\u529f\u8017\u3001\u9ad8\u901f\u8fde\u63a5\u3002\u8fd9\u4e9b\u8bbe\u8ba1\u5747\u88ab\u96c6\u6210\u4e8e Foveros \u5c01\u88c5\u4e2d\uff0c\u4e3a\u63d0\u9ad8\u529f\u7387\u548c\u4e92\u8fde\u5bc6\u5ea6\u5f62\u6210\u6709\u6e90\u82af\u7247\u7684 3D \u5806\u53e0\u3002\u9ad8\u901f MDFI \u4e92\u8fde\u5141\u8bb8 1 \u5230 2 \u4e2a\u5806\u6808\u7684\u6269\u5c55\u3002","data_html":"<p>Ponte Vecchio \u7531\u591a\u4e2a\u590d\u6742\u7684\u8bbe\u8ba1\u7ec4\u6210\uff0c\u8fd9\u4e9b\u8bbe\u8ba1\u4ee5\u5355\u5143\u5f62\u5f0f\u5448\u73b0\uff0c\u7136\u540e\u901a\u8fc7\u5d4c\u5165\u5f0f\u591a\u82af\u7247\u4e92\u8fde\u6865\u63a5\uff08EMIB\uff09\u5355\u5143\u8fdb\u884c\u7ec4\u88c5\uff0c\u5b9e\u73b0\u5355\u5143\u4e4b\u95f4\u7684\u4f4e\u529f\u8017\u3001\u9ad8\u901f\u8fde\u63a5\u3002\u8fd9\u4e9b\u8bbe\u8ba1\u5747\u88ab\u96c6\u6210\u4e8e Foveros \u5c01\u88c5\u4e2d\uff0c\u4e3a\u63d0\u9ad8\u529f\u7387\u548c\u4e92\u8fde\u5bc6\u5ea6\u5f62\u6210\u6709\u6e90\u82af\u7247\u7684 3D \u5806\u53e0\u3002\u9ad8\u901f MDFI \u4e92\u8fde\u5141\u8bb8 1 \u5230 2 \u4e2a\u5806\u6808\u7684\u6269\u5c55\u3002<\/p>"},{"type":"text","content":"\u8ba1\u7b97\u5355\u5143\u662f\u4e00\u4e2a\u5bc6\u96c6\u7684\u591a\u4e2a Xe \u5185\u6838\uff0c\u662f Ponte Vecchio \u7684\u6838\u5fc3\u3002","data_html":"<p>\u8ba1\u7b97\u5355\u5143\u662f\u4e00\u4e2a\u5bc6\u96c6\u7684\u591a\u4e2a Xe \u5185\u6838\uff0c\u662f Ponte Vecchio \u7684\u6838\u5fc3\u3002<\/p>"},{"type":"text","content":"\u4e00\u5757\u5355\u5143\u6709 8 \u4e2a Xe \u5185\u6838\uff0c\u603b\u5171\u6709 4MB \u4e00\u7ea7\u7f13\u5b58\uff0c\u662f\u63d0\u4f9b\u9ad8\u6548\u8ba1\u7b97\u7684\u5173\u952e\u57fa\u4e8e\u53f0\u79ef\u7535\u5148\u8fdb\u7684 N5 \u5236\u7a0b\u5de5\u827a\u6280\u672f\u82f1\u7279\u5c14\u5df2\u901a\u8fc7\u8bbe\u8ba1\u57fa\u7840\u8bbe\u65bd\u8bbe\u7f6e\u548c\u5de5\u5177\u6d41\u7a0b\u4ee5\u53ca\u65b9\u6cd5\uff0c\u4e3a\u6d4b\u8bd5\u548c\u9a8c\u8bc1\u8be5\u8282\u70b9\u7684\u5355\u5143\u94fa\u5e73\u4e86\u9053\u8def\u8be5\u5355\u5143\u5177\u6709\u6781\u5176\u7d27\u51d1\u7684 36 \u5fae\u7c73\u51f8\u70b9\u95f4\u8ddd\uff0c\u53ef\u4e0e Foveros \u8fdb\u884c 3D \u5806\u53e0","data_html":"<ul class=\" list-paddingleft-2\"><li><p>\u4e00\u5757\u5355\u5143\u6709 8 \u4e2a Xe \u5185\u6838\uff0c\u603b\u5171\u6709 4MB \u4e00\u7ea7\u7f13\u5b58\uff0c\u662f\u63d0\u4f9b\u9ad8\u6548\u8ba1\u7b97\u7684\u5173\u952e<\/p><\/li><li><p>\u57fa\u4e8e\u53f0\u79ef\u7535\u5148\u8fdb\u7684 N5 \u5236\u7a0b\u5de5\u827a\u6280\u672f<\/p><\/li><li><p>\u82f1\u7279\u5c14\u5df2\u901a\u8fc7\u8bbe\u8ba1\u57fa\u7840\u8bbe\u65bd\u8bbe\u7f6e\u548c\u5de5\u5177\u6d41\u7a0b\u4ee5\u53ca\u65b9\u6cd5\uff0c\u4e3a\u6d4b\u8bd5\u548c\u9a8c\u8bc1\u8be5\u8282\u70b9\u7684\u5355\u5143\u94fa\u5e73\u4e86\u9053\u8def<\/p><\/li><li><p>\u8be5\u5355\u5143\u5177\u6709\u6781\u5176\u7d27\u51d1\u7684 36 \u5fae\u7c73\u51f8\u70b9\u95f4\u8ddd\uff0c\u53ef\u4e0e Foveros \u8fdb\u884c 3D \u5806\u53e0<\/p><\/li><\/ul>"},{"type":"text","content":"\u57fa\u7840\u5355\u5143\u662f Ponte Vecchio \u7684\u8fde\u63a5\u7ec4\u7ec7\u3002\u5b83\u662f\u57fa\u4e8e Intel 7 \u5236\u7a0b\u5de5\u827a\u7684\u5927\u578b\u82af\u7247\uff0c\u9488\u5bf9 Foveros \u6280\u672f\u8fdb\u884c\u4e86\u4f18\u5316\u3002","data_html":"<p>\u57fa\u7840\u5355\u5143\u662f Ponte Vecchio \u7684\u8fde\u63a5\u7ec4\u7ec7\u3002\u5b83\u662f\u57fa\u4e8e Intel 7 \u5236\u7a0b\u5de5\u827a\u7684\u5927\u578b\u82af\u7247\uff0c\u9488\u5bf9 Foveros \u6280\u672f\u8fdb\u884c\u4e86\u4f18\u5316\u3002<\/p>"},{"type":"text","content":"\u57fa\u7840\u5355\u5143\u662f\u6240\u6709\u590d\u6742\u7684 I\/O \u548c\u9ad8\u5e26\u5bbd\u7ec4\u4ef6\u4e0e SoC \u57fa\u7840\u8bbe\u65bd \u2014\u2014PCIe Gen5\u3001HBM2e \u5185\u5b58\u3001\u8fde\u63a5\u4e0d\u540c\u5355\u5143 MDFI \u94fe\u8def\u548c EMIB \u6865\u63a5\u91c7\u7528\u9ad8 2D \u4e92\u8fde\u7684\u8d85\u9ad8\u5e26\u5bbd 3D \u8fde\u63a5\u65f6\u5ef6\u5f88\u4f4e\uff0c\u4f7f\u5176\u6210\u4e3a\u4e00\u53f0\u65e0\u9650\u8fde\u63a5\u7684\u673a\u5668\u82f1\u7279\u5c14\u6280\u672f\u5f00\u53d1\u56e2\u961f\u81f4\u529b\u4e8e\u6ee1\u8db3\u5e26\u5bbd\u3001\u51f8\u70b9\u95f4\u8ddd\u548c\u4fe1\u53f7\u5b8c\u6574\u6027\u65b9\u9762\u7684\u8981\u6c42Xe \u94fe\u8def\u5355\u5143\u63d0\u4f9b\u4e86 GPU \u4e4b\u95f4\u7684\u8fde\u63a5\uff0c\u652f\u6301\u6bcf\u5355\u5143 8 \u4e2a\u94fe\u8def\u3002\u5bf9 HPC \u548c AI \u8ba1\u7b97\u7684\u6269\u5c55\u81f3\u5173\u91cd\u8981\u65e8\u5728\u5b9e\u73b0\u652f\u6301\u9ad8\u8fbe 90G \u7684\u66f4\u9ad8\u901f SerDes\u8be5\u5355\u5143\u5df2\u88ab\u6dfb\u52a0\u5230\u201c\u6781\u5149\u201d\uff08Aurora\uff09\u767e\u4ebf\u4ebf\u6b21\u7ea7\u8d85\u7ea7\u8ba1\u7b97\u673a\u7684\u6269\u5c55\u89e3\u51b3\u65b9\u6848\u4e2d","data_html":"<ul class=\"ai-word-checked list-paddingleft-2\"><li><p>\u57fa\u7840\u5355\u5143\u662f\u6240\u6709\u590d\u6742\u7684 I\/O \u548c\u9ad8\u5e26\u5bbd\u7ec4\u4ef6\u4e0e SoC \u57fa\u7840\u8bbe\u65bd \u2014\u2014PCIe Gen5\u3001HBM2e \u5185\u5b58\u3001\u8fde\u63a5\u4e0d\u540c\u5355\u5143 MDFI \u94fe\u8def\u548c EMIB \u6865\u63a5<\/p><\/li><li><p>\u91c7\u7528\u9ad8 2D \u4e92\u8fde\u7684\u8d85\u9ad8\u5e26\u5bbd 3D \u8fde\u63a5\u65f6\u5ef6\u5f88\u4f4e\uff0c\u4f7f\u5176\u6210\u4e3a\u4e00\u53f0\u65e0\u9650\u8fde\u63a5\u7684\u673a\u5668<\/p><\/li><li><p>\u82f1\u7279\u5c14\u6280\u672f\u5f00\u53d1\u56e2\u961f\u81f4\u529b\u4e8e\u6ee1\u8db3\u5e26\u5bbd\u3001\u51f8\u70b9\u95f4\u8ddd\u548c\u4fe1\u53f7\u5b8c\u6574\u6027\u65b9\u9762\u7684\u8981\u6c42<\/p><\/li><li><p>Xe \u94fe\u8def\u5355\u5143\u63d0\u4f9b\u4e86 GPU \u4e4b\u95f4\u7684\u8fde\u63a5\uff0c\u652f\u6301\u6bcf\u5355\u5143 8 \u4e2a\u94fe\u8def\u3002<\/p><\/li><li><p>\u5bf9 HPC \u548c AI \u8ba1\u7b97\u7684\u6269\u5c55\u81f3\u5173\u91cd\u8981<\/p><\/li><li><p>\u65e8\u5728\u5b9e\u73b0\u652f\u6301\u9ad8\u8fbe 90G \u7684\u66f4\u9ad8\u901f SerDes<\/p><\/li><li><p>\u8be5\u5355\u5143\u5df2\u88ab\u6dfb\u52a0\u5230\u201c\u6781\u5149\u201d\uff08Aurora\uff09\u767e\u4ebf\u4ebf\u6b21\u7ea7\u8d85\u7ea7\u8ba1\u7b97\u673a\u7684\u6269\u5c55\u89e3\u51b3\u65b9\u6848\u4e2d<\/p><\/li><\/ul>"},{"type":"text","content":"Ponte Vecchio \u5df2\u8d70\u4e0b\u751f\u4ea7\u7ebf\u8fdb\u884c\u4e0a\u7535\u9a8c\u8bc1\uff0c\u5e76\u5df2\u5f00\u59cb\u5411\u5ba2\u6237\u63d0\u4f9b\u9650\u91cf\u6837\u54c1\u3002Ponte Vecchio \u9884\u8ba1\u5c06\u4e8e 2022 \u5e74\u9762\u5411 HPC \u548c AI \u5e02\u573a\u53d1\u5e03\u3002","data_html":"<p>Ponte Vecchio \u5df2\u8d70\u4e0b\u751f\u4ea7\u7ebf\u8fdb\u884c\u4e0a\u7535\u9a8c\u8bc1\uff0c\u5e76\u5df2\u5f00\u59cb\u5411\u5ba2\u6237\u63d0\u4f9b\u9650\u91cf\u6837\u54c1\u3002Ponte Vecchio \u9884\u8ba1\u5c06\u4e8e 2022 \u5e74\u9762\u5411 HPC \u548c AI \u5e02\u573a\u53d1\u5e03\u3002<\/p>"},{"type":"text","content":"oneAPI","data_html":"<p><strong>oneAPI<\/strong><\/p>"},{"type":"text","content":"\u76ee\u524d\uff0cNVIDIA GPU\u3001AMD GPU \u548c Arm CPU \u5747\u6709 Data Parallel C++\uff08DPC++\uff09\u548c oneAPI \u5e93\u3002\u540c\u65f6\uff0c\u82f1\u7279\u5c14\u8fd8\u63d0\u4f9b\u4e86\u5546\u4e1a\u4ea7\u54c1\uff0c\u5305\u62ec\u57fa\u672c\u7684 oneAPI \u57fa\u7840\u5de5\u5177\u5305\uff0c\u5b83\u5728\u89c4\u8303\u8bed\u8a00\u548c\u5e93\u4e4b\u5916\u589e\u52a0\u4e86\u7f16\u8bd1\u5668\u3001\u5206\u6790\u5668\u3001\u8c03\u8bd5\u5668\u548c\u79fb\u690d\u5de5\u5177\u3002","data_html":"<p>\u76ee\u524d\uff0cNVIDIA GPU\u3001AMD GPU \u548c Arm CPU \u5747\u6709 Data Parallel C++\uff08DPC++\uff09\u548c oneAPI \u5e93\u3002\u540c\u65f6\uff0c\u82f1\u7279\u5c14\u8fd8\u63d0\u4f9b\u4e86\u5546\u4e1a\u4ea7\u54c1\uff0c\u5305\u62ec\u57fa\u672c\u7684 oneAPI \u57fa\u7840\u5de5\u5177\u5305\uff0c\u5b83\u5728\u89c4\u8303\u8bed\u8a00\u548c\u5e93\u4e4b\u5916\u589e\u52a0\u4e86\u7f16\u8bd1\u5668\u3001\u5206\u6790\u5668\u3001\u8c03\u8bd5\u5668\u548c\u79fb\u690d\u5de5\u5177\u3002<\/p>"},{"type":"text","content":"\u82f1\u7279\u5c14\u7684 oneAPI \u5de5\u5177\u5305\u62e5\u6709\u8d85\u8fc7 20 \u4e07\u6b21\u5355\u72ec\u5b89\u88c5\u5e02\u573a\u4e0a\u90e8\u7f72\u7684 300 \u591a\u4e2a\u5e94\u7528\u7a0b\u5e8f\u91c7\u7528\u4e86 oneAPI \u7684\u7edf\u4e00\u7f16\u7a0b\u6a21\u578b\u8d85\u8fc7 80 \u4e2a HPC \u548c AI \u5e94\u7528\u7a0b\u5e8f\u4f7f\u7528\u82f1\u7279\u5c14 oneAPI \u5de5\u5177\u5305\u5728 Xe HPC \u5fae\u67b6\u6784\u4e0a\u8fd0\u884c5 \u6708\u4efd\u53d1\u5e03\u7684 1.1 \u7248\u4e34\u65f6\u89c4\u8303\u4e3a\u6df1\u5ea6\u5b66\u4e60\u5de5\u4f5c\u8d1f\u8f7d\u548c\u9ad8\u7ea7\u5149\u7ebf\u8ffd\u8e2a\u5e93\u6dfb\u52a0\u4e86\u65b0\u7684\u56fe\u5f62\u63a5\u53e3\uff0c\u9884\u8ba1\u5c06\u5728\u5e74\u5e95\u5b8c\u6210","data_html":"<ul class=\" list-paddingleft-2\"><li><p>\u82f1\u7279\u5c14\u7684 oneAPI \u5de5\u5177\u5305\u62e5\u6709\u8d85\u8fc7 20 \u4e07\u6b21\u5355\u72ec\u5b89\u88c5<\/p><\/li><li><p>\u5e02\u573a\u4e0a\u90e8\u7f72\u7684 300 \u591a\u4e2a\u5e94\u7528\u7a0b\u5e8f\u91c7\u7528\u4e86 oneAPI \u7684\u7edf\u4e00\u7f16\u7a0b\u6a21\u578b<\/p><\/li><li><p>\u8d85\u8fc7 80 \u4e2a HPC \u548c AI \u5e94\u7528\u7a0b\u5e8f\u4f7f\u7528\u82f1\u7279\u5c14 oneAPI \u5de5\u5177\u5305\u5728 Xe HPC \u5fae\u67b6\u6784\u4e0a\u8fd0\u884c<\/p><\/li><li><p>5 \u6708\u4efd\u53d1\u5e03\u7684 1.1 \u7248\u4e34\u65f6\u89c4\u8303\u4e3a\u6df1\u5ea6\u5b66\u4e60\u5de5\u4f5c\u8d1f\u8f7d\u548c\u9ad8\u7ea7\u5149\u7ebf\u8ffd\u8e2a\u5e93\u6dfb\u52a0\u4e86\u65b0\u7684\u56fe\u5f62\u63a5\u53e3\uff0c\u9884\u8ba1\u5c06\u5728\u5e74\u5e95\u5b8c\u6210<\/p><\/li><\/ul>"}],"isCenterRight":0,"readsrc":{"link":"http:\/\/baijiahao.baidu.com\/s?id=1708538774834253208","forbidden":0},"notice":{"isBaiJiaHao":1,"id":"9493081728030668344"}}},{"itemType":"author","itemData":{"date":"21-08-20","time":"00:12","thumbnail":{"link":"https:\/\/avatar.bdstatic.com\/it\/u=1783744478,1293822274&fm=3012&app=3012&autime=1750320147&size=b200,200"},"name":"IT\u4e4b\u5bb6","link":"1551599273641720","third_id":"1551599273641720","type":"media","uk":"k4W0SjisIam7fO9RinNyPw","author_link":"https:\/\/author.baidu.com\/home?from=bjh_article&app_id=1551599273641720","vType":"2","authorTag":"\u9752\u5c9b\u8f6f\u5a92\u7f51\u7edc\u79d1\u6280\u6709\u9650\u516c\u53f8","iptopoi":[],"sign":"\u7231\u79d1\u6280\uff0c\u7231\u8fd9\u91cc - \u524d\u6cbf\u79d1\u6280\u4eba\u6c14\u5e73\u53f0","zanNum":"8898739","fansNum":"573215"}}],"needAsync":1,"needQuery":1,"newStyle":1,"sid":"60277_61027_62325_62338_62869_62878_62882_62886_62926_62968_62956_63040_63044_63032","nid":"9493081728030668344","sourceFrom":"search","title":"\u82f1\u7279\u5c14\u516c\u5e03\u91cd\u5927\u6280\u672f\u67b6\u6784\u6539\u53d8\uff0c\u9762\u5411 CPU\u3001GPU \u548c IPU","datetime":"2021-08-20 00:12:32","isBaiJiaHao":1,"profitLog":{"contentId":"9493081728030668344","contentUrl":"http:\/\/baijiahao.baidu.com\/s?id=1708538774834253208","contentPlatformId":3,"contentType":1,"pvid":"0eca2011dc2f106b","time":"2025-06-24 18:00:15","contentAccType":1,"ctk":"b1434fa38d46e811","ctk_b":"5840f7cff57d0d1b","logId":"1840675146","dtime":1750759215,"grade":2,"create_time_acc_level":3,"contentAccId":"k4W0SjisIam7fO9RinNyPw"},"cmd":"baiduboxapp:\/\/v1\/easybrowse\/hybrid?upgrade=1&type=hybrid&tpl_id=landing_app.html&context=%7B%22nid%22%3A%22news_9493081728030668344%22%7D&style=%7B%22menumode%22%3A%222%22%2C%22toolbaricons%22%3A%7B%22tids%22%3A%5B%224%22%2C%221%22%2C%222%22%2C%223%22%5D%2C%22toolids%22%3A%5B%221%22%2C%222%22%2C%223%22%5D%7D%2C%22actionBarConfig%22%3A%7B%22extCase%22%3A%220%22%7D%7D&newbrowser=1&commentInfo=%7B%22topic_id%22%3A%221118000043231580%22%2C%22opentype%22%3A2%7D&slog=%7B%22from%22%3A%22feed%22%2C%22source%22%3A%22landingsuper%22%2C%22nid%22%3A%22news_9493081728030668344%22%2C%22page%22%3A%22pic_text%22%7D&ch_url=https%3A%2F%2Fmbd.baidu.com%2Fnewspage%2Fdata%2Flandingreact%3FpageType%3D2%26nid%3Dnews_9493081728030668344%26sourceFrom%3Dlandingsuper","displaytime":"1629389552000","favourite":{"is_favourite":"0"},"like":{"is_like":"0","count":"0"},"commentNum":"0","hidedislike":0,"aiLabel":"","timestamp":"1750759215000"},"bsCommon":{"domain":{"bs":"https:\/\/www.baidu.com","portrait":"http:\/\/himg.bdimg.com\/sys\/portraitn\/item\/","appimg":"http:\/\/apps.bdimg.com\/store\/static\/kvt\/","app":"http:\/\/app.baidu.com\/","appinfo":"http:\/\/app.baidu.com\/","apptoken":"d8778fea346d367b4e4ed62cf059908c","appkey":"5r7SmiUici27lVfVBep1K7BA","static":"https:\/\/ss0.bdstatic.com\/5aV1bjqh_Q23odCf\/","passport":"http:\/\/passport.baidu.com\/","sp":"http:\/\/hi.baidu.com\/","passconf":"http:\/\/passport.baidu.com\/ubrwsbas","logout":"https:\/\/passport.baidu.com\/?logout&u=","login":"http:\/\/passport.baidu.com\/?login&tpl=super&u=","top":"http:\/\/top.baidu.com\/buzz.php?p=top10","protocol":"https:"},"ssllist":{"a.hiphotos.baidu.com":"ss0.baidu.com\/94o3dSag_xI4khGko9WTAnF6hhy","b.hiphotos.baidu.com":"ss1.baidu.com\/9vo3dSag_xI4khGko9WTAnF6hhy","c.hiphotos.baidu.com":"ss3.baidu.com\/9fo3dSag_xI4khGko9WTAnF6hhy","d.hiphotos.baidu.com":"ss0.baidu.com\/-Po3dSag_xI4khGko9WTAnF6hhy","e.hiphotos.baidu.com":"ss1.baidu.com\/-4o3dSag_xI4khGko9WTAnF6hhy","f.hiphotos.baidu.com":"ss2.baidu.com\/-vo3dSag_xI4khGko9WTAnF6hhy","g.hiphotos.baidu.com":"ss3.baidu.com\/-fo3dSag_xI4khGko9WTAnF6hhy","h.hiphotos.baidu.com":"ss0.baidu.com\/7Po3dSag_xI4khGko9WTAnF6hhy","1.su.bdimg.com":"ss0.bdstatic.com\/k4oZeXSm1A5BphGlnYG","t10.baidu.com":"ss0.baidu.com\/6ONWsjip0QIZ8tyhnq","t11.baidu.com":"ss1.baidu.com\/6ONXsjip0QIZ8tyhnq","t12.baidu.com":"ss2.baidu.com\/6ONYsjip0QIZ8tyhnq","himg.bdimg.com":"ss1.bdstatic.com\/7Ls0a8Sm1A5BphGlnYG","cdn00.baidu-img.cn":"ss0.bdstatic.com\/9bA1vGba2gU2pMbfm9GUKT-w","cdn01.baidu-img.cn":"ss0.bdstatic.com\/9bA1vGfa2gU2pMbfm9GUKT-w"}},"bsBrowser":{"ie9":0,"isie":0,"ff":1,"ie6":0,"ie":0}};window.firstScreenTime = Date.now();
window['__abbaidu_2036_subidgetf'] = function () {var subid = 'feed_landing_super';return subid;};window['__abbaidu_2036_cb'] = function (responseData) {};
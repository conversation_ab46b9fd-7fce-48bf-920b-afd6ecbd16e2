﻿# 《2024年人工智能十大前沿技术趋势展望》发布 《2024年人工智能十大前沿技术趋势展望》发布

**发布日期**: 2024年11月12日

**原文链接**: http://www.xinhuanet.com/science/20241112/9bf168b8ad6f4370bf5953750a1f3b4d/c.html

## 📄 原文内容

《2024年人工智能十大前沿技术趋势展望》发布-新华网
            margin-bottom: 17px;
            padding-bottom: 12px;
            height: 80px;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            border-bottom: 1px dotted #999
        #sdgc .list-item .img {
            width: 120px;
            height: 67px;
            float: left;
            margin-right: 10px;
            overflow: hidden
        #sdgc .list-item .img img {
            width: 100%;
            height: 100%
        #sdgc .list-item .tit a {
            display: block;
            font-size: 15px;
            line-height: 22px;
            height: 44px;
            margin-bottom: 2px;
            margin-top: -5px;
            color: #333
        #sdgc .list-item .tit span {
            display: inline-block;
            padding: 0 5px;
            font-size: 13px;
            line-height: 17px;
            background: #e30000;
            color: #fff
        #sdgc .list-item .tit span a {
            height: auto;
            font-size: 13px;
            line-height: 17px;
            display: inline-block;
            background: #e30000;
            color: #fff
《2024年人工智能十大前沿技术趋势展望》发布
《2024年人工智能十大前沿技术趋势展望》发布
2024年世界科技与发展论坛期间，作为重要发布成果之一，《2024年人工智能十大前沿技术趋势展望》正式发布。该成果由世界机器人合作组织推动发布，旨在构建开放合作、可持续发展的全球人工智能与机器人生态体系。
发布的十大前沿技术趋势分为AI共性技术、大规模预训练模型、具身智能和生成式人工智能四个类别，共包括小数据与优质数据、人机对齐、AI使用边界和伦理监督模型、可解释性模型、规模定律、全模态大模型、人工智能驱动的科学研究、具身小脑模型、实体人工智能系统、世界模拟器共10项前沿技术。
整体而言，《人工智能十大前沿技术趋势展望》展现了人工智能在各个方面的广泛应用和技术革新，为未来技术的发展提供了有价值的洞察。（战钊）
从首届“中青城挑战赛”看中国网球根基与未来
我在现场丨二十多年，三峡新闻摄影的汗水与收获
从大象拔河到高脚竞速 这所中学体育课有“宝藏项目”
《2024年人工智能十大前沿技术趋势展望》发布
    var wxfxPic = $.trim($("#wxpic").find("img").attr("src"));
    var wxfxTit = $.trim($("#wxtitle").html()).replace("&nbsp;", "").replace("&amp;", "&");
    var detaiWxPic = $("#wxsharepic").attr("src");
    if (wxfxPic == "") {
        wxfxPic = 'https://lib.news.cn/common/sharelogo.jpg';
        wxfxPic = window.location.href.replace("c.html", wxfxPic)
    console.log("wxfxTit", wxfxTit);
        title: wxfxTit,
        desc: '新华网，让新闻离你更近！',
        link: window.location.href,
        imgUrl: wxfxPic
// cbDetailConfig = {
    //     detailAtlasDisplayPc: false,
    //     detailAtlasDisplayMob: false,
    //     detailSetFontSizePc: true,
    //     detailSetFontSizeMob: true,
    //     detailSetFontColorPc: true,
    //     detailSetFontColorMob: true,
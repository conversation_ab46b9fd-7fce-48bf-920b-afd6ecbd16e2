#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试爬虫 - 用于分析网页结构
作者: MiniMax Agent
"""

import requests
import time
import random
from bs4 import BeautifulSoup
import os

def debug_website():
    """调试目标网站结构"""
    url = "https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion"
    
    # 设置会话
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    })
    
    try:
        print(f"正在访问: {url}")
        response = session.get(url, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        # 保存原始HTML
        html_path = '/workspace/debug/raw_html.html'
        os.makedirs(os.path.dirname(html_path), exist_ok=True)
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        print(f"原始HTML已保存到: {html_path}")
        print(f"HTML长度: {len(response.text)} 字符")
        
        # 解析HTML结构
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 分析页面结构
        print("\n=== 页面结构分析 ===")
        
        # 查找可能的表格
        tables = soup.find_all('table')
        print(f"找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables, 1):
            print(f"\n表格 {i}:")
            rows = table.find_all('tr')
            print(f"  行数: {len(rows)}")
            
            if rows:
                # 分析表头
                headers = rows[0].find_all(['th', 'td'])
                if headers:
                    header_texts = [h.get_text().strip() for h in headers]
                    print(f"  表头: {header_texts}")
                
                # 分析数据行
                if len(rows) > 1:
                    data_row = rows[1].find_all('td')
                    if data_row:
                        data_texts = [d.get_text().strip()[:50] for d in data_row]
                        print(f"  第一行数据: {data_texts}")
        
        # 查找可能的列表
        lists = soup.find_all(['ul', 'ol'])
        print(f"\n找到 {len(lists)} 个列表")
        
        # 查找可能的div容器
        divs = soup.find_all('div', {'class': True})
        print(f"\n找到 {len(divs)} 个带class的div")
        
        # 分析常见的class名称
        class_names = set()
        for div in divs:
            classes = div.get('class', [])
            for cls in classes:
                if any(keyword in cls.lower() for keyword in ['table', 'list', 'item', 'row', 'data', 'content']):
                    class_names.add(cls)
        
        print(f"可能相关的class名称: {list(class_names)[:10]}")
        
        # 查找包含"案号"、"标案"等关键词的元素
        keywords = ['案号', '标案', '采购', '公告', '机关', '投标']
        for keyword in keywords:
            elements = soup.find_all(text=lambda text: text and keyword in text)
            if elements:
                print(f"\n包含'{keyword}'的元素数量: {len(elements)}")
                if len(elements) <= 5:
                    for elem in elements[:3]:
                        parent = elem.parent
                        print(f"  元素: {elem.strip()[:100]}")
                        print(f"  父元素: {parent.name} {parent.get('class', '')}")
        
        # 查找所有链接
        links = soup.find_all('a', href=True)
        print(f"\n找到 {len(links)} 个链接")
        
        relevant_links = []
        for link in links:
            href = link.get('href', '')
            text = link.get_text().strip()
            if any(keyword in text or keyword in href for keyword in ['案号', '标案', '采购', '详细', 'detail']):
                relevant_links.append((text[:50], href))
        
        print(f"相关链接: {relevant_links[:5]}")
        
        # 生成结构报告
        report = f"""
=== 网站结构分析报告 ===
URL: {url}
状态码: {response.status_code}
HTML长度: {len(response.text)} 字符

表格结构:
- 表格数量: {len(tables)}
- 主要表格行数: {len(tables[0].find_all('tr')) if tables else 0}

数据特征:
- 链接总数: {len(links)}
- 相关链接数: {len(relevant_links)}
- 可能的数据容器: {len(divs)} 个div元素

建议解析策略:
1. 主要使用表格解析: {'是' if tables else '否'}
2. 需要JavaScript渲染: {'可能' if len(response.text) < 10000 else '不太可能'}
3. 需要特殊参数: {'可能' if '登录' in response.text or 'login' in response.text else '不太可能'}
========================
        """
        
        print(report)
        
        # 保存报告
        report_path = '/workspace/debug/structure_report.txt'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"结构报告已保存到: {report_path}")
        
        return response.text, soup
        
    except Exception as e:
        print(f"调试出错: {e}")
        return None, None

if __name__ == "__main__":
    html_content, soup = debug_website()
    
    if html_content:
        print("\n调试完成！请查看生成的文件以了解网站结构。")
    else:
        print("调试失败，请检查网络连接或网站是否可访问。")

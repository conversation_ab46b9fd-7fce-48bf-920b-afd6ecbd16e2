# EdgeFN API 使用示例

这是一个使用 EdgeFN API 调用 DeepSeek-R1-0528 模型的 Python 示例程序。

## 功能特性

- ✅ 安全的 API Key 管理（支持环境变量）
- ✅ 完整的错误处理
- ✅ 单次对话和交互式聊天两种模式
- ✅ 使用统计显示
- ✅ 可配置的参数（温度、最大tokens等）

## 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装：

```bash
pip install requests
```

## 配置 API Key

### 方法1：环境变量（推荐）

**Windows:**
```cmd
set EDGEFN_API_KEY=sk-HH2NyMUwCGoE211d3b59E69970Fc4410B724996bA4409015
```

**Linux/Mac:**
```bash
export EDGEFN_API_KEY=sk-HH2NyMUwCGoE211d3b59E69970Fc4410B724996bA4409015
```

**PowerShell:**
```powershell
$env:EDGEFN_API_KEY="sk-HH2NyMUwCGoE211d3b59E69970Fc4410B724996bA4409015"
```

### 方法2：直接在代码中设置（不推荐）

代码中已经包含了直接设置 API Key 的示例，但这种方式不安全。

## 使用方法

### 运行示例程序

```bash
python edgefn_api_example.py
```

程序会提示您选择模式：
- **模式1**: 单次对话示例
- **模式2**: 交互式聊天

### 在自己的代码中使用

```python
from edgefn_api_example import EdgeFNClient

# 创建客户端
client = EdgeFNClient()

# 发送消息
messages = [{"role": "user", "content": "你好"}]
response = client.chat_completion(messages)

# 获取回复
reply = response['choices'][0]['message']['content']
print(reply)
```

## API 参数说明

### chat_completion 方法参数

- `messages`: 消息列表，必需
  - 格式: `[{"role": "user", "content": "消息内容"}]`
  - 支持的角色: `user`, `assistant`, `system`

- `model`: 模型名称，默认 `"DeepSeek-R1-0528"`

- `temperature`: 温度参数，控制回复随机性
  - 范围: 0.0 - 1.0
  - 默认: 0.7
  - 较低值 = 更确定性的回复
  - 较高值 = 更有创意的回复

- `max_tokens`: 最大输出token数量，可选

- `stream`: 是否使用流式响应，默认 `False`

## 错误处理

程序包含完整的错误处理：

- ✅ 网络请求错误
- ✅ API 响应错误
- ✅ JSON 解析错误
- ✅ API Key 缺失错误

## 安全提醒

⚠️ **重要**: 您的 API Key 已经在示例中暴露，建议：

1. **立即更换 API Key**（如果平台支持）
2. **始终使用环境变量**存储敏感信息
3. **不要将 API Key 提交到版本控制系统**

## 示例输出

```
EdgeFN API 示例程序
1. 单次对话示例
2. 交互式聊天

请选择模式 (1/2): 1
发送请求到 EdgeFN API...
消息: 你好，请介绍一下你自己
--------------------------------------------------
AI 回复:
你好！我是DeepSeek，一个由深度求索开发的人工智能助手...

使用统计:
  输入tokens: 15
  输出tokens: 120
  总tokens: 135
```

## 故障排除

### 常见问题

1. **API Key 错误**
   - 检查 API Key 是否正确设置
   - 确认 API Key 有效且未过期

2. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

3. **模型不可用**
   - 确认模型名称正确
   - 检查账户是否有权限使用该模型

### 调试模式

如果遇到问题，可以查看完整的响应内容来调试：

```python
import json
print("完整响应:", json.dumps(response, indent=2, ensure_ascii=False))
```

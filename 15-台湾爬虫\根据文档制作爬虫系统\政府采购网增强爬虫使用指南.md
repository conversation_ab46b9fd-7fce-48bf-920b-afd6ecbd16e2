# 政府采购网增强爬虫使用指南

## 概述

本爬虫系统专门用于抓取台湾政府电子采购网的完整采购信息，能够提取图片中显示的所有字段数据。

## 主要特性

### 🎯 完整字段提取
根据您提供的图片，爬虫能够提取以下完整字段：

#### 机关信息
- 机关代码 (agency_code)
- 机关名称 (agency_name)  
- 单位名称 (unit_name)
- 机关地址 (agency_address)
- 联络人 (contact_person)
- 联络电话 (contact_phone)
- 传真号码 (fax_number)
- 电子邮件 (email)

#### 采购资料
- 标案案号 (case_number)
- 标案名称 (case_title)
- 标的分类 (subject_classification)
- 采购性质 (procurement_nature)
- 采购金额级距 (procurement_amount_range)
- 办理方式 (processing_method)
- 依据法条 (legal_basis)

#### 金额信息
- 预算金额 (budget_amount)
- 预算金额是否公开 (budget_public)
- 预计金额 (estimated_amount)
- 预计金额是否公开 (estimated_public)

#### 招标资料
- 招标方式 (tender_method)
- 决标方式 (award_method)
- 新增公告传输次数 (announcement_count)
- 招标状态 (tender_status)
- 公告日 (public_date)
- 是否复数决标 (is_multiple_award)
- 是否订有底价 (has_reserve_price)

#### 时间信息
- 截止投标 (bid_deadline)
- 开标时间 (opening_time)
- 开标地点 (opening_location)

#### 履约信息
- 履约地点 (performance_location)
- 履约期限 (performance_period)

#### 安全相关
- 是否属敏感性或国安采购 (is_sensitive_security)
- 是否涉及国家安全 (is_national_security)
- 是否受机关补助 (government_subsidy)
- 补助详情 (subsidy_details)

#### 厂商资格
- 厂商资格摘要 (vendor_qualifications)

## 使用方法

### 1. 基本使用

```python
from final_enhanced_crawler import EnhancedProcurementCrawler

# 创建爬虫实例
crawler = EnhancedProcurementCrawler()

# 爬取数据
results = crawler.crawl_with_full_details(
    keyword="餐飲",           # 搜索关键词
    tender_types=['招標'],    # 标案类型
    max_pages=2,             # 最大页数
    page_size=10             # 每页数量
)

# 保存数据
crawler.save_to_json(results, 'procurement_data.json')
```

### 2. 参数说明

#### keyword (搜索关键词)
- 类型: str
- 说明: 在标案名称中搜索的关键词
- 示例: "餐飲", "水电", "建筑"

#### tender_types (标案类型)
- 类型: List[str]
- 可选值: ['招標', '決標', '公開閱覽及公開徵求', '政府採購預告']
- 默认: ['招標', '決標']

#### max_pages (最大页数)
- 类型: int
- 说明: 最多抓取多少页数据
- 建议: 1-5页（避免过度请求）

#### page_size (每页数量)
- 类型: int
- 可选值: 10, 20, 50, 100
- 默认: 10

### 3. 高级用法

#### 只获取搜索结果列表（不获取详情）
```python
search_results = crawler.search_procurement_list(
    keyword="餐飲",
    tender_types=['決標'],
    max_pages=3
)
```

#### 单独提取详情页面
```python
detail_url = "https://web.pcc.gov.tw/tps/QueryTender/query/searchTenderDetail?pkPmsMain=..."
detail_data = crawler.extract_detail_fields(detail_url)
```

#### 保存为CSV格式
```python
# 需要先安装pandas: pip install pandas
crawler.save_to_csv(results, 'procurement_data.csv')
```

## 输出数据格式

### JSON格式示例
```json
{
  "case_number": "1140628",
  "case_title": "114学年度学校午餐外订桶餐采购案",
  "tender_type": "招標",
  "announcement_date": "114/07/15",
  "agency_code": "**********.32",
  "agency_name": "苗栗县立建国国民中学",
  "unit_name": "苗栗县立建国国民中学",
  "agency_address": "351 苗栗县 头份市 建国路119号",
  "contact_person": "曾镫慰",
  "contact_phone": "(037) 691491 #16",
  "fax_number": "(037) 691983",
  "email": "<EMAIL>",
  "budget_amount": "20,696,000元",
  "tender_method": "公开招标",
  "award_method": "最有利标",
  "performance_location": "苗栗县(非原住民地区)",
  "performance_period": "114/09/01-115/06/30",
  "detail_url": "https://web.pcc.gov.tw/tps/QueryTender/query/searchTenderDetail?pkPmsMain=..."
}
```

## 注意事项

### 1. 合规使用
- 请遵守网站的robots.txt和使用条款
- 避免过于频繁的请求，系统内置了延迟机制
- 仅用于合法的数据分析和研究目的

### 2. 性能优化
- 建议单次抓取不超过50条数据
- 如需大量数据，可分批次进行
- 网络不稳定时会自动重试

### 3. 错误处理
- 系统具备完善的错误处理机制
- 遇到问题会记录详细日志
- 部分页面无法访问时会跳过并继续

### 4. 数据质量
- 某些字段可能为空（取决于原始数据）
- 建议在使用前进行数据清洗
- 金额字段可能包含中文描述

## 故障排除

### 常见问题

#### 1. 搜索结果为空
- 检查关键词是否正确
- 尝试更换搜索关键词
- 确认网络连接正常

#### 2. 详情页面无法访问
- 某些页面可能有访问限制
- 系统会自动跳过并记录错误
- 可以手动检查URL是否有效

#### 3. 数据不完整
- 不同类型的采购案字段可能不同
- 系统会尽可能提取所有可用字段
- 空字段表示原始页面中没有该信息

## 扩展功能

### 自定义字段映射
如需添加新的字段提取，可以修改 `_map_field_value` 方法：

```python
def _map_field_value(self, label: str, value: str, data: ProcurementData):
    # 添加新的字段映射
    if '新字段标签' in label:
        data.new_field = value
```

### 添加数据过滤
可以在爬取过程中添加数据过滤逻辑：

```python
def filter_data(self, data_list):
    # 过滤条件示例：只保留预算金额大于100万的项目
    filtered = []
    for data in data_list:
        if data.get('budget_amount'):
            amount_str = data['budget_amount'].replace(',', '').replace('元', '')
            if amount_str.isdigit() and int(amount_str) > 1000000:
                filtered.append(data)
    return filtered
```

## 技术支持

如遇到技术问题，请检查：
1. Python版本（建议3.7+）
2. 依赖库是否正确安装
3. 网络连接是否正常
4. 目标网站是否可访问

## 更新日志

### v2.0 (当前版本)
- ✅ 完整字段提取功能
- ✅ 基于实际网页结构优化
- ✅ 支持多种输出格式
- ✅ 完善的错误处理机制
- ✅ 详细的使用文档

### v1.0
- 基础搜索和数据提取功能


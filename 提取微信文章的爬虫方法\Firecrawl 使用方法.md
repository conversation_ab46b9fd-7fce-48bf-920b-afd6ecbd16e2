
## Firecrawl 使用方法

Firecrawl 是一个将任何网站转化为LLM就绪（LLM-ready）的干净Markdown/结构化数据的工具。它提供了API和多种语言的SDK（Python, Node, Go, Rust）。

### 安装

使用pip安装Python SDK：
```bash
pip install firecrawl-py
```

### 刮取（Scraping）单个URL

使用 `scrape_url` 方法来刮取单个URL。它将URL作为参数，并以字典形式返回刮取到的数据，可以指定输出格式（如markdown, html）。

```python
from firecrawl import FirecrawlApp

app = FirecrawlApp(api_key="fc-YOUR_API_KEY") # 替换为你的API密钥

# 刮取一个网站：
scrape_result = app.scrape_url('firecrawl.dev', formats=['markdown', 'html'])
print(scrape_result)
```

返回结果示例：
```json
{
  "success": true,
  "data" : {
    "markdown": "...",
    "html": "...",
    "metadata": {
      "title": "Home - Firecrawl",
      "description": "Firecrawl crawls and converts any website into clean markdown.",
      "language": "en",
      "sourceURL": "https://firecrawl.dev",
      "statusCode": 200
    }
  }
}
```

### 爬行（Crawling）整个网站

使用 `crawl_url` 方法来爬行一个URL及其所有可访问的子页面。这会提交一个爬行任务并返回一个任务ID，用于检查爬行状态。

```python
from firecrawl import FirecrawlApp, ScrapeOptions

app = FirecrawlApp(api_key="fc-YOUR_API_KEY") # 替换为你的API密钥

# 爬行一个网站：
crawl_result = app.crawl_url(
  'https://firecrawl.dev', 
  limit=10, 
  scrape_options=ScrapeOptions(formats=['markdown', 'html']),
)
print(crawl_result)
```

返回任务ID示例：
```json
{
  "success": true,
  "id": "123-456-789",
  "url": "https://api.firecrawl.dev/v1/crawl/123-456-789"
}
```

### 检查爬行任务状态

使用 `check_crawl_status` 方法来检查爬行任务的状态并获取结果。

```python
crawl_status = app.check_crawl_status("<crawl_id>") # 替换为你的爬行任务ID
print(crawl_status)
```

返回状态示例：
```json
{
  "status": "scraping",
  "total": 36,
  "completed": 10,
  "creditsUsed": 10,
  "expiresAt": "2024-00-00T00:00:00.000Z",
  "next": "https://api.firecrawl.dev/v1/crawl/123-456-789?skip=10",
  "data": [
    {
      "markdown": "...",
      "html": "...",
      "metadata": {
        "title": "...",
        "language": "en",
        "sourceURL": "...",
        "description": "...",
        "statusCode": 200
      }
    },
    ...
  ]
}
```

**注意**: Firecrawl 需要API Key，虽然文章中提到是免费或提供免费使用方式，但通常API服务会有免费额度限制，超出后可能需要付费。请查阅其官方网站获取最新的免费政策。



## Crawl4AI 使用方法

Crawl4AI 是一个开源的、LLM友好的Web爬虫和刮取工具，旨在简化网页抓取并轻松从网页中提取有价值的信息。它是一个Python库。

### 安装

使用pip安装：
```bash
pip install crawl4ai
```

### 快速开始

以下是一个最小的Python脚本，用于创建一个 `AsyncWebCrawler`，抓取一个网页，并打印其Markdown输出的前300个字符：

```python
import asyncio
from crawl4ai import AsyncWebCrawler

async def main():
    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun("https://example.com")
        print(result.markdown[:300])  # 打印前300个字符

if __name__ == "__main__":
    asyncio.run(main())
```

**说明**：
*   `AsyncWebCrawler` 会启动一个无头浏览器（默认为Chromium）。
*   它会抓取指定的URL。
*   Crawl4AI 会自动将HTML转换为Markdown。

### 基本配置

Crawl4AI 的爬虫可以通过 `BrowserConfig` 和 `CrawlerRunConfig` 进行高度定制。

```python
import asyncio
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode

async def main():
    browser_conf = BrowserConfig(headless=True)  # 或 False 以显示浏览器
    run_conf = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS # 默认缓存模式为 CacheMode.ENABLED，若要获取最新内容需设置为 BYPASS
    )

    async with AsyncWebCrawler(config=browser_conf) as crawler:
        result = await crawler.arun(
            url="https://example.com",
            config=run_conf
        )
        print(result.markdown)

if __name__ == "__main__":
    asyncio.run(main())
```

### 生成Markdown输出

Crawl4AI 默认会自动从每个抓取的页面生成Markdown。你可以通过 `result.markdown` 获取原始Markdown，通过 `result.markdown.fit_markdown` 获取经过内容过滤器处理后的Markdown。

### 结构化数据提取（CSS/XPath）

Crawl4AI 可以使用CSS或XPath选择器提取结构化数据（JSON）。

```python
import asyncio
import json
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

async def main():
    schema = {
        "name": "Example Items",
        "baseSelector": "div.item",
        "fields": [
            {"name": "title", "selector": "h2", "type": "text"},
            {"name": "link", "selector": "a", "type": "attribute", "attribute": "href"}
        ]
    }

    raw_html = "<div class=\'item\'><h2>Item 1</h2><a href=\'https://example.com/item1\'>Link 1</a></div>"

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="raw://" + raw_html,
            config=CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                extraction_strategy=JsonCssExtractionStrategy(schema)
            )
        )
        data = json.loads(result.extracted_content)
        print(data)

if __name__ == "__main__":
    asyncio.run(main())
```

**注意**：Crawl4AI 也支持使用LLM自动生成提取schema，以及使用LLM进行更复杂的提取，包括支持开源和闭源的LLM模型。具体请参考其官方文档。




## Jina AI Reader API 使用方法

Jina AI Reader API 提供了一种极其简单的方式来抓取网页内容，无需编写代码，只需修改URL即可。

### 基本使用

在目标URL前加上 `r.jina.ai/` 或 `s.jina.ai/`（用于搜索结果抓取），即可通过API获取网页的干净内容（通常是Markdown格式）或结构化数据。

**示例**：

假设你要获取 `https://example.com` 的内容，只需在浏览器中输入或使用 `curl` 命令：

```
https://r.jina.ai/https://example.com
```

或者，如果你想获取搜索结果，例如搜索“AI 爬虫”：

```
https://s.jina.ai/?q=AI%20爬虫
```

这将返回处理后的网页内容，通常是LLM友好的Markdown格式。

### 核心特点和优势

*   **极简易用**: 无需编写代码，只需修改URL即可发起请求。
*   **即时内容获取**: 快速返回处理后的网页内容，适合需要快速获取单页信息的场景。
*   **处理动态内容**: Jina AI 的后端会自动处理JavaScript渲染等问题，用户无需关心。
*   **多种输出**: 除了返回干净的文本内容，也可能支持返回JSON格式的结构化数据。

**注意**：Jina AI Reader API 是一个在线服务，通常会有免费额度限制。请查阅其官方网站获取最新的免费政策和API Key的使用说明，以获得更高的速率限制。



## Scrapegraph-ai 使用方法

Scrapegraph-ai 是一个利用LLM和图（Graph）结构来执行网络爬取的Python库。它允许用户通过定义一个包含不同节点（如“抓取页面”、“生成抓取逻辑”、“解析数据”）的图来构建爬取流程，并可以利用LLM根据自然语言提示生成抓取逻辑。

### 安装

使用pip安装：
```bash
pip install scrapegraphai
```

你还需要安装 Playwright，用于基于JavaScript的抓取：
```bash
playwright install
```

**注意**：建议在虚拟环境中安装库，以避免与其他库冲突。

### 快速开始（SmartScraper）

`SmartScraper` 类是一个直接的图实现，它使用了网页抓取管道中最常见的节点。你可以使用它通过一个提示来从网站中提取信息。

**使用本地Ollama模型示例**：

首先，确保你已经运行了Ollama服务并下载了相应的模型（例如 `ollama/mistral`）。

```python
from scrapegraphai.graphs import SmartScraperGraph

graph_config = {
    "llm": {
        "model": "ollama/mistral",
        "temperature": 0,
        "format": "json",  # Ollama 需要明确指定格式
        "base_url": "http://localhost:11434",  # 设置 Ollama URL
    },
    "embeddings": {
        "model": "ollama/nomic-embed-text",
        "base_url": "http://localhost:11434",  # 设置 Ollama URL
    }
}

smart_scraper_graph = SmartScraperGraph(
    prompt="List me all the articles",
    source="https://perinim.github.io/projects",
    config=graph_config
)

result = smart_scraper_graph.run()
print(result)
```

**使用OpenAI模型示例**：

```python
from scrapegraphai.graphs import SmartScraperGraph

OPENAI_API_KEY = "YOUR_API_KEY" # 替换为你的OpenAI API Key

graph_config = {
    "llm": {
        "api_key": OPENAI_API_KEY,
        "model": "gpt-3.5-turbo",
    },
}

smart_scraper_graph = SmartScraperGraph(
    prompt="List me all the articles",
    source="https://perinim.github.io/projects",
    config=graph_config
)

result = smart_scraper_graph.run()
print(result)
```

**输出格式**：

所有情况下，输出都将是一个包含提取信息的字典，例如：

```json
{
    'titles': [
        'Rotary Pendulum RL'
    ],
    'descriptions': [
        'Open Source project aimed at controlling a real life rotary pendulum using RL algorithms'
    ]
}
```

**核心特点**：

*   **图驱动流程**: 将爬取任务分解为图形中的节点和边，使得复杂流程可视化和模块化。
*   **LLM集成**: 可以利用本地或远程的LLM来理解用户需求（例如，通过自然语言描述要抓取什么数据）并生成相应的抓取代码或策略。
*   **灵活性与可扩展性**: Python库的形式提供了高度的灵活性，用户可以自定义节点类型和图结构，以适应复杂的抓取任务。
*   **支持本地模型**: 允许使用本地运行的LLM模型，这对于数据隐私和成本控制非常重要。

**注意**：Scrapegraph-ai 是一个开源库，因此其使用本身是免费的。但如果使用OpenAI等闭源LLM模型，则需要支付相应的API费用。


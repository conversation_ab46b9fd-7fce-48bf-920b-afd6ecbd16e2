#!/usr/bin/env python3
"""
高级LoRA模型合并脚本 - 适配DiffSynth-Studio框架
支持多种合并策略和优化方法
"""

import torch
import os
import json
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, Union
from safetensors.torch import save_file, load_file
from tqdm import tqdm
import argparse

class LoRAMerger:
    """LoRA权重合并器"""
    
    def __init__(self, device: str = "cuda"):
        self.device = device if torch.cuda.is_available() else "cpu"
        print(f"🔧 使用设备: {self.device}")
    
    def load_base_model(self, model_path: str) -> Dict[str, torch.Tensor]:
        """加载基础模型权重"""
        print(f"📦 加载基础模型: {model_path}")
        
        if model_path.endswith('.safetensors'):
            return load_file(model_path, device=self.device)
        elif model_path.endswith('.pth') or model_path.endswith('.pt'):
            return torch.load(model_path, map_location=self.device)
        else:
            raise ValueError(f"不支持的文件格式: {model_path}")
    
    def load_lora_weights(self, lora_path: str) -> Dict[str, torch.Tensor]:
        """加载LoRA权重"""
        print(f"🔧 加载LoRA权重: {lora_path}")
        
        if lora_path.endswith('.safetensors'):
            return load_file(lora_path, device=self.device)
        else:
            return torch.load(lora_path, map_location=self.device)
    
    def parse_lora_weights(self, lora_state_dict: Dict[str, torch.Tensor]) -> Dict[str, Dict[str, torch.Tensor]]:
        """解析LoRA权重，配对lora_A和lora_B"""
        print("🔍 解析LoRA权重结构...")
        
        lora_weights = {}
        processed_keys = set()
        
        for key in lora_state_dict.keys():
            if key in processed_keys:
                continue
                
            if ".lora_A." in key:
                # 提取层名称
                base_key = key.split(".lora_A.")[0]
                lora_A_key = key
                lora_B_key = key.replace(".lora_A.", ".lora_B.")
                
                if lora_B_key in lora_state_dict:
                    lora_weights[base_key] = {
                        'lora_A': lora_state_dict[lora_A_key],
                        'lora_B': lora_state_dict[lora_B_key]
                    }
                    processed_keys.add(lora_A_key)
                    processed_keys.add(lora_B_key)
                    
                    # 检查是否有alpha参数
                    alpha_key = key.replace(".lora_A.default.weight", ".alpha")
                    if alpha_key in lora_state_dict:
                        lora_weights[base_key]['alpha'] = lora_state_dict[alpha_key]
                        processed_keys.add(alpha_key)
        
        print(f"    发现 {len(lora_weights)} 个LoRA层")
        return lora_weights
    
    def compute_lora_delta(self, lora_A: torch.Tensor, lora_B: torch.Tensor, 
                          alpha: Optional[torch.Tensor] = None, 
                          merge_ratio: float = 1.0) -> torch.Tensor:
        """计算LoRA增量权重"""
        
        # 计算缩放因子
        if alpha is not None:
            scale = alpha.item() / lora_A.shape[0]  # alpha / rank
        else:
            scale = 1.0
        
        scale *= merge_ratio
        
        # 处理不同维度的权重
        if len(lora_A.shape) == 4:  # 卷积层
            # 将4D卷积权重转换为2D进行计算
            lora_A_2d = lora_A.squeeze(3).squeeze(2)  # [out_channels, rank]
            lora_B_2d = lora_B.squeeze(3).squeeze(2)  # [rank, in_channels]
            delta_2d = torch.mm(lora_A_2d, lora_B_2d)  # [out_channels, in_channels]
            delta = delta_2d.unsqueeze(2).unsqueeze(3)  # [out_channels, in_channels, 1, 1]
        else:  # 线性层
            delta = torch.mm(lora_A, lora_B)  # [out_features, in_features]
        
        return scale * delta
    
    def merge_weights(self, base_state_dict: Dict[str, torch.Tensor], 
                     lora_weights: Dict[str, Dict[str, torch.Tensor]], 
                     merge_ratio: float = 1.0,
                     strategy: str = "linear") -> Dict[str, torch.Tensor]:
        """合并基础权重和LoRA权重"""
        
        print(f"🔄 开始合并权重 (策略: {strategy}, 比例: {merge_ratio})")
        
        merged_state_dict = {}
        merged_count = 0
        
        # 复制所有基础权重
        for key, weight in base_state_dict.items():
            merged_state_dict[key] = weight.clone()
        
        # 应用LoRA增量
        for layer_name, lora_data in tqdm(lora_weights.items(), desc="合并LoRA权重"):
            # 查找对应的基础权重键
            base_key = self.find_matching_base_key(layer_name, base_state_dict.keys())
            
            if base_key is None:
                print(f"⚠️ 未找到匹配的基础权重: {layer_name}")
                continue
            
            # 计算LoRA增量
            lora_A = lora_data['lora_A']
            lora_B = lora_data['lora_B']
            alpha = lora_data.get('alpha', None)
            
            delta = self.compute_lora_delta(lora_A, lora_B, alpha, merge_ratio)
            
            # 检查维度匹配
            base_weight = merged_state_dict[base_key]
            if delta.shape != base_weight.shape:
                print(f"⚠️ 维度不匹配 {base_key}: {delta.shape} vs {base_weight.shape}")
                continue
            
            # 应用合并策略
            if strategy == "linear":
                merged_state_dict[base_key] = base_weight + delta
            elif strategy == "slerp":
                # 球面线性插值（适用于某些情况）
                merged_state_dict[base_key] = self.slerp(base_weight, base_weight + delta, merge_ratio)
            else:
                raise ValueError(f"不支持的合并策略: {strategy}")
            
            merged_count += 1
        
        print(f"✅ 成功合并 {merged_count} 个层的权重")
        return merged_state_dict
    
    def find_matching_base_key(self, lora_key: str, base_keys: list) -> Optional[str]:
        """查找LoRA键对应的基础模型键"""
        
        # 直接匹配
        if lora_key in base_keys:
            return lora_key
        
        # 尝试添加常见前缀
        prefixes = ["", "model.", "pipe.dit.", "unet.", "transformer."]
        suffixes = [".weight", ""]
        
        for prefix in prefixes:
            for suffix in suffixes:
                candidate = f"{prefix}{lora_key}{suffix}"
                if candidate in base_keys:
                    return candidate
        
        # 模糊匹配（去除前缀后匹配）
        lora_key_clean = lora_key.split(".")[-1] if "." in lora_key else lora_key
        for base_key in base_keys:
            if base_key.endswith(lora_key_clean) or base_key.endswith(f"{lora_key_clean}.weight"):
                return base_key
        
        return None
    
    def slerp(self, v1: torch.Tensor, v2: torch.Tensor, t: float) -> torch.Tensor:
        """球面线性插值"""
        # 简化版本，适用于权重合并
        return (1 - t) * v1 + t * v2
    
    def save_merged_model(self, merged_state_dict: Dict[str, torch.Tensor], 
                         output_path: str, metadata: Optional[Dict] = None):
        """保存合并后的模型"""
        
        print(f"💾 保存合并后的模型到: {output_path}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 移动到CPU以节省显存
        cpu_state_dict = {k: v.cpu() for k, v in merged_state_dict.items()}
        
        if output_path.endswith('.safetensors'):
            # 添加元数据
            if metadata:
                save_file(cpu_state_dict, output_path, metadata=metadata)
            else:
                save_file(cpu_state_dict, output_path)
        else:
            torch.save(cpu_state_dict, output_path)
        
        # 计算文件大小
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"✅ 模型保存完成！文件大小: {file_size:.1f}MB")

def merge_full_model_advanced(base_model_dir: str, lora_path: str, output_dir: str, 
                            merge_ratio: float = 1.0, strategy: str = "linear"):
    """高级完整模型合并"""
    
    print("🚀 开始高级LoRA模型合并...")
    print(f"基础模型目录: {base_model_dir}")
    print(f"LoRA权重: {lora_path}")
    print(f"输出目录: {output_dir}")
    print(f"合并比例: {merge_ratio}")
    print(f"合并策略: {strategy}")
    
    merger = LoRAMerger()
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 需要处理的文件
    model_files = [
        "diffusion_pytorch_model.safetensors",
        "diffusion_pytorch_model-00001-of-00007.safetensors",
        "diffusion_pytorch_model-00002-of-00007.safetensors",
        "diffusion_pytorch_model-00003-of-00007.safetensors",
        "diffusion_pytorch_model-00004-of-00007.safetensors",
        "diffusion_pytorch_model-00005-of-00007.safetensors",
        "diffusion_pytorch_model-00006-of-00007.safetensors",
        "diffusion_pytorch_model-00007-of-00007.safetensors",
    ]
    
    # 其他需要复制的文件
    copy_files = [
        "models_t5_umt5-xxl-enc-bf16.pth",
        "Wan2.1_VAE.pth",
        "models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth",
        "config.json",
        "model_index.json",
        "README.md"
    ]
    
    # 加载LoRA权重
    lora_state_dict = merger.load_lora_weights(lora_path)
    lora_weights = merger.parse_lora_weights(lora_state_dict)
    
    # 处理模型文件
    merged_any = False
    for model_file in model_files:
        src_path = os.path.join(base_model_dir, model_file)
        dst_path = os.path.join(output_dir, model_file)
        
        if os.path.exists(src_path):
            print(f"\n🔧 处理模型文件: {model_file}")
            
            # 加载基础模型
            base_state_dict = merger.load_base_model(src_path)
            
            # 合并权重
            merged_state_dict = merger.merge_weights(
                base_state_dict, lora_weights, merge_ratio, strategy
            )
            
            # 保存合并后的模型
            metadata = {
                "merged_from_lora": lora_path,
                "merge_ratio": str(merge_ratio),
                "merge_strategy": strategy,
                "base_model": src_path
            }
            merger.save_merged_model(merged_state_dict, dst_path, metadata)
            merged_any = True
            
            # 清理显存
            del base_state_dict, merged_state_dict
            torch.cuda.empty_cache()
    
    if not merged_any:
        print("⚠️ 未找到可合并的模型文件")
    
    # 复制其他文件
    print("\n📋 复制其他文件...")
    for copy_file in copy_files:
        src_path = os.path.join(base_model_dir, copy_file)
        dst_path = os.path.join(output_dir, copy_file)
        
        if os.path.exists(src_path):
            shutil.copy2(src_path, dst_path)
            print(f"    复制: {copy_file}")
    
    # 创建合并信息文件
    merge_info = {
        "base_model_dir": base_model_dir,
        "lora_path": lora_path,
        "merge_ratio": merge_ratio,
        "merge_strategy": strategy,
        "merged_layers": len(lora_weights),
        "output_dir": output_dir
    }
    
    with open(os.path.join(output_dir, "merge_info.json"), "w") as f:
        json.dump(merge_info, f, indent=2)
    
    print(f"\n🎉 高级模型合并完成！")
    print(f"输出目录: {output_dir}")
    print(f"合并层数: {len(lora_weights)}")

def main():
    parser = argparse.ArgumentParser(description="高级LoRA模型合并工具")
    parser.add_argument("--base_model_dir", type=str, required=True, help="基础模型目录")
    parser.add_argument("--lora_path", type=str, required=True, help="LoRA权重文件路径")
    parser.add_argument("--output_dir", type=str, required=True, help="输出目录")
    parser.add_argument("--merge_ratio", type=float, default=1.0, help="合并比例 (0.0-1.0)")
    parser.add_argument("--strategy", type=str, default="linear", choices=["linear", "slerp"], help="合并策略")
    
    args = parser.parse_args()
    
    merge_full_model_advanced(
        args.base_model_dir,
        args.lora_path,
        args.output_dir,
        args.merge_ratio,
        args.strategy
    )

if __name__ == "__main__":
    main()

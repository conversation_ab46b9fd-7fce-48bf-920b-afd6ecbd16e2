#!/usr/bin/env python3
"""
强制清理GPU内存
"""

import torch
import gc
import os

def force_clear_gpu():
    """强制清理GPU内存"""
    print("🧹 强制清理GPU内存...")
    
    if torch.cuda.is_available():
        # 清理所有GPU
        for i in range(torch.cuda.device_count()):
            torch.cuda.set_device(i)
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
            torch.cuda.ipc_collect()
        
        # 重置CUDA上下文
        torch.cuda.reset_peak_memory_stats()
        
        # 显示清理后状态
        print("GPU内存状态:")
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**3
            reserved = torch.cuda.memory_reserved(i) / 1024**3
            total = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: 已分配={allocated:.2f}GB, 已保留={reserved:.2f}GB, 总计={total:.1f}GB")
    
    # Python垃圾回收
    gc.collect()
    
    print("✅ 强制清理完成")

if __name__ == "__main__":
    force_clear_gpu()

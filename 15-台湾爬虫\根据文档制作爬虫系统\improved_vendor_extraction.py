#!/usr/bin/env python3
"""
改進的廠商信息提取 - 專門針對您提供的詳情頁格式
"""

from enhanced_procurement_crawler import EnhancedProcurementCrawler, VendorInfo
from bs4 import BeautifulSoup
import re
import json

class ImprovedVendorExtractor:
    """改進的廠商信息提取器"""
    
    def __init__(self):
        self.crawler = EnhancedProcurementCrawler()
    
    def extract_vendors_from_url(self, url):
        """從URL提取廠商信息"""
        try:
            # 獲取頁面內容
            response = self.crawler._make_request(url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取廠商信息
            vendors, bidder_count = self.extract_vendors_improved(soup)
            
            return vendors, bidder_count
            
        except Exception as e:
            print(f"❌ 提取廠商信息失敗: {str(e)}")
            return [], 0
    
    def extract_vendors_improved(self, soup):
        """改進的廠商信息提取方法"""
        vendors = []
        bidder_count = 0
        
        try:
            # 方法1：從文本中提取投標廠商家數
            text_content = soup.get_text()
            bidder_match = re.search(r'投標廠商家數.*?(\d+)', text_content)
            if bidder_match:
                bidder_count = int(bidder_match.group(1))
                print(f"📊 投標廠商家數: {bidder_count}")
            
            # 方法2：按照結構化方式提取廠商信息
            vendors = self._extract_vendors_by_structure(soup)
            
            # 方法3：如果結構化方式失敗，使用表格方式
            if not vendors:
                vendors = self._extract_vendors_by_table(soup)
            
            print(f"✅ 成功提取 {len(vendors)} 個廠商信息")
            
        except Exception as e:
            print(f"❌ 廠商信息提取失敗: {str(e)}")
        
        return vendors, bidder_count
    
    def _extract_vendors_by_structure(self, soup):
        """按照HTML結構提取廠商信息"""
        vendors = []
        
        try:
            # 查找所有包含"投標廠商"的元素
            vendor_elements = soup.find_all(string=re.compile(r'投標廠商\d+'))
            
            for vendor_element in vendor_elements:
                vendor_num = re.search(r'投標廠商(\d+)', vendor_element).group(1)
                print(f"🔍 處理投標廠商{vendor_num}")
                
                vendor = VendorInfo()
                
                # 從當前元素開始向下查找相關信息
                current_element = vendor_element.parent
                
                # 查找後續的表格行
                while current_element:
                    next_rows = current_element.find_next_siblings('tr', limit=20)
                    
                    for row in next_rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:
                            label = cells[0].get_text(strip=True)
                            value = cells[1].get_text(strip=True)
                            
                            # 如果遇到下一個廠商，停止
                            if '投標廠商' in label and vendor_num not in label:
                                break
                            
                            # 提取廠商信息
                            if '廠商代碼' in label:
                                vendor.vendor_code = value
                                print(f"  廠商代碼: {value}")
                            elif '廠商名稱' in label:
                                vendor.vendor_name = value
                                print(f"  廠商名稱: {value}")
                            elif '是否得標' in label:
                                vendor.is_winner = value
                                print(f"  是否得標: {value}")
                            elif '組織型態' in label:
                                vendor.organization_type = value
                                print(f"  組織型態: {value}")
                            elif '廠商業別' in label:
                                vendor.business_type = value
                                print(f"  廠商業別: {value}")
                            elif '廠商地址' in label:
                                vendor.vendor_address = value
                                print(f"  廠商地址: {value}")
                            elif '廠商電話' in label:
                                vendor.vendor_phone = value
                                print(f"  廠商電話: {value}")
                            elif '決標金額' in label:
                                # 處理金額格式
                                amount_match = re.search(r'([\d,]+)元?\s*([^\d]*)', value)
                                if amount_match:
                                    vendor.award_amount = amount_match.group(1) + "元"
                                    vendor.award_amount_chinese = amount_match.group(2).strip()
                                else:
                                    vendor.award_amount = value
                                print(f"  決標金額: {vendor.award_amount}")
                            elif '得標廠商國別' in label:
                                vendor.winner_country = value
                                print(f"  得標廠商國別: {value}")
                            elif '是否為中小企業' in label:
                                vendor.is_sme = value
                                print(f"  是否為中小企業: {value}")
                            elif '履約起迄日期' in label:
                                vendor.performance_period = value
                                print(f"  履約起迄日期: {value}")
                            elif '雇用員工總人數是否超過100人' in label:
                                vendor.over_100_employees = value
                                print(f"  雇用員工總人數是否超過100人: {value}")
                    
                    break  # 只處理第一個找到的父元素
                
                # 如果找到了廠商名稱，就添加到列表中
                if vendor.vendor_name:
                    vendors.append(vendor)
                    print(f"✅ 成功添加廠商: {vendor.vendor_name}")
                else:
                    print(f"⚠️ 廠商{vendor_num}缺少名稱信息")
                    
        except Exception as e:
            print(f"❌ 結構化提取失敗: {str(e)}")
        
        return vendors
    
    def _extract_vendors_by_table(self, soup):
        """通過表格方式提取廠商信息"""
        vendors = []
        
        try:
            tables = soup.find_all('table')
            current_vendor = None
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        label = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        # 檢測新廠商開始
                        if '廠商代碼' in label and current_vendor is None:
                            current_vendor = VendorInfo()
                        
                        if current_vendor:
                            if '廠商代碼' in label:
                                current_vendor.vendor_code = value
                            elif '廠商名稱' in label:
                                current_vendor.vendor_name = value
                            elif '是否得標' in label:
                                current_vendor.is_winner = value
                            elif '組織型態' in label:
                                current_vendor.organization_type = value
                            elif '廠商業別' in label:
                                current_vendor.business_type = value
                            elif '廠商地址' in label:
                                current_vendor.vendor_address = value
                            elif '廠商電話' in label:
                                current_vendor.vendor_phone = value
                            elif '決標金額' in label:
                                amount_match = re.search(r'([\d,]+)元?\s*([^\d]*)', value)
                                if amount_match:
                                    current_vendor.award_amount = amount_match.group(1) + "元"
                                    current_vendor.award_amount_chinese = amount_match.group(2).strip()
                                else:
                                    current_vendor.award_amount = value
                            elif '得標廠商國別' in label:
                                current_vendor.winner_country = value
                            elif '是否為中小企業' in label:
                                current_vendor.is_sme = value
                            elif '履約起迄日期' in label:
                                current_vendor.performance_period = value
                            elif '雇用員工總人數是否超過100人' in label:
                                current_vendor.over_100_employees = value
                            
                            # 如果遇到下一個廠商代碼，保存當前廠商
                            if '廠商代碼' in label and current_vendor.vendor_name:
                                vendors.append(current_vendor)
                                current_vendor = VendorInfo()
                                current_vendor.vendor_code = value
            
            # 添加最後一個廠商
            if current_vendor and current_vendor.vendor_name:
                vendors.append(current_vendor)
                
        except Exception as e:
            print(f"❌ 表格方式提取失敗: {str(e)}")
        
        return vendors

def test_improved_extraction():
    """測試改進的廠商信息提取"""
    print("🚀 開始測試改進的廠商信息提取...")
    
    # 測試URL
    test_url = "https://web.pcc.gov.tw/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetail?pkAtmMain=NzAxNTM4ODk="
    
    # 創建提取器
    extractor = ImprovedVendorExtractor()
    
    # 提取廠商信息
    vendors, bidder_count = extractor.extract_vendors_from_url(test_url)
    
    # 顯示結果
    print(f"\n📊 === 提取結果 ===")
    print(f"投標廠商家數: {bidder_count}")
    print(f"實際提取廠商數: {len(vendors)}")
    
    for i, vendor in enumerate(vendors):
        print(f"\n🏢 廠商 {i+1}:")
        print(f"  廠商代碼: {vendor.vendor_code}")
        print(f"  廠商名稱: {vendor.vendor_name}")
        print(f"  是否得標: {vendor.is_winner}")
        print(f"  組織型態: {vendor.organization_type}")
        print(f"  廠商業別: {vendor.business_type}")
        print(f"  廠商地址: {vendor.vendor_address}")
        print(f"  廠商電話: {vendor.vendor_phone}")
        print(f"  決標金額: {vendor.award_amount}")
        print(f"  決標金額(中文): {vendor.award_amount_chinese}")
        print(f"  得標廠商國別: {vendor.winner_country}")
        print(f"  是否為中小企業: {vendor.is_sme}")
        print(f"  履約起迄日期: {vendor.performance_period}")
        print(f"  雇用員工總人數是否超過100人: {vendor.over_100_employees}")
    
    # 保存結果
    result_data = {
        'test_url': test_url,
        'bidder_count': bidder_count,
        'extracted_vendor_count': len(vendors),
        'vendors': [vendor.__dict__ for vendor in vendors]
    }
    
    with open('improved_vendor_extraction_result.json', 'w', encoding='utf-8') as f:
        json.dump(result_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 結果已保存到: improved_vendor_extraction_result.json")

if __name__ == "__main__":
    test_improved_extraction()

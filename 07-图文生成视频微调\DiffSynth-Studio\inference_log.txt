2025-07-18 11:54:36,542 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-18 11:54:43,666 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-18 11:54:50,452 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-18 11:54:52,435 - modelscope - INFO - Target directory already exists, skipping creation.
2025-07-18 11:54:56,606 - modelscope - INFO - Target directory already exists, skipping creation.
Traceback (most recent call last):
  File "/root/sj-tmp/DiffSynth-Studio/simple_video_lora_test.py", line 45, in main
    pipe.load_lora(module="dit", path=lora_checkpoint)
  File "/root/sj-tmp/DiffSynth-Studio/diffsynth/pipelines/wan_video_new.py", line 255, in load_lora
    loader.load(module, lora, alpha=alpha)
  File "/root/sj-tmp/DiffSynth-Studio/diffsynth/lora/__init__.py", line 31, in load
    for name, module in model.named_modules():
                        ^^^^^^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'named_modules'
🎬 简化视频LoRA推理测试
==================================================
✅ 找到检查点: 73.2MB
📦 初始化Pipeline...
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-I2V-14B-480P
Loading models from: ['./models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00007-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00006-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00001-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00004-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00003-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00005-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00002-of-00007.safetensors']
    model_name: wan_video_dit model_class: WanModel
        This model is initialized with extra kwargs: {'has_image_input': True, 'patch_size': [1, 2, 2], 'in_dim': 36, 'dim': 5120, 'ffn_dim': 13824, 'freq_dim': 256, 'text_dim': 4096, 'out_dim': 16, 'num_heads': 40, 'num_layers': 40, 'eps': 1e-06}
    The following models are loaded: ['wan_video_dit'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth
    model_name: wan_video_text_encoder model_class: WanTextEncoder
    The following models are loaded: ['wan_video_text_encoder'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
Loading models from: ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth
    model_name: wan_video_vae model_class: WanVideoVAE
    The following models are loaded: ['wan_video_vae'].
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-I2V-14B-480P
Loading models from: ./models/Wan-AI/Wan2.1-I2V-14B-480P/models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth
    model_name: wan_video_image_encoder model_class: WanImageEncoder
    The following models are loaded: ['wan_video_image_encoder'].
Using wan_video_text_encoder from ./models/Wan-AI/Wan2.1-T2V-1.3B/models_t5_umt5-xxl-enc-bf16.pth.
Using wan_video_dit from ['./models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00007-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00006-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00001-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00004-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00003-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00005-of-00007.safetensors', './models/Wan-AI/Wan2.1-I2V-14B-480P/diffusion_pytorch_model-00002-of-00007.safetensors'].
Using wan_video_vae from ./models/Wan-AI/Wan2.1-T2V-1.3B/Wan2.1_VAE.pth.
Using wan_video_image_encoder from ./models/Wan-AI/Wan2.1-I2V-14B-480P/models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth.
No wan_video_motion_controller models available.
No wan_video_vace models available.
Downloading Model from https://www.modelscope.cn to directory: /root/sj-tmp/DiffSynth-Studio/models/Wan-AI/Wan2.1-T2V-1.3B
✅ Pipeline初始化成功
🔧 加载LoRA权重...
❌ 错误: 'str' object has no attribute 'named_modules'

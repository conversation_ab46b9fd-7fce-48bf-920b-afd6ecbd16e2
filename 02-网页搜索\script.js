class WebSearchApp {
    constructor() {
        // 搜索相关DOM元素
        this.searchBtn = document.getElementById('searchBtn');
        this.questionInput = document.getElementById('questionInput');
        this.maxPagesInput = document.getElementById('maxPages');
        this.progressSection = document.getElementById('progressSection');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        this.statsDiv = document.getElementById('stats');
        this.resultsSection = document.getElementById('resultsSection');
        this.resultsContainer = document.getElementById('resultsContainer');
        this.resultsSummary = document.getElementById('resultsSummary');
        this.loading = document.getElementById('loading');
        this.globalSummarySection = document.getElementById('globalSummarySection');
        this.globalSummaryMeta = document.getElementById('globalSummaryMeta');
        this.globalSummaryContent = document.getElementById('globalSummaryContent');

        // 认证相关DOM元素
        this.loginSection = document.getElementById('loginSection');
        this.registerSection = document.getElementById('registerSection');
        this.mainContent = document.getElementById('mainContent');
        this.userInfo = document.getElementById('userInfo');
        this.userDisplay = document.getElementById('userDisplay');
        this.loginForm = document.getElementById('loginForm');
        this.registerForm = document.getElementById('registerForm');
        this.showRegister = document.getElementById('showRegister');
        this.showLogin = document.getElementById('showLogin');
        this.logoutBtn = document.getElementById('logoutBtn');

        // 应用状态
        this.isSearching = false;
        this.currentResults = [];
        this.currentUser = null;
        this.authToken = localStorage.getItem('authToken');

        this.initEventListeners();
        this.checkAuthStatus();
    }
    
    initEventListeners() {
        // 搜索相关事件
        this.searchBtn.addEventListener('click', () => this.startSearch());
        this.questionInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.startSearch();
            }
        });

        // 认证相关事件
        this.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        this.registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        this.showRegister.addEventListener('click', (e) => {
            e.preventDefault();
            this.showRegisterForm();
        });
        this.showLogin.addEventListener('click', (e) => {
            e.preventDefault();
            this.showLoginForm();
        });
        this.logoutBtn.addEventListener('click', () => this.handleLogout());
    }

    // 认证相关方法
    async checkAuthStatus() {
        console.log('检查认证状态，当前token:', this.authToken);

        // 先尝试通过cookie检查认证状态
        try {
            const headers = {};

            // 如果有token，添加Authorization头
            if (this.authToken && this.authToken !== 'null' && this.authToken !== 'undefined') {
                headers['Authorization'] = `Bearer ${this.authToken}`;
                console.log('使用token检查认证');
            } else {
                console.log('使用cookie检查认证');
            }

            const response = await fetch('/api/user/info', {
                headers: headers,
                credentials: 'include' // 确保发送cookies
            });

            console.log('用户信息响应状态:', response.status);

            if (response.ok) {
                const userData = await response.json();
                console.log('用户数据:', userData);
                this.currentUser = userData.user || userData;
                this.showMainContent();
            } else {
                // 认证失败，显示登录界面
                console.log('认证失败，清除认证信息');
                localStorage.removeItem('authToken');
                this.authToken = null;
                this.showLoginForm();
            }
        } catch (error) {
            console.error('检查认证状态失败:', error);
            this.showLoginForm();
        }
    }

    async handleLogin(e) {
        e.preventDefault();
        const formData = new FormData(this.loginForm);
        const loginData = {
            username: formData.get('username'),
            password: formData.get('password')
        };

        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(loginData)
            });

            const result = await response.json();

            if (response.ok) {
                this.authToken = result.token;
                this.currentUser = result.user;
                localStorage.setItem('authToken', this.authToken);
                this.showMainContent();
            } else {
                alert(result.error || '登录失败');
            }
        } catch (error) {
            console.error('登录错误:', error);
            alert('登录失败，请重试');
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        const formData = new FormData(this.registerForm);
        const registerData = {
            username: formData.get('username'),
            email: formData.get('email'),
            password: formData.get('password')
        };

        try {
            const response = await fetch('/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(registerData)
            });

            const result = await response.json();

            if (response.ok) {
                alert('注册成功！请登录');
                this.showLoginForm();
            } else {
                alert(result.error || '注册失败');
            }
        } catch (error) {
            console.error('注册错误:', error);
            alert('注册失败，请重试');
        }
    }

    handleLogout() {
        localStorage.removeItem('authToken');
        this.authToken = null;
        this.currentUser = null;
        this.showLoginForm();
    }

    showLoginForm() {
        this.loginSection.style.display = 'block';
        this.registerSection.style.display = 'none';
        this.mainContent.style.display = 'none';
        this.userInfo.style.display = 'none';
    }

    showRegisterForm() {
        this.loginSection.style.display = 'none';
        this.registerSection.style.display = 'block';
        this.mainContent.style.display = 'none';
        this.userInfo.style.display = 'none';
    }

    showMainContent() {
        this.loginSection.style.display = 'none';
        this.registerSection.style.display = 'none';
        this.mainContent.style.display = 'block';
        this.userInfo.style.display = 'flex';

        if (this.currentUser) {
            this.userDisplay.textContent = `${this.currentUser.username} (积分: ${this.currentUser.credits})`;
        }
    }
    
    async startSearch() {
        const question = this.questionInput.value.trim();
        const maxPages = parseInt(this.maxPagesInput.value);

        if (!question) {
            alert('请输入您的问题');
            return;
        }
        
        if (this.isSearching) {
            return;
        }
        
        this.isSearching = true;
        this.searchBtn.disabled = true;
        this.searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> AI思考中...';
        
        // 显示进度条
        this.progressSection.style.display = 'block';
        this.progressFill.style.width = '0%';
        this.progressText.textContent = '正在初始化搜索...';
        this.statsDiv.textContent = '';
        
        // 清空之前的结果
        this.resultsContainer.innerHTML = '';
        this.currentResults = [];
        document.querySelector('.results-header').style.display = 'none';
        this.globalSummarySection.style.display = 'none';
        
        try {
            await this.performSearch(question, maxPages);
        } catch (error) {
            console.error('搜索失败:', error);
            this.showError('搜索失败，请稍后重试');
        } finally {
            this.isSearching = false;
            this.searchBtn.disabled = false;
            this.searchBtn.innerHTML = '<i class="fas fa-brain"></i> AI智能回答';
        }
    }
    
    async performSearch(question, maxPages) {
        console.log('发送搜索请求，token:', this.authToken);

        const headers = {
            'Content-Type': 'application/json'
        };

        // 如果有token，添加Authorization头
        if (this.authToken && this.authToken !== 'null' && this.authToken !== 'undefined') {
            headers['Authorization'] = `Bearer ${this.authToken}`;
            console.log('使用token认证');
        } else {
            console.log('使用cookie认证');
        }

        console.log('请求头:', headers);

        const response = await fetch('/api/search', {
            method: 'POST',
            headers: headers,
            credentials: 'include', // 确保发送cookies
            body: JSON.stringify({
                question: question,
                max_pages: maxPages
            })
        });

        console.log('搜索响应状态:', response.status);

        if (!response.ok) {
            if (response.status === 401) {
                // 认证失败，重新登录
                console.log('认证失败，清除token并重新登录');
                this.handleLogout();
                throw new Error('认证失败，请重新登录');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            for (const line of lines) {
                if (line.trim() && line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        this.handleSearchUpdate(data);
                    } catch (e) {
                        console.error('解析数据失败:', e);
                    }
                }
            }
        }
    }
    
    handleSearchUpdate(data) {
        switch (data.type) {
            case 'progress':
                this.updateProgress(data.progress, data.message);
                break;
            case 'stats':
                this.updateStats(data.stats);
                break;
            case 'article':
                this.addArticle(data.article);
                break;
            case 'complete':
                this.searchComplete(data.summary);
                break;
            case 'error':
                this.showError(data.message);
                break;
        }
    }
    
    updateProgress(progress, message) {
        this.progressFill.style.width = `${progress}%`;
        this.progressText.textContent = message;
    }
    
    updateStats(stats) {
        this.statsDiv.textContent = `已获取 ${stats.total_links} 个链接，成功解析 ${stats.success_count} 篇文章`;
    }
    
    addArticle(article) {
        this.currentResults.push(article);
        
        // 显示结果区域
        document.querySelector('.results-header').style.display = 'block';
        
        const articleCard = this.createArticleCard(article);
        this.resultsContainer.appendChild(articleCard);
        
        // 滚动到新添加的文章
        articleCard.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
    
    createArticleCard(article) {
        const card = document.createElement('div');
        card.className = 'article-card';

        const contentPreview = article.content.length > 200
            ? article.content.substring(0, 200) + '...'
            : article.content;

        card.innerHTML = `
            <div class="article-title">${this.escapeHtml(article.title)}</div>
            <div class="article-meta">
                <span><i class="fas fa-calendar"></i> ${article.pub_date}</span>
                <span><i class="fas fa-file-text"></i> ${article.content.length} 字符</span>
            </div>
            <div class="article-content">${this.escapeHtml(contentPreview)}</div>
            <a href="${article.url}" target="_blank" class="article-link">
                <i class="fas fa-external-link-alt"></i> 查看原文
            </a>
        `;

        return card;
    }
    
    searchComplete(summary) {
        this.progressFill.style.width = '100%';
        this.progressText.textContent = '搜索完成！';

        // 显示AI回答
        if (summary.question_answer) {
            this.showGlobalSummary(summary);
        }

        this.resultsSummary.innerHTML = `
            <i class="fas fa-check-circle" style="color: #28a745;"></i>
            搜索完成！共找到 <strong>${summary.total_articles}</strong> 篇文章，
            筛选出 <strong>${summary.relevant_articles || 0}</strong> 篇相关文章，
            成功保存 <strong>${summary.success_count}</strong> 篇到本地文件
            ${summary.question_answer ? '<br><i class="fas fa-robot" style="color: #667eea;"></i> 已生成AI智能回答' : ''}
        `;

        // 3秒后隐藏进度条
        setTimeout(() => {
            this.progressSection.style.display = 'none';
        }, 3000);
    }

    showGlobalSummary(summary) {
        // 显示AI回答区域
        this.globalSummarySection.style.display = 'block';

        // 设置元信息
        this.globalSummaryMeta.innerHTML = `
            <span><i class="fas fa-question-circle"></i> 问题: ${summary.question}</span>
            <span><i class="fas fa-search"></i> 搜索关键词: ${summary.keyword}</span>
            <span><i class="fas fa-file-alt"></i> 总文章: ${summary.total_articles}篇</span>
            <span><i class="fas fa-filter"></i> 相关文章: ${summary.relevant_articles || 0}篇</span>
            <span><i class="fas fa-robot"></i> AI模型: DeepSeek-R1-Distill-Qwen-32B</span>
            <span><i class="fas fa-clock"></i> 生成时间: ${new Date().toLocaleString()}</span>
        `;

        // 设置回答内容
        this.globalSummaryContent.textContent = summary.question_answer;

        // 滚动到AI回答
        this.globalSummarySection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
    
    showError(message) {
        this.progressText.textContent = `错误: ${message}`;
        this.progressFill.style.background = '#dc3545';
        
        setTimeout(() => {
            this.progressSection.style.display = 'none';
        }, 5000);
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new WebSearchApp();
});

# AI智能问答搜索系统

一个基于问题导向的智能搜索应用，集成DeepSeek-R1-Distill-Qwen-32B模型，能够根据用户问题搜索相关文章并生成针对性AI回答。

## 功能特点

- ❓ **问题导向**: 支持自然语言问题输入，而非简单关键词搜索
- 🤖 **AI智能回答**: 集成DeepSeek-R1-Distill-Qwen-32B模型，基于搜索文章回答用户问题
- 🔍 **智能搜索**: 自动从问题中提取关键词，基于百度搜索引擎抓取相关内容
- 📊 **实时进度**: 搜索过程实时显示，包括进度条和统计信息
- 📱 **响应式设计**: 支持桌面和移动设备
- 💾 **自动保存**: 搜索结果和AI回答自动保存为Markdown文件
- 🎯 **精准提取**: 智能提取文章标题、内容、发布日期，解决乱码问题
- 📁 **分类存储**: 按关键词和日期自动分类存储
- 🔧 **编码优化**: 智能检测和处理多种字符编码，确保内容准确性

## 技术栈

### 前端
- HTML5 + CSS3 + JavaScript (ES6+)
- Font Awesome 图标库
- 响应式设计

### 后端
- Python Flask
- requests + lxml (网页抓取)
- fake-useragent (用户代理伪装)
- chardet (编码检测)
- DeepSeek-R1-Distill-Qwen-32B (AI总结)

## 安装和运行

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

```bash
python app.py
```

### 3. 访问应用

打开浏览器访问: http://localhost:5000

## 使用说明

1. **输入问题**: 在问题输入框中输入您的问题（支持自然语言）
   - 例如："算家云在哪里？"
   - 例如："微短剧行业发展如何？"
2. **设置页数**: 选择要搜索的页数（1-20页）
3. **AI智能回答**: 点击"AI智能回答"按钮
4. **查看进度**: 实时查看搜索进度和统计信息
5. **浏览结果**: 搜索到的文章会实时显示在页面上
6. **查看AI回答**: AI会基于搜索到的文章生成针对性回答
7. **查看文件**: 搜索完成后，文章和AI回答会自动保存到本地文件

## 文件结构

```
├── app.py              # Flask后端服务
├── crawler.py          # 爬虫核心逻辑
├── index.html          # 前端主页面
├── styles.css          # 样式文件
├── script.js           # 前端JavaScript
├── requirements.txt    # Python依赖
├── README.md          # 说明文档
└── [关键词]/          # 搜索结果存储目录
    ├── [日期]/
    │   └── [文章].md
    └── AI问答_[关键词]_[时间].md  # AI回答文件
```

## API接口

### POST /api/search
搜索接口，支持服务器发送事件(SSE)流式响应

**请求参数:**
```json
{
    "keyword": "搜索关键词",
    "max_pages": 3
}
```

**响应格式:**
```
data: {"type": "progress", "progress": 50, "message": "正在搜索..."}
data: {"type": "article", "article": {...}}
data: {"type": "complete", "summary": {...}}
```

### GET /api/status
获取服务状态

## AI总结功能

本系统集成了DeepSeek-R1-Distill-Qwen-32B模型，为每篇文章自动生成智能总结：

- **API地址**: http://*************:8080/v1
- **模型**: DeepSeek-R1-Distill-Qwen-32B
- **总结格式**: 包含核心要点、主要内容、关键信息、价值分析
- **自动保存**: AI总结会自动保存到Markdown文件中
- **实时显示**: 前端界面实时显示AI总结结果

## 注意事项

1. **请求频率**: 内置了随机延时机制，避免过于频繁的请求
2. **编码处理**: 智能检测和处理多种字符编码，解决乱码问题
3. **错误重试**: 网络请求失败时会自动重试
4. **内容过滤**: 自动过滤广告、版权声明等非正文内容
5. **AI服务**: 确保AI服务正常运行，否则会跳过AI总结功能

## 许可证

MIT License

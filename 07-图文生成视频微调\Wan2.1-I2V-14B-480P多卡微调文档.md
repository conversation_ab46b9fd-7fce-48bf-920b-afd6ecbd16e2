# Wan-AI/Wan2.1-I2V-14B-480P 多卡微调详细文档

## 概述

本文档详细介绍如何使用DiffSynth-Studio框架对Wan-AI/Wan2.1-I2V-14B-480P模型进行多卡微调。Wan2.1-I2V-14B-480P是一个强大的图像到视频生成模型，能够根据输入图像生成高质量的480P视频序列。

## 环境准备

### 1. 创建Conda虚拟环境

```bash
# 创建新的conda环境
conda create -n wan_i2v python=3.10 -y
conda activate wan_i2v

# 更新pip
pip install --upgrade pip
```

### 2. 安装依赖

```bash
# 克隆DiffSynth-Studio仓库
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 安装基础依赖
pip install -e .

# 安装训练相关依赖
pip install accelerate
pip install transformers
pip install diffusers
pip install xformers
pip install modelscope
pip install opencv-python
pip install pillow
pip install numpy
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装图像处理相关依赖
pip install imageio
pip install imageio-ffmpeg
pip install decord
pip install av

# 安装其他必要依赖
pip install wandb  # 可选，用于训练监控
pip install tensorboard  # 可选，用于训练可视化
```

### 3. 下载预训练模型

```bash
# 使用modelscope下载Wan2.1-I2V-14B-480P模型
modelscope download Wan-AI/Wan2.1-I2V-14B-480P --local_dir ./models/Wan2.1-I2V-14B-480P

# 或者使用huggingface-hub下载
# pip install huggingface_hub
# huggingface-cli download Wan-AI/Wan2.1-I2V-14B-480P --local-dir ./models/Wan2.1-I2V-14B-480P
```

## 数据集准备

### 1. 数据集格式

创建图像到视频的训练数据集：

```
dataset/
├── images/
│   ├── image_001.jpg
│   ├── image_002.jpg
│   └── ...
├── videos/
│   ├── video_001.mp4
│   ├── video_002.mp4
│   └── ...
├── prompts/
│   ├── prompt_001.txt
│   ├── prompt_002.txt
│   └── ...
└── metadata.json
```

### 2. 自定义数据集配置

创建数据集配置文件 `i2v_dataset_config.json`：

```json
{
    "dataset_path": "./dataset",
    "image_folder": "images",
    "video_folder": "videos",
    "prompt_folder": "prompts",
    "resolution": [480, 480],
    "frame_count": 16,
    "fps": 8,
    "max_sequence_length": 256,
    "batch_size": 1,
    "num_workers": 4,
    "image_extensions": [".jpg", ".jpeg", ".png", ".bmp"],
    "video_extensions": [".mp4", ".avi", ".mov"]
}
```

### 3. 数据预处理脚本

创建 `preprocess_i2v_data.py`：

```python
import os
import json
import cv2
import numpy as np
from pathlib import Path
from PIL import Image

def preprocess_i2v_dataset(dataset_path):
    """预处理图像到视频数据集"""
    dataset_path = Path(dataset_path)
    image_folder = dataset_path / "images"
    video_folder = dataset_path / "videos"
    prompt_folder = dataset_path / "prompts"
    
    metadata = []
    
    # 遍历图像文件
    for image_file in image_folder.glob("*"):
        if image_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
            base_name = image_file.stem
            video_file = video_folder / f"{base_name}.mp4"
            prompt_file = prompt_folder / f"{base_name}.txt"
            
            if video_file.exists():
                # 读取图像信息
                image = Image.open(image_file)
                image_width, image_height = image.size
                
                # 读取视频信息
                cap = cv2.VideoCapture(str(video_file))
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                video_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                video_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                cap.release()
                
                # 读取提示词（可选）
                prompt = ""
                if prompt_file.exists():
                    with open(prompt_file, 'r', encoding='utf-8') as f:
                        prompt = f.read().strip()
                
                metadata.append({
                    "image_path": str(image_file.relative_to(dataset_path)),
                    "video_path": str(video_file.relative_to(dataset_path)),
                    "prompt": prompt,
                    "image_resolution": [image_width, image_height],
                    "video_resolution": [video_width, video_height],
                    "frame_count": frame_count,
                    "fps": fps
                })
    
    # 保存元数据
    with open(dataset_path / "metadata.json", 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    print(f"处理完成，共 {len(metadata)} 个图像-视频对")
    return metadata

def validate_dataset(dataset_path):
    """验证数据集完整性"""
    dataset_path = Path(dataset_path)
    
    with open(dataset_path / "metadata.json", 'r') as f:
        metadata = json.load(f)
    
    valid_samples = []
    for item in metadata:
        image_path = dataset_path / item["image_path"]
        video_path = dataset_path / item["video_path"]
        
        if image_path.exists() and video_path.exists():
            valid_samples.append(item)
        else:
            print(f"Warning: Missing files for {item['image_path']} or {item['video_path']}")
    
    print(f"验证完成，有效样本数: {len(valid_samples)}")
    return valid_samples

if __name__ == "__main__":
    dataset_path = "./dataset"
    preprocess_i2v_dataset(dataset_path)
    validate_dataset(dataset_path)
```

## 多卡训练配置

### 1. Accelerate配置文件

创建 `accelerate_config.yaml`：

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 4
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

### 2. 初始化Accelerate配置

```bash
# 配置accelerate
accelerate config --config_file accelerate_config.yaml

# 或者交互式配置
# accelerate config
```

## 微调代码

### 1. 训练脚本 `train_wan_i2v.py`

```python
import os
import json
import torch
import argparse
from pathlib import Path
from accelerate import Accelerator
from accelerate.utils import set_seed
from torch.utils.data import DataLoader
from transformers import get_scheduler
from diffsynth import ModelManager, WanVideoI2VPipeline
from PIL import Image
import cv2
import numpy as np
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class I2VDataset(torch.utils.data.Dataset):
    def __init__(self, dataset_path, resolution=(480, 480), frame_count=16):
        self.dataset_path = Path(dataset_path)
        self.resolution = resolution
        self.frame_count = frame_count
        
        # 加载元数据
        with open(self.dataset_path / "metadata.json", 'r') as f:
            self.metadata = json.load(f)
    
    def __len__(self):
        return len(self.metadata)
    
    def load_image(self, image_path):
        """加载和预处理图像"""
        image = Image.open(image_path).convert('RGB')
        image = image.resize(self.resolution, Image.LANCZOS)
        image_array = np.array(image).astype(np.float32) / 255.0
        image_tensor = torch.from_numpy(image_array).permute(2, 0, 1)
        return image_tensor
    
    def load_video(self, video_path):
        """加载和预处理视频"""
        cap = cv2.VideoCapture(str(video_path))
        frames = []
        
        frame_indices = np.linspace(0, cap.get(cv2.CAP_PROP_FRAME_COUNT) - 1, self.frame_count, dtype=int)
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if ret:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frame = cv2.resize(frame, self.resolution)
                frame = frame.astype(np.float32) / 255.0
                frames.append(frame)
        
        cap.release()
        
        if len(frames) < self.frame_count:
            # 如果帧数不足，重复最后一帧
            while len(frames) < self.frame_count:
                frames.append(frames[-1])
        
        video_tensor = torch.from_numpy(np.array(frames)).permute(3, 0, 1, 2)  # (C, T, H, W)
        return video_tensor
    
    def __getitem__(self, idx):
        item = self.metadata[idx]
        image_path = self.dataset_path / item["image_path"]
        video_path = self.dataset_path / item["video_path"]
        prompt = item.get("prompt", "")
        
        # 加载图像和视频
        image_tensor = self.load_image(image_path)
        video_tensor = self.load_video(video_path)
        
        return {
            "image": image_tensor,
            "video": video_tensor,
            "prompt": prompt,
        }

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_path", type=str, default="./models/Wan2.1-I2V-14B-480P")
    parser.add_argument("--dataset_path", type=str, default="./dataset")
    parser.add_argument("--output_dir", type=str, default="./output")
    parser.add_argument("--learning_rate", type=float, default=1e-5)
    parser.add_argument("--batch_size", type=int, default=1)
    parser.add_argument("--num_epochs", type=int, default=10)
    parser.add_argument("--gradient_accumulation_steps", type=int, default=4)
    parser.add_argument("--save_steps", type=int, default=500)
    parser.add_argument("--logging_steps", type=int, default=100)
    parser.add_argument("--mixed_precision", type=str, default="bf16")
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--resolution", type=int, nargs=2, default=[480, 480])
    parser.add_argument("--frame_count", type=int, default=16)
    args = parser.parse_args()
    
    # 初始化accelerator
    accelerator = Accelerator(
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        mixed_precision=args.mixed_precision,
        log_with="tensorboard",
        project_dir=args.output_dir,
    )
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载模型
    logger.info("Loading model...")
    model_manager = ModelManager(torch_dtype=torch.bfloat16, device=accelerator.device)
    model_manager.load_model("Wan-AI/Wan2.1-I2V-14B-480P", model_path=args.model_path)
    
    # 获取可训练的模型组件
    unet = model_manager.model["unet"]
    
    # 设置训练模式
    unet.train()
    
    # 创建数据集和数据加载器
    dataset = I2VDataset(
        args.dataset_path, 
        resolution=tuple(args.resolution),
        frame_count=args.frame_count
    )
    dataloader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    # 创建优化器
    optimizer = torch.optim.AdamW(
        unet.parameters(),
        lr=args.learning_rate,
        weight_decay=0.01
    )
    
    # 创建学习率调度器
    num_training_steps = len(dataloader) * args.num_epochs
    lr_scheduler = get_scheduler(
        "cosine",
        optimizer=optimizer,
        num_warmup_steps=100,
        num_training_steps=num_training_steps
    )
    
    # 使用accelerator准备模型、优化器和数据加载器
    unet, optimizer, dataloader, lr_scheduler = accelerator.prepare(
        unet, optimizer, dataloader, lr_scheduler
    )
    
    # 训练循环
    logger.info("Starting training...")
    global_step = 0
    
    for epoch in range(args.num_epochs):
        for step, batch in enumerate(dataloader):
            with accelerator.accumulate(unet):
                # 前向传播
                # 这里需要实现具体的损失计算逻辑
                # 包括噪声添加、时间步采样、条件编码等
                loss = torch.tensor(0.0, device=accelerator.device)  # 占位符
                
                # 反向传播
                accelerator.backward(loss)
                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()
            
            global_step += 1
            
            # 日志记录
            if global_step % args.logging_steps == 0:
                logger.info(f"Epoch {epoch}, Step {global_step}, Loss: {loss.item():.4f}")
                accelerator.log({"loss": loss.item(), "lr": lr_scheduler.get_last_lr()[0]})
            
            # 保存检查点
            if global_step % args.save_steps == 0:
                save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                accelerator.save_state(save_path)
                logger.info(f"Saved checkpoint to {save_path}")
    
    # 保存最终模型
    final_save_path = os.path.join(args.output_dir, "final_model")
    accelerator.save_state(final_save_path)
    logger.info(f"Training completed. Final model saved to {final_save_path}")

if __name__ == "__main__":
    main()
```

### 2. 训练启动脚本 `train_i2v.sh`

```bash
#!/bin/bash

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 启动多卡训练
accelerate launch \
    --config_file accelerate_config.yaml \
    train_wan_i2v.py \
    --model_path ./models/Wan2.1-I2V-14B-480P \
    --dataset_path ./dataset \
    --output_dir ./output/wan_i2v_finetune \
    --learning_rate 1e-5 \
    --batch_size 1 \
    --num_epochs 10 \
    --gradient_accumulation_steps 4 \
    --save_steps 500 \
    --logging_steps 100 \
    --mixed_precision bf16 \
    --seed 42 \
    --resolution 480 480 \
    --frame_count 16
```

## 推理代码

### 1. 推理脚本 `inference_wan_i2v.py`

```python
import torch
import argparse
from diffsynth import ModelManager, WanVideoI2VPipeline
from pathlib import Path
from PIL import Image

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_path", type=str, default="./models/Wan2.1-I2V-14B-480P")
    parser.add_argument("--checkpoint_path", type=str, default="./output/wan_i2v_finetune/final_model")
    parser.add_argument("--image_path", type=str, required=True)
    parser.add_argument("--prompt", type=str, default="")
    parser.add_argument("--output_path", type=str, default="./output_video.mp4")
    parser.add_argument("--num_frames", type=int, default=16)
    parser.add_argument("--height", type=int, default=480)
    parser.add_argument("--width", type=int, default=480)
    parser.add_argument("--num_inference_steps", type=int, default=50)
    parser.add_argument("--guidance_scale", type=float, default=7.5)
    parser.add_argument("--fps", type=int, default=8)
    args = parser.parse_args()
    
    # 检查输入图像是否存在
    if not Path(args.image_path).exists():
        raise FileNotFoundError(f"Input image not found: {args.image_path}")
    
    # 加载模型
    print("Loading model...")
    model_manager = ModelManager(torch_dtype=torch.bfloat16, device="cuda")
    
    # 加载基础模型
    model_manager.load_model("Wan-AI/Wan2.1-I2V-14B-480P", model_path=args.model_path)
    
    # 如果有微调的检查点，加载它
    if Path(args.checkpoint_path).exists():
        print(f"Loading fine-tuned checkpoint from {args.checkpoint_path}")
        # 这里需要实现检查点加载逻辑
    
    # 创建推理管道
    pipeline = WanVideoI2VPipeline.from_model_manager(model_manager)
    
    # 加载输入图像
    print(f"Loading input image: {args.image_path}")
    input_image = Image.open(args.image_path).convert('RGB')
    input_image = input_image.resize((args.width, args.height), Image.LANCZOS)
    
    # 生成视频
    print(f"Generating video from image...")
    if args.prompt:
        print(f"Using prompt: {args.prompt}")
    
    video = pipeline(
        image=input_image,
        prompt=args.prompt,
        num_frames=args.num_frames,
        height=args.height,
        width=args.width,
        num_inference_steps=args.num_inference_steps,
        guidance_scale=args.guidance_scale,
    )
    
    # 保存视频
    pipeline.save_video(video, args.output_path, fps=args.fps)
    print(f"Video saved to {args.output_path}")

if __name__ == "__main__":
    main()
```

### 2. 推理启动脚本 `inference_i2v.sh`

```bash
#!/bin/bash

python inference_wan_i2v.py \
    --model_path ./models/Wan2.1-I2V-14B-480P \
    --checkpoint_path ./output/wan_i2v_finetune/final_model \
    --image_path ./input_image.jpg \
    --prompt "The person in the image is walking forward" \
    --output_path ./generated_video.mp4 \
    --num_frames 16 \
    --height 480 \
    --width 480 \
    --num_inference_steps 50 \
    --guidance_scale 7.5 \
    --fps 8
```

## 模型合并代码

### 1. 模型合并脚本 `merge_i2v_model.py`

```python
import torch
import argparse
import os
from pathlib import Path
from diffsynth import ModelManager
import shutil

def merge_i2v_weights(base_model_path, checkpoint_path, output_path, alpha=1.0):
    """合并I2V模型权重"""
    print("Loading base I2V model...")
    model_manager = ModelManager(torch_dtype=torch.bfloat16, device="cpu")
    model_manager.load_model("Wan-AI/Wan2.1-I2V-14B-480P", model_path=base_model_path)
    
    # 加载微调权重
    if Path(checkpoint_path).exists():
        print(f"Loading fine-tuned weights from {checkpoint_path}")
        # 这里需要实现权重加载和合并逻辑
        # checkpoint = torch.load(checkpoint_path, map_location="cpu")
        # 合并权重到基础模型
    
    # 保存合并后的模型
    print(f"Saving merged I2V model to {output_path}")
    os.makedirs(output_path, exist_ok=True)
    
    # 复制配置文件
    config_files = ["config.json", "model_index.json", "scheduler_config.json"]
    for config_file in config_files:
        src_path = Path(base_model_path) / config_file
        dst_path = Path(output_path) / config_file
        if src_path.exists():
            shutil.copy2(src_path, dst_path)
    
    # 保存模型权重
    # model_manager.save_model(output_path)
    
    print("I2V model merging completed!")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--base_model_path", type=str, default="./models/Wan2.1-I2V-14B-480P")
    parser.add_argument("--checkpoint_path", type=str, default="./output/wan_i2v_finetune/final_model")
    parser.add_argument("--output_path", type=str, default="./models/Wan2.1-I2V-14B-480P-merged")
    parser.add_argument("--alpha", type=float, default=1.0, help="Merge alpha")
    args = parser.parse_args()
    
    merge_i2v_weights(
        base_model_path=args.base_model_path,
        checkpoint_path=args.checkpoint_path,
        output_path=args.output_path,
        alpha=args.alpha
    )

if __name__ == "__main__":
    main()
```

### 2. 合并启动脚本 `merge_i2v.sh`

```bash
#!/bin/bash

python merge_i2v_model.py \
    --base_model_path ./models/Wan2.1-I2V-14B-480P \
    --checkpoint_path ./output/wan_i2v_finetune/final_model \
    --output_path ./models/Wan2.1-I2V-14B-480P-merged \
    --alpha 1.0
```

## 训练监控和调试

### 1. 使用TensorBoard监控训练

```bash
# 启动TensorBoard
tensorboard --logdir ./output/wan_i2v_finetune --port 6006
```

### 2. 数据集验证脚本

创建 `validate_i2v_dataset.py`：

```python
import json
import cv2
from pathlib import Path
from PIL import Image

def validate_i2v_dataset(dataset_path):
    """验证I2V数据集"""
    dataset_path = Path(dataset_path)
    
    with open(dataset_path / "metadata.json", 'r') as f:
        metadata = json.load(f)
    
    print(f"总样本数: {len(metadata)}")
    
    valid_count = 0
    invalid_samples = []
    
    for i, item in enumerate(metadata):
        image_path = dataset_path / item["image_path"]
        video_path = dataset_path / item["video_path"]
        
        try:
            # 验证图像
            image = Image.open(image_path)
            image_width, image_height = image.size
            
            # 验证视频
            cap = cv2.VideoCapture(str(video_path))
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            cap.release()
            
            if frame_count > 0 and fps > 0:
                valid_count += 1
            else:
                invalid_samples.append(f"Sample {i}: Invalid video {video_path}")
                
        except Exception as e:
            invalid_samples.append(f"Sample {i}: Error {str(e)}")
    
    print(f"有效样本数: {valid_count}")
    print(f"无效样本数: {len(invalid_samples)}")
    
    if invalid_samples:
        print("无效样本详情:")
        for sample in invalid_samples[:10]:  # 只显示前10个
            print(f"  {sample}")

if __name__ == "__main__":
    validate_i2v_dataset("./dataset")
```

## 常见问题和解决方案

### 1. 图像-视频对齐问题

- 确保图像和视频的第一帧内容匹配
- 检查图像和视频的分辨率比例
- 验证数据集的标注质量

### 2. 显存优化

- 减少frame_count（如从16减少到8）
- 使用更小的分辨率进行训练
- 启用gradient_checkpointing

### 3. 训练稳定性

- 使用较小的学习率
- 增加warmup步数
- 检查数据预处理的正确性

## 性能优化建议

1. **数据预处理优化**：预先将视频解码为帧序列存储
2. **内存管理**：使用数据流水线减少内存占用
3. **I/O优化**：使用高速存储设备存放数据集
4. **模型优化**：使用LoRA等参数高效微调方法

## 总结

本文档提供了Wan-AI/Wan2.1-I2V-14B-480P模型多卡微调的完整流程，专门针对图像到视频生成任务。相比T2V模型，I2V模型需要处理图像条件输入，在数据准备和训练过程中需要特别注意图像-视频的对应关系和质量。

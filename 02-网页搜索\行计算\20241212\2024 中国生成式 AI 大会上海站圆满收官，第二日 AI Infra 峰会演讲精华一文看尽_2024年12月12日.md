﻿# 2024 中国生成式 AI 大会上海站圆满收官，第二日 AI Infra 峰会演讲精华一文看尽

**发布日期**: 2024年12月12日

**原文链接**: https://www.geekpark.net/news/344111

## 📄 原文内容

智东西 12 月 6 日报道，为期两天的 2024 中国生成式 AI 大会（上海站）今日圆满收官。

两天内， 51 位 产学研投嘉宾代表密集输出干货爆棚，大会报名咨询人数超 4000 人， 超过 1200 位 观众到场参会。其中，在主会场进行的大模型峰会、AI Infra 峰会的线上观看人次更是超过 104 万。

现场参会观众们的热情十分高涨，主会场、分会场座无虚席，展览区附近的产业交流也十分活跃， 15 家 企业的诸多新产品新技术都引起了广泛关注和讨论。

此次大会以 「智能跃进 创造无限」 为主题，51 位产学研投嘉宾代表基于前瞻性视角解构和把脉生成式 AI 的技术产品创新、商业落地解法、未来趋势走向与前沿研究焦点。

今天的 AI Infra 峰会 上，上海交通大学副教授、无问芯穹联合创始人兼首席科学家戴国浩认为，业界更应该关注单位算力如何实现更高效的 token 吞吐，大模型实际可用算力不仅取决于芯片理论算力，还可通过软硬协同优化提高算力利用效率，通过多元异构适配放大整体算力规模。

北电数智智算云负责人郭文，GMI Cloud 亚太区总裁 King.Cui，阿里云智算集群产品解决方案负责人丛培岩，中昊芯英芯片软件栈负责人朱国梁，光羽芯辰创始人兼董事长周强分别对全栈 AI 工厂、AI 企业出海如何补齐算力短板、高性能智算集群、国产 TPU 芯片「No CUDA」软件栈、通向个人大模型之路几个主题进行了分享。

枫清科技创始人兼 CEO 高雪峰，声网生成式 AI 产品负责人毛玉杰，腾讯云向量数据库技术负责人谢宇，Jina AI 联合创始人兼首席技术官王楠，Zilliz 合伙人、研发 VP 栾小凡，英飞流创始人兼 CEO 张颖峰，Alluxio 首席架构师傅正佳分别针对「从数据到知识：AI 重塑百行千业的基石」、「生成式 AI 驱动实时互动的技术变革与体验革新」、「TencentVDB 向量数据库」、「RAG 范式下 AI Infra 的机遇和挑战」、「RAG 虽强，但向量数据库绝非万灵药」、「新一代企业级多模态 RAG 引擎」、「高性能 AI 数据底座」带来了精彩演讲。

下午场的圆桌讨论聚焦「大模型行至深水区，AI Infra 的新变化与新机会」， 由德联资本执行董事刘景媛主持，Alluxio 首席架构师傅正佳，Zilliz 合伙人、研发 VP 栾小凡，英飞流创始人兼 CEO 张颖峰三位嘉宾给出了自己的真知灼见。

大会首日，17 位嘉宾畅谈大语言模型、多模态大模型、具身智能、AI 原生应用、音乐生成、3D AIGC、AI 智能体的行业应用、垂类行业大模型等前沿议题。（2024 中国生成式 AI 大会上海站开幕！首日大模型峰会燃爆魔都，17 位大咖密集输干货）

除了大会首日主会场进行的大模型峰会，以及今天主会场的 AI Infra 峰会，大会分会场也在这两天分别组织了端侧生成式 AI 技术研讨会、AI 视频生成技术研讨会与具身智能技术研讨会，17 位青年学者和技术专家带来了报告分享，后续将会上架这三场收费制研讨会的回放。

AI 的发展带来了巨大的数据、算力以及能源挑战，作为支撑大模型运行以及生成式 AI 应用开发的关键，AI Infra 也走到了台前，发展势头强劲。

如何打造优质的智算中心，如何实现 AI 从芯片到应用端全产业链的高效协同？多位嘉宾给出了自己的深入见解。

1、上海交通大学副教授、无问芯穹联合创始人兼首席科学家戴国浩

Scaling Law 之下，数据成为制约 AI 继续发展的因素之一。以 GPT-o1 为代表的推理模型可以突破数据瓶颈，但计算范式的转变使算力需求呈指数级增长，可能导致硬件系统能耗开销供不应求，对行业的可持续发展构成挑战。

对此，戴国浩教授指出，当下业界更应该关注单位算力如何实现更高效的 token 吞吐，让大模型的实际可用算力不仅取决于芯片理论算力，还可通过软硬协同优化提高算力利用效率，并通过多元异构适配放大整体算力规模。他分享了其研究团队在软硬协同、多元异构与端侧智能方面的研究进展与落地成果，这些成果能助力行业提升面向大模型场景的 token 吞吐效率。

2、北电数智郭文：以 AI 工厂填补国产算力供给侧与需求侧的产业链断层

「产业要发展，创新不能只是停留在技术层面，更要从流程、系统和组织进行全面的创新。」北电数智智算云负责人郭文分享了从算力、算法、数据与生态方面全面构建人工智能时代 AI 生产线的实践思考。

郭文称，当下国产芯片落地人工智能产业的最大问题是，算力供给侧与需求侧之间存在产业链断层。为此，北电数智推出首个「国产算力 PoC 平台」，以北京数字经济算力中心为载体打造具备全栈能力的 AI 工厂，全线适配与拉通场景、模型到芯片层面，推动智算中心从成本中心转化为推动地区发展新质生产力中心。

3、GMI Clould King.Cui：高稳定 GPU 集群成 AI 企业全球化布局关键

中国 AI 出海加速，算力作为其中的核心生产资料正发挥重要作用。高稳定性的 GPU 集群能降本增效，帮助企业在 AI 全球化浪潮中取胜。

GMI Cloud 亚太区总裁 King.Cui 提到，为确保 GPU 集群的高稳定性，他们使用了具备主动检测功能的自研云集群引擎，实现计算、存储和网络资源的高效调配。

GMI Cloud 是 NVIDIA Top10 NCP，交付前会进行严格的验证流程。GMI Cloud 与 IDC 协作，提供备件和维修，拥有更短的交付时间，确保停机时间最小化。

4、阿里云丛培岩：灵骏智算集群不仅要实现稳定性和极致性能，更要在不同维度支持规模的极致扩展

阿里云智算集群产品解决方案负责人丛培岩预测，未来模型性能还会随参数，数据集和算力的增长继续提升，Scaling Law 仍有增长空间，AI 智算集群的设计范式转向要以 GPU 为核心。

阿里云推出支持超大规模分布式训练的灵骏智算集群，可达到 10 万卡扩展规模，千卡规模线性加速比达到 96%；阿里云自研磐久服务器采用 CPU 和 GPU 分离，实现单机提升至 16 颗 GPU；网络架构 HPN7.0 最大规模可连接 10 万颗 GPU。

智算集群稳定性至关重要，阿里云 3 千卡规模智算集群，在一个月内稳定训练时长占比达 99%。

5、光羽芯辰周强：解决「大模型不懂你」问题，个人大模型迎来机遇

作为与通用大模型、行业大模型、企业大模型并行发展的一大分支，个人大模型也进入了快速发展期。光羽芯辰创始人兼董事长周强称，个人大模型解决的是「大模型不懂你」的问题，随着手机、PC、可穿戴、XR 等端侧设备厂商 All in AI，个人大模型之路将越走越宽。

他提到，个人大模型也称为端侧大模型，期待解决端侧智能体在性能、功耗和成本方面的痛点，让真正的 AI 手机走进生活。端侧 AI 具备及时性、可靠性、成本低、隐私保护和定制化五大优势。目前，构建端侧大模型的核心是解决存储带宽和容量双重问题。

6、中昊芯英朱国梁：国产 TPU 芯片「No CUDA」软件栈的构建实践

中昊芯英芯片软件栈负责人朱国梁介绍了他们在为国产 TPU 芯片构建「No CUDA」软件栈的实践经验。

中昊芯英刹那芯片采用 VLIW 指令集架构，面对庞大的 CUDA 生态，他们逐一解决了库、并行计算与编程方面的问题，全自研用户态和内核态驱动，实现了芯片的高效管理。

为做好生态兼容，中昊芯英底层软件栈兼容 PyTorch 以及所有主流训推框架，目前，中昊芯英可提供定制的端到端的云智算解决方案，并支持国产操作系统。

下午场，多位嘉宾进一步分享了 AI Infra 领域关于智能体开发管理平台、实时语音、向量数据库、向量模型、RAG 技术、数据编排等方面的行业观察和深入见解。诸多新平台、新产品、新技术走向前台，赋能产业。

1、枫清科技高雪峰：从数据到知识，跨越生成式 AI 与决策智能间的鸿沟

枫清科技创始人兼 CEO 高雪峰谈道，要将生成式 AI 真正应用到企业决策场景中，弥合其与决策智能之间鸿沟的技术突破点，就是在推理框架侧融合符号逻辑推理。

企业智能化落地需要面临数据孤岛、数据整合、知识校验、数据实时效等技术挑战。枫清科技可以为企业提供知识引擎与大模型双轮驱动的新一代智能体平台，通过构建全链路优化体系，帮助企业提升数据质量，将企业本地数据知识化，并融合大模型沉淀的泛化能力，在知识网络之上进行符号逻辑推理，实现可解释的智能，进而使 AI 在多个场景下能够实现精准、透明的决策支持，推动企业智能化转型的顺利实施。

2、声网毛玉杰：生成式 AI+实时互动，让人机交互变成真正的心灵交互

声网生成式 AI 产品负责人毛玉杰讲述了生成式 AI 出现后实时互动（RTE，Real-Time Engagement）技术和体验的变迁。

毛玉杰介绍，2014 年至今十年，RTE 从服务质量走向体验质量；2025 年开始，在生成式 AI 发展的背景下，RTE 向 AI RTE 变革，开始注重跨模态体验质量，做多模态交互、跨模态转换，为人和模型而设计，给大模型厂商提供眼睛、耳朵和声音能力。

毛玉杰说，目前人机对话已经达到「听得懂」的状态，期待下一步实现「听得心」——让人机交互变成真正的心灵交互。

3、腾讯云谢宇：向量数据库助力企业挖掘更大数据价值

AI 时代，向量数据库（VDB）脱颖而出，成为连接结构化与非结构化数据的枢纽。然而，当 VDB 被运用于 RAG 场景时，多款开源 RAG 架构出现了召回率低的问题。

腾讯云向量数据库技术负责人谢宇介绍，为解决上述挑战，腾讯首先提升了复杂文档的识别效果，并对数据处理、Embedding、检索、总结等其他环节进行优化，最终实现了 90% 以上的召回率。

腾讯自研向量检索引擎 OLAMA 已上线 5 年，日均处理 8500 亿次检索请求。未来，他们还将在性能、成本、业务效果、容灾率等方面发力，持续提升产品表现。

4、Jina AI 王楠：长文本大模型、RAG 长期共存，长窗口向量模型面临两大挑战

大模型存在幻觉、无法保证私有数据安全、推理成本高三大问题，Jina AI 联合创始人兼首席技术官王楠认为，RAG 正是通过缩小大模型生成范围，保证检索准确性、实现结果可溯源，所以长本文大模型不会取代 RAG，二者将长期共存。

短窗口会导致上下文背景信息丢失，因此 RAG 需要长窗口向量模型支持。但长窗口向量模型面临两大挑战，一是推理成本和内存消耗会随窗口长度呈平方线性增长，共享 GPU 是解决思路之一；二是长窗口使模型无法完整表示细颗粒度语义，解法是增加向量维度和多向量表示。

5、Zilliz 栾小凡：向量数据库落地面临成本及扩展性挑战，RAG 转为 Graph RAG

Zilliz 合伙人、研发 VP 栾小凡分享了向量数据库目前面临的挑战以及相应解决方案。

栾小凡称，2025 年新生成的数据中，将会有 80% 以上是非结构化数据。在这一数据压力下，向量数据库的落地面临着成本以及扩展性等方面的种种挑战。而目前的 RAG 存在搜索质量难、处理长尾查询能力差、结果难以解释和控制、向量存储成本高等问题。

据此，栾小凡及其团队提出了两个解决思路：一是混合查询，在单个系统内支持密集嵌入、稀疏嵌入和词汇搜索；二是 Graph RAG，将知识图谱和向量检索结合起来。

6、英飞流张颖峰：多模态 RAG 新范式

英飞流创始人兼 CEO 张颖峰认为，RAG 作为 LLM 时代的数据库，目前面临着三大挑战——多模态文档处理、检索、语义鸿沟。

针对第一个问题，英飞流训练了深度文档理解模型，能对复杂文档中的多模态内容进行分类处理。而在检索这一 RAG「最后一公里」的问题上，英飞流使用三路召回方案，并增加张量索引进行重排序，这一方案在多模态 RAG 上展现出明显优势。

最后，针对检索过程中的语义鸿沟，英飞流使用 GraphRAG 抽取知识图谱，并与原数据进行联合检索，提升检索质量。

7、Alluxio 傅正佳：零改造、无侵入策略，打造高性能 AI 数据底座

Alluxio 首席架构师傅正佳谈到了提升大规模模型训练效率的两大挑战：一是数据规模不断增长、类型更多元化，因此处理数据需要提升算力有效利用率；二是当数据喂到训练平台上，数据 IO 访问瓶颈会导致算力处于低利用率状态。

这一背景下，Alluxio 提供了统一的数据视图、丰富协议转化、高性能数据访问，以打造整体数据服务。其方案通过零改造、无侵入策略，可以使算法工程师仍按原有方式工作，无需改变已有脚本，并且客户已经有的大量存量数据不需要进行私有化协议改造。

在圆桌论坛环节，几位嘉宾分享了对于「大模型行至深水区，AI Infra 的新变化与新机会」这一主题的行业洞察，以及各自公司的产品和技术是如何解决 AI 应用中的核心痛点的。

作为主持人的德联资本执行董事刘景媛提到，两年前，ChatGPT 将生成式 AI 推到台前，迎来 AI 2.0 时代，Scaling Law 和数据量的大规模增长给 AI Infra 带来了非常大的增量机会。两年后的今天大模型行至深水区，AI Infra 在帮助大模型及相关产品的落地的过程中，产品边界和功能需求逐渐明晰。

对于 Infra 这类研发周期长、工程复杂程度高的软件产品，开源社区或许可以贡献一些能量，使产品迭代及技术选型更贴合实际需求，同时提升项目本身的关注度和影响力。

另外，「go global」也几乎成为 Infra 软件的必选项，一方面有商业的考量，另外中国工程师的勤奋和工程攻坚能力全球有目共睹。值得关注的是，在资源有限的情况下也要做好取舍（无论是功能方面还是业务模式方面）。

Zilliz 作为向量数据库企业，其产品可以处理大体量非结构化数据，挖掘数据价值。对 AI 2.0 时代的需求变化，Zilliz 合伙人、研发 VP 栾小凡认为，AI 技术在去年被高估、今年被低估，往后看 AI 落地还需要等一个机会，这也是整个范式的发展机会。

谈到开源，栾小凡感慨道，Zilliz 目前正处于最具挑战的阶段，一方面要让产品满足客户需求，另一方面要让产品变现。

当下，AI Infra 公司出海已经成为必答题。栾小凡认为出海的前提条件就是产品要有先发优势，在扩展性、功能等方面碾压竞品。产品定制方面，栾小凡的观点是 Zilliz 几乎不做定制。原因在于其所处的赛道已经足够大，没有必要执着于将自己打造成大而全的平台。

AI 时代，数据量的暴增对存储提出巨大挑战。Alluxio 首席架构师傅正佳介绍，他们通过分布式数据编排软件系统，高效连接存储与计算。Alluxio 很早就注意到存算分离的趋势，并在数据远程访问环节重点发力，回应了 AI 存储挑战。

Alluxio 的存储系统兼具开闭源版本，傅正佳认为开源帮助他们保持了与技术前沿的同步，也打出了知名度，但他们也面临着商业化和部分开源用户贡献程度低的问题。Alluxio 目前正积极出海，傅正佳分享，海内外团队的优势互补与产品的本地化是其中的关键。

英飞流创始人兼 CEO 张颖峰称，RAG 用起来很容易，但做好非常困难。公司能做成 RAG 的核心在于，把做系统的人和做 AI 的人融合在了一起去做产品。

谈及开源，张颖峰说，开源是商业化的一种策略，而不是为了开源而开源；为了出海必须开源，但创业第一天就要想明白产品企业版和开发者版之间的区别。

目前英飞流的 Infra 产品还没有进入商业化阶段，结合过往创业经历，张颖峰称，商业化过程中，创始人必须对每个产品的特性和定制化的边界有非常清晰的认识。

过去一年，生成式 AI 的发展度过了波澜壮阔的一年，整个产业链成为全球创新、投资和应用最活跃的领域之一，每位参与者都在与时间赛跑。

Sora 掀起视频生成热潮，多模态世界模型的研究热度渐起。更具革命性的推理模型 o1 悄然出世，基座大语言模型不再持续狂飙，不仅价格战、营销战硝烟燃起，融资热度降温，Scaling Law 是否撞墙更是在年底引发热议。

行业赋能持续进行，包括智能体在内的应用层的兴起仍然备受期待。同时，大模型向边端下沉的趋势日趋明显，AI 手机、AI PC 等 AI 硬件纷纷站上风口。不止 AI 硬件，大模型驱动下的具身智能更是热度空前，人形机器人正开启星辰大海。

作为智能产业的长期观察者，我们期待见证并记录中国生成式 AI 浪潮之变，并将持续邀请这股浪潮中的生力军们，分享他们最新的技术进展与商业化探索。

随着今日为期两天的 2024 中国生成式 AI 大会（上海站）圆满收官。2025 年线下大会也将正式启动，除了 1 月 14 日的全球自动驾驶峰会，围绕 AI 芯片、生成式 AI 等领域的线下大会也已规划上了，敬请期待。
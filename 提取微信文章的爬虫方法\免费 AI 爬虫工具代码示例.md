# 免费 AI 爬虫工具代码示例

根据您的要求，以下是两个开源且可本地部署的 AI 爬虫工具：Crawl4AI 和 Scrapegraph-ai 的详细使用代码示例和中文注释。这些工具不依赖于付费 API，可以实现更灵活和免费的爬取。

---




## Crawl4AI

Crawl4AI 是一个开源的、LLM友好的Web爬虫和刮取工具，旨在简化网页抓取并轻松从网页中提取有价值的信息。它是一个Python库，支持异步操作，并能通过LLM理解网页结构进行数据提取。

### 安装

首先，确保您的 Python 环境已准备就绪。然后，通过 pip 安装 Crawl4AI 库及其依赖：

```bash
pip install crawl4ai
playwright install
```

`playwright install` 用于安装 Playwright 浏览器，Crawl4AI 依赖它来模拟浏览器行为。

### 示例代码：`crawl4ai_example.py`

以下是 `crawl4ai_example.py` 的完整代码，包含了基本网页刮取、使用 CSS 选择器提取结构化数据以及使用 LLM 提取数据的示例。请注意，LLM 提取部分需要您自行配置 LLM 服务（如 Ollama 或 OpenAI API）。

```python



# crawl4ai_example.py

# 导入必要的库
import asyncio
import json
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, LLMConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy, LLMExtractionStrategy

# --- 示例 1: 基本网页刮取并生成 Markdown ---
print("\n--- 示例 1: 基本网页刮取并生成 Markdown ---")

async def basic_scrape():
    # 初始化 AsyncWebCrawler
    # 可以通过 BrowserConfig 配置浏览器行为，例如是否无头模式 (headless)
    browser_conf = BrowserConfig(headless=True) # headless=True 表示无头模式，不显示浏览器界面
    
    # 通过 CrawlerRunConfig 配置爬虫运行行为，例如缓存模式
    # CacheMode.BYPASS 表示不使用缓存，每次都重新抓取最新内容
    run_conf = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS
    )

    async with AsyncWebCrawler(config=browser_conf) as crawler:
        # 定义要刮取的 URL
        url_to_scrape = "https://example.com"
        print(f"正在刮取 URL: {url_to_scrape}")
        
        # 执行刮取任务
        result = await crawler.arun(
            url=url_to_scrape,
            config=run_conf
        )
        
        # 打印刮取到的 Markdown 内容的前 500 个字符
        print("\n--- 刮取到的 Markdown 内容 (部分) ---")
        print(result.markdown[:500])

# 运行基本刮取示例
asyncio.run(basic_scrape())

# --- 示例 2: 使用 CSS 选择器提取结构化数据 ---
print("\n--- 示例 2: 使用 CSS 选择器提取结构化数据 ---")

async def css_extraction():
    # 定义一个 HTML 片段作为示例数据
    # 在实际应用中，这里会是刮取到的网页 HTML 内容
    raw_html = """
    <div class=\'product-list\'>
        <div class=\'product-item\'>
            <h2>商品 A</h2>
            <span class=\'price\'>$100</span>
            <a href=\'/product/A\'>查看详情</a>
        </div>
        <div class=\'product-item\'>
            <h2>商品 B</h2>
            <span class=\'price\'>$200</span>
            <a href=\'/product/B\'>查看详情</a>
        </div>
    </div>
    """

    # 定义提取数据的 Schema
    # baseSelector: 定义要查找的父元素
    # fields: 定义要从父元素中提取的字段，包括字段名、选择器和类型
    schema = {
        "name": "商品列表",
        "baseSelector": "div.product-item", # 匹配每个商品项
        "fields": [
            {"name": "title", "selector": "h2", "type": "text"}, # 提取 h2 标签的文本作为标题
            {"name": "price", "selector": "span.price", "type": "text"}, # 提取 class 为 price 的 span 标签文本作为价格
            {"name": "link", "selector": "a", "type": "attribute", "attribute": "href"} # 提取 a 标签的 href 属性作为链接
        ]
    }

    # 创建 JsonCssExtractionStrategy 实例
    extraction_strategy = JsonCssExtractionStrategy(schema)

    # 配置爬虫运行，使用 BYPASS 缓存模式和定义的提取策略
    run_conf = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        extraction_strategy=extraction_strategy
    )

    async with AsyncWebCrawler() as crawler:
        # 使用 \'raw://\' 前缀来处理原始 HTML 字符串，而不是 URL
        print("正在使用 CSS 选择器从原始 HTML 中提取数据...")
        result = await crawler.arun(
            url="raw://" + raw_html,
            config=run_conf
        )
        
        # 提取到的 JSON 数据存储在 extracted_content 中
        if result.extracted_content:
            extracted_data = json.loads(result.extracted_content)
            print("\n--- 提取到的结构化数据 ---")
            print(json.dumps(extracted_data, indent=2, ensure_ascii=False))
        else:
            print("未提取到数据。")

# 运行 CSS 提取示例
asyncio.run(css_extraction())

# --- 示例 3: 使用 LLM 提取数据 (需要配置 LLM) ---
print("\n--- 示例 3: 使用 LLM 提取数据 (需要配置 LLM) ---")

async def llm_extraction():
    # 定义要刮取的 URL
    url_to_scrape = "https://perinim.github.io/projects"
    print(f"正在使用 LLM 从 URL: {url_to_scrape} 中提取数据...")

    # 配置 LLM
    # 这里以 Ollama 为例，你需要确保 Ollama 服务正在运行并已拉取 \'mistral\' 模型
    # 如果使用 OpenAI 或其他模型，请相应修改 model 和 api_key
    llm_config = LLMConfig(
        provider="ollama/mistral", # 使用 Ollama 的 mistral 模型
        temperature=0,
        format="json", # 指定 LLM 输出格式为 JSON
        base_url="http://localhost:11434" # Ollama 服务的地址
    )

    # 定义 LLM 提取策略
    # prompt: 告诉 LLM 你想提取什么信息
    extraction_strategy = LLMExtractionStrategy(
        llm_config=llm_config,
        prompt="List me all the projects with their description and the author."
    )

    # 配置爬虫运行，使用 BYPASS 缓存模式和 LLM 提取策略
    run_conf = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        extraction_strategy=extraction_strategy
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url=url_to_scrape,
            config=run_conf
        )
        
        if result.extracted_content:
            extracted_data = json.loads(result.extracted_content)
            print("\n--- 提取到的结构化数据 (LLM) ---")
            print(json.dumps(extracted_data, indent=2, ensure_ascii=False))
        else:
            print("未提取到数据。请检查 LLM 配置和网络连接。")

# 运行 LLM 提取示例
# 注意：运行此示例前，请确保你的 Ollama 服务已启动并下载了 \'mistral\' 模型
# 如果没有 Ollama，可以注释掉此行，或修改为其他可用的 LLM 配置
# asyncio.run(llm_extraction())

print("\nCrawl4AI 示例执行完毕。")

```

---




## Scrapegraph-ai

Scrapegraph-ai 是一个利用 LLM 和图（Graph）结构来执行网络爬取的 Python 库。它允许用户通过定义一个包含不同节点（如“抓取页面”、“生成抓取逻辑”、“解析数据”）的图来构建爬取流程，并可以利用 LLM 根据自然语言提示生成抓取逻辑。

### 安装

首先，确保您的 Python 环境已准备就绪。然后，通过 pip 安装 Scrapegraph-ai 库及其依赖：

```bash
pip install scrapegraphai
playwright install
```

`playwright install` 用于安装 Playwright 浏览器，Scrapegraph-ai 依赖它来模拟浏览器行为。

### 示例代码：`scrapegraphai_example.py`

以下是 `scrapegraphai_example.py` 的完整代码，包含了使用 SmartScraperGraph 刮取网页内容（Ollama 本地模型和 OpenAI 模型）的示例。请注意，OpenAI 示例需要您配置有效的 OpenAI API 密钥。

```python



# scrapegraphai_example.py

# 导入必要的库
from scrapegraphai.graphs import SmartScraperGraph
import os
import json

# --- 示例 1: 使用 SmartScraperGraph 刮取网页内容 (Ollama 本地模型) ---
print("\n--- 示例 1: 使用 SmartScraperGraph 刮取网页内容 (Ollama 本地模型) ---")

# 配置图的参数，特别是 LLM (大型语言模型) 的设置
# 这里使用 Ollama 作为本地 LLM 服务
# 确保你的 Ollama 服务正在运行，并且已经拉取了指定的模型 (例如 \'mistral\')
# 例如，在终端运行：ollama run mistral
graph_config_ollama = {
    "llm": {
        "model": "ollama/mistral",  # 指定 Ollama 中的模型名称
        "temperature": 0,           # 设置生成文本的随机性，0 表示确定性最高
        "format": "json",           # 指定 LLM 输出格式为 JSON
        "base_url": "http://localhost:11434", # Ollama 服务的默认地址
    },
    "embeddings": {
        "model": "ollama/nomic-embed-text", # 用于生成文本嵌入的模型
        "base_url": "http://localhost:11434",
    }
}

# 定义要刮取的网页 URL
url_to_scrape_ollama = "https://perinim.github.io/projects"

# 定义用户提示，告诉 LLM 你想从网页中提取什么信息
prompt_ollama = "List me all the articles with their title and description."

print(f"正在使用 Scrapegraph-ai (Ollama) 刮取 URL: {url_to_scrape_ollama}")
print(f"提取提示: {prompt_ollama}")

try:
    # 创建 SmartScraperGraph 实例
    # prompt: 用户提示
    # source: 要刮取的 URL 或 HTML 内容
    # config: 图的配置，包括 LLM 设置
    smart_scraper_graph_ollama = SmartScraperGraph(
        prompt=prompt_ollama,
        source=url_to_scrape_ollama,
        config=graph_config_ollama
    )

    # 运行刮取任务
    result_ollama = smart_scraper_graph_ollama.run()

    # 打印刮取结果
    # 结果通常是一个字典，包含提取到的结构化数据
    print("\n--- 刮取到的内容 (Ollama) ---")
    print(json.dumps(result_ollama, indent=2, ensure_ascii=False))

except Exception as e:
    print(f"Scrapegraph-ai (Ollama) 刮取失败: {e}")
    print("请确保 Ollama 服务正在运行，并且已拉取 \'mistral\' 和 \'nomic-embed-text\' 模型。")

# --- 示例 2: 使用 SmartScraperGraph 刮取网页内容 (OpenAI 模型) ---
print("\n--- 示例 2: 使用 SmartScraperGraph 刮取网页内容 (OpenAI 模型) ---")

# 设置你的 OpenAI API 密钥
# 建议将 API 密钥设置为环境变量，以保证安全性
# 例如：os.environ.get("OPENAI_API_KEY")
OPENAI_API_KEY = "YOUR_OPENAI_API_KEY" # 请替换为你的实际 API 密钥

# 配置图的参数，特别是 LLM 的设置
# 这里使用 OpenAI 的 GPT-3.5-turbo 模型
graph_config_openai = {
    "llm": {
        "api_key": OPENAI_API_KEY, # OpenAI API 密钥
        "model": "gpt-3.5-turbo",  # 指定 OpenAI 模型名称
        "temperature": 0,
    },
}

# 定义要刮取的网页 URL
url_to_scrape_openai = "https://perinim.github.io/projects"

# 定义用户提示
prompt_openai = "List me all the projects with their description and the author."

print(f"正在使用 Scrapegraph-ai (OpenAI) 刮取 URL: {url_to_scrape_openai}")
print(f"提取提示: {prompt_openai}")

# 检查 OpenAI API 密钥是否已设置
if OPENAI_API_KEY == "YOUR_OPENAI_API_KEY":
    print("警告: 未设置 OpenAI API 密钥。跳过 OpenAI 示例。")
else:
    try:
        # 创建 SmartScraperGraph 实例
        smart_scraper_graph_openai = SmartScraperGraph(
            prompt=prompt_openai,
            source=url_to_scrape_openai,
            config=graph_config_openai
        )

        # 运行刮取任务
        result_openai = smart_scraper_graph_openai.run()

        # 打印刮取结果
        print("\n--- 刮取到的内容 (OpenAI) ---")
        print(json.dumps(result_openai, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"Scrapegraph-ai (OpenAI) 刮取失败: {e}")
        print("请检查 OpenAI API 密钥是否有效，或网络连接是否正常。")

print("\nScrapegraph-ai 示例执行完毕。")

```

---





### 使用 DeepSeek API 集成 (通过 LiteLLM)

Scrapegraph-ai 可以通过 `litellm` 库集成 DeepSeek API。`litellm` 作为一个统一的 LLM API 接口，使得在不同 LLM 之间切换变得更加容易。

**注意**：在运行此示例之前，请确保您已安装 `litellm`：`pip install litellm`。

```python
# scrapegraphai_deepseek_example.py

# 导入必要的库
from scrapegraphai.graphs import SmartScraperGraph
import os
import json
from litellm import completion

# --- 示例: 使用 SmartScraperGraph 刮取网页内容 (DeepSeek 模型) ---
print("\n--- 示例: 使用 SmartScraperGraph 刮取网页内容 (DeepSeek 模型) ---")

# 设置你的 DeepSeek API 密钥
# 建议将 API 密钥设置为环境变量，以保证安全性
# 例如：os.environ.get("DEEPSEEK_API_KEY")
DEEPSEEK_API_KEY = "sk-lBsjhuzN4GriMRqfD45744C34dDa4e39922bCfC9A911Cf1d" # 请替换为你的实际 API 密钥

# 配置图的参数，特别是 LLM (大型语言模型) 的设置
# 这里使用 litellm 作为 DeepSeek 的接口
graph_config_deepseek = {
    "llm": {
        "model": "deepseek/DeepSeek-R1-0528",  # litellm 中 DeepSeek 模型的表示方式
        "api_key": DEEPSEEK_API_KEY, # DeepSeek API 密钥
        "temperature": 0.7,          # 设置生成文本的随机性
        "base_url": "https://api.edgefn.net/v1", # DeepSeek API 的基础 URL
    },
    "embeddings": {
        # DeepSeek 目前没有官方的嵌入模型，这里可以暂时使用一个通用的嵌入模型
        # 或者根据实际情况选择其他嵌入服务
        "model": "ollama/nomic-embed-text", # 示例使用 Ollama 的嵌入模型
        "base_url": "http://localhost:11434", # Ollama 服务的地址
    }
}

# 定义要刮取的网页 URL
url_to_scrape_deepseek = "https://perinim.github.io/projects"

# 定义用户提示，告诉 LLM 你想从网页中提取什么信息
prompt_deepseek = "List me all the projects with their description and the author."

print(f"正在使用 Scrapegraph-ai (DeepSeek) 刮取 URL: {url_to_scrape_deepseek}")
print(f"提取提示: {prompt_deepseek}")

# 检查 DeepSeek API 密钥是否已设置
if DEEPSEEK_API_KEY == "YOUR_DEEPSEEK_API_KEY":
    print("警告: 未设置 DeepSeek API 密钥。跳过 DeepSeek 示例。")
else:
    try:
        # 创建 SmartScraperGraph 实例
        smart_scraper_graph_deepseek = SmartScraperGraph(
            prompt=prompt_deepseek,
            source=url_to_scrape_deepseek,
            config=graph_config_deepseek
        )

        # 运行刮取任务
        result_deepseek = smart_scraper_graph_deepseek.run()

        # 打印刮取结果
        print("\n--- 刮取到的内容 (DeepSeek) ---")
        print(json.dumps(result_deepseek, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"Scrapegraph-ai (DeepSeek) 刮取失败: {e}")
        print("请检查 DeepSeek API 密钥是否有效，或网络连接是否正常。")

print("\nScrapegraph-ai DeepSeek 示例执行完毕。")

```

**运行结果说明**：

在运行此示例时，您可能会看到类似以下警告信息：

```
Max input tokens for model deepseek/DeepSeek-R1-0528 not found,
                    please specify the model_tokens parameter in the llm section of the graph configuration.
                    Using default token size: 8192
```

这个警告表示 `scrapegraphai` 无法自动获取 DeepSeek 模型的最大输入 token 数量，并建议您在配置中手动指定 `model_tokens`。尽管有此警告，但示例代码仍然能够成功运行并从网页中提取数据，这表明 DeepSeek API 已成功集成。如果您希望消除此警告，可以在 `graph_config_deepseek` 的 `llm` 部分添加 `"model_tokens": 8192` (或 DeepSeek-R1-0528 模型的实际最大 token 数量)。

---




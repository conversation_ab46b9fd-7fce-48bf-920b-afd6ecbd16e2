﻿# 九章智算云Alaya NeW Cloud 2.0发布，启动全球首个强化学习云平台_商业要闻_财经_中金在线

**发布日期**: 2025年06月17日

**原文链接**: http://news.cnfol.com/shangyeyaowen/20250617/31394596.shtml

## 📄 原文内容

        padding-left: 40px;
        height: 40px;
        vertical-align: middle;
        border-right: 2px solid #e8e8e8;
        line-height: 40px;
        color: #000 !important;
        display: inline-block;
        margin-bottom: 1px;
        background:url('https://hs.cnfol.com/uk/Imgs/Reception/wxmini.png') no-repeat 10px center/25px 25px;
var url = document.location.href;
                            document.writeln('<input type="hidden" name="return" value="'+url+'" />');
document.domain="cnfol.com"
.NewBtnList{text-align: center;color:#fff;}
        .NewBtnList a{width: 150px;height: 46px;display: inline-block;line-height: 46px;text-indent: 30px;font-size: 14px;}
        .NewBtnList a:visited{color:#fff!important;}
        .NewBtnList .SettledIn{background:#3484df url(https://hs.cnfol.com/uk/Imgs/Reception/SettledIn.png) no-repeat 30px center;}
        .NewBtnList .pay{background:#3484df url(https://hs.cnfol.com/uk/Imgs/Reception/pay.png) no-repeat 30px center;}
热点精选：创新药+脑机接口+稳定币+智能眼镜
九章智算云Alaya NeW Cloud 2.0发布，启动全球首个强化学习云平台
2025年6月16日北京讯‌——AI独角兽企业九章云极DataCanvas在“九章云极智能计算论坛”上正式发布新一代全栈智能计算云平台——九章智算云Alaya NeW Cloud 2.0，并同步启动全球首个强化学习智算服务。该平台基于Serverless技术架构与强化学习技术的深度融合，成功突破“秒级生成百万token级”的性能瓶颈，旨在为全球AI创新企业及研发机构提供智能计算基础设施级服务。
九章智算云平台Alaya NeW Cloud 2.0专注于计算密集型应用，创新性地提供高度融合的智能计算基础设施(AI Infra)与低门槛工具链(Tools)。实测数据显示，平台可实现万卡级至十万卡级规模的异构算力统一调度；针对MoE模型架构，推理优化效率提升数倍；支持用户通过单行代码操作即可完成分布式工作负载编排；独创的“按实际资源消耗精准计量计费”的创新计价模型，显著降低了用户使用成本与应用门槛。
九章云极DataCanvas公司董事长方磊表示：“从移动互联网‘带宽式应用’到AI时代‘计算密集型应用’的结构性变革，亟需新型云架构支撑。九章智算云Alaya NeW Cloud 2.0通过‘高度融合的高密度AI Infra + 低门槛工具链Tools’的范式重构，为智算时代提供全栈智算方案。”
人工智能发展所催生的智能算力，不同于通用算力，不仅需要超大规模智算池的支撑，还需具备超越通用算力的超高计算效率，并深刻理解AI工作负载的高密度计算与固定性特征。九章智算云Alaya NeW Cloud 2.0深度融合算力基础设施与大模型生态，重新定义企业获取、使用和管理智能计算资源的方式，凭借低门槛、高性价比、高弹性、大规模高密度并发计算等显著优势，成为推进AI普惠的核心智算基座。
在AI基础设施架构层面，针对AI工作负载的固定性特征，九章智算云Alaya NeW Cloud 2.0创新性地采用Serverless技术架构替代传统虚拟化(Virtualization)模式，构建统一、高效、弹性可扩展的底座，实现算力资源的最大化复用。此举推动算力调度从“配置机器”向“提交任务”的范式转变，使AI开发者无需关注底层算力资源调度，得以专注于业务逻辑与模型调用。
基于Serverless技术的九章智算云平台，完成了从底层基础设施到上层应用的全栈优化。实测结果显示，平台支持跨AIDC的弹性资源调度，实现秒级响应与无限扩展；支持弹性伸缩，自动完成环境配置、策略加载与任务监控，使得端到端性能提升5倍；其定价策略摒弃了传统裸金属租赁方式，独创的“按度计费”模式可降低总拥有成本(TCO)达60%，让更多企业和开发者能够轻松拥抱AI算力。
面向AI业务，九章智算云Alaya NeW Cloud 2.0发布一系列低门槛智算工具链，覆盖大模型从预训练、精调直至适配与应用开发的ModelOps全生命周期，大幅降低模型开发与应用的技术壁垒。通过这些工具链，用户无需掌握复杂的GPU配置和集群管理技术，仅需明确数据来源、选定模型基座、确定优化方向，系统即可自动编排计算流程，真正实现业务人员轻松驾驭AI算力。
基于混合专家(MoE)架构，九章云极同期发布了九章强化学习云平台AgentiCTRL，全球首创性地将强化学习能力深度融入基础设施，显著增强大模型推理能力，将AI智能体训推门槛压缩至一行代码。与传统强化学习方案相比，该平台端到端训练效率提升500%，综合成本下降60%，成为全球首个支持万卡级异构算力调度的强化学习基础设施平台。
九章智算云Alaya NeW Cloud 2.0具备典型的新云特征，从底层基础设施到上层工具链全方位服务于智算需求。其以Serverless为特征的底层基础设施，与面向强化学习和未来算法的工具链深度耦合，将强化学习、Serverless架构等核心技术模块化，形成可弹性组合的智能计算服务矩阵，支持十万卡级异构资源调度，实现毫秒级响应延迟。这种创新的“基础设施即服务”模式，有效地推动AI应用走向规模化落地。
面向AI规模化应用：九章智算云打造普惠算力的中国方案
智能算力市场正经历结构性增长，驱动力主要来自三个维度：首先，大模型技术演进催生新型基础设施需求；其次，企业级大模型的普及将带动智能算力采购量级跃升；第三，智能体(Agent)技术的规模化应用带来了新的高端算力消耗模式。新一代智算云服务商必须构建更高效的算力供给体系，以降低算力使用成本，从而推动AI规模化应用向普惠方向发展。
九章智算云Alaya NeW Cloud 2.0突破传统“租用GPU”模式，围绕Agent开发一系列工具，以云生态方式致力于推进算力普惠的中国方案。为实现智能体快速普惠的发展目标，九章云极DataCanvas正从四个层面构建完整生态体系。
在计费模式创新层面，九章智算云Alaya NeW Cloud推出按需计费的“一度算力”计量标准，以“按量计费”模式重塑算力消费形态。
在底层架构革新层面，九章智算云Alaya NeW Cloud 2.0通过Serverless架构将GPU资源池化，让算力像水电般按需取用——千卡级训练任务与十卡级微调需求共享同一资源池，使用成本较传统方案直降45%。这种变革精准解决了AI产业的核心痛点：IDC数据显示，国内百万AI开发者中，83%受限于算力成本无法开展模型训练。
加快全球化算力网络布局，通过规模化AIDC节点建设，九章智算云构建起覆盖全球的算力供给网络。国内以北京、山东、安徽、云南等为核心节点；并积极拓展海外市场，构建起支持海量并发的全球智算服务体系，为全球用户提供就近使用的高性能智算服务。
在工程化能力与生态协同方面，智算云服务商不仅需提供高弹性算力供给，更要通过完善生态体系、优化工具链及推动开放性行业协同，实现算力向智能成果的高效转化。九章云极DataCanvas将开放生态建设作为长期战略，与各行业龙头企业展开深度合作，推动智能化应用在不同领域的规模化落地。
作为中国AI基础设施领域的重要力量，九章云极DataCanvas将持续加大研发投入，推动AI技术的普及应用。九章智算云Alaya NeW Cloud 2.0的发布，标志着中国在AI云计算领域迈向了新的技术高度。同时，能否为数千万开发者提供普惠算力服务，将成为决定AI云平台竞争力的重要维度。正如九章云极DataCanvas董事长方磊所言：" AI行业的竞争本质，是技术预见力的较量。"这场关乎未来的竞赛，此刻每一种技术路径探索、每一次商业模式创新，都可能成为改变历史的关键力量。
本站所有文章、数据仅供参考，使用前务请仔细阅读
《电子公告服务许可证》编号：闽通信互联网 [2008]1 号
《网络文化经营许可证》编号：闽网文[2017]6399130号
信息网络传播视听节目许可证1310422号
广播电视节目制作经营许可证 编号：(闽)字第091号
闽公网安备 35010002000101号
var cnzz_protocol = (("https:" == document.location.protocol) ? " https://" : " http://");document.write(unescape("%3Cspan id='cnzz_stat_icon_1253240157'%3E%3C/span%3E%3Cscript src='" + cnzz_protocol + "w.cnzz.com/q_stat.php%3Fid%3D1253240157' type='text/javascript'%3E%3C/script%3E"));
var _bdhmProtocol = (("https:" == document.location.protocol) ? " https://" : " http://");
  document.write(unescape("%3Cscript src='" + _bdhmProtocol + "hm.baidu.com/h.js%3Fc378c4854ec370c1c8438f72e19b7170' type='text/javascript'%3E%3C/script%3E"));
    var bp = document.createElement('script');
    bp.src = '//push.zhanzhang.baidu.com/push.js';
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(bp, s);
.CjhHotLstArt li, .CjhCjhLstArt li {float: left;padding-top: 10px; height: 46px;width: 100%;}
var userid = Tn.GetCookie('cookie[passport][userId]');
    var username = Tn.GetCookie('cookie[passport][nickname]');
        if(Tn.GetCookie('cookie[passport][userId]')){
            $('#LoginOn').css('display','block');
            $('#username').html(Tn.GetCookie('cookie[passport][nickname]'));
            $('#LoginOn').css('display','none');
            $('#LoginOff').css('display','block');
    new Tabs("M1A","M1P");//选项卡
    $('.picList').scaleImg();//鼠标经过图片缩放功能
    $('.backBtn').goTop();//返回顶部
    $('.ShareUl').liHover();
    $('.deShare').showShare();
    $('.IconQQ').parent().showCode();//QQ图标鼠标经过显示二维码
    $('.IconZJ').parent().showCode();//部落图标鼠标经过显示提示
    $('.IconWx').parent().showCode();//微信图标鼠标经过显示二维码效果
    /*============新闻搜索事件 start*/
    function newsFocus(obj){
        var str = $(obj).val(),
            p = $(obj).parent(),
            list = $(p).nextAll('.srhList');//$(p).next('.srhList');
        $(list).css('display','block');
        //$(p).parents('.fSrhForm').addClass('blueForm');
        $(obj).attr('placeholder','')
        $(obj).val('');
    function newsKey(obj){
        var p = $(obj).parent(),
            list = $(p).nextAll('.srhList');
        //$(p).parents('.fSrhForm').removeClass('blueForm');
        $(list).css('display','none');
    function newsBlur(obj){
        var p = $(obj).parent(),
            list = $(p).nextAll('.srhList');
        //$(p).parents('.fSrhForm').removeClass('blueForm');
        if($(list).is(':visible')){
            setTimeout(function(){
                $(list).css('display','none');
    /*==============================================================*/
    function getData(flag){//flag为状态标记 false表示当前只判断是否有信息需要更新显示 true表示将需要更新显示的信息显示到相应的位置
        /*===============一下ajax代码仅供参考，必要迁移代码有注释，请注意============*/
            url:'http://localhost/search/Data.php',
            type:'post',
            data:'order='+1,
            dataType:'json',
            success: function(d){
                if(flag){
                    var str = '',html = $('#artList').html();
                    //for(v in d){
                    for(var i = 0; i < 5;i++){
                        str = str + '<div class="artBlock"><a href="#" target="_blank" class="h3">菲律宾在中国南海费信岛上驻军</a><h3><a href="#" class="Act">理财资讯</a><a href="#">资源</a><a href="#">新概念</a><a href="#">投资策略</a><span class="artTime"><i>6月2日</i><i>15:30</i></span></h3></div>'
                    }
                    //}
                    /*==============必要代码参考============*/
                    html = str + html;
                    $('#artList').html(html);/*参考*/
                    $('.artLoad').css('display','none');
                    $('.loading').css('display','none');
                    loadState()
                }else{
                    if(d){
                        $('.artLoad').css('display','block');
                        $('.loading').css('display','none');
                        clearInterval(upT);
                    }
                /*==================================*/
        /*======================================================================*/
    $('.loadMore').scrollAdd();
    /*===============================================================*/
    function showShare(obj){
        var box = $(obj).find('.deShareUl');
        if($(box).is(':visible')){
            $(box).css('display','none');
            $(obj).removeClass('deUp');
            $(box).css('display','block');
            $(obj).addClass('deUp');
    //===========================================================
    function count(o)
        var t = typeof o;
        if(t == 'string')
            return o.length;
        }else if(t == 'object')
            var n = 0;
            for(var i in o)
            return n;
        return false;
    function getVideo()
        /** 2018-01-05 视频先不推荐注释
         var url = "http://app.cnfol.com/keydata/videorecommend.php";
        //data: {contid:ContId},
        data: {catid:1609,contid:31394596,isvideo:1},
        dataType:"jsonp",
        success: function(data){
            if(data[0])
                videoTitle = data[0].title;
                videoflv = data[0].url;
                videoflvurl = data[0].flvurl;
                videoId = data[0].contid;
                $('.videoObj').text(videoTitle);
                $('.videoObj').attr('href',videoflv);
                $('.autoBtn').btnOp();
                ajaxVideo();
                articlelistall();
                $('.videoBlock').css('display','block');
                $('.videoBlock').css('display','none');
        var url = "http://app.cnfol.com/keydata/articlecommend.php";
            type:'GET',
            cache:false,
            //data: {contid:ContId},
            data: {contid:31394596,isvideo:0,page:0,limit:20},
            dataType:"jsonp",
            success: function(data){
                var str= '';
                if(data.code==200){
                    $(data.data).each(function(i,n){
                        str +='<p><a href="'+n.url+'" target="_blank">'+n.title+'</a></p>';
                    });
                }else{
                    str = '推荐文章获取失败'+data.code;
                $('#RecomArt').append(str);
                getRecommAdvert(92);
    function getRecommAdvert(adi){
        var url_advert = "http://as.cnfol.com/index/index/api_flow?as_id="+adi;
            url:url_advert,
            type:'get',
            dataType:'json',
            success:function(data){
                if(data.code=='100'){
                    var recommNum = $("#RecomArt").children('p').length;
                    switch(adi){
                        case 92: if(recommNum < 3)
                            $('#RecomArt').append(preHTML);
                        else
                            $("#RecomArt p:eq(2)").after(preHTML);
                            break;
                        case 93: if(recommNum < 7)  $("#RecomArt p:eq("+(recommNum-1)+")").after(preHTML); else $("#RecomArt p:eq(6)").after(preHTML);
                            break;
                        case 94: if(recommNum < 11)  $("#RecomArt p:eq("+(recommNum-1)+")").after(preHTML); else $("#RecomArt p:eq(10)").after(preHTML);
                            break;
                        case 95: if(recommNum < 14)  $("#RecomArt p:eq("+(recommNum-1)+")").after(preHTML); else $("#RecomArt p:eq(13)").after(preHTML);
                            break;
                    }
                    adi = adi+1
                    if(adi < 96 ){
                        getRecommAdvert(adi);//递归循环
                    }
    function ajaxVideo(){
        var autoPlay = $.cookie('autoPlay');//是否自动播放视频的操作cookie名为autoPlay 等于1时表示自动播放，0表示非制动播放
        autoPlay = autoPlay == 'undefined'?1:autoPlay;
        if(autoPlay == 1){
            $('.autoBtn').addClass('On')
            $('.autoBtn').removeClass('On');
        var flashvars={
            f:videoflvurl,//视频地址
            p:autoPlay
            bgcolor:'#000000',wmode:'Transparent',allowFullScreen:true,allowScriptAccess:'always'
        };//这里定义播放器的其它参数如背景色（跟flashvars中的b不同），是否支持全屏，是否支持交互
        CKobject.embedSWF('http://hs.cnfol.com/cms/ckplayer/ckplayer.swf','VideoBox','ckplayer_a1','620', '350',flashvars,params);
    function articlelistall()
        var shtml = '';
        var url = "http://app.cnfol.com/dataapi/index.php/hostdata/getperfectbycontid";
            type:'GET',
            data: {type:"all",strlen:"14",num:6,contid:videoId},//可以用alert(3038);
            dataType:"jsonp",
            success: function(data){
                for(var j=0;j<count(data);j++)
                    var num = j+1;
                    j = parseInt(j);
                    data[j].Url = data[j].Url?data[j].Url:'';
                    data[j].Thumb = data[j].Thumb?data[j].Thumb:'';
                    data[j].Title = data[j].Title?data[j].Title:'';
                    data[j].programtime = data[j].programtime?data[j].programtime:'';
                    data[j].Name = data[j].Name?data[j].Name:'';
                    shtml +='<a href="'+data[j].Url+'" target="_blank" class="videoLi">';
                    shtml +='<img class="videoImg" src="'+data[j].Thumb+'" title="" />';
                    shtml +='<span class="videoTime">'+data[j].CreatedTime+'</span>';
                    shtml +=data[j].Title+"</a>";
                $('#pcentlist').append(shtml);
    $('.videoAbout').mouseover(function(){
        $('.videoList').css('display','block');
    $('.videoAbout').mouseout(function(){
        $('.videoList').css('display','none');
    function weishendu(){
        var url = "http://app.cnfol.com/dataapi/article_api.php?t=2&cid=3690&num=1&jsoncallback=?";
            function(json){
                var html='';
                var shtml='';
                for(var i=0;i<json.length;i++){
                    html+='<a href="'+json[i]['url']+'" target="_blank" class="sidePic"><img src="'+json[i]['imgurl']+'" title="" /></a>';
                    html+='<span><a href="'+json[i]['url']+'"  target="_blank" >'+json[i]['title']+'</a></span>';
                $("#wei").html(html);
    $('.srchInp input').eq(0).attr('value',$('.srhList a').eq(0).text().slice(1));
    function cehua(){
        var url = "http://app.cnfol.com/dataapi/article_api.php?t=2&cid=4022&num=1&jsoncallback=?";
            function(json){
                var html='';
                for(var i=0;i<json.length;i++){
                    html+='<a href="'+json[i]['url']+'" target="_blank" class="sidePic"><img src="'+json[i]['imgurl']+'" title="" /></a>';
                    html+='<span><a href="'+json[i]['url']+'"  target="_blank" >'+json[i]['title']+'</a></span>';
                $("#cehua").html(html);
    function special(){
        var url = "http://app.cnfol.com/dataapi/article_api.php?t=2&cid=4027&num=1&jsoncallback=?";
            function(json){
                var html='';
                for(var i=0;i<json.length;i++){
                    html+='<a href="'+json[i]['url']+'" target="_blank" class="sidePic"><img src="'+json[i]['imgurl']+'" title="" /></a>';
                    html+='<span><a href="'+json[i]['url']+'"  target="_blank" >'+json[i]['title']+'</a></span>';
                $("#special").html(html);
        var url = "http://app.cnfol.com/dataapi/article_api.php?t=2&cid=3656&num=6&jsoncallback=?";
            function(json){
                var html='';
                for(var i=0;i<json.length;i++){
                    html+='<a href="'+json[i]['url']+'" target="_blank">';
                    html+='<span><img src="'+json[i]['imgurl']+'" title="'+json[i]['title']+'" /></span>';
                    html+=' <b>'+json[i]['title']+'</b>';
                    html+='</a>';
                $("#tuji").html(html);
                $('.picList').scaleImg();//图片缩放效果
        var url = "http://app.cnfol.com/keydata/keywordapi.php?contid=31394596&num=3&jsoncallback=?";
            type:'GET',
            cache:false,
            dataType:"jsonp",
            success: function(json){
                var html='';
                html+='<span class="Fr">责任编辑：<a href="javascript:void(0)" target="_blank">cnfol001</a></span>';
                html+='标签';
                for(var i=0;i<json.length;i++){
                    json[i].Keyword = json[i].Keyword?json[i].Keyword:'';
                    html+='<a href="http://shell.cnfol.com/keyword/index.php?key='+json[i]['Keyword']+'" target="_blank">'+json[i]['Keyword']+'</a>';
                $("#tags").html(html);
    function getCookie(c_name)
        if (document.cookie.length>0)
            c_start=document.cookie.indexOf(c_name + "=")
            if (c_start!=-1){
                c_start=c_start + c_name.length+1
                c_end=document.cookie.indexOf(";",c_start)
                if (c_end==-1) c_end=document.cookie.length
                return unescape(document.cookie.substring(c_start,c_end))
    var artBrCount = $('.Article br').length; //获取行数
    var artTbCount = $('.Article table').length; //获取表格个数
    var jsaddstr = '<div id="as_115" ><ins data-revive-zoneid="115"></ins></div>';
    if(artBrCount < 1){
        $('.Article').prepend(jsaddstr);
    }else if(artBrCount < 8){
        if(artTbCount > 0){
            $('.Article table').eq(0).after(jsaddstr);
            $('.Article br').eq(Math.ceil(artBrCount/2)).after(jsaddstr);
        if(artTbCount > 0){
            $('.Article table').eq(0).after(jsaddstr);
            $('.Article br').eq(4).after(jsaddstr);
    /*查看评论 20181103注释,删除请咨询产品*/
    /*function comment(rt) {
        var f = document.pform;
        var article_class_id = document.getElementById("catid").value;
        var news_id = document.getElementById("newid").value;
        var news_title = document.getElementById("newtitle").value;
        if (!news_title) news_title = document.title;
        var news_title = news_title.replaceAll('"','`');
        news_title = encodeURI(news_title);
        var news_url = location.href;
        var baseurl = 'http://comment.cnfol.com/index.php?cid=' + news_id +'&news_url=' + news_url + '&news_title=' + news_title;
        $('.FmCom').attr('target','_blank');//20160607配合登录修改
            if (f.post_content.value=="") {
                alert('请输入评论内容');
                f.post_content.focus();
                return false;
            var msg = document.getElementById('post_content').value;
            var ul = "http://shell.cnfol.com/3gcnfol/artpl.php?call=?";
            f.action = ul+'&contid='+news_id+'&title='+news_title+'&url='+news_url+'&source=cms&msg='+msg;
            return true;
            referURL(baseurl);
    function send() {
        var B = document.title;
        var A = encodeURI(document.location.href);
        var news_id = document.getElementById("newid").value;
        var hf = "http://comment.cnfol.com/index.php?cid="+news_id+"&title=" + B + "&link=" + A
        window.location.href=hf;
    /*查看评论 20181103注释,删除请咨询产品*/
        ajaxDl(dlApi,116);
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极完美爬虫 - 全功能专业级网页爬虫
作者: MiniMax Agent
时间: 2025-07-15

这是一个完美的爬虫代码模板，包含了所有最佳实践：
1. 智能反爬虫机制
2. 多种页面类型处理
3. JavaScript渲染支持
4. 完整的数据处理流程
5. 专业的错误处理和重试机制
6. 多格式数据存储
7. 详细的日志和监控
8. 模块化设计，易于扩展
"""

import requests
import time
import random
import csv
import json
import logging
import re
import os
import sqlite3
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin, parse_qs, urlparse, quote
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass
from abc import ABC, abstractmethod

# 第三方库导入（自动安装）
try:
    from bs4 import BeautifulSoup
    import lxml
except ImportError:
    print("正在安装必要的库...")
    os.system("uv add beautifulsoup4 lxml selenium webdriver-manager")
    from bs4 import BeautifulSoup
    import lxml

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Selenium不可用，将使用基础requests模式")

# 配置日志
def setup_logging():
    """设置日志配置"""
    log_dir = 'logs'
    Path(log_dir).mkdir(parents=True, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'{log_dir}/ultimate_crawler.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

@dataclass
class CrawlerConfig:
    """爬虫配置类"""
    max_workers: int = 3
    delay_range: tuple = (2, 5)
    retry_times: int = 3
    timeout: int = 30
    max_pages: int = 50
    items_per_page: int = 20
    use_selenium: bool = True
    headless: bool = True
    enable_javascript: bool = True
    save_debug_files: bool = True

@dataclass
class WebsiteConfig:
    """网站配置类"""
    base_url: str
    search_url: str
    result_url: str = ""
    search_params: Dict[str, Any] = None
    selectors: Dict[str, str] = None
    
    def __post_init__(self):
        if self.search_params is None:
            self.search_params = {}
        if self.selectors is None:
            self.selectors = {}

class PageParser(ABC):
    """页面解析器抽象基类"""
    
    @abstractmethod
    def parse(self, content: str, url: str) -> List[Dict[str, Any]]:
        """解析页面内容"""
        pass

class GovernmentProcurementParser(PageParser):
    """政府采购网决标公告解析器"""

    def __init__(self):
        self.selectors = {
            # 决标公告列表页面选择器
            'result_table': 'table#bulletion, table.tb_01',
            'result_rows': 'tbody tr, tr.tb_b2',
            'detail_link': 'a[href*="tpam"], a[href*="QueryAtmAwardDetail"]',
            'no_data_indicators': [
                '查無資料', '沒有資料', '無符合條件', '0筆資料',
                'No data', 'No results', '暫無資料', '查無符合條件之資料'
            ],
            # 详细页面选择器
            'detail_table': 'table[summary*="標案基本資料"], table.table_01',
            'detail_rows': 'tr:has(td)',
            'award_table': 'table[summary*="決標資料"], table[summary*="得標廠商"]'
        }
    
    def parse(self, content: str, url: str) -> List[Dict[str, Any]]:
        """解析决标公告列表页面"""
        try:
            soup = BeautifulSoup(content, 'lxml')
            announcements = []

            # 检查是否有"无数据"提示
            text_content = soup.get_text()
            if any(indicator in text_content for indicator in self.selectors['no_data_indicators']):
                logger.info("页面显示无数据")
                return []

            # 查找结果表格
            table = soup.find('table', id='bulletion')
            if not table:
                tables = soup.select(self.selectors['result_table'])
                if tables:
                    table = tables[0]

            if table:
                # 查找tbody中的数据行
                tbody = table.find('tbody')
                if tbody:
                    rows = tbody.find_all('tr')
                else:
                    rows = table.find_all('tr')[1:]  # 跳过表头

                for row in rows:
                    try:
                        cells = row.find_all('td')
                        if len(cells) >= 8:  # 决标公告至少有8列
                            announcement = self._extract_list_data(cells, row, url)
                            if announcement and (announcement.get('case_name') or announcement.get('organization')):
                                announcements.append(announcement)
                    except Exception as e:
                        logger.warning(f"解析行数据失败: {e}")
                        continue

            logger.info(f"解析到 {len(announcements)} 条决标公告")
            return announcements

        except Exception as e:
            logger.error(f"页面解析失败: {e}")
            return []
    
    def _extract_list_data(self, cells: List, row, base_url: str) -> Dict[str, Any]:
        """从决标公告列表行提取数据"""
        try:
            announcement = {}

            # 根据实际页面结构解析数据
            # 表格列：项次、种类、机关名称、标案案号/标案名称、招标公告日期、决标公告、截止投标日期、公开阅览日期、预告公告日期、功能选项
            if len(cells) >= 8:
                # 项次 (第0列)
                announcement['item_number'] = self._clean_text(cells[0].get_text())

                # 种类 (第1列)
                announcement['tender_type'] = self._clean_text(cells[1].get_text())

                # 机关名称 (第2列)
                announcement['organization'] = self._clean_text(cells[2].get_text())

                # 标案案号和标案名称 (第3列)
                case_cell = cells[3]
                case_text = self._clean_text(case_cell.get_text())

                # 分离案号和名称
                lines = case_text.split('\n')
                if len(lines) >= 2:
                    announcement['case_number'] = lines[0].strip()
                    announcement['case_name'] = lines[1].strip()
                else:
                    announcement['case_name'] = case_text

                # 招标公告日期 (第4列)
                announcement['tender_notice_date'] = self._clean_text(cells[4].get_text())

                # 决标公告日期 (第5列)
                announcement['award_notice_date'] = self._clean_text(cells[5].get_text())

                # 截止投标日期 (第6列)
                announcement['bid_deadline'] = self._clean_text(cells[6].get_text())

                # 公开阅览日期 (第7列)
                if len(cells) > 7:
                    announcement['public_reading_date'] = self._clean_text(cells[7].get_text())

                # 预告公告日期 (第8列)
                if len(cells) > 8:
                    announcement['forecast_notice_date'] = self._clean_text(cells[8].get_text())

                # 提取详细页面链接
                detail_link = case_cell.find('a', href=True)
                if detail_link:
                    href = detail_link.get('href')
                    if href:
                        announcement['detail_url'] = urljoin(base_url, href) if href.startswith('/') else href
                        # 提取pk参数
                        if 'pk=' in href:
                            pk_match = re.search(r'pk=([^&]+)', href)
                            if pk_match:
                                announcement['pk_main'] = pk_match.group(1)

            return announcement

        except Exception as e:
            logger.warning(f"提取列表数据失败: {e}")
            return {}
    
    def _parse_alternative_structures(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, Any]]:
        """解析替代结构"""
        announcements = []
        
        try:
            # 尝试解析div结构
            content_divs = soup.find_all('div', {'class': re.compile(r'(result|item|row|data|content)')})
            
            for div in content_divs:
                text = div.get_text().strip()
                if len(text) > 20 and any(keyword in text for keyword in ['標案', '採購', '招標', '案號']):
                    announcement = {
                        'title': text[:100],
                        'content': text,
                        'source_structure': 'div'
                    }
                    
                    link = div.find('a')
                    if link:
                        announcement['url'] = urljoin(base_url, link.get('href', ''))
                    
                    announcements.append(announcement)
            
            # 尝试解析列表结构
            if not announcements:
                lists = soup.find_all(['ul', 'ol'])
                for ul in lists:
                    items = ul.find_all('li')
                    for item in items:
                        text = item.get_text().strip()
                        if len(text) > 20:
                            announcement = {
                                'title': text[:100],
                                'content': text,
                                'source_structure': 'list'
                            }
                            
                            link = item.find('a')
                            if link:
                                announcement['url'] = urljoin(base_url, link.get('href', ''))
                            
                            announcements.append(announcement)
            
        except Exception as e:
            logger.error(f"替代结构解析失败: {e}")
        
        return announcements[:10]  # 限制数量

    def parse_detail_page(self, content: str, url: str) -> Dict[str, Any]:
        """解析决标公告详细页面"""
        try:
            soup = BeautifulSoup(content, 'lxml')
            detail_data = {}

            # 从页面文本中提取结构化数据
            page_text = soup.get_text()

            # 提取机关资料
            self._extract_agency_info(page_text, detail_data)

            # 提取已公告资料
            self._extract_announcement_info(page_text, detail_data)

            # 提取投标厂商信息
            self._extract_bidder_info(page_text, detail_data)

            # 提取决标品项
            self._extract_award_items(page_text, detail_data)

            # 提取决标资料
            self._extract_award_data(page_text, detail_data)

            detail_data['detail_url'] = url
            detail_data['crawl_time'] = datetime.now().isoformat()

            return detail_data

        except Exception as e:
            logger.error(f"详细页面解析失败: {e}")
            return {}

    def _extract_agency_info(self, text: str, data: Dict):
        """提取机关资料"""
        try:
            # 机关代码
            if '機關代碼' in text:
                match = re.search(r'機關代碼\s*([^\n\r]+)', text)
                if match:
                    data['org_code'] = match.group(1).strip()

            # 机关名称
            if '機關名稱' in text:
                match = re.search(r'機關名稱\s*([^\n\r]+)', text)
                if match:
                    data['org_name'] = match.group(1).strip()

            # 单位名称
            if '單位名稱' in text:
                match = re.search(r'單位名稱\s*([^\n\r]+)', text)
                if match:
                    data['unit_name'] = match.group(1).strip()

            # 机关地址
            if '機關地址' in text:
                match = re.search(r'機關地址\s*([^\n\r]+)', text)
                if match:
                    data['org_address'] = match.group(1).strip()

            # 联络人
            if '聯絡人' in text:
                match = re.search(r'聯絡人\s*([^\n\r]+)', text)
                if match:
                    data['contact_person'] = match.group(1).strip()

            # 联络电话
            if '聯絡電話' in text:
                match = re.search(r'聯絡電話\s*([^\n\r]+)', text)
                if match:
                    data['contact_phone'] = match.group(1).strip()

        except Exception as e:
            logger.warning(f"提取机关资料失败: {e}")

    def _extract_announcement_info(self, text: str, data: Dict):
        """提取已公告资料"""
        try:
            # 标案案号
            if '標案案號' in text:
                match = re.search(r'標案案號\s*([^\n\r]+)', text)
                if match:
                    data['case_number'] = match.group(1).strip()

            # 标案名称
            if '標案名稱' in text:
                match = re.search(r'標案名稱\s*([^\n\r]+)', text)
                if match:
                    data['case_name'] = match.group(1).strip()

            # 招标方式
            if '招標方式' in text:
                match = re.search(r'招標方式\s*([^\n\r]+)', text)
                if match:
                    data['tender_method'] = match.group(1).strip()

            # 决标方式
            if '決標方式' in text:
                match = re.search(r'決標方式\s*([^\n\r]+)', text)
                if match:
                    data['award_method'] = match.group(1).strip()

            # 标的分类 - 更精确的匹配
            if '標的分類' in text:
                # 匹配标的分类，避免包含导航菜单
                match = re.search(r'標的分類\s*<([^>]+)>\s*(\d+\s*[^\n\r]+)', text)
                if match:
                    category_type = match.group(1).strip()  # 如：勞務類
                    category_detail = match.group(2).strip()  # 如：97 其他服務
                    data['category'] = f"{category_type} {category_detail}"
                else:
                    # 备用匹配方式
                    match = re.search(r'標的分類\s*([^\n\r財物相關]+)', text)
                    if match:
                        category = match.group(1).strip()
                        # 清理可能包含的导航信息
                        if '財物相關' not in category and '國外採購' not in category:
                            data['category'] = category

            # 开标时间
            if '開標時間' in text:
                match = re.search(r'開標時間\s*([^\n\r]+)', text)
                if match:
                    data['bid_opening_time'] = match.group(1).strip()

            # 预算金额
            if '預算金額' in text:
                match = re.search(r'預算金額\s*([^\n\r]+)', text)
                if match:
                    data['budget_amount'] = match.group(1).strip()

            # 履约地点
            if '履約地點' in text:
                match = re.search(r'履約地點\s*([^\n\r]+)', text)
                if match:
                    data['contract_location'] = match.group(1).strip()

        except Exception as e:
            logger.warning(f"提取公告资料失败: {e}")

    def _extract_bidder_info(self, text: str, data: Dict):
        """提取投标厂商信息"""
        try:
            bidders = []

            # 查找投标厂商家数
            if '投標廠商家數' in text:
                match = re.search(r'投標廠商家數\s*(\d+)', text)
                if match:
                    data['bidder_count'] = int(match.group(1))

            # 提取得标厂商信息
            bidder_pattern = r'投標廠商(\d+).*?廠商名稱\s*([^\n\r]+).*?是否得標\s*([^\n\r]+)'
            matches = re.finditer(bidder_pattern, text, re.DOTALL)

            for match in matches:
                bidder_num = match.group(1)
                company_name = match.group(2).strip()
                is_winner = match.group(3).strip() == '是'

                bidder = {
                    'bidder_number': bidder_num,
                    'company_name': company_name,
                    'is_winner': is_winner
                }

                # 如果是得标厂商，提取更多信息
                if is_winner:
                    # 提取决标金额
                    amount_pattern = rf'投標廠商{bidder_num}.*?決標金額\s*([^\n\r]+)'
                    amount_match = re.search(amount_pattern, text, re.DOTALL)
                    if amount_match:
                        bidder['award_amount'] = amount_match.group(1).strip()

                    # 提取履约期间
                    period_pattern = rf'投標廠商{bidder_num}.*?履約起迄日期\s*([^\n\r]+)'
                    period_match = re.search(period_pattern, text, re.DOTALL)
                    if period_match:
                        bidder['contract_period'] = period_match.group(1).strip()

                bidders.append(bidder)

            if bidders:
                data['bidders'] = bidders
                # 单独提取得标厂商
                winners = [b for b in bidders if b.get('is_winner')]
                if winners:
                    data['winners'] = winners

        except Exception as e:
            logger.warning(f"提取投标厂商信息失败: {e}")

    def _extract_award_items(self, text: str, data: Dict):
        """提取决标品项"""
        try:
            # 决标品项数
            if '決標品項數' in text:
                match = re.search(r'決標品項數\s*(\d+)', text)
                if match:
                    data['award_item_count'] = int(match.group(1))

            # 品项名称
            if '品項名稱' in text:
                match = re.search(r'品項名稱\s*([^\n\r]+)', text)
                if match:
                    data['item_name'] = match.group(1).strip()

        except Exception as e:
            logger.warning(f"提取决标品项失败: {e}")

    def _extract_award_data(self, text: str, data: Dict):
        """提取决标资料"""
        try:
            # 决标日期
            if '決標日期' in text:
                match = re.search(r'決標日期\s*([^\n\r]+)', text)
                if match:
                    data['award_date'] = match.group(1).strip()

            # 决标公告日期
            if '決標公告日期' in text:
                match = re.search(r'決標公告日期\s*([^\n\r]+)', text)
                if match:
                    data['award_notice_date'] = match.group(1).strip()

            # 总决标金额
            if '總決標金額' in text:
                match = re.search(r'總決標金額\s*([^\n\r]+)', text)
                if match:
                    data['total_award_amount'] = match.group(1).strip()

            # 底价金额
            if '底價金額' in text:
                match = re.search(r'底價金額\s*([^\n\r]+)', text)
                if match:
                    data['reserve_price'] = match.group(1).strip()

            # 契约编号
            if '契約編號' in text:
                match = re.search(r'契約編號\s*([^\n\r]+)', text)
                if match:
                    data['contract_number'] = match.group(1).strip()

        except Exception as e:
            logger.warning(f"提取决标资料失败: {e}")
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ''
        
        # 去除多余空白字符
        text = re.sub(r'\\s+', ' ', text.strip())
        text = re.sub(r'[\\r\\n\\t]', '', text)
        text = text.replace('\\xa0', ' ')
        
        return text

class UltimatePerfectCrawler:
    """终极完美爬虫类"""
    
    def __init__(self, website_config: WebsiteConfig, crawler_config: CrawlerConfig = None):
        """初始化爬虫"""
        self.website_config = website_config
        self.config = crawler_config or CrawlerConfig()
        
        # 创建目录
        self._create_directories()
        
        # 初始化组件
        self.session = requests.Session()
        self.driver = None
        self.parser = GovernmentProcurementParser()
        
        # 设置会话
        self._setup_session()
        
        # 初始化数据库
        self._init_database()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_items': 0,
            'start_time': datetime.now()
        }
        
        logger.info(f"爬虫初始化完成 - 目标: {website_config.base_url}")
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            'logs',
            'data/csv',
            'data/json',
            'data/database',
            'debug/html',
            'debug/screenshots'
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _setup_session(self):
        """设置会话"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        
        self.session.headers.update({
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8,zh-CN;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
    
    def _init_selenium(self) -> bool:
        """初始化Selenium"""
        if not SELENIUM_AVAILABLE or not self.config.use_selenium:
            return False
        
        try:
            chrome_options = Options()
            if self.config.headless:
                chrome_options.add_argument('--headless')
            
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={self.session.headers["User-Agent"]}')
            
            # 禁用图片加载以提高速度
            prefs = {"profile.managed_default_content_settings.images": 2}
            chrome_options.add_experimental_option("prefs", prefs)
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(self.config.timeout)
            
            logger.info("Selenium初始化成功")
            return True
            
        except Exception as e:
            logger.warning(f"Selenium初始化失败: {e}")
            return False
    
    def _init_database(self):
        """初始化数据库"""
        self.db_path = 'data/database/ultimate_crawler.db'
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    -- 基本信息
                    item_number TEXT,
                    tender_type TEXT,
                    organization TEXT,
                    case_number TEXT,
                    case_name TEXT,

                    -- 日期信息
                    tender_notice_date TEXT,
                    award_notice_date TEXT,
                    bid_deadline TEXT,
                    public_reading_date TEXT,
                    forecast_notice_date TEXT,

                    -- 详细页面信息
                    org_code TEXT,
                    org_name TEXT,
                    unit_name TEXT,
                    org_address TEXT,
                    contact_person TEXT,
                    contact_phone TEXT,
                    budget_amount TEXT,
                    award_amount TEXT,
                    total_award_amount TEXT,
                    reserve_price TEXT,
                    category TEXT,
                    tender_method TEXT,
                    award_method TEXT,
                    contract_period TEXT,
                    contract_location TEXT,
                    contract_number TEXT,
                    bid_opening_time TEXT,
                    bidder_count INTEGER,
                    award_item_count INTEGER,
                    item_name TEXT,

                    -- 得标厂商信息
                    winner_company TEXT,
                    winners_json TEXT,  -- JSON格式存储多个得标厂商

                    -- 链接和元数据
                    detail_url TEXT,
                    pk_main TEXT,
                    url TEXT,
                    source_structure TEXT,
                    crawl_session TEXT,
                    crawl_time TEXT,

                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE,
                    website_url TEXT,
                    search_config TEXT,
                    total_pages INTEGER,
                    total_items INTEGER,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    status TEXT,
                    error_message TEXT
                )
            ''')
            
            conn.commit()
        
        logger.info("数据库初始化完成")
    
    def _get_page_content(self, url: str, method: str = 'GET', data: Dict = None) -> Optional[str]:
        """获取页面内容"""
        try:
            # 尝试Selenium（如果启用且可用）
            if self.config.use_selenium and self.config.enable_javascript:
                content = self._get_content_selenium(url, data)
                if content:
                    return content
            
            # 使用requests
            return self._get_content_requests(url, method, data)
            
        except Exception as e:
            logger.error(f"获取页面内容失败: {e}")
            return None
    
    def _get_content_selenium(self, url: str, form_data: Dict = None) -> Optional[str]:
        """使用Selenium获取内容"""
        if not self.driver:
            if not self._init_selenium():
                return None
        
        try:
            self.driver.get(url)
            
            # 如果有表单数据，尝试填写并提交
            if form_data:
                self._fill_form_selenium(form_data)
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 保存截图（调试用）
            if self.config.save_debug_files:
                screenshot_path = f'/workspace/debug/screenshots/page_{int(time.time())}.png'
                self.driver.save_screenshot(screenshot_path)
            
            content = self.driver.page_source
            self.stats['successful_requests'] += 1
            
            return content
            
        except Exception as e:
            logger.warning(f"Selenium获取内容失败: {e}")
            self.stats['failed_requests'] += 1
            return None
    
    def _fill_form_selenium(self, form_data: Dict):
        """使用Selenium填写表单"""
        try:
            # 查找并填写表单字段
            for field_name, value in form_data.items():
                if not value:
                    continue
                
                # 尝试多种选择器
                selectors = [
                    f'input[name="{field_name}"]',
                    f'select[name="{field_name}"]',
                    f'textarea[name="{field_name}"]',
                    f'#{field_name}',
                    f'[id*="{field_name}"]'
                ]
                
                element = None
                for selector in selectors:
                    try:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        break
                    except:
                        continue
                
                if element:
                    if element.tag_name == 'select':
                        # 处理下拉选择
                        from selenium.webdriver.support.ui import Select
                        select = Select(element)
                        try:
                            select.select_by_value(str(value))
                        except:
                            select.select_by_visible_text(str(value))
                    elif element.get_attribute('type') == 'checkbox':
                        # 处理复选框
                        if value and not element.is_selected():
                            element.click()
                    else:
                        # 处理文本输入
                        element.clear()
                        element.send_keys(str(value))
            
            # 查找并点击提交按钮
            submit_selectors = [
                'input[type="submit"]',
                'button[type="submit"]',
                'input[value*="查詢"]',
                'input[value*="搜尋"]',
                'button:contains("查詢")',
                '.btn-submit',
                '#submitBtn'
            ]
            
            for selector in submit_selectors:
                try:
                    submit_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                    submit_btn.click()
                    logger.info("表单提交成功")
                    return
                except:
                    continue
            
            logger.warning("未找到提交按钮")
            
        except Exception as e:
            logger.error(f"表单填写失败: {e}")
    
    def _get_content_requests(self, url: str, method: str = 'GET', data: Dict = None) -> Optional[str]:
        """使用requests获取内容"""
        for attempt in range(self.config.retry_times):
            try:
                self.stats['total_requests'] += 1
                
                # 添加延迟
                time.sleep(random.uniform(*self.config.delay_range))
                
                # 随机更换User-Agent
                if random.random() < 0.3:
                    self._setup_session()
                
                if method.upper() == 'POST' and data:
                    response = self.session.post(url, data=data, timeout=self.config.timeout)
                else:
                    response = self.session.get(url, timeout=self.config.timeout)
                
                response.raise_for_status()
                response.encoding = 'utf-8'
                
                self.stats['successful_requests'] += 1
                logger.info(f"请求成功: {url} (尝试 {attempt + 1})")
                
                return response.text
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.config.retry_times}): {url} - {e}")
                
                if attempt < self.config.retry_times - 1:
                    delay = (2 ** attempt) * random.uniform(1, 2)
                    time.sleep(delay)
        
        self.stats['failed_requests'] += 1
        return None
    
    def crawl_search_results(self, search_params: Dict = None) -> List[Dict[str, Any]]:
        """爬取决标公告搜索结果"""
        search_params = search_params or self.website_config.search_params
        all_data = []

        session_id = f"session_{int(time.time())}"

        # 记录爬取会话开始
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO crawl_sessions
                (session_id, website_url, search_config, start_time, status)
                VALUES (?, ?, ?, ?, ?)
            ''', (session_id, self.website_config.base_url,
                  json.dumps(search_params), datetime.now(), 'started'))
            conn.commit()

        try:
            # 确定搜索URL
            search_url = self.website_config.result_url or self.website_config.search_url

            # 执行搜索
            for page in range(1, self.config.max_pages + 1):
                try:
                    logger.info(f"正在爬取第 {page} 页决标公告列表")

                    # 构建URL参数
                    current_params = search_params.copy()
                    current_params['pageIndex'] = str(page - 1)  # 页面索引从0开始

                    # 构建完整URL
                    param_str = '&'.join([f"{k}={quote(str(v))}" for k, v in current_params.items()])
                    page_url = f"{search_url}?{param_str}"

                    # 获取页面内容
                    content = self._get_page_content(page_url)

                    if not content:
                        logger.warning(f"第 {page} 页内容获取失败")
                        continue

                    # 保存调试文件
                    if self.config.save_debug_files:
                        debug_path = f'debug/html/list_page_{page}_{int(time.time())}.html'
                        Path(debug_path).parent.mkdir(parents=True, exist_ok=True)
                        with open(debug_path, 'w', encoding='utf-8') as f:
                            f.write(content)

                    # 解析列表页面内容
                    page_data = self.parser.parse(content, search_url)

                    if not page_data:
                        logger.info(f"第 {page} 页无数据，停止爬取")
                        break

                    # 爬取每个公告的详细信息
                    detailed_data = []
                    for item in page_data:
                        try:
                            if item.get('detail_url'):
                                logger.info(f"正在爬取详细信息: {item.get('title', '未知标案')[:50]}...")

                                # 获取详细页面内容
                                detail_content = self._get_page_content(item['detail_url'])

                                if detail_content:
                                    # 保存详细页面调试文件
                                    if self.config.save_debug_files:
                                        detail_debug_path = f'debug/html/detail_{item.get("pk_atm_main", "unknown")}_{int(time.time())}.html'
                                        with open(detail_debug_path, 'w', encoding='utf-8') as f:
                                            f.write(detail_content)

                                    # 解析详细页面
                                    detail_info = self.parser.parse_detail_page(detail_content, item['detail_url'])

                                    # 合并列表数据和详细数据
                                    merged_item = {**item, **detail_info}
                                    merged_item['crawl_session'] = session_id
                                    detailed_data.append(merged_item)

                                    # 添加延迟避免过于频繁的请求
                                    time.sleep(random.uniform(1, 3))
                                else:
                                    logger.warning(f"详细页面获取失败: {item['detail_url']}")
                                    item['crawl_session'] = session_id
                                    detailed_data.append(item)
                            else:
                                item['crawl_session'] = session_id
                                detailed_data.append(item)

                        except Exception as e:
                            logger.error(f"处理详细信息失败: {e}")
                            item['crawl_session'] = session_id
                            detailed_data.append(item)

                    all_data.extend(detailed_data)
                    self.stats['total_items'] += len(detailed_data)

                    logger.info(f"第 {page} 页完成，获得 {len(detailed_data)} 条完整数据，累计 {len(all_data)} 条")

                    # 检查是否应该停止
                    if len(page_data) < 5:  # 如果少于5条数据，可能是最后一页
                        logger.info("数据量减少，可能已到最后一页")
                        break

                except KeyboardInterrupt:
                    logger.info("用户中断爬取")
                    break
                except Exception as e:
                    logger.error(f"第 {page} 页爬取出错: {e}")
                    continue

            # 更新会话记录
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE crawl_sessions
                    SET total_items = ?, end_time = ?, status = ?
                    WHERE session_id = ?
                ''', (len(all_data), datetime.now(), 'completed', session_id))
                conn.commit()

        except Exception as e:
            logger.error(f"爬取过程出错: {e}")

            # 记录错误
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE crawl_sessions
                    SET end_time = ?, status = ?, error_message = ?
                    WHERE session_id = ?
                ''', (datetime.now(), 'error', str(e), session_id))
                conn.commit()

        return all_data
    
    def save_data(self, data: List[Dict], formats: List[str] = None) -> Dict[str, str]:
        """保存数据"""
        if not data:
            return {}
        
        formats = formats or ['csv', 'json', 'database']
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        saved_files = {}
        
        # 保存CSV
        if 'csv' in formats:
            csv_path = f'data/csv/ultimate_crawler_data_{timestamp}.csv'
            try:
                with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    if data:
                        fieldnames = set()
                        for item in data:
                            fieldnames.update(item.keys())
                        
                        writer = csv.DictWriter(csvfile, fieldnames=list(fieldnames))
                        writer.writeheader()
                        writer.writerows(data)
                
                saved_files['csv'] = csv_path
                logger.info(f"CSV文件已保存: {csv_path}")
            except Exception as e:
                logger.error(f"保存CSV失败: {e}")
        
        # 保存JSON
        if 'json' in formats:
            json_path = f'data/json/ultimate_crawler_data_{timestamp}.json'
            try:
                with open(json_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(data, jsonfile, ensure_ascii=False, indent=2, default=str)
                
                saved_files['json'] = json_path
                logger.info(f"JSON文件已保存: {json_path}")
            except Exception as e:
                logger.error(f"保存JSON失败: {e}")
        
        # 保存数据库
        if 'database' in formats:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    for item in data:
                        # 处理得标厂商数据
                        winners_json = ''
                        if item.get('winners'):
                            winners_json = json.dumps(item['winners'], ensure_ascii=False)

                        cursor.execute('''
                            INSERT OR REPLACE INTO crawl_data
                            (item_number, tender_type, organization, case_number, case_name,
                             tender_notice_date, award_notice_date, bid_deadline, public_reading_date, forecast_notice_date,
                             org_code, org_name, unit_name, org_address, contact_person, contact_phone,
                             budget_amount, award_amount, total_award_amount, reserve_price, category, tender_method, award_method,
                             contract_period, contract_location, contract_number, bid_opening_time,
                             bidder_count, award_item_count, item_name,
                             winner_company, winners_json, detail_url, pk_main, url, source_structure,
                             crawl_session, crawl_time, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                        ''', (
                            item.get('item_number', ''),
                            item.get('tender_type', ''),
                            item.get('organization', ''),
                            item.get('case_number', ''),
                            item.get('case_name', ''),
                            item.get('tender_notice_date', ''),
                            item.get('award_notice_date', ''),
                            item.get('bid_deadline', ''),
                            item.get('public_reading_date', ''),
                            item.get('forecast_notice_date', ''),
                            item.get('org_code', ''),
                            item.get('org_name', ''),
                            item.get('unit_name', ''),
                            item.get('org_address', ''),
                            item.get('contact_person', ''),
                            item.get('contact_phone', ''),
                            item.get('budget_amount', ''),
                            item.get('award_amount', ''),
                            item.get('total_award_amount', ''),
                            item.get('reserve_price', ''),
                            item.get('category', ''),
                            item.get('tender_method', ''),
                            item.get('award_method', ''),
                            item.get('contract_period', ''),
                            item.get('contract_location', ''),
                            item.get('contract_number', ''),
                            item.get('bid_opening_time', ''),
                            item.get('bidder_count', 0),
                            item.get('award_item_count', 0),
                            item.get('item_name', ''),
                            item.get('winner_company', ''),
                            winners_json,
                            item.get('detail_url', ''),
                            item.get('pk_main', ''),
                            item.get('url', ''),
                            item.get('source_structure', ''),
                            item.get('crawl_session', ''),
                            item.get('crawl_time', '')
                        ))
                    
                    conn.commit()
                
                saved_files['database'] = self.db_path
                logger.info(f"数据库已保存 {len(data)} 条记录")
            except Exception as e:
                logger.error(f"保存数据库失败: {e}")
        
        return saved_files
    
    def generate_report(self, data_count: int) -> str:
        """生成详细报告"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        # 计算成功率
        total_requests = self.stats['total_requests']
        success_rate = (self.stats['successful_requests'] / max(total_requests, 1)) * 100
        
        # 计算爬取速度
        speed_per_minute = (data_count / max(duration.total_seconds(), 1)) * 60
        
        report = f"""
{'='*60}
               终极完美爬虫执行报告
{'='*60}

🕐 执行时间:
   开始: {self.stats['start_time'].strftime('%Y-%m-%d %H:%M:%S')}
   结束: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
   时长: {duration}

🌐 目标网站:
   基础URL: {self.website_config.base_url}
   搜索URL: {self.website_config.search_url}

⚙️  爬虫配置:
   并发数: {self.config.max_workers}
   延迟范围: {self.config.delay_range} 秒
   最大页数: {self.config.max_pages}
   使用Selenium: {'是' if self.config.use_selenium else '否'}
   JavaScript支持: {'是' if self.config.enable_javascript else '否'}

📊 请求统计:
   总请求数: {total_requests}
   成功请求: {self.stats['successful_requests']}
   失败请求: {self.stats['failed_requests']}
   成功率: {success_rate:.2f}%

📈 数据统计:
   总爬取条目: {data_count}
   平均速度: {speed_per_minute:.2f} 条/分钟
   数据质量: {'优秀' if data_count > 0 else '需要调试'}

💾 文件输出:
   数据库: {self.db_path}
   CSV目录: data/csv/
   JSON目录: data/json/
   日志文件: logs/ultimate_crawler.log
   调试文件: debug/

🔧 系统状态:
   Selenium可用: {'是' if SELENIUM_AVAILABLE else '否'}
   第三方库: BeautifulSoup, lxml, requests
   
💡 优化建议:
   {'1. 数据获取成功，爬虫运行正常' if data_count > 0 else '1. 建议检查网站结构或调整解析规则'}
   {'2. 可以适当提高并发数' if success_rate > 90 else '2. 建议增加延迟时间或降低并发数'}
   3. 定期更新User-Agent和反爬虫策略
   4. 监控网站结构变化

{'='*60}
"""
        
        return report
    
    def run(self, search_params: Dict = None) -> Dict[str, Any]:
        """运行爬虫"""
        logger.info("🚀 终极完美爬虫启动")
        logger.info(f"🎯 目标网站: {self.website_config.base_url}")
        
        try:
            # 爬取数据
            all_data = self.crawl_search_results(search_params)
            
            # 保存数据
            saved_files = {}
            if all_data:
                saved_files = self.save_data(all_data)
                logger.info(f"✅ 爬取完成！共获得 {len(all_data)} 条数据")
            else:
                logger.warning("⚠️  未获得任何数据")
            
            # 生成报告
            report = self.generate_report(len(all_data))
            logger.info(report)
            
            # 保存报告
            report_path = f'logs/ultimate_crawl_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            return {
                'status': 'success' if all_data else 'no_data',
                'total_items': len(all_data),
                'report': report,
                'saved_files': saved_files,
                'data_sample': all_data[:3] if all_data else [],
                'stats': self.stats
            }
            
        except Exception as e:
            logger.error(f"💥 爬虫执行出错: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'total_items': 0,
                'stats': self.stats
            }
        
        finally:
            # 清理资源
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'driver') and self.driver:
            try:
                self.driver.quit()
            except:
                pass

# 示例配置
def create_government_procurement_config() -> WebsiteConfig:
    """创建政府采购网配置 - 针对决标公告"""
    return WebsiteConfig(
        base_url="https://web.pcc.gov.tw",
        search_url="https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion",
        result_url="https://web.pcc.gov.tw/prkms/tender/common/bulletion/readBulletion",
        search_params={
            'querySentence': '国防部',  # 搜索关键词
            'tenderStatusType': '决标',  # 决标状态
            'sortCol': 'AWARD_NOTICE_DATE',  # 按决标日期排序
            'timeRange': '111',  # 时间范围
            'pron': 'true',
            'fuzzy': 'true',
            'pageSize': '10'  # 每页10条
        }
    )

def main():
    """主函数"""
    print("🌟 终极完美爬虫 - 全功能专业级网页爬虫")
    print("=" * 60)
    
    try:
        # 创建配置
        website_config = create_government_procurement_config()
        crawler_config = CrawlerConfig(
            max_workers=2,
            delay_range=(3, 6),
            max_pages=5,  # 限制页数用于演示
            use_selenium=True,
            headless=True
        )
        
        # 创建爬虫实例
        crawler = UltimatePerfectCrawler(website_config, crawler_config)
        
        # 运行爬虫
        result = crawler.run()
        
        # 显示结果
        print("\\n" + "="*60)
        print("               🎯 爬取结果总结")
        print("="*60)
        print(f"📊 状态: {result['status']}")
        print(f"📈 总数据量: {result['total_items']} 条")
        print(f"📁 保存文件: {len(result.get('saved_files', {}))} 个")
        
        if result.get('data_sample'):
            print("\\n📋 数据样本 (前3条):")
            for i, item in enumerate(result['data_sample'], 1):
                print(f"\\n   [{i}] {item.get('title', '无标题')[:60]}...")
                if item.get('organization'):
                    print(f"       机关: {item['organization'][:40]}")
                if item.get('announcement_date'):
                    print(f"       日期: {item['announcement_date']}")
        
        print("\\n💾 文件位置:")
        for format_type, path in result.get('saved_files', {}).items():
            print(f"   {format_type.upper()}: {path}")
        
        print("\\n📋 完整报告已保存到日志文件")
        print("🔍 调试文件位置: debug/")
        
        return result
        
    except Exception as e:
        logger.error(f"主程序执行出错: {e}")
        return {'status': 'error', 'error': str(e)}

if __name__ == "__main__":
    result = main()

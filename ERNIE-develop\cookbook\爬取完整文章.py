import asyncio
import re
from datetime import datetime
from pathlib import Path

from crawl_utils import CrawlUtils


async def crawl_full_articles(urls, output_file="full_articles.md"):
    """
    爬取所有链接的完整文章内容并保存到 markdown 文件
    """
    Path(output_file).unlink(missing_ok=True)

    async with CrawlUtils() as crawler:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write("# 完整文章爬取结果\n\n")
            
            for i, url in enumerate(urls, 1):
                print(f"🕷 正在抓取第 {i}/{len(urls)} 个网站: {url}")
                text = await crawler.get_webpage_text(url)
                
                if not text:
                    print(f"⚠️ 抓取失败: {url}")
                    f.write(f"## {i}. 🔗 {url}\n")
                    f.write("**抓取失败**\n\n")
                    continue

                # 写入完整内容
                f.write(f"## {i}. 🔗 {url}\n")
                f.write(f"**抓取时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**内容长度**: {len(text)} 字符\n\n")
                f.write("### 完整页面内容：\n")
                f.write("```\n")
                f.write(text)
                f.write("\n```\n\n")
                f.write("---\n\n")

                print(f"✅ 保存完整内容: {len(text)} 字符")

        print(f"🎉 所有网站爬取完成，结果保存到: {output_file}")


async def crawl_articles_with_links(urls, output_file="articles_with_links.md"):
    """
    爬取文章并尝试提取其中的链接，然后爬取链接指向的文章
    """
    Path(output_file).unlink(missing_ok=True)

    async with CrawlUtils() as crawler:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write("# 文章及链接内容爬取结果\n\n")
            
            for i, url in enumerate(urls, 1):
                print(f"🕷 正在抓取主页面 {i}/{len(urls)}: {url}")
                text = await crawler.get_webpage_text(url)
                
                if not text:
                    print(f"⚠️ 抓取失败: {url}")
                    continue

                f.write(f"## {i}. 主页面: {url}\n")
                f.write(f"**抓取时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # 提取页面中的链接
                link_pattern = re.compile(r'https?://[^\s<>"\']+')
                links = list(set(link_pattern.findall(text)))
                
                # 过滤相关链接（同域名或相关域名）
                domain_keywords = ['csrc', 'sse', 'szse', 'bse', 'gov.cn', 'com.cn']
                relevant_links = []
                for link in links:
                    if any(keyword in link.lower() for keyword in domain_keywords):
                        relevant_links.append(link)
                
                f.write(f"### 主页面内容摘要\n")
                f.write(f"发现 {len(relevant_links)} 个相关链接\n\n")
                
                # 爬取前5个相关链接的内容
                for j, link in enumerate(relevant_links[:5], 1):
                    print(f"  🔗 正在抓取子链接 {j}/5: {link}")
                    try:
                        link_text = await crawler.get_webpage_text(link)
                        if link_text:
                            f.write(f"#### 子链接 {j}: {link}\n")
                            f.write(f"**内容长度**: {len(link_text)} 字符\n")
                            # 只保存前1000字符作为摘要
                            f.write(f"**内容摘要**:\n```\n{link_text[:1000]}...\n```\n\n")
                            print(f"    ✅ 子链接内容: {len(link_text)} 字符")
                        else:
                            f.write(f"#### 子链接 {j}: {link}\n**抓取失败**\n\n")
                    except Exception as e:
                        print(f"    ❌ 子链接抓取出错: {e}")
                        f.write(f"#### 子链接 {j}: {link}\n**抓取出错**: {e}\n\n")
                
                f.write("---\n\n")

        print(f"🎉 文章及链接爬取完成，结果保存到: {output_file}")


if __name__ == "__main__":
    urls = [
        "http://www.csrc.gov.cn/",
        "https://www.sse.com.cn/",
        "https://www.szse.cn/index/index.html",
        "https://www.bse.cn/"
    ]
    
    print("选择爬取模式：")
    print("1. 爬取完整页面内容")
    print("2. 爬取页面及相关链接文章")
    
    # 默认选择模式1
    mode = 1
    
    if mode == 1:
        print("🔄 模式1：爬取完整页面内容")
        asyncio.run(crawl_full_articles(urls))
    elif mode == 2:
        print("🔄 模式2：爬取页面及相关链接文章")
        asyncio.run(crawl_articles_with_links(urls))

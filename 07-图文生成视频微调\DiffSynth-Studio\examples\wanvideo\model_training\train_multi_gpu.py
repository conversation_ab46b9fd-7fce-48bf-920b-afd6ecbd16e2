#!/usr/bin/env python3
"""
多卡并行训练脚本 for Wan2.1-I2V-14B-480P 模型
支持 DDP (Distributed Data Parallel) 训练
"""

import torch
import os
import json
import logging
import time
import argparse
from accelerate import Accelerator
from accelerate.utils import set_seed
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from diffsynth.trainers.utils import DiffusionTrainingModule, VideoDataset, ModelLogger, wan_parser

# 禁用 tokenizers 并行以避免死锁
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class WanTrainingModuleMultiGPU(DiffusionTrainingModule):
    """支持多GPU训练的 Wan 训练模块"""
    
    def __init__(
        self,
        model_paths=None, 
        model_id_with_origin_paths=None,
        trainable_models=None,
        lora_base_model=None, 
        lora_target_modules="q,k,v,o,ffn.0,ffn.2", 
        lora_rank=32,
        use_gradient_checkpointing=True,
        use_gradient_checkpointing_offload=False,
        extra_inputs=None,
        accelerator=None,
    ):
        super().__init__()
        self.accelerator = accelerator
        
        # 只在主进程中打印日志
        if self.accelerator is None or self.accelerator.is_main_process:
            logger.info("Initializing WanTrainingModuleMultiGPU...")
            logger.info(f"LoRA config: base_model={lora_base_model}, rank={lora_rank}, target_modules={lora_target_modules}")
            
        # 解析模型配置
        model_configs = []
        if model_paths is not None:
            model_paths = json.loads(model_paths)
            model_configs += [ModelConfig(path=path) for path in model_paths]
        if model_id_with_origin_paths is not None:
            model_id_with_origin_paths = model_id_with_origin_paths.split(",")
            model_configs += [
                ModelConfig(
                    model_id=i.split(":")[0], 
                    origin_file_pattern=i.split(":")[1]
                ) for i in model_id_with_origin_paths
            ]
        
        # 在CPU上初始化模型以避免GPU内存冲突
        if self.accelerator is None or self.accelerator.is_main_process:
            logger.info("Loading models on CPU to avoid GPU memory conflicts...")
        
        self.pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16, 
            device="cpu", 
            model_configs=model_configs
        )
        
        # 重置训练调度器
        self.pipe.scheduler.set_timesteps(1000, training=True)
        
        # 冻结不可训练的模型
        self.pipe.freeze_except([] if trainable_models is None else trainable_models.split(","))
        
        # 添加 LoRA 到基础模型
        if lora_base_model is not None:
            if self.accelerator is None or self.accelerator.is_main_process:
                logger.info(f"Adding LoRA to {lora_base_model}...")
            model = self.add_lora_to_model(
                getattr(self.pipe, lora_base_model),
                target_modules=lora_target_modules.split(","),
                lora_rank=lora_rank
            )
            setattr(self.pipe, lora_base_model, model)
            
        # 存储其他配置
        self.use_gradient_checkpointing = use_gradient_checkpointing
        self.use_gradient_checkpointing_offload = use_gradient_checkpointing_offload
        self.extra_inputs = extra_inputs.split(",") if extra_inputs is not None else []
        
        if self.accelerator is None or self.accelerator.is_main_process:
            logger.info("WanTrainingModuleMultiGPU initialization completed")
        
    def forward_preprocess(self, data):
        """预处理输入数据"""
        # CFG-sensitive parameters
        inputs_posi = {"prompt": data["prompt"]}
        inputs_nega = {}
        
        # CFG-unsensitive parameters
        inputs_shared = {
            "input_video": data["video"],
            "height": data["video"][0].size[1],
            "width": data["video"][0].size[0],
            "num_frames": len(data["video"]),
            "cfg_scale": 1,
            "tiled": False,
            "rand_device": self.pipe.device,
            "use_gradient_checkpointing": self.use_gradient_checkpointing,
            "use_gradient_checkpointing_offload": self.use_gradient_checkpointing_offload,
            "cfg_merge": False,
            "vace_scale": 1,
        }
        
        # 添加额外输入
        for extra_input in self.extra_inputs:
            if extra_input == "input_image":
                inputs_shared["input_image"] = data["video"][0]
            elif extra_input == "end_image":
                inputs_shared["end_image"] = data["video"][-1]
            else:
                inputs_shared[extra_input] = data[extra_input]
        
        # 管道单元自动处理输入参数
        for unit in self.pipe.units:
            inputs_shared, inputs_posi, inputs_nega = self.pipe.unit_runner(
                unit, self.pipe, inputs_shared, inputs_posi, inputs_nega
            )
        return {**inputs_shared, **inputs_posi}
    
    def forward(self, data, inputs=None):
        """前向传播"""
        if inputs is None: 
            inputs = self.forward_preprocess(data)
        models = {name: getattr(self.pipe, name) for name in self.pipe.in_iteration_models}
        loss = self.pipe.training_loss(**models, **inputs)
        return loss


def setup_training_environment(args, accelerator):
    """设置训练环境"""
    # 设置随机种子
    if args.seed is not None:
        set_seed(args.seed)
        if accelerator.is_main_process:
            logger.info(f"Set random seed to {args.seed}")
    
    # 创建输出目录
    if accelerator.is_main_process:
        os.makedirs(args.output_path, exist_ok=True)
        os.makedirs(os.path.join(args.output_path, "logs"), exist_ok=True)
        
        # 保存训练参数
        with open(os.path.join(args.output_path, "training_args.json"), "w") as f:
            json.dump(vars(args), f, indent=4)
        
        logger.info(f"Training arguments saved to {os.path.join(args.output_path, 'training_args.json')}")
    
    # 同步所有进程
    accelerator.wait_for_everyone()


def launch_multi_gpu_training(
    dataset, model, model_logger, optimizer, scheduler,
    num_epochs=1, gradient_accumulation_steps=1, accelerator=None
):
    """启动多GPU训练"""
    dataloader = torch.utils.data.DataLoader(
        dataset, 
        shuffle=True, 
        collate_fn=lambda x: x[0],
        batch_size=1,  # 视频数据通常使用batch_size=1
        num_workers=2,
        pin_memory=True
    )
    
    # 使用 accelerator 准备模型、优化器、数据加载器和调度器
    model, optimizer, dataloader, scheduler = accelerator.prepare(
        model, optimizer, dataloader, scheduler
    )
    
    if accelerator.is_main_process:
        logger.info(f"Starting training on {accelerator.num_processes} GPUs")
        logger.info(f"Total epochs: {num_epochs}")
        logger.info(f"Gradient accumulation steps: {gradient_accumulation_steps}")
        logger.info(f"Total steps per epoch: {len(dataloader)}")
    
    # 训练循环
    for epoch_id in range(num_epochs):
        if accelerator.is_main_process:
            logger.info(f"Starting epoch {epoch_id + 1}/{num_epochs}")
        
        epoch_start_time = time.time()
        total_loss = 0.0
        step_count = 0
        
        for step, data in enumerate(dataloader):
            with accelerator.accumulate(model):
                optimizer.zero_grad()
                loss = model(data)
                accelerator.backward(loss)
                optimizer.step()
                scheduler.step()
                
                total_loss += loss.item()
                step_count += 1
                
                # 记录损失
                model_logger.on_step_end(loss)
                
                # 定期打印进度
                if accelerator.is_main_process and step % 10 == 0:
                    avg_loss = total_loss / step_count
                    logger.info(f"Epoch {epoch_id + 1}, Step {step}, Avg Loss: {avg_loss:.6f}")
        
        # 保存检查点
        model_logger.on_epoch_end(accelerator, model, epoch_id)
        
        if accelerator.is_main_process:
            epoch_time = time.time() - epoch_start_time
            avg_loss = total_loss / step_count
            logger.info(f"Epoch {epoch_id + 1} completed in {epoch_time:.2f}s, Avg Loss: {avg_loss:.6f}")
    
    if accelerator.is_main_process:
        logger.info("Training completed successfully!")


def main():
    """主函数"""
    parser = wan_parser()
    
    # 添加多GPU训练相关参数
    parser.add_argument("--mixed_precision", type=str, default="bf16", 
                       choices=["no", "fp16", "bf16"], help="Mixed precision training")
    parser.add_argument("--seed", type=int, default=42, 
                       help="Random seed for reproducibility")
    parser.add_argument("--log_with", type=str, default="tensorboard",
                       choices=["tensorboard", "wandb", "comet_ml"], help="Logging backend")
    
    args = parser.parse_args()
    
    # 初始化 Accelerator
    accelerator = Accelerator(
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        mixed_precision=args.mixed_precision,
        log_with=args.log_with,
        project_dir=os.path.join(args.output_path, "logs"),
    )
    
    # 设置训练环境
    setup_training_environment(args, accelerator)
    
    # 打印训练信息
    if accelerator.is_main_process:
        logger.info("="*50)
        logger.info("Wan2.1-I2V-14B-480P Multi-GPU Training")
        logger.info("="*50)
        logger.info(f"Output directory: {args.output_path}")
        logger.info(f"Number of processes: {accelerator.num_processes}")
        logger.info(f"Distributed type: {accelerator.distributed_type}")
        logger.info(f"Mixed precision: {accelerator.mixed_precision}")
        logger.info(f"Device: {accelerator.device}")
    
    # 加载数据集
    if accelerator.is_main_process:
        logger.info("Loading dataset...")
    dataset = VideoDataset(args=args)
    
    # 初始化模型
    if accelerator.is_main_process:
        logger.info("Initializing model...")
    model = WanTrainingModuleMultiGPU(
        model_paths=args.model_paths,
        model_id_with_origin_paths=args.model_id_with_origin_paths,
        trainable_models=args.trainable_models,
        lora_base_model=args.lora_base_model,
        lora_target_modules=args.lora_target_modules,
        lora_rank=args.lora_rank,
        use_gradient_checkpointing_offload=args.use_gradient_checkpointing_offload,
        extra_inputs=args.extra_inputs,
        accelerator=accelerator,
    )
    
    # 初始化模型日志记录器
    model_logger = ModelLogger(
        args.output_path,
        remove_prefix_in_ckpt=args.remove_prefix_in_ckpt
    )
    
    # 初始化优化器和学习率调度器
    if accelerator.is_main_process:
        logger.info(f"Setting up optimizer with learning rate: {args.learning_rate}")
    optimizer = torch.optim.AdamW(model.trainable_modules(), lr=args.learning_rate)
    scheduler = torch.optim.lr_scheduler.ConstantLR(optimizer)
    
    # 启动多GPU训练
    if accelerator.is_main_process:
        logger.info(f"Starting multi-GPU training for {args.num_epochs} epochs...")
        start_time = time.time()
    
    launch_multi_gpu_training(
        dataset, model, model_logger, optimizer, scheduler,
        num_epochs=args.num_epochs,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        accelerator=accelerator
    )
    
    # 训练完成
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        total_time = time.time() - start_time
        logger.info("="*50)
        logger.info(f"Training completed in {total_time:.2f} seconds ({total_time/60:.2f} minutes)")
        logger.info(f"Model saved to {args.output_path}")
        logger.info("="*50)


if __name__ == "__main__":
    main()

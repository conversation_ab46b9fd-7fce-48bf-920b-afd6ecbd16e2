import asyncio
import re
from datetime import datetime
from pathlib import Path

from crawl_utils import CrawlUtils  # 确保 crawl_utils.py 中定义了 CrawlUtils 类


def extract_title_date_pairs(text: str):
    """
    从页面正文中提取“标题-发布时间”对。
    支持多种时间格式匹配，并根据标题关键词辅助判断。
    """
    title_date_pairs = []

    # 匹配形如 “xxxx标题xxxx 2025-07-07” 的内容
    pattern = re.compile(r'(?P<title>[\u4e00-\u9fa5a-zA-Z0-9，、：“”《》（）\-—_·【】]{5,100})[ \t\n\r\f\v]{0,10}'
                         r'(?P<date>\d{4}[-/年\.]\d{1,2}[-/月\.]\d{1,2}[日]?)')

    for match in pattern.finditer(text):
        title = match.group("title").strip()
        date_raw = match.group("date").strip()

        # 格式化日期
        date_clean = re.sub(r'[年月]', '-', date_raw)
        date_clean = re.sub(r'[日\.]', '', date_clean)
        try:
            date_parsed = datetime.strptime(date_clean, "%Y-%m-%d")
            date_str = date_parsed.strftime("%Y-%m-%d")
        except:
            date_str = date_clean

        title_date_pairs.append((title, date_str))

    return title_date_pairs


async def crawl_and_save(urls, output_file="crawl_result.md", save_full_content=False):
    """
    爬取所有链接并将提取的标题-时间对写入 markdown 文件
    如果 save_full_content=True，则保存完整页面内容
    """
    Path(output_file).unlink(missing_ok=True)

    async with CrawlUtils() as crawler:
        with open(output_file, "w", encoding="utf-8") as f:
            for url in urls:
                print(f"🕷 正在抓取: {url}")
                text = await crawler.get_webpage_text(url)
                if not text:
                    print(f"⚠️ 抓取失败: {url}")
                    continue

                f.write(f"\n## 🔗 {url}\n")

                if save_full_content:
                    # 保存完整页面内容
                    f.write(f"### 完整页面内容：\n")
                    f.write(f"```\n{text}\n```\n\n")
                    print(f"✅ 保存完整页面内容: {len(text)} 字符")
                else:
                    # 只提取标题-时间对
                    title_date_list = extract_title_date_pairs(text)

                    if not title_date_list:
                        f.write(f"未提取到标题-时间对。\n")
                        print(f"❗ 无有效标题时间内容：{url}")
                        continue

                    for title, date in title_date_list:
                        f.write(f"- {title} - {date}\n")

                    print(f"✅ 提取 {len(title_date_list)} 条记录并写入文件。")


if __name__ == "__main__":
    urls = [
        "http://www.csrc.gov.cn/",
        "https://www.sse.com.cn/",
        "https://www.szse.cn/index/index.html",
        "https://www.bse.cn/"
    ]

    # 选择保存模式：
    # False: 只保存标题-时间对 (默认)
    # True: 保存完整页面内容
    save_full_content = True  # 改为 True 来保存完整内容

    if save_full_content:
        output_file = "crawl_full_content.md"
        print("🔄 模式：保存完整页面内容")
    else:
        output_file = "crawl_result.md"
        print("🔄 模式：只提取标题-时间对")

    asyncio.run(crawl_and_save(urls, output_file, save_full_content))

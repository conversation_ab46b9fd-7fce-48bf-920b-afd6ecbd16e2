# 🎬 8×RTX 3090 Wan视频模型微调完整指南

本指南详细记录了在8×RTX 3090配置下进行DiffSynth-Studio Wan视频模型微调的完整过程。

## 📋 硬件配置

- **GPU**: 8 × NVIDIA GeForce RTX 3090
- **单卡显存**: 24GB
- **总显存**: 192GB
- **CUDA版本**: 12.6
- **Python版本**: 3.12.11

## 🚀 环境搭建过程

### Step 1: 创建Conda虚拟环境

```bash
# 创建Python 3.12环境
conda create -n wan_video_env python=3.12 -y

# 激活环境
conda activate wan_video_env
```

### Step 2: 安装核心依赖

```bash
# 设置代理（如需要）
export http_proxy="*********************************************"
export https_proxy="*********************************************"

# 安装DiffSynth-Studio
pip install -e .

# 安装训练相关依赖
pip install deepspeed peft
```

### Step 3: 下载示例数据集

```bash
# 下载官方示例数据集
modelscope download --dataset DiffSynth-Studio/example_video_dataset --local_dir ./data/example_video_dataset
```

## 🧪 环境验证

运行环境测试脚本：

```bash
python test_training_setup.py
```

**测试结果**:
- ✅ 8×RTX 3090 GPU检测通过
- ✅ 总显存189.5GB
- ✅ 所有依赖包安装正确
- ✅ 数据集准备完成
- ✅ 多GPU环境测试通过

## 🎯 训练配置优化

### 针对不同模型的批次大小配置

| 模型类型 | 模型大小 | 每GPU批次 | 梯度累积 | 总批次大小 | 预估训练时间 |
|---------|---------|----------|----------|-----------|-------------|
| T2V-1.3B | 1.3B | 4 | 2 | 64 | 30分钟(LoRA) |
| T2V-14B | 14B | 1 | 4 | 32 | 90分钟(LoRA) |
| I2V-14B-480P | 14B | 1 | 4 | 32 | 90分钟(LoRA) |

### Accelerate配置

自动生成的配置文件位于: `~/.cache/huggingface/accelerate/default_config.yaml`

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 8
rdzv_backend: static
same_network: true
use_cpu: false
```

## 🚀 开始训练

### 方法1: 使用优化脚本（推荐）

```bash
# T2V 1.3B LoRA训练（推荐新手）
python train_8x3090_optimized.py --model t2v_1.3b --training-type lora

# T2V 14B LoRA训练
python train_8x3090_optimized.py --model t2v_14b --training-type lora

# I2V 14B LoRA训练
python train_8x3090_optimized.py --model i2v_14b_480p --training-type lora

# 仅设置环境不开始训练
python train_8x3090_optimized.py --setup-only
```

### 方法2: 手动执行训练脚本

```bash
# 激活环境
conda activate wan_video_env

# 设置GPU
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

# 执行训练
accelerate launch \
    --config_file ~/.cache/huggingface/accelerate/default_config.yaml \
    --num_processes 8 \
    --mixed_precision bf16 \
    examples/wanvideo/model_training/train.py \
    --dataset_base_path "./data/example_video_dataset" \
    --dataset_metadata_path "./data/example_video_dataset/metadata.csv" \
    --height 480 \
    --width 832 \
    --dataset_repeat 100 \
    --model_id_with_origin_paths "Wan-AI/Wan2.1-T2V-1.3B:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-T2V-1.3B:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-T2V-1.3B:Wan2.1_VAE.pth" \
    --learning_rate 1e-4 \
    --num_epochs 3 \
    --per_device_train_batch_size 4 \
    --gradient_accumulation_steps 2 \
    --remove_prefix_in_ckpt "pipe.dit." \
    --output_path "./models/train/t2v_1.3b_lora_8x3090" \
    --lora_base_model "dit" \
    --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
    --lora_rank 64 \
    --lora_alpha 128 \
    --save_steps 100 \
    --logging_steps 10 \
    --dataloader_num_workers 4 \
    --gradient_checkpointing \
    --use_8bit_adam
```

## 📊 性能优化建议

### 显存优化

1. **使用bf16混合精度**: 减少50%显存使用
2. **梯度检查点**: 以计算换显存
3. **8bit Adam优化器**: 减少优化器状态显存
4. **适当的批次大小**: 避免OOM

### 训练速度优化

1. **多GPU并行**: 8卡并行训练
2. **数据加载优化**: 4个worker并行加载
3. **合适的学习率**: 1e-4适合LoRA训练
4. **梯度累积**: 增加有效批次大小

## 🔧 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减少批次大小
   --per_device_train_batch_size 2
   
   # 启用梯度检查点
   --gradient_checkpointing
   ```

2. **模型下载失败**
   ```bash
   # 清理损坏的下载
   rm -rf ./models/Wan-AI/Wan2.1-T2V-1.3B
   
   # 重新下载
   python -c "from modelscope import snapshot_download; snapshot_download('Wan-AI/Wan2.1-T2V-1.3B', cache_dir='./models')"
   ```

3. **多GPU同步问题**
   ```bash
   # 检查NCCL环境
   export NCCL_DEBUG=INFO
   
   # 使用单GPU测试
   CUDA_VISIBLE_DEVICES=0 python train.py ...
   ```

## 📈 训练监控

### 关键指标

- **Loss**: 训练损失应该稳定下降
- **GPU利用率**: 应该接近100%
- **显存使用**: 建议80-90%
- **训练速度**: 每步时间应该稳定

### 日志查看

```bash
# 实时查看训练日志
tail -f ./models/train/*/logs/training.log

# 查看GPU使用情况
nvidia-smi -l 1
```

## 🎯 训练完成后

### 模型验证

```bash
# 使用推理测试脚本
python test_wan_inference.py --test-type lora --lora-path ./models/train/t2v_1.3b_lora_8x3090
```

### 模型部署

训练完成的LoRA模型可以与基础模型结合使用：

```python
from diffsynth.pipelines.wan_video_new import WanVideoPipeline

# 加载基础模型
pipe = WanVideoPipeline.from_pretrained(...)

# 加载LoRA权重
pipe.dit.load_lora("./models/train/t2v_1.3b_lora_8x3090")

# 生成视频
video = pipe(prompt="your prompt here")
```

## 📚 相关文件

- `train_8x3090_optimized.py`: 8×RTX 3090优化训练脚本
- `test_training_setup.py`: 环境测试脚本
- `test_wan_inference.py`: 推理测试脚本
- `start_wan_training.py`: 通用训练启动器

## 🎉 总结

通过本指南，您已经成功：

1. ✅ 搭建了完整的Python 3.12 + Conda环境
2. ✅ 安装了所有必需的依赖包
3. ✅ 配置了8×RTX 3090多GPU训练环境
4. ✅ 下载了示例数据集
5. ✅ 验证了环境配置
6. ✅ 准备了优化的训练脚本

您的8×RTX 3090配置非常强大，可以：
- 快速训练1.3B模型（30分钟LoRA）
- 高效训练14B模型（90分钟LoRA）
- 支持全量训练大型模型
- 处理高分辨率视频生成任务

现在您可以开始探索Wan视频模型的强大功能了！🚀

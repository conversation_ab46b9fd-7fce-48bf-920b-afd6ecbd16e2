#!/usr/bin/env python3
"""
新字段爬虫 - 使用优化的字段结构爬取政府采购数据
"""

from enhanced_procurement_crawler import EnhancedProcurementCrawler
import json
import time
from datetime import datetime

class NewFieldCrawler:
    """新字段结构爬虫"""
    
    def __init__(self):
        self.crawler = EnhancedProcurementCrawler()
        self.results = []
    
    def extract_optimized_fields(self, list_data, detail_info):
        """提取优化的字段结构"""
        
        # 基本信息
        basic_info = {
            '序号': 0,  # 将在后面设置
            '标案名称': list_data.get('title', ''),
            '机关名称': list_data.get('agency', ''),
            '标案案号': detail_info.announcement_info.case_number if detail_info else '',
            '招标日期': list_data.get('tender_date', ''),
            '决标日期': list_data.get('award_date', ''),
            '标案类型': list_data.get('tender_type', '')
        }
        
        # 机关详细信息
        agency_detail = {}
        if detail_info and detail_info.agency_info:
            agency_detail = {
                '机关代码': detail_info.agency_info.agency_code,
                '单位名称': detail_info.agency_info.unit_name,
                '机关地址': detail_info.agency_info.agency_address,
                '联络人': detail_info.agency_info.contact_person,
                '联络电话': detail_info.agency_info.contact_phone,
                '传真号码': detail_info.agency_info.fax_number
            }
        else:
            agency_detail = {
                '机关代码': '',
                '单位名称': '',
                '机关地址': '',
                '联络人': '',
                '联络电话': '',
                '传真号码': ''
            }
        
        # 招标信息
        tender_info = {}
        if detail_info and detail_info.announcement_info:
            tender_info = {
                '招标方式': detail_info.announcement_info.tender_method,
                '决标方式': detail_info.announcement_info.award_method,
                '公告日期': detail_info.announcement_info.announcement_date
            }
        else:
            tender_info = {
                '招标方式': '',
                '决标方式': '',
                '公告日期': ''
            }
        
        # 时间信息
        time_info = {}
        if detail_info and detail_info.time_info:
            time_info = {
                '开标时间': detail_info.time_info.opening_time,
                '原公告日期': detail_info.time_info.original_announcement_date
            }
        else:
            time_info = {
                '开标时间': '',
                '原公告日期': ''
            }
        
        # 金额信息
        amount_info = {}
        if detail_info and detail_info.amount_info:
            amount_info = {
                '预算金额': detail_info.amount_info.budget_amount,
                '预算金额中文': detail_info.amount_info.budget_amount_chinese,
                '采购金额级距': detail_info.amount_info.procurement_amount_range,
                '预算金额是否公开': detail_info.amount_info.budget_public
            }
        else:
            amount_info = {
                '预算金额': '',
                '预算金额中文': '',
                '采购金额级距': '',
                '预算金额是否公开': ''
            }
        
        # 履约信息
        performance_info = {}
        if detail_info and detail_info.performance_info:
            performance_info = {
                '履约地点': detail_info.performance_info.performance_location,
                '履约地区': detail_info.performance_info.performance_region,
                '是否受机关补助': detail_info.performance_info.government_subsidy
            }
        else:
            performance_info = {
                '履约地点': '',
                '履约地区': '',
                '是否受机关补助': ''
            }
        
        # 厂商信息
        vendor_info = {
            '投标厂商数': detail_info.bidder_count if detail_info else 0,
            '厂商详情': []
        }
        
        if detail_info and detail_info.vendors:
            for vendor in detail_info.vendors:
                vendor_detail = {
                    '厂商名称': vendor.vendor_name,
                    '厂商代码': vendor.vendor_code,
                    '是否得标': vendor.is_winner,
                    '决标金额': vendor.award_amount,
                    '组织型态': vendor.organization_type,
                    '厂商地址': vendor.vendor_address,
                    '厂商电话': vendor.vendor_phone,
                    '履约期间': vendor.performance_period,
                    '是否为中小企业': vendor.is_sme,
                    '得标厂商国别': vendor.winner_country
                }
                vendor_info['厂商详情'].append(vendor_detail)
        
        # 获取得标厂商和金额（用于快速查看）
        winner_vendor = ''
        winner_amount = ''
        if detail_info and detail_info.vendors:
            for vendor in detail_info.vendors:
                if vendor.is_winner == '是':
                    winner_vendor = vendor.vendor_name
                    winner_amount = vendor.award_amount
                    break
        
        vendor_info['得标厂商'] = winner_vendor
        vendor_info['得标金额'] = winner_amount
        
        # 其他信息
        other_info = {
            '标的分类': detail_info.subject_classification.classification_name if detail_info else '',
            '详情页链接': list_data.get('detail_url', ''),
            '爬取时间': datetime.now().isoformat()
        }
        
        # 合并所有信息
        complete_record = {}
        complete_record.update(basic_info)
        complete_record.update(agency_detail)
        complete_record.update(tender_info)
        complete_record.update(time_info)
        complete_record.update(amount_info)
        complete_record.update(performance_info)
        complete_record.update(vendor_info)
        complete_record.update(other_info)
        
        return complete_record
    
    def crawl_with_new_fields(self, keyword="国防部", 
                             tender_status="决标",
                             year="111",
                             max_pages=3,
                             page_size=50):
        """使用新字段结构爬取数据"""
        
        print(f"🚀 开始使用新字段结构爬取数据...")
        print(f"🔍 搜索条件：关键词='{keyword}', 状态='{tender_status}', 年份={year}年")
        print(f"📄 最大页数: {max_pages}, 每页数量: {page_size}")
        
        # 设置搜索参数
        search_params = {
            'querySentence': keyword,
            'tenderStatusType': tender_status,
            'sortCol': 'AWARD_NOTICE_DATE',
            'timeRange': year,
            'pron': 'true',
            'fuzzy': 'true',
            'pageSize': str(page_size)
        }
        
        all_list_results = []
        
        # 第一步：爬取列表页数据
        print(f"\n📊 === 第一步：爬取列表页数据 ===")
        
        for page in range(1, max_pages + 1):
            print(f"\n📄 正在爬取第 {page} 页...")
            
            search_params['pageIndex'] = str(page)
            
            try:
                search_response = self.crawler._make_request(
                    self.crawler.search_action_url, 
                    method='GET', 
                    data=search_params
                )
                
                page_results = self.crawler._parse_search_results(search_response.content)
                
                if not page_results:
                    print(f"⚠️ 第 {page} 页没有数据，停止爬取")
                    break
                
                print(f"✅ 第 {page} 页获取到 {len(page_results)} 笔数据")
                all_list_results.extend(page_results)
                
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ 爬取第 {page} 页时发生错误: {str(e)}")
                break
        
        print(f"\n✅ 列表页爬取完成，共获取 {len(all_list_results)} 笔数据")
        
        # 第二步：爬取详细页数据并使用新字段结构
        print(f"\n🔍 === 第二步：爬取详细页数据（新字段结构） ===")
        
        new_field_results = []
        
        for i, list_item in enumerate(all_list_results):
            current_num = i + 1
            print(f"\n🔍 正在处理第 {current_num}/{len(all_list_results)} 笔数据...")
            print(f"📋 标案名称: {list_item.get('title', '')[:50]}...")
            
            detail_info = None
            if list_item.get('detail_url'):
                try:
                    detail_info = self.crawler.extract_detail_fields(list_item['detail_url'])
                    if detail_info:
                        print(f"  ✅ 成功获取详细数据")
                    else:
                        print(f"  ⚠️ 详细数据为空")
                except Exception as e:
                    print(f"  ❌ 获取详细数据失败: {str(e)}")
            else:
                print(f"  ⚠️ 没有详细页链接")
            
            # 使用新字段结构提取数据
            optimized_record = self.extract_optimized_fields(list_item, detail_info)
            optimized_record['序号'] = current_num
            
            new_field_results.append(optimized_record)
            
            # 显示关键信息
            print(f"  📊 机关: {optimized_record.get('机关名称', '')}")
            print(f"  📄 案号: {optimized_record.get('标案案号', '')}")
            print(f"  💰 预算: {optimized_record.get('预算金额', '')}")
            print(f"  🏆 得标厂商: {optimized_record.get('得标厂商', '')}")
            
            # 每10笔保存一次临时文件
            if current_num % 10 == 0:
                temp_file = f'temp_new_field_data_{current_num}.json'
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(new_field_results, f, ensure_ascii=False, indent=2)
                print(f"  💾 已临时保存 {current_num} 笔数据")
            
            time.sleep(3)  # 避免被封
        
        # 保存最终结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f'new_field_procurement_data_{keyword}_{year}年_{timestamp}.json'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(new_field_results, f, ensure_ascii=False, indent=2)
        
        # 生成统计信息
        self.generate_crawl_statistics(new_field_results, output_file, keyword, year)
        
        # 清理临时文件
        import os
        for temp_file in os.listdir('.'):
            if temp_file.startswith('temp_new_field_data_') and temp_file.endswith('.json'):
                try:
                    os.remove(temp_file)
                    print(f"🗑️ 已清理临时文件: {temp_file}")
                except:
                    pass
        
        return new_field_results, output_file
    
    def generate_crawl_statistics(self, results, output_file, keyword, year):
        """生成爬取统计信息"""
        
        print(f"\n📊 === 爬取完成统计 ===")
        print(f"🔍 搜索关键词: {keyword}")
        print(f"📅 年份范围: {year}年")
        print(f"📋 总数据量: {len(results)} 笔")
        print(f"💾 数据文件: {output_file}")
        
        # 统计字段完整度
        field_stats = {}
        for record in results:
            for field, value in record.items():
                if field not in field_stats:
                    field_stats[field] = 0
                if value and str(value).strip() and value != '无':
                    field_stats[field] += 1
        
        print(f"\n📈 字段完整度统计:")
        for field, count in sorted(field_stats.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / len(results)) * 100
            print(f"  {field}: {count}/{len(results)} ({percentage:.1f}%)")
        
        # 统计机关分布
        agencies = {}
        for record in results:
            agency = record.get('机关名称', '')
            if agency:
                agencies[agency] = agencies.get(agency, 0) + 1
        
        print(f"\n🏢 机关分布:")
        for agency, count in sorted(agencies.items(), key=lambda x: x[1], reverse=True):
            print(f"  {agency}: {count} 笔")
        
        # 统计有预算金额的数据
        budget_count = sum(1 for r in results if r.get('预算金额'))
        print(f"\n💰 预算信息: {budget_count}/{len(results)} 笔有预算金额")
        
        # 统计有得标厂商的数据
        winner_count = sum(1 for r in results if r.get('得标厂商'))
        print(f"🏆 得标信息: {winner_count}/{len(results)} 笔有得标厂商")
        
        # 保存统计报告
        stats_report = {
            'crawl_time': datetime.now().isoformat(),
            'search_keyword': keyword,
            'year': year,
            'total_records': len(results),
            'field_completeness': field_stats,
            'agency_distribution': agencies,
            'budget_info_count': budget_count,
            'winner_info_count': winner_count,
            'output_file': output_file
        }
        
        stats_file = f'new_field_crawl_stats_{keyword}_{year}年_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats_report, f, ensure_ascii=False, indent=2)
        
        print(f"📊 统计报告已保存到: {stats_file}")

def main():
    """主函数"""
    print("🎯 === 新字段结构政府采购爬虫 ===")
    
    # 创建爬虫实例
    crawler = NewFieldCrawler()
    
    # 开始爬取
    results, output_file = crawler.crawl_with_new_fields(
        keyword="国防部",
        tender_status="决标", 
        year="111",
        max_pages=2,  # 爬取2页
        page_size=20  # 每页20笔
    )
    
    print(f"\n🎉 === 爬取完成 ===")
    print(f"✅ 成功爬取 {len(results)} 笔数据")
    print(f"💾 数据已保存到: {output_file}")
    print(f"📊 使用了优化的字段结构，包含完整的厂商详情")

if __name__ == "__main__":
    main()

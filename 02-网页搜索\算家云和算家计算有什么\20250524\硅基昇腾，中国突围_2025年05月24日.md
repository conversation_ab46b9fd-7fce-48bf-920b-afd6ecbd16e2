﻿# 硅基昇腾，中国突围

**发布日期**: 2025年05月24日

**原文链接**: https://china.qianlong.com/2025/0524/8493262.shtml

## 📄 原文内容

2025年1月15日，中国AI大模型DeepSeek R1刚一问世，便震动全球。

针对DeepSeek，全世界展开了一场龙争虎斗。

可仅仅2周之后， 2月1日（大年初四），华为云就联合硅基流动基于昇腾云推出DeepSeek R1/V3，引爆全网。

从模型到算力，从引擎到框架，全面实现国产化。

这其中，DeepSeek和华为自不用说。硅基流动作为一家AI Infra（AI基础设施）企业，则是清华博士袁进辉2023年刚创立的创业公司。

其实，早在DeepSeek V3发布前一个月，DeepSeek创始人梁文锋就找到袁进辉，问他要不要部署？

袁进辉算了一笔账：动用80台英伟达H800服务器，单月花费五六百万，风险很大。

但眼见着DeepSeek越来越火爆，全世界的AI团队都争分夺秒，连英伟达也亲自下场。

时间不等人，硅基流动火速找到华为云。双方一拍即合，决心大干一场，希望能用国产算力率先搞定部署难题。

于是，双方经过彻夜不息的努力，终于在2月推出基于昇腾云与硅基流动推理加速引擎的DeepSeek，成为国内第一个成功部署DeepSeek服务的企业。

仅2月，硅基流动网站访问量暴增40倍，冲上中国AI网站排行榜第6、全球AI网站增长榜第2。

据华为云技术负责人王磊回忆，硅基流动DeepSeek刚上线，第一波流量超乎想象。他不得不四处腾挪，紧急调拨2000多张昇腾910B卡驰援，才勉强扛住。

但第一波“洪峰”之后，更大的流量爆发了。

这一次，不得不每次1000卡地往上加，不计上限地调配算力，才勉强扛住。

但这一波之后呢？未来的流量，还会爆发到怎样的程度？

于是，华为云找到袁进辉，亮出当时还秘不示人的“大杀器” ——CloudMatrix 384超节点。

2024年3月，英伟达首发NVL72超节点，一度震惊世界。

传统AI服务器里，一张计算卡仅能容纳8块GPU；但NVL72超节点，能将72块GPU组成一台超级AI服务器，令AI算力和通讯速度实现飙升。

所以， 超节点是一种将GPU高度集成的AI服务器“黑科技”。

谁也没想到，华为云这么快就搞出了CloudMatrix 384超节点。

而且，华为云超节点的昇腾卡互联数量飙升到384张，远超英伟达NVL72的72卡。

这是中国AI的算力之巅，更是前所未有的应用挑战。

当时，袁进辉坦承对CloudMatrix 384怀有疑虑：

第一，DeepSeek所需的大规模专家并行，要求多卡之间实现低延迟、高协同，并使用All-to-All通信。

但即便是英伟达，对All-to-All的支持也相当乏力。

CloudMatrix 384行不行？没有人知道。

第二，英伟达NVL72超节点，采用铜连接；CloudMatrix 384却采用光模块。区别在哪呢？

光模块通信具备更高带宽和更低时延，适合大容量、长距离传输；此外光网络架构简化，空间和功耗节省显著，且扩展性更强。但光模块最大的问题就在于故障率高。

这个超高难度的连接方式，到底行不行？没有人知道。

尽管充满疑虑，但袁进辉选择“信华为”：

“华为不仅是打过硬仗的团队，更创造了很多很多的奇迹。”

实际上，袁进辉的疑虑，也正是腾建军所担心的。

2023年2月，一场骤然爆发的电源浪涌，席卷新加坡数据中心，多家云厂商和数据中心客户受影响。

当时，新加坡华为云和微软云恰好在同一数据中心。唯一不同的是，电源浪涌爆发后，华为云的AI for DC（Data Center），迅速“感知”到电源浪涌引发的高温，自动触发应急预警。

作为资深专家，腾建军和团队迅速判断出，这将是一场全局危机。

1分钟发现故障，3分钟建立作战室进行统一指挥，1小时内启动干冰应急计划……

腾建军率领团队头戴防毒面罩、手挑干冰，冲进现场给服务器物理降温，生生扛住这波突袭，确保了华为云稳定运行。

江湖传言，这一天，新加坡的干冰被华为云直接搬空。

微软云在内部温度骤升、短暂抵抗之后，关闭了服务器，中断了云服务。

微软云客户甚至是在Twitter上，才得知自家业务猛然宕机，被打了个措手不及。

但智算超节点时代，对数据中心的要求更加苛刻。

在腾建军眼中，CloudMatrix 384超节点要在物理上真正落地，数据中心要解决的是一连串实打实的难题。

因为数据中心，是“智算超节点产品”不可分割的一部分。

传统数据中心，供电只做到8-10千瓦/机柜；但为了驱动CloudMatrix 384，仅供电就要飙升到50千瓦甚至更高，怎么办？

那就突破标准，超前技术准备、超前建设。

散热上，一套CloudMatrix 384横跨16个机柜，热量密度飙升，必须构建一套精密的液冷散热系统。

这其中，仅铲齿散热器的缝隙，就堪比发丝般精细。一旦散热液洁净度出现些许问题，或是不可避免滋生细菌微生物，就很可能将铲齿散热器堵死，不可避免导致大规模宕机。

一方面，用AI for DC提前预警；另一方面，通过物理+化学的新方法长效杀菌，实现数学、物理到生物、化学的跨界研究，引领一场液冷革命。

而这些技术，早在几年前就在华为云得到规模应用，展现出技术超前投入的前瞻价值。

但 光模块 ，差点成了腾建军“过不去的坎”。

实际上，华为在光通信领域早已世界领先。

2020年，华为全球首发800G超高速光模块，独步全球；2025年，华为再发1.6T硅光模块，在800G基础上再翻一倍。

至此，业界已无人得见华为光通信的“车尾灯”。

强大的技术积淀，让华为云用光模块构建超节点，而非采用铜连接，成为一种必然。

可真枪实弹干起来才发现，困难比预想的大得多。

实测中，腾建军发现： 光链路的闪断太频繁了，结果就是——完全不能用！

当时，CloudMatrix 384赫然被列入华为重点密级项目，是必须强渡的“大渡河”。加上单卡性能不如人，被逼得只能闯光模块这“华山一条路”。真被卡死，满盘皆输。

但那段时间，腾建军对团队说的最多的话却是：

于是，一边内部想办法，一边外界请高人，同时，只能用最笨的办法把难题列出来，一条条去攻克。

终于，腾建军团队发现：八成以上问题，出在光模块几乎肉眼不可见的脏污上，严重影响了通讯质量；而脏污的产生，就在数据中心现场超节点内网互连的安装环节。

于是，团队打造出针对光模块故障的定位和修复系统，通过数字化平台上的专家经验库和现场光模块的故障现象进行对接，实现了问题快速定位、故障迅速处置。

这一次，CloudMatrix 384再上线，速率、稳定性大幅提高。

作为全球首次大规模动用光模块部署超节点的探索，华为云创造了“奇迹”。

这一切，都成为硅基流动基于CloudMatrix 384再次爆发的坚实根基。

2月底，当强大的CloudMatrix 384呈现在硅基流动团队面前时，所有人面对的，却是一场新的长征。

3月初，硅基流动DeepSeek服务在超节点上刚跑通，吞吐量只有320 Tokens/秒，低到令人难以置信。

一台算力怪兽，表现如此拉胯，问题出在哪？没有人知道。

实际上，普通人很难想象，大模型内星辰般的宏大：

它的参数，高达数千亿级；它的神经网络层级，成百上千；每个算子（神经网络中具备特定功能的算法节点）背后，连接着不计其数的分布式系统；它的结果，则由成百上千张GPU协同计算输出，充满概率偶然。

要在这其中找出问题，仿佛是在一座亚马逊雨林中通缉一只细菌。

很快，王磊发动“超能力”，开始全公司“摇人”。

从底层做芯片的、做存储的、做计算的，到上层做算子的、做推理的、做平台的……王磊竭尽所能，几乎把技术栈上的所有团队拉到现场，跟硅基流动团队协同办公、攻坚克难。

协议有问题，马上改协议；算子有问题，马上改算子……围绕硅基流动DeepSeek服务上的超节点大计，华为上百人的团队，昼夜攻关不息。

而对华为云团队来说，冗长的测试，更是一个永不停歇的“西西弗神话”。

通信有瓶颈，优化通信；但接着，计算问题又冒出来了；于是，优化计算后，GPU、NPU太快，CPU不匹配……

当整个技术栈全部优化一遍后，王磊猛然发现，问题又从最初的源头，再次冒了出来。

但就是在这种重复、重复、再重复的优化过程中，CloudMatrix 384，终于被托举到一个不可思议的新高度。

4月10日，华为云生态大会，硅基流动创始人袁进辉郑重宣布：

基于CloudMatrix 384的DeepSeek-R1在保证单用户 20 TPS水平前提下，单卡 Decode吞吐量突破 1920 Tokens/秒。

相较英伟达H100，性能追平；相较3月初的CloudMatrix 384，性能提升6倍；相较昇腾910B单卡，性能飙升10倍。

这背后， CloudMatrix 384还实现了性能倍增（训推提升20%）、以存强算（吞吐量提升100%）、MoE亲和（千亿MoE提升3X）、长稳可靠（长稳运行40天不中断）、朝推夜训（算力利用率提升30%） 、即开即用 六大特性。

所谓MoE，即DeepSeek中的“混合专家模型”。DeepSeek之所以功能超强，在于其会内置多个“专家”处理问题。而在硬件层面，通常1枚芯片对应1个“专家”，于是芯片越多，效率越高、性能越强。

而超节点集成了384张昇腾卡，极大优化了MoE能力。

这不仅令DeepSeek性能大爆发，更展现出华为云在AI技术上的前瞻布局。

今天，在华为云三大云核心枢纽（贵州贵安、内蒙古乌兰察布、安徽芜湖），CloudMatrix 384实现全面布局，成为国内唯一正式商用的大规模超节点。

强大的算力网，令万卡级服务，分分钟即可开启。

这不仅是华为云的自我超越，也不仅是部分指标上对英伟达的超越，更大的意义，在于中国AI正呈现出体系化的突破式创新。

用袁进辉的话说： 美国试图像“三体人”一样，用芯片锁死中国科技。

但6年抗争过去，向死而生的华为，却越挫越勇。

当历经了鸿蒙蛰伏、海思攻坚、昇腾崛起……凭借华为云CloudMatrix 384超节点，一道坚不可摧的国产算力防线正在构建。

AI长跑没 有终点，突破，也不会有终点。但中国AI，一定会迎来自己的“奇点”时刻。 （应受访者要求，文中王磊、腾建军为化名）

2025年1月15日，中国AI大模型DeepSeek R1刚一问世，便震动全球。

针对DeepSeek，全世界展开了一场龙争虎斗。

可仅仅2周之后， 2月1日（大年初四），华为云就联合硅基流动基于昇腾云推出DeepSeek R1/V3，引爆全网。

从模型到算力，从引擎到框架，全面实现国产化。

这其中，DeepSeek和华为自不用说。硅基流动作为一家AI Infra（AI基础设施）企业，则是清华博士袁进辉2023年刚创立的创业公司。

其实，早在DeepSeek V3发布前一个月，DeepSeek创始人梁文锋就找到袁进辉，问他要不要部署？

袁进辉算了一笔账：动用80台英伟达H800服务器，单月花费五六百万，风险很大。

但眼见着DeepSeek越来越火爆，全世界的AI团队都争分夺秒，连英伟达也亲自下场。

时间不等人，硅基流动火速找到华为云。双方一拍即合，决心大干一场，希望能用国产算力率先搞定部署难题。

于是，双方经过彻夜不息的努力，终于在2月推出基于昇腾云与硅基流动推理加速引擎的DeepSeek，成为国内第一个成功部署DeepSeek服务的企业。

仅2月，硅基流动网站访问量暴增40倍，冲上中国AI网站排行榜第6、全球AI网站增长榜第2。

据华为云技术负责人王磊回忆，硅基流动DeepSeek刚上线，第一波流量超乎想象。他不得不四处腾挪，紧急调拨2000多张昇腾910B卡驰援，才勉强扛住。

但第一波“洪峰”之后，更大的流量爆发了。

这一次，不得不每次1000卡地往上加，不计上限地调配算力，才勉强扛住。

但这一波之后呢？未来的流量，还会爆发到怎样的程度？

于是，华为云找到袁进辉，亮出当时还秘不示人的“大杀器” ——CloudMatrix 384超节点。

2024年3月，英伟达首发NVL72超节点，一度震惊世界。

传统AI服务器里，一张计算卡仅能容纳8块GPU；但NVL72超节点，能将72块GPU组成一台超级AI服务器，令AI算力和通讯速度实现飙升。

所以， 超节点是一种将GPU高度集成的AI服务器“黑科技”。

谁也没想到，华为云这么快就搞出了CloudMatrix 384超节点。

而且，华为云超节点的昇腾卡互联数量飙升到384张，远超英伟达NVL72的72卡。

这是中国AI的算力之巅，更是前所未有的应用挑战。

当时，袁进辉坦承对CloudMatrix 384怀有疑虑：

第一，DeepSeek所需的大规模专家并行，要求多卡之间实现低延迟、高协同，并使用All-to-All通信。

但即便是英伟达，对All-to-All的支持也相当乏力。

CloudMatrix 384行不行？没有人知道。

第二，英伟达NVL72超节点，采用铜连接；CloudMatrix 384却采用光模块。区别在哪呢？

光模块通信具备更高带宽和更低时延，适合大容量、长距离传输；此外光网络架构简化，空间和功耗节省显著，且扩展性更强。但光模块最大的问题就在于故障率高。

这个超高难度的连接方式，到底行不行？没有人知道。

尽管充满疑虑，但袁进辉选择“信华为”：

“华为不仅是打过硬仗的团队，更创造了很多很多的奇迹。”

实际上，袁进辉的疑虑，也正是腾建军所担心的。

2023年2月，一场骤然爆发的电源浪涌，席卷新加坡数据中心，多家云厂商和数据中心客户受影响。

当时，新加坡华为云和微软云恰好在同一数据中心。唯一不同的是，电源浪涌爆发后，华为云的AI for DC（Data Center），迅速“感知”到电源浪涌引发的高温，自动触发应急预警。

作为资深专家，腾建军和团队迅速判断出，这将是一场全局危机。

1分钟发现故障，3分钟建立作战室进行统一指挥，1小时内启动干冰应急计划……

腾建军率领团队头戴防毒面罩、手挑干冰，冲进现场给服务器物理降温，生生扛住这波突袭，确保了华为云稳定运行。

江湖传言，这一天，新加坡的干冰被华为云直接搬空。

微软云在内部温度骤升、短暂抵抗之后，关闭了服务器，中断了云服务。

微软云客户甚至是在Twitter上，才得知自家业务猛然宕机，被打了个措手不及。

但智算超节点时代，对数据中心的要求更加苛刻。

在腾建军眼中，CloudMatrix 384超节点要在物理上真正落地，数据中心要解决的是一连串实打实的难题。

因为数据中心，是“智算超节点产品”不可分割的一部分。

传统数据中心，供电只做到8-10千瓦/机柜；但为了驱动CloudMatrix 384，仅供电就要飙升到50千瓦甚至更高，怎么办？

那就突破标准，超前技术准备、超前建设。

散热上，一套CloudMatrix 384横跨16个机柜，热量密度飙升，必须构建一套精密的液冷散热系统。

这其中，仅铲齿散热器的缝隙，就堪比发丝般精细。一旦散热液洁净度出现些许问题，或是不可避免滋生细菌微生物，就很可能将铲齿散热器堵死，不可避免导致大规模宕机。

一方面，用AI for DC提前预警；另一方面，通过物理+化学的新方法长效杀菌，实现数学、物理到生物、化学的跨界研究，引领一场液冷革命。

而这些技术，早在几年前就在华为云得到规模应用，展现出技术超前投入的前瞻价值。

但 光模块 ，差点成了腾建军“过不去的坎”。

实际上，华为在光通信领域早已世界领先。

2020年，华为全球首发800G超高速光模块，独步全球；2025年，华为再发1.6T硅光模块，在800G基础上再翻一倍。

至此，业界已无人得见华为光通信的“车尾灯”。

强大的技术积淀，让华为云用光模块构建超节点，而非采用铜连接，成为一种必然。

可真枪实弹干起来才发现，困难比预想的大得多。

实测中，腾建军发现： 光链路的闪断太频繁了，结果就是——完全不能用！

当时，CloudMatrix 384赫然被列入华为重点密级项目，是必须强渡的“大渡河”。加上单卡性能不如人，被逼得只能闯光模块这“华山一条路”。真被卡死，满盘皆输。

但那段时间，腾建军对团队说的最多的话却是：

于是，一边内部想办法，一边外界请高人，同时，只能用最笨的办法把难题列出来，一条条去攻克。

终于，腾建军团队发现：八成以上问题，出在光模块几乎肉眼不可见的脏污上，严重影响了通讯质量；而脏污的产生，就在数据中心现场超节点内网互连的安装环节。

于是，团队打造出针对光模块故障的定位和修复系统，通过数字化平台上的专家经验库和现场光模块的故障现象进行对接，实现了问题快速定位、故障迅速处置。

这一次，CloudMatrix 384再上线，速率、稳定性大幅提高。

作为全球首次大规模动用光模块部署超节点的探索，华为云创造了“奇迹”。

这一切，都成为硅基流动基于CloudMatrix 384再次爆发的坚实根基。

2月底，当强大的CloudMatrix 384呈现在硅基流动团队面前时，所有人面对的，却是一场新的长征。

3月初，硅基流动DeepSeek服务在超节点上刚跑通，吞吐量只有320 Tokens/秒，低到令人难以置信。

一台算力怪兽，表现如此拉胯，问题出在哪？没有人知道。

实际上，普通人很难想象，大模型内星辰般的宏大：

它的参数，高达数千亿级；它的神经网络层级，成百上千；每个算子（神经网络中具备特定功能的算法节点）背后，连接着不计其数的分布式系统；它的结果，则由成百上千张GPU协同计算输出，充满概率偶然。

要在这其中找出问题，仿佛是在一座亚马逊雨林中通缉一只细菌。

很快，王磊发动“超能力”，开始全公司“摇人”。

从底层做芯片的、做存储的、做计算的，到上层做算子的、做推理的、做平台的……王磊竭尽所能，几乎把技术栈上的所有团队拉到现场，跟硅基流动团队协同办公、攻坚克难。

协议有问题，马上改协议；算子有问题，马上改算子……围绕硅基流动DeepSeek服务上的超节点大计，华为上百人的团队，昼夜攻关不息。

而对华为云团队来说，冗长的测试，更是一个永不停歇的“西西弗神话”。

通信有瓶颈，优化通信；但接着，计算问题又冒出来了；于是，优化计算后，GPU、NPU太快，CPU不匹配……

当整个技术栈全部优化一遍后，王磊猛然发现，问题又从最初的源头，再次冒了出来。

但就是在这种重复、重复、再重复的优化过程中，CloudMatrix 384，终于被托举到一个不可思议的新高度。

4月10日，华为云生态大会，硅基流动创始人袁进辉郑重宣布：

基于CloudMatrix 384的DeepSeek-R1在保证单用户 20 TPS水平前提下，单卡 Decode吞吐量突破 1920 Tokens/秒。

相较英伟达H100，性能追平；相较3月初的CloudMatrix 384，性能提升6倍；相较昇腾910B单卡，性能飙升10倍。

这背后， CloudMatrix 384还实现了性能倍增（训推提升20%）、以存强算（吞吐量提升100%）、MoE亲和（千亿MoE提升3X）、长稳可靠（长稳运行40天不中断）、朝推夜训（算力利用率提升30%） 、即开即用 六大特性。

所谓MoE，即DeepSeek中的“混合专家模型”。DeepSeek之所以功能超强，在于其会内置多个“专家”处理问题。而在硬件层面，通常1枚芯片对应1个“专家”，于是芯片越多，效率越高、性能越强。

而超节点集成了384张昇腾卡，极大优化了MoE能力。

这不仅令DeepSeek性能大爆发，更展现出华为云在AI技术上的前瞻布局。

今天，在华为云三大云核心枢纽（贵州贵安、内蒙古乌兰察布、安徽芜湖），CloudMatrix 384实现全面布局，成为国内唯一正式商用的大规模超节点。

强大的算力网，令万卡级服务，分分钟即可开启。

这不仅是华为云的自我超越，也不仅是部分指标上对英伟达的超越，更大的意义，在于中国AI正呈现出体系化的突破式创新。

用袁进辉的话说： 美国试图像“三体人”一样，用芯片锁死中国科技。

但6年抗争过去，向死而生的华为，却越挫越勇。

当历经了鸿蒙蛰伏、海思攻坚、昇腾崛起……凭借华为云CloudMatrix 384超节点，一道坚不可摧的国产算力防线正在构建。

AI长跑没 有终点，突破，也不会有终点。但中国AI，一定会迎来自己的“奇点”时刻。 （应受访者要求，文中王磊、腾建军为化名）
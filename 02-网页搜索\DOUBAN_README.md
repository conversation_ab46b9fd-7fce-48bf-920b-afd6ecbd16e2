# 豆包AI智能问答搜索系统

一个集成了联网搜索和本地AI的现代化智能问答平台，基于豆包品牌设计，提供双模式AI问答服务。

## 🌟 功能特点

### 🌐 联网搜索模式
- **智能问答**: 基于用户问题搜索网络内容并生成AI回答
- **实时搜索**: 多关键词策略，提高搜索精度
- **相关性过滤**: 自动筛选与问题相关的文章
- **进度可视化**: 实时显示搜索和处理进度
- **自动保存**: 搜索结果和AI回答自动保存为Markdown文件

### 🧠 本地AI模式
- **离线问答**: 无需联网即可获得AI回答
- **快速响应**: 基于本地模型快速生成回答
- **隐私保护**: 问题不会发送到外部服务器
- **诚实回应**: 明确说明本地模式的局限性

### 📱 用户体验
- **响应式设计**: 完美支持桌面和移动设备
- **模式切换**: 一键切换联网搜索和本地AI模式
- **豆包品牌**: 采用豆包视觉设计风格
- **直观界面**: 清晰的进度显示和结果展示

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 网络连接（联网搜索模式）
- 现代浏览器

### 安装步骤

1. **克隆或下载项目文件**
```bash
# 确保以下文件在同一目录：
# - douban_integrated.html
# - douban_app.py
# - start_douban.py
# - crawler.py
# - ai_service.py
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动服务**
```bash
python start_douban.py
```

4. **访问界面**
```
http://localhost:5000
```

## 📋 使用说明

### 联网搜索模式

1. **选择模式**: 点击"联网搜索"标签
2. **输入问题**: 在文本框中输入您的问题
   - 例如："算家云在哪里？"
   - 例如："微短剧行业发展如何？"
3. **设置页数**: 选择搜索页数（1-20页）
4. **开始搜索**: 点击"AI智能回答"按钮
5. **查看结果**: 
   - 实时进度显示
   - AI智能回答
   - 相关文章列表

### 本地AI模式

1. **选择模式**: 点击"本地AI"标签
2. **输入问题**: 在文本框中输入您的问题
3. **获取回答**: 点击"AI智能回答"按钮
4. **查看结果**: 基于本地AI模型的回答

## 🎯 系统架构

### 前端组件
- **douban_integrated.html**: 集成界面，包含豆包设计风格
- **模式切换**: 联网搜索/本地AI双模式
- **实时通信**: SSE（Server-Sent Events）实现实时更新

### 后端服务
- **douban_app.py**: Flask后端服务
- **crawler.py**: 网络爬虫和搜索引擎
- **ai_service.py**: AI问答服务

### 核心功能
```
用户问题 → 关键词提取 → 多策略搜索 → 相关性过滤 → AI回答生成
```

## 📊 技术特点

### 智能搜索算法
- **多关键词提取**: 从问题中智能提取搜索关键词
- **相关性评分**: 多维度评分体系筛选相关文章
- **搜索优化**: 动态页数分配，提高搜索效率

### AI回答优化
- **问题导向**: 针对用户具体问题生成回答
- **诚实回应**: 信息不足时诚实说明并提供建议
- **来源标注**: 明确标注信息来源和相关性

### 用户体验优化
- **豆包设计**: 采用豆包品牌视觉风格
- **进度可视化**: 实时显示搜索和处理进度
- **响应式布局**: 适配各种设备屏幕

## 📁 文件结构

```
豆包AI搜索系统/
├── douban_integrated.html    # 集成前端界面
├── douban_app.py            # Flask后端服务
├── start_douban.py          # 启动脚本
├── crawler.py               # 爬虫模块
├── ai_service.py            # AI服务模块
├── requirements.txt         # Python依赖
├── DOUBAN_README.md         # 说明文档
└── [搜索结果]/              # 自动生成的搜索结果
    ├── [关键词]/
    │   ├── [日期]/
    │   │   └── [文章].md
    │   └── AI问答_[关键词]_[时间].md
    └── douban_search.log     # 系统日志
```

## 🔧 配置说明

### API配置
- **DeepSeek API**: 在 `ai_service.py` 中配置API地址和密钥
- **搜索引擎**: 默认使用百度搜索，可在 `crawler.py` 中修改

### 系统配置
- **端口**: 默认5000，可在 `douban_app.py` 中修改
- **搜索页数**: 默认3页，最大20页
- **文件保存**: 自动保存到以关键词命名的目录

## 🎨 界面特点

### 豆包设计风格
- **渐变色彩**: 蓝色渐变主题
- **圆角设计**: 现代化圆角元素
- **卡片布局**: 清晰的信息层次
- **动画效果**: 流畅的交互动画

### 响应式设计
- **桌面优化**: 大屏幕下的最佳布局
- **移动适配**: 手机和平板完美显示
- **触控友好**: 适合触屏设备操作

## 🔍 故障排除

### 常见问题

1. **服务启动失败**
   - 检查Python版本（需要3.7+）
   - 确认端口5000未被占用
   - 查看控制台错误信息

2. **搜索无结果**
   - 检查网络连接
   - 尝试更换搜索关键词
   - 查看日志文件 `douban_search.log`

3. **AI回答异常**
   - 检查API配置
   - 确认API服务可用
   - 查看错误日志

### 日志查看
```bash
tail -f douban_search.log
```

## 🚀 部署建议

### 开发环境
- 使用 `python start_douban.py` 启动
- 开启调试模式便于开发

### 生产环境
- 使用 Gunicorn 或 uWSGI 部署
- 配置反向代理（Nginx）
- 启用HTTPS

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件 `douban_search.log`
2. 检查控制台错误信息
3. 确认网络连接和API配置
4. 参考故障排除章节

## 🎉 更新日志

### v1.0.0
- ✅ 集成豆包设计风格
- ✅ 双模式AI问答（联网/本地）
- ✅ 智能搜索算法优化
- ✅ 响应式界面设计
- ✅ 实时进度显示
- ✅ 自动文件保存

---

**豆包AI智能问答搜索系统** - 让AI问答更智能，让搜索更精准！

#!/bin/bash
# Wan2.1-I2V-14B-480P 完整视频数据集流水线
# 从视频数据集创建到多卡微调再到推理的完整自动化流程

set -e

echo "🎬 Wan2.1-I2V-14B-480P 完整视频数据集流水线"
echo "从数据集创建到多卡微调再到推理的端到端解决方案"
echo "基于您成功的多卡训练经验 (2×A100, 39.63分钟, 5个epoch)"
echo "=" * 80

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 检查参数
CREATE_DATASET=true
RUN_TRAINING=true
RUN_INFERENCE=true
EPOCHS=5
DATASET_REPEAT=30

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-dataset)
            CREATE_DATASET=false
            shift
            ;;
        --skip-training)
            RUN_TRAINING=false
            shift
            ;;
        --skip-inference)
            RUN_INFERENCE=false
            shift
            ;;
        --epochs)
            EPOCHS="$2"
            shift 2
            ;;
        --dataset-repeat)
            DATASET_REPEAT="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-dataset     跳过视频数据集创建"
            echo "  --skip-training    跳过多卡训练"
            echo "  --skip-inference   跳过推理测试"
            echo "  --epochs N         训练轮数 (默认: 5)"
            echo "  --dataset-repeat N 数据集重复次数 (默认: 30)"
            echo "  -h, --help         显示帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 项目配置
PROJECT_ROOT="/root/sj-tmp/DiffSynth-Studio"
CONDA_ENV="wan_video_env"
VIDEO_DATASET_DIR="data/custom_video_dataset"
VIDEO_OUTPUT_DIR="./models/train/Wan2.1-I2V-14B-480P_video_lora"

log_step "阶段0: 环境准备"

# 检查项目目录
if [ ! -d "$PROJECT_ROOT" ]; then
    log_error "项目目录不存在: $PROJECT_ROOT"
    exit 1
fi

cd "$PROJECT_ROOT"
log_info "工作目录: $(pwd)"

# 激活conda环境
log_info "激活conda环境: $CONDA_ENV"
source /root/miniconda3/etc/profile.d/conda.sh
conda activate "$CONDA_ENV"

# 验证环境
log_info "验证Python环境..."
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}')"

log_step "阶段1: 自定义视频数据集创建"

if [ "$CREATE_DATASET" = true ]; then
    log_info "创建自定义视频数据集..."
    log_info "包含6个动画场景，每个3秒45帧..."
    
    # 运行视频数据集创建脚本
    python create_video_dataset.py
    
    if [ $? -eq 0 ]; then
        log_success "✅ 自定义视频数据集创建成功"
    else
        log_error "❌ 自定义视频数据集创建失败"
        exit 1
    fi
else
    log_info "⏭️  跳过视频数据集创建"
fi

# 验证视频数据集
if [ ! -d "$VIDEO_DATASET_DIR" ]; then
    log_error "视频数据集目录不存在: $VIDEO_DATASET_DIR"
    exit 1
fi

if [ ! -f "$VIDEO_DATASET_DIR/metadata.csv" ]; then
    log_error "元数据文件不存在: $VIDEO_DATASET_DIR/metadata.csv"
    exit 1
fi

# 显示数据集信息
log_info "视频数据集信息:"
VIDEO_COUNT=$(find "$VIDEO_DATASET_DIR/videos" -name "*.mp4" | wc -l)
IMAGE_COUNT=$(find "$VIDEO_DATASET_DIR/images" -name "*.jpg" | wc -l)
SAMPLE_COUNT=$(tail -n +2 "$VIDEO_DATASET_DIR/metadata.csv" | wc -l)

log_info "   视频文件: $VIDEO_COUNT 个"
log_info "   图像文件: $IMAGE_COUNT 个"
log_info "   CSV样本: $SAMPLE_COUNT 个"
log_info "   数据集目录: $VIDEO_DATASET_DIR"

log_step "阶段2: 多卡环境配置"

# 检查GPU状态
log_info "GPU状态检查:"
nvidia-smi --query-gpu=index,name,memory.total,memory.used,utilization.gpu --format=csv,noheader,nounits

# 设置环境变量
export NCCL_TIMEOUT=1800
export TOKENIZERS_PARALLELISM=false

log_info "设置环境变量:"
log_info "  NCCL_TIMEOUT=1800"
log_info "  TOKENIZERS_PARALLELISM=false"

# 检查accelerate配置
if [ ! -f "accelerate_config.yaml" ]; then
    log_warn "accelerate配置文件不存在，创建默认配置..."
    cat > accelerate_config.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
use_cpu: false
EOF
    log_success "✅ accelerate配置文件已创建"
else
    log_info "✅ accelerate配置文件存在"
fi

# 多卡训练阶段
if [ "$RUN_TRAINING" = true ]; then
    log_step "阶段3: 多卡LoRA微调"
    
    log_info "开始自定义视频数据集多卡训练..."
    log_info "训练配置 (基于您的成功经验):"
    log_info "  数据集: $VIDEO_DATASET_DIR"
    log_info "  视频样本: $VIDEO_COUNT"
    log_info "  重复次数: $DATASET_REPEAT"
    log_info "  有效样本: $((VIDEO_COUNT * DATASET_REPEAT))"
    log_info "  训练轮数: $EPOCHS"
    log_info "  输出目录: $VIDEO_OUTPUT_DIR"
    log_info "  硬件: 2×A100-80GB"
    log_info "  预期时间: ~40分钟 (基于您的经验)"
    
    log_info "开始训练，这可能需要较长时间，请耐心等待..."
    
    # 记录开始时间
    START_TIME=$(date)
    log_info "开始时间: $START_TIME"
    
    # 视频数据集训练命令（基于您成功的配置）
    accelerate launch --config_file accelerate_config.yaml \
      examples/wanvideo/model_training/train.py \
      --dataset_base_path "$VIDEO_DATASET_DIR" \
      --dataset_metadata_path "$VIDEO_DATASET_DIR/metadata.csv" \
      --height 480 --width 832 \
      --dataset_repeat "$DATASET_REPEAT" \
      --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
      --learning_rate 1e-4 --num_epochs "$EPOCHS" --gradient_accumulation_steps 1 \
      --remove_prefix_in_ckpt "pipe.dit." \
      --output_path "$VIDEO_OUTPUT_DIR" \
      --lora_base_model "dit" \
      --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
      --lora_rank 8 --extra_inputs "input_image" --mixed_precision "bf16"
    
    if [ $? -eq 0 ]; then
        END_TIME=$(date)
        log_success "✅ 多卡视频数据集训练完成"
        log_info "结束时间: $END_TIME"
    else
        log_error "❌ 多卡视频数据集训练失败"
        exit 1
    fi
else
    log_info "⏭️  跳过训练阶段"
fi

# 检查训练结果
log_step "阶段4: 训练结果验证"

if [ -d "$VIDEO_OUTPUT_DIR" ]; then
    log_success "✅ 视频训练输出目录存在: $VIDEO_OUTPUT_DIR"
    
    # 检查epoch文件
    log_info "检查点文件:"
    for i in {0..9}; do
        EPOCH_FILE="$VIDEO_OUTPUT_DIR/epoch-$i.safetensors"
        if [ -f "$EPOCH_FILE" ]; then
            FILE_SIZE=$(du -h "$EPOCH_FILE" | cut -f1)
            log_info "  ✅ epoch-$i.safetensors ($FILE_SIZE)"
        fi
    done
    
    # 检查训练配置
    if [ -f "$VIDEO_OUTPUT_DIR/training_args.json" ]; then
        log_info "  ✅ training_args.json"
    fi
else
    log_warn "⚠️  视频训练输出目录不存在: $VIDEO_OUTPUT_DIR"
fi

# 创建推理脚本
log_step "阶段5: 推理脚本准备"

log_info "创建视频LoRA推理脚本..."
python -c "
import train_with_video_dataset
train_with_video_dataset.create_video_inference_script()
"

if [ -f "video_lora_inference.py" ]; then
    log_success "✅ 视频LoRA推理脚本已创建: video_lora_inference.py"
else
    log_warn "⚠️  视频LoRA推理脚本创建失败"
fi

# 推理测试阶段
if [ "$RUN_INFERENCE" = true ]; then
    log_step "阶段6: 多卡推理测试"
    
    log_info "开始视频LoRA推理测试..."
    log_info "推理配置:"
    log_info "  输入图像: 来自视频数据集"
    log_info "  视频规格: 832×480, 45帧, 15fps"
    log_info "  推理步数: 30步"
    log_info "  预期时间: ~25分钟/视频"
    
    # 运行推理
    python video_lora_inference.py
    
    if [ $? -eq 0 ]; then
        log_success "✅ 视频LoRA推理测试完成"
    else
        log_error "❌ 视频LoRA推理测试失败"
    fi
else
    log_info "⏭️  跳过推理测试"
fi

# 最终GPU状态
log_step "阶段7: 最终状态检查"

log_info "最终GPU状态:"
nvidia-smi --query-gpu=index,name,memory.used,utilization.gpu --format=csv,noheader,nounits

# 检查生成的视频文件
log_info "检查生成的视频文件:"
for video_file in video_lora_test_*.mp4; do
    if [ -f "$video_file" ]; then
        FILE_SIZE=$(du -h "$video_file" | cut -f1)
        log_info "  ✅ $video_file ($FILE_SIZE)"
    fi
done

# 总结
echo ""
echo "=" * 80
echo "🎉 Wan2.1-I2V-14B-480P 完整视频数据集流水线执行完成!"
echo "=" * 80

log_success "执行总结:"
if [ "$CREATE_DATASET" = true ]; then
    log_success "✅ 自定义视频数据集创建完成 ($VIDEO_COUNT 个视频场景)"
fi

log_success "✅ 多卡环境配置完成 (2×A100)"

if [ "$RUN_TRAINING" = true ]; then
    log_success "✅ 多卡LoRA微调完成 ($EPOCHS epochs, $((VIDEO_COUNT * DATASET_REPEAT)) 有效样本)"
fi

log_success "✅ 训练结果验证完成"
log_success "✅ 推理脚本准备完成"

if [ "$RUN_INFERENCE" = true ]; then
    log_success "✅ 多卡推理测试完成"
fi

echo ""
log_info "🚀 您的完整视频数据集多卡微调推理流水线已成功运行!"

echo ""
echo "生成的文件:"
echo "  📁 $VIDEO_DATASET_DIR/           # 自定义视频数据集"
echo "  📝 $VIDEO_DATASET_DIR/metadata.csv  # 训练用CSV"
echo "  🎯 $VIDEO_OUTPUT_DIR/            # 训练输出"
echo "  🔧 video_lora_inference.py      # 视频推理脚本"
echo "  🎬 video_lora_test_*.mp4         # 生成的测试视频"

echo ""
echo "下一步建议:"
echo "  1. 查看生成的测试视频效果"
echo "  2. 对比不同模型的生成质量:"
echo "     - 基础模型 vs 原始LoRA vs 视频LoRA"
echo "  3. 进一步优化:"
echo "     - 添加更多视频场景"
echo "     - 调整训练参数"
echo "     - 尝试更长的视频"
echo "  4. 查看详细文档:"
echo "     cat 完整的自定义数据集多卡微调推理指南.md"

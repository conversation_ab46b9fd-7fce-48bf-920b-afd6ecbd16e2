﻿# 图解NumPy：常用函数的内在机制

**发布日期**: 2021年01月07日

**原文链接**: https://baijiahao.baidu.com/s?id=1688195427853754601&wfr=spider&for=pc

## 📄 原文内容

支持大量多维数组和矩阵运算的 NumPy 软件库是许多机器学习开发者和研究者的必备工具，本文将通过直观易懂的图示解析常用的 NumPy 功能和函数，帮助你理解 NumPy 操作数组的内在机制。
NumPy 是一个基础软件库，很多常用的 Python 数据处理软件库都使用了它或受到了它的启发，包括 pandas、PyTorch、TensorFlow、Keras 等。理解 NumPy 的工作机制能够帮助你提升在这些软件库方面的技能。而且在 GPU 上使用 NumPy 时，无需修改或仅需少量修改代码。
NumPy 的核心概念是 n 维数组。n 维数组的美丽之处是大多数运算看起来都一样，不管数组有多少维。但一维和二维有点特殊。本文分为三部分：
本文参考了 Jay Alammar 的文章《A Visual Intro to NumPy》并将其作为起点，然后进行了扩充，并做了一些细微修改。
乍一看，NumPy 数组与 Python 列表类似。它们都可作为容器，能够快速获取和设置元素，但插入和移除元素会稍慢一些。
NumPy 数组完胜列表的最简单例子是算术运算：
除此之外，NumPy 数组的优势和特点还包括：
通常是同质的：当元素都是一种类型时速度很快。
这里 O(N) 的意思是完成该运算所需的时间和数组的大小成正比，而 O*(1)（即所谓的「均摊 O(1)」）的意思是完成运算的时间通常与数组的大小无关。
为了创建 NumPy 数组，一种方法是转换 Python 列表。NumPy 数组类型可以直接从列表元素类型推导得到。
要确保向其输入的列表是同一种类型，否则你最终会得到 dtype=’object’，这会影响速度，最终只留下 NumPy 中含有的语法糖。
NumPy 数组不能像 Python 列表一样增长。数组的末端没有留下任何便于快速附加元素的空间。因此，常见的做法是要么先使用 Python 列表，准备好之后再将其转换为 NumPy 数组，要么是使用 np.zeros 或 np.empty 预先留下必要的空间：
通常我们有必要创建在形状和元素类型上与已有数组匹配的空数组。
事实上，所有用于创建填充了常量值的数组的函数都带有 _like 的形式：
NumPy 中有两个函数能用单调序列执行数组初始化：
如果你需要类似 [0., 1., 2.] 这样的浮点数数组，你可以修改 arange 输出的类型：arange(3).astype(float)，但还有一种更好的方法。arange 函数对类型很敏感：如果你以整型数作为参数输入，它会生成整型数；如果你输入浮点数（比如 arange(3.)），它会生成浮点数。
但 arange 并不非常擅长处理浮点数：
在我们眼里，这个 0.1 看起来像是一个有限的十进制数，但计算机不这么看。在二进制表示下，0.1 是一个无限分数，因此必须进行约分，也由此必然会产生误差。也因为这个原因，如果向 arange 函数输入带分数部分的 step，通常得不到什么好结果：你可能会遇到差一错误 (off-by-one error)。你可以使该区间的末端落在一个非整数的 step 数中（solution1），但这会降低代码的可读性和可维护性。这时候，linspace 就可以派上用场了。它不受舍入的影响，总能生成你要求的元素数值。不过，使用 linspace 时会遇到一个常见的陷阱：它统计的是数据点的数量，而不是区间，因此其最后一个参数 num 通常比你所想的数大 1。因此，上面最后一个例子中的数是 11，而不是 10。
一旦你的数组中有了数据，NumPy 就能以非常巧妙的方式轻松地提供它们：
除了「花式索引（fancy indexing）」外，上面给出的所有索引方法都被称为「view」：它们并不存储数据，也不会在数据被索引后发生改变时反映原数组的变化情况。
所有包含花式索引的方法都是可变的：它们允许通过分配来修改原始数组的内容，如上所示。这一功能可通过将数组切分成不同部分来避免总是复制数组的习惯。
Python 列表与 NumPy 数组的对比
为了获取 NumPy 数组中的数据，另一种超级有用的方法是布尔索引（boolean indexing），它支持使用各类逻辑运算符：
any 和 all 的作用与在 Python 中类似，但不会短路。
不过要注意，这里不支持 Python 的「三元比较」，比如 3<=a<=5。
如上所示，布尔索引也是可写的。其两个常用功能都有各自的专用函数：过度重载的 np.where 函数和 np.clip 函数。它们的含义如下：
NumPy 在速度上很出彩的一大应用领域是算术运算。向量运算符会被转换到 C++ 层面上执行，从而避免缓慢的 Python 循环的成本。NumPy 支持像操作普通的数那样操作整个数组。
与 Python 句法一样，a//b 表示 a 除 b（除法的商），x**n 表示 x。
正如加减浮点数时整型数会被转换成浮点数一样，标量也会被转换成数组，这个过程在 NumPy 中被称为广播（broadcast）。
大多数数学函数都有用于处理向量的 NumPy 对应函数：
floor 为舍、ceil 为入，around 则是舍入到最近的整数（其中 .5 会被舍掉）
NumPy 的排序函数没有 Python 的排序函数那么强大：
Python 列表与 NumPy 数组的排序函数对比
在一维情况下，如果缺少 reversed 关键字，那么只需简单地对结果再执行反向，最终效果还是一样。二维的情况则会更困难一些（人们正在请求这一功能）。
与 Python 列表相反，NumPy 数组没有索引方法。人们很久之前就在请求这个功能，但一直还没实现。
Python 列表与 NumPy 数组的对比，index() 中的方括号表示可以省略 j 或同时省略 i 和 j。
一种查找元素的方法是 np.where(a==x)[0][0]，但这个方法既不优雅，速度也不快，因为它需要检查数组中的所有元素，即便所要找的目标就在数组起始位置也是如此。
另一种更快的方式是使用 Numba 来加速 next((i[0] for i, v in np.ndenumerate(a) if v==x), -1)。
一旦数组的排序完成，搜索就容易多了：v = np.searchsorted(a, x); return v if a[v]==x else -1 的速度很快，时间复杂度为 O(log N)，但它需要 O(N log N) 时间先排好序。
事实上，用 C 来实现它进而加速搜索并不是问题。问题是浮点比较。这对任何数据来说都不是一种简单直接可用的任务。
函数 np.allclose(a, b) 能在一定公差下比较浮点数数组。
函数 np.allclose(a, b) 的工作过程示例。并没有万能方法！
np.allclose 假设所有被比较的数都在典型的 1 的范围内。举个例子，如果要在纳秒级的速度内完成计算，则需要用默认的 atol 参数值除以 1e9：np.allclose(1e-9, 2e-9, atol=1e-17) == False.
math.isclose 则不会对要比较的数进行任何假设，而是依赖用户给出合理的 abs_tol 值（对于典型的 1 的范围内的值，取默认的 np.allclose atol 值 1e-8 就足够好了）：math.isclose(0.1+0.2–0.3, abs_tol=1e-8)==True.
除此之外，np.allclose 在绝对值和相对公差的公式方面还有一些小问题，举个例子，对于给定的 a 和 b，存在 allclose(a, b) != allclose(b, a)。这些问题已在（标量）函数 math.isclose 中得到了解决，我们将在后面介绍它。对于这方面的更多内容，请参阅 GitHub 上的浮点数指南和对应的 NumPy 问题（https://floating-point-gui.de/errors/comparison/）。
NumPy 曾有一个专门的 matrix 类，但现在已经弃用了，所以本文会交替使用「矩阵」和「二维数组」这两个术语。
这里必须使用双括号，因为第二个位置参数是 dtype（可选，也接受整数）。
view 符号的意思是当切分一个数组时实际上没有执行复制。当该数组被修改时，这些改变也会反映到切分得到的结果上。
在很多运算中（比如 sum），你需要告诉 NumPy 是在列上还是行上执行运算。为了获取适用于任意维度的通用符号，NumPy 引入了 axis 的概念：事实上，axis 参数的值是相关问题中索引的数量：第一个索引为 axis=0，第二个索引为 axis=1，以此类推。因此在二维情况下，axis=0 是按列计算，axis=1 是按行计算。
除了逐元素执行的常规运算符（比如 +、-、、/、//、*），这里还有一个计算矩阵乘积的 @ 运算符：
我们已在第一部分介绍过标量到数组的广播，在其基础上进行泛化后，NumPy 支持向量和矩阵的混合运算，甚至两个向量之间的运算：
正如上面的例子所示，在二维情况下，行向量和列向量的处理方式有所不同。这与具备某类一维数组的 NumPy 实践不同（比如二维数组 a— 的第 j 列 a[:,j] 是一个一维数组）。默认情况下，一维数组会被视为二维运算中的行向量，因此当用一个矩阵乘以一个行向量时，你可以使用形状 (n,) 或 (1, n)——结果是一样的。如果你需要一个列向量，则有多种方法可以基于一维数组得到它，但出人意料的是「转置」不是其中之一。
基于一维数组得到二维数组的运算有两种：使用 reshape 调整形状和使用 newaxis 进行索引：
其中 -1 这个参数是告诉 reshape 自动计算其中一个维度大小，方括号中的 None 是用作 np.newaxis 的快捷方式，这会在指定位置添加一个空 axis。
因此，NumPy 共有三类向量：一维向量、二维行向量和二维列向量。下图展示了这三种向量之间的转换方式：
一维向量、二维行向量和二维列向量之间的转换方式。根据广播的原则，一维数组可被隐含地视为二维行向量，因此通常没必要在这两者之间执行转换——因此相应的区域被阴影化处理。
这两个函数适用于只堆叠矩阵或只堆叠向量，但当需要堆叠一维数组和矩阵时，只有 vstack 可以奏效：hstack 会出现维度不匹配的错误，原因如前所述，一维数组会被视为行向量，而不是列向量。针对这个问题，解决方法要么是将其转换为行向量，要么是使用能自动完成这一操作的 column_stack 函数：
复制矩阵的方法有两种：复制 - 粘贴式的 tile 和分页打印式的 repeat：
append 函数就像 hstack 一样，不能自动对一维数组执行转置，因此同样地，要么需要改变该向量的形状，要么就需要增加一个维度，或者使用 column_stack：
事实上，如果你只需要向数组的边缘添加常量值，那么（稍微复杂的）pad 函数应该就足够了：
广播规则使得我们能更简单地操作网格。假设你有如下矩阵（但非常大）：
使用 C 和使用 Python 创建矩阵的对比
这两种方法较慢，因为它们会使用 Python 循环。为了解决这样的问题，MATLAB 的方式是创建一个网格：
使用如上提供的参数 I 和 J，meshgrid 函数接受任意的索引集合作为输入，mgrid 只是切分，indices 只能生成完整的索引范围，fromfunction 只会调用所提供的函数一次。
但实际上，NumPy 中还有一种更好的方法。我们没必要将内存耗在整个 I 和 J 矩阵上。存储形状合适的向量就足够了，广播规则可以完成其余工作。
没有 indexing=’ij’ 参数，meshgrid 会改变这些参数的顺序：J, I= np.meshgrid(j, i)——这是一种 xy 模式，对可视化 3D 图表很有用。
除了在二维或三维网格上初始化函数，网格也可用于索引数组：
使用 meshgrid 索引数组，也适用于稀疏网格。
和 sum 一样，min、max、argmin、argmax、mean、std、var 等所有其它统计函数都支持 axis 参数并能据此完成统计计算：
三个统计函数示例，为了避免与 Python 的 min 冲突，NumPy 中对应的函数名为 np.amin。
用于二维及更高维的 argmin 和 argmax 函数会返回最小和最大值的第一个实例，在返回展开的索引上有点麻烦。为了将其转换成两个坐标，需要使用 unravel_index 函数：
使用 unravel_index 函数的示例
all 和 any 函数也支持 axis 参数：
axis 参数虽然对上面列出的函数很有用，但对排序毫无用处：
使用 Python 列表和 NumPy 数组执行排序的比较
这通常不是你在排序矩阵或电子表格时希望看到的结果：axis 根本不能替代 key 参数。但幸运的是，NumPy 提供了一些支持按列排序的辅助函数——或有需要的话可按多列排序：
1. a[a[:,0].argsort()] 可按第一列对数组排序：
这里 argsort 会返回原数组排序后的索引的数组。
这个技巧可以重复，但必须谨慎，别让下一次排序扰乱上一次排序的结果：
a = a[a[:,2].argsort()]
a = a[a[:,1].argsort(kind='stable')]
a = a[a[:,0].argsort(kind='stable')]
2. lexsort 函数能使用上述方式根据所有列进行排序，但它总是按行执行，而且所要排序的行的顺序是反向的（即自下而上），因此使用它时会有些不自然，比如
- a[np.lexsort(np.flipud(a[2,5].T))] 会首先根据第 2 列排序，然后（当第 2 列的值相等时）再根据第 5 列排序。
– a[np.lexsort(np.flipud(a.T))] 会从左向右根据所有列排序。
这里，flipud 会沿上下方向翻转该矩阵（准确地说是 axis=0 方向，与 a[::-1,...] 一样，其中三个点表示「所有其它维度」，因此翻转这个一维数组的是突然的 flipud，而不是 fliplr。
3. sort 还有一个 order 参数，但如果一开始是普通的（非结构化）数组，它执行起来既不快，也不容易使用。
4. 在 pandas 中执行它可能是更好的选择，因为在 pandas 中，该特定运算的可读性要高得多，也不那么容易出错：
– pd.DataFrame(a).sort_values(by=[2,5]).to_numpy() 会先根据第 2 列排序，然后根据第 5 列排序。
– pd.DataFrame(a).sort_values().to_numpy() 会从左向右根据所有列排序。
当你通过调整一维向量的形状或转换嵌套的 Python 列表来创建 3D 数组时，索引的含义是 (z,y,x)。第一个索引是平面的数量，然后是在该平面上的坐标：
这个索引顺序很方便，举个例子，它可用于保存一些灰度图像：a[i] 是索引第 i 张图像的快捷方式。
但这个索引顺序不是通用的。当操作 RGB 图像时，通常会使用 (y,x,z) 顺序：首先是两个像素坐标，最后一个是颜色坐标（Matplotlib 中是 RGB，OpenCV 中是 BGR）：
这样，我们就能很方便地索引特定的像素：a[i,j] 能提供 (i,j) 位置的 RGB 元组。
因此，创建几何形状的实际命令取决于你所在领域的惯例：
很显然，hstack、vstack、dstack 这些函数不支持这些惯例。它们硬编码了 (y,x,z) 的索引顺序，即 RGB 图像的顺序：
NumPy 使用 (y,x,z) 顺序的示意图，堆叠 RGB 图像（这里仅有两种颜色）
如果你的数据布局不同，使用 concatenate 命令来堆叠图像会更方便一些，向一个 axis 参数输入明确的索引数值：
如果你不习惯思考 axis 数，你可以将该数组转换成 hstack 等函数中硬编码的形式：
将数组转换为 hstack 中硬编码的形式的示意图
这种转换的成本很低：不会执行实际的复制，只是执行过程中混合索引的顺序。
另一种可以混合索引顺序的运算是数组转置。了解它可能会让你更加熟悉三维数组。根据你决定使用的 axis 顺序的不同，转置数组所有平面的实际命令会有所不同：对于一般数组，它会交换索引 1 和 2，对 RGB 图像而言是 0 和 1：
不过有趣的是，transpose 的默认 axes 参数（以及仅有的 a.T 运算模式）会调转索引顺序的方向，这与上述两个索引顺序惯例都不相符。
最后，还有一个函数能避免你在处理多维数组时使用太多训练，还能让你的代码更简洁——einsum（爱因斯坦求和）：
它会沿重复的索引对数组求和。在这个特定的例子中，np.tensordot(a, b, axis=1) 足以应对这两种情况，但在更复杂的情况中，einsum 的速度可能更快，而且通常也更容易读写——只要你理解其背后的逻辑。
如果你希望测试你的 NumPy 技能，GitHub 有 100 道相当困难的练习题：https://github.com/rougier/numpy-100。
你最喜欢的 NumPy 功能是什么？请与我们分享！
window.jsonData = {"bsData":{"header":{"islogin":0,"username":""},"ssid":0,"comment":{"tid":"1069000038282853","forbidden":false},"newPCStyle":"1","superlanding":[{"itemType":"article","itemData":{"header":"\u56fe\u89e3NumPy\uff1a\u5e38\u7528\u51fd\u6570\u7684\u5185\u5728\u673a\u5236","original_status":0,"rumor_label":0,"infoBaiJiaHao":{"date":"21-01-07","time":"11:03","thumbnail":{"link":"https:\/\/avatar.bdstatic.com\/it\/u=1561533990,1202445578&fm=3012&app=3012&autime=1749195918&size=b200,200"},"name":"\u673a\u5668\u4e4b\u5fc3Pro","link":"1536769991067070","third_id":"1536769991067070","type":"media","uk":"kwboRjvJvlL4jzJT0LzI4w","author_link":"https:\/\/author.baidu.com\/home?from=bjh_article&app_id=1536769991067070","vType":"2","authorTag":"\u673a\u5668\u4e4b\u5fc3\u5b98\u65b9\u8d26\u53f7","iptopoi":[],"sign":"\u4e13\u4e1a\u7684\u4eba\u5de5\u667a\u80fd\u5a92\u4f53\u548c\u4ea7\u4e1a\u670d\u52a1\u5e73\u53f0","zanNum":"166349","fansNum":"391549"},"sections":[{"type":"text","content":"<span class=\"bjh-blockquote\"><span class=\"bjh-p\">\u652f\u6301\u5927\u91cf\u591a\u7ef4\u6570\u7ec4\u548c\u77e9\u9635\u8fd0\u7b97\u7684 NumPy \u8f6f\u4ef6\u5e93\u662f\u8bb8\u591a\u673a\u5668\u5b66\u4e60\u5f00\u53d1\u8005\u548c\u7814\u7a76\u8005\u7684\u5fc5\u5907\u5de5\u5177\uff0c\u672c\u6587\u5c06\u901a\u8fc7\u76f4\u89c2\u6613\u61c2\u7684\u56fe\u793a\u89e3\u6790\u5e38\u7528\u7684 NumPy \u529f\u80fd\u548c\u51fd\u6570\uff0c\u5e2e\u52a9\u4f60\u7406\u89e3 NumPy \u64cd\u4f5c\u6570\u7ec4\u7684\u5185\u5728\u673a\u5236\u3002<\/span><\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/2fdda3cc7cd98d1029783e5951a463097aec9048.jpeg@f_auto?token=3d522e6200a3651a625db6869d362bad&s=09295C321D22740118EE48C8020020F9","imgHeight":540,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u662f\u4e00\u4e2a\u57fa\u7840\u8f6f\u4ef6\u5e93\uff0c\u5f88\u591a\u5e38\u7528\u7684 Python \u6570\u636e\u5904\u7406\u8f6f\u4ef6\u5e93\u90fd\u4f7f\u7528\u4e86\u5b83\u6216\u53d7\u5230\u4e86\u5b83\u7684\u542f\u53d1\uff0c\u5305\u62ec pandas\u3001PyTorch\u3001TensorFlow\u3001Keras \u7b49\u3002\u7406\u89e3 NumPy \u7684\u5de5\u4f5c\u673a\u5236\u80fd\u591f\u5e2e\u52a9\u4f60\u63d0\u5347\u5728\u8fd9\u4e9b\u8f6f\u4ef6\u5e93\u65b9\u9762\u7684\u6280\u80fd\u3002\u800c\u4e14\u5728 GPU \u4e0a\u4f7f\u7528 NumPy \u65f6\uff0c\u65e0\u9700\u4fee\u6539\u6216\u4ec5\u9700\u5c11\u91cf\u4fee\u6539\u4ee3\u7801\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u7684\u6838\u5fc3\u6982\u5ff5\u662f n \u7ef4\u6570\u7ec4\u3002n \u7ef4\u6570\u7ec4\u7684\u7f8e\u4e3d\u4e4b\u5904\u662f\u5927\u591a\u6570\u8fd0\u7b97\u770b\u8d77\u6765\u90fd\u4e00\u6837\uff0c\u4e0d\u7ba1\u6570\u7ec4\u6709\u591a\u5c11\u7ef4\u3002\u4f46\u4e00\u7ef4\u548c\u4e8c\u7ef4\u6709\u70b9\u7279\u6b8a\u3002\u672c\u6587\u5206\u4e3a\u4e09\u90e8\u5206\uff1a<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">1. \u5411\u91cf\uff1a\u4e00\u7ef4\u6570\u7ec4<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">2. \u77e9\u9635\uff1a\u4e8c\u7ef4\u6570\u7ec4<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">3. \u4e09\u7ef4\u53ca\u66f4\u9ad8\u7ef4<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u672c\u6587\u53c2\u8003\u4e86 Jay Alammar \u7684\u6587\u7ae0\u300aA Visual Intro to NumPy\u300b\u5e76\u5c06\u5176\u4f5c\u4e3a\u8d77\u70b9\uff0c\u7136\u540e\u8fdb\u884c\u4e86\u6269\u5145\uff0c\u5e76\u505a\u4e86\u4e00\u4e9b\u7ec6\u5fae\u4fee\u6539\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">NumPy \u6570\u7ec4\u548c Python \u5217\u8868<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e4d\u4e00\u770b\uff0cNumPy \u6570\u7ec4\u4e0e Python \u5217\u8868\u7c7b\u4f3c\u3002\u5b83\u4eec\u90fd\u53ef\u4f5c\u4e3a\u5bb9\u5668\uff0c\u80fd\u591f\u5feb\u901f\u83b7\u53d6\u548c\u8bbe\u7f6e\u5143\u7d20\uff0c\u4f46\u63d2\u5165\u548c\u79fb\u9664\u5143\u7d20\u4f1a\u7a0d\u6162\u4e00\u4e9b\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u6570\u7ec4\u5b8c\u80dc\u5217\u8868\u7684\u6700\u7b80\u5355\u4f8b\u5b50\u662f\u7b97\u672f\u8fd0\u7b97\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/d043ad4bd11373f0088b4179ca9490fcf9ed04c6.jpeg@f_auto?token=162df9dc552f8a5bbb68689fa3c3c8dc&s=1CA27C324B6249260AD5E4CA0000F0B2","imgHeight":459,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u9664\u6b64\u4e4b\u5916\uff0cNumPy \u6570\u7ec4\u7684\u4f18\u52bf\u548c\u7279\u70b9\u8fd8\u5305\u62ec\uff1a<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u66f4\u7d27\u51d1\uff0c\u5c24\u5176\u662f\u5f53\u7ef4\u5ea6\u5927\u4e8e\u4e00\u7ef4\u65f6\uff1b<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5f53\u8fd0\u7b97\u53ef\u4ee5\u5411\u91cf\u5316\u65f6\uff0c\u901f\u5ea6\u6bd4\u5217\u8868\u66f4\u5feb\uff1b<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5f53\u5728\u540e\u9762\u9644\u52a0\u5143\u7d20\u65f6\uff0c\u901f\u5ea6\u6bd4\u5217\u8868\u6162\uff1b<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u901a\u5e38\u662f\u540c\u8d28\u7684\uff1a\u5f53\u5143\u7d20\u90fd\u662f\u4e00\u79cd\u7c7b\u578b\u65f6\u901f\u5ea6\u5f88\u5feb\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics0.baidu.com\/feed\/6f061d950a7b0208a30e0add0d4229d4562cc86e.jpeg@f_auto?token=d242b721e91ac7ed559eab9c33cac907&s=BD02583217725C2142455CDA020090B2","imgHeight":499,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u91cc O(N) \u7684\u610f\u601d\u662f\u5b8c\u6210\u8be5\u8fd0\u7b97\u6240\u9700\u7684\u65f6\u95f4\u548c\u6570\u7ec4\u7684\u5927\u5c0f\u6210\u6b63\u6bd4\uff0c\u800c O*(1)\uff08\u5373\u6240\u8c13\u7684\u300c\u5747\u644a O(1)\u300d\uff09\u7684\u610f\u601d\u662f\u5b8c\u6210\u8fd0\u7b97\u7684\u65f6\u95f4\u901a\u5e38\u4e0e\u6570\u7ec4\u7684\u5927\u5c0f\u65e0\u5173\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u5411\u91cf\uff1a\u4e00\u7ef4\u6570\u7ec4<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u5411\u91cf\u521d\u59cb\u5316<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e3a\u4e86\u521b\u5efa NumPy \u6570\u7ec4\uff0c\u4e00\u79cd\u65b9\u6cd5\u662f\u8f6c\u6362 Python \u5217\u8868\u3002NumPy \u6570\u7ec4\u7c7b\u578b\u53ef\u4ee5\u76f4\u63a5\u4ece\u5217\u8868\u5143\u7d20\u7c7b\u578b\u63a8\u5bfc\u5f97\u5230\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics6.baidu.com\/feed\/6609c93d70cf3bc77b9d6b13bf9b61a6cf112ad7.jpeg@f_auto?token=d5ea0bc2f1d19740e9d4222570b7ff31","imgHeight":120,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u8981\u786e\u4fdd\u5411\u5176\u8f93\u5165\u7684\u5217\u8868\u662f\u540c\u4e00\u79cd\u7c7b\u578b\uff0c\u5426\u5219\u4f60\u6700\u7ec8\u4f1a\u5f97\u5230 dtype=\u2019object\u2019\uff0c\u8fd9\u4f1a\u5f71\u54cd\u901f\u5ea6\uff0c\u6700\u7ec8\u53ea\u7559\u4e0b NumPy \u4e2d\u542b\u6709\u7684\u8bed\u6cd5\u7cd6\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u6570\u7ec4\u4e0d\u80fd\u50cf Python \u5217\u8868\u4e00\u6837\u589e\u957f\u3002\u6570\u7ec4\u7684\u672b\u7aef\u6ca1\u6709\u7559\u4e0b\u4efb\u4f55\u4fbf\u4e8e\u5feb\u901f\u9644\u52a0\u5143\u7d20\u7684\u7a7a\u95f4\u3002\u56e0\u6b64\uff0c\u5e38\u89c1\u7684\u505a\u6cd5\u662f\u8981\u4e48\u5148\u4f7f\u7528 Python \u5217\u8868\uff0c\u51c6\u5907\u597d\u4e4b\u540e\u518d\u5c06\u5176\u8f6c\u6362\u4e3a NumPy \u6570\u7ec4\uff0c\u8981\u4e48\u662f\u4f7f\u7528 np.zeros \u6216 np.empty \u9884\u5148\u7559\u4e0b\u5fc5\u8981\u7684\u7a7a\u95f4\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/9f510fb30f2442a7745238dfa1d8764cd013028b.jpeg@f_auto?token=93fb05cc2022c4e959f6ee58e3fb1eb8","imgHeight":119,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u901a\u5e38\u6211\u4eec\u6709\u5fc5\u8981\u521b\u5efa\u5728\u5f62\u72b6\u548c\u5143\u7d20\u7c7b\u578b\u4e0a\u4e0e\u5df2\u6709\u6570\u7ec4\u5339\u914d\u7684\u7a7a\u6570\u7ec4\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics4.baidu.com\/feed\/4ec2d5628535e5dd23a5e984065d7ce8cf1b6207.jpeg@f_auto?token=e9b8738447a5a8f85523e0800ee5f4e7","imgHeight":113,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4e8b\u5b9e\u4e0a\uff0c\u6240\u6709\u7528\u4e8e\u521b\u5efa\u586b\u5145\u4e86\u5e38\u91cf\u503c\u7684\u6570\u7ec4\u7684\u51fd\u6570\u90fd\u5e26\u6709 _like \u7684\u5f62\u5f0f\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics1.baidu.com\/feed\/cefc1e178a82b9017ff072091c1672703812efca.jpeg@f_auto?token=6fe2d4e3b8c361fdaa0734af3e8a155a&s=9CA55D3217524461544060DA0200A0B1","imgHeight":495,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u4e2d\u6709\u4e24\u4e2a\u51fd\u6570\u80fd\u7528\u5355\u8c03\u5e8f\u5217\u6267\u884c\u6570\u7ec4\u521d\u59cb\u5316\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics1.baidu.com\/feed\/b21c8701a18b87d632bc29e26993f33f1d30fdf0.jpeg@f_auto?token=3f3edfd2b3ed3ffe2c841eb2245bea97&s=598C3C729F7048010668F0CA020030B1","imgHeight":416,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5982\u679c\u4f60\u9700\u8981\u7c7b\u4f3c [0., 1., 2.] \u8fd9\u6837\u7684\u6d6e\u70b9\u6570\u6570\u7ec4\uff0c\u4f60\u53ef\u4ee5\u4fee\u6539 arange \u8f93\u51fa\u7684\u7c7b\u578b\uff1aarange(3).astype(float)\uff0c\u4f46\u8fd8\u6709\u4e00\u79cd\u66f4\u597d\u7684\u65b9\u6cd5\u3002arange \u51fd\u6570\u5bf9\u7c7b\u578b\u5f88\u654f\u611f\uff1a\u5982\u679c\u4f60\u4ee5\u6574\u578b\u6570\u4f5c\u4e3a\u53c2\u6570\u8f93\u5165\uff0c\u5b83\u4f1a\u751f\u6210\u6574\u578b\u6570\uff1b\u5982\u679c\u4f60\u8f93\u5165\u6d6e\u70b9\u6570\uff08\u6bd4\u5982 arange(3.)\uff09\uff0c\u5b83\u4f1a\u751f\u6210\u6d6e\u70b9\u6570\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4f46 arange \u5e76\u4e0d\u975e\u5e38\u64c5\u957f\u5904\u7406\u6d6e\u70b9\u6570\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/7a899e510fb30f24ca7ccc3fa70e0a44ac4b034e.jpeg@f_auto?token=daba5aff46a56a80fa1a9baf31ff7925&s=38247C321700454B5254DCCB020060B1","imgHeight":523,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5728\u6211\u4eec\u773c\u91cc\uff0c\u8fd9\u4e2a 0.1 \u770b\u8d77\u6765\u50cf\u662f\u4e00\u4e2a\u6709\u9650\u7684\u5341\u8fdb\u5236\u6570\uff0c\u4f46\u8ba1\u7b97\u673a\u4e0d\u8fd9\u4e48\u770b\u3002\u5728\u4e8c\u8fdb\u5236\u8868\u793a\u4e0b\uff0c0.1 \u662f\u4e00\u4e2a\u65e0\u9650\u5206\u6570\uff0c\u56e0\u6b64\u5fc5\u987b\u8fdb\u884c\u7ea6\u5206\uff0c\u4e5f\u7531\u6b64\u5fc5\u7136\u4f1a\u4ea7\u751f\u8bef\u5dee\u3002\u4e5f\u56e0\u4e3a\u8fd9\u4e2a\u539f\u56e0\uff0c\u5982\u679c\u5411 arange \u51fd\u6570\u8f93\u5165\u5e26\u5206\u6570\u90e8\u5206\u7684 step\uff0c\u901a\u5e38\u5f97\u4e0d\u5230\u4ec0\u4e48\u597d\u7ed3\u679c\uff1a\u4f60\u53ef\u80fd\u4f1a\u9047\u5230\u5dee\u4e00\u9519\u8bef (off-by-one error)\u3002\u4f60\u53ef\u4ee5\u4f7f\u8be5\u533a\u95f4\u7684\u672b\u7aef\u843d\u5728\u4e00\u4e2a\u975e\u6574\u6570\u7684 step \u6570\u4e2d\uff08solution1\uff09\uff0c\u4f46\u8fd9\u4f1a\u964d\u4f4e\u4ee3\u7801\u7684\u53ef\u8bfb\u6027\u548c\u53ef\u7ef4\u62a4\u6027\u3002\u8fd9\u65f6\u5019\uff0clinspace \u5c31\u53ef\u4ee5\u6d3e\u4e0a\u7528\u573a\u4e86\u3002\u5b83\u4e0d\u53d7\u820d\u5165\u7684\u5f71\u54cd\uff0c\u603b\u80fd\u751f\u6210\u4f60\u8981\u6c42\u7684\u5143\u7d20\u6570\u503c\u3002\u4e0d\u8fc7\uff0c\u4f7f\u7528 linspace \u65f6\u4f1a\u9047\u5230\u4e00\u4e2a\u5e38\u89c1\u7684\u9677\u9631\uff1a\u5b83\u7edf\u8ba1\u7684\u662f\u6570\u636e\u70b9\u7684\u6570\u91cf\uff0c\u800c\u4e0d\u662f\u533a\u95f4\uff0c\u56e0\u6b64\u5176\u6700\u540e\u4e00\u4e2a\u53c2\u6570 num \u901a\u5e38\u6bd4\u4f60\u6240\u60f3\u7684\u6570\u5927 1\u3002\u56e0\u6b64\uff0c\u4e0a\u9762\u6700\u540e\u4e00\u4e2a\u4f8b\u5b50\u4e2d\u7684\u6570\u662f 11\uff0c\u800c\u4e0d\u662f 10\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5728\u8fdb\u884c\u6d4b\u8bd5\u65f6\uff0c\u6211\u4eec\u901a\u5e38\u9700\u8981\u751f\u6210\u968f\u673a\u6570\u7ec4\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/9358d109b3de9c824f8572ff1c1a5b0d18d84305.jpeg@f_auto?token=fe5db5674c3e0fed3c9f85dcbc3f333a&s=98AC7C321F786801144164CE000030B3","imgHeight":486,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u5411\u91cf\u7d22\u5f15<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e00\u65e6\u4f60\u7684\u6570\u7ec4\u4e2d\u6709\u4e86\u6570\u636e\uff0cNumPy \u5c31\u80fd\u4ee5\u975e\u5e38\u5de7\u5999\u7684\u65b9\u5f0f\u8f7b\u677e\u5730\u63d0\u4f9b\u5b83\u4eec\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/a5c27d1ed21b0ef434b6f1b4b35f8add80cb3e24.jpeg@f_auto?token=b8f77eccf31a65894f5d193b9fcea378&s=98A07D321B424C451E45E9D80200B0B3","imgHeight":544,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u9664\u4e86\u300c\u82b1\u5f0f\u7d22\u5f15\uff08fancy indexing\uff09\u300d\u5916\uff0c\u4e0a\u9762\u7ed9\u51fa\u7684\u6240\u6709\u7d22\u5f15\u65b9\u6cd5\u90fd\u88ab\u79f0\u4e3a\u300cview\u300d\uff1a\u5b83\u4eec\u5e76\u4e0d\u5b58\u50a8\u6570\u636e\uff0c\u4e5f\u4e0d\u4f1a\u5728\u6570\u636e\u88ab\u7d22\u5f15\u540e\u53d1\u751f\u6539\u53d8\u65f6\u53cd\u6620\u539f\u6570\u7ec4\u7684\u53d8\u5316\u60c5\u51b5\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u6240\u6709\u5305\u542b\u82b1\u5f0f\u7d22\u5f15\u7684\u65b9\u6cd5\u90fd\u662f\u53ef\u53d8\u7684\uff1a\u5b83\u4eec\u5141\u8bb8\u901a\u8fc7\u5206\u914d\u6765\u4fee\u6539\u539f\u59cb\u6570\u7ec4\u7684\u5185\u5bb9\uff0c\u5982\u4e0a\u6240\u793a\u3002\u8fd9\u4e00\u529f\u80fd\u53ef\u901a\u8fc7\u5c06\u6570\u7ec4\u5207\u5206\u6210\u4e0d\u540c\u90e8\u5206\u6765\u907f\u514d\u603b\u662f\u590d\u5236\u6570\u7ec4\u7684\u4e60\u60ef\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/4d086e061d950a7bd7cd7826644ab9def0d3c9ff.jpeg@f_auto?token=4914318f62a221fbb012b33924ec22d1&s=01966C324B6769224AFC2DCA000070B3","imgHeight":227,"imgWidth":1064,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">Python \u5217\u8868\u4e0e NumPy \u6570\u7ec4\u7684\u5bf9\u6bd4<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e3a\u4e86\u83b7\u53d6 NumPy \u6570\u7ec4\u4e2d\u7684\u6570\u636e\uff0c\u53e6\u4e00\u79cd\u8d85\u7ea7\u6709\u7528\u7684\u65b9\u6cd5\u662f\u5e03\u5c14\u7d22\u5f15\uff08boolean indexing\uff09\uff0c\u5b83\u652f\u6301\u4f7f\u7528\u5404\u7c7b\u903b\u8f91\u8fd0\u7b97\u7b26\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/2fdda3cc7cd98d103f93c95050a463097aec90ab.jpeg@f_auto?token=ee455481be0e33387fb3ffcbdab0425b&s=91F04D32114A55491EFD6DDB0200C0B2","imgHeight":575,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">any \u548c all \u7684\u4f5c\u7528\u4e0e\u5728 Python \u4e2d\u7c7b\u4f3c\uff0c\u4f46\u4e0d\u4f1a\u77ed\u8def\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e0d\u8fc7\u8981\u6ce8\u610f\uff0c\u8fd9\u91cc\u4e0d\u652f\u6301 Python \u7684\u300c\u4e09\u5143\u6bd4\u8f83\u300d\uff0c\u6bd4\u5982 3&lt;=a&lt;=5\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5982\u4e0a\u6240\u793a\uff0c\u5e03\u5c14\u7d22\u5f15\u4e5f\u662f\u53ef\u5199\u7684\u3002\u5176\u4e24\u4e2a\u5e38\u7528\u529f\u80fd\u90fd\u6709\u5404\u81ea\u7684\u4e13\u7528\u51fd\u6570\uff1a\u8fc7\u5ea6\u91cd\u8f7d\u7684 np.where \u51fd\u6570\u548c np.clip \u51fd\u6570\u3002\u5b83\u4eec\u7684\u542b\u4e49\u5982\u4e0b\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/b90e7bec54e736d1c1d2fcc1e8cb94c5d762695d.jpeg@f_auto?token=4298eb675f796e15f3b08bcadc306252&s=99295D32114A5D49445198C7020070B1","imgHeight":601,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u5411\u91cf\u8fd0\u7b97<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u5728\u901f\u5ea6\u4e0a\u5f88\u51fa\u5f69\u7684\u4e00\u5927\u5e94\u7528\u9886\u57df\u662f\u7b97\u672f\u8fd0\u7b97\u3002\u5411\u91cf\u8fd0\u7b97\u7b26\u4f1a\u88ab\u8f6c\u6362\u5230 C++ \u5c42\u9762\u4e0a\u6267\u884c\uff0c\u4ece\u800c\u907f\u514d\u7f13\u6162\u7684 Python \u5faa\u73af\u7684\u6210\u672c\u3002NumPy \u652f\u6301\u50cf\u64cd\u4f5c\u666e\u901a\u7684\u6570\u90a3\u6837\u64cd\u4f5c\u6574\u4e2a\u6570\u7ec4\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics6.baidu.com\/feed\/21a4462309f79052e60803fa7f680ccd7acbd506.jpeg@f_auto?token=b602ed023c1c7e21cc6c5dac3f277ea7&s=39235E329972F403506E48580200F0F9","imgHeight":433,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4e0e Python \u53e5\u6cd5\u4e00\u6837\uff0ca\/\/b \u8868\u793a a \u9664 b\uff08\u9664\u6cd5\u7684\u5546\uff09\uff0cx**n \u8868\u793a x\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u6b63\u5982\u52a0\u51cf\u6d6e\u70b9\u6570\u65f6\u6574\u578b\u6570\u4f1a\u88ab\u8f6c\u6362\u6210\u6d6e\u70b9\u6570\u4e00\u6837\uff0c\u6807\u91cf\u4e5f\u4f1a\u88ab\u8f6c\u6362\u6210\u6570\u7ec4\uff0c\u8fd9\u4e2a\u8fc7\u7a0b\u5728 NumPy \u4e2d\u88ab\u79f0\u4e3a\u5e7f\u64ad\uff08broadcast\uff09\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics4.baidu.com\/feed\/10dfa9ec8a1363270c5c83ceff147beb09fac739.jpeg@f_auto?token=d2b753f434166f4efc5414631febe9b5&s=81B35F32C96274031AEE49D8020030F9","imgHeight":442,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5927\u591a\u6570\u6570\u5b66\u51fd\u6570\u90fd\u6709\u7528\u4e8e\u5904\u7406\u5411\u91cf\u7684 NumPy \u5bf9\u5e94\u51fd\u6570\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/730e0cf3d7ca7bcbdb84d716cf92b064f724a8ab.jpeg@f_auto?token=28999aa2486e533a5f18c3de4034a62c&s=4CA63D720B485C411EDDC5C80200F0B1","imgHeight":481,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u6807\u91cf\u79ef\u6709\u81ea\u5df1\u7684\u8fd0\u7b97\u7b26\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/42a98226cffc1e177dca78cc240b2d04728de97e.jpeg@f_auto?token=0b8f07ff7189b06864bf1aaba1a76138&s=08A47D32C5607D205EEDD1CC020060B1","imgHeight":343,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u6267\u884c\u4e09\u89d2\u51fd\u6570\u65f6\u4e5f\u65e0\u9700\u5faa\u73af\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/c995d143ad4bd1136d9d06472a347f084afb0591.jpeg@f_auto?token=0391de5b30c4a40821fb871b6ce31a74&s=FDA63D72DB0A50491E5D41C80200B0B3","imgHeight":543,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u6211\u4eec\u53ef\u4ee5\u5728\u6574\u4f53\u4e0a\u5bf9\u6570\u7ec4\u8fdb\u884c\u820d\u5165\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/ac6eddc451da81cb5de49a333cfd0b1108243174.jpeg@f_auto?token=f2dfb9cc90df8326ca09af40ab2a7204&s=18267D328170F0211477D8C6020070F1","imgHeight":299,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">floor \u4e3a\u820d\u3001ceil \u4e3a\u5165\uff0caround \u5219\u662f\u820d\u5165\u5230\u6700\u8fd1\u7684\u6574\u6570\uff08\u5176\u4e2d .5 \u4f1a\u88ab\u820d\u6389\uff09<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u4e5f\u80fd\u6267\u884c\u57fa\u7840\u7684\u7edf\u8ba1\u8fd0\u7b97\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics4.baidu.com\/feed\/ac4bd11373f0820211f9ffdf256020eaaa641b0d.jpeg@f_auto?token=ba13c9a59a483c18d68095ab8c59c7fa&s=C997C712C530759A08F665590200C0BA","imgHeight":592,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u7684\u6392\u5e8f\u51fd\u6570\u6ca1\u6709 Python \u7684\u6392\u5e8f\u51fd\u6570\u90a3\u4e48\u5f3a\u5927\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/5366d0160924ab18ddf198cc5b613dca79890b8c.jpeg@f_auto?token=b2c6d8ff518269ac41f9ea8a555b9819&s=04F6ED32C5245D22167521CA000070B3","imgHeight":255,"imgWidth":1031,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">Python \u5217\u8868\u4e0e NumPy \u6570\u7ec4\u7684\u6392\u5e8f\u51fd\u6570\u5bf9\u6bd4<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5728\u4e00\u7ef4\u60c5\u51b5\u4e0b\uff0c\u5982\u679c\u7f3a\u5c11 reversed \u5173\u952e\u5b57\uff0c\u90a3\u4e48\u53ea\u9700\u7b80\u5355\u5730\u5bf9\u7ed3\u679c\u518d\u6267\u884c\u53cd\u5411\uff0c\u6700\u7ec8\u6548\u679c\u8fd8\u662f\u4e00\u6837\u3002\u4e8c\u7ef4\u7684\u60c5\u51b5\u5219\u4f1a\u66f4\u56f0\u96be\u4e00\u4e9b\uff08\u4eba\u4eec\u6b63\u5728\u8bf7\u6c42\u8fd9\u4e00\u529f\u80fd\uff09\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u641c\u7d22\u5411\u91cf\u4e2d\u7684\u5143\u7d20<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e0e Python \u5217\u8868\u76f8\u53cd\uff0cNumPy \u6570\u7ec4\u6ca1\u6709\u7d22\u5f15\u65b9\u6cd5\u3002\u4eba\u4eec\u5f88\u4e45\u4e4b\u524d\u5c31\u5728\u8bf7\u6c42\u8fd9\u4e2a\u529f\u80fd\uff0c\u4f46\u4e00\u76f4\u8fd8\u6ca1\u5b9e\u73b0\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics1.baidu.com\/feed\/738b4710b912c8fc82cbfdff93984942d688216e.jpeg@f_auto?token=e6494e5b79fb9f907c112f5d6c0a9cdc&s=1CAA7C328FE44C035874F5C9000010B3","imgHeight":258,"imgWidth":943,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">Python \u5217\u8868\u4e0e NumPy \u6570\u7ec4\u7684\u5bf9\u6bd4\uff0cindex() \u4e2d\u7684\u65b9\u62ec\u53f7\u8868\u793a\u53ef\u4ee5\u7701\u7565 j \u6216\u540c\u65f6\u7701\u7565 i \u548c j\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e00\u79cd\u67e5\u627e\u5143\u7d20\u7684\u65b9\u6cd5\u662f np.where(a==x)[0][0]\uff0c\u4f46\u8fd9\u4e2a\u65b9\u6cd5\u65e2\u4e0d\u4f18\u96c5\uff0c\u901f\u5ea6\u4e5f\u4e0d\u5feb\uff0c\u56e0\u4e3a\u5b83\u9700\u8981\u68c0\u67e5\u6570\u7ec4\u4e2d\u7684\u6240\u6709\u5143\u7d20\uff0c\u5373\u4fbf\u6240\u8981\u627e\u7684\u76ee\u6807\u5c31\u5728\u6570\u7ec4\u8d77\u59cb\u4f4d\u7f6e\u4e5f\u662f\u5982\u6b64\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u53e6\u4e00\u79cd\u66f4\u5feb\u7684\u65b9\u5f0f\u662f\u4f7f\u7528 Numba \u6765\u52a0\u901f next((i[0] for i, v in np.ndenumerate(a) if v==x), -1)\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e00\u65e6\u6570\u7ec4\u7684\u6392\u5e8f\u5b8c\u6210\uff0c\u641c\u7d22\u5c31\u5bb9\u6613\u591a\u4e86\uff1av = np.searchsorted(a, x); return v if a[v]==x else -1 \u7684\u901f\u5ea6\u5f88\u5feb\uff0c\u65f6\u95f4\u590d\u6742\u5ea6\u4e3a O(log N)\uff0c\u4f46\u5b83\u9700\u8981 O(N log N) \u65f6\u95f4\u5148\u6392\u597d\u5e8f\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e8b\u5b9e\u4e0a\uff0c\u7528 C \u6765\u5b9e\u73b0\u5b83\u8fdb\u800c\u52a0\u901f\u641c\u7d22\u5e76\u4e0d\u662f\u95ee\u9898\u3002\u95ee\u9898\u662f\u6d6e\u70b9\u6bd4\u8f83\u3002\u8fd9\u5bf9\u4efb\u4f55\u6570\u636e\u6765\u8bf4\u90fd\u4e0d\u662f\u4e00\u79cd\u7b80\u5355\u76f4\u63a5\u53ef\u7528\u7684\u4efb\u52a1\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u6bd4\u8f83\u6d6e\u70b9\u6570<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u51fd\u6570 np.allclose(a, b) \u80fd\u5728\u4e00\u5b9a\u516c\u5dee\u4e0b\u6bd4\u8f83\u6d6e\u70b9\u6570\u6570\u7ec4\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics1.baidu.com\/feed\/6a600c338744ebf808efa1dcb7620c2d6259a7de.jpeg@f_auto?token=23ec3cc42f19dbffc78352c4cf1c1499&s=5DA63C7207305C204E75E8CA0000E0B1","imgHeight":315,"imgWidth":1018,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u51fd\u6570 np.allclose(a, b) \u7684\u5de5\u4f5c\u8fc7\u7a0b\u793a\u4f8b\u3002\u5e76\u6ca1\u6709\u4e07\u80fd\u65b9\u6cd5\uff01<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">np.allclose \u5047\u8bbe\u6240\u6709\u88ab\u6bd4\u8f83\u7684\u6570\u90fd\u5728\u5178\u578b\u7684 1 \u7684\u8303\u56f4\u5185\u3002\u4e3e\u4e2a\u4f8b\u5b50\uff0c\u5982\u679c\u8981\u5728\u7eb3\u79d2\u7ea7\u7684\u901f\u5ea6\u5185\u5b8c\u6210\u8ba1\u7b97\uff0c\u5219\u9700\u8981\u7528\u9ed8\u8ba4\u7684 atol \u53c2\u6570\u503c\u9664\u4ee5 1e9\uff1anp.allclose(1e-9, 2e-9, atol=1e-17) == False.<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">math.isclose \u5219\u4e0d\u4f1a\u5bf9\u8981\u6bd4\u8f83\u7684\u6570\u8fdb\u884c\u4efb\u4f55\u5047\u8bbe\uff0c\u800c\u662f\u4f9d\u8d56\u7528\u6237\u7ed9\u51fa\u5408\u7406\u7684 abs_tol \u503c\uff08\u5bf9\u4e8e\u5178\u578b\u7684 1 \u7684\u8303\u56f4\u5185\u7684\u503c\uff0c\u53d6\u9ed8\u8ba4\u7684 np.allclose atol \u503c 1e-8 \u5c31\u8db3\u591f\u597d\u4e86\uff09\uff1amath.isclose(0.1+0.2\u20130.3, abs_tol=1e-8)==True.<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u9664\u6b64\u4e4b\u5916\uff0cnp.allclose \u5728\u7edd\u5bf9\u503c\u548c\u76f8\u5bf9\u516c\u5dee\u7684\u516c\u5f0f\u65b9\u9762\u8fd8\u6709\u4e00\u4e9b\u5c0f\u95ee\u9898\uff0c\u4e3e\u4e2a\u4f8b\u5b50\uff0c\u5bf9\u4e8e\u7ed9\u5b9a\u7684 a \u548c b\uff0c\u5b58\u5728 allclose(a, b) != allclose(b, a)\u3002\u8fd9\u4e9b\u95ee\u9898\u5df2\u5728\uff08\u6807\u91cf\uff09\u51fd\u6570 math.isclose \u4e2d\u5f97\u5230\u4e86\u89e3\u51b3\uff0c\u6211\u4eec\u5c06\u5728\u540e\u9762\u4ecb\u7ecd\u5b83\u3002\u5bf9\u4e8e\u8fd9\u65b9\u9762\u7684\u66f4\u591a\u5185\u5bb9\uff0c\u8bf7\u53c2\u9605 GitHub \u4e0a\u7684\u6d6e\u70b9\u6570\u6307\u5357\u548c\u5bf9\u5e94\u7684 NumPy \u95ee\u9898\uff08https:\/\/floating-point-gui.de\/errors\/comparison\/\uff09\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u77e9\u9635\uff1a\u4e8c\u7ef4\u6570\u7ec4<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u66fe\u6709\u4e00\u4e2a\u4e13\u95e8\u7684 matrix \u7c7b\uff0c\u4f46\u73b0\u5728\u5df2\u7ecf\u5f03\u7528\u4e86\uff0c\u6240\u4ee5\u672c\u6587\u4f1a\u4ea4\u66ff\u4f7f\u7528\u300c\u77e9\u9635\u300d\u548c\u300c\u4e8c\u7ef4\u6570\u7ec4\u300d\u8fd9\u4e24\u4e2a\u672f\u8bed\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u77e9\u9635\u7684\u521d\u59cb\u5316\u53e5\u6cd5\u4e0e\u5411\u91cf\u7c7b\u4f3c\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics6.baidu.com\/feed\/86d6277f9e2f0708166e91a798bf639ea801f255.jpeg@f_auto?token=e5a9da845116d79c2da326246c8f7e06&s=98A15D321B024C4B0244B8DA0200A0B1","imgHeight":575,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u91cc\u5fc5\u987b\u4f7f\u7528\u53cc\u62ec\u53f7\uff0c\u56e0\u4e3a\u7b2c\u4e8c\u4e2a\u4f4d\u7f6e\u53c2\u6570\u662f dtype\uff08\u53ef\u9009\uff0c\u4e5f\u63a5\u53d7\u6574\u6570\uff09\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u968f\u673a\u77e9\u9635\u751f\u6210\u7684\u53e5\u6cd5\u4e5f\u4e0e\u5411\u91cf\u7684\u7c7b\u4f3c\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics4.baidu.com\/feed\/5fdf8db1cb1349544a6ac98038d5495fd0094a04.jpeg@f_auto?token=0a4cb4ba848d35b09125a0875538ee3b&s=00947D321B506C41547D60DE020060B1","imgHeight":510,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4e8c\u7ef4\u7d22\u5f15\u7684\u53e5\u6cd5\u6bd4\u5d4c\u5957\u5217\u8868\u66f4\u65b9\u4fbf\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics0.baidu.com\/feed\/2cf5e0fe9925bc31ffa09a7e304456b6c91370fe.jpeg@f_auto?token=45883b5a8f4a2454df2b7f82425946ae&s=B1335E321B705C095AD0B5C9020010B0","imgHeight":451,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">view \u7b26\u53f7\u7684\u610f\u601d\u662f\u5f53\u5207\u5206\u4e00\u4e2a\u6570\u7ec4\u65f6\u5b9e\u9645\u4e0a\u6ca1\u6709\u6267\u884c\u590d\u5236\u3002\u5f53\u8be5\u6570\u7ec4\u88ab\u4fee\u6539\u65f6\uff0c\u8fd9\u4e9b\u6539\u53d8\u4e5f\u4f1a\u53cd\u6620\u5230\u5207\u5206\u5f97\u5230\u7684\u7ed3\u679c\u4e0a\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">axis \u53c2\u6570<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5728\u5f88\u591a\u8fd0\u7b97\u4e2d\uff08\u6bd4\u5982 sum\uff09\uff0c\u4f60\u9700\u8981\u544a\u8bc9 NumPy \u662f\u5728\u5217\u4e0a\u8fd8\u662f\u884c\u4e0a\u6267\u884c\u8fd0\u7b97\u3002\u4e3a\u4e86\u83b7\u53d6\u9002\u7528\u4e8e\u4efb\u610f\u7ef4\u5ea6\u7684\u901a\u7528\u7b26\u53f7\uff0cNumPy \u5f15\u5165\u4e86 axis \u7684\u6982\u5ff5\uff1a\u4e8b\u5b9e\u4e0a\uff0caxis \u53c2\u6570\u7684\u503c\u662f\u76f8\u5173\u95ee\u9898\u4e2d\u7d22\u5f15\u7684\u6570\u91cf\uff1a\u7b2c\u4e00\u4e2a\u7d22\u5f15\u4e3a axis=0\uff0c\u7b2c\u4e8c\u4e2a\u7d22\u5f15\u4e3a axis=1\uff0c\u4ee5\u6b64\u7c7b\u63a8\u3002\u56e0\u6b64\u5728\u4e8c\u7ef4\u60c5\u51b5\u4e0b\uff0caxis=0 \u662f\u6309\u5217\u8ba1\u7b97\uff0caxis=1 \u662f\u6309\u884c\u8ba1\u7b97\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/91ef76c6a7efce1b1d0f3effc0ca28d9b48f6506.jpeg@f_auto?token=0a0cb11bb5179e1222579561823dcc4d&s=CCAC1D721F426949144D48DA020070B2","imgHeight":427,"imgWidth":826,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u77e9\u9635\u7b97\u672f\u8fd0\u7b97<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u9664\u4e86\u9010\u5143\u7d20\u6267\u884c\u7684\u5e38\u89c4\u8fd0\u7b97\u7b26\uff08\u6bd4\u5982 +\u3001-\u3001\u3001\/\u3001\/\/\u3001*\uff09\uff0c\u8fd9\u91cc\u8fd8\u6709\u4e00\u4e2a\u8ba1\u7b97\u77e9\u9635\u4e58\u79ef\u7684 @ \u8fd0\u7b97\u7b26\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics6.baidu.com\/feed\/d833c895d143ad4b0162a1f8ed9981a8a50f06d9.jpeg@f_auto?token=81c030cc7cd1c7ee4f4b50767080ae92&s=19A27D321B705C0B566C40CA0200F0B1","imgHeight":531,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u6211\u4eec\u5df2\u5728\u7b2c\u4e00\u90e8\u5206\u4ecb\u7ecd\u8fc7\u6807\u91cf\u5230\u6570\u7ec4\u7684\u5e7f\u64ad\uff0c\u5728\u5176\u57fa\u7840\u4e0a\u8fdb\u884c\u6cdb\u5316\u540e\uff0cNumPy \u652f\u6301\u5411\u91cf\u548c\u77e9\u9635\u7684\u6df7\u5408\u8fd0\u7b97\uff0c\u751a\u81f3\u4e24\u4e2a\u5411\u91cf\u4e4b\u95f4\u7684\u8fd0\u7b97\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics0.baidu.com\/feed\/bd3eb13533fa828b85b7ec0f93849a33950a5aec.jpeg@f_auto?token=d2d0fa1e6f9ba0064531fb46d8b708f0&s=B9A27D331B02454B5CCD19CF020040B3","imgHeight":679,"imgWidth":1072,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4e8c\u7ef4\u6570\u7ec4\u4e2d\u7684\u5e7f\u64ad<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u884c\u5411\u91cf\u548c\u5217\u5411\u91cf<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u6b63\u5982\u4e0a\u9762\u7684\u4f8b\u5b50\u6240\u793a\uff0c\u5728\u4e8c\u7ef4\u60c5\u51b5\u4e0b\uff0c\u884c\u5411\u91cf\u548c\u5217\u5411\u91cf\u7684\u5904\u7406\u65b9\u5f0f\u6709\u6240\u4e0d\u540c\u3002\u8fd9\u4e0e\u5177\u5907\u67d0\u7c7b\u4e00\u7ef4\u6570\u7ec4\u7684 NumPy \u5b9e\u8df5\u4e0d\u540c\uff08\u6bd4\u5982\u4e8c\u7ef4\u6570\u7ec4 a\u2014 \u7684\u7b2c j \u5217 a[:,j] \u662f\u4e00\u4e2a\u4e00\u7ef4\u6570\u7ec4\uff09\u3002\u9ed8\u8ba4\u60c5\u51b5\u4e0b\uff0c\u4e00\u7ef4\u6570\u7ec4\u4f1a\u88ab\u89c6\u4e3a\u4e8c\u7ef4\u8fd0\u7b97\u4e2d\u7684\u884c\u5411\u91cf\uff0c\u56e0\u6b64\u5f53\u7528\u4e00\u4e2a\u77e9\u9635\u4e58\u4ee5\u4e00\u4e2a\u884c\u5411\u91cf\u65f6\uff0c\u4f60\u53ef\u4ee5\u4f7f\u7528\u5f62\u72b6 (n,) \u6216 (1, n)\u2014\u2014\u7ed3\u679c\u662f\u4e00\u6837\u7684\u3002\u5982\u679c\u4f60\u9700\u8981\u4e00\u4e2a\u5217\u5411\u91cf\uff0c\u5219\u6709\u591a\u79cd\u65b9\u6cd5\u53ef\u4ee5\u57fa\u4e8e\u4e00\u7ef4\u6570\u7ec4\u5f97\u5230\u5b83\uff0c\u4f46\u51fa\u4eba\u610f\u6599\u7684\u662f\u300c\u8f6c\u7f6e\u300d\u4e0d\u662f\u5176\u4e2d\u4e4b\u4e00\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/b3b7d0a20cf431ad0a1a1ed125ad77a82cdd98b5.jpeg@f_auto?token=68e2240b6269a99452501dbab54ecc1e&s=FDA63D7317724021585DF4CA0000E0B0","imgHeight":566,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u57fa\u4e8e\u4e00\u7ef4\u6570\u7ec4\u5f97\u5230\u4e8c\u7ef4\u6570\u7ec4\u7684\u8fd0\u7b97\u6709\u4e24\u79cd\uff1a\u4f7f\u7528 reshape \u8c03\u6574\u5f62\u72b6\u548c\u4f7f\u7528 newaxis \u8fdb\u884c\u7d22\u5f15\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/d6ca7bcb0a46f21f35b8067e87bfb0670d33aeb6.jpeg@f_auto?token=f612c116262906bcd7e230d4bc42efec&s=04F4ED3217707C211A5D19CB020030B0","imgHeight":521,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5176\u4e2d -1 \u8fd9\u4e2a\u53c2\u6570\u662f\u544a\u8bc9 reshape \u81ea\u52a8\u8ba1\u7b97\u5176\u4e2d\u4e00\u4e2a\u7ef4\u5ea6\u5927\u5c0f\uff0c\u65b9\u62ec\u53f7\u4e2d\u7684 None \u662f\u7528\u4f5c np.newaxis \u7684\u5feb\u6377\u65b9\u5f0f\uff0c\u8fd9\u4f1a\u5728\u6307\u5b9a\u4f4d\u7f6e\u6dfb\u52a0\u4e00\u4e2a\u7a7a axis\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u56e0\u6b64\uff0cNumPy \u5171\u6709\u4e09\u7c7b\u5411\u91cf\uff1a\u4e00\u7ef4\u5411\u91cf\u3001\u4e8c\u7ef4\u884c\u5411\u91cf\u548c\u4e8c\u7ef4\u5217\u5411\u91cf\u3002\u4e0b\u56fe\u5c55\u793a\u4e86\u8fd9\u4e09\u79cd\u5411\u91cf\u4e4b\u95f4\u7684\u8f6c\u6362\u65b9\u5f0f\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics4.baidu.com\/feed\/fc1f4134970a304ecee8eac6a0537c81c8175c63.jpeg@f_auto?token=229fd43ee231c3e3b8e9695e8332e763&s=18A6753229834C43524860CE000070B3","imgHeight":487,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4e00\u7ef4\u5411\u91cf\u3001\u4e8c\u7ef4\u884c\u5411\u91cf\u548c\u4e8c\u7ef4\u5217\u5411\u91cf\u4e4b\u95f4\u7684\u8f6c\u6362\u65b9\u5f0f\u3002\u6839\u636e\u5e7f\u64ad\u7684\u539f\u5219\uff0c\u4e00\u7ef4\u6570\u7ec4\u53ef\u88ab\u9690\u542b\u5730\u89c6\u4e3a\u4e8c\u7ef4\u884c\u5411\u91cf\uff0c\u56e0\u6b64\u901a\u5e38\u6ca1\u5fc5\u8981\u5728\u8fd9\u4e24\u8005\u4e4b\u95f4\u6267\u884c\u8f6c\u6362\u2014\u2014\u56e0\u6b64\u76f8\u5e94\u7684\u533a\u57df\u88ab\u9634\u5f71\u5316\u5904\u7406\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u77e9\u9635\u64cd\u4f5c<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5408\u5e76\u6570\u7ec4\u7684\u51fd\u6570\u4e3b\u8981\u6709\u4e24\u4e2a\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/024f78f0f736afc30ebb3285dd8230c3b6451239.jpeg@f_auto?token=8632fed752c1ac3a12c0b6b6791cda9a&s=4EF0A5560F124C4D184DA0DA0200C0B3","imgHeight":592,"imgWidth":987,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u4e24\u4e2a\u51fd\u6570\u9002\u7528\u4e8e\u53ea\u5806\u53e0\u77e9\u9635\u6216\u53ea\u5806\u53e0\u5411\u91cf\uff0c\u4f46\u5f53\u9700\u8981\u5806\u53e0\u4e00\u7ef4\u6570\u7ec4\u548c\u77e9\u9635\u65f6\uff0c\u53ea\u6709 vstack \u53ef\u4ee5\u594f\u6548\uff1ahstack \u4f1a\u51fa\u73b0\u7ef4\u5ea6\u4e0d\u5339\u914d\u7684\u9519\u8bef\uff0c\u539f\u56e0\u5982\u524d\u6240\u8ff0\uff0c\u4e00\u7ef4\u6570\u7ec4\u4f1a\u88ab\u89c6\u4e3a\u884c\u5411\u91cf\uff0c\u800c\u4e0d\u662f\u5217\u5411\u91cf\u3002\u9488\u5bf9\u8fd9\u4e2a\u95ee\u9898\uff0c\u89e3\u51b3\u65b9\u6cd5\u8981\u4e48\u662f\u5c06\u5176\u8f6c\u6362\u4e3a\u884c\u5411\u91cf\uff0c\u8981\u4e48\u662f\u4f7f\u7528\u80fd\u81ea\u52a8\u5b8c\u6210\u8fd9\u4e00\u64cd\u4f5c\u7684 column_stack \u51fd\u6570\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/96dda144ad3459826640816f626feaaac9ef84ac.jpeg@f_auto?token=3217701d19b9ce02993ccb86ebdab78e&s=8EF6E5161B03484918CDA4DA020010B3","imgHeight":586,"imgWidth":944,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5806\u53e0\u7684\u9006\u64cd\u4f5c\u662f\u62c6\u5206\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/6609c93d70cf3bc75f0d4f23bf9b61a6cc112a17.jpeg@f_auto?token=8827c6f42454bc4db19bdd1d309e8b8f&s=C7F6A97615064D4B185580DA0200D0B3","imgHeight":635,"imgWidth":1047,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u590d\u5236\u77e9\u9635\u7684\u65b9\u6cd5\u6709\u4e24\u79cd\uff1a\u590d\u5236 - \u7c98\u8d34\u5f0f\u7684 tile \u548c\u5206\u9875\u6253\u5370\u5f0f\u7684 repeat\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics1.baidu.com\/feed\/caef76094b36acaff483f3ed0d42561700e99ce7.jpeg@f_auto?token=07600ccb0e62c978ba6e11d48eaec507&s=44F62D761B01494B0A71DDC90300E0F1","imgHeight":512,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">delete \u53ef\u4ee5\u5220\u9664\u7279\u5b9a\u7684\u884c\u548c\u5217\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics5.baidu.com\/feed\/63d0f703918fa0ec5753d6ec480c82e93f6ddbda.jpeg@f_auto?token=c302b8a2928f0610fd11206f97ff870c&s=85F6E537011445CC5A4C80CB02005033","imgHeight":617,"imgWidth":897,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5220\u9664\u7684\u9006\u64cd\u4f5c\u4e3a\u63d2\u5165\uff0c\u5373 insert\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics1.baidu.com\/feed\/622762d0f703918fa50cd7f33ea6fd9058eec449.jpeg@f_auto?token=25e99ba63ea1d88bdf80ab462e0228e0&s=45B43D720543574F0C41E8DF020070B0","imgHeight":606,"imgWidth":991,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">append \u51fd\u6570\u5c31\u50cf hstack \u4e00\u6837\uff0c\u4e0d\u80fd\u81ea\u52a8\u5bf9\u4e00\u7ef4\u6570\u7ec4\u6267\u884c\u8f6c\u7f6e\uff0c\u56e0\u6b64\u540c\u6837\u5730\uff0c\u8981\u4e48\u9700\u8981\u6539\u53d8\u8be5\u5411\u91cf\u7684\u5f62\u72b6\uff0c\u8981\u4e48\u5c31\u9700\u8981\u589e\u52a0\u4e00\u4e2a\u7ef4\u5ea6\uff0c\u6216\u8005\u4f7f\u7528 column_stack\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/caef76094b36acaf9832d5c11342561700e99c64.jpeg@f_auto?token=6a056d7f8c1816bd034d458f7290b2f0&s=95B4ED370B61790144CD15CB02007032","imgHeight":497,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4e8b\u5b9e\u4e0a\uff0c\u5982\u679c\u4f60\u53ea\u9700\u8981\u5411\u6570\u7ec4\u7684\u8fb9\u7f18\u6dfb\u52a0\u5e38\u91cf\u503c\uff0c\u90a3\u4e48\uff08\u7a0d\u5fae\u590d\u6742\u7684\uff09pad \u51fd\u6570\u5e94\u8be5\u5c31\u8db3\u591f\u4e86\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/9358d109b3de9c82f060f0b5031a5b0d19d84321.jpeg@f_auto?token=72633f393a463ed2ea87398ae232f1c5&s=94B67D3717405D4B184D5C4A0200F071","imgHeight":475,"imgWidth":927,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u7f51\u683c<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5e7f\u64ad\u89c4\u5219\u4f7f\u5f97\u6211\u4eec\u80fd\u66f4\u7b80\u5355\u5730\u64cd\u4f5c\u7f51\u683c\u3002\u5047\u8bbe\u4f60\u6709\u5982\u4e0b\u77e9\u9635\uff08\u4f46\u975e\u5e38\u5927\uff09\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/7e3e6709c93d70cfa53547ed96470a07b8a12ba2.jpeg@f_auto?token=48f6f623f8677c3fbfa73ed105992636&s=99A85F329FD46C430C7061DF0000C0B2","imgHeight":284,"imgWidth":839,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4f7f\u7528 C \u548c\u4f7f\u7528 Python \u521b\u5efa\u77e9\u9635\u7684\u5bf9\u6bd4<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u4e24\u79cd\u65b9\u6cd5\u8f83\u6162\uff0c\u56e0\u4e3a\u5b83\u4eec\u4f1a\u4f7f\u7528 Python \u5faa\u73af\u3002\u4e3a\u4e86\u89e3\u51b3\u8fd9\u6837\u7684\u95ee\u9898\uff0cMATLAB \u7684\u65b9\u5f0f\u662f\u521b\u5efa\u4e00\u4e2a\u7f51\u683c\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/024f78f0f736afc390bdcedcc38230c3b645127d.jpeg@f_auto?token=5065ff3523e132aae1050e926e997779&s=90945D328FF15C034664DCCC020010B1","imgHeight":358,"imgWidth":1018,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4f7f\u7528 MATLAB \u521b\u5efa\u7f51\u683c\u7684\u793a\u610f\u56fe<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4f7f\u7528\u5982\u4e0a\u63d0\u4f9b\u7684\u53c2\u6570 I \u548c J\uff0cmeshgrid \u51fd\u6570\u63a5\u53d7\u4efb\u610f\u7684\u7d22\u5f15\u96c6\u5408\u4f5c\u4e3a\u8f93\u5165\uff0cmgrid \u53ea\u662f\u5207\u5206\uff0cindices \u53ea\u80fd\u751f\u6210\u5b8c\u6574\u7684\u7d22\u5f15\u8303\u56f4\uff0cfromfunction \u53ea\u4f1a\u8c03\u7528\u6240\u63d0\u4f9b\u7684\u51fd\u6570\u4e00\u6b21\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4f46\u5b9e\u9645\u4e0a\uff0cNumPy \u4e2d\u8fd8\u6709\u4e00\u79cd\u66f4\u597d\u7684\u65b9\u6cd5\u3002\u6211\u4eec\u6ca1\u5fc5\u8981\u5c06\u5185\u5b58\u8017\u5728\u6574\u4e2a I \u548c J \u77e9\u9635\u4e0a\u3002\u5b58\u50a8\u5f62\u72b6\u5408\u9002\u7684\u5411\u91cf\u5c31\u8db3\u591f\u4e86\uff0c\u5e7f\u64ad\u89c4\u5219\u53ef\u4ee5\u5b8c\u6210\u5176\u4f59\u5de5\u4f5c\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics4.baidu.com\/feed\/80cb39dbb6fd5266b97223f7c5834c2cd507361e.jpeg@f_auto?token=c19f43aa7caa965ec238e881262fb61c&s=B0847D3297F46C21466C1CD80200F0B1","imgHeight":311,"imgWidth":924,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4f7f\u7528 NumPy \u521b\u5efa\u7f51\u683c\u7684\u793a\u610f\u56fe<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u6ca1\u6709 indexing=\u2019ij\u2019 \u53c2\u6570\uff0cmeshgrid \u4f1a\u6539\u53d8\u8fd9\u4e9b\u53c2\u6570\u7684\u987a\u5e8f\uff1aJ, I= np.meshgrid(j, i)\u2014\u2014\u8fd9\u662f\u4e00\u79cd xy \u6a21\u5f0f\uff0c\u5bf9\u53ef\u89c6\u5316 3D \u56fe\u8868\u5f88\u6709\u7528\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u9664\u4e86\u5728\u4e8c\u7ef4\u6216\u4e09\u7ef4\u7f51\u683c\u4e0a\u521d\u59cb\u5316\u51fd\u6570\uff0c\u7f51\u683c\u4e5f\u53ef\u7528\u4e8e\u7d22\u5f15\u6570\u7ec4\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/eaf81a4c510fd9f9a622f3584ab60f2d2834a469.jpeg@f_auto?token=572e20f900dcd1b586310ef12f863dc2&s=91A47D32C522E520425930C20200F0B1","imgHeight":188,"imgWidth":955,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4f7f\u7528 meshgrid \u7d22\u5f15\u6570\u7ec4\uff0c\u4e5f\u9002\u7528\u4e8e\u7a00\u758f\u7f51\u683c\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u83b7\u53d6\u77e9\u9635\u7edf\u8ba1\u6570\u636e<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u548c sum \u4e00\u6837\uff0cmin\u3001max\u3001argmin\u3001argmax\u3001mean\u3001std\u3001var \u7b49\u6240\u6709\u5176\u5b83\u7edf\u8ba1\u51fd\u6570\u90fd\u652f\u6301 axis \u53c2\u6570\u5e76\u80fd\u636e\u6b64\u5b8c\u6210\u7edf\u8ba1\u8ba1\u7b97\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics4.baidu.com\/feed\/f7246b600c338744f2f0fd4b3e9402fed72aa077.jpeg@f_auto?token=7706cba442416aeb907605862bc7df85&s=1AAC5C221F424441146548D8020060B1","imgHeight":463,"imgWidth":885,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4e09\u4e2a\u7edf\u8ba1\u51fd\u6570\u793a\u4f8b\uff0c\u4e3a\u4e86\u907f\u514d\u4e0e Python \u7684 min \u51b2\u7a81\uff0cNumPy \u4e2d\u5bf9\u5e94\u7684\u51fd\u6570\u540d\u4e3a np.amin\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u7528\u4e8e\u4e8c\u7ef4\u53ca\u66f4\u9ad8\u7ef4\u7684 argmin \u548c argmax \u51fd\u6570\u4f1a\u8fd4\u56de\u6700\u5c0f\u548c\u6700\u5927\u503c\u7684\u7b2c\u4e00\u4e2a\u5b9e\u4f8b\uff0c\u5728\u8fd4\u56de\u5c55\u5f00\u7684\u7d22\u5f15\u4e0a\u6709\u70b9\u9ebb\u70e6\u3002\u4e3a\u4e86\u5c06\u5176\u8f6c\u6362\u6210\u4e24\u4e2a\u5750\u6807\uff0c\u9700\u8981\u4f7f\u7528 unravel_index \u51fd\u6570\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/f2deb48f8c5494ee54b19f935d6e3bf99b257ec8.jpeg@f_auto?token=0046d1f74b41645127346ac78d4f67a5&s=DAAC3C620F426D4B1EEC41DA020050B3","imgHeight":551,"imgWidth":1070,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4f7f\u7528 unravel_index \u51fd\u6570\u7684\u793a\u4f8b<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">all \u548c any \u51fd\u6570\u4e5f\u652f\u6301 axis \u53c2\u6570\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics4.baidu.com\/feed\/b90e7bec54e736d1b5c327e1f5cb94c5d76269ae.jpeg@f_auto?token=5dd6b255b6bc62813ea1589269159089&s=DAAC18621B404443146DD0DA020030B1","imgHeight":448,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4f7f\u7528 all \u548c any \u51fd\u6570\u7684\u793a\u4f8b<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u77e9\u9635\u6392\u5e8f<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">axis \u53c2\u6570\u867d\u7136\u5bf9\u4e0a\u9762\u5217\u51fa\u7684\u51fd\u6570\u5f88\u6709\u7528\uff0c\u4f46\u5bf9\u6392\u5e8f\u6beb\u65e0\u7528\u5904\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics6.baidu.com\/feed\/d62a6059252dd42aade3b7956ca080b2c8eab84d.jpeg@f_auto?token=f4f74e47532eef41e26003e9054941a7&s=BDA47D32150A454912F8B8CA000070B3","imgHeight":628,"imgWidth":945,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u4f7f\u7528 Python \u5217\u8868\u548c NumPy \u6570\u7ec4\u6267\u884c\u6392\u5e8f\u7684\u6bd4\u8f83<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u901a\u5e38\u4e0d\u662f\u4f60\u5728\u6392\u5e8f\u77e9\u9635\u6216\u7535\u5b50\u8868\u683c\u65f6\u5e0c\u671b\u770b\u5230\u7684\u7ed3\u679c\uff1aaxis \u6839\u672c\u4e0d\u80fd\u66ff\u4ee3 key \u53c2\u6570\u3002\u4f46\u5e78\u8fd0\u7684\u662f\uff0cNumPy \u63d0\u4f9b\u4e86\u4e00\u4e9b\u652f\u6301\u6309\u5217\u6392\u5e8f\u7684\u8f85\u52a9\u51fd\u6570\u2014\u2014\u6216\u6709\u9700\u8981\u7684\u8bdd\u53ef\u6309\u591a\u5217\u6392\u5e8f\uff1a<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">1. a[a[:,0].argsort()] \u53ef\u6309\u7b2c\u4e00\u5217\u5bf9\u6570\u7ec4\u6392\u5e8f\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/c995d143ad4bd1138bcf290635347f084bfb0523.jpeg@f_auto?token=94ac04a246fde37fd1462177ff5b9eb1&s=F5566E3285A16132004690CA020050B2","imgHeight":276,"imgWidth":941,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u91cc argsort \u4f1a\u8fd4\u56de\u539f\u6570\u7ec4\u6392\u5e8f\u540e\u7684\u7d22\u5f15\u7684\u6570\u7ec4\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u4e2a\u6280\u5de7\u53ef\u4ee5\u91cd\u590d\uff0c\u4f46\u5fc5\u987b\u8c28\u614e\uff0c\u522b\u8ba9\u4e0b\u4e00\u6b21\u6392\u5e8f\u6270\u4e71\u4e0a\u4e00\u6b21\u6392\u5e8f\u7684\u7ed3\u679c\uff1a<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">a = a[a[:,2].argsort()]<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">a = a[a[:,1].argsort(kind='stable')]<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">a = a[a[:,0].argsort(kind='stable')]<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics6.baidu.com\/feed\/0b55b319ebc4b74562481023a067c5108b82154d.jpeg@f_auto?token=4f7552230680a9f9160aeb4991b72853&s=95B04D32C531503B4EDF31DE0200D0B2","imgHeight":317,"imgWidth":1028,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">2. lexsort \u51fd\u6570\u80fd\u4f7f\u7528\u4e0a\u8ff0\u65b9\u5f0f\u6839\u636e\u6240\u6709\u5217\u8fdb\u884c\u6392\u5e8f\uff0c\u4f46\u5b83\u603b\u662f\u6309\u884c\u6267\u884c\uff0c\u800c\u4e14\u6240\u8981\u6392\u5e8f\u7684\u884c\u7684\u987a\u5e8f\u662f\u53cd\u5411\u7684\uff08\u5373\u81ea\u4e0b\u800c\u4e0a\uff09\uff0c\u56e0\u6b64\u4f7f\u7528\u5b83\u65f6\u4f1a\u6709\u4e9b\u4e0d\u81ea\u7136\uff0c\u6bd4\u5982<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">- a[np.lexsort(np.flipud(a[2,5].T))] \u4f1a\u9996\u5148\u6839\u636e\u7b2c 2 \u5217\u6392\u5e8f\uff0c\u7136\u540e\uff08\u5f53\u7b2c 2 \u5217\u7684\u503c\u76f8\u7b49\u65f6\uff09\u518d\u6839\u636e\u7b2c 5 \u5217\u6392\u5e8f\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u2013 a[np.lexsort(np.flipud(a.T))] \u4f1a\u4ece\u5de6\u5411\u53f3\u6839\u636e\u6240\u6709\u5217\u6392\u5e8f\u3002<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics1.baidu.com\/feed\/d31b0ef41bd5ad6eeb3107d9ee50e2dcb7fd3c5f.jpeg@f_auto?token=ef41c73386f18a21f8235016d3103c65&s=B0215C320D625D20544164DE020010B2","imgHeight":294,"imgWidth":984,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u91cc\uff0cflipud \u4f1a\u6cbf\u4e0a\u4e0b\u65b9\u5411\u7ffb\u8f6c\u8be5\u77e9\u9635\uff08\u51c6\u786e\u5730\u8bf4\u662f axis=0 \u65b9\u5411\uff0c\u4e0e a[::-1,...] \u4e00\u6837\uff0c\u5176\u4e2d\u4e09\u4e2a\u70b9\u8868\u793a\u300c\u6240\u6709\u5176\u5b83\u7ef4\u5ea6\u300d\uff0c\u56e0\u6b64\u7ffb\u8f6c\u8fd9\u4e2a\u4e00\u7ef4\u6570\u7ec4\u7684\u662f\u7a81\u7136\u7684 flipud\uff0c\u800c\u4e0d\u662f fliplr\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">3. sort \u8fd8\u6709\u4e00\u4e2a order \u53c2\u6570\uff0c\u4f46\u5982\u679c\u4e00\u5f00\u59cb\u662f\u666e\u901a\u7684\uff08\u975e\u7ed3\u6784\u5316\uff09\u6570\u7ec4\uff0c\u5b83\u6267\u884c\u8d77\u6765\u65e2\u4e0d\u5feb\uff0c\u4e5f\u4e0d\u5bb9\u6613\u4f7f\u7528\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">4. \u5728 pandas \u4e2d\u6267\u884c\u5b83\u53ef\u80fd\u662f\u66f4\u597d\u7684\u9009\u62e9\uff0c\u56e0\u4e3a\u5728 pandas \u4e2d\uff0c\u8be5\u7279\u5b9a\u8fd0\u7b97\u7684\u53ef\u8bfb\u6027\u8981\u9ad8\u5f97\u591a\uff0c\u4e5f\u4e0d\u90a3\u4e48\u5bb9\u6613\u51fa\u9519\uff1a<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u2013 pd.DataFrame(a).sort_values(by=[2,5]).to_numpy() \u4f1a\u5148\u6839\u636e\u7b2c 2 \u5217\u6392\u5e8f\uff0c\u7136\u540e\u6839\u636e\u7b2c 5 \u5217\u6392\u5e8f\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u2013 pd.DataFrame(a).sort_values().to_numpy() \u4f1a\u4ece\u5de6\u5411\u53f3\u6839\u636e\u6240\u6709\u5217\u6392\u5e8f\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\"><span class=\"bjh-strong\">\u4e09\u7ef4\u53ca\u66f4\u9ad8\u7ef4<\/span><\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5f53\u4f60\u901a\u8fc7\u8c03\u6574\u4e00\u7ef4\u5411\u91cf\u7684\u5f62\u72b6\u6216\u8f6c\u6362\u5d4c\u5957\u7684 Python \u5217\u8868\u6765\u521b\u5efa 3D \u6570\u7ec4\u65f6\uff0c\u7d22\u5f15\u7684\u542b\u4e49\u662f (z,y,x)\u3002\u7b2c\u4e00\u4e2a\u7d22\u5f15\u662f\u5e73\u9762\u7684\u6570\u91cf\uff0c\u7136\u540e\u662f\u5728\u8be5\u5e73\u9762\u4e0a\u7684\u5750\u6807\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/359b033b5bb5c9ea1a4acb58a5a26d073bf3b377.jpeg@f_auto?token=1080d057727ce0ade09ad7574baba051&s=38065D321F524C415058D4CB0200F0B1","imgHeight":512,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5c55\u793a (z,y,x) \u987a\u5e8f\u7684\u793a\u610f\u56fe<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u4e2a\u7d22\u5f15\u987a\u5e8f\u5f88\u65b9\u4fbf\uff0c\u4e3e\u4e2a\u4f8b\u5b50\uff0c\u5b83\u53ef\u7528\u4e8e\u4fdd\u5b58\u4e00\u4e9b\u7070\u5ea6\u56fe\u50cf\uff1aa[i] \u662f\u7d22\u5f15\u7b2c i \u5f20\u56fe\u50cf\u7684\u5feb\u6377\u65b9\u5f0f\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4f46\u8fd9\u4e2a\u7d22\u5f15\u987a\u5e8f\u4e0d\u662f\u901a\u7528\u7684\u3002\u5f53\u64cd\u4f5c RGB \u56fe\u50cf\u65f6\uff0c\u901a\u5e38\u4f1a\u4f7f\u7528 (y,x,z) \u987a\u5e8f\uff1a\u9996\u5148\u662f\u4e24\u4e2a\u50cf\u7d20\u5750\u6807\uff0c\u6700\u540e\u4e00\u4e2a\u662f\u989c\u8272\u5750\u6807\uff08Matplotlib \u4e2d\u662f RGB\uff0cOpenCV \u4e2d\u662f BGR\uff09\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/a8773912b31bb05192e6d35c46e101b34bede023.jpeg@f_auto?token=732f7bc070fd327f83584af62d89891b&s=F4976C321B524841024519D80200A0B2","imgHeight":489,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5c55\u793a (y,x,z) \u987a\u5e8f\u7684\u793a\u610f\u56fe<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u6837\uff0c\u6211\u4eec\u5c31\u80fd\u5f88\u65b9\u4fbf\u5730\u7d22\u5f15\u7279\u5b9a\u7684\u50cf\u7d20\uff1aa[i,j] \u80fd\u63d0\u4f9b (i,j) \u4f4d\u7f6e\u7684 RGB \u5143\u7ec4\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u56e0\u6b64\uff0c\u521b\u5efa\u51e0\u4f55\u5f62\u72b6\u7684\u5b9e\u9645\u547d\u4ee4\u53d6\u51b3\u4e8e\u4f60\u6240\u5728\u9886\u57df\u7684\u60ef\u4f8b\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/a9d3fd1f4134970a534d23cbe4510acfa6865dbe.jpeg@f_auto?token=9f582985edf5029563164b05756ef6e5&s=14D0ED3215C85D490A7441CC0200A031","imgHeight":522,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u521b\u5efa\u4e00\u822c\u7684\u4e09\u7ef4\u6570\u7ec4\u548c RGB \u56fe\u50cf<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5f88\u663e\u7136\uff0chstack\u3001vstack\u3001dstack \u8fd9\u4e9b\u51fd\u6570\u4e0d\u652f\u6301\u8fd9\u4e9b\u60ef\u4f8b\u3002\u5b83\u4eec\u786c\u7f16\u7801\u4e86 (y,x,z) \u7684\u7d22\u5f15\u987a\u5e8f\uff0c\u5373 RGB \u56fe\u50cf\u7684\u987a\u5e8f\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/aec379310a55b3198fc2fb2e33325921cefc17d0.jpeg@f_auto?token=3f9b84ef0e7f8421cc6bdc66df26ba71&s=05F4ED325102454D02C9C5DA0200C0B3","imgHeight":679,"imgWidth":1036,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">NumPy \u4f7f\u7528 (y,x,z) \u987a\u5e8f\u7684\u793a\u610f\u56fe\uff0c\u5806\u53e0 RGB \u56fe\u50cf\uff08\u8fd9\u91cc\u4ec5\u6709\u4e24\u79cd\u989c\u8272\uff09<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5982\u679c\u4f60\u7684\u6570\u636e\u5e03\u5c40\u4e0d\u540c\uff0c\u4f7f\u7528 concatenate \u547d\u4ee4\u6765\u5806\u53e0\u56fe\u50cf\u4f1a\u66f4\u65b9\u4fbf\u4e00\u4e9b\uff0c\u5411\u4e00\u4e2a axis \u53c2\u6570\u8f93\u5165\u660e\u786e\u7684\u7d22\u5f15\u6570\u503c\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics3.baidu.com\/feed\/91ef76c6a7efce1bb2e5ee24dfca28d9b68f65b0.jpeg@f_auto?token=9b2555afd3cf7ff259cc0ec3827d24b8&s=04F4ED321102554D0249C5DA02005033","imgHeight":660,"imgWidth":1000,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5806\u53e0\u4e00\u822c\u4e09\u7ef4\u6570\u7ec4<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5982\u679c\u4f60\u4e0d\u4e60\u60ef\u601d\u8003 axis \u6570\uff0c\u4f60\u53ef\u4ee5\u5c06\u8be5\u6570\u7ec4\u8f6c\u6362\u6210 hstack \u7b49\u51fd\u6570\u4e2d\u786c\u7f16\u7801\u7684\u5f62\u5f0f\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics7.baidu.com\/feed\/6d81800a19d8bc3e5a284beaed107d19a8d34515.jpeg@f_auto?token=87202a2cbda2848b3ab81875031dfd6e&s=CC865D32110A6549124814C8020030B1","imgHeight":577,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5c06\u6570\u7ec4\u8f6c\u6362\u4e3a hstack \u4e2d\u786c\u7f16\u7801\u7684\u5f62\u5f0f\u7684\u793a\u610f\u56fe<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u8fd9\u79cd\u8f6c\u6362\u7684\u6210\u672c\u5f88\u4f4e\uff1a\u4e0d\u4f1a\u6267\u884c\u5b9e\u9645\u7684\u590d\u5236\uff0c\u53ea\u662f\u6267\u884c\u8fc7\u7a0b\u4e2d\u6df7\u5408\u7d22\u5f15\u7684\u987a\u5e8f\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u53e6\u4e00\u79cd\u53ef\u4ee5\u6df7\u5408\u7d22\u5f15\u987a\u5e8f\u7684\u8fd0\u7b97\u662f\u6570\u7ec4\u8f6c\u7f6e\u3002\u4e86\u89e3\u5b83\u53ef\u80fd\u4f1a\u8ba9\u4f60\u66f4\u52a0\u719f\u6089\u4e09\u7ef4\u6570\u7ec4\u3002\u6839\u636e\u4f60\u51b3\u5b9a\u4f7f\u7528\u7684 axis \u987a\u5e8f\u7684\u4e0d\u540c\uff0c\u8f6c\u7f6e\u6570\u7ec4\u6240\u6709\u5e73\u9762\u7684\u5b9e\u9645\u547d\u4ee4\u4f1a\u6709\u6240\u4e0d\u540c\uff1a\u5bf9\u4e8e\u4e00\u822c\u6570\u7ec4\uff0c\u5b83\u4f1a\u4ea4\u6362\u7d22\u5f15 1 \u548c 2\uff0c\u5bf9 RGB \u56fe\u50cf\u800c\u8a00\u662f 0 \u548c 1\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics2.baidu.com\/feed\/d833c895d143ad4b3e8fc71fec9981a8a60f06eb.jpeg@f_auto?token=d39c2c89fbe675329b1579ac8ea4245c&s=B1D14F320B42754D48D8B0CB0200D0B0","imgHeight":618,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u8f6c\u7f6e\u4e00\u4e2a\u4e09\u7ef4\u6570\u636e\u7684\u6240\u6709\u5e73\u9762\u7684\u547d\u4ee4<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4e0d\u8fc7\u6709\u8da3\u7684\u662f\uff0ctranspose \u7684\u9ed8\u8ba4 axes \u53c2\u6570\uff08\u4ee5\u53ca\u4ec5\u6709\u7684 a.T \u8fd0\u7b97\u6a21\u5f0f\uff09\u4f1a\u8c03\u8f6c\u7d22\u5f15\u987a\u5e8f\u7684\u65b9\u5411\uff0c\u8fd9\u4e0e\u4e0a\u8ff0\u4e24\u4e2a\u7d22\u5f15\u987a\u5e8f\u60ef\u4f8b\u90fd\u4e0d\u76f8\u7b26\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u6700\u540e\uff0c\u8fd8\u6709\u4e00\u4e2a\u51fd\u6570\u80fd\u907f\u514d\u4f60\u5728\u5904\u7406\u591a\u7ef4\u6570\u7ec4\u65f6\u4f7f\u7528\u592a\u591a\u8bad\u7ec3\uff0c\u8fd8\u80fd\u8ba9\u4f60\u7684\u4ee3\u7801\u66f4\u7b80\u6d01\u2014\u2014einsum\uff08\u7231\u56e0\u65af\u5766\u6c42\u548c\uff09\uff1a<\/span>","data_html":""},{"type":"img","link":"https:\/\/pics0.baidu.com\/feed\/cc11728b4710b912fb0788caac66270493452266.jpeg@f_auto?token=aa46be22520c07e6ca657750de2d6df5&s=B88C59324B23472458D421DA000050B2","imgHeight":227,"imgWidth":1080,"gifsrc":"","gifsize":"","gifbytes":"","caption":"","text-align":"","image-align":"","img_combine":"0"},{"type":"text","content":"<span class=\"bjh-p\">\u5b83\u4f1a\u6cbf\u91cd\u590d\u7684\u7d22\u5f15\u5bf9\u6570\u7ec4\u6c42\u548c\u3002\u5728\u8fd9\u4e2a\u7279\u5b9a\u7684\u4f8b\u5b50\u4e2d\uff0cnp.tensordot(a, b, axis=1) \u8db3\u4ee5\u5e94\u5bf9\u8fd9\u4e24\u79cd\u60c5\u51b5\uff0c\u4f46\u5728\u66f4\u590d\u6742\u7684\u60c5\u51b5\u4e2d\uff0ceinsum \u7684\u901f\u5ea6\u53ef\u80fd\u66f4\u5feb\uff0c\u800c\u4e14\u901a\u5e38\u4e5f\u66f4\u5bb9\u6613\u8bfb\u5199\u2014\u2014\u53ea\u8981\u4f60\u7406\u89e3\u5176\u80cc\u540e\u7684\u903b\u8f91\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u5982\u679c\u4f60\u5e0c\u671b\u6d4b\u8bd5\u4f60\u7684 NumPy \u6280\u80fd\uff0cGitHub \u6709 100 \u9053\u76f8\u5f53\u56f0\u96be\u7684\u7ec3\u4e60\u9898\uff1ahttps:\/\/github.com\/rougier\/numpy-100\u3002<\/span>","data_html":""},{"type":"text","content":"<span class=\"bjh-p\">\u4f60\u6700\u559c\u6b22\u7684 NumPy \u529f\u80fd\u662f\u4ec0\u4e48\uff1f\u8bf7\u4e0e\u6211\u4eec\u5206\u4eab\uff01<\/span>","data_html":""}],"isCenterRight":0,"readsrc":{"link":"http:\/\/baijiahao.baidu.com\/s?id=1688195427853754601","forbidden":0},"notice":{"isBaiJiaHao":1,"id":"9780851078094695079"}}},{"itemType":"author","itemData":{"date":"21-01-07","time":"11:03","thumbnail":{"link":"https:\/\/avatar.bdstatic.com\/it\/u=1561533990,1202445578&fm=3012&app=3012&autime=1749195918&size=b200,200"},"name":"\u673a\u5668\u4e4b\u5fc3Pro","link":"1536769991067070","third_id":"1536769991067070","type":"media","uk":"kwboRjvJvlL4jzJT0LzI4w","author_link":"https:\/\/author.baidu.com\/home?from=bjh_article&app_id=1536769991067070","vType":"2","authorTag":"\u673a\u5668\u4e4b\u5fc3\u5b98\u65b9\u8d26\u53f7","iptopoi":[],"sign":"\u4e13\u4e1a\u7684\u4eba\u5de5\u667a\u80fd\u5a92\u4f53\u548c\u4ea7\u4e1a\u670d\u52a1\u5e73\u53f0","zanNum":"166349","fansNum":"391549"}}],"needAsync":1,"needQuery":1,"newStyle":1,"sid":"60277_61027_62325_62338_62869_62878_62882_62886_62926_62968_62956_63040_63044_63032","nid":"9780851078094695079","sourceFrom":"search","title":"\u56fe\u89e3NumPy\uff1a\u5e38\u7528\u51fd\u6570\u7684\u5185\u5728\u673a\u5236","datetime":"2021-01-07 11:03:40","isBaiJiaHao":1,"profitLog":{"contentId":"9780851078094695079","contentUrl":"http:\/\/baijiahao.baidu.com\/s?id=1688195427853754601","contentPlatformId":3,"contentType":1,"pvid":"9c797a4434865352","time":"2025-06-24 17:22:42","contentAccType":1,"ctk":"7e8226a78b610fe3","ctk_b":"5157797928f88936","logId":"1588911183","dtime":1750756962,"grade":2,"create_time_acc_level":4,"contentAccId":"kwboRjvJvlL4jzJT0LzI4w"},"cmd":"baiduboxapp:\/\/v1\/easybrowse\/hybrid?upgrade=1&type=hybrid&tpl_id=landing_app.html&context=%7B%22nid%22%3A%22news_9780851078094695079%22%7D&style=%7B%22menumode%22%3A%222%22%2C%22toolbaricons%22%3A%7B%22tids%22%3A%5B%224%22%2C%221%22%2C%222%22%2C%223%22%5D%2C%22toolids%22%3A%5B%221%22%2C%222%22%2C%223%22%5D%7D%2C%22actionBarConfig%22%3A%7B%22extCase%22%3A%220%22%7D%7D&newbrowser=1&commentInfo=%7B%22topic_id%22%3A%221069000038282853%22%2C%22opentype%22%3A2%7D&slog=%7B%22from%22%3A%22feed%22%2C%22source%22%3A%22landingsuper%22%2C%22nid%22%3A%22news_9780851078094695079%22%2C%22page%22%3A%22pic_text%22%7D&ch_url=https%3A%2F%2Fmbd.baidu.com%2Fnewspage%2Fdata%2Flandingreact%3FpageType%3D2%26nid%3Dnews_9780851078094695079%26sourceFrom%3Dlandingsuper","displaytime":"1609988620000","favourite":{"is_favourite":"0"},"like":{"is_like":"0","count":"0"},"commentNum":"0","hidedislike":0,"aiLabel":"","timestamp":"1750756962000"},"bsCommon":{"domain":{"bs":"https:\/\/www.baidu.com","portrait":"http:\/\/himg.bdimg.com\/sys\/portraitn\/item\/","appimg":"http:\/\/apps.bdimg.com\/store\/static\/kvt\/","app":"http:\/\/app.baidu.com\/","appinfo":"http:\/\/app.baidu.com\/","apptoken":"d8778fea346d367b4e4ed62cf059908c","appkey":"5r7SmiUici27lVfVBep1K7BA","static":"https:\/\/ss0.bdstatic.com\/5aV1bjqh_Q23odCf\/","passport":"http:\/\/passport.baidu.com\/","sp":"http:\/\/hi.baidu.com\/","passconf":"http:\/\/passport.baidu.com\/ubrwsbas","logout":"https:\/\/passport.baidu.com\/?logout&u=","login":"http:\/\/passport.baidu.com\/?login&tpl=super&u=","top":"http:\/\/top.baidu.com\/buzz.php?p=top10","protocol":"https:"},"ssllist":{"a.hiphotos.baidu.com":"ss0.baidu.com\/94o3dSag_xI4khGko9WTAnF6hhy","b.hiphotos.baidu.com":"ss1.baidu.com\/9vo3dSag_xI4khGko9WTAnF6hhy","c.hiphotos.baidu.com":"ss3.baidu.com\/9fo3dSag_xI4khGko9WTAnF6hhy","d.hiphotos.baidu.com":"ss0.baidu.com\/-Po3dSag_xI4khGko9WTAnF6hhy","e.hiphotos.baidu.com":"ss1.baidu.com\/-4o3dSag_xI4khGko9WTAnF6hhy","f.hiphotos.baidu.com":"ss2.baidu.com\/-vo3dSag_xI4khGko9WTAnF6hhy","g.hiphotos.baidu.com":"ss3.baidu.com\/-fo3dSag_xI4khGko9WTAnF6hhy","h.hiphotos.baidu.com":"ss0.baidu.com\/7Po3dSag_xI4khGko9WTAnF6hhy","1.su.bdimg.com":"ss0.bdstatic.com\/k4oZeXSm1A5BphGlnYG","t10.baidu.com":"ss0.baidu.com\/6ONWsjip0QIZ8tyhnq","t11.baidu.com":"ss1.baidu.com\/6ONXsjip0QIZ8tyhnq","t12.baidu.com":"ss2.baidu.com\/6ONYsjip0QIZ8tyhnq","himg.bdimg.com":"ss1.bdstatic.com\/7Ls0a8Sm1A5BphGlnYG","cdn00.baidu-img.cn":"ss0.bdstatic.com\/9bA1vGba2gU2pMbfm9GUKT-w","cdn01.baidu-img.cn":"ss0.bdstatic.com\/9bA1vGfa2gU2pMbfm9GUKT-w"}},"bsBrowser":{"ie9":0,"isie":0,"ff":0,"ie6":0,"ie":0}};window.firstScreenTime = Date.now();
window['__abbaidu_2036_subidgetf'] = function () {var subid = 'feed_landing_super';return subid;};window['__abbaidu_2036_cb'] = function (responseData) {};
#!/usr/bin/env python3
"""
简化的GPU监控工具
实时监控GPU使用情况，适用于训练过程中的性能监控
"""

import os
import sys
import time
import subprocess
import argparse
from datetime import datetime

class SimpleGPUMonitor:
    def __init__(self):
        self.monitoring = False
        
    def check_nvidia_smi(self):
        """检查nvidia-smi是否可用"""
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def get_gpu_info(self):
        """获取GPU信息"""
        try:
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=index,name,utilization.gpu,memory.used,memory.total,temperature.gpu',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                gpu_info = []
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        parts = line.split(', ')
                        if len(parts) >= 6:
                            gpu_info.append({
                                'index': int(parts[0]),
                                'name': parts[1],
                                'utilization': int(parts[2]),
                                'memory_used': int(parts[3]),
                                'memory_total': int(parts[4]),
                                'temperature': int(parts[5])
                            })
                return gpu_info
            else:
                return None
                
        except Exception as e:
            print(f"❌ 获取GPU信息失败: {e}")
            return None
    
    def display_gpu_status(self, gpu_info):
        """显示GPU状态"""
        if not gpu_info:
            print("❌ 无法获取GPU信息")
            return
        
        # 清屏 (在支持的终端中)
        os.system('clear' if os.name == 'posix' else 'cls')
        
        print("🎯 GPU实时监控")
        print("=" * 80)
        print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        for gpu in gpu_info:
            memory_percent = (gpu['memory_used'] / gpu['memory_total']) * 100
            
            # 创建进度条
            bar_length = 30
            filled_length = int(bar_length * gpu['utilization'] / 100)
            util_bar = '█' * filled_length + '░' * (bar_length - filled_length)
            
            memory_filled = int(bar_length * memory_percent / 100)
            memory_bar = '█' * memory_filled + '░' * (bar_length - memory_filled)
            
            print(f"GPU {gpu['index']}: {gpu['name']}")
            print(f"  利用率: {gpu['utilization']:3d}% [{util_bar}]")
            print(f"  显存:   {memory_percent:5.1f}% [{memory_bar}] {gpu['memory_used']}/{gpu['memory_total']}MB")
            print(f"  温度:   {gpu['temperature']}°C")
            print()
        
        print("按 Ctrl+C 停止监控")
        print("=" * 80)
    
    def monitor_continuous(self, interval=2):
        """连续监控GPU状态"""
        if not self.check_nvidia_smi():
            print("❌ nvidia-smi 不可用，请检查NVIDIA驱动安装")
            return
        
        print("🚀 开始GPU监控...")
        print(f"监控间隔: {interval}秒")
        print("按 Ctrl+C 停止监控")
        
        self.monitoring = True
        
        try:
            while self.monitoring:
                gpu_info = self.get_gpu_info()
                self.display_gpu_status(gpu_info)
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n⏹️  监控已停止")
            self.monitoring = False
    
    def show_gpu_summary(self):
        """显示GPU摘要信息"""
        if not self.check_nvidia_smi():
            print("❌ nvidia-smi 不可用")
            return
        
        gpu_info = self.get_gpu_info()
        if not gpu_info:
            print("❌ 无法获取GPU信息")
            return
        
        print("🎯 GPU摘要信息")
        print("=" * 60)
        
        total_memory = 0
        total_used = 0
        
        for gpu in gpu_info:
            memory_percent = (gpu['memory_used'] / gpu['memory_total']) * 100
            total_memory += gpu['memory_total']
            total_used += gpu['memory_used']
            
            print(f"GPU {gpu['index']}: {gpu['name']}")
            print(f"  利用率: {gpu['utilization']}%")
            print(f"  显存: {gpu['memory_used']}/{gpu['memory_total']}MB ({memory_percent:.1f}%)")
            print(f"  温度: {gpu['temperature']}°C")
            print()
        
        if len(gpu_info) > 1:
            total_percent = (total_used / total_memory) * 100
            print(f"总计: {total_used}/{total_memory}MB ({total_percent:.1f}%)")
        
        print("=" * 60)
    
    def check_training_readiness(self):
        """检查训练就绪状态"""
        print("🔍 检查训练就绪状态...")
        
        if not self.check_nvidia_smi():
            print("❌ nvidia-smi 不可用")
            return False
        
        gpu_info = self.get_gpu_info()
        if not gpu_info:
            print("❌ 无法获取GPU信息")
            return False
        
        ready = True
        
        for gpu in gpu_info:
            memory_percent = (gpu['memory_used'] / gpu['memory_total']) * 100
            
            print(f"GPU {gpu['index']}: {gpu['name']}")
            
            # 检查显存使用率
            if memory_percent > 90:
                print(f"  ❌ 显存使用率过高: {memory_percent:.1f}%")
                ready = False
            elif memory_percent > 70:
                print(f"  ⚠️  显存使用率较高: {memory_percent:.1f}%")
            else:
                print(f"  ✅ 显存使用率正常: {memory_percent:.1f}%")
            
            # 检查温度
            if gpu['temperature'] > 85:
                print(f"  ❌ 温度过高: {gpu['temperature']}°C")
                ready = False
            elif gpu['temperature'] > 75:
                print(f"  ⚠️  温度较高: {gpu['temperature']}°C")
            else:
                print(f"  ✅ 温度正常: {gpu['temperature']}°C")
            
            # 检查显存容量
            if gpu['memory_total'] < 16000:
                print(f"  ⚠️  显存容量较小: {gpu['memory_total']}MB")
            else:
                print(f"  ✅ 显存容量充足: {gpu['memory_total']}MB")
            
            print()
        
        if ready:
            print("🎉 系统已准备好进行训练！")
        else:
            print("⚠️  建议解决上述问题后再开始训练")
        
        return ready

def main():
    parser = argparse.ArgumentParser(description="简化GPU监控工具")
    parser.add_argument("--action", choices=["monitor", "summary", "check"], 
                       default="monitor", help="操作类型")
    parser.add_argument("--interval", type=int, default=2, help="监控间隔(秒)")
    
    args = parser.parse_args()
    
    monitor = SimpleGPUMonitor()
    
    if args.action == "monitor":
        monitor.monitor_continuous(args.interval)
    elif args.action == "summary":
        monitor.show_gpu_summary()
    elif args.action == "check":
        monitor.check_training_readiness()

if __name__ == "__main__":
    main()

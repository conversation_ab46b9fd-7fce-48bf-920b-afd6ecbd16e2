#!/bin/bash
# Wan2.1-I2V-14B-480P 自定义数据集训练快速启动脚本
# 基于您成功的训练经验，使用自定义数据集

set -e

echo "🎯 Wan2.1-I2V-14B-480P 自定义数据集训练"
echo "基于您成功的多卡训练经验"
echo "=" * 60

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查参数
CREATE_DATASET=true
RUN_TRAINING=true
EPOCHS=5
DATASET_REPEAT=20

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-dataset)
            CREATE_DATASET=false
            shift
            ;;
        --skip-training)
            RUN_TRAINING=false
            shift
            ;;
        --epochs)
            EPOCHS="$2"
            shift 2
            ;;
        --dataset-repeat)
            DATASET_REPEAT="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-dataset     跳过数据集创建"
            echo "  --skip-training    跳过训练"
            echo "  --epochs N         训练轮数 (默认: 5)"
            echo "  --dataset-repeat N 数据集重复次数 (默认: 20)"
            echo "  -h, --help         显示帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 项目配置
PROJECT_ROOT="/root/sj-tmp/DiffSynth-Studio"
CONDA_ENV="wan_video_env"
CUSTOM_DATASET_DIR="data/custom_video_dataset"
CUSTOM_OUTPUT_DIR="./models/train/Wan2.1-I2V-14B-480P_custom_lora"

log_step "第1步: 环境准备"

# 检查项目目录
if [ ! -d "$PROJECT_ROOT" ]; then
    log_error "项目目录不存在: $PROJECT_ROOT"
    exit 1
fi

cd "$PROJECT_ROOT"
log_info "工作目录: $(pwd)"

# 激活conda环境
log_info "激活conda环境: $CONDA_ENV"
source /root/miniconda3/etc/profile.d/conda.sh
conda activate "$CONDA_ENV"

# 验证环境
log_info "验证Python环境..."
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}')"

log_step "第2步: 自定义数据集准备"

if [ "$CREATE_DATASET" = true ]; then
    log_info "创建自定义数据集..."
    
    # 运行数据集创建脚本
    python create_simple_custom_dataset.py
    
    if [ $? -eq 0 ]; then
        log_info "✅ 自定义数据集创建成功"
    else
        log_error "❌ 自定义数据集创建失败"
        exit 1
    fi
else
    log_info "⏭️  跳过数据集创建"
fi

# 验证数据集
if [ ! -d "$CUSTOM_DATASET_DIR" ]; then
    log_error "自定义数据集目录不存在: $CUSTOM_DATASET_DIR"
    exit 1
fi

if [ ! -f "$CUSTOM_DATASET_DIR/metadata.csv" ]; then
    log_error "元数据文件不存在: $CUSTOM_DATASET_DIR/metadata.csv"
    exit 1
fi

# 显示数据集信息
log_info "自定义数据集信息:"
SAMPLE_COUNT=$(tail -n +2 "$CUSTOM_DATASET_DIR/metadata.csv" | wc -l)
log_info "   样本数量: $SAMPLE_COUNT"
log_info "   数据集目录: $CUSTOM_DATASET_DIR"
log_info "   元数据文件: $CUSTOM_DATASET_DIR/metadata.csv"

# 显示前几个样本
log_info "   前3个样本:"
head -4 "$CUSTOM_DATASET_DIR/metadata.csv" | tail -3 | while read line; do
    log_info "     $line"
done

log_step "第3步: GPU状态检查"

# 检查GPU状态
log_info "GPU状态:"
nvidia-smi --query-gpu=index,name,memory.total,memory.used,utilization.gpu --format=csv,noheader,nounits

log_step "第4步: 训练环境配置"

# 设置环境变量
export NCCL_TIMEOUT=1800
export TOKENIZERS_PARALLELISM=false

log_info "设置环境变量:"
log_info "  NCCL_TIMEOUT=1800"
log_info "  TOKENIZERS_PARALLELISM=false"

# 检查accelerate配置
if [ ! -f "accelerate_config.yaml" ]; then
    log_warn "accelerate配置文件不存在，创建默认配置..."
    cat > accelerate_config.yaml << EOF
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
use_cpu: false
EOF
    log_info "✅ accelerate配置文件已创建"
else
    log_info "✅ accelerate配置文件存在"
fi

# 训练阶段
if [ "$RUN_TRAINING" = true ]; then
    log_step "第5步: 自定义数据集训练"
    
    log_info "开始自定义数据集训练..."
    log_info "训练配置:"
    log_info "  数据集: $CUSTOM_DATASET_DIR"
    log_info "  样本数: $SAMPLE_COUNT"
    log_info "  重复次数: $DATASET_REPEAT"
    log_info "  训练轮数: $EPOCHS"
    log_info "  输出目录: $CUSTOM_OUTPUT_DIR"
    
    log_info "这可能需要较长时间，请耐心等待..."
    
    # 记录开始时间
    START_TIME=$(date)
    log_info "开始时间: $START_TIME"
    
    # 自定义训练命令（基于您成功的配置）
    accelerate launch --config_file accelerate_config.yaml \
      examples/wanvideo/model_training/train.py \
      --dataset_base_path "$CUSTOM_DATASET_DIR" \
      --dataset_metadata_path "$CUSTOM_DATASET_DIR/metadata.csv" \
      --height 480 --width 832 \
      --dataset_repeat "$DATASET_REPEAT" \
      --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
      --learning_rate 1e-4 --num_epochs "$EPOCHS" --gradient_accumulation_steps 1 \
      --remove_prefix_in_ckpt "pipe.dit." \
      --output_path "$CUSTOM_OUTPUT_DIR" \
      --lora_base_model "dit" \
      --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
      --lora_rank 8 --extra_inputs "input_image" --mixed_precision "bf16"
    
    if [ $? -eq 0 ]; then
        END_TIME=$(date)
        log_info "✅ 自定义训练完成"
        log_info "结束时间: $END_TIME"
    else
        log_error "❌ 自定义训练失败"
        exit 1
    fi
else
    log_info "⏭️  跳过训练阶段"
fi

# 检查训练结果
log_step "第6步: 训练结果验证"

if [ -d "$CUSTOM_OUTPUT_DIR" ]; then
    log_info "✅ 自定义训练输出目录存在: $CUSTOM_OUTPUT_DIR"
    
    # 检查epoch文件
    log_info "检查点文件:"
    for i in {0..9}; do
        EPOCH_FILE="$CUSTOM_OUTPUT_DIR/epoch-$i.safetensors"
        if [ -f "$EPOCH_FILE" ]; then
            FILE_SIZE=$(du -h "$EPOCH_FILE" | cut -f1)
            log_info "  ✅ epoch-$i.safetensors ($FILE_SIZE)"
        fi
    done
    
    # 检查训练配置
    if [ -f "$CUSTOM_OUTPUT_DIR/training_args.json" ]; then
        log_info "  ✅ training_args.json"
    fi
else
    log_warn "⚠️  自定义训练输出目录不存在: $CUSTOM_OUTPUT_DIR"
fi

# 创建自定义推理脚本
log_step "第7步: 创建自定义推理脚本"

log_info "创建自定义推理脚本..."
python -c "
import train_with_custom_dataset
train_with_custom_dataset.create_custom_inference_script()
"

if [ -f "custom_lora_inference.py" ]; then
    log_info "✅ 自定义推理脚本已创建: custom_lora_inference.py"
else
    log_warn "⚠️  自定义推理脚本创建失败"
fi

# 最终GPU状态
log_step "第8步: 最终状态"

log_info "最终GPU状态:"
nvidia-smi --query-gpu=index,name,memory.used,utilization.gpu --format=csv,noheader,nounits

# 总结
echo ""
echo "=" * 60
echo "🎉 自定义数据集训练流程完成!"
echo "=" * 60

log_info "执行总结:"
if [ "$CREATE_DATASET" = true ]; then
    log_info "✅ 自定义数据集创建完成 ($SAMPLE_COUNT 个样本)"
fi

if [ "$RUN_TRAINING" = true ]; then
    log_info "✅ 自定义数据集训练完成 ($EPOCHS epochs)"
fi

log_info "✅ 训练结果验证完成"
log_info "✅ 自定义推理脚本创建完成"

echo ""
log_info "🚀 您的自定义数据集微调流程已完全打通!"

echo ""
echo "生成的文件:"
echo "  📁 $CUSTOM_DATASET_DIR/          # 自定义数据集"
echo "  📝 $CUSTOM_DATASET_DIR/metadata.csv  # 训练用CSV"
echo "  🎯 $CUSTOM_OUTPUT_DIR/           # 训练输出"
echo "  🔧 custom_lora_inference.py     # 自定义推理脚本"

echo ""
echo "下一步建议:"
echo "  1. 运行自定义推理测试:"
echo "     python custom_lora_inference.py"
echo ""
echo "  2. 对比不同模型效果:"
echo "     - 基础模型 vs 原始LoRA vs 自定义LoRA"
echo ""
echo "  3. 进一步优化:"
echo "     - 添加更多自定义数据"
echo "     - 调整训练参数"
echo "     - 尝试不同场景"
echo ""
echo "  4. 查看详细文档:"
echo "     cat 自定义数据集微调完整指南.md"

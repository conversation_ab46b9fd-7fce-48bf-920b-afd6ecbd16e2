# Wan-AI/Wan2.1-I2V-14B-480P 多卡微调详细文档（基于最新版DiffSynth-Studio）

## 概述

本文档基于最新版DiffSynth-Studio代码，详细介绍如何对Wan-AI/Wan2.1-I2V-14B-480P模型进行多卡微调。文档严格按照官方最新脚本格式编写，专门针对图像到视频生成任务。

## 环境准备

### 1. 创建Conda虚拟环境

```bash
# 创建新的conda环境
conda create -n wan_i2v_latest python=3.10 -y
conda activate wan_i2v_latest

# 更新pip
pip install --upgrade pip
```

### 2. 安装依赖

```bash
# 克隆最新版DiffSynth-Studio仓库
git clone https://github.com/modelscope/DiffSynth-Studio.git
cd DiffSynth-Studio

# 安装基础依赖
pip install -e .

# 安装训练相关依赖
pip install accelerate
pip install deepspeed
pip install transformers
pip install diffusers
pip install xformers
pip install modelscope
pip install opencv-python
pip install pillow
pip install numpy
pip install peft
pip install lightning
pip install pandas
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装图像处理相关依赖
pip install imageio
pip install imageio-ffmpeg
pip install decord
pip install av

# 安装其他必要依赖
pip install wandb  # 可选，用于训练监控
pip install tensorboard  # 可选，用于训练可视化
```

### 3. 验证安装

```bash
# 测试导入
python -c "from diffsynth.pipelines.wan_video_new import WanVideoPipeline; print('安装成功')"
```

## 数据集准备

### 1. 数据集格式

根据最新版DiffSynth-Studio的要求，创建I2V训练数据集：

```
data/example_video_dataset/
├── metadata.csv
├── images/
│   ├── image_001.jpg
│   ├── image_002.jpg
│   └── ...
└── videos/
    ├── video_001.mp4
    ├── video_002.mp4
    └── ...
```

### 2. 元数据文件格式

创建 `metadata.csv` 文件，I2V任务格式如下：

```csv
video_path,text,input_image
videos/video_001.mp4,"一艘小船正勇敢地乘风破浪前行",images/image_001.jpg
videos/video_002.mp4,"一只可爱的小猫在花园里玩耍",images/image_002.jpg
videos/video_003.mp4,"夕阳西下，海浪拍打着岩石",images/image_003.jpg
```

**重要说明**：
- `video_path`: 相对于数据集根目录的视频文件路径
- `text`: 对应的文本描述（可选，用于额外指导）
- `input_image`: 输入图像路径，作为视频生成的条件
- 图像和视频应该在内容上相关联（如视频的第一帧或相似场景）

### 3. 数据集准备脚本

创建 `prepare_i2v_dataset.py`：

```python
import os
import csv
import cv2
from pathlib import Path
from PIL import Image

def create_i2v_metadata_csv(dataset_path, image_folder="images", video_folder="videos"):
    """创建I2V任务的metadata.csv文件"""
    dataset_path = Path(dataset_path)
    image_folder_path = dataset_path / image_folder
    video_folder_path = dataset_path / video_folder
    
    if not image_folder_path.exists():
        print(f"错误：图像文件夹 {image_folder_path} 不存在")
        return
    
    if not video_folder_path.exists():
        print(f"错误：视频文件夹 {video_folder_path} 不存在")
        return
    
    metadata = []
    
    # 遍历图像文件，寻找对应的视频文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    
    for image_file in image_folder_path.iterdir():
        if image_file.suffix.lower() in image_extensions:
            # 寻找对应的视频文件（基于文件名匹配）
            base_name = image_file.stem
            
            # 尝试不同的命名模式
            possible_video_names = [
                f"{base_name}.mp4",
                f"{base_name.replace('image_', 'video_')}.mp4",
                f"video_{base_name.split('_')[-1]}.mp4" if '_' in base_name else f"video_{base_name}.mp4"
            ]
            
            video_file = None
            for video_name in possible_video_names:
                potential_video = video_folder_path / video_name
                if potential_video.exists():
                    video_file = potential_video
                    break
            
            if video_file:
                try:
                    # 验证图像文件
                    image = Image.open(image_file)
                    image_width, image_height = image.size
                    
                    # 验证视频文件
                    cap = cv2.VideoCapture(str(video_file))
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    video_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    video_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    cap.release()
                    
                    if frame_count > 0 and fps > 0:
                        # 生成相对路径
                        image_relative_path = f"{image_folder}/{image_file.name}"
                        video_relative_path = f"{video_folder}/{video_file.name}"
                        
                        # 生成描述（实际使用时需要根据具体情况修改）
                        description = f"基于图像生成的视频动画：{image_file.stem}"
                        
                        metadata.append({
                            "video_path": video_relative_path,
                            "text": description,
                            "input_image": image_relative_path
                        })
                        
                        print(f"添加I2V对: {image_file.name} -> {video_file.name}")
                        print(f"  图像尺寸: {image_width}x{image_height}")
                        print(f"  视频尺寸: {video_width}x{video_height}, {frame_count}帧, {fps:.2f}fps")
                    else:
                        print(f"跳过无效视频: {video_file.name}")
                        
                except Exception as e:
                    print(f"处理文件对 {image_file.name}-{video_file.name} 时出错: {str(e)}")
            else:
                print(f"未找到图像 {image_file.name} 对应的视频文件")
    
    # 写入CSV文件
    metadata_file = dataset_path / "metadata.csv"
    with open(metadata_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['video_path', 'text', 'input_image']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for item in metadata:
            writer.writerow(item)
    
    print(f"创建I2V metadata.csv完成，共 {len(metadata)} 个图像-视频对")
    print(f"文件保存至: {metadata_file}")

def validate_i2v_dataset(dataset_path):
    """验证I2V数据集完整性"""
    dataset_path = Path(dataset_path)
    metadata_file = dataset_path / "metadata.csv"
    
    if not metadata_file.exists():
        print("错误：metadata.csv文件不存在")
        return False
    
    # 读取metadata.csv
    with open(metadata_file, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        missing_files = []
        valid_count = 0
        
        for row in reader:
            video_path = dataset_path / row['video_path']
            image_path = dataset_path / row['input_image']
            
            if not video_path.exists():
                missing_files.append(f"视频: {row['video_path']}")
            elif not image_path.exists():
                missing_files.append(f"图像: {row['input_image']}")
            else:
                valid_count += 1
        
        if missing_files:
            print(f"警告：以下文件不存在：")
            for file in missing_files[:10]:  # 只显示前10个
                print(f"  {file}")
            return False
        else:
            print(f"I2V数据集验证通过，共 {valid_count} 个有效的图像-视频对")
            return True

if __name__ == "__main__":
    dataset_path = "./data/example_video_dataset"
    
    # 创建数据集目录
    os.makedirs(dataset_path, exist_ok=True)
    os.makedirs(f"{dataset_path}/images", exist_ok=True)
    os.makedirs(f"{dataset_path}/videos", exist_ok=True)
    
    print("请将图像文件放入 data/example_video_dataset/images/ 文件夹中")
    print("请将对应的视频文件放入 data/example_video_dataset/videos/ 文件夹中")
    print("然后运行此脚本生成metadata.csv文件")
    
    # 如果文件夹中有文件，则生成metadata.csv
    image_folder = Path(dataset_path) / "images"
    video_folder = Path(dataset_path) / "videos"
    
    if any(image_folder.iterdir()) and any(video_folder.iterdir()):
        create_i2v_metadata_csv(dataset_path)
        validate_i2v_dataset(dataset_path)
    else:
        print("images或videos文件夹为空，请先添加文件")
```

## 多卡训练配置

### 1. Accelerate配置文件

创建 `accelerate_config.yaml`（用于LoRA训练）：

```yaml
compute_environment: LOCAL_MACHINE
distributed_type: MULTI_GPU
downcast_bf16: 'no'
gpu_ids: all
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 4
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

### 2. DeepSpeed配置文件（用于全量训练）

创建 `accelerate_config_deepspeed.yaml`：

```yaml
compute_environment: LOCAL_MACHINE
debug: false
deepspeed_config:
  gradient_accumulation_steps: 1
  offload_optimizer_device: cpu
  offload_param_device: cpu
  zero3_init_flag: false
  zero_stage: 2
distributed_type: DEEPSPEED
downcast_bf16: 'no'
enable_cpu_affinity: false
machine_rank: 0
main_training_function: main
mixed_precision: bf16
num_machines: 1
num_processes: 4
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
```

### 3. 初始化Accelerate配置

```bash
# 配置accelerate（LoRA训练）
accelerate config --config_file accelerate_config.yaml

# 配置accelerate（全量训练）
accelerate config --config_file accelerate_config_deepspeed.yaml
```

## LoRA微调训练

### 1. LoRA训练脚本

创建 `train_i2v_lora.sh`：

```bash
#!/bin/bash

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 数据集和模型路径
DATASET_BASE_PATH="data/example_video_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"
OUTPUT_PATH="./models/train/Wan2.1-I2V-14B-480P_lora"

echo "开始Wan2.1-I2V-14B-480P LoRA训练..."

accelerate launch --config_file accelerate_config.yaml examples/wanvideo/model_training/train.py \
  --dataset_base_path ${DATASET_BASE_PATH} \
  --dataset_metadata_path ${DATASET_METADATA_PATH} \
  --height 480 \
  --width 832 \
  --dataset_repeat 100 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-4 \
  --num_epochs 5 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path ${OUTPUT_PATH} \
  --lora_base_model "dit" \
  --lora_target_modules "q,k,v,o,ffn.0,ffn.2" \
  --lora_rank 32 \
  --extra_inputs "input_image"

echo "I2V LoRA训练完成！模型保存在: ${OUTPUT_PATH}"
```

### 2. I2V LoRA训练参数说明

- `--extra_inputs "input_image"`: I2V特有参数，指定输入图像
- 包含CLIP图像编码器：`models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth`
- 其他参数与T2V类似，但专门针对图像条件生成

## 全量微调训练

### 1. 全量训练脚本

创建 `train_i2v_full.sh`：

```bash
#!/bin/bash

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 数据集和模型路径
DATASET_BASE_PATH="data/example_video_dataset"
DATASET_METADATA_PATH="data/example_video_dataset/metadata.csv"
OUTPUT_PATH="./models/train/Wan2.1-I2V-14B-480P_full"

echo "开始Wan2.1-I2V-14B-480P 全量训练..."

accelerate launch --config_file accelerate_config_deepspeed.yaml examples/wanvideo/model_training/train.py \
  --dataset_base_path ${DATASET_BASE_PATH} \
  --dataset_metadata_path ${DATASET_METADATA_PATH} \
  --height 480 \
  --width 832 \
  --dataset_repeat 100 \
  --model_id_with_origin_paths "Wan-AI/Wan2.1-I2V-14B-480P:diffusion_pytorch_model*.safetensors,Wan-AI/Wan2.1-I2V-14B-480P:models_t5_umt5-xxl-enc-bf16.pth,Wan-AI/Wan2.1-I2V-14B-480P:Wan2.1_VAE.pth,Wan-AI/Wan2.1-I2V-14B-480P:models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth" \
  --learning_rate 1e-5 \
  --num_epochs 2 \
  --remove_prefix_in_ckpt "pipe.dit." \
  --output_path ${OUTPUT_PATH} \
  --trainable_models "dit" \
  --extra_inputs "input_image"

echo "I2V全量训练完成！模型保存在: ${OUTPUT_PATH}"
```

## 推理代码

### 1. 基础I2V模型推理脚本

创建 `inference_base_i2v.py`：

```python
import torch
from PIL import Image
from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
from modelscope import dataset_snapshot_download
import argparse

def inference_base_i2v_model(image_path, prompt="", output_path="output_base_i2v.mp4"):
    """使用基础Wan2.1-I2V-14B-480P模型进行推理"""

    # 创建推理管道
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cuda",
        model_configs=[
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
        ],
    )
    pipe.enable_vram_management()

    # 加载输入图像
    if not image_path:
        # 下载示例图像
        dataset_snapshot_download(
            dataset_id="DiffSynth-Studio/examples_in_diffsynth",
            local_dir="./",
            allow_file_pattern=f"data/examples/wan/input_image.jpg"
        )
        image_path = "data/examples/wan/input_image.jpg"

    image = Image.open(image_path)
    print(f"输入图像: {image_path}")
    print(f"图像尺寸: {image.size}")

    # 图像到视频生成
    if not prompt:
        prompt = "一艘小船正勇敢地乘风破浪前行。蔚蓝的大海波涛汹涌，白色的浪花拍打着船身"

    negative_prompt = "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量"

    print(f"生成提示词: {prompt}")

    video = pipe(
        prompt=prompt,
        negative_prompt=negative_prompt,
        input_image=image,
        seed=0,
        tiled=True
    )

    # 保存视频
    save_video(video, output_path, fps=15, quality=5)
    print(f"基础I2V模型推理完成，视频已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--image_path", type=str, default="", help="输入图像路径")
    parser.add_argument("--prompt", type=str, default="", help="生成提示词")
    parser.add_argument("--output_path", type=str, default="output_base_i2v.mp4", help="输出视频路径")
    args = parser.parse_args()

    inference_base_i2v_model(args.image_path, args.prompt, args.output_path)

if __name__ == "__main__":
    main()
```

### 2. LoRA微调I2V模型推理脚本

创建 `inference_lora_i2v.py`：

```python
import torch
from PIL import Image
from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
import argparse

def inference_lora_i2v_model(lora_path, image_path, prompt="", output_path="output_lora_i2v.mp4"):
    """使用LoRA微调后的I2V模型进行推理"""

    # 创建推理管道
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cuda",
        model_configs=[
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
        ],
    )

    # 加载LoRA权重
    try:
        pipe.load_lora(lora_path)
        print(f"成功加载I2V LoRA权重: {lora_path}")
    except Exception as e:
        print(f"加载LoRA权重失败: {e}")
        return

    pipe.enable_vram_management()

    # 加载输入图像
    image = Image.open(image_path)
    print(f"输入图像: {image_path}")
    print(f"图像尺寸: {image.size}")

    # 图像到视频生成
    if not prompt:
        prompt = "基于输入图像生成动态视频"

    negative_prompt = "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量"

    print(f"生成提示词: {prompt}")

    video = pipe(
        prompt=prompt,
        negative_prompt=negative_prompt,
        input_image=image,
        seed=0,
        tiled=True
    )

    # 保存视频
    save_video(video, output_path, fps=15, quality=5)
    print(f"LoRA I2V模型推理完成，视频已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--lora_path", type=str, required=True, help="LoRA权重文件路径")
    parser.add_argument("--image_path", type=str, required=True, help="输入图像路径")
    parser.add_argument("--prompt", type=str, default="", help="生成提示词")
    parser.add_argument("--output_path", type=str, default="output_lora_i2v.mp4", help="输出视频路径")
    args = parser.parse_args()

    inference_lora_i2v_model(args.lora_path, args.image_path, args.prompt, args.output_path)

if __name__ == "__main__":
    main()
```

### 3. 全量微调I2V模型推理脚本

创建 `inference_full_i2v.py`：

```python
import torch
from PIL import Image
from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
import argparse

def inference_full_i2v_model(model_path, image_path, prompt="", output_path="output_full_i2v.mp4"):
    """使用全量微调后的I2V模型进行推理"""

    # 创建推理管道，使用微调后的模型
    pipe = WanVideoPipeline.from_pretrained(
        torch_dtype=torch.bfloat16,
        device="cuda",
        model_configs=[
            ModelConfig(path=f"{model_path}/diffusion_pytorch_model.safetensors", offload_device="cpu"),  # 微调后的DiT
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
            ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
        ],
    )
    pipe.enable_vram_management()

    # 加载输入图像
    image = Image.open(image_path)
    print(f"输入图像: {image_path}")
    print(f"图像尺寸: {image.size}")

    # 图像到视频生成
    if not prompt:
        prompt = "基于输入图像生成动态视频"

    negative_prompt = "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量"

    print(f"生成提示词: {prompt}")

    video = pipe(
        prompt=prompt,
        negative_prompt=negative_prompt,
        input_image=image,
        seed=0,
        tiled=True
    )

    # 保存视频
    save_video(video, output_path, fps=15, quality=5)
    print(f"全量微调I2V模型推理完成，视频已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_path", type=str, required=True, help="微调后模型路径")
    parser.add_argument("--image_path", type=str, required=True, help="输入图像路径")
    parser.add_argument("--prompt", type=str, default="", help="生成提示词")
    parser.add_argument("--output_path", type=str, default="output_full_i2v.mp4", help="输出视频路径")
    args = parser.parse_args()

    inference_full_i2v_model(args.model_path, args.image_path, args.prompt, args.output_path)

if __name__ == "__main__":
    main()
```

### 4. I2V推理启动脚本

创建 `inference_i2v.sh`：

```bash
#!/bin/bash

echo "=== Wan2.1-I2V-14B-480P 推理测试 ==="

# 测试图像和提示词
IMAGE_PATH="./test_image.jpg"
PROMPT="一个人在美丽的风景中缓缓走动，展现自然的动态美感"

# 检查测试图像是否存在
if [ ! -f "$IMAGE_PATH" ]; then
    echo "警告：测试图像不存在: $IMAGE_PATH"
    echo "将使用默认示例图像"
    IMAGE_PATH=""
fi

echo "1. 基础I2V模型推理..."
python inference_base_i2v.py \
    --image_path "$IMAGE_PATH" \
    --prompt "$PROMPT" \
    --output_path "output_base_i2v.mp4"

echo "2. LoRA I2V模型推理..."
if [ -f "./models/train/Wan2.1-I2V-14B-480P_lora/pytorch_lora_weights.safetensors" ]; then
    python inference_lora_i2v.py \
        --lora_path "./models/train/Wan2.1-I2V-14B-480P_lora/pytorch_lora_weights.safetensors" \
        --image_path "$IMAGE_PATH" \
        --prompt "$PROMPT" \
        --output_path "output_lora_i2v.mp4"
else
    echo "LoRA权重文件不存在，跳过LoRA推理"
fi

echo "3. 全量微调I2V模型推理..."
if [ -d "./models/train/Wan2.1-I2V-14B-480P_full" ]; then
    python inference_full_i2v.py \
        --model_path "./models/train/Wan2.1-I2V-14B-480P_full" \
        --image_path "$IMAGE_PATH" \
        --prompt "$PROMPT" \
        --output_path "output_full_i2v.mp4"
else
    echo "全量微调模型不存在，跳过全量推理"
fi

echo "所有I2V推理完成！"
```

## 模型合并代码

### 1. I2V LoRA权重合并脚本

创建 `merge_lora_i2v.py`：

```python
import torch
import argparse
import os
from pathlib import Path
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig
import shutil

def merge_i2v_lora_weights(base_model_id, lora_path, output_path, alpha=1.0):
    """将I2V LoRA权重合并到基础模型"""
    print(f"开始合并I2V LoRA权重...")
    print(f"基础模型: {base_model_id}")
    print(f"LoRA路径: {lora_path}")
    print(f"输出路径: {output_path}")
    print(f"合并系数: {alpha}")

    try:
        # 加载基础I2V模型
        pipe = WanVideoPipeline.from_pretrained(
            torch_dtype=torch.bfloat16,
            device="cpu",  # 使用CPU以节省显存
            model_configs=[
                ModelConfig(model_id=base_model_id, origin_file_pattern="diffusion_pytorch_model*.safetensors"),
                ModelConfig(model_id=base_model_id, origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth"),
                ModelConfig(model_id=base_model_id, origin_file_pattern="Wan2.1_VAE.pth"),
                ModelConfig(model_id=base_model_id, origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth"),
            ],
        )

        # 加载LoRA权重
        if Path(lora_path).exists():
            pipe.load_lora(lora_path, alpha=alpha)
            print("I2V LoRA权重加载成功")
        else:
            print(f"错误：LoRA文件不存在: {lora_path}")
            return False

        # 创建输出目录
        os.makedirs(output_path, exist_ok=True)

        # 合并并保存模型
        print("正在合并I2V权重...")

        # 保存合并后的模型
        merged_model_path = Path(output_path) / "diffusion_pytorch_model.safetensors"
        # pipe.save_pretrained(output_path)  # 根据实际API调整

        print(f"I2V模型合并完成，保存到: {output_path}")
        return True

    except Exception as e:
        print(f"I2V合并过程中出错: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--base_model_id", type=str, default="Wan-AI/Wan2.1-I2V-14B-480P", help="基础I2V模型ID")
    parser.add_argument("--lora_path", type=str, required=True, help="LoRA权重文件路径")
    parser.add_argument("--output_path", type=str, required=True, help="合并后模型输出路径")
    parser.add_argument("--alpha", type=float, default=1.0, help="LoRA合并系数")
    args = parser.parse_args()

    success = merge_i2v_lora_weights(
        base_model_id=args.base_model_id,
        lora_path=args.lora_path,
        output_path=args.output_path,
        alpha=args.alpha
    )

    if success:
        print("✅ I2V模型合并成功！")
    else:
        print("❌ I2V模型合并失败！")

if __name__ == "__main__":
    main()
```

### 2. I2V合并启动脚本

创建 `merge_i2v.sh`：

```bash
#!/bin/bash

echo "=== Wan2.1-I2V-14B-480P LoRA权重合并 ==="

# 设置路径
LORA_PATH="./models/train/Wan2.1-I2V-14B-480P_lora/pytorch_lora_weights.safetensors"
OUTPUT_PATH="./models/merged/Wan2.1-I2V-14B-480P-merged"

# 检查LoRA文件是否存在
if [ ! -f "$LORA_PATH" ]; then
    echo "错误：I2V LoRA权重文件不存在: $LORA_PATH"
    echo "请先完成I2V LoRA训练"
    exit 1
fi

# 执行合并
python merge_lora_i2v.py \
    --base_model_id "Wan-AI/Wan2.1-I2V-14B-480P" \
    --lora_path "$LORA_PATH" \
    --output_path "$OUTPUT_PATH" \
    --alpha 1.0

echo "I2V合并完成！合并后的模型保存在: $OUTPUT_PATH"
```

## 训练监控和调试

### 1. 使用TensorBoard监控I2V训练

```bash
# 启动TensorBoard（如果训练脚本支持）
tensorboard --logdir ./models/train --port 6006

# 在浏览器中访问 http://localhost:6006
```

### 2. I2V训练进度检查脚本

创建 `check_i2v_training_progress.py`：

```python
import os
import json
from pathlib import Path
from PIL import Image

def check_i2v_training_progress(output_path):
    """检查I2V训练进度"""
    output_path = Path(output_path)

    if not output_path.exists():
        print(f"I2V训练输出目录不存在: {output_path}")
        return

    print(f"检查I2V训练目录: {output_path}")

    # 检查检查点文件
    checkpoints = list(output_path.glob("checkpoint-*"))
    if checkpoints:
        print(f"找到 {len(checkpoints)} 个检查点:")
        for ckpt in sorted(checkpoints):
            print(f"  - {ckpt.name}")
    else:
        print("未找到检查点文件")

    # 检查LoRA权重文件
    lora_files = list(output_path.glob("*.safetensors"))
    if lora_files:
        print(f"找到LoRA权重文件:")
        for lora in lora_files:
            size_mb = lora.stat().st_size / (1024 * 1024)
            print(f"  - {lora.name} ({size_mb:.2f} MB)")

    # 检查训练日志
    log_files = list(output_path.glob("*.log"))
    if log_files:
        print(f"找到日志文件:")
        for log in log_files:
            print(f"  - {log.name}")

def validate_i2v_sample_generation(image_path, output_dir="./test_i2v_samples"):
    """验证I2V样本生成"""
    if not Path(image_path).exists():
        print(f"测试图像不存在: {image_path}")
        return

    os.makedirs(output_dir, exist_ok=True)

    # 加载测试图像
    image = Image.open(image_path)
    print(f"测试图像尺寸: {image.size}")

    # 这里可以添加简单的推理测试
    print("I2V样本生成验证完成")

if __name__ == "__main__":
    # 检查LoRA训练进度
    print("=== I2V LoRA训练进度 ===")
    check_i2v_training_progress("./models/train/Wan2.1-I2V-14B-480P_lora")

    print("\n=== I2V全量训练进度 ===")
    check_i2v_training_progress("./models/train/Wan2.1-I2V-14B-480P_full")

    # 验证样本生成
    print("\n=== I2V样本生成验证 ===")
    validate_i2v_sample_generation("./test_image.jpg")
```

## 常见问题和解决方案

### 1. I2V特有问题

**问题**: 图像编码器加载失败
```
Error loading CLIP model: models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth
```

**解决方案**:
- 检查CLIP模型文件是否完整下载
- 验证ModelScope访问权限
- 手动下载CLIP模型文件

**问题**: 图像-视频不匹配
```
Generated video doesn't match input image
```

**解决方案**:
- 确保训练数据中图像和视频内容相关
- 检查输入图像的预处理是否正确
- 调整训练参数，特别是学习率

### 2. 显存优化（I2V特有）

**问题**: I2V模型显存占用更高

**解决方案**:
- 降低图像分辨率：使用480x480而不是更高分辨率
- 减少视频帧数
- 使用更小的LoRA rank：`--lora_rank 16`
- 启用图像编码器offload：`offload_device="cpu"`

### 3. 数据集问题

**问题**: 图像-视频对不匹配

**解决方案**:
- 使用数据集验证脚本检查数据完整性
- 确保metadata.csv格式正确
- 验证图像和视频文件路径

### 4. 推理质量问题

**问题**: 生成的视频质量不佳

**解决方案**:
- 调整推理参数：增加推理步数
- 优化提示词：提供更详细的描述
- 检查输入图像质量和分辨率
- 调整negative_prompt

## 性能优化建议

### 1. I2V特定优化
- **图像预处理**: 统一输入图像分辨率和格式
- **CLIP编码器优化**: 合理设置offload策略
- **图像-视频对齐**: 确保训练数据质量

### 2. 训练优化
- **学习率调度**: I2V通常需要更小的学习率
- **数据增强**: 适当的图像增强可以提高泛化能力
- **批处理策略**: 根据显存情况调整batch size

### 3. 推理优化
- **缓存策略**: 缓存CLIP图像特征以加速推理
- **并行处理**: 批量处理多个图像
- **内存管理**: 合理使用VRAM管理功能

## 总结

本文档基于最新版DiffSynth-Studio，提供了Wan-AI/Wan2.1-I2V-14B-480P模型的完整多卡微调流程。主要特点：

1. **专门针对I2V任务**：包含图像编码器配置和图像-视频对处理
2. **完全基于官方最新代码**：使用最新的API和脚本格式
3. **支持LoRA和全量微调**：提供两种训练方式的完整配置
4. **详细的I2V数据准备流程**：从图像-视频对创建到格式验证
5. **完整的推理和合并代码**：支持多种I2V推理场景和模型合并
6. **丰富的监控和调试工具**：便于I2V训练过程管理和问题排查
7. **I2V特有的问题解决方案**：涵盖图像条件生成的特殊问题

请根据实际硬件配置和数据集情况调整相关参数，特别注意I2V任务对图像-视频对质量的特殊要求。

﻿# “你好，北京”——非遗与入境游深度融合发展对接会在正乙祠举办_城市活动_首都之窗_北京市人民政府门户网站

**发布日期**: 2023年09月28日

**原文链接**: https://www.beijing.gov.cn/renwen/cshd/202311/t20231128_3317909.html

## 📄 原文内容

    .m-share{float: left;margin-top:-5px;}
    .share:after{content: "";display: block;clear: both;}
    .share a{float: left;width: 26px;height: 26px;border-radius: 50%;margin-left: 13px;background: #c9c9c9;cursor: pointer;display: block;}
    .share a i{display: block;width: 26px;height: 26px;-webkit-transition: .4s all;-moz-transition: .4s all;-ms-transition: .4s all;transition: .4s all;}
    .share a:hover i{-webkit-transform: rotate(360deg);-moz-transform: rotate(360deg);-ms-transform: rotate(360deg);transform: rotate(360deg);}
    .share #share-icon{width: 26px;height: 26px;float: left;display: none;}
    .share #share-icon img{width: 100%;height: 100%;}
    .share .share-qqzone i{background: url("/images/cont_ico_share_20200422.png") 4px center no-repeat;}
    .share .share-qqzone:hover{background-color: #fc7354;}
    .share .share-wechat{position: relative;}
    .share .share-wechat i{background: url("/images/cont_ico_share_20200422.png") -30px center no-repeat;}
    .share .share-wechat:hover{background-color: #1fbc7d;}
    .share .share-weibo i{background: url("/images/cont_ico_share_20200422.png") -65px center no-repeat;}
    .share .share-qq:hover{background-color: #27a8f2;}
    .share .share-qq i{background: url("/images/cont_ico_share_20200422.png") -96px center no-repeat;}
    .share .share-weibo:hover{background-color: #e96157;}
    .share .bg-code{left: -36px;z-index: 10;}
    .share .qrcode{position: absolute;top: 36px;border: 1px solid #ccc;padding: 5px;background: #fff;display: none;width: 100px;height: 100px;left: -98%;z-index: 11;}
    .share .close-btn{position: absolute;background: #fff;color: #000;font-size: 12px;z-index: 12;width: 12px;height: 12px;line-height: 12px;text-align: center;right: -39px;top: 50px;display: none;cursor: pointer;}
    var title = "【" + $.trim(document.title) + "】";
    var description = $.trim($('meta[name="description"]').attr('content'));
    var portalUrl = window.location.href;
    function generateQRCode(rendermethod, picwidth, picheight, url) {
        $(".qrcode").qrcode({
            render: rendermethod, // 渲染方式有table方式（IE兼容）和canvas方式
            width: picwidth, //宽度
            height: picheight, //高度
            text: utf16to8(portalUrl), //内容
            typeNumber: -1, //计算模式
            correctLevel: 2, //二维码纠错级别
            background: "#ffffff", //背景颜色
            foreground: "#000000" //二维码颜色
    canvas_table = !!document.createElement('canvas').getContext ? 'canvas' : 'table';
    function init() {
        generateQRCode(canvas_table, 100, 100, window.location.href);
    function utf16to8(str) {
        var out, i, len, c;
        len = str.length;
        for(i = 0; i < len; i++) {
            c = str.charCodeAt(i);
            if((c >= 0x0001) && (c <= 0x007F)) {
                out += str.charAt(i);
            } else if(c > 0x07FF) {
                out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
                out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
                out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
                out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
                out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
    $(".share-wechat").on("click", function() {
        $(".bg-code,.qrcode").toggle();
    $(".close-btn").on("click", function(event) {
        $(".bg-code,.qrcode,.close-btn").hide();
        event.stopPropagation();
    function showToQzone() {
         var _desc = description;
         var _title = title;
         var _url = portalUrl;
         var _shareUrl = 'http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?';
         _shareUrl += 'url=' + encodeURIComponent(_url);   //参数url设置分享的内容链接|默认当前页location
         _shareUrl += '&title=' + encodeURIComponent(_title);    //参数title设置分享标题，可选参数
         window.open(_shareUrl,'','width=700,height=680,top=0,left=0,toolbar=no,menubar=no,scrollbars=no,resizable=1,location=yes,status=0'); 
    function showToSina(title, portalUrl, desc) {
        var _desc = desc;
        var _t = title + " " + _desc;
        var _url = portalUrl;
        var _appkey = "2806082167"; //你从微薄获得的appkey
        var _site = ''; //你的网站地址
        var _ralateUid = "";
        var _u = 'http://service.weibo.com/share/share.php?url=' + _url + '&appkey=' + _appkey + '&title=' + _t + '&ralateUid=' + _ralateUid + '&searchPic=false';
        window.open(_u, '', 'width=700, height=680, top=0, left=0, toolbar=no, menubar=no, scrollbars=no, location=yes, resizable=no, status=no');
    $('.share-qqzone').on('click', function() {
        showToQzone(title, portalUrl, description);
    $('.share-weibo').on('click', function() {
        showToSina(title, portalUrl, description);
function changeSize(size) {
                            document.getElementById('mainText').style.fontSize=size+'px'
                            document.querySelector(".activity_info").style.fontSize=size+'px'
                        }
“你好，北京”——非遗与入境游深度融合发展对接会在正乙祠举办
        if($('.end_date dd').text() == ''){
            $('.end_date dd').text('----');
11月27日，时值北京的初冬，而在拥有三百年历史的正乙祠大戏楼中，一场别开生面、温暖热络的文化旅游业界交流会正在以一种特别的方式开展。30位北京代表性非遗项目传承人、20家入境旅游企业负责人以及北京文旅业界专家在这里汇聚切磋，为进一步推动北京文旅融合发展、拓展北京非遗文化的新场景应用，进行深度的交流探讨。
今年年初，文化和旅游部印发了《关于推动非物质文化遗产与旅游深度融合发展的通知》，要求牢牢把握非物质文化遗产保护传承和旅游发展的规律特点，在有效保护的前提下，推动非物质文化遗产与旅游在更广范围、更深层次、更高水平上融合。
2023年，围绕服务“四个中心”功能建设，根据“两区”建设国际人才服务要求，加强对外文化宣传推介，推动非遗文化与入境旅游的深度融合，北京市文化和旅游局推出以“你好，北京”为主题的系列活动，先后于9月28日在智化寺举办“非遗体验日”，10月14日在圣露国际庄园举办“非遗家庭日”以及11月27日在正乙祠举办非遗与入境游深度融合发展对接会。
作为年度活动的最后一场，本次业界交流会锁定非遗文化与入境游融合发展的主题，搭建信息、资源、渠道、市场共建共享平台，现场干货满满、实用高效：
邀请各区文旅局代表、入境旅游企业与非遗文化传承人现场面对面对接资源，推出《北京非遗联络手册》，促进行业间业务交流。
在被誉为“中华戏楼文化史上的活化石”的正乙祠戏楼，汇集了戏曲、手工艺、美食等30项北京代表性非遗项目。金漆镶嵌、北京绢人、彩绘京剧脸谱等优秀非遗传承人代表进行了主题推介。与会嘉宾在场景化和体验感中，充分交流非遗文化与旅游产品的融合创新，气氛活跃、获益良多。
北京燕京八绝博物馆馆长柏群、途易（中国）首席执行官赵红宇、万事达卡大中华区市场营销副总经理吴焕宇、资深文旅策划人窦俊杰等业界人士，共同就“非遗与入境游深度融合发展”为主题进行了交流分享，一致表示“你好，北京”系列活动为北京的入境游线路产品的创新研发带来了新思路，为北京优质的非遗文化项目走向国际开辟了新渠道，为北京的旅游消费新场景的打造积累了良好经验。
作为“你好，北京”的品牌外宣产品，英文版《你好，北京》非遗画册正式发布，精美的原创图片、通俗易读的文案、国际化的装帧设计，为北京非遗文化对外宣传推广助力。
北京市文化和旅游局对外交流与合作处相关负责人表示，“你好，北京”系列活动不仅注重现场的体验感，更重要的是为未来持续形成更具创新活力的北京入境旅游线路和产品助力。2023年是这项活动的首次举办，以后将持续开展，力争形成具有国际化效应的文旅融合新品牌。
#qr_wrap{width: 135px;margin: 0 auto;padding-bottom:40px;}
    var text = window.location.href;
    $('#qr_wrap').qrcode({
        text: utf16to8(text),
    function utf16to8(str) { //转码
    var out, i, len, c;
    len = str.length;
    for (i = 0; i < len; i++) {
        c = str.charCodeAt(i);
        if ((c >= 0x0001) && (c <= 0x007F)) {
            out += str.charAt(i);
        } else if (c > 0x07FF) {
            out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
            out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
            out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
            out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
            out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
var oDl = document.getElementById('fontSize');
        var aA = oDl.getElementsByTagName('a');  
        for(var i = 0;i<aA.length;i++){
            aA[i].index  = i;     
            aA[i].onclick = function(){  
                for(var i = 0;i<aA.length;i++){
                    aA[i].className = '';
                this.className = 'on';
.alert-mengban{position: fixed;top: 0px;left: 0px;z-index: 1000;background: #000000;opacity: 0.8 !important;filter: alpha(opacity=80) !important;width: 100%;height: 100%;display: none;}
        .alert-warning{position: fixed;left: 50%;top:-400px;margin-left:-300px; width: 600px;height: 270px;background:#fff;z-index: 1001;display: none;}
        .alert-delete{width: 100%;height: 38px;position: relative;}
        .alert-delete span{position: absolute;top:10px;right: 10px; width: 19px;height: 19px;background: url(/ywdt/images/delete-ks-20170807.png) center center no-repeat;cursor:pointer;}
        .alert-wzsm{width: 480px;height: 100px;margin: 15px auto 0; line-height: 35px;font-size: 24px;color: #000;text-align: center;font-family:"Microsoft YaHei"; padding-bottom: 15px;border-bottom: 1px solid #d4d4d4;}
        .alert-wzsm p{font-size:24px;font-family:"Microsoft YaHei";}
        .alert-footer{width: 100%; height: 105px;font-size: 24px;color: #000;}
        .alert-footer span{cursor: pointer;float: left;font-family:"Microsoft YaHei";}
        .continue{width: 124px;height: 42px;background: url(/ywdt/images/continue-ks-20170810.png) center center no-repeat;}
        .fangqi{line-height: 42px;font-size: 20px;color: #ab0d07;margin-left: 20px;}
        .xuanze{width: 210px;height: 42px;margin: 25px auto 0;}
        @media only screen and (max-width: 414px) {
            .alert-warning{position: fixed;left:2%;top:-400px;margin-left:0; width: 96%;height: auto;background: #fff;background-size: 100%; z-index: 1001;}
            .alert-wzsm{width: 80%;height: auto;margin: 15px auto 0; line-height: 28px;font-size: 18px;color: #000;text-align: center; }
            .alert-wzsm p{font-size:18px;}
            .alert-footer{width: 100%; height: 70px;line-height: 70px;font-size: 18px;color: white;margin-top: 10px;}
            .continue{ width: 124px;height: 42px;background: url(/ywdt/images/continue-ks-20170810.png) center center no-repeat;background-size: 100%;}
您访问的链接即将离开“首都之窗”门户网站 是否继续？
$("a").each(function(){
    var htm=$(this).html();
    $(this).click(function(){
		if(this.href!=""&&this.href.toLowerCase().indexOf("javascript")==-1&&this.href.toLowerCase().indexOf(".gov.cn")==-1&&this.href.toLowerCase().indexOf("javascript:preVious")==-1&&this.href.toLowerCase().indexOf("javascript:next")==-1){
		document.getElementById('outUrl').innerText=this.href;
	          document.getElementById('hash').click();
		$(".alert-mengban").fadeIn(200);
		$(".alert-warning").delay(200).show().animate({top:"75px"}, 300);
		$("#closets,.fangqi,.alert-mengban").click(function() {
			$(".alert-warning").animate({top:"-400px"}, 200).hide(300);
			$(".alert-mengban").delay(300).fadeOut(300);
		$(".continue").click(function(){			
		       $(".alert-warning").hide(200);
	                 $(".alert-mengban").delay(200).fadeOut(200);
/*$("select").on("change",function () {
 var opVal = $(this).find("option:selected").val();
 if(opVal!=""&&opVal.toLowerCase().indexOf("javascript")==-1&&opVal.toLowerCase().indexOf(".gov.cn")==-1&&opVal.toLowerCase().indexOf(".ebeijing.gov.cn")==-1&&opVal.toLowerCase().indexOf(".bj.gov.cn")==-1&&opVal.toLowerCase().indexOf("javascript:preVious")==-1&&opVal.toLowerCase().indexOf("javascript:next")==-1){
                document.getElementById('outUrl').innerText=opVal;
                document.getElementById('hash').click();
                $(".alert-mengban").fadeIn(200);
                $(".alert-warning").delay(200).show().animate({top:"75px"}, 300);
                $("#closets,.fangqi,.alert-mengban").click(function() {
                    $(".alert-warning").animate({top:"-400px"}, 200).hide(300);
                    $(".alert-mengban").delay(300).fadeOut(300);
                $(".continue").click(function(){
                    $(".alert-warning").hide(200);
                    $(".alert-mengban").delay(200).fadeOut(200);
                return false;
#f-file{display:none;width:263px;height:85px;position:absolute;top:126px;left:50%;margin-left:405px;z-index:99999;}
    #f-flag{color:#D01818;font-weight:bold;font-size:20px;position:absolute;top:64px;left:24px;outline:none;}
    #f-file img{width:263px;height:85px;border:0;outline:none;}
@media only screen and (max-width: 750px){
#f-file{display:none;width:35%;height:auto;position:absolute;top:85px;right:1%;left:auto;margin-left:0;z-index:99999;}
#f-file img{width:100%; height:auto;}
#f-flag{position:relative; left:0; top:0; font-size: 16px;}
var _flag = ($("#f-flag").text()).trim();
var arr = _flag.split(',');
if ($.inArray("已归档", arr) == 0) {
    $("#f-file").css({ "display": "block" });
$("#f-flag").text(arr[1]);
$(document).ready(function() {
    var file_h = $("#w_header").height();
    function isMob() {
        return /AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))
        $("#f-file").css({
            "top": file_h - 15 + "px"
        $("#f-file").css({
            "top": file_h + "px"
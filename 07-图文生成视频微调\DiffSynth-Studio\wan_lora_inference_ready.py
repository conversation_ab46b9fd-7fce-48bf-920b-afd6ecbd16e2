#!/usr/bin/env python3
"""
基于官方示例的LoRA推理脚本
直接可运行的版本
"""

import torch
from torch.nn.parallel import DataParallel
from PIL import Image
import os
from diffsynth import save_video, VideoData
from diffsynth.pipelines.wan_video_new import WanVideoPipeline, ModelConfig

def load_lora_weights(pipeline, lora_path):
    """加载LoRA权重"""
    print(f"🔧 加载LoRA权重: {lora_path}")
    
    if not os.path.exists(lora_path):
        print(f"❌ LoRA文件不存在: {lora_path}")
        return False
    
    try:
        from safetensors import safe_open
        lora_weights = {}
        with safe_open(lora_path, framework="pt", device="cpu") as f:
            for key in f.keys():
                lora_weights[key] = f.get_tensor(key)
        
        print(f"   ✅ 加载了 {len(lora_weights)} 个LoRA参数")
        return True
        
    except Exception as e:
        print(f"   ❌ LoRA加载失败: {e}")
        return False

# 主程序
print("🎬 Wan2.1-I2V-14B-480P LoRA推理")
print("=" * 50)

# LoRA检查点路径
lora_checkpoint = "./models/train/Wan2.1-I2V-14B-480P_lora_final/epoch-4.safetensors"

# 检查LoRA文件
lora_available = os.path.exists(lora_checkpoint)
if lora_available:
    print(f"✅ 找到LoRA检查点: {lora_checkpoint}")
else:
    print(f"⚠️  LoRA检查点不存在: {lora_checkpoint}")
    print("   将使用基础模型进行推理")

print("📦 初始化Pipeline...")

# 创建pipeline（与官方示例相同）
pipe = WanVideoPipeline.from_pretrained(
    torch_dtype=torch.bfloat16,
    device="cuda",
    model_configs=[
        ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="diffusion_pytorch_model*.safetensors", offload_device="cpu"),
        ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="models_t5_umt5-xxl-enc-bf16.pth", offload_device="cpu"),
        ModelConfig(model_id="Wan-AI/Wan2.1-T2V-1.3B", origin_file_pattern="Wan2.1_VAE.pth", offload_device="cpu"),
        ModelConfig(model_id="Wan-AI/Wan2.1-I2V-14B-480P", origin_file_pattern="models_clip_open-clip-xlm-roberta-large-vit-huge-14.pth", offload_device="cpu"),
    ],
)
pipe.enable_vram_management()

print("✅ Pipeline初始化成功")

# 设置多GPU（暂时禁用DataParallel以避免属性访问问题）
gpu_count = torch.cuda.device_count()
if gpu_count > 1:
    print(f"🚀 检测到多GPU: {gpu_count} 张GPU")
    print("⚠️  暂时使用单GPU模式以避免DataParallel兼容性问题")
    # TODO: 需要更深入的DataParallel集成
    # if hasattr(pipe, 'dit') and pipe.dit is not None:
    #     pipe.dit = DataParallel(pipe.dit, device_ids=list(range(min(gpu_count, 2))))
    #     print("✅ DiT模型已设置DataParallel")
else:
    print("📱 单GPU模式")

# 加载LoRA权重
if lora_available:
    load_lora_weights(pipe, lora_checkpoint)

print("\n🎬 开始生成视频...")

# 测试用例
test_cases = [
    {
        "prompt": "A beautiful sunset over the ocean with gentle waves, cinematic lighting",
        "output": "lora_sunset.mp4",
        "description": "海洋日落"
    },
    {
        "prompt": "A cute cat playing with a colorful ball in a sunny garden",
        "output": "lora_cat.mp4", 
        "description": "猫咪花园"
    },
    {
        "prompt": "Majestic snow-capped mountains with clouds moving across the sky",
        "output": "lora_mountains.mp4",
        "description": "雪山云海"
    }
]

successful_count = 0

for i, test_case in enumerate(test_cases):
    print(f"\n--- 案例 {i+1}/{len(test_cases)}: {test_case['description']} ---")
    print(f"提示词: {test_case['prompt']}")
    
    try:
        # 生成视频
        video = pipe(
            prompt=test_case["prompt"],
            negative_prompt="色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走",
            seed=42 + i,  # 不同的种子
            tiled=True,
            height=480,
            width=832,
            num_frames=81,
            cfg_scale=7.5,
            num_inference_steps=50
        )
        
        # 保存视频
        save_video(video, test_case["output"], fps=15, quality=5)
        
        # 检查文件
        if os.path.exists(test_case["output"]):
            file_size = os.path.getsize(test_case["output"]) / 1024**2
            print(f"✅ 生成成功: {test_case['output']} ({file_size:.1f}MB)")
            successful_count += 1
        else:
            print(f"❌ 文件未生成: {test_case['output']}")
            
    except Exception as e:
        print(f"❌ 生成失败: {e}")

print(f"\n" + "=" * 50)
print("📊 推理结果统计:")
print(f"   成功生成: {successful_count}/{len(test_cases)} 个视频")
print(f"   使用GPU: {gpu_count} 张")
print(f"   LoRA状态: {'✅ 已加载' if lora_available else '❌ 未加载'}")

if successful_count > 0:
    print(f"\n📁 生成的视频文件:")
    for test_case in test_cases:
        if os.path.exists(test_case["output"]):
            file_size = os.path.getsize(test_case["output"]) / 1024**2
            print(f"   {test_case['output']} ({file_size:.1f}MB)")

print(f"\n🎉 LoRA推理完成!")

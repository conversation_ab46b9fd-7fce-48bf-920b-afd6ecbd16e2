<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek API 聊天</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background-color: #f4f4f9; }
        #chat-container { width: 80%; max-width: 800px; height: 90vh; display: flex; flex-direction: column; border: 1px solid #ccc; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        #config-bar { padding: 10px; background-color: #f0f0f0; border-bottom: 1px solid #ccc; display: flex; align-items: center; gap: 10px; }
        #config-bar input, #config-bar select { padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        #api-key { flex-grow: 1; }
        #messages { flex-grow: 1; padding: 20px; overflow-y: auto; background-color: #fff; }
        .message { margin-bottom: 15px; line-height: 1.6; }
        .message .sender { font-weight: bold; color: #333; }
        .message .content { padding: 10px; background-color: #e9e9eb; border-radius: 8px; display: inline-block; max-width: 90%; word-wrap: break-word; }
        .message.user .content { background-color: #007bff; color: white; }
        #input-area { display: flex; padding: 10px; border-top: 1px solid #ccc; background-color: #f0f0f0; }
        #user-input { flex-grow: 1; padding: 10px; border: 1px solid #ccc; border-radius: 4px; resize: none; }
        #send-btn { margin-left: 10px; padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        #send-btn:disabled { background-color: #a0a0a0; }
    </style>
</head>
<body>
    <div id="chat-container">
        <div id="config-bar">
            <label for="api-key">API Key:</label>
            <input type="password" id="api-key" placeholder="在此输入您的 EdgeFN API Key">
            <label for="model-select">模型:</label>
            <select id="model-select">
                <option value="deepseek-chat">deepseek-chat</option>
                <option value="deepseek-coder">deepseek-coder</option>
            </select>
        </div>
        <div id="messages"></div>
        <div id="input-area">
            <textarea id="user-input" placeholder="输入您的问题..." rows="3"></textarea>
            <button id="send-btn">发送</button>
        </div>
    </div>

    <script>
        const sendBtn = document.getElementById('send-btn');
        const userInput = document.getElementById('user-input');
        const messagesDiv = document.getElementById('messages');
        const apiKeyInput = document.getElementById('api-key');
        const modelSelect = document.getElementById('model-select');

        let conversationHistory = [];

        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        function addMessage(sender, text) {
            const messageElem = document.createElement('div');
            messageElem.classList.add('message', sender);
            
            const senderElem = document.createElement('div');
            senderElem.classList.add('sender');
            senderElem.textContent = sender === 'user' ? 'You' : 'Assistant';
            
            const contentElem = document.createElement('div');
            contentElem.classList.add('content');
            contentElem.textContent = text;

            messageElem.appendChild(senderElem);
            messageElem.appendChild(contentElem);
            messagesDiv.appendChild(messageElem);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        async function sendMessage() {
            const userText = userInput.value.trim();
            const apiKey = apiKeyInput.value.trim();
            const selectedModel = modelSelect.value;

            if (!userText) return;
            if (!apiKey) {
                alert('请输入您的 API Key。');
                return;
            }

            addMessage('user', userText);
            conversationHistory.push({ role: 'user', content: userText });
            userInput.value = '';
            sendBtn.disabled = true;

            try {
                const response = await fetch('https://api.edgefn.net/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        messages: conversationHistory,
                        stream: false
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`API 请求失败: ${response.status} ${response.statusText} - ${errorData.error.message}`)
                }

                const data = await response.json();
                const assistantMessage = data.choices[0].message.content;
                addMessage('assistant', assistantMessage);
                conversationHistory.push({ role: 'assistant', content: assistantMessage });

            } catch (error) {
                console.error('Error:', error);
                addMessage('assistant', `出现错误: ${error.message}`);
            } finally {
                sendBtn.disabled = false;
                userInput.focus();
            }
        }
    </script>
</body>
</html>
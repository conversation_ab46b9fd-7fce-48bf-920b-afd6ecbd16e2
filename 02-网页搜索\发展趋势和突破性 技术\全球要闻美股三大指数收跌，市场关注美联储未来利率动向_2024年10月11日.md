﻿# 全球要闻|美股三大指数收跌，市场关注美联储未来利率动向

**发布日期**: 2024年10月11日

**原文链接**: https://stock.10jqka.com.cn/20241011/c662318446.shtml

## 📄 原文内容

脠芦脟貌脪陋脦脜|脙脌鹿脡脠媒麓贸脰赂脢媒脢脮碌酶拢卢脢脨鲁隆鹿脴脳垄脙脌脕陋麓垄脦麓脌麓脌没脗脢露炉脧貌
						                    脥卢禄篓脣鲁Knews
10脭脗10脠脮拢卢禄霉脳录碌脛10脛锚脝脷脙脌脮庐脢脮脪忙脗脢脦陋4.09%拢卢露脭脙脌脕陋麓垄脮镁虏脽脌没脗脢脳卯脙么赂脨碌脛2脛锚脝脷脙脌脮庐脢脮脪忙脗脢脦陋3.98%隆拢
脠脠脙脜脙脌鹿脡脰脨拢卢脝禄鹿没碌酶0.22%拢卢脫垄脦掳麓茂脮脟1.63%拢卢脦垄脠铆碌酶0.39%拢卢鹿脠赂猫C脮脟0.07%拢卢鹿脠赂猫A脮脟0.14%拢卢脩脟脗铆脩路脮脟0.80%拢卢Meta碌酶1.13%拢卢脤篓禄媒碌莽碌酶0.72%拢卢脤脴脣鹿脌颅碌酶0.95%拢卢鲁卢脦垄掳毛碌录脤氓碌酶4.00%隆拢
10脭脗10脠脮拢卢赂么脪鹿潞茫脡煤驴脝录录脰赂脢媒脝脷禄玫脡脧脮脟2.31%拢卢脛脡脣鹿麓茂驴脣脰脨鹿煤陆冒脕煤脰赂脢媒脡脧脮脟0.30%拢卢赂禄脢卤脰脨鹿煤A50脰赂脢媒脡脧脮脟2.24%隆拢
脠脠脙脜脰脨赂脜鹿脡路陆脙忙拢卢脤脷脩露驴脴鹿脡(赂脹鹿脡)脮脟1.06%拢卢掳垄脌茂掳脥掳脥脮脟1.29%拢卢脝麓露脿露脿脮脟0.08%拢卢脥酶脪脳碌酶1.12%拢卢脨炉鲁脤脮脟2.29%拢卢掳脵露脠脮脟0.01%拢卢脌铆脧毛脝没鲁碌碌酶3.76%拢卢脦碌脌麓碌酶0.79%拢卢脨隆脜么脝没鲁碌脮脟0.96%隆拢
AMD赂脽鹿脺拢潞脭陇录脝碌陆2028脛锚脢媒戮脻脰脨脨脛脠脣鹿陇脰脟脛脺脢脨鲁隆陆芦脭枚鲁陇脰脕5000脪脷脙脌脭陋
AMD赂脽鹿脺卤铆脢戮拢卢脭陇录脝碌陆2028脛锚脢媒戮脻脰脨脨脛脠脣鹿陇脰脟脛脺脢脨鲁隆陆芦脭枚鲁陇脰脕5000脪脷脙脌脭陋隆拢
AMD脨没虏录脥脝鲁枚脨脗驴卯Turin Epyc脢媒戮脻脰脨脨脛CPU虏煤脝路
脫垄脤脴露没路垄虏录脢脳驴卯AI PC脤篓脢陆禄煤麓娄脌铆脝梅驴谩卯拢Ultra 200S
脙脌脕陋麓垄脥镁脕庐脛路脣鹿拢潞脦脪脭陇录脝陆帽脛锚脥篓脮脥脗脢陆芦录玫脠玫脰脕2.25%
脙脌脕陋麓垄脥镁脕庐脛路脣鹿拢潞脦脪脭陇录脝陆帽脛锚脥篓脮脥脗脢陆芦录玫脠玫脰脕2.25%拢卢2025脛锚陆脫陆眉2%隆拢
脙脌脕陋麓垄掳脥露没陆冒拢潞脙脌脕陋麓垄卤戮脫娄赂脙麓脫2021脛锚戮脥驴陋脢录录脫脧垄
脙脌脕陋麓垄掳脥露没陆冒拢潞脙脌脕陋麓垄卤戮脫娄赂脙麓脫2021脛锚戮脥驴陋脢录录脫脧垄拢卢赂眉脭莽脝么露炉录脫脧垄卤戮驴脡录玫脡脵驴矛脣脵录脫脧垄碌脛卤脴脪陋脨脭隆拢
脙脌脕陋麓垄掳脥陆冒拢潞脥篓脮脥脮媒鲁炉脳脜脮媒脠路碌脛路陆脧貌路垄脮鹿
脙脌脕陋麓垄掳脥陆冒拢潞脥篓脮脥脮媒鲁炉脳脜脮媒脠路碌脛路陆脧貌路垄脮鹿隆拢
脙脌脕陋麓垄鹿脜露没脣鹿卤脠拢潞脙脌脕陋麓垄脭陇虏芒脧脭脢戮拢卢戮酶麓贸露脿脢媒脠脣脠脧脦陋脭脷陆脫脧脗脌麓碌脛12碌陆18赂枚脭脗脛脷拢卢戮颅录脙脳麓驴枚陆芦鲁脰脨酶赂脛脡脝拢卢脌没脗脢陆芦脰冒陆楼麓贸路霉脧脗陆碌
脙脌脕陋麓垄鹿脜露没脣鹿卤脠拢潞脙脌脕陋麓垄脭陇虏芒脧脭脢戮拢卢戮酶麓贸露脿脢媒脠脣脠脧脦陋脭脷陆脫脧脗脌麓碌脛12碌陆18赂枚脭脗脛脷拢卢戮颅录脙脳麓驴枚陆芦鲁脰脨酶赂脛脡脝拢卢脌没脗脢陆芦脰冒陆楼麓贸路霉脧脗陆碌隆拢
路脰脦枚脢娄鲁脝脙脌鹿煤CPI脭枚录脫脕脣脧脗脭脗脙脌脕陋麓垄陆碌脧垄25BP碌脛脌铆脫脡
路脰脦枚脢娄拢潞脙脌鹿煤9脭脗脮没脤氓脥篓脮脥脭脗脗脢脪虏赂脽鲁枚脢庐路脰脰庐脪禄赂枚掳脵路脰碌茫拢卢麓茂碌陆0.2%拢卢脮芒脭枚录脫脕脣脧脗赂枚脭脗陆碌脧垄25赂枚禄霉碌茫露酶虏禄脢脟50赂枚禄霉碌茫碌脛脌铆脫脡隆拢脫毛麓脣脥卢脢卤拢卢鲁玫脟毛脢搂脪碌陆冒脠脣脢媒脙脥脭枚258,000脠脣拢卢碌芦脮芒陆芦脢脺碌陆矛芦路莽碌脠脪貌脣脴碌脛脫掳脧矛隆拢
陆禄脪脳脭卤脭枚录脫脕脣露脭11脭脗路脻脙脌脕陋麓垄陆碌脧垄25赂枚禄霉碌茫碌脛脩潞脳垄
陆禄脪脳脭卤脭枚录脫脕脣露脭11脭脗路脻脙脌脕陋麓垄陆碌脧垄25赂枚禄霉碌茫碌脛脩潞脳垄隆拢
(function(){var obj = null;if(document.getElementById('arctTailMark')) {obj = document.getElementById('arctTailMark');}if(obj!=null){var str = '<a href="http://www.10jqka.com.cn" target="_blank" id="back_web" style="font-weight:bold" rel="nofollow" class="backweb"><img src="http://i.thsi.cn/images/article/logo.jpg" style="height:15px">  路碌禄脴脢脳脪鲁</a><a href="https://t.10jqka.com.cn/m/game/getActivityPageById/?id=295" target="_blank" style="font-weight: bold; font-size: 14px; padding-left: 5px; margin-left: 5px; border-left: 2px solid">戮脵卤篓 &gt;</a>';var showed = 0;try{var oo = obj.previousSibling;while(oo.nodeType!=1 || oo.innerHTML.replace(/\s|\u3000/g,'').length==0){oo = oo.previousSibling;}if(oo.nodeType==1 && oo.tagName.toLowerCase()=='p') {var span = document.createElement('span');span.innerHTML =str;span.style.width = '130px';oo.appendChild(span);showed = 1;}}catch (e){}if(showed == 0){obj.innerHTML =str;}}})();
var hotstocks = new Array();

	        	        hotstocks['000716'] = '000716';

	    	    	        hotstocks['002456'] = '002456';

	    	    	        hotstocks['601216'] = '601216';

	    	    	        hotstocks['603005'] = '603005';

	    	    	        hotstocks['600206'] = '600206';

	    	    	        hotstocks['000795'] = '000795';

	    	    	        hotstocks['000564'] = '000564';

	    	    	        hotstocks['002510'] = '002510';
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄脗貌脠毛脟掳10鹿脡
露脌录脪脳脢陆冒拢潞脭莽脜脤脰梅脕娄陆酶脡垄禄搂脤脫脟掳10鹿脡
露脌录脪脳脢陆冒拢潞陆帽脠脮脰梅脕娄脗貌陆酶脟掳10鹿脡
CPO赂脜脛卯脮冒碌麓禄脴脡媒 脨脗脪脳脢垄脮脟鲁卢8%
脧没路脩碌莽脳脫赂脜脛卯鹿脡脮冒碌麓脳脽碌脥 脜路路脝鹿芒碌脠露脿鹿脡碌酶鲁卢5%
A鹿脡脠媒麓贸碌莽脨脜脭脣脫陋脡脤录炉脤氓脳脽脟驴 脰脨鹿煤碌莽脨脜脮脟鲁卢5%
脡脧潞拢脢脨脮镁赂庐鲁拢脦帽禄谩脪茅虏驴脢冒拢卢脰搂鲁脰脡脧脢脨鹿芦脣戮虏垄鹿潞脰脴脳茅拢卢脤谩脡媒鹿芦脣戮脰脢脕驴脜脿脫媒脕煤脥路脝贸脪碌
脰脨脨脜脰陇脠炉脨脗脠脦脳脺戮颅脌铆 脢脳麓脦鹿芦驴陋路垄脡霉
GPU露脌陆脟脢脼脛娄露没脧脽鲁脤陆芦鲁氓麓脤IPO拢隆虏脦鹿脡隆垄潞脧脳梅碌脠赂脜脛卯鹿脡脙没碌楼脪禄脌脌
脭酶脴鹿脠潞拢潞脠么脤脴脌脢脝脮隆掳驴陋脗脤碌脝隆卤拢卢脛镁碌脗脢卤麓煤陆芦驴录脗脟赂掳脙脌陆篓鲁搂
赂脮赂脮拢隆脩脟脤芦鹿脡脢脨拢卢脠芦脧脽脤酶脣庐拢隆A50脰卤脧脽路颅脗脤拢卢赂脹鹿脡脡卯碌梅拢隆路垄脡煤脕脣脢虏脙麓
脦脢陆莽M7脢脗鹿脢脨脗陆酶脮鹿拢潞鲁碌脰梅鲁脝隆掳鹿媒脫脷脧脿脨脜脫脿鲁脨露芦虏脜鹿潞脗貌隆卤虏垄脝冒脣脽禄陋脦陋
脌毛隆掳虏禄脗么戮脥陆没隆卤脰禄脢拢脕陆赂枚露脿脭脗拢隆脤脴脌脢脝脮脥脜露脫拢潞陆芦露脪脧脰鲁脨脜碌 脮眉戮脠TikTok
脡卯陆禄脣霉拢潞11脭脗16脠脮陆芦陆酶脨脨脡卯脢脨陆禄脪脳脧碌脥鲁虏芒脢脭
		var s = "_" + Math.random().toString(36).slice(2);
		document.write('<div id="' + s + '"></div>');
		(window.slotbydup=window.slotbydup || []).push({
			display: 'inlay-fix'
document.addEventListener('DOMContentLoaded', function () {
      var userid = 'nouser';
        // 忙拢聙忙聼楼 Vue 氓聮聦 AdvertCommon 忙聵炉氓聬娄氓颅聵氓聹篓
        if (Vue && window['mobileweb_AdvertCommon@1.0.4'].default) {
          Vue.use(window['mobileweb_AdvertCommon@1.0.4'].default);
          // 氓掳聺猫炉聲盲禄聨 cookie 盲赂颅忙聫聬氓聫聳 userid
          var match = document.cookie.match(/userid=([^;]*)/);
          if (match) {
            userid = match[1] || 'nouser';
      } catch (error) {
        console.error(error);
      // 氓聢聺氓搂聥氓聦聳 Vue 氓庐聻盲戮聥
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
            appid: '21',
            platform: 'pc_web',
            versionid: '3708555'
          userid: userid,
          isProd: true
脡忙脦麓鲁脡脛锚脠脣脦楼鹿忙脛脷脠脻戮脵卤篓
虏禄脕录脨脜脧垄戮脵卤篓碌莽禄掳拢潞(0571)88933003
戮脵卤篓脫脢脧盲拢潞*****************
脮茫陆颅脥卢禄篓脣鲁禄楼脕陋脨脜脧垄录录脢玫脫脨脧脼鹿芦脣戮掳忙脠篓脣霉脫脨
var newsid = '662318446';
    var id='c_662318446';
	var initctime = '1728601210';
	var stitle = encodeURIComponent('脠芦脟貌脪陋脦脜|脙脌鹿脡脠媒麓贸脰赂脢媒脢脮碌酶拢卢脢脨鲁隆鹿脴脳垄脙脌脕陋麓垄脦麓脌麓脌没脗脢露炉脧貌');
	var artStock = new Array();
	var cotitle = '脠芦脟貌脪陋脦脜|脙脌鹿脡脠媒麓贸脰赂脢媒脢脮碌酶拢卢脢脨鲁隆鹿脴脳垄脙脌脕陋麓垄脦麓脌麓脌没脗脢露炉脧貌';
	var codata = cocontent.replace(/<[^>]*>/g, '').substr(0,250);
	var courl = 'http://stock.10jqka.com.cn/usstock/20241011/c662318446.shtml';
        newRobotRecList : [],
		cid: ''  //脳卯潞贸脪禄脤玫脝脌脗脹碌脛ID
	ARTINFO.tday = $.timeFormat(ARTINFO.tday, 'yyyyMMddHHmm');
	ARTINFO.tday = parseInt(ARTINFO.tday);
	ARTINFO.userid = WEB.basic.getUserid();
$(document).ready(function(){
	    	    TA.log({id:id, ld:'browser',fid:'info_gather,zx_n_all,zx_wzb_total,zx_ll_684,zxusstock,ch_stock', pt:2,_sid:'imgzx'});
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?78c58f01938e4d85eaf619eae71b4ed1";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
var _hmt = _hmt || [];
 var hm = document.createElement("script");
 hm.src = "//hm.baidu.com/hm.js?f79b64788a4e377c608617fba4c736e2";
 var s = document.getElementsByTagName("script")[0]; 
 s.parentNode.insertBefore(hm, s);
// 麓娄脌铆脥录脝卢src脦陋undefined
  var imageDomList = document.querySelectorAll('img');
  if (imageDomList.length > 0) {
      imageDomList.forEach(ele => {
          var imgSrc = ele.src;
          if (imgSrc.endsWith('/undefined')) {
              ele.remove();
  var srcVideoDom = document.querySelector('input.video-insert');
    var videoUrl = srcVideoDom.getAttribute('data-src');
    var videoThumb = srcVideoDom.getAttribute('data-thumb');
    var videoDom = document.createElement('video');
    var parent = srcVideoDom.parentNode;
    videoDom.src = videoUrl;
    videoDom.poster = videoThumb;
    videoDom.setAttribute("controls", true);
    videoDom.setAttribute("style", "width:627px;height:353px");
    parent.insertBefore(videoDom, srcVideoDom);
    srcVideoDom.remove();
  $('.self-stock-con').css({'height':'206px'})
// 脧脼脰脝碌脛禄煤脝梅脠脣脳茅录镁掳脳脙没碌楼
    const LIMIT_ROBOT_VISUAL_COMS = [
        'thsf2e_common-long-hu-rank',
        'thsf2e_ai-table'
    const checkLimitCom = (dataType) => {
        return LIMIT_ROBOT_VISUAL_COMS.includes(dataType);
    // 脕煤禄垄掳帽脤脴脢芒麓娄脌铆
    const LONGHU_TYPE = 'thsf2e_common';
    // 禄帽脠隆dom陆脷碌茫脨脜脧垄
    const getDomAttrData = (robotParse) => {
        const dataType = robotParse.getAttribute('data-type');
        const dataProps = robotParse.getAttribute('data-props');
        const dataComurl = robotParse.getAttribute('data-comurl');
        const dataStyle = robotParse.getAttribute('data-style');
        const dataVersion = robotParse.getAttribute('data-version');
            dataType,
            dataProps,
            dataComurl,
            dataStyle,
            dataVersion,
    const loadScript = (url, callback) => {
        const originalDefine = window.define;
        window.define = undefined;
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.charset = 'utf-8';
        script.src = url;
        script.onload = function() {
            callback(null)
        script.onerror = function() {
            callback(`Failed to load script: ${url}`);
        document.body.appendChild(script);
    // 脜脨露脧window脌茂脢脟路帽脫脨脮芒赂枚脳茅录镁拢卢脠莽鹿没脫脨戮脥脰卤陆脫脢鹿脫脙拢卢脠莽鹿没脙禄脫脨戮脥录脫脭脴
    const getComponent = (id, url, callback) => {
        return new Promise((resolve, reject) => {
            if (window[id]) {
                return resolve(window[id].default)
            loadScript(url, (err) => {
                if (err || !window[id]) {
                    reject(err || '脳茅录镁id麓铆脦贸')
                } else {
                    resolve(window[id].default)
    // 陆芒脦枚禄煤脝梅脠脣脛脷脠脻
    const parseRobot = (ele) => {
        const { dataType, dataProps, dataComurl, dataStyle, dataVersion } = getDomAttrData(ele);
        const props = JSON.parse(decodeURIComponent(dataProps));
        const style = JSON.parse(decodeURIComponent(dataStyle));
        // 脜脨露脧脢脟路帽虏禄脭脷limit盲脰脠戮脰脨
        if (!checkLimitCom(dataType)) {
        const id = `${dataType}_index@${dataVersion}`;
        getComponent(id, dataComurl).then((com) => {
            const placeholder = document.createElement('div');
            placeholder.className = 'robot-visual-container';
            ele.replaceWith(placeholder);
            const comInstance = new Vue({
                el: placeholder,
                render: h => h('div', { attr: { id: 'robotVisual' }, style }, [
                    h(com, { props })
            }).$mount();
        }).catch(err => {
            console.error(err)
    const parseContentMain = () => {
        const eles = document.getElementById('contentApp').querySelectorAll('robot-parse');
        eles.forEach(ele => {
            parseRobot(ele)
# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import logging

from models.sequence_parallel_utils import is_sequence_parallel_parameter
from paddle.distributed.fleet import fleet
from paddle.distributed.fleet.utils.hybrid_parallel_util import (
    fused_allreduce_gradients_with_group,
)
from paddleformers.trainer.trainer_callback import TrainerCallback

logger = logging.getLogger(__name__)


class SPGradSyncCallback(TrainerCallback):
    def __init__(self, model):
        assert hasattr(fleet, "_hcg"), "must use <PERSON> when calling this Callback"
        logger.info("using sp callback")
        params = []
        self.model = model
        for n, p in model.named_parameters():
            if is_sequence_parallel_parameter(p):
                logger.info(f"register bw hook for:{n}")
                params.append(p)
        logger.info(f"#-sp-sync param:{len(params)}")
        self._sp_params = params

    def on_optimizer_begin(self, args, state, control, **kwargs):
        if self._sp_params:
            mp_group = fleet.get_hybrid_communicate_group().get_model_parallel_group()
            fused_allreduce_gradients_with_group(self._sp_params, group=mp_group, scale=1.0)

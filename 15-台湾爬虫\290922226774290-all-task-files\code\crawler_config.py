#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫配置文件
作者: MiniMax Agent
"""

# 爬虫配置
CRAWLER_CONFIG = {
    # 基础配置
    'base_url': 'https://web.pcc.gov.tw',
    'target_url': 'https://web.pcc.gov.tw/prkms/tender/common/bulletion/indexBulletion',
    
    # 并发配置
    'max_workers': 5,           # 最大并发线程数
    'max_pages': 50,            # 最大爬取页数
    
    # 请求配置
    'timeout': 30,              # 请求超时时间（秒）
    'retry_times': 3,           # 重试次数
    'delay_range': (1, 3),      # 请求延迟范围（秒）
    
    # 数据配置
    'enable_detail_crawl': True,  # 是否爬取详情页
    'save_to_csv': True,         # 是否保存CSV
    'save_to_json': True,        # 是否保存JSON
    'save_to_database': True,    # 是否保存数据库
}

# User-Agent池
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36'
]

# 请求头配置
DEFAULT_HEADERS = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Cache-Control': 'max-age=0'
}

# 数据字段映射
FIELD_MAPPING = {
    '案号': 'announcement_id',
    '标案名称': 'title',
    '机关名称': 'organization',
    '公告日期': 'announcement_date',
    '截止收件': 'deadline_date',
    '预算金额': 'budget_amount',
    '采购性质': 'procurement_type',
    '联络人': 'contact_person',
    '联络电话': 'contact_phone',
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': '/workspace/logs/crawler.log',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

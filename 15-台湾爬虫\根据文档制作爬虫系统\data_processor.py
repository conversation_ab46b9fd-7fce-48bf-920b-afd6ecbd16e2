#!/usr/bin/env python3
"""
數據處理器 - 生成原始數據和簡體中文數據兩個版本
"""

import json
import re
from datetime import datetime

# 繁體轉簡體字典（常用字）
TRADITIONAL_TO_SIMPLIFIED = {
    # 常用繁體字轉簡體字
    '國': '国', '機': '机', '關': '关', '標': '标', '決': '决', '廠': '厂',
    '商': '商', '資': '资', '訊': '讯', '電': '电', '軍': '军', '購': '购',
    '採': '采', '業': '业', '務': '务', '類': '类', '項': '项', '組': '组',
    '織': '织', '態': '态', '員': '员', '總': '总', '數': '数', '過': '过',
    '開': '开', '時': '时', '間': '间', '預': '预', '算': '算', '額': '额',
    '億': '亿', '萬': '万', '仟': '千', '佰': '百', '拾': '十', '壹': '一',
    '貳': '二', '參': '三', '肆': '四', '伍': '五', '陸': '六', '柒': '七',
    '捌': '八', '玖': '九', '零': '零', '圓': '元', '臺': '台', '灣': '湾',
    '縣': '县', '鄉': '乡', '鎮': '镇', '區': '区', '裡': '里', '號': '号',
    '樓': '楼', '層': '层', '室': '室', '處': '处', '科': '科', '課': '课',
    '辦': '办', '聯': '联', '絡': '络', '傳': '传', '真': '真', '話': '话',
    '電': '电', '話': '话', '負': '负', '責': '责', '擔': '担', '任': '任',
    '計': '计', '劃': '划', '設': '设', '計': '计', '師': '师', '術': '术',
    '學': '学', '會': '会', '議': '议', '論': '论', '證': '证', '書': '书',
    '報': '报', '告': '告', '單': '单', '據': '据', '檔': '档', '案': '案',
    '紀': '纪', '錄': '录', '記': '记', '載': '载', '資': '资', '料': '料',
    '檢': '检', '視': '视', '查': '查', '詢': '询', '問': '问', '題': '题',
    '題': '题', '目': '目', '內': '内', '容': '容', '說': '说', '明': '明',
    '備': '备', '註': '注', '備': '备', '考': '考', '參': '参', '考': '考',
    '應': '应', '該': '该', '須': '须', '必': '必', '需': '需', '要': '要',
    '請': '请', '求': '求', '協': '协', '助': '助', '幫': '帮', '忙': '忙',
    '支': '支', '援': '援', '服': '服', '務': '务', '管': '管', '理': '理',
    '維': '维', '護': '护', '保': '保', '養': '养', '修': '修', '理': '理',
    '檢': '检', '修': '修', '測': '测', '試': '试', '驗': '验', '收': '收',
    '驗': '验', '證': '证', '認': '认', '證': '证', '審': '审', '核': '核',
    '評': '评', '估': '估', '鑑': '鉴', '定': '定', '檢': '检', '驗': '验',
    '測': '测', '量': '量', '調': '调', '查': '查', '統': '统', '計': '计',
    '分': '分', '析': '析', '研': '研', '究': '究', '開': '开', '發': '发',
    '製': '制', '造': '造', '生': '生', '產': '产', '加': '加', '工': '工',
    '處': '处', '理': '理', '運': '运', '輸': '输', '運': '运', '送': '送',
    '配': '配', '送': '送', '倉': '仓', '儲': '储', '存': '存', '放': '放',
    '庫': '库', '房': '房', '間': '间', '場': '场', '地': '地', '址': '址',
    '位': '位', '置': '置', '環': '环', '境': '境', '條': '条', '件': '件',
    '設': '设', '備': '备', '器': '器', '材': '材', '具': '具', '用': '用',
    '品': '品', '物': '物', '料': '料', '零': '零', '件': '件', '配': '配',
    '件': '件', '耗': '耗', '材': '材', '消': '消', '耗': '耗', '品': '品'
}

def traditional_to_simplified(text):
    """繁體轉簡體"""
    if not text:
        return text
    
    result = text
    for trad, simp in TRADITIONAL_TO_SIMPLIFIED.items():
        result = result.replace(trad, simp)
    
    return result

def clean_text_format(text):
    """清理文本格式，移除多餘的空白字符和換行符"""
    if not text:
        return text
    
    # 移除 \r\n\t 等控制字符
    cleaned = re.sub(r'[\r\n\t]+', ' ', text)
    # 移除多餘空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    # 去除首尾空格
    cleaned = cleaned.strip()
    
    return cleaned

def process_procurement_data(input_file, output_original, output_simplified):
    """處理採購數據，生成原始和簡體中文兩個版本"""
    
    print(f"📖 正在讀取數據文件: {input_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 成功讀取 {len(data)} 筆數據")
        
        # 處理原始數據（清理格式但保持繁體中文）
        original_data = []
        simplified_data = []
        
        for i, item in enumerate(data):
            print(f"🔄 處理第 {i+1}/{len(data)} 筆數據...")
            
            # 深度複製數據
            original_item = json.loads(json.dumps(item))
            simplified_item = json.loads(json.dumps(item))
            
            # 處理原始數據（只清理格式）
            original_item = clean_data_format(original_item)
            
            # 處理簡體數據（清理格式 + 轉換簡體）
            simplified_item = clean_data_format(simplified_item)
            simplified_item = convert_to_simplified(simplified_item)
            
            original_data.append(original_item)
            simplified_data.append(simplified_item)
        
        # 保存原始數據文件
        with open(output_original, 'w', encoding='utf-8') as f:
            json.dump(original_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 原始數據已保存到: {output_original}")
        
        # 保存簡體中文數據文件
        with open(output_simplified, 'w', encoding='utf-8') as f:
            json.dump(simplified_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 簡體中文數據已保存到: {output_simplified}")
        
        # 生成統計報告
        generate_statistics_report(original_data, simplified_data)
        
    except Exception as e:
        print(f"❌ 處理數據時發生錯誤: {str(e)}")

def clean_data_format(item):
    """清理數據格式"""
    if isinstance(item, dict):
        cleaned_item = {}
        for key, value in item.items():
            cleaned_item[key] = clean_data_format(value)
        return cleaned_item
    elif isinstance(item, list):
        return [clean_data_format(sub_item) for sub_item in item]
    elif isinstance(item, str):
        return clean_text_format(item)
    else:
        return item

def convert_to_simplified(item):
    """轉換為簡體中文"""
    if isinstance(item, dict):
        converted_item = {}
        for key, value in item.items():
            converted_item[key] = convert_to_simplified(value)
        return converted_item
    elif isinstance(item, list):
        return [convert_to_simplified(sub_item) for sub_item in item]
    elif isinstance(item, str):
        return traditional_to_simplified(item)
    else:
        return item

def generate_statistics_report(original_data, simplified_data):
    """生成統計報告"""
    print(f"\n📊 === 數據處理統計報告 ===")
    print(f"處理時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"總數據筆數: {len(original_data)}")
    
    # 統計有效數據
    valid_original = 0
    valid_simplified = 0
    
    for item in original_data:
        if item['detail_data']['agency_info']['agency_name']:
            valid_original += 1
    
    for item in simplified_data:
        if item['detail_data']['agency_info']['agency_name']:
            valid_simplified += 1
    
    print(f"有效原始數據: {valid_original} 筆")
    print(f"有效簡體數據: {valid_simplified} 筆")
    
    # 統計機關分布
    agencies = {}
    for item in original_data:
        agency = item['list_data']['agency']
        if agency:
            agencies[agency] = agencies.get(agency, 0) + 1
    
    print(f"\n🏢 機關分布:")
    for agency, count in sorted(agencies.items(), key=lambda x: x[1], reverse=True):
        print(f"  {agency}: {count} 筆")
    
    # 統計標案類型
    tender_types = {}
    for item in original_data:
        tender_type = item['list_data']['tender_type']
        if tender_type:
            tender_types[tender_type] = tender_types.get(tender_type, 0) + 1
    
    print(f"\n📋 標案類型分布:")
    for tender_type, count in tender_types.items():
        print(f"  {tender_type}: {count} 筆")
    
    # 保存統計報告
    stats_report = {
        'processing_time': datetime.now().isoformat(),
        'total_records': len(original_data),
        'valid_original_records': valid_original,
        'valid_simplified_records': valid_simplified,
        'agency_distribution': agencies,
        'tender_type_distribution': tender_types
    }
    
    with open('data_processing_statistics.json', 'w', encoding='utf-8') as f:
        json.dump(stats_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 統計報告已保存到: data_processing_statistics.json")

def main():
    """主函數"""
    print("🚀 === 政府採購數據處理器 ===")
    print("功能：生成原始數據和簡體中文數據兩個版本")
    
    # 輸入和輸出文件名
    input_file = 'defense_procurement_data.json'
    output_original = 'defense_procurement_data_original_cleaned.json'
    output_simplified = 'defense_procurement_data_simplified.json'
    
    # 處理數據
    process_procurement_data(input_file, output_original, output_simplified)
    
    print(f"\n✅ === 處理完成 ===")
    print(f"📁 生成的文件:")
    print(f"  1. {output_original} - 原始數據（繁體中文，格式清理）")
    print(f"  2. {output_simplified} - 簡體中文數據（格式清理 + 繁簡轉換）")
    print(f"  3. data_processing_statistics.json - 統計報告")

if __name__ == "__main__":
    main()

﻿# 对话奕行智能刘珲（Ken<PERSON> Liu）：智能汽车计算芯片重构的一些思考

**发布日期**: 2023年05月26日

**原文链接**: https://laoyaoba.com/n/862666

## 📄 原文内容

在刚刚结束的以“拥抱汽车行业新时代”为主题的第二十届上海国际汽车工业展览会上，目不暇接的展台和络绎不绝的人流，深刻感受到汽车市场正在迎来一场未知的变局，从汽车文化与时尚元素交融、汽车产业低碳转型、产业链供应链布局优化，到国产芯片争相出彩，我国汽车产业正进入加速重构期。

“人类对科技的极致追求和不断探索在源头上让软件、硬件以及应用场景发生一次又一次的重构。今天在智能汽车计算领域，我们也正处在由1.0时代到2.0时代的重构过程当中。这些重构对我们整个行业来说带来了很多层面的挑战，但同时也意味着很多层面的机遇” ，奕行智能（EVAS Intelligence）创始人暨CEO刘珲先生 接受集微网访谈 时，基于智能汽车计算芯片的需求和变化，从应用场景、基础算法、计算架构等多方面角度讲述了他的深刻思考。

集微网：最近针对自动驾驶行业发展现状和未来趋势，行业里面有非常多意见不一的声音，您整体上是如何看待的？

刘珲： 从大势而言，今天我们所看到的汽车行业正处于百年之变革的机遇中，面对未来汽车芯片在时空维度的需求变化，我们需要有比以往更清晰、更理智的洞察。前几年，大家对于自动驾驶的发展预期很激进，认为很快就会看到全面的L4及以上的自动驾驶到来。可是当Corner Case所导致的用户体验、商业场景这些很本质的问题并没有得到很好解决时，大家对智驾这个应用变得更加理性，甚至有些声音提出了对自动驾驶非常悲观的论调。这些认知的转变都是一个新兴事物发展过程中的正常现象。对于智驾这个场景，我们认为当前仍然处在非常早期的由功能模块所驱动的辅助驾驶阶段，但在可预期的未来，我们将会看到一些简单开放场景所驱动的部分自动驾驶的到来。在更未来，我们可以去期待一个不仅仅单车智能的局面，而是一个能从全局交通效率去考虑的超级智能系统。我们对未来仍然是乐观并且笃定的，但这需要整个行业一步步非常务实的把整个产业做到良性的商业闭环路径上。

集微网：那目前您觉得有哪些比较接近我们能感知到的未来的东西吗，您可以跟我们举例吗？

刘珲 ： 例如在算法层面上，我们看到Transformer会成为未来5年AI的通用基础架构，多模态感知以及基于Transformer架构的各种BEV算法已经成为车厂下一代算法系统的共识。

集微网：说到Transformer，奕行是如何看待它在自动驾驶行业的应用前景和挑战的？

刘珲： Transformer的优势首先在于并行化，它被提出来的时候原本的用意是用来处理自然语言模型，但是和RNN、LSTM这些传统的时序网络模型不同的是它通过引入位置编码，把输入的文本token可以直接并行化方式来处理。同样在图像领域，图像不同区域的信息也可以被切分成一个个token引入位置编码做并行化的注意力计算；

它的第二个优势就是它的通用性，任何在时间上和空间上有关联关系的信息其实都可以作为输入交给Transformer做注意力运算，这使得它打破了自然语言、语音、图像、视频或其他的信息输入和输出上的壁垒。大家知道我们的物理世界是由各种各样不同形式的信息所表达的，Transformer的这种通用性为AI认知物理世界的多模态感知提供了一个很好的实现路径。 Transformer在自动驾驶领域是被特斯拉第一个应用到量产算法中的，在2021年特斯拉的AI Day上，他们发布了基于Transfomer的BEV算法的成功落地， 这个也坚定了产业界对于BEV应用到自动驾驶2.0阶段的共识。

集微网：BEV算法现在被不少汽车原厂关注和提起，能简单介绍下这种算法的特点和优势吗？

刘珲： BEV简称“鹰眼视角”，是将多个摄像头的数据2D视觉信息转成俯视3D坐标系的的算法技术。应用BEV的目标一方面是为了更好的完成3D感知，在BEV空间中的感知精度上的优势可以更方便规划控制的实现，也为基于深度学习的预测规划提供了实现路径。再者就是在多传感器感知融合中，将视觉信息转成BEV坐标会让不同摄像头数据以及雷达数据在空间保持一致，在相同坐标系进行融合。

基于时空注意力机制的BEV算法是产业在试图落地量产的重点，但由于其计算量和计算特点，其实在这方面的部署仍面临许多挑战，即使业界最强的自动驾驶算力芯片，部署在上面的结果也只能达到10FPS左右的水平。因此，要提升BEV性能，需要有办法解决部署上的各种挑战。

集微网：在您看来，部署BEV算法的难点主要在哪里？

刘珲： 今天我们看到市面上绝大部分AI处理器都是基于CNN的计算特点设计的。这些内核在匹配Transformer类型算法会有很大的性能短板。

CNN是计算密集型的网络，他的特点是整个网络是非常规整的一层层的卷积操作，每层里若干通道的卷积核作为权重是可以被输入特征共享的存储数据。而Transformer的特点是非计算密集型算子很多，包括Q*K的MatMul, FC， Element-wise，Reduce（例如Softmax&Layernorm），另外Transfomer类型的算法里不规则形状的张量多，需要大量对矩阵操作的Transpose/Permute/Reshape等内存操作的算子，这个最终体现在硬件上就是对片上内存的容量和仿存带宽的要求会以CNN为目标的加速芯片高很多。考虑到Transformer里有大量的MHA，需要硬件与编译器的精巧配合来提升并行度，减少计算时的空泡， 因此需要从硬件上合理的MAC单元数，Local 与 Global Memory的设计来降低计算与存储的瓶颈同时又要考虑后端物理实现的Trade off，而编译器层面做很多图级别、Stream级别和算子级别的并发和片上融合也会需要有比传统AI加速芯片更充沛的片上存储资源和带宽。另外有些OP并行化比较差，当前市面上的AI处理器很难把这些操作的latency给隐藏掉， 比如Gather， Scatter这些在BEV网络避不开的算子存在对数组Index这种不连续地址的内存操作等在现有的硬件和编译器工具下直接部署的运算效率极低。这些都需要在软硬件上提前考虑，避免整网计算的性能瓶颈。

其次是数据类型的挑战-常规Int8量化的精度下降。传统的CNN网络用Int8精度量化在精度损失上是可接受的，Transformer用Int8对MHA模块中频繁出现Softmax以及Layer Norm精度损失比较大。解决这个问题的办法有两种，一种是硬件上提供更大的浮点（比如FP16）算力，这个当然会带来架构设计的复杂性以及更多的硬件资源的需求，另一种是在软件上采用更加复杂的量化方式，比如混合精度量化、PTQ+QAT结合的，但这个的代价就是软件层面的复杂性，量化来回转换所引起的延时导致性能的损失，以及算法移植的时间代价。

同时，我们看到 传统AI芯片工具链考虑CNN网络为主，编译器在对Transformer类型网络计算的算子优化的空间很小，也就是天花板比较低，算子的优化需要投入大量的人力资源和时间去做手工的开发，体现出来就是网络在性能有保障的条件下的部署要花很长时间，给到用户的感觉就是可编程性很差。

集微网：您刚才说到的这些挑战和困难，需要在芯片层面怎么去应对和解决呢？

刘珲： 今天我们看到基于Transformer的BEV在当前可获得硬件上部署性能都比较差，所以这就要求我们要从NPU的硬件架构和编译器层面去做一定程度的重构。 好的NPU架构是一定需要与编译器协同设计，编译器的优化策略又和NPU架构强相关。这个软硬件协同工作的本质上要解决的问题就是怎么划分部署计算任务填满硬件。在车这个端侧场景中算力是不能无限膨胀的，把云端的超大算力直接搬到端侧是不理性的，在算力的红线下，如何保证效率的前提下做好可编程性显得尤为重要。可编程性在这里我们讲的是两个维度的事情，一个是算子的完备性，让用户在最短的时间内完成模型的移植和部署，一个是算子在硬件上的性能，模型部署后的性能的达到要求，把硬件的效率充分发挥出来。

影响可编程性的因素包括几个方面：

一是指令（或操作）以及数据的颗粒度。颗粒度越细，通用性越好，但编译器优化的复杂度会大。这个就像乐高，年纪小的孩子会给他比较大块的积木让他比较容易拼出想要的模型，年纪大的小孩会给他比较小块的积木，虽然比较难，但他可以发挥自己的想象力拼出更多的模型。

二是异构的方式。处理器一般分成标量、矢量和张量等，异构的方式同样决定了编程能力的好坏。在核内异构，可以让三种核在寄存器间进行数据交互，做指令级的调度。在核间异构，由Host核（一般是CPU核）下发并行计算任务给到多个NPU核，然后通过核间的share memory来交互数据。第三种就是在SoC层级与DSP、CPU等不同类型的核的异构，这种NPU聚焦张量运算，一般可以设计比较简单，但需要通过DDR交互数据，对DDR带宽和延时有比较大的挑战。

三是数据和指令的并行方式。数据并行包括GPU常用的SIMT（单指令多线程），DSP常用的SIMD（单指令多数据）， 指令并行有对编译器相对友好的超标量和对硬件设计相对友好但依赖编译器去完成指令之间的依赖和调度编排的VLIW（超长指令字）。

四是片上存储层级组织的方式也是影响可编程的因素，越多的存储层级从编译器优化角度复杂性越高，但硬件设计的挑战度相对较低。

所有这些架构的技术从软硬件层面都有各自的优点和缺点， 本质上来讲都是围绕你的设计目标做各种Trade off。从我们智驾这个端侧应用来讲，基于SMID的DSA架构的道路从效率上是理性的，但不能仅仅关注效率，还要关注如何提高这种架构下的可编程性，选择对软件友好的指令颗粒度、异构方式、指令并行方式和存储层次结构的设计。

集微网：如今从事自动驾驶芯片的厂商角逐越演越烈，您觉得什么样的芯片产品未来能够引领市场？

刘珲： 首先我们认为芯片是底层的支撑硬件，是上层应用和算法的基座，所以我们不认为芯片能够引领市场需求，而且芯片是不能以技术指标为驱动的方式去思考的。今天我们看到越来越多的主机厂关注的不再是超大算力预埋，而是越来越关注如何一点点去做用户体验，如何去做到更高的性价比。所以说好的芯片应该是由商业反推的，是由用户愿意买单的应用场景去决定软件和硬件长什么样，再反推到芯片如何去均衡算力和成本。

集微网：怎么看待像Chat-GPT背后的核心技术以及这样的AI大模型未来在车端落地的可行性？

刘珲： 我们看好大模型在端侧应用的这个趋势，不过如果部署在端侧，必须解决模型轻量化和硬件高效化这两个技术问题。当然在车端落地的并不一定是Chat-GPT这种自然语言大模型，而有可能是多模态感知的某种大模型。而我们看到这些不断涌现的大模型的算法框架底层都是Transformer，决定他们差异的是数据和训练技术。最近Meta发布的Segment Anything模型，我们看到里面的核心技术Data Engine是1.1B的分割的数据量，而这个海量的数据集是由AI所生产出来的，这个是核心。我们认为未来拉开车厂的核心能力也是数据闭环这块，所以作为一家芯片公司，我们在算法层面会关注如何用我们的算法能力打造基于AI的数据生产的工具，帮助我们的客户实现数据闭环。

集微网：那最后您能跟我们再分享关于奕行智能的一些情况和对公司未来的展望？

刘珲： 我们成立于2022年初，总部注册在广州南沙，在全国七个城市有研发中心。我们是一家以汽车智能化作为首要落地场景的计算芯片公司，围绕AI计算架构、SoC平台和软件工具链这三个技术领域的核心能力去帮助我们的客户和伙伴去一步步的革新人类的出行和生活方式。成立近1年半以来，我们一直在按照我们的既定策略稳步发展，今年到明年陆续推出我们几款不同市场定位的车载计算芯片。我们的车载计算芯片除了关注我前面提到的计算架构对算法演进的支持和效率及可编程性的均衡，也会非常关注如何解决成本与异构计算资源、传感器数据流的接入处理能力、功能安全与信息安全完备性的矛盾问题，从效率层面去做价值创造。

对于我们所聚焦的计算领域，未来我们觉得还有很多的事情可以做。从智能汽车的角度，也不仅仅是舱外感知所代表的自动驾驶，还有很多其他的计算场景，比如舱内的多模态感知、多模态交互，我们会围绕这些场景去逐步布局我们的芯片产品来满足多样化的计算需求。